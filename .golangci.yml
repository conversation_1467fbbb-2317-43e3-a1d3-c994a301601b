# This file is licensed under the terms of the MIT license https://opensource.org/license/mit
# Copyright (c) 2021-2025 <PERSON><PERSON>

## Golden config for golangci-lint v2.1.6
#
# This is the best config for golangci-lint based on my experience and opinion.
# It is very strict, but not extremely strict.
# Feel free to adapt it to suit your needs.
# If this config helps you, please consider keeping a link to this file (see the next comment).

# Based on https://gist.github.com/maratori/47a4d00457a92aa426dbd48a18776322
version: "2"
linters:
  default: none
  enable:
    - asciicheck # checks that your code does not contain non-ASCII identifiers
    - bidichk # checks for dangerous unicode character sequences
    - bodyclose # checks whether HTTP response body is closed successfully
    - forcetypeassert # [replaced by errcheck] finds forced type assertions
    - gocheckcompilerdirectives # validates go compiler directive comments (//go:)
    - gocritic # provides diagnostics that check for bugs, performance and style issues
    - gosec # inspects source code for security problems
    - govet # reports suspicious constructs, such as Printf calls whose arguments do not align with the format string
    - ineffassign # detects when assignments to existing variables are not used
    - nilerr # finds the code that returns nil even if it checks that the error is not nil
    - nilnesserr # reports that it checks for err != nil, but it returns a different nil value error (powered by nilness and nilerr)
    - noctx # finds sending http request without context.Context
    - sqlclosecheck # checks that sql.Rows and sql.Stmt are closed
    - unconvert # removes unnecessary type conversions
    - unparam # reports unused function parameters
    - unused # checks for unused constants, variables, functions and types
    - usestdlibvars # detects the possibility to use variables/constants from the Go standard library
    - unparam # reports unused function parameters
    - unused # checks for unused constants, variables, functions and types


  # All settings can be found here https://github.com/golangci/golangci-lint/blob/HEAD/.golangci.reference.yml
  settings:
    gocritic:
      # Settings passed to gocritic.
      # The settings key is the name of a supported gocritic checker.
      # The list of supported checkers can be found at https://go-critic.com/overview.
      disabled-checks:
          - commentFormatting
          - ifElseChain
      settings:
        captLocal:
          # Whether to restrict checker to params only.
          # Default: true
          paramsOnly: false
        underef:
          # Whether to skip (*x).method() calls where x is a pointer receiver.
          # Default: true
          skipRecvDeref: false
        
    govet:
      # Enable all analyzers.
      # Default: false
      enable-all: true
      # Disable analyzers by name.
      # Run `GL_DEBUG=govet golangci-lint run --enable=govet` to see default, all available analyzers, and enabled analyzers.
      # Default: []
      disable:
        - fieldalignment # too strict
        - shadow


    gosec:
      excludes:
        - G101
        - G104
        - G404

  exclusions:
    # Log a warning if an exclusion rule is unused.
    # Default: false
    warn-unused: true
    # Predefined exclusion rules.
    # Default: []
    presets:
      - std-error-handling
      - common-false-positives
    # Excluding configuration per-path, per-linter, per-text and per-source.
    rules:
      - source: 'TODO'
        linters: [ godot ]
      - text: 'should have a package comment'
        linters: [ revive ]
      - text: 'exported \S+ \S+ should have comment( \(or a comment on this block\))? or be unexported'
        linters: [ revive ]
      - text: 'package comment should be of the form ".+"'
        source: '// ?(nolint|TODO)'
        linters: [ revive ]
      - text: 'comment on exported \S+ \S+ should be of the form ".+"'
        source: '// ?(nolint|TODO)'
        linters: [ revive, staticcheck ]
      - path: '_test\.go'
        linters:
          - bodyclose
          - dupl
          - errcheck
          - funlen
          - goconst
          - gosec
          - noctx
          - wrapcheck