package shared

import (
	"context"
	"errors"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"net/url"
	"os"
	"time"
)

type SignedURLOpt struct {
	bucketName string
}

func decodeEncodedFilename(encoded string) string {
	decode, err := url.QueryUnescape(encoded)
	if err != nil {
		decode = encoded
	}
	return decode
}

func SignedURLFromAWS(ctx context.Context, objectPath string, expiredDuration time.Duration, signedURLOpts ...SignedURLOpt) (string, error) {
	objectPath = decodeEncodedFilename(objectPath)
	cfg, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		return "", errors.New("failed load env aws")
	}

	bucketName := os.Getenv(`ENVIRONMENT`)
	if len(signedURLOpts) > 0 {
		bucketName = signedURLOpts[0].bucketName
	}

	if len(bucketName) < 1 {
		return "", errors.New("bucket can't be empty, please check your bucket name")
	}

	client := s3.NewFromConfig(cfg)
	presignClient := s3.NewPresignClient(client)
	presignedURL, err := presignClient.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucketName),
		Key:    aws.String(objectPath),
	}, s3.WithPresignExpires(expiredDuration))
	if err != nil {
		return "", fmt.Errorf("failed to generate signed URL: %v", err)
	}

	return presignedURL.URL, nil
}
