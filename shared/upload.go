package shared

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"io/ioutil"
	"mime/multipart"
	"net/url"
	"os"
	"strconv"
	"strings"
	"time"

	"cloud.google.com/go/storage"
	"github.com/Lionparcel/hydra/config/keys"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/feature/s3/manager"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"google.golang.org/api/option"
)

var (
	storageClient     *storage.Client
	HeaderContentType = `Content-Type`
)

type UploadInfo struct {
	BucketName          string
	FolderPath          string
	MultipartFile       multipart.File
	MultipartFileHeader *multipart.FileHeader
	TypeName            string
	TypeData            string
	BufferFile          *bytes.Buffer

	// image.png
	FileName string
}

type Storage struct {
	Client *storage.Client
	Bucket *storage.Writer
}

type GoogleCredentials struct {
	PrivateKey  string `json:"private_key"`
	ClientEmail string `json:"client_email"`
}

// UploadToGcp uploads file to bucket
func UploadToGcp(ctx context.Context, info UploadInfo) (string, error) {
	bucket := os.Getenv(`ENVIRONMENT`)
	var err error

	// Validation mimeType
	if err := validateMimeType(info); err != nil {
		return "", err
	}

	extfile := strings.Split(info.MultipartFileHeader.Filename, ".")
	fileName := info.TypeName + strconv.Itoa(int(time.Now().UTC().Unix())) + "." + extfile[len(extfile)-1]

	storageClient, err = storage.NewClient(ctx, option.WithCredentialsFile(keys.GoogleKey()))
	if err != nil {
		return "", fmt.Errorf(err.Error())
	}

	defer info.MultipartFile.Close()

	sw := storageClient.Bucket(bucket).Object(info.FolderPath + fileName).NewWriter(ctx)

	if _, err := io.Copy(sw, info.MultipartFile); err != nil {
		return "", fmt.Errorf(err.Error())
	}

	if err := sw.Close(); err != nil {
		return "", fmt.Errorf(err.Error())
	}

	u, err := url.Parse("/" + bucket + "/" + sw.Attrs().Name)
	if err != nil {
		return "", fmt.Errorf(err.Error())
	}

	return fmt.Sprintf("https://storage.googleapis.com%s", u.EscapedPath()), nil
}

func CreateClient(Filename string, contentType string) (*Storage, error) {
	bucket := os.Getenv(`ENVIRONMENT`)
	storageClient, err := storage.NewClient(context.Background(), option.WithCredentialsFile(keys.GoogleKey()))
	if err != nil {
		return nil, fmt.Errorf(err.Error())
	}

	bucketWriter := storageClient.Bucket(bucket).Object(Filename).NewWriter(context.Background())
	bucketWriter.ContentType = contentType

	return &Storage{
		Client: storageClient,
		Bucket: bucketWriter,
	}, nil
}

func (c *Storage) Close() {
	c.Client.Close()
	c.Bucket.Close()
}

// DeleteFile object Ex: bulk_upload1631088497.csv <- without link
func DeleteFile(ctx context.Context, object string) error {
	bucket := os.Getenv(`ENVIRONMENT`)
	storageClient, err := storage.NewClient(ctx, option.WithCredentialsFile(keys.GoogleKey()))
	if err != nil {
		return fmt.Errorf(err.Error())
	}
	defer storageClient.Close()

	ctx, cancel := context.WithTimeout(ctx, time.Second*10)
	defer cancel()

	o := storageClient.Bucket(bucket).Object(object)
	if err := o.Delete(ctx); err != nil {
		return fmt.Errorf("Object(%q).Delete: %v", object, err)
	}

	return nil
}

// Function upload file to S3 AWS
func UploadFileToAWS(ctx context.Context, fileInfo UploadInfo) (string, error) {
	newFileName := ""
	fileExtension := ""

	// Validation mimeType
	if err := validateMimeType(fileInfo); err != nil {
		return "", err
	}

	if fileInfo.MultipartFileHeader != nil {
		tempFileName := strings.Split(fileInfo.MultipartFileHeader.Filename, ".")
		fileExtension = tempFileName[len(tempFileName)-1]
		tempFileName = strings.Split(fileInfo.FileName, ".")
		newFileName = tempFileName[0]
	} else {
		tempFileName := strings.Split(fileInfo.FileName, ".")
		fileExtension = tempFileName[len(tempFileName)-1]
		newFileName = tempFileName[0]
	}

	newFileName += fmt.Sprintf("%d.%s", int(time.Now().UTC().Unix()), fileExtension)

	// Load env AWS*
	cfg, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		return "", errors.New("failed load env aws")
	}

	// Create an Amazon S3 service client
	client := s3.NewFromConfig(cfg)

	// Upload file to S3
	uploader := manager.NewUploader(client)
	upload, err := uploader.Upload(ctx, &s3.PutObjectInput{
		Bucket: aws.String(os.Getenv(`ENVIRONMENT`)),
		Key:    aws.String(fmt.Sprintf(`%s%s`, fileInfo.FolderPath, newFileName)),
		Body: func() io.Reader {
			if fileInfo.BufferFile == nil {
				return fileInfo.MultipartFile
			}
			return fileInfo.BufferFile
		}(),
	})
	if err != nil {
		return "", err
	}

	return upload.Location, nil
}

func validateMimeType(fileInfo UploadInfo) error {
	if fileInfo.MultipartFileHeader == nil {
		return nil
	}

	mimeType := fileInfo.MultipartFileHeader.Header.Get("Content-Type")

	switch fileInfo.TypeData {
	case "image":
		return validateImageMimeType(mimeType)
	case "attachment":
		return validateAttachmentMimeType(mimeType)
	}

	return nil
}

func validateImageMimeType(mimeType string) error {
	if !strings.HasPrefix(mimeType, "image/") {
		return errors.New("only image file allowed")
	}
	return nil
}

func validateAttachmentMimeType(mimeType string) error {
	forbiddenMimeTypes := getForbiddenMimeTypes()
	for _, forbiddenType := range forbiddenMimeTypes {
		if strings.Contains(mimeType, forbiddenType) {
			return errors.New("file not allowed")
		}
	}
	return nil
}

func getForbiddenMimeTypes() []string {
	return []string{
		"application/vnd.android.package-archive",
		"video/",
		"application/x-msdos-program",
		"application/octet-stream",
		"application/x-apple-diskimage",
		"application/json",
	}
}

// Function delete file from S3 AWS
func DeleteFileFromAWS(ctx context.Context, fileInfo UploadInfo) error {
	// Load env AWS*
	cfg, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		return errors.New("failed load env aws")
	}

	// Create an Amazon S3 service client
	client := s3.NewFromConfig(cfg)

	// Delete object
	_, err = client.DeleteObject(ctx, &s3.DeleteObjectInput{
		Bucket: aws.String(os.Getenv(`ENVIRONMENT`)),
		Key:    aws.String(fileInfo.FileName),
	})
	if err != nil {
		return err
	}

	return nil
}

func SignedURL(objectPath string, expiredDuration time.Duration) (string, error) {
	objectPath = decodeEncodedFilename(objectPath)
	bucket := os.Getenv(`ENVIRONMENT`)
	var err error

	// Initialize the GCS client
	ctx := context.Background()

	storageClient, err = storage.NewClient(ctx, option.WithCredentialsFile(keys.GoogleKey()))
	if err != nil {
		return "", fmt.Errorf(err.Error())
	}

	var cred, _ = loadCredentials("./config/hydra_credential.json")

	defer storageClient.Close()

	signedURLOptions := &storage.SignedURLOptions{
		Method:         "GET",
		Expires:        time.Now().Add(expiredDuration),
		GoogleAccessID: cred.ClientEmail,
		PrivateKey:     []byte(cred.PrivateKey),
	}

	// Generate the signed URL
	signedURL, err := storage.SignedURL(bucket, objectPath, signedURLOptions)

	if err != nil {
		return "", err
	}

	return signedURL, nil

}

func loadCredentials(configPath string) (*GoogleCredentials, error) {
	data, err := ioutil.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("unable to read credentials file: %v", err)
	}

	var creds GoogleCredentials
	if err = json.Unmarshal(data, &creds); err != nil {
		return nil, fmt.Errorf("unable to unmarshal credentials: %v", err)
	}

	return &creds, nil
}
