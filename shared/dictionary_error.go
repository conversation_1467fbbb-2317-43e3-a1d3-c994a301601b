package shared

import (
	"fmt"

	"github.com/Lionparcel/hydra/src/model"
)

func NewDictionaryError() DictionaryError {
	return &dictionaryError{}
}

type DictionaryError interface {
	ErrDB() *MultiStringBadRequestError
	ErrDBNotFound(detail string) *MultiStringBadRequestError
	ErrInvalidFormat(detail string) *MultiStringBadRequestError
	MalformatRequest() *MultiStringBadRequestError
	ErrRequestHttp() *MultiStringBadRequestError
	ErrFailedToRetrieveData(param string) *MultiStringBadRequestError
	ProductInactive() *MultiStringValidationError
	DataNotFound() *MultiStringBadRequestError
	ErrFromExternalService() *MultiStringBadRequestError
	RequiredParam(param string) *MultiStringBadRequestError

	// string return
	ErrDataNotFoundString(countryID, param string) string
	ErrInterpackOnlyAllowed(countryID string) string
	ErrSTTCannotBeProccessed(countryID string) string
	ErrStatusNotValidWithStatusSuggest(CountryCode, param string) string
	ErrSTTCannotBeProccessedSTITwice(CountryCode string) string
}

type dictionaryError struct{}

func (d *dictionaryError) ErrDB() *MultiStringBadRequestError {
	return NewMultiStringBadRequestError(HTTPErrorBadRequest, map[string]string{
		"en": "An error occurred while querying db",
		"id": "Terjadi kesalahan pada saat query db",
	})
}

func (d *dictionaryError) MalformatRequest() *MultiStringBadRequestError {
	return NewMultiStringBadRequestError(HTTPErrorBadRequest, map[string]string{
		"en": "Malformat request",
		"id": "Permintaan tidak benar",
	})
}

func (d *dictionaryError) ErrDBNotFound(param string) *MultiStringBadRequestError {
	return NewMultiStringBadRequestError(HTTPErrorBadRequest, map[string]string{
		"en": fmt.Sprintf("%s not found", param),
		"id": fmt.Sprintf("%s tidak ditemukan", param),
	})
}

func (d *dictionaryError) ErrInvalidFormat(detail string) *MultiStringBadRequestError {
	return NewMultiStringBadRequestError(HTTPErrorUnprocessableEntity, map[string]string{
		"en": fmt.Sprintf("%s invalid format", detail),
		"id": fmt.Sprintf("Format %s tidak benar", detail),
	})
}

func (d *dictionaryError) ErrFailedToRetrieveData(param string) *MultiStringBadRequestError {
	return NewMultiStringBadRequestError(HTTPErrorBadRequest, map[string]string{
		"en": fmt.Sprintf("Failed to retreive %s", param),
		"id": fmt.Sprintf("Gagal mendapatkan %s", param),
	})
}

func (d *dictionaryError) ErrRequestHttp() *MultiStringBadRequestError {
	return NewMultiStringBadRequestError(HTTPErrorBadRequest, map[string]string{
		"en": "An error occurred while http request",
		"id": "Terjadi kesalahan pada saat request http",
	})
}

func (d *dictionaryError) ProductInactive() *MultiStringValidationError {
	return NewMultiStringValidationError(HTTPErrorUnprocessableEntity, map[string]string{
		"en": "Product Type is inactive",
		"id": "Product Type sedang tidak aktif",
	})
}

func (d *dictionaryError) RequiredParam(param string) *MultiStringBadRequestError {
	return NewMultiStringBadRequestError(HTTPErrorBadRequest, map[string]string{
		"en": fmt.Sprintf("%s is required", param),
		"id": fmt.Sprintf("%s harus diisi", param),
	})
}

func (d *dictionaryError) DataNotFound() *MultiStringBadRequestError {
	return NewMultiStringBadRequestError(HTTPErrorBadRequest, map[string]string{
		"en": "Data not found",
		"id": "Data tidak ditemukan",
	})
}

func (d *dictionaryError) ErrFromExternalService() *MultiStringBadRequestError {
	return NewMultiStringBadRequestError(HTTPErrorBadRequest, map[string]string{
		"en": "An error occurred while connect to external",
		"id": "Terjadi kesalahan pada saat menghubungkan ke layanan eksternal",
	})
}

func (d *dictionaryError) ErrDataNotFoundString(CountryCode, param string) string {
	if CountryCode != model.CountryID {
		return d.ErrDBNotFound(param).Message()["en"]
	}

	if param == `STT` {
		return fmt.Sprintf("%s Tidak dapat ditemukan", param)
	}

	return d.ErrDBNotFound(param).Message()["id"]
}

func (d *dictionaryError) ErrInterpackOnlyAllowed(CountryCode string) string {
	if CountryCode == model.CountryID {
		return "Hanya untuk jenis pengiriman INTERPACK. Cek & atur ulang"
	}
	return "Only for INTERPACK shipping types. Check and reset"
}

func (d *dictionaryError) ErrSTTCannotBeProccessed(CountryCode string) string {
	if CountryCode == model.CountryID {
		return "STT tidak dapat diproses"
	}
	return "STT cannot be processed"
}

func (d *dictionaryError) ErrStatusNotValidWithStatusSuggest(CountryCode, param string) string {
	if CountryCode == model.CountryID {
		return fmt.Sprintf("Status tidak valid. Silahkan pilih status “%s”", param)
	}
	return fmt.Sprintf("Invalid status. Please select “%s” status", param)
}

func (d *dictionaryError) ErrSTTCannotBeProccessedSTITwice(CountryCode string) string {
	if CountryCode == model.CountryID {
		return "STT Tidak dapat diproses STI 2 kali"
	}
	return "STT Cannot be processed STI 2 times"
}
