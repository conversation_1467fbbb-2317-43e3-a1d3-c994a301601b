package shared

import (
	"fmt"
	"strings"
	"time"
)

const (
	// FormatDateTime ...
	FormatDateTime = `2006-01-02 15:04:05`
	// FormatDateTime ...
	FormatDateDDMMYY      = `02/01/06`
	FormatDateHourMinutes = `2006-01-02 15:04`
	// FormatDateTime ...
	FormatDateTimeWithTimeZone = `2006-01-02 15:04:05 -0700 MST`
	// AlgoFormatDateTime ...
	AlgoFormatDateTime = "2006-01-02T15:04:05Z07"
	// NgenFormatDateTimeZone ...
	NgenFormatDateTimeZone = "2006-01-02T15:04:05Z07:00"
	// FormatDate ...
	FormatDate = `2006-01-02`
	// FormatDateDDMMYYY
	FormatDateDDMMYYY = `02-01-2006`
	// DateMonthFormat
	DateMonthFormat = `02 January 2006`
	// DateMonthFormat
	DateMonthFormatWihtoutYear = `02 January`
	// FormatDateMonthYearHourMinute
	FormatDateMonthYearHourMinute = `02-Jan-2006 15:04Z07:00`
	// FormatLocalTime
	FormatLocalTime               = `02-Jan-2006`
	FormatLocalTimeDDsMMsYYYY     = `02 Jan 2006`
	FormatLocalTimeDDdashMMdashYY = `02-Jan-06`
	//FormatDateForUpdateAccount
	FormatDateForUpdateAccount = `2006-01-02T15:04:05+07:00`
	// FormatTimeMinute ...
	FormatTimeMinute = `15:04`
	// FormatTimeMinuteSecond ...
	FormatTimeMinuteSecond = `15:04:05`

	// FormatDateConcise
	FormatDateConcise = `02-Jan-06`

	FormatTimeStampSF = `2006-01-02T15:04:05.999-0700`
	FormatTimestampTZ = `2006-01-02T15:04:05-0700`

	// AsiaJakarta ...
	AsiaJakarta = `Asia/Jakarta`
	// AsiaMakassar ...
	AsiaMakassar = `Asia/Makassar`
	// AsiaJayapura ...
	AsiaJayapura = `Asia/Jayapura`
	// UTC
	UTC = `UTC`

	// indonesia country timezone in table city
	// GMT7 ...
	GMT7 = `GMT+7:00`
	// GMT8 ...
	GMT8 = `GMT+8:00`
	// GMT9 ...
	GMT9 = `GMT+9:00`

	// GMT07
	GMT07 = `+07:00`

	// Month
	January   = `January`
	February  = `February`
	March     = `March`
	April     = `April`
	May       = `May`
	June      = `June`
	July      = `July`
	August    = `August`
	September = `September`
	October   = `October`
	November  = `November`
	December  = `December`
)

var (
	// loc
	loc, _ = time.LoadLocation(AsiaJakarta)

	// MappingTimzoneToTimeLocation ...
	MappingTimzoneToTimeLocation = map[string]string{
		GMT7: AsiaJakarta,
		GMT8: AsiaMakassar,
		GMT9: AsiaJayapura,
	}

	// Month to Bulan
	MappingMonthToBulan = map[string]string{
		January:   `Januari`,
		February:  `Februari`,
		March:     `Maret`,
		April:     `April`,
		May:       `Mei`,
		June:      `Juni`,
		July:      `Juli`,
		August:    `Agustus`,
		September: `September`,
		October:   `Oktober`,
		November:  `November`,
		December:  `Desember`,
	}

	MappingTimeMonthToBulan = map[time.Month]string{
		time.January:   `Januari`,
		time.February:  `Februari`,
		time.March:     `Maret`,
		time.April:     `April`,
		time.May:       `Mei`,
		time.June:      `Juni`,
		time.July:      `Juli`,
		time.August:    `Agustus`,
		time.September: `September`,
		time.October:   `Oktober`,
		time.November:  `November`,
		time.December:  `Desember`,
	}
)

type GroupDate struct {
	StartDate time.Time
	EndDate   time.Time
}

// check date equal
func CheckDateEqual(today, newDate time.Time) bool {
	return today.Format("20060102") == newDate.Format("20060102")
}

// check time valid
func CheckTimeValid(date time.Time) error {
	today := time.Now()
	hours, minutes, _ := today.Clock()
	newHours, newMinutes, _ := date.Clock()

	todayTime := hours*60 + minutes
	dateTime := newHours*60 + newMinutes

	if todayTime > dateTime {
		return fmt.Errorf("Waktu melewati waktu perilisan")
	}

	if dateTime-todayTime < 10 {
		return fmt.Errorf("Waktu melewati batas perubahan data")
	}

	return nil
}

func CombineDateTime(date, dateTime string) (time.Time, error) {
	return time.Parse("2006-01-02 15:04:05", date+" "+dateTime)
}

// SetToLateNight receive date parameter value "YYYY-MM-DD"
func SetToLateNight(date string) (time.Time, error) {
	parsed, err := time.Parse("2006-01-02", date)
	if err != nil {
		return time.Time{}, err
	}
	return time.Date(parsed.Year(), parsed.Month(), parsed.Day(), 23, 59, 59, 0, parsed.Location()), nil
}

// CheckDateInput for validation
func CheckDateInput(date string) (bool, error) {

	tstart, err := SetToLateNight(time.Now().Format("2006-01-02"))
	if err != nil {
		return false, fmt.Errorf("cannot parse startdate: %v", err)
	}
	tend, err := SetToLateNight(date)
	if err != nil {
		return false, fmt.Errorf("cannot parse date: %v", err)
	}

	if tstart.After(tend) {
		return false, fmt.Errorf("Tanggal Expired tidak boleh kurang dari tanggal sekarang")
	}
	return true, err
}

// UTC7 ...
func UTC7(t time.Time) time.Time {
	location, err := time.LoadLocation("Asia/Jakarta")
	if err != nil {
		return time.Now()
	}
	return t.In(location)
}

// UTC7 ...
func ForceUTC7(t time.Time) time.Time {
	location, err := time.LoadLocation("Asia/Jakarta")
	if err != nil {
		return time.Now()
	}
	return t.In(location).Add(-7 * time.Hour)
}

// UTC0 ...
func UTC0(t time.Time) time.Time {
	location, err := time.LoadLocation("UTC")
	if err != nil {
		return time.Now()
	}
	return t.In(location)
}

func NowUTC7() time.Time {
	return time.Now().In(loc)
}

// ParseUTC7 ...
func ParseUTC7(timeFormat string, value string) (time.Time, error) {
	timeUTC7, err := time.ParseInLocation(timeFormat, value, loc)
	if err != nil {
		return time.Now(), err
	}

	return timeUTC7, nil
}

// this function will parse the original time to Asia/Jakarta location
func ParseUTC7V(timeFormat string, value string) (time.Time, error) {
	t, err := time.Parse(timeFormat, value)
	if err != nil {
		return time.Now(), err
	}

	return t.In(loc), nil
}

func ParseTimeToMillisecond(t time.Time) int64 {
	return t.UnixNano() / int64(time.Millisecond)
}

func ParseMillisecondToTime(t int64) time.Time {
	return time.Unix(0, t*int64(time.Millisecond))
}

// ParseInLocation ...
func ParseInLocation(timeFormat, value, location string) (time.Time, error) {
	locc, err := time.LoadLocation(location)
	if err != nil {
		return time.Now(), err
	}

	timeLocal, err := time.ParseInLocation(timeFormat, value, locc)
	if err != nil {
		return time.Now(), err
	}

	return timeLocal, nil
}

func ParseTimeWithLocation(t, layout, location string) (time.Time, error) {
	datetime, err := time.Parse(layout, t)
	if err != nil {
		return time.Now(), err
	}

	locc, err := time.LoadLocation(location)
	if err != nil {
		return time.Now(), err
	}

	return datetime.In(locc), nil
}

func GetTimeLocal(data string) string {
	t, _ := time.Parse(FormatDate, data)
	return t.Format(FormatLocalTime)
}

// it will return 2009-11-10 00:00:00
func StartDate(t time.Time) time.Time {
	time, _ := ParseUTC7(FormatDate, t.Format(FormatDate))
	return time
}

func EndDate(t time.Time) time.Time {
	// it will return ex 2009-11-10 23:59:00
	return StartDate(t).Add(23 * time.Hour).Add(59 * time.Minute).Add(59 * time.Second)
}

// it will return ex 2009-11-10 23:59:59.999999999
func EndDateNano(t time.Time) time.Time {
	return StartDate(t).Add(23 * time.Hour).Add(59 * time.Minute).Add(59 * time.Second).Add(999 * time.Millisecond).Add(999 * time.Microsecond).Add(999 * time.Nanosecond)
}

func StartDateString(t string) time.Time {
	// it will return 2009-11-10 00:00:00
	time, _ := ParseUTC7(FormatDate, t)
	return time
}

func EndDateString(t string) time.Time {
	// it will return ex 2009-11-10 23:59:59
	return StartDateString(t).Add(23 * time.Hour).Add(59 * time.Minute).Add(59 * time.Second)
}

func StartHalfDay(t time.Time, isAfterNoon bool) time.Time {
	// it will return 2009-11-10 00:00:00
	start, _ := ParseUTC7(FormatDate, t.Format(FormatDate))

	if isAfterNoon {
		// it will return 2009-11-10 12:00:01
		return start.Add(12 * time.Hour).Add(00 * time.Minute).Add(01 * time.Second)
	}
	return start
}

func EndHalfDay(t time.Time, isAfterNoon bool) time.Time {

	if isAfterNoon {
		// it will return ex 2009-11-10 23:59:59
		return StartDate(t).Add(23 * time.Hour).Add(59 * time.Minute).Add(59 * time.Second)
	}

	// it will return ex 2009-11-10 12:00:00
	return StartDate(t).Add(12 * time.Hour).Add(00 * time.Minute).Add(00 * time.Second)

}

func StartDayDividedByEight(t time.Time, partOfTheDay int) time.Time {
	// it will return 2009-11-10 00:00:00
	start, _ := ParseUTC7(FormatDate, t.Format(FormatDate))

	switch partOfTheDay {
	case 1:
		// it will return 2009-11-10 03:00:01
		return start.Add(3 * time.Hour).Add(00 * time.Minute).Add(01 * time.Second)
	case 2:
		// it will return 2009-11-10 06:00:01
		return start.Add(6 * time.Hour).Add(00 * time.Minute).Add(01 * time.Second)
	case 3:
		// it will return 2009-11-10 08:00:01
		return start.Add(9 * time.Hour).Add(00 * time.Minute).Add(01 * time.Second)
	case 4:
		// it will return 2009-11-10 12:00:01
		return start.Add(12 * time.Hour).Add(00 * time.Minute).Add(01 * time.Second)
	case 5:
		// it will return 2009-11-10 15:00:01
		return start.Add(15 * time.Hour).Add(00 * time.Minute).Add(01 * time.Second)
	case 6:
		// it will return 2009-11-10 18:00:01
		return start.Add(18 * time.Hour).Add(00 * time.Minute).Add(01 * time.Second)
	case 7:
		// it will return 2009-11-10 21:00:01
		return start.Add(21 * time.Hour).Add(00 * time.Minute).Add(01 * time.Second)
	}

	return start
}

func EndDayDividedByEight(t time.Time, partOfTheDay int) time.Time {

	switch partOfTheDay {
	case 1:
		// it will return ex 2009-11-10 06:00:00
		return StartDate(t).Add(6 * time.Hour).Add(00 * time.Minute).Add(00 * time.Second).Add(999 * time.Millisecond).Add(999 * time.Microsecond).Add(999 * time.Nanosecond)
	case 2:
		// it will return ex 2009-11-10 09:00:00
		return StartDate(t).Add(9 * time.Hour).Add(00 * time.Minute).Add(00 * time.Second).Add(999 * time.Millisecond).Add(999 * time.Microsecond).Add(999 * time.Nanosecond)
	case 3:
		// it will return ex 2009-11-10 12:00:00
		return StartDate(t).Add(12 * time.Hour).Add(00 * time.Minute).Add(00 * time.Second).Add(999 * time.Millisecond).Add(999 * time.Microsecond).Add(999 * time.Nanosecond)
	case 4:
		// it will return ex 2009-11-10 15:00:00
		return StartDate(t).Add(15 * time.Hour).Add(00 * time.Minute).Add(00 * time.Second).Add(999 * time.Millisecond).Add(999 * time.Microsecond).Add(999 * time.Nanosecond)
	case 5:
		// it will return ex 2009-11-10 18:00:00
		return StartDate(t).Add(18 * time.Hour).Add(00 * time.Minute).Add(00 * time.Second).Add(999 * time.Millisecond).Add(999 * time.Microsecond).Add(999 * time.Nanosecond)
	case 6:
		// it will return ex 2009-11-10 21:00:00
		return StartDate(t).Add(21 * time.Hour).Add(00 * time.Minute).Add(00 * time.Second).Add(999 * time.Millisecond).Add(999 * time.Microsecond).Add(999 * time.Nanosecond)
	case 7:
		// it will return ex 2009-11-10 23:59:59
		return StartDate(t).Add(23 * time.Hour).Add(59 * time.Minute).Add(59 * time.Second).Add(999 * time.Millisecond).Add(999 * time.Microsecond).Add(999 * time.Nanosecond)
	}

	// it will return ex 2009-11-10 03:00:00
	return StartDate(t).Add(3 * time.Hour).Add(00 * time.Minute).Add(00 * time.Second).Add(999 * time.Millisecond).Add(999 * time.Microsecond).Add(999 * time.Nanosecond)

}

func ValidTimePointer(t *time.Time) time.Time {
	if t == nil {
		return time.Time{}
	}
	return *t
}

// rangeDate returns a date range function over start date to end date inclusive.
// After the end of the range, the range function returns a zero date,
// date.IsZero() is true.
func RangeDate(start, end time.Time) func() time.Time {
	y, m, d := start.Date()
	start = time.Date(y, m, d, 0, 0, 0, 0, time.UTC)
	y, m, d = end.Date()
	end = time.Date(y, m, d, 0, 0, 0, 0, time.UTC)

	return func() time.Time {
		if start.After(end) {
			return time.Time{}
		}
		date := start
		start = start.AddDate(0, 0, 1)
		return date
	}
}

func BeginningOfTheMonth(t time.Time) time.Time {
	y, m, _ := t.Date()
	return time.Date(y, m, 1, 0, 0, 0, 0, loc)
}

func EndOfTheMonth(t time.Time) time.Time {
	return BeginningOfTheMonth(t).AddDate(0, 1, 0).Add(-time.Second)
}

func StartDayDividedByFour(t time.Time, partOfTheDay int) time.Time {
	// it will return 2009-11-10 00:00:00
	start, _ := ParseUTC7(FormatDate, t.Format(FormatDate))

	switch partOfTheDay {
	case 1:
		// it will return 2009-11-10 06:00:01
		return start.Add(6 * time.Hour).Add(00 * time.Minute).Add(01 * time.Second)
	case 2:
		// it will return 2009-11-10 12:00:01
		return start.Add(12 * time.Hour).Add(00 * time.Minute).Add(01 * time.Second)
	case 3:
		// it will return 2009-11-10 18:00:01
		return start.Add(18 * time.Hour).Add(00 * time.Minute).Add(01 * time.Second)
	}

	return start
}

func EndDayDividedByFour(t time.Time, partOfTheDay int) time.Time {

	switch partOfTheDay {
	case 1:
		// it will return ex 2009-11-10 12:00:00.999999999
		return StartDate(t).Add(12 * time.Hour).Add(00 * time.Minute).Add(00 * time.Second).Add(999 * time.Millisecond).Add(999 * time.Microsecond).Add(999 * time.Nanosecond)
	case 2:
		// it will return ex 2009-11-10 18:00:00.999999999
		return StartDate(t).Add(18 * time.Hour).Add(00 * time.Minute).Add(00 * time.Second).Add(999 * time.Millisecond).Add(999 * time.Microsecond).Add(999 * time.Nanosecond)
	case 3:
		// it will return ex 2009-11-10 23:59:59.999999999
		return StartDate(t).Add(23 * time.Hour).Add(59 * time.Minute).Add(59 * time.Second).Add(999 * time.Millisecond).Add(999 * time.Microsecond).Add(999 * time.Nanosecond)
	}

	// it will return ex 2009-11-10 06:00:00.999999999
	return StartDate(t).Add(6 * time.Hour).Add(00 * time.Minute).Add(00 * time.Second).Add(999 * time.Millisecond).Add(999 * time.Microsecond).Add(999 * time.Nanosecond)

}

// will return 01 January - 01 January 2022
func FormatStartEndDate(startPeriod time.Time, endPeriod time.Time) string {
	return fmt.Sprintf("%d %s - %d %s %d",
		startPeriod.Day(), startPeriod.Format("January"),
		endPeriod.Day(), endPeriod.Format("January"), endPeriod.Year())
}

func GetTotalMonthInRange(startDate time.Time, endDate time.Time) int {
	z := int(endDate.Month()) - int(startDate.Month())

	return (endDate.Year()-startDate.Year())*12 + z + 1
}

// CAUTION
// ensure that the startDate is <= endDate
func GenerateGroupDate(startDate time.Time, endDate time.Time) []GroupDate {
	groupDate := []GroupDate{}
	start := StartDate(startDate)
	end := EndDateNano(endDate)
	d := end.Sub(start)

	totalDays := d.Hours() / 24
	totalMonths := GetTotalMonthInRange(startDate, endDate)

	limitStart := start
	switch {
	case totalDays <= 7: // <7 hari ---> 1 bar = 1 hari
		for limitStart.Before(end) || limitStart.Equal(end) {
			groupDate = append(groupDate, GroupDate{
				StartDate: limitStart,
				EndDate:   EndDateNano(limitStart),
			})
			limitStart = limitStart.AddDate(0, 0, 1)
		}
	case totalDays > 7 && totalDays < 49: // >7 hari --> 1 bar = 1 minggu
		for {
			limitStart = limitStart.AddDate(0, 0, 6)
			if limitStart.Before(end) || limitStart.Equal(end) {
				groupDate = append(groupDate, GroupDate{
					StartDate: limitStart.AddDate(0, 0, -6),
					EndDate:   EndDateNano(limitStart),
				})
				limitStart = limitStart.AddDate(0, 0, 1)
				continue
			}

			if limitStart.After(end) && limitStart.AddDate(0, 0, -6).Before(end) {
				groupDate = append(groupDate, GroupDate{
					StartDate: limitStart.AddDate(0, 0, -6),
					EndDate:   end,
				})
				break
			}

			break
		}
	case totalDays >= 49 && totalMonths <= 7: // >7 minggu --> 1 bar = 1 bulan
		startTemp := time.Date(start.Year(), start.Month(), 1, 0, 0, 0, 0, loc)
		firstLoop := true
		for {
			startTemp = startTemp.AddDate(0, 1, 0)
			limitStart = limitStart.AddDate(0, 1, 0)
			if startTemp.Before(end) || startTemp.Equal(end) {
				if firstLoop {
					groupDate = append(groupDate, GroupDate{
						StartDate: limitStart.AddDate(0, -1, 0),
						EndDate:   EndDateNano(startTemp.AddDate(0, 0, -1)),
					})

					firstLoop = false
					continue
				}

				groupDate = append(groupDate, GroupDate{
					StartDate: startTemp.AddDate(0, -1, 0),
					EndDate:   EndDateNano(startTemp.AddDate(0, 0, -1)),
				})
				continue
			}

			if startTemp.After(end) && startTemp.AddDate(0, -1, 0).Before(end) {
				groupDate = append(groupDate, GroupDate{
					StartDate: startTemp.AddDate(0, -1, 0),
					EndDate:   end,
				})
				break
			}
			break
		}
	case totalDays >= 49 && totalMonths > 7: // >7 bulan --> 1 bar = 3 bulan
		startTemp := time.Date(start.Year(), start.Month(), 1, 0, 0, 0, 0, loc)
		firstLoop := true
		for {
			startTemp = startTemp.AddDate(0, 3, 0)
			limitStart = limitStart.AddDate(0, 3, 0)
			if startTemp.Before(end) || startTemp.Equal(end) {
				if firstLoop {
					groupDate = append(groupDate, GroupDate{
						StartDate: limitStart.AddDate(0, -3, 0),
						EndDate:   EndDateNano(startTemp.AddDate(0, 0, -1)),
					})

					firstLoop = false
					continue
				}

				groupDate = append(groupDate, GroupDate{
					StartDate: startTemp.AddDate(0, -3, 0),
					EndDate:   EndDateNano(startTemp.AddDate(0, 0, -1)),
				})
				continue
			}

			if startTemp.After(end) && startTemp.AddDate(0, -3, 0).Before(end) {
				groupDate = append(groupDate, GroupDate{
					StartDate: startTemp.AddDate(0, -3, 0),
					EndDate:   end,
				})
				break
			}
			break
		}

	}

	return groupDate
}

// TimeMinuteIsBetween ...
func TimeMinuteIsBetween(between, start, end time.Time) bool {
	between, _ = ParseUTC7(FormatTimeMinute, between.Format(FormatTimeMinute))
	start, _ = ParseUTC7(FormatTimeMinute, start.Format(FormatTimeMinute))
	end, _ = ParseUTC7(FormatTimeMinute, end.Format(FormatTimeMinute))

	return (between.Equal(start) || between.After(start)) && (between.Equal(end) || between.Before(end))
}

func GenerateTimeByInterval(startDate, endDate time.Time) []string {
	res := []string{}
	for rd := rangeDate(startDate, endDate); ; {
		date := rd()
		if date.IsZero() {
			break
		}
		res = append(res, date.Format("2006-01-02"))
	}
	return res
}

func rangeDate(start, end time.Time) func() time.Time {
	y, m, d := start.Date()
	start = time.Date(y, m, d, 0, 0, 0, 0, time.UTC)
	y, m, d = end.Date()
	end = time.Date(y, m, d, 0, 0, 0, 0, time.UTC)

	return func() time.Time {
		if start.After(end) {
			return time.Time{}
		}
		date := start
		start = start.AddDate(0, 0, 1)
		return date
	}
}

// convert data pointer time to string
func PointerTimeToString(param *time.Time, formatDate string) string {
	res := ""
	if param != nil {
		res = param.Format(formatDate)
	}
	return res
}

// convert data pointer time to string
func TimeToString(param time.Time, formatDate string) string {
	res := ""
	if !param.IsZero() {
		res = param.Format(formatDate)
	}
	return res
}

func GetTimeDate(t time.Time) time.Time {
	y, m, d := t.Date()
	t = time.Date(y, m, d, 0, 0, 0, 0, time.UTC)

	return t
}

// PowDuration Fungsi untuk menghitung pangkat dari time.Duration
func PowDuration(base time.Duration, exponent, limit int) time.Duration {
	for i := 0; i < limit; i++ {
		base = base * time.Duration(exponent)
	}
	return base
}

type DateRange struct {
	Start time.Time
	End   time.Time
}

// SplitDateRange splits a date range into two ranges based on a split date.
// If the split date is before the range, the first range will be nil, and the second will cover the entire range.
// If the split date is after the range, the first range will cover the entire range, and the second will be nil.
// example; https://go.dev/play/p/4uPqBFIMuDi
func SplitDateRange(start, end, split time.Time) (*DateRange, *DateRange) {
	// If the split date is before the start date, return the full range as range2 and nil for range1
	if split.Before(start) {
		return nil, &DateRange{Start: start, End: end}
	}

	// If the split date is after the end date, return the full range as range1 and nil for range2
	if split.After(end) {
		return &DateRange{Start: start, End: end}, nil
	}

	// Otherwise, split the range into two valid ranges
	var range1, range2 *DateRange

	// First range: from start to split date
	range1 = &DateRange{
		Start: start,
		End:   split,
	}

	// Second range: from the day after the split date to the end date
	if end.After(split) {
		range2 = &DateRange{
			Start: split.Add(time.Second), // +1 second
			End:   end,
		}
	}

	return range1, range2
}

// IsToday checks if the given date is today
func IsToday(inputDate time.Time) bool {
	// Get the current date
	now := time.Now()

	// Extract the year, month, and day from the current date and input date
	currentYear, currentMonth, currentDay := now.Date()
	inputYear, inputMonth, inputDay := inputDate.Date()

	// Compare the dates
	return currentYear == inputYear && currentMonth == inputMonth && currentDay == inputDay
}

// IsToday checks if the given date is today
func IsLessThanToday(inputDate time.Time) bool {
	now := time.Now().Truncate(24 * time.Hour)

	input := inputDate.Truncate(24 * time.Hour)

	return input.Before(now)
}

func GetNextDay(currentDay string) string {
	daysOfWeekAndNext := map[string]string{
		"monday":    "tuesday",
		"tuesday":   "wednesday",
		"wednesday": "thursday",
		"thursday":  "friday",
		"friday":    "saturday",
		"saturday":  "sunday",
		"sunday":    "monday",
	}
	return daysOfWeekAndNext[strings.ToLower(currentDay)]
}

func GetNextDateForDay(now time.Time, targetDay string) time.Time {
	currentDay := strings.ToLower(now.Weekday().String())

	// If the target day is the same as current day, return next week
	if currentDay == targetDay {
		return now.AddDate(0, 0, 7)
	}

	// Days of the week in order
	days := []string{
		"sunday",
		"monday",
		"tuesday",
		"wednesday",
		"thursday",
		"friday",
		"saturday",
	}

	// Find current and target day indices
	currentIndex := -1
	targetIndex := -1
	for i, day := range days {
		if day == currentDay {
			currentIndex = i
		}
		if day == targetDay {
			targetIndex = i
		}
	}

	// Calculate days to add
	daysToAdd := (targetIndex - currentIndex + 7) % 7
	if daysToAdd == 0 {
		daysToAdd = 7
	}

	return now.AddDate(0, 0, daysToAdd)
}

func GetDiffDays(t1 time.Time, t2 time.Time) int {
	t1 = time.Date(t1.Year(), t1.Month(), t1.Day(), 0, 0, 0, 0, t1.Location())
	t2 = time.Date(t2.Year(), t2.Month(), t2.Day(), 0, 0, 0, 0, t2.Location())

	return int(t2.Sub(t1).Hours() / 24)
}
