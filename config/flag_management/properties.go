package flag_management

import "github.com/rollout/rox-go/v5/server"

type FlagsModel struct {
	CloudBees   *FlagsCloudBeesModel
	OpenFeature *OpenFeatureFlag
}

type FlagsCloudBeesModel struct {
	EnableMappingS3Link                   server.RoxFlag
	IsSendMessageDELAfterTransferDelivery server.RoxFlag
	EnableBookingInterpack                server.RoxFlag
	WebURL                                server.RoxString
	RTSHQDefaultName                      server.RoxString
	RTSHQDefaultAddress                   server.RoxString
	RTSHQDefaultPhoneNumber               server.RoxString
	RTSHQDefaultDistrictCode              server.RoxString
	RTSHQDefaultAddressType               server.RoxString
	DEXAssessmentDashboardCache           server.RoxInt
	ShipmentCAGrossWeight                 server.RoxFlag
	SamedayCutOffTime                     server.RoxString
	SttHandleMissingStatusDate            server.RoxString
	HoldSttPenaltyAmount                  server.RoxDouble
	HoldSttPenaltyType                    server.RoxString
	HoldSttPenaltyMin                     server.RoxDouble
	HoldSttPenaltyMax                     server.RoxDouble
	DFODPastiProgram                      server.RoxFlag
	CodDfodDayThreshold                   server.RoxInt
	DisableCodDfodForLimitedNumber        server.RoxFlag
}

var flags = &FlagsModel{
	CloudBees: &FlagsCloudBeesModel{
		EnableMappingS3Link:                   server.NewRoxFlag(false),
		IsSendMessageDELAfterTransferDelivery: server.NewRoxFlag(false),
		EnableBookingInterpack:                server.NewRoxFlag(false),
		WebURL:                                server.NewRoxString("https://website-dev.thelionparcel.com", nil),
		RTSHQDefaultName:                      server.NewRoxString("Megahub Lion Parcel (Team Return)", nil),
		RTSHQDefaultAddress:                   server.NewRoxString("Jl. Pembangunan No.9, RT.002/RW.005, Mekarsari, Kec. Neglasari, Kota Tangerang, Banten 15129 - Pergudangan Flexo", nil),
		RTSHQDefaultPhoneNumber:               server.NewRoxString("083879687050", nil),
		RTSHQDefaultDistrictCode:              server.NewRoxString("TNGPCU260", nil),
		RTSHQDefaultAddressType:               server.NewRoxString("office", nil),
		DEXAssessmentDashboardCache:           server.NewRoxInt(15, nil),
		ShipmentCAGrossWeight:                 server.NewRoxFlag(false),
		SamedayCutOffTime:                     server.NewRoxString("", nil),
		SttHandleMissingStatusDate:            server.NewRoxString("", nil),
		HoldSttPenaltyAmount:                  server.NewRoxDouble(0.0, nil),
		HoldSttPenaltyType:                    server.NewRoxString("", nil),
		HoldSttPenaltyMin:                     server.NewRoxDouble(0.0, nil),
		HoldSttPenaltyMax:                     server.NewRoxDouble(0.0, nil),
		DFODPastiProgram:                      server.NewRoxFlag(false),
		CodDfodDayThreshold:                   server.NewRoxInt(3, nil),
		DisableCodDfodForLimitedNumber:        server.NewRoxFlag(false),
	},
}
