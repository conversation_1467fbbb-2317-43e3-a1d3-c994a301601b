package flag_management

import (
	"context"
	"fmt"
	"maps"
	"os"
	"strings"

	"github.com/Lionparcel/go-lptool/v2/lptoken"
	"github.com/Lionparcel/hydra/shared/logger"
	gofeatureflag "github.com/open-feature/go-sdk-contrib/providers/go-feature-flag-in-process/pkg"
	"github.com/open-feature/go-sdk/openfeature"
	"github.com/rollout/cloudbees-openfeature-provider-go/pkg/cloudbees"
	"github.com/rollout/rox-go/v6/server"
	gff "github.com/thomaspoignant/go-feature-flag"
	"github.com/thomaspoignant/go-feature-flag/retriever"
	"github.com/thomaspoignant/go-feature-flag/retriever/githubretriever"
)

// Claims represents the user context for flag evaluation, aliasing JWT claims.
type Claims = lptoken.JWTClaims

// EvaluationOptions contains additional context data for flag evaluation.
// It allows passing custom attributes that can influence flag evaluation logic.
type EvaluationOptions struct {
	AdditionalContext map[string]any
}

// EvaluationOption is a function type that modifies EvaluationOptions.
// It implements the functional options pattern for flexible configuration.
type EvaluationOption func(*EvaluationOptions)

// FlagRequest encapsulates all parameters needed for flag evaluation.
// It groups the flag key and evaluation options into a single structure
// to reduce the number of function parameters.
type FlagRequest struct {
	FlagKey string
	Options []EvaluationOption
}

// OpenFeatureFlag provides feature flag evaluation capabilities using the OpenFeature standard.
// It wraps the OpenFeature client and provides convenient methods for evaluating different
// types of feature flags with user context and additional evaluation options.
type OpenFeatureFlag struct {
	client *openfeature.Client
}

// NewGitHubRetriever creates a new GitHub retriever for fetching feature flag configurations
// from a GitHub repository. It uses environment variables to configure the repository
// connection with fallback defaults based on service configuration.
func NewGitHubRetriever() *githubretriever.Retriever {
	repoSlug := os.Getenv("FLAG_MANAGEMENT_GITHUB_REPO_SLUG")
	if repoSlug == "" {
		repoSlug = "Lionparcel/config-management"
	}

	branch := os.Getenv("FLAG_MANAGEMENT_GITHUB_BRANCH")
	if branch == "" {
		environment := os.Getenv("ENVIRONMENT")
		serviceName := os.Getenv("SERVICE_NAME")
		if environment != "" && serviceName != "" {
			env := strings.Split(environment, "-")
			branch = fmt.Sprintf("flag/%s/%s", env[0], serviceName)
		}
	}

	filePath := os.Getenv("FLAG_MANAGEMENT_GITHUB_FILE_PATH")
	if filePath == "" {
		filePath = "flags.yaml"
	}

	return &githubretriever.Retriever{
		RepositorySlug: repoSlug,
		Branch:         branch,
		FilePath:       filePath,
		GithubToken:    os.Getenv("GITHUB_TOKEN"),
	}
}

// NewOpenFeatureFlag creates a new OpenFeatureFlag instance with the specified retriever.
// The retriever is responsible for fetching feature flag configurations from the source
// (e.g., GitHub repository, configuration service).
//
// It initializes the OpenFeature provider and sets up the client with the service name
// from the SERVICE_NAME environment variable.
func NewOpenFeatureFlag(retriever retriever.Retriever) *OpenFeatureFlag {
	options := gofeatureflag.ProviderOptions{
		GOFeatureFlagConfig: &gff.Config{
			Context:   context.Background(),
			Retriever: retriever,
		},
	}

	provider, err := gofeatureflag.NewProvider(options)
	if err != nil {
		logger.Ef("Failed to create OpenFeature provider:", err)
	}

	openfeature.SetProvider(provider)
	client := openfeature.NewClient(os.Getenv("SERVICE_NAME"))

	return &OpenFeatureFlag{
		client: client,
	}
}

// NewCloudBeesOpenFeatureFlag creates a new OpenFeatureFlag instance using CloudBees provider.
// It uses the CloudBees OpenFeature provider to connect to CloudBees Feature Management
// and evaluates flags using the OpenFeature standard.
//
// Environment variables required:
//   - CLOUDBEES_KEY: The CloudBees environment key
//   - CLOUDBEES_DEV_MODE_KEY: Optional dev mode key for development environments
//   - SERVICE_NAME: The service name used as the OpenFeature client name
func NewCloudBeesOpenFeatureFlag() *OpenFeatureFlag {
	appKey := os.Getenv("CLOUDBEES_KEY")
	devModeKey := os.Getenv("CLOUDBEES_DEV_MODE_KEY")

	options := server.NewRoxOptions(server.RoxOptionsBuilder{
		DevModeKey: devModeKey,
	})
	provider, err := cloudbees.NewProviderWithOptions(appKey, options)

	if err != nil {
		logger.Ef("Failed to create CloudBees OpenFeature provider:", err)
		return nil
	}

	openfeature.SetProvider(provider)
	client := openfeature.NewClient(os.Getenv("SERVICE_NAME"))

	return &OpenFeatureFlag{
		client: client,
	}
}

// WithAdditionalContext returns an EvaluationOption that adds a single key-value pair
// to the evaluation context. This allows passing custom attributes that can be used
// in flag evaluation logic.
//
// Example:
//
//	WithAdditionalContext("region", "US")
//	WithAdditionalContext("userTier", "premium")
func WithAdditionalContext(key string, value any) EvaluationOption {
	return func(opts *EvaluationOptions) {
		ensureAdditionalContext(opts)
		opts.AdditionalContext[key] = value
	}
}

// WithContextMap returns an EvaluationOption that adds multiple key-value pairs
// to the evaluation context. This is useful when you have a map of context data
// to include in flag evaluation.
//
// Example:
//
//	contextData := map[string]any{"region": "US", "tier": "premium"}
//	WithContextMap(contextData)
func WithContextMap(context map[string]any) EvaluationOption {
	return func(opts *EvaluationOptions) {
		ensureAdditionalContext(opts)
		maps.Copy(opts.AdditionalContext, context)
	}
}

// IsFeatureEnabled evaluates a boolean feature flag and returns its value.
// It returns false if the flag doesn't exist, evaluation fails, or any error occurs.
// The user context and additional options are used to determine the flag's value
// based on the configured flag rules.
//
// Example:
//
//	req := FlagRequest{
//	    FlagKey: "new-checkout-flow",
//	    Options: []EvaluationOption{WithAdditionalContext("region", "US")},
//	}
//	enabled := ffs.IsFeatureEnabled(ctx, req)
func (f *OpenFeatureFlag) IsFeatureEnabled(ctx context.Context, req FlagRequest) bool {
	result := f.evaluateFlagWithDefault(ctx, req, false, func(ctx context.Context, key string, evalCtx openfeature.EvaluationContext) (any, error) {
		return f.client.BooleanValue(ctx, key, false, evalCtx)
	})
	return result.(bool)
}

// GetStringFlag evaluates a string feature flag and returns its value.
// If the flag doesn't exist, evaluation fails, or any error occurs, it returns the defaultValue.
// The user context and additional options are used to determine the flag's value
// based on the configured flag rules.
//
// Example:
//
//	req := FlagRequest{
//	    FlagKey: "api-endpoint",
//	    Options: []EvaluationOption{WithAdditionalContext("environment", "staging")},
//	}
//	endpoint := ffs.GetStringFlag(ctx, req, "https://api.production.com")
func (f *OpenFeatureFlag) GetStringFlag(ctx context.Context, req FlagRequest, defaultValue string) string {
	result := f.evaluateFlagWithDefault(ctx, req, defaultValue, func(ctx context.Context, key string, evalCtx openfeature.EvaluationContext) (any, error) {
		return f.client.StringValue(ctx, key, defaultValue, evalCtx)
	})
	return result.(string)
}

// GetIntFlag evaluates an integer feature flag and returns its value.
// If the flag doesn't exist, evaluation fails, or any error occurs, it returns the defaultValue.
// The user context and additional options are used to determine the flag's value
// based on the configured flag rules.
//
// Example:
//
//	req := FlagRequest{
//	    FlagKey: "max-retries",
//	    Options: []EvaluationOption{WithAdditionalContext("service", "payment")},
//	}
//	maxRetries := ffs.GetIntFlag(ctx, req, 3)
func (f *OpenFeatureFlag) GetIntFlag(ctx context.Context, req FlagRequest, defaultValue int) int {
	result := f.evaluateFlagWithDefault(ctx, req, int64(defaultValue), func(ctx context.Context, key string, evalCtx openfeature.EvaluationContext) (any, error) {
		return f.client.IntValue(ctx, key, int64(defaultValue), evalCtx)
	})
	return int(result.(int64))
}

// GetFloatFlag evaluates a float64 feature flag and returns its value.
// If the flag doesn't exist, evaluation fails, or any error occurs, it returns the defaultValue.
// The user context and additional options are used to determine the flag's value
// based on the configured flag rules.
//
// Example:
//
//	req := FlagRequest{
//	    FlagKey: "discount-rate",
//	    Options: []EvaluationOption{WithAdditionalContext("membership", "gold")},
//	}
//	discountRate := ffs.GetFloatFlag(ctx, req, 0.05)
func (f *OpenFeatureFlag) GetFloatFlag(ctx context.Context, req FlagRequest, defaultValue float64) float64 {
	result := f.evaluateFlagWithDefault(ctx, req, defaultValue, func(ctx context.Context, key string, evalCtx openfeature.EvaluationContext) (any, error) {
		return f.client.FloatValue(ctx, key, defaultValue, evalCtx)
	})
	return result.(float64)
}

func (f *OpenFeatureFlag) evaluateFlag(ctx context.Context, req FlagRequest, evaluator func(context.Context, string, openfeature.EvaluationContext) (any, error)) (any, error) {
	opts := f.processOptions(req.Options...)
	evaluationContext := f.createEvaluationContext(f.claimsFromContext(ctx), opts)
	return evaluator(ctx, req.FlagKey, evaluationContext)
}

func (f *OpenFeatureFlag) evaluateFlagWithDefault(ctx context.Context, req FlagRequest, defaultValue any, evaluator func(context.Context, string, openfeature.EvaluationContext) (any, error)) any {
	result, err := f.evaluateFlag(ctx, req, evaluator)
	if err != nil {
		logger.Ef("Error evaluating flag %s: %v", req.FlagKey, err)
		return defaultValue
	}
	return result
}

func (f *OpenFeatureFlag) processOptions(options ...EvaluationOption) *EvaluationOptions {
	opts := &EvaluationOptions{}
	for _, option := range options {
		option(opts)
	}
	return opts
}

func (f *OpenFeatureFlag) createEvaluationContext(account Claims, opts *EvaluationOptions) openfeature.EvaluationContext {
	context := map[string]any{
		"accountName":     account.AccountName,
		"accountRole":     account.AccountRole,
		"partnerType":     account.PartnerType,
		"partnerID":       account.PartnerID,
		"partnerCode":     account.PartnerCode,
		"partnerName":     account.PartnerName,
		"clientID":        account.ClientID,
		"clientCode":      account.ClientCode,
		"clientName":      account.ClientName,
		"username":        account.Username,
		"type":            account.Type,
		"accountRoleName": account.AccountRoleName,
	}

	if opts != nil && opts.AdditionalContext != nil {
		maps.Copy(context, opts.AdditionalContext)
	}

	return openfeature.NewEvaluationContext(
		fmt.Sprintf("account-%d", account.AccountID),
		context,
	)
}

func (f *OpenFeatureFlag) claimsFromContext(ctx context.Context) Claims {
	claims, ok := ctx.Value("flagClaims").(Claims)
	if !ok {
		// Return empty claims if not found - the flag evaluation will handle this gracefully
		claims = Claims{}
	}
	return claims
}

func ensureAdditionalContext(opts *EvaluationOptions) {
	if opts.AdditionalContext == nil {
		opts.AdditionalContext = make(map[string]any)
	}
}
