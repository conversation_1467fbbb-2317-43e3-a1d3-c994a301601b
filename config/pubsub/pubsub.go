package pubsub

import (
	"context"
	"os"

	"cloud.google.com/go/pubsub"
	"github.com/Lionparcel/hydra/config/keys"
	"github.com/Lionparcel/hydra/shared/logger"
	"google.golang.org/api/option"
)

type pubsubClient struct {
	client *pubsub.Client
}

func (c *pubsubClient) Client() *pubsub.Client {
	return c.client
}

// Pubsub abstraction
type Pubsub interface {
	Client() *pubsub.Client
}

func InitPubsub() Pubsub {
	pubsubClient := new(pubsubClient)

	ctx := context.Background()

	// use config with options below for local pubsub emulator
	// client, err := pubsub.NewClient(ctx, os.Getenv(`PUBSUB_PROJECT_ID`), option.WithEndpoint(os.Getenv(`PUBSUB_HOST`)), option.WithoutAuthentication(), option.WithGRPCDialOption(grpc.WithInsecure()))
	// use config with options below for remote pubsub
	client, err := pubsub.NewClient(ctx, os.Getenv(`PUBSUB_PROJECT_ID`), option.WithCredentialsFile(keys.GoogleKey()))
	if err != nil {
		logger.Panic(err)
	}

	pubsubClient.client = client

	return pubsubClient
}
