// Code generated by mockery v2.23.2. DO NOT EDIT.

package mocks

import (
	context "context"

	gopubsub "cloud.google.com/go/pubsub"

	mock "github.com/stretchr/testify/mock"

	pubsub "github.com/Lionparcel/hydra/config/pubsub"
)

// GooglePubsub is an autogenerated mock type for the GooglePubsub type
type GooglePubsub struct {
	mock.Mock
}

// Close provides a mock function with given fields:
func (_m *GooglePubsub) Close() error {
	ret := _m.Called()

	var r0 error
	if rf, ok := ret.Get(0).(func() error); ok {
		r0 = rf()
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PublishMessage provides a mock function with given fields: ctx, topicID, data
func (_m *GooglePubsub) PublishMessage(ctx context.Context, topicID string, data *pubsub.PubSubMessage) (string, error) {
	ret := _m.Called(ctx, topicID, data)

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *pubsub.PubSubMessage) (string, error)); ok {
		return rf(ctx, topicID, data)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, *pubsub.PubSubMessage) string); ok {
		r0 = rf(ctx, topicID, data)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, *pubsub.PubSubMessage) error); ok {
		r1 = rf(ctx, topicID, data)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PullMessage provides a mock function with given fields: ctx, subscriptionID, settings, f
func (_m *GooglePubsub) PullMessage(ctx context.Context, subscriptionID string, settings *gopubsub.ReceiveSettings, f func(context.Context, *gopubsub.Message)) error {
	ret := _m.Called(ctx, subscriptionID, settings, f)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, string, *gopubsub.ReceiveSettings, func(context.Context, *gopubsub.Message)) error); ok {
		r0 = rf(ctx, subscriptionID, settings, f)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewGooglePubsub interface {
	mock.TestingT
	Cleanup(func())
}

// NewGooglePubsub creates a new instance of GooglePubsub. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewGooglePubsub(t mockConstructorTestingTNewGooglePubsub) *GooglePubsub {
	mock := &GooglePubsub{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
