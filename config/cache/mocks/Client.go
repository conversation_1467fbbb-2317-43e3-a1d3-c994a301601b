// Code generated by mockery v2.39.1. DO NOT EDIT.

package mocks

import (
	redis "github.com/go-redis/redis"
	mock "github.com/stretchr/testify/mock"

	time "time"
)

// Client is an autogenerated mock type for the Client type
type Client struct {
	mock.Mock
}

// Decr provides a mock function with given fields: key
func (_m *Client) Decr(key string) (int64, error) {
	ret := _m.Called(key)

	if len(ret) == 0 {
		panic("no return value specified for Decr")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (int64, error)); ok {
		return rf(key)
	}
	if rf, ok := ret.Get(0).(func(string) int64); ok {
		r0 = rf(key)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// <PERSON> provides a mock function with given fields: key
func (_m *Client) Del(key string) (int64, error) {
	ret := _m.Called(key)

	if len(ret) == 0 {
		panic("no return value specified for Del")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (int64, error)); ok {
		return rf(key)
	}
	if rf, ok := ret.Get(0).(func(string) int64); ok {
		r0 = rf(key)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Expire provides a mock function with given fields: key, exp
func (_m *Client) Expire(key string, exp time.Duration) (bool, error) {
	ret := _m.Called(key, exp)

	if len(ret) == 0 {
		panic("no return value specified for Expire")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string, time.Duration) (bool, error)); ok {
		return rf(key, exp)
	}
	if rf, ok := ret.Get(0).(func(string, time.Duration) bool); ok {
		r0 = rf(key, exp)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string, time.Duration) error); ok {
		r1 = rf(key, exp)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Get provides a mock function with given fields: key
func (_m *Client) Get(key string) (string, error) {
	ret := _m.Called(key)

	if len(ret) == 0 {
		panic("no return value specified for Get")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (string, error)); ok {
		return rf(key)
	}
	if rf, ok := ret.Get(0).(func(string) string); ok {
		r0 = rf(key)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// HDel provides a mock function with given fields: key, field
func (_m *Client) HDel(key string, field ...string) (int64, error) {
	_va := make([]interface{}, len(field))
	for _i := range field {
		_va[_i] = field[_i]
	}
	var _ca []interface{}
	_ca = append(_ca, key)
	_ca = append(_ca, _va...)
	ret := _m.Called(_ca...)

	if len(ret) == 0 {
		panic("no return value specified for HDel")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(string, ...string) (int64, error)); ok {
		return rf(key, field...)
	}
	if rf, ok := ret.Get(0).(func(string, ...string) int64); ok {
		r0 = rf(key, field...)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(string, ...string) error); ok {
		r1 = rf(key, field...)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// HGet provides a mock function with given fields: key, field
func (_m *Client) HGet(key string, field string) (string, error) {
	ret := _m.Called(key, field)

	if len(ret) == 0 {
		panic("no return value specified for HGet")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string) (string, error)); ok {
		return rf(key, field)
	}
	if rf, ok := ret.Get(0).(func(string, string) string); ok {
		r0 = rf(key, field)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string, string) error); ok {
		r1 = rf(key, field)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// HSet provides a mock function with given fields: key, field, value
func (_m *Client) HSet(key string, field string, value string) (bool, error) {
	ret := _m.Called(key, field, value)

	if len(ret) == 0 {
		panic("no return value specified for HSet")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, string) (bool, error)); ok {
		return rf(key, field, value)
	}
	if rf, ok := ret.Get(0).(func(string, string, string) bool); ok {
		r0 = rf(key, field, value)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string, string, string) error); ok {
		r1 = rf(key, field, value)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Incr provides a mock function with given fields: key
func (_m *Client) Incr(key string) (int64, error) {
	ret := _m.Called(key)

	if len(ret) == 0 {
		panic("no return value specified for Incr")
	}

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(string) (int64, error)); ok {
		return rf(key)
	}
	if rf, ok := ret.Get(0).(func(string) int64); ok {
		r0 = rf(key)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Keys provides a mock function with given fields: key
func (_m *Client) Keys(key string) ([]string, error) {
	ret := _m.Called(key)

	if len(ret) == 0 {
		panic("no return value specified for Keys")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(string) ([]string, error)); ok {
		return rf(key)
	}
	if rf, ok := ret.Get(0).(func(string) []string); ok {
		r0 = rf(key)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(string) error); ok {
		r1 = rf(key)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Ping provides a mock function with given fields:
func (_m *Client) Ping() (string, error) {
	ret := _m.Called()

	if len(ret) == 0 {
		panic("no return value specified for Ping")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func() (string, error)); ok {
		return rf()
	}
	if rf, ok := ret.Get(0).(func() string); ok {
		r0 = rf()
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func() error); ok {
		r1 = rf()
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Set provides a mock function with given fields: key, value, duration
func (_m *Client) Set(key string, value string, duration time.Duration) (string, error) {
	ret := _m.Called(key, value, duration)

	if len(ret) == 0 {
		panic("no return value specified for Set")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, time.Duration) (string, error)); ok {
		return rf(key, value, duration)
	}
	if rf, ok := ret.Get(0).(func(string, string, time.Duration) string); ok {
		r0 = rf(key, value, duration)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(string, string, time.Duration) error); ok {
		r1 = rf(key, value, duration)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetNX provides a mock function with given fields: key, value, expiration
func (_m *Client) SetNX(key string, value interface{}, expiration time.Duration) (bool, error) {
	ret := _m.Called(key, value, expiration)

	if len(ret) == 0 {
		panic("no return value specified for SetNX")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string, interface{}, time.Duration) (bool, error)); ok {
		return rf(key, value, expiration)
	}
	if rf, ok := ret.Get(0).(func(string, interface{}, time.Duration) bool); ok {
		r0 = rf(key, value, expiration)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string, interface{}, time.Duration) error); ok {
		r1 = rf(key, value, expiration)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SetOnce provides a mock function with given fields: key, value, duration
func (_m *Client) SetOnce(key string, value string, duration time.Duration) (bool, error) {
	ret := _m.Called(key, value, duration)

	if len(ret) == 0 {
		panic("no return value specified for SetOnce")
	}

	var r0 bool
	var r1 error
	if rf, ok := ret.Get(0).(func(string, string, time.Duration) (bool, error)); ok {
		return rf(key, value, duration)
	}
	if rf, ok := ret.Get(0).(func(string, string, time.Duration) bool); ok {
		r0 = rf(key, value, duration)
	} else {
		r0 = ret.Get(0).(bool)
	}

	if rf, ok := ret.Get(1).(func(string, string, time.Duration) error); ok {
		r1 = rf(key, value, duration)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Watch provides a mock function with given fields: fn
func (_m *Client) Watch(fn func(*redis.Tx) error) error {
	ret := _m.Called(fn)

	if len(ret) == 0 {
		panic("no return value specified for Watch")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(func(*redis.Tx) error) error); ok {
		r0 = rf(fn)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewClient creates a new instance of Client. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewClient(t interface {
	mock.TestingT
	Cleanup(func())
}) *Client {
	mock := &Client{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
