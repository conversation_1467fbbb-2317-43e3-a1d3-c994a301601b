package cache

import (
	"crypto/tls"
	"fmt"
	"os"
	"strconv"
	"time"

	"github.com/go-redis/redis"
)

// Conn base struct
type Conn struct {
	Client *redis.Client
}

// ConnectRedis init redis
func ConnectRedis() (Client, error) {
	cl, err := GetRedis(
		os.Getenv("REDIS_HOST"),
		os.Getenv("REDIS_PORT"),
		os.<PERSON><PERSON><PERSON>("REDIS_PASSWORD"),
		os.<PERSON>env("REDIS_DB"),
		os.Getenv("REDIS_TLS"),
	)
	if err != nil {
		return nil, err
	}
	return Conn{
		Client: cl,
	}, nil
}

// Del key
func (r Conn) Del(key string) (int64, error) {
	return r.Client.Del(key).Result()
}

// HGet Key
func (r Conn) HGet(key, field string) (string, error) {
	return r.Client.HGet(key, field).Result()
}

// Get key
func (r Conn) Get(key string) (string, error) {
	return r.Client.Get(key).Result()
}

func (r Conn) HSet(key, field, value string) (bool, error) {
	return r.Client.HSet(key, field, value).Result()
}

// Set redis
func (r Conn) Set(key string, value string, exp time.Duration) (string, error) {
	return r.Client.Set(key, value, exp).Result()
}

// Set redis once
func (r Conn) SetOnce(key string, value string, exp time.Duration) (bool, error) {
	return r.Client.SetNX(key, value, exp).Result()
}

// Ping result
func (r Conn) Ping() (string, error) {
	return r.Client.Ping().Result()
}

// Expire result
func (r Conn) Expire(key string, exp time.Duration) (bool, error) {
	return r.Client.Expire(key, exp).Result()
}

// Keys get multi key
func (r Conn) Keys(key string) ([]string, error) {
	return r.Client.Keys(key).Result()
}

// Keys get multi key
func (r Conn) Incr(key string) (int64, error) {
	return r.Client.Incr(key).Result()
}

func (r Conn) Decr(key string) (int64, error) {
	return r.Client.Decr(key).Result()
}

// GetRedis function
func GetRedis(redisHost, redisPort, redisPassword, redisDB, redisTLS string) (*redis.Client, error) {
	tlsSecured, err := strconv.ParseBool(redisTLS)
	if err != nil {
		return nil, err
	}

	var conf *tls.Config

	// force checking for unsecured aws redis
	if tlsSecured {
		conf = &tls.Config{
			InsecureSkipVerify: tlsSecured,
		}
	} else {
		conf = nil
	}

	useDB, _ := strconv.Atoi(redisDB)
	cl := redis.NewClient(&redis.Options{
		Addr:      fmt.Sprintf("%v:%v", redisHost, redisPort),
		Password:  redisPassword,
		DB:        useDB, // use default DB
		TLSConfig: conf,
	})

	return cl, nil
}

func (r Conn) Watch(fn func(tx *redis.Tx) error) error {
	return r.Client.Watch(fn)
}

func (r Conn) SetNX(key string, value interface{}, expiration time.Duration) (bool, error) {
	return r.Client.SetNX(key, value, expiration).Result()
}

func (r Conn) HDel(key string, field ...string) (int64, error) {
	return r.Client.HDel(key, field...).Result()
}
