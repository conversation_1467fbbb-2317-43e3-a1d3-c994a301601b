package cache

import (
	"github.com/go-redis/redis"
	"time"
)

// Client interface contract
//
//go:generate mockery --name Client --output mocks --outpkg mocks --filename Client.go
type Client interface {
	Get(key string) (string, error)
	HGet(key, field string) (string, error)
	Del(key string) (int64, error)
	Set(key string, value string, duration time.Duration) (string, error)
	SetOnce(key string, value string, duration time.Duration) (bool, error)
	HSet(key, field, value string) (bool, error)
	Ping() (string, error)
	Expire(key string, exp time.Duration) (bool, error)
	Keys(key string) ([]string, error)
	Incr(key string) (int64, error)
	Decr(key string) (int64, error)
	Watch(fn func(tx *redis.Tx) error) error
	SetNX(key string, value interface{}, expiration time.Duration) (bool, error)
	HDel(key string, field ...string) (int64, error)
}
