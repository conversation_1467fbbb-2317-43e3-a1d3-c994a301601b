package firebase

import (
	"context"
	"log"

	firebase "firebase.google.com/go"
	"firebase.google.com/go/messaging"
	"github.com/Lionparcel/hydra/config/keys"
	"github.com/Lionparcel/hydra/shared/logger"
	"google.golang.org/api/option"
)

type fcmClient struct {
	firebaseApp *messaging.Client
}

func (c *fcmClient) Client() *messaging.Client {
	return c.firebaseApp
}

type Firebase interface {
	Client() *messaging.Client
}

func InitFirebaseCloudMessaging() Firebase {
	fcmClient := new(fcmClient)

	ctx := context.Background()

	app, err := firebase.NewApp(ctx, nil, option.WithCredentialsFile(keys.GoogleKey()))
	if err != nil {
		logger.Panic(err)
	}

	client, err := app.Messaging(ctx)
	if err != nil {
		log.Fatalf("error getting Messaging client: %v\n", err)
	}

	fcmClient.firebaseApp = client
	return fcmClient
}
