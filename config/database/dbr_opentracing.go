package database

import (
	"context"
	"github.com/abiewardani/dbr/v2"
)

type TraceReceiver struct {
	dbr.NullEventReceiver
	started           []struct{ eventName, query string }
	errored, finished int
}

func (t *TraceReceiver) SpanStart(ctx context.Context, eventName, query string) context.Context {
	t.started = append(t.started, struct{ eventName, query string }{eventName, query})
	return ctx
}
func (t *TraceReceiver) SpanError(ctx context.Context, err error) { t.errored++ }
func (t *TraceReceiver) SpanFinish(ctx context.Context)           { t.finished++ }
