package database

import (
	"fmt"
	"os"
	"time"

	logger2 "github.com/Lionparcel/hydra/shared/logger"
	"github.com/XSAM/otelsql"
	dbr "github.com/abiewardani/dbr/v2"
	"github.com/abiewardani/dbr/v2/dialect"
	_ "github.com/lib/pq"
	semconv "go.opentelemetry.io/otel/semconv/v1.12.0"
)

type dbrInstance struct {
	master, slave, slaveReport, slaveAnalytics, slaveExternal, holmesV2, postgres, genesisAnalytics *dbr.Session
}

func (c *dbrInstance) Master() *dbr.Session {
	return c.master
}

func (c *dbrInstance) Slave() *dbr.Session {
	return c.slave
}

func (c *dbrInstance) SlaveReport() *dbr.Session {
	return c.slaveReport
}

func (c *dbrInstance) SlaveAnalytics() *dbr.Session {
	return c.slaveAnalytics
}

func (c *dbrInstance) HolmesV2() *dbr.Session {
	return c.holmesV2
}

func (c *dbrInstance) SlaveExternal() *dbr.Session {
	return c.slaveExternal
}

func (c *dbrInstance) Postgres() *dbr.Session {
	return c.postgres
}

func (c *dbrInstance) GenesisAnalytics() *dbr.Session {
	return c.genesisAnalytics
}

func (c *dbrInstance) Close() {
	err := c.master.DB.Close()
	if err != nil {
		logger2.E(err)
	}
	err = c.slave.DB.Close()
	if err != nil {
		logger2.E(err)
	}
	err = c.holmesV2.DB.Close()
	if err != nil {
		logger2.E(err)
	}
	err = c.slaveExternal.DB.Close()
	if err != nil {
		logger2.E(err)
	}
	err = c.slaveReport.DB.Close()
	if err != nil {
		logger2.E(err)
	}
}

// DbrDatabase abstraction
type DbrDatabase interface {
	Master() *dbr.Session
	Slave() *dbr.Session
	SlaveReport() *dbr.Session
	SlaveAnalytics() *dbr.Session
	HolmesV2() *dbr.Session
	SlaveExternal() *dbr.Session
	Postgres() *dbr.Session
	GenesisAnalytics() *dbr.Session
	Close()
}

// InitDbr ...
func InitDbr() DbrDatabase {
	inst := new(dbrInstance)

	dsnMaster := fmt.Sprintf(`%s:%s@tcp(%s:%s)/%s?parseTime=true`,
		os.Getenv("DB_MASTER_USERNAME"), os.Getenv("DB_MASTER_PASSWORD"), os.Getenv("DB_MASTER_HOST"),
		os.Getenv("DB_MASTER_PORT"), os.Getenv("DB_MASTER_NAME"))
	dsnMaster += `&loc=Asia%2FJakarta&charset=utf8`
	sqlMaster, err := otelsql.Open("mysql", dsnMaster, otelsql.WithAttributes(
		semconv.DBSystemMySQL,
	))
	connMaster := &dbr.Connection{
		DB:            sqlMaster, // <- underlying database/sql.DB is instrumented
		EventReceiver: &dbr.NullEventReceiver{},
		Dialect:       dialect.MySQL,
	}

	sqlMaster.SetConnMaxLifetime(100)
	sqlMaster.SetMaxIdleConns(10)
	sqlMaster.SetConnMaxLifetime(time.Minute)

	if err != nil {
		logger2.Panic(err)
	}

	if err := connMaster.Ping(); err != nil {
		logger2.Panic(err)
	}

	//connMaster.SetMaxOpenConns(100)
	//connMaster.SetMaxIdleConns(10)
	//connMaster.SetConnMaxLifetime(time.Minute)
	inst.master = connMaster.NewSession(nil)

	dsnSlave := fmt.Sprintf(`%s:%s@tcp(%s:%s)/%s?parseTime=true`,
		os.Getenv("DB_SLAVE_USERNAME"), os.Getenv("DB_SLAVE_PASSWORD"), os.Getenv("DB_SLAVE_HOST"),
		os.Getenv("DB_SLAVE_PORT"), os.Getenv("DB_SLAVE_NAME"))
	dsnSlave += `&loc=Asia%2FJakarta`
	sqlSlave, err := otelsql.Open("mysql", dsnSlave, otelsql.WithAttributes(
		semconv.DBSystemMySQL,
	))

	sqlSlave.SetMaxOpenConns(100)
	sqlSlave.SetMaxIdleConns(10)
	sqlSlave.SetConnMaxLifetime(time.Minute)

	connSlave := &dbr.Connection{
		DB:            sqlSlave, // <- underlying database/sql.DB is instrumented
		EventReceiver: &dbr.NullEventReceiver{},
		Dialect:       dialect.MySQL,
	}

	if err != nil {
		logger2.Panic(err)
	}

	if err := connSlave.Ping(); err != nil {
		logger2.Panic(err)
	}

	inst.slave = connSlave.NewSession(nil)

	// Slave Analytics
	dsnSlaveAnalytics := fmt.Sprintf(`%s:%s@tcp(%s:%s)/%s?parseTime=true`,
		os.Getenv("DB_SLAVE_ANALYTICS_USERNAME"), os.Getenv("DB_SLAVE_ANALYTICS_PASSWORD"), os.Getenv("DB_SLAVE_ANALYTICS_HOST"),
		os.Getenv("DB_SLAVE_ANALYTICS_PORT"), os.Getenv("DB_SLAVE_ANALYTICS_NAME"))
	dsnSlaveAnalytics += `&loc=Asia%2FJakarta`
	sqlSlaveAnalytics, err := otelsql.Open("mysql", dsnSlaveAnalytics, otelsql.WithAttributes(
		semconv.DBSystemMySQL,
	))

	sqlSlaveAnalytics.SetMaxOpenConns(100)
	sqlSlaveAnalytics.SetMaxIdleConns(10)
	sqlSlaveAnalytics.SetConnMaxLifetime(time.Minute)

	connSlaveAnalytics := &dbr.Connection{
		DB:            sqlSlaveAnalytics, // <- underlying database/sql.DB is instrumented
		EventReceiver: &dbr.NullEventReceiver{},
		Dialect:       dialect.MySQL,
	}

	if err != nil {
		logger2.Panic(err)
	}

	if err := connSlaveAnalytics.Ping(); err != nil {
		logger2.Panic(err)
	}
	inst.slaveAnalytics = connSlaveAnalytics.NewSession(nil)

	dsnSlaveReport := fmt.Sprintf(`%s:%s@tcp(%s:%s)/%s?parseTime=true`,
		os.Getenv("DB_SLAVE_REPORT_USERNAME"), os.Getenv("DB_SLAVE_REPORT_PASSWORD"), os.Getenv("DB_SLAVE_REPORT_HOST"),
		os.Getenv("DB_SLAVE_REPORT_PORT"), os.Getenv("DB_SLAVE_REPORT_NAME"))
	dsnSlaveReport += `&loc=Asia%2FJakarta`
	sqlSlaveReport, err := otelsql.Open("mysql", dsnSlaveReport, otelsql.WithAttributes(
		semconv.DBSystemMySQL,
	))

	sqlSlaveReport.SetMaxOpenConns(100)
	sqlSlaveReport.SetMaxIdleConns(10)
	sqlSlaveReport.SetConnMaxLifetime(time.Minute)

	connSlaveReport := &dbr.Connection{
		DB:            sqlSlaveReport, // <- underlying database/sql.DB is instrumented
		EventReceiver: &dbr.NullEventReceiver{},
		Dialect:       dialect.MySQL,
	}

	if err != nil {
		logger2.Panic(err)
	}

	if err := connSlaveReport.Ping(); err != nil {
		logger2.Panic(err)
	}
	inst.slaveReport = connSlaveReport.NewSession(nil)

	dsnHolmesV2 := fmt.Sprintf(
		"user=%s password=%s dbname=%s host=%s port=%s sslmode=require TimeZone=UTC+7",
		os.Getenv("DB_HOLMES_V2_USERNAME"),
		os.Getenv("DB_HOLMES_V2_PASSWORD"),
		os.Getenv("DB_HOLMES_V2_NAME"),
		os.Getenv("DB_HOLMES_V2_HOST"),
		os.Getenv("DB_HOLMES_V2_PORT"),
	)

	sqlHolmesV2, err := otelsql.Open("postgres", dsnHolmesV2, otelsql.WithAttributes(
		semconv.DBSystemPostgreSQL,
	))

	sqlHolmesV2.SetMaxOpenConns(100)
	sqlHolmesV2.SetMaxIdleConns(10)
	sqlHolmesV2.SetConnMaxLifetime(time.Minute)

	connHolmesV2 := &dbr.Connection{
		DB:            sqlHolmesV2, // <- underlying database/sql.DB is instrumented
		EventReceiver: &dbr.NullEventReceiver{},
		Dialect:       dialect.PostgreSQL,
	}

	if err != nil {
		logger2.E(err)
	}

	if err := connHolmesV2.Ping(); err != nil {
		logger2.E(err)
	}
	inst.holmesV2 = connHolmesV2.NewSession(nil)

	dsnSlaveExternal := fmt.Sprintf(`%s:%s@tcp(%s:%s)/%s?parseTime=true`,
		os.Getenv("DB_SLAVE_EXTERNAL_USERNAME"), os.Getenv("DB_SLAVE_EXTERNAL_PASSWORD"), os.Getenv("DB_SLAVE_EXTERNAL_HOST"),
		os.Getenv("DB_SLAVE_EXTERNAL_PORT"), os.Getenv("DB_SLAVE_EXTERNAL_NAME"))
	dsnSlaveExternal += `&loc=Asia%2FJakarta`
	sqlSlaveExternal, err := otelsql.Open("mysql", dsnSlaveExternal, otelsql.WithAttributes(
		semconv.DBSystemMySQL,
	))

	sqlSlaveExternal.SetMaxOpenConns(100)
	sqlSlaveExternal.SetMaxIdleConns(10)
	sqlSlaveExternal.SetConnMaxLifetime(time.Minute)

	connSlaveExternal := &dbr.Connection{
		DB:            sqlSlaveExternal, // <- underlying database/sql.DB is instrumented
		EventReceiver: &dbr.NullEventReceiver{},
		Dialect:       dialect.MySQL,
	}

	if err != nil {
		logger2.Panic(err)
	}

	if err := connSlaveExternal.Ping(); err != nil {
		logger2.Panic(err)
	}
	inst.slaveExternal = connSlaveExternal.NewSession(nil)

	dsnPostgres := fmt.Sprintf(
		"user=%s password=%s dbname=%s host=%s port=%s sslmode=require TimeZone=UTC+7",
		os.Getenv("DB_POSTGRES_USERNAME"),
		os.Getenv("DB_POSTGRES_PASSWORD"),
		os.Getenv("DB_POSTGRES_NAME"),
		os.Getenv("DB_POSTGRES_HOST"),
		os.Getenv("DB_POSTGRES_PORT"),
	)

	sqlPostgres, err := otelsql.Open("postgres", dsnPostgres, otelsql.WithAttributes(
		semconv.DBSystemPostgreSQL,
	))

	sqlPostgres.SetMaxOpenConns(100)
	sqlPostgres.SetMaxIdleConns(10)
	sqlPostgres.SetConnMaxLifetime(time.Minute)

	connPostgres := &dbr.Connection{
		DB:            sqlPostgres, // <- underlying database/sql.DB is instrumented
		EventReceiver: &dbr.NullEventReceiver{},
		Dialect:       dialect.PostgreSQL,
	}

	if err != nil {
		logger2.Panic(err)
	}

	if err := connPostgres.Ping(); err != nil {
		logger2.Panic(err)
	}
	inst.postgres = connPostgres.NewSession(nil)

	inst.genesisAnalytics = dbGenesisAnalytics()

	return inst
}

func dbGenesisAnalytics() *dbr.Session {
	dsnGenesisAnalytics := fmt.Sprintf(
		"user=%s password=%s dbname=%s host=%s port=%s sslmode=require TimeZone=UTC+7",
		os.Getenv("DB_GENESIS_ANALYTICS_USERNAME"),
		os.Getenv("DB_GENESIS_ANALYTICS_PASSWORD"),
		os.Getenv("DB_GENESIS_ANALYTICS_NAME"),
		os.Getenv("DB_GENESIS_ANALYTICS_HOST"),
		os.Getenv("DB_GENESIS_ANALYTICS_PORT"),
	)

	sqlGenesisAnalytics, err := otelsql.Open("postgres", dsnGenesisAnalytics, otelsql.WithAttributes(
		semconv.DBSystemPostgreSQL,
	))

	sqlGenesisAnalytics.SetMaxOpenConns(100)
	sqlGenesisAnalytics.SetMaxIdleConns(10)
	sqlGenesisAnalytics.SetConnMaxLifetime(time.Minute)

	connGenesisAnalytics := &dbr.Connection{
		DB:            sqlGenesisAnalytics, // <- underlying database/sql.DB is instrumented
		EventReceiver: &dbr.NullEventReceiver{},
		Dialect:       dialect.PostgreSQL,
	}

	if err != nil {
		logger2.E(err)
	}

	if err := connGenesisAnalytics.Ping(); err != nil {
		logger2.E(err)
	}

	return connGenesisAnalytics.NewSession(nil)
}
