package database

import (
	"database/sql"
	"fmt"
	"log"
	"os"
	"time"

	logger2 "github.com/Lionparcel/hydra/shared/logger"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

type gormInstance struct {
	master, slave *gorm.DB
}

// Master initialize DB for master data
func (g *gormInstance) Master() *gorm.DB {
	return g.master
}

func (g *gormInstance) Slave() *gorm.DB {
	return g.slave
}

func (g *gormInstance) Close() {

}

// GormDatabase abstraction
type GormDatabase interface {
	Master() *gorm.DB
	Slave() *gorm.DB
	Close()
}

// InitGorm ...
func InitGorm() GormDatabase {
	inst := new(gormInstance)

	dbLogger := logger.New(
		log.New(os.Stdout, "\r\n", log.LstdFlags), // io writer
		logger.Config{
			SlowThreshold: time.Second,   // Slow SQL threshold
			LogLevel:      logger.Silent, // Log level
			Colorful:      true,          // Disable color
		},
	)

	gormConfig := &gorm.Config{
		// enhance performance config
		PrepareStmt:            true,
		SkipDefaultTransaction: true,
		Logger:                 dbLogger,
	}

	// username, password, host, port, database
	dsnMaster := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True",
		os.Getenv("DB_MASTER_USERNAME"), os.Getenv("DB_MASTER_PASSWORD"), os.Getenv("DB_MASTER_HOST"),
		os.Getenv("DB_MASTER_PORT"), os.Getenv("DB_MASTER_NAME"))
	dsnMaster += `&loc=Asia%2FJakarta&charset=utf8`

	sqlMaster, errMaster := sql.Open("mysql", dsnMaster)
	// SetMaxIdleConns sets the maximum number of connections in the idle connection pool.
	sqlMaster.SetMaxIdleConns(10)
	// SetMaxOpenConns sets the maximum number of open connections to the database.
	sqlMaster.SetMaxOpenConns(100)
	// SetConnMaxLifetime sets the maximum amount of time a connection may be reused.
	sqlMaster.SetConnMaxLifetime(time.Hour)

	dbMaster, errMaster := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlMaster,
	}), gormConfig)

	if errMaster != nil {
		logger2.Panic(errMaster)
	}

	inst.master = dbMaster

	dsnSlave := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8&parseTime=True",
		os.Getenv("DB_SLAVE_USERNAME"), os.Getenv("DB_SLAVE_PASSWORD"), os.Getenv("DB_SLAVE_HOST"),
		os.Getenv("DB_SLAVE_PORT"), os.Getenv("DB_SLAVE_NAME"))
	dsnSlave += `&loc=Asia%2FJakarta&charset=utf8`

	sqlSlave, errSlave := sql.Open("mysql", dsnSlave)
	// SetMaxIdleConns sets the maximum number of connections in the idle connection pool.
	sqlSlave.SetMaxIdleConns(10)
	// SetMaxOpenConns sets the maximum number of open connections to the database.
	sqlSlave.SetMaxOpenConns(100)
	// SetConnMaxLifetime sets the maximum amount of time a connection may be reused.
	sqlSlave.SetConnMaxLifetime(time.Hour)
	if errSlave != nil {
		logger2.Panic(errSlave)
	}

	dbSlave, errSlave := gorm.Open(mysql.New(mysql.Config{
		Conn: sqlSlave,
	}), gormConfig)

	if errSlave != nil {
		logger2.Panic(errSlave)
	}
	inst.slave = dbSlave
	return inst
}
