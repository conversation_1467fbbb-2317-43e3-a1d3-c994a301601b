// Code generated by mockery v2.14.0. DO NOT EDIT.

package mocks

import (
	context "context"

	email "github.com/Lionparcel/hydra/config/email"
	mock "github.com/stretchr/testify/mock"
)

// EmailSendGrid is an autogenerated mock type for the EmailSendGrid type
type EmailSendGrid struct {
	mock.Mock
}

// SendEmail provides a mock function with given fields: ctx, data
func (_m *EmailSendGrid) SendEmail(ctx context.Context, data *email.EmailSendGridRequest) error {
	ret := _m.Called(ctx, data)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *email.EmailSendGridRequest) error); ok {
		r0 = rf(ctx, data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewEmailSendGrid interface {
	mock.TestingT
	Cleanup(func())
}

// NewEmailSendGrid creates a new instance of EmailSendGrid. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewEmailSendGrid(t mockConstructorTestingTNewEmailSendGrid) *EmailSendGrid {
	mock := &EmailSendGrid{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
