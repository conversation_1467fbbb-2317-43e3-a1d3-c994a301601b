package email

import (
	"context"
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
)

// EmailSendGridConfig ...
type EmailSendGridConfig struct {
	EmailSource   string
	EmailName     string
	EmailHost     string
	EmailEndPoint string
	EmailAPIKey   string
}

// EmailSendGridRequest ...
type EmailSendGridRequest struct {
	EmailDestination    string
	EmailContent        string
	EmailSubject        string
	EmailTemplate       string
	EmailTemplateParams map[string]string
	EmailAttachment     []byte
	EmailAttachmentType string
}

// EmailSendGrid ...
type EmailSendGrid interface {
	SendEmail(ctx context.Context, data *EmailSendGridRequest) error
}

// emailSendGrid ...
type emailSendGrid struct {
	config EmailSendGridConfig
}

// NewEmailSendGrid ...
func NewEmailSendGrid(config EmailSendGridConfig) EmailSendGrid {
	return &emailSendGrid{
		config: config,
	}
}

func (c *emailSendGrid) SendEmail(ctx context.Context, data *EmailSendGridRequest) error {
	// create new *SGMailV3
	body := mail.NewV3Mail()

	from := mail.NewEmail(c.config.EmailName, c.config.EmailSource)

	body.SetFrom(from)
	// populate `personalization` with data
	to := mail.NewEmail("", data.EmailDestination)

	// create new *Personalization
	personalization := mail.NewPersonalization()

	personalization.AddTos(to)
	personalization.Subject = data.EmailSubject

	// if data template exist use it
	if data.EmailTemplate != `` {
		body.SetTemplateID(data.EmailTemplate)
		// if parameter dynamic template exist use it
		for key, value := range data.EmailTemplateParams {
			personalization.SetDynamicTemplateData(key, value)
		}
	} else {
		content := mail.NewContent(`text/html`, data.EmailContent)
		body.AddContent(content)
	}

	// add `personalization` to `body`
	body.AddPersonalizations(personalization)

	// read/attach .pdf file
	if len(data.EmailAttachment) > 0 {
		file := mail.NewAttachment()
		encoded := base64.StdEncoding.EncodeToString(data.EmailAttachment)
		file.SetContent(encoded)
		file.SetType(data.EmailAttachmentType)
		fileName := fmt.Sprintf("%s_%v", strings.ReplaceAll(data.EmailSubject, " ", "_"), time.Now().UnixNano()/int64(time.Millisecond))
		file.SetFilename(fileName)
		file.SetDisposition("attachment")

		// add `attachment` to `body`
		body.AddAttachment(file)
	}

	req := sendgrid.GetRequest(c.config.EmailAPIKey, c.config.EmailEndPoint, c.config.EmailHost)
	req.Method = "POST"
	req.Body = mail.GetRequestBody(body)

	// send email
	res, err := sendgrid.API(req)
	if err != nil {
		logger.Ef("email : send email to Customer error %s", err)
		return err
	}

	logger.I("respone code send email ", res.StatusCode)

	return nil
}
