package config

import (
	"crypto/rsa"
	"encoding/json"
	"io/ioutil"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/Lionparcel/hydra/config/cache"
	"github.com/Lionparcel/hydra/config/database"
	"github.com/Lionparcel/hydra/config/elastic"
	"github.com/Lionparcel/hydra/config/email"
	"github.com/Lionparcel/hydra/config/firebase"
	"github.com/Lionparcel/hydra/config/flag_management"
	"github.com/Lionparcel/hydra/config/keys"
	"github.com/Lionparcel/hydra/config/pubsub"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/slack"
	"github.com/Lionparcel/hydra/src/model"
)

var (
	FormatDate = `2006-01-02`
)

// Config struct
type Config struct {
	serviceName string
	environment string
	debug       bool
	port        int
	frontEndUrl string

	stream bool
	rpc    bool
	rest   bool

	redis RedisConfig

	token TokenConfig

	db database.DbrDatabase

	cache cache.Client

	es elastic.ElasticClient

	mailTemplateIDResetPassword string

	mail email.EmailSendGrid

	pubsub pubsub.GooglePubsub

	noSqlDB database.MongoDB

	googleCredential *GoogleCredential

	usernameBasicAuth     string
	passwordBasicAuth     string
	usernameBasicAuthNgen string
	passwordBasicAuthNgen string

	fcmClient firebase.FirebaseFcm

	cloudbeesKey        string
	cloudbeesDevModeKey string
	flagManagement      *flag_management.FlagsModel
	openFeature         *flag_management.OpenFeatureFlag

	assessmentRelabel AssessmentRelabel
}

type GoogleCredential struct {
	ServiceAccount string `json:"service_account"`
	ProjectID      string `json:"project_id"`
	ClientEmail    string `json:"client_email"`
	ClientID       string `json:"client_id"`
}

type RedisConfig struct {
	Host, Port, Password, DB, TLS string
}

type PemKey struct {
	Cert    string
	Private string
}

type TokenConfig struct {
	Name                string
	Type                string
	TemporaryLifetime   time.Duration
	AccessTokenLifeTime time.Duration
	PrivateKey          *rsa.PrivateKey
	PublicKey           *rsa.PublicKey
}

type LimitedAssigned3LCRule struct {
	Type string `json:"type"`
	Role string `json:"role"`
}

// NewConfig func
func NewConfig() *Config {
	cfg := new(Config)

	cfg.InitEnvironmentService()
	cfg.ConnectDB()
	cfg.InitRedis()
	cfg.InitToken()
	cfg.InitElastic()
	cfg.InitEmail()
	cfg.InitPubsub()
	// cfg.InitMongoDB()
	// cfg.InitTracer()
	cfg.InitGoogleCredential()
	cfg.InitBasicAuth()
	cfg.InitBasicAuthNgen()
	cfg.InitFirebaseCloudMessaging()
	cfg.InitFlagManagementCloudBees()
	cfg.InitOpenFeature()
	cfg.InitAssessmentRelabel()
	return cfg
}

func (c *Config) InitEnvironmentService() {
	c.cloudbeesKey = os.Getenv(`CLOUDBEES_KEY`)
	if c.cloudbeesKey == `` {
		panic(`CLOUDBEES_KEY empty`)
	}
	c.cloudbeesDevModeKey = os.Getenv(`CLOUDBEES_DEV_MODE_KEY`)
	if c.cloudbeesDevModeKey == `` {
		panic(`CLOUDBEES_DEV_MODE_KEY empty`)
	}
}

func (c *Config) ServiceName() string {
	return os.Getenv(`SERVICE_NAME`)
}

func (c *Config) APMServiceName() string {
	return os.Getenv(`APM_SERVICE_NAME`)
}

func (c *Config) HostOtelCollector() string {
	return os.Getenv(`HOST_OTEL_COLLECTOR`)
}

func (c *Config) Environment() string {
	return os.Getenv(`ENVIRONMENT`)
}

func (c *Config) FrontEndUrl() string {
	frontEndUrl := ``
	switch c.Environment() {
	case `dev-genesis`:
		frontEndUrl = os.Getenv(`FRONT_END_DEV_URL`)
	case `stg-genesis`:
		frontEndUrl = os.Getenv(`FRONT_END_STAGING_URL`)
	case `prd-genesis`:
		frontEndUrl = os.Getenv(`FRONT_END_PRODUCTION_URL`)
	}
	return frontEndUrl
}

// Debug func
func (c *Config) Debug() bool {
	v := os.Getenv("DEBUG")
	c.debug, _ = strconv.ParseBool(v)

	return c.debug
}

// REST func
func (c *Config) REST() bool {
	v := os.Getenv("REST")
	c.rest, _ = strconv.ParseBool(v)

	return c.rest
}

// RPC func
func (c *Config) RPC() bool {
	v := os.Getenv("RPC")
	c.rpc, _ = strconv.ParseBool(v)

	return c.rpc
}

// STREAM func
func (c *Config) STREAM() bool {
	v := os.Getenv("STREAM")
	c.stream, _ = strconv.ParseBool(v)

	return c.stream
}

// Port func
func (c *Config) Port() int {
	v := os.Getenv("PORT")
	c.port, _ = strconv.Atoi(v)

	return c.port
}

// Redis ..
func (c *Config) Redis() RedisConfig {
	c.redis = RedisConfig{
		Host:     os.Getenv("REDIS_HOST"),
		Port:     os.Getenv("REDIS_PORT"),
		Password: os.Getenv("REDIS_PASSWORD"),
		DB:       os.Getenv("REDIS_DB"),
		TLS:      os.Getenv("REDIS_TLS"),
	}

	return c.redis
}

// InitRedis ..
func (c *Config) InitRedis() {
	client, err := cache.ConnectRedis()
	if err != nil {
		panic(err)
	}

	c.InitCache(client)
}

func (c *Config) InitCache(client cache.Client) {
	c.cache = client
}

// Cache ..
func (c *Config) Cache() cache.Client {
	return c.cache
}

// ConnectDB func
func (c *Config) ConnectDB() {
	c.db = database.InitDbr()
}

// DB func
func (c *Config) DB() database.DbrDatabase {
	return c.db
}

// InitToken ...
func (c *Config) InitToken() {
	temporaryLifetimeStr := os.Getenv("TOKEN_TEMPORARY_LIFETIME")
	temporaryLifetime, err := strconv.Atoi(temporaryLifetimeStr)
	if err != nil {
		logger.Panic(`ENV TOKEN_TEMPORARY_LIFETIME ERROR `, err)
	}

	accessTokenLifetimeStr := os.Getenv("ACCESS_TOKEN_LIFETIME")
	accessTokenLifetime, err := strconv.Atoi(accessTokenLifetimeStr)
	if err != nil {
		logger.Panic(`ENV ACCESS_TOKEN_LIFETIME ERROR `, err)
	}

	publicKey, err := keys.InitPublicKey()
	if err != nil {
		logger.Panic(err)
	}

	privateKey, err := keys.InitPrivateKey()
	if err != nil {
		logger.Panic(err)
	}

	c.token = TokenConfig{
		Name:                os.Getenv("TOKEN_NAME"),
		Type:                os.Getenv("TOKEN_TYPE"),
		TemporaryLifetime:   time.Duration(temporaryLifetime) * time.Second,
		AccessTokenLifeTime: time.Duration(accessTokenLifetime) * time.Hour,
		PrivateKey:          privateKey,
		PublicKey:           publicKey,
	}
}

// Token ...
func (c *Config) Token() TokenConfig {
	return c.token
}

// InitElastic ...
func (c *Config) InitElastic() {
	var err error
	c.es, err = elastic.NewClient()
	if err != nil {
		logger.E(err)
	}
}

// ES ...
func (c *Config) ES() elastic.ElasticClient {
	return c.es
}

// InitEmail ..
func (c *Config) InitEmail() {
	configMail := email.EmailSendGridConfig{
		EmailAPIKey:   os.Getenv(`EMAIL_API_KEY`),
		EmailHost:     os.Getenv(`EMAIL_URL`),
		EmailEndPoint: os.Getenv(`EMAIL_END_POINT`),
		EmailSource:   os.Getenv(`EMAIL_SOURCE`),
		EmailName:     os.Getenv(`EMAIL_NAME`),
	}
	c.mail = email.NewEmailSendGrid(configMail)
}

// Mail ...
func (c *Config) Mail() email.EmailSendGrid {
	return c.mail
}

// InitMongoDB
func (c *Config) InitMongoDB() {
	c.noSqlDB = database.InitMongoDB()
}

// NoSqlDB
func (c *Config) NoSqlDB() database.MongoDB {
	return c.noSqlDB
}

func (c *Config) EmailTemplateIDResetPassword() string {
	return os.Getenv(`EMAIL_TEMPLATE_ID_RESET_PASSWORD`)
}

func (c *Config) EmailTemplateIDResiSendEmail() string {
	return os.Getenv(`EMAIL_TEMPLATE_ID_RESI_SEND_EMAIL`)
}

// HordeURL ...
func (c *Config) HordeURL() string {
	return os.Getenv(`HORDE_URL`)
}

// Interval Del
func (c *Config) PosSttDelInterval() int {
	i := os.Getenv(`POS_STT_DEL_INTERVAL`)
	interval, err := strconv.Atoi(i)
	if err != nil {
		return -3
	}
	return interval
}

// IntervalDex
func (c *Config) PosSttDexInterval() int {
	i := os.Getenv(`POS_STT_DEX_INTERVAL`)
	interval, err := strconv.Atoi(i)
	if err != nil {
		return -3
	}
	return interval
}

// IntervalDex
func (c *Config) DashboardInsightInterval() int {
	i := os.Getenv(`STT_DASHBOARD_INSIGHT_INTERVAL`)
	interval, err := strconv.Atoi(i)
	if err != nil {
		return -30
	}
	return interval
}

// HordeURL ...
func (c *Config) GoberURL() string {
	return os.Getenv(`GOBER_URL`)
}

// ALGOURL ...
func (c *Config) ALGOURL() string {
	return os.Getenv(`ALGO_URL`)
}

// AlgoAuthUsername ...
func (c *Config) AlgoAuthUsername() string {
	return os.Getenv(`ALGO_AUTH_USERNAME`)
}

// AlgoAuthPassword ...
func (c *Config) AlgoAuthPassword() string {
	return os.Getenv(`ALGO_AUTH_PASSWORD`)
}

// AlgoAuthRole ...
func (c *Config) AlgoAuthRole() string {
	return os.Getenv(`ALGO_AUTH_ROLE`)
}

// AlgoAuthUsername ...
func (c *Config) AlgoAuthTrackingUsername() string {
	username := os.Getenv(`ALGO_TRACKING_AUTH_USERNAME`)
	if username == "" {
		username = "web-server"
	}

	return username
}

// AlgoAuthPassword ...
func (c *Config) AlgoAuthTrackingPassword() string {
	pass := os.Getenv(`ALGO_TRACKING_AUTH_PASSWORD`)
	if pass == "" {
		pass = "web-server"
	}

	return pass
}

// AlgoAuthRole ...
func (c *Config) AlgoAuthTrackingRole() string {
	role := os.Getenv(`ALGO_TRACKING_AUTH_ROLE`)
	if role == "" {
		role = "INTERNAL"
	}

	return role
}

// PubSubProjectID ...
func (c *Config) PubSubProjectID() string {
	return os.Getenv(`PUBSUB_PROJECT_ID`)
}

// Init Pubsub
func (c *Config) InitPubsub() {
	c.pubsub = pubsub.NewPubsub(c.PubSubProjectID(), keys.GoogleKey())
}

func (c *Config) InitFirebaseCloudMessaging() {
	c.fcmClient = firebase.NewFirebaseCloudMessaging()
}

// PUBSUB func
func (c *Config) PUBSUB() pubsub.GooglePubsub {
	return c.pubsub
}

// EnvAlgoPubsub ...
func (c *Config) EnvAlgoPubsub() string {
	return os.Getenv(`ENV_ALGO_PUBSUB`)
}

// NgenURL ...
func (c *Config) NgenURL() string {
	return os.Getenv(`NGEN_URL`)
}

// MiddlewareURL ...
func (c *Config) MiddlewareURL() string {
	return os.Getenv(`MIDDLEWARE_URL`)
}

// NgenUsername ...
func (c *Config) NgenUsername() string {
	return os.Getenv(`USERNAME_NGEN`)
}

// NgenPassword ...
func (c *Config) NgenPassword() string {
	return os.Getenv(`PASSWORD_NGEN`)
}

// HydraUsername ...
func (c *Config) HydraUsername() string {
	return os.Getenv(`HYDRA_USERNAME`)
}

// HydraPassword ...
func (c *Config) HydraPassword() string {
	return os.Getenv(`HYDRA_PASSWORD`)
}

// PegasusUsername ...
func (c *Config) PegasusUsername() string {
	return os.Getenv(`PEGASUS_USERNAME`)
}

// PegasusPassword ...
func (c *Config) PegasusPassword() string {
	return os.Getenv(`PEGASUS_PASSWORD`)
}

// Hub9Credentials
func (c *Config) Hub9UrlApiSms() string {
	return os.Getenv(`HUB9_URL_API_SMS`)
}

func (c *Config) Hub9SmsParamsSC() string {
	return os.Getenv(`HUB9_SMS_PARAMS_SC`)
}

func (c *Config) Hub9RegulerSmsUid() string {
	return os.Getenv(`HUB9_REGULER_SMS_UID`)
}

func (c *Config) Hub9RegulerSmsPwd() string {
	return os.Getenv(`HUB9_REGULER_SMS_PWD`)
}

func (c *Config) Hub9PremiumSmsUid() string {
	return os.Getenv(`HUB9_PREMIUM_SMS_UID`)
}

func (c *Config) Hub9PremiumSmsPwd() string {
	return os.Getenv(`HUB9_PREMIUM_SMS_PWD`)
}

func (c *Config) Hub9UrlApiWa() string {
	return os.Getenv(`HUB9_URL_API_WA`)
}

func (c *Config) Hub9LongnumberWaUid() string {
	return os.Getenv(`HUB9_LONGNUMBER_WA_UID`)
}

func (c *Config) Hub9LongnumberWaPwd() string {
	return os.Getenv(`HUB9_LONGNUMBER_WA_PWD`)
}

func (c *Config) YmUrlApiWa() string {
	return os.Getenv(`YM_URL_API_WA`)
}

func (c *Config) YmPremiumWaToken() string {
	return os.Getenv(`YM_PREMIUM_WA_TOKEN`)
}

func (c *Config) YmPremiumTtlBody() string {
	return os.Getenv(`YM_PREMIUM_TTL_BODY`)
}

func (c *Config) YmPremiumTypeBody() string {
	return os.Getenv(`YM_PREMIUM_TYPE_BODY`)
}

func (c *Config) YmPremiumNamespaceHsmBody() string {
	return os.Getenv(`YM_PREMIUM_NAMESPACE_HSM_BODY`)
}

func (c *Config) YmPremiumPolicyLanguageHsmBody() string {
	return os.Getenv(`YM_PREMIUM_POLICY_LANGUAGE_HSM_BODY`)
}

func (c *Config) YmPremiumCodeLanguageHsmBody() string {
	return os.Getenv(`YM_PREMIUM_CODE_LANGUAGE_HSM_BODY`)
}

func (c *Config) YmBotIdLpCustomer() string {
	return os.Getenv(`YM_BOT_ID_LP_CUSTOMER`)
}

func (c *Config) YmBotIdLpMitra() string {
	return os.Getenv(`YM_BOT_ID_LP_MITRA`)
}

func (c *Config) YmBotIdLpKvp() string {
	return os.Getenv(`YM_BOT_ID_LP_KVP`)
}

// NinjaURL ...
func (c *Config) NinjaURL() string {
	return os.Getenv(`NINJA_URL`)
}

// NinjaClientID ...
func (c *Config) NinjaClientID() string {
	return os.Getenv(`NINJA_CLIENT_ID`)
}

// NinjaClientSecret ...
func (c *Config) NinjaClientSecret() string {
	return os.Getenv(`NINJA_CLIENT_SECRET`)
}

// NinjaCountry ...
func (c *Config) NinjaCountry() string {
	return os.Getenv(`NINJA_COUNTRY`)
}

// NinjaBooking ...
func (c *Config) NinjaBooking() bool {
	ninjaBooking := false
	ninjaBooking, _ = strconv.ParseBool(os.Getenv(`NINJA_BOOKING`))
	return ninjaBooking
}

func (c *Config) ApiKeyElexys() string {
	return os.Getenv(`API_KEY_ELEXYS`)
}

func (c *Config) ApiUrlElexys() string {
	return os.Getenv(`API_URL_ELEXYS`)
}

func (c *Config) InternalElexysPosCode() string {
	return os.Getenv(`INTERNAL_ELEXYS_POS_CODE`)
}

func (c *Config) SubmitSttTopicID() string {
	topicID := os.Getenv(`SUBMIT_STT_TOPIC_ID`)
	if topicID == `` {
		topicID = model.SubmitSttTopicID
	}

	return topicID
}

func (c *Config) UpdateStatusSttTopicID() string {
	topicID := os.Getenv(`UPDATE_STATUS_STT_TOPIC_ID`)
	if topicID == `` {
		topicID = model.UpdateStatusSttTopicID
	}

	return topicID
}

func (c *Config) AdjustmentSttTopicID() string {
	topicID := os.Getenv(`ADJUSTMENT_STT_TOPIC_ID`)
	if topicID == `` {
		topicID = model.AdjustmentSttTopicID
	}

	return topicID
}

func (c *Config) GoberTransactionTopicID() string {
	topicID := os.Getenv(`GOBER_TRANSACTION_TOPIC_ID`)
	if topicID == `` {
		topicID = model.GoberTransaction
	}

	return topicID
}

func (c *Config) GoberRevertBookingCodTopicID() string {
	topicID := os.Getenv(`GOBER_REVERT_BOOKING_COD_TOPIC_ID`)
	if topicID == `` {
		topicID = model.GoberRevertBookingCodTopicID
	}

	return topicID
}

func (c *Config) OrchestraCrossDockingTopicID() string {
	topicID := os.Getenv(`ORCHESTRA_CROSS_DOCKING_TOPIC_ID`)
	if topicID == `` {
		topicID = model.HydraCrossDockingBagging
	}

	return topicID
}

func (c *Config) GoberDTPOLCommissionSttTopicID() string {
	topicID := os.Getenv(`GOBER_DTPOL_COMMISSION_STT_TOPIC_ID`)
	if topicID == `` {
		topicID = model.GoberDTPOLCommissionSttTopicID
	}

	return topicID
}

func (c *Config) MaxRetryPickupManifest() int {
	maxRetry, err := strconv.Atoi(os.Getenv(`MAX_RETRY_PICKUP_MANIFEST`))
	if err != nil {
		return 2
	}

	return maxRetry
}

func (c *Config) IsElexysConfig() bool {
	isElexys, err := strconv.ParseBool(os.Getenv(`ELEXYS_ACTIVE`))
	if err != nil {
		panic(err)
	}

	return isElexys
}

func (c *Config) SttManualLimit() uint64 {
	sttManualLimit, err := strconv.ParseUint(os.Getenv(`STT_MANUAL_LIMIT`), 10, 64)
	if err != nil {
		return 1000
	}

	return sttManualLimit
}

func (c *Config) SttManualExpiration() int {
	sttManualExpiration, err := strconv.Atoi(os.Getenv(`STT_MANUAL_EXPIRATION`))
	if err != nil {
		return 1
	}

	return sttManualExpiration
}

func (c *Config) IsSttManualUpsert() bool {
	isUpsert, err := strconv.ParseBool(os.Getenv(`STT_MANUAL_UPSERT`))
	if err != nil {
		panic(err)
	}

	return isUpsert
}

func (c *Config) IsSttManualNoSQLEnable() bool {
	isEnableNoSQL, err := strconv.ParseBool(os.Getenv(`STT_MANUAL_NOSQL_ENABLE`))
	if err != nil {
		panic(err)
	}

	return isEnableNoSQL
}

func (c *Config) InstantBookingExpiration() int {
	instantBookingExpiration, err := strconv.Atoi(os.Getenv(`INSTANT_BOOKING_EXPIRATION`))
	if err != nil {
		return 2
	}

	return instantBookingExpiration
}

func (c *Config) DtpolFeeReportLimitRangeDays() int {
	dtpolFeeReportLimitRangeDays, err := strconv.Atoi(os.Getenv(`DTPOL_FEE_LIMIT_RANGE_DAYS`))
	if err != nil {
		return 1
	}

	return dtpolFeeReportLimitRangeDays
}

func (c *Config) TimeoutCommission() int { // in Seconds
	customTimeout, err := strconv.Atoi(os.Getenv(`TIMEOUT_COMMISSION`))
	if err != nil || customTimeout == 0 {
		return 3 * 60 // default 3 minutes
	}

	return customTimeout
}

func (c *Config) CustomTimeout() int {
	customTimeout, err := strconv.Atoi(os.Getenv(`CUSTOM_TIMEOUT`))
	if err != nil {
		return 1
	}

	return customTimeout
}

func (c *Config) MaxCustomTimeoutNgen() int {
	maxCustomTimeout, err := strconv.Atoi(os.Getenv(`MAX_CUSTOM_TIMEOUT_NGEN`))
	if err != nil {
		return 3
	}

	return maxCustomTimeout
}

func (c *Config) CloudSchedulerAuth() string {
	return os.Getenv(`CLOUD_SCHEDULER_AUTH`)
}

func (c *Config) JNEURL() string {
	return os.Getenv(`JNE_URL`)
}

func (c *Config) JNEUsername() string {
	return os.Getenv(`JNE_USERNAME`)
}

func (c *Config) JNEApiKey() string {
	return os.Getenv(`JNE_API_KEY`)
}

func (c *Config) JNECustomer() string {
	return os.Getenv(`JNE_CUSTOMER`)
}

func (c *Config) JNEBooking() bool {
	jneBooking, _ := strconv.ParseBool(os.Getenv(`JNE_BOOKING`))
	return jneBooking
}

func (c *Config) JNEBookingVersion() int {
	v, _ := strconv.Atoi(os.Getenv(`JNE_BOOKING_VERSION`))
	if v == 0 {
		v = 1
	}

	return v
}

func (c *Config) BookingTransactionSchedulerLimitMultiplier() int {
	customMultiplier, err := strconv.Atoi(os.Getenv(`BOOKING_TRANSACTION_SCHEDULER_LIMIT_MULTIPLIER`))
	if err != nil {
		return 1
	}

	return customMultiplier
}

func (c *Config) BookingTransactionSchedulerLimit() int {
	customLimit, err := strconv.Atoi(os.Getenv(`BOOKING_TRANSACTION_SCHEDULER_LIMIT`))
	if err != nil {
		return 1000
	}

	return customLimit
}

func (c *Config) DefaultLimitPagination() int {
	limitPagination, err := strconv.Atoi(os.Getenv(`DEFAULT_LIMIT_PAGINATION`))
	if err != nil {
		return 5
	}

	return limitPagination
}

func (c *Config) SttDTPOLEndpointRetry() int {
	retryTimes, err := strconv.Atoi(os.Getenv(`STT_DTPOL_ENDPOINT_RETRY`))
	if err != nil {
		return 3
	}

	return retryTimes
}

// SttDTPOLEndpointDelay in second
func (c *Config) SttDTPOLEndpointDelay() int {
	delayInSecond, err := strconv.Atoi(os.Getenv(`STT_DTPOL_ENDPOINT_DELAY`))
	if err != nil {
		return 2
	}

	return delayInSecond
}

// Stt Report V2
func (c *Config) SttReportsTableName() string {
	tableName := os.Getenv(`STT_REPORTS_TABLE_NAME`)
	if tableName == `` {
		tableName = model.SttReportsTableName
	}

	return tableName
}

// Stt Report V2
func (c *Config) SttReportsV2TableName() string {
	tableName := os.Getenv(`STT_REPORTS_V2_TABLE_NAME`)
	if tableName == `` {
		tableName = `stt_reports`
	}

	return tableName
}

func (c *Config) DashboardDateDefault() int {
	dashboardDate, err := strconv.Atoi(os.Getenv(`DASHBOARD_DATE_DEFAULT`))
	if err != nil {
		return 1
	}

	return dashboardDate
}

func (c *Config) RangeDateSchedulerNgen() int {
	rangeDate, err := strconv.Atoi(os.Getenv(`RANGE_DATE_SCHEDULER_NGEN`))
	if err != nil {
		return 2
	}

	return rangeDate
}

func (c *Config) SchedulerNgenSignature() string {
	return os.Getenv(`SCHEDULER_NGEN_SIGNATURE`)
}

func (c *Config) ReleaseV2CodDashboard() string {
	return os.Getenv(`RELEASE_V2_COD_DASHBOARD`)
}

func (c *Config) SchedulerMessageRetrySignature() string {
	return os.Getenv(`SCHEDULER_MESSAGE_RETRY_SIGNATURE`)
}

func (c *Config) SchedulerBookLuwjistiksRetrySignature() string {
	return os.Getenv(`SCHEDULER_BOOKING_LUWJISTIK_RETRY_SIGNATURE`)
}

func (c *Config) LuwjistiksWebhookClientID() string {
	return os.Getenv(`LUWJISTIK_WEBHOOK_CLIENT_ID`)
}

func (c *Config) LuwjistiksMiniapps() string {
	return os.Getenv(`LUWJISTIK_MINIAPPS`)
}

func (c *Config) EnableNewCodBooking() bool {
	flagging, err := strconv.ParseBool(os.Getenv(`ENABLE_NEW_COD_BOOKING`))
	if err != nil {
		return false
	}

	return flagging
}

func (c *Config) LimitDtpolReports() int {
	number, err := strconv.Atoi(os.Getenv(`LIMIT_DTPOL_REPORTS`))
	if err != nil {
		return 10000
	}

	return number
}

// Reverse Dashboard Summary TableName
func (c *Config) ReverseDashboardSummaryTableName() string {
	tableName := os.Getenv(`REVERSE_DASHBOARD_SUMMARY_TABLE_NAME`)
	if tableName == `` {
		tableName = `reverse_outgoing_summary`
	}

	return tableName
}

// Reverse Incoming Summary TableName
func (c *Config) ReverseIncomingSummaryTableName() string {
	tableName := os.Getenv(`REVERSE_INCOMING_SUMMARY_TABLE_NAME`)
	if tableName == `` {
		tableName = `reverse_incoming_summary`
	}

	return tableName
}

// Reverse Outgoing TableName
func (c *Config) ReverseOutgoingTableName() string {
	tableName := os.Getenv(`REVERSE_OUTGOING_TABLE_NAME`)
	if tableName == `` {
		tableName = `reverse_outgoing`
	}

	return tableName
}

// Reverse Incoming TableName
func (c *Config) ReverseIncomingTableName() string {
	tableName := os.Getenv(`REVERSE_INCOMING_TABLE_NAME`)
	if tableName == `` {
		tableName = `reverse_incoming`
	}

	return tableName
}

// Reverse Incoming TableName
func (c *Config) ReadyToCargoTableName() string {
	tableName := os.Getenv(`READY_TO_CARGO_TABLE_NAME`)
	if tableName == `` {
		tableName = `ready_to_cargo`
	}

	return tableName
}

func (c *Config) LimitTierXReports() int {
	number, err := strconv.Atoi(os.Getenv(`LIMIT_TIER_X_REPORTS`))
	if err != nil {
		return 1000
	}

	return number
}

func (c *Config) LimitProgressiveCommissionReports() int {
	number, err := strconv.Atoi(os.Getenv(`LIMIT_PROGRESSIVE_COMMISSION_REPORTS`))
	if err != nil || number == 0 {
		return 1000
	}
	return number
}

func (c *Config) DeliveryFeeDtpolActiveDate() time.Time {
	date, err := time.Parse(FormatDate, os.Getenv(`DELIVERY_FEE_DTPOL_ACTIVE_DATE`))
	if err != nil {
		defaultDate, _ := time.Parse(FormatDate, "2022-07-01")
		return defaultDate
	}

	return date
}

func (c *Config) GoberSttShipmentPaymentNotificationTopicID() string {
	topicID := os.Getenv(`GOBER_STT_SHIPMENT_PAYMENT_NOTIFICATION_TOPIC_ID`)
	if topicID == `` {
		topicID = model.GoberSttShipmentPaymentNotification
	}

	return topicID
}

func (c *Config) PublishMessageRetry() int {
	number, err := strconv.Atoi(os.Getenv(`PUBLISH_MESSAGE_RETRY`))
	if err != nil {
		return 3
	}

	return number
}

func (c *Config) EnableFeatureHoldCommission() bool {
	flagging, err := strconv.ParseBool(os.Getenv(`ENABLE_FEATURE_HOLD_COMMISSION`))
	if err != nil {
		return false
	}

	return flagging
}

func (c *Config) DropOffTruckingDtpolActiveDate() time.Time {
	date, err := time.Parse(FormatDate, os.Getenv(`DROPOFF_TRUCKING_DTPOL_ACTIVE_DATE`))
	if err != nil {
		defaultDate, _ := time.Parse(FormatDate, "2022-08-01")
		return defaultDate
	}

	return date
}

func (c *Config) CheckSttNoDuplicationRetry() int {
	customRetry, err := strconv.Atoi(os.Getenv(`CHECK_STT_NO_DUPLICATION_RETRY`))
	if err != nil {
		return 3
	}

	return customRetry
}

func (c *Config) EnableCheckSttNoDuplicationRetry() bool {
	flag, err := strconv.ParseBool(os.Getenv(`ENABLE_CHECK_STT_NO_DUPLICATION_RETRY`))
	if err != nil {
		return false
	}

	return flag
}

func (c *Config) ProgressiveCommissionSchedullerExpiration() int {
	expired, err := strconv.Atoi(os.Getenv(`PROGRESSIVE_COMMISSION_EXPIRATION`))
	if err != nil {
		return 5
	}

	return expired
}

func (c *Config) HydraProgressiveCommissionToGoberTopicID() string {
	topicID := os.Getenv(`HYDRA_PROGRESSIVE_COMMISSION_TO_GOBER_TOPIC_ID`)
	if topicID == `` {
		topicID = model.HydraProgressiveCommissionToGober
	}

	return topicID
}

func (c *Config) InitGoogleCredential() {
	credential := &GoogleCredential{}

	file, err := ioutil.ReadFile(keys.GoogleKey())
	if err != nil {
		logger.Panic(err)
	}

	err = json.Unmarshal(file, credential)
	if err != nil {
		logger.Panic(err)
	}

	c.googleCredential = credential
}

func (c *Config) GoogleCredential() *GoogleCredential {
	return c.googleCredential
}

func (c *Config) GoberProgressiveCommissionTopicID() string {
	topicID := os.Getenv(`GOBER_PROGRESSIVE_COMMISSION_TOPIC_ID`)
	if topicID == `` {
		topicID = model.GoberProgressiveCommission
	}

	return topicID
}

func (c *Config) SchedulerClientID() string {
	return os.Getenv(`SCHEDULER_CLIENT_ID`)
}

func (c *Config) SchedulerClientSecret() string {
	return os.Getenv(`SCHEDULER_CLIENT_SECRET`)
}

func (c *Config) HydraCalculateRetailTariffTopicID() string {
	topicID := os.Getenv(`HYDRA_CALCULATE_RETAIL_TARIFF_TOPIC_ID`)
	if topicID == `` {
		topicID = model.HydraCalculateRetailTariff
	}

	return topicID
}

func (c *Config) GeneralRetry() int {
	retryTimes, err := strconv.Atoi(os.Getenv(`GENERAL_RETRY`))
	if err != nil {
		return 3
	}

	return retryTimes
}

// GeneralDelayRetry in second
func (c *Config) GeneralDelayRetry() time.Duration {
	delayInSecond, err := strconv.Atoi(os.Getenv(`GENERAL_DELAY_RETRY`))
	if err != nil {
		return 2 * time.Second
	}

	return time.Duration(delayInSecond) * time.Second
}

func (c *Config) CodDashboardTableName() string {
	tableName := os.Getenv(`COD_DASHBOARD_TABLE_NAME`)
	if tableName == `` {
		tableName = model.CodDashboardTableName
	}

	return tableName
}

func (c *Config) LUWJISTIK_URL() string {
	return os.Getenv(`LUWJISTIK_URL`)
}

func (c *Config) LUWJISTIK_API_SECRET_KEY() string {
	return os.Getenv(`LUWJISTIK_API_SECRET_KEY`)
}

func (c *Config) LUWJISTIK_API_APP_ID() string {
	return os.Getenv(`LUWJISTIK_API_APP_ID`)
}

func (c *Config) LUWJISTIK_ACTIVE() bool {

	v := os.Getenv("LUWJISTIK_ACTIVE")
	status, _ := strconv.ParseBool(v)

	return status
}

func (c *Config) CorporateDashboardTableName() string {
	tableName := os.Getenv(`CORPORATE_DASHBOARD_TABLE_NAME`)
	if tableName == `` {
		tableName = model.CorporateDashboardTableName
	}

	return tableName
}

func (c *Config) DefaultLimitRowQueryRedshift() int {
	s := os.Getenv(`DEFAULT_LIMIT_ROW_QUERY_REDSHIFT`)
	i, _ := strconv.Atoi(s)
	if i == 0 {
		return 2000
	}
	return i
}

// GMA Dashboard TableName
func (c *Config) GMADashboardTableName() string {
	tableName := os.Getenv(`GMA_DASHBOARD_TABLE_NAME`)
	if tableName == `` {
		tableName = `stt_gma_dashboard`
	}

	return tableName
}

func (c *Config) ClaimHistoryTableName() string {
	tableName := os.Getenv(`CLAIM_HISTORY_TABLE_NAME`)
	if tableName == `` {
		tableName = model.ClaimHistoryTableName
	}

	return tableName
}

func (c *Config) ConfigReverseRtsJourney() bool {
	reverseRts, err := strconv.ParseBool(os.Getenv(`REVERSE_RTS_JOURNEY`))
	if err != nil {
		reverseRts = false
	}

	return reverseRts
}

func (c *Config) DefaultDistrictRtsRtshq() string {
	code := os.Getenv(`DEFAULT_DISTRICT_RTS_RTSHQ`)
	if code == `` {
		code = "DKI00102"
	}
	return code
}

func (c *Config) RetryCargoEnable() bool {
	retryCargo, _ := strconv.ParseBool(os.Getenv(`RETRY_CARGO_ENABLE`))
	return retryCargo
}
func (c *Config) InitBasicAuth() {
	c.usernameBasicAuth = os.Getenv(`USERNAME_BASIC_AUTHENTICATION`)
	c.passwordBasicAuth = os.Getenv(`PASSWORD_BASIC_AUTHENTICATION`)
}

func (c *Config) InitBasicAuthNgen() {
	c.usernameBasicAuthNgen = os.Getenv(`USERNAME_BASIC_AUTH_NGEN`)
	c.passwordBasicAuthNgen = os.Getenv(`PASSWORD_BASIC_AUTH_NGEN`)
}

func (c *Config) GetBasicAuthUsername() string {
	return c.usernameBasicAuth
}

func (c *Config) GetBasicAuthPassword() string {
	return c.passwordBasicAuth
}

func (c *Config) GetBasicAuthNgenUsername() string {
	return c.usernameBasicAuthNgen
}

func (c *Config) GetBasicAuthNgenPassword() string {
	return c.passwordBasicAuthNgen
}

func (c *Config) ConfigReverseCNXJourney() bool {
	reverseRts, err := strconv.ParseBool(os.Getenv(`REVERSE_CNX_JOURNEY`))
	if err != nil {
		reverseRts = false
	}

	return reverseRts
}

func (c *Config) ConfigAllowSetToPODDEX() bool {
	setToPodDex, err := strconv.ParseBool(os.Getenv(`ALLOW_SET_TO_PODDEX`))
	if err != nil {
		setToPodDex = false
	}

	return setToPodDex
}

func (c *Config) GoberPosParentCommissionTopicID() string {
	topicID := os.Getenv(`GOBER_POS_PARENT_COMMISSION_TOPIC_ID`)
	if topicID == `` {
		topicID = model.GoberPosParentCommissionTopicID
	}

	return topicID
}

func (c *Config) HydraReadyToCargoTopicID() string {
	topicID := os.Getenv(`HYDRA_READY_TO_CARGO_TOPIC_ID`)
	if topicID == `` {
		topicID = model.HydraReadyToCargoTopicID
	}

	return topicID
}

func (c *Config) HydraReadyToCargoRemoveTopicID() string {
	topicID := os.Getenv(`HYDRA_CARGO_RTC_REMOVE_TOPIC_ID`)
	if topicID == `` {
		topicID = model.HydraReadyToCargoRemoveTopicID
	}

	return topicID
}

func (c *Config) ConfigAllowScanSTIDESTIsPickupTrucking() bool {
	isAllow, err := strconv.ParseBool(os.Getenv(`ALLOW_STI_DEST_PICKUP_TRUCKING`))
	if err != nil {
		isAllow = false
	}

	return isAllow
}

func (c *Config) DefaultLimitSttManualUnused() int {
	s := os.Getenv(`DEFAULT_LIMIT_STT_MANUAL_UNUSED`)
	i, _ := strconv.Atoi(s)
	if i == 0 {
		return 100
	}
	return i
}

func (c *Config) HydraGenerateSttManualTopicID() string {
	topicID := os.Getenv(`HYDRA_CROSS_DOCKING_GENERATE_STT_MANUAL_TOPIC_ID`)
	if topicID == `` {
		topicID = model.HydraCrossDockingGenerateSttManual
	}

	return topicID
}

func (c *Config) GenerateTotalStt() int {
	s := os.Getenv(`GENERATE_TOTAL_STT`)
	i, _ := strconv.Atoi(s)
	if i == 0 {
		return 100
	}
	return i
}

func (c *Config) ConfigEnableConfirmCA() bool {
	isAllow, err := strconv.ParseBool(os.Getenv(`ENABLE_CONFIRM_TO_CA`))
	if err != nil {
		isAllow = false
	}

	return isAllow
}

func (c *Config) ConfigRTSEnableConfirmCA() bool {
	isAllow, err := strconv.ParseBool(os.Getenv(`ENABLE_RTS_CONFIRM_TO_CA`))
	if err != nil {
		isAllow = false
	}

	return isAllow
}

func (c *Config) ChannelNotifyUpdateSttPromo() string {
	chName := os.Getenv(`CHANNEL_NOTIFY_UPDATE_STT_PROMO`)
	if chName == `` {
		chName = slack.GenesisUpdateTotalAmount
	}
	return chName
}

func (c *Config) EnableUpdatePromoTotalAmount() bool {
	isAllow, err := strconv.ParseBool(os.Getenv(`ENABLE_UPDATE_PROMO_TOTAL_AMOUNT`))
	if err != nil {
		isAllow = false
	}

	return isAllow
}

func (c *Config) SttIDPromoAfter() int {
	id, err := strconv.Atoi(os.Getenv(`STT_ID_PROMO_AFTER`))
	if err != nil {
		id = 0
	}

	return id
}

func (c *Config) EnableNgenCargoReserve() bool {
	isEnable, err := strconv.ParseBool(os.Getenv(`ENABLE_CARGO_NGEN_RESERVE`))
	if err != nil {
		isEnable = false
	}

	return isEnable
}

func (c *Config) EnableAwbReserveSpecificConsole() bool {
	isEnable, err := strconv.ParseBool(os.Getenv(`ENABLE_AWB_RESERVE_SPECIFIC_CONSOLE`))
	if err != nil {
		isEnable = false
	}

	return isEnable
}

func (c *Config) AwbReserveSpecificConsoleCityCode() string {
	code := os.Getenv(`AWB_RESERVE_SPECIFIC_CONSOLE_CITY_CODE`)
	if code == `` {
		code = ""
	}

	return code
}

func (c *Config) EnableRetrieveAwbReserve() bool {
	isEnable, err := strconv.ParseBool(os.Getenv(`ENABLE_RETRIEVE_AWB_RESERVE`))
	if err != nil {
		isEnable = false
	}

	return isEnable
}

func (c *Config) NgenCargoReserveRetryNumber() int {
	retryCount, _ := strconv.Atoi(os.Getenv(`NGEN_CARGO_RESERVE_RETRY_NUMBER`))
	if retryCount == 0 {
		retryCount = 20
	}

	return retryCount
}

func (c *Config) NgenRetryCargoNum() int {
	retryCountTime, _ := strconv.Atoi(os.Getenv(`NGEN_RETRY_CARGO_NUMBER`))
	if retryCountTime == 0 {
		retryCountTime = 2
	}

	return retryCountTime
}

func (c *Config) RetryCargoImmediateTimeExceeded() int {
	retryCountTime, _ := strconv.Atoi(os.Getenv(`RETRY_CARGO_IMMEDIATE_TIME_EXCEEDED`))
	if retryCountTime == 0 {
		retryCountTime = 10
	}

	return retryCountTime
}

func (c *Config) HydraRetryCargoTopicID() string {
	topicID := os.Getenv(`HYDRA_RETRY_CARGO_TOPIC_ID`)
	if topicID == `` {
		topicID = model.HydraRetryCargoTopicID
	}

	return topicID
}

func (c *Config) HydraRtcSetInactiveBatchPerRow() int {
	topicID, _ := strconv.Atoi(os.Getenv(`HYDRA_RTC_SET_INACTIVE_BATCH_PER_ROW`))
	if topicID == 0 {
		topicID = 15
	}

	return topicID
}

func (c *Config) GetPartnerIDCGK() int {
	partnerID, _ := strconv.Atoi(os.Getenv(`GET_PARTNER_ID_CGK`))
	if partnerID == 0 {
		partnerID = 41
	}
	return partnerID
}

func (c *Config) GetPartnerCodeCGK() string {
	partnerCode := os.Getenv(`GET_PARTNER_CODE_CGK`)
	if partnerCode == `` {
		partnerCode = `CONS41`
	}
	return partnerCode
}

func (c *Config) GetPartnerNameCGK() string {
	partnerName := os.Getenv(`GET_PARTNER_NAME_CGK`)
	if partnerName == `` {
		partnerName = `PT. Lion Express`
	}
	return partnerName
}

func (c *Config) HydraSttManualReserveLoop() int {
	loop, _ := strconv.Atoi(os.Getenv(`HYDRA_STT_MANUAL_RESERVE_LOOP`))
	if loop == 0 {
		loop = 20
	}

	return loop
}

func (c *Config) HydraSttManualReserveTTL() int {
	ttl, _ := strconv.Atoi(os.Getenv(`HYDRA_STT_MANUAL_RESERVE_TTL`))
	if ttl == 0 {
		ttl = 360
	}

	return ttl
}

func (c *Config) HydraEnableThresholdNgen() bool {
	enable, err := strconv.ParseBool(os.Getenv(`ENABLE_THRESHOLD_NGEN`))
	if err != nil {
		return false
	}

	return enable
}

func (c *Config) HydraThresholdNgen() string {
	if len(os.Getenv(`NGEN_THRESHOLD`)) > 0 {
		return os.Getenv(`NGEN_THRESHOLD`)
	}
	return "180"
}

func (c *Config) SalesForceRTSUrlProcess() string {
	return os.Getenv(`SALESFORCE_RTS_URL_PROCESS`)
}

func (c *Config) SalesForceAuthUrl() string {
	return os.Getenv(`SALESFORCE_AUTH_URL`)
}

func (c *Config) SalesForceUrlNew() string {
	return os.Getenv(`SALESFORCE_URL_NEW`)
}

func (c *Config) SalesForceClientIDNew() string {
	return os.Getenv(`SF_CLIENT_ID_NEW`)
}

func (c *Config) SalesForceClientSecretNew() string {
	return os.Getenv(`SF_CLIENT_SECRET_NEW`)
}

func (c *Config) SalesForceUsernameNew() string {
	return os.Getenv(`SF_USERNAME_NEW`)
}

func (c *Config) SalesForcePasswordNew() string {
	return os.Getenv(`SF_PASSWORD_NEW`)
}

func (c *Config) SalesForceUsernameNewSttRemove() string {
	return os.Getenv(`SF_USERNAME_NEW_STT_REMOVE`)
}

func (c *Config) SalesForcePasswordNewSttRemove() string {
	return os.Getenv(`SF_PASSWORD_NEW_STT_REMOVE`)
}

func (c *Config) SalesForceUrl() string {
	return os.Getenv(`SALESFORCE_URL`)
}

func (c *Config) SalesForceClientID() string {
	return os.Getenv(`SF_CLIENT_ID`)
}

func (c *Config) SalesForceClientSecret() string {
	return os.Getenv(`SF_CLIENT_SECRET`)
}

func (c *Config) SalesForceUsername() string {
	return os.Getenv(`SF_USERNAME`)
}

func (c *Config) SalesForcePassword() string {
	return os.Getenv(`SF_PASSWORD`)
}

func (c *Config) GenesisSalesForceUsername() string {
	return os.Getenv(`GENESIS_SF_USERNAME`)
}

func (c *Config) GenesisSalesForcePassword() string {
	return os.Getenv(`GENESIS_SF_PASSWORD`)
}

func (c *Config) GenesisSalesForceExcludeActor() string {
	return os.Getenv(`GENESIS_SF_EXCLUDE_ACTOR`)
}

func (c *Config) AwbBookingExpired() int {
	ttl, _ := strconv.Atoi(os.Getenv(`NGEN_AWB_BOOKING_EXPIRED`))
	if ttl == 0 {
		ttl = 1
	}

	return ttl
}

func (c *Config) ConfigNgenCancel() bool {
	isAllow, err := strconv.ParseBool(os.Getenv(`ENABLE_CONFIG_NGEN_CANCEL`))
	if err != nil {
		isAllow = false
	}

	return isAllow
}

func (c *Config) EnableFlightDateTime() bool {
	enabled, err := strconv.ParseBool(os.Getenv(`ENABLE_FLIGHT_DATETIME`))
	if err != nil {
		enabled = false
	}

	return enabled
}

func (c *Config) AwbReserveTrackingLimit() int {
	limit, _ := strconv.Atoi(os.Getenv(`AWB_RESERVE_TRACKING_LIMIT `))
	if limit == 0 {
		limit = 10
	}

	return limit
}

func (c *Config) ImmediateTrackingUseDayLimit() bool {
	use, err := strconv.ParseBool(os.Getenv(`IMMEDIATE_TRACKING_USE_DAY_LIMIT`))
	if err != nil {
		return false
	}

	return use
}

func (c *Config) ImmediateTrackingDayLimit() int {
	limit, _ := strconv.Atoi(os.Getenv(`IMMEDIATE_TRACKING_DAY_LIMIT`))
	if limit == 0 {
		limit = 2
	}

	return limit
}

func (c *Config) UseOldRetryCargoImmediate() bool {
	enabled, err := strconv.ParseBool(os.Getenv(`ENABLE_OLD_RETRY_CARGO_IMMEDIATE`))
	if err != nil {
		enabled = false
	}

	return enabled
}

func (c *Config) RateVatShipment() float64 {
	rateVatShipment, err := strconv.ParseFloat(os.Getenv(`RATE_VAT_SHIPMENT`), 8)
	if err != nil {
		return 0
	}

	return rateVatShipment
}

func (c *Config) RateVatCod() float64 {
	rateVatCod, err := strconv.ParseFloat(os.Getenv(`RATE_VAT_COD`), 8)
	if err != nil {
		return 0
	}

	return rateVatCod
}

func (c *Config) NgenCachingLoginTimout() int {
	timeout, _ := strconv.Atoi(os.Getenv(`NGEN_CACHING_LOGIN_TIMEOUT`))
	if timeout == 0 {
		timeout = 1
	}

	return timeout
}

func (c *Config) EnableCodRetail() bool {
	active, err := strconv.ParseBool(os.Getenv(`ENABLE_COD_RETAIL`))
	if err != nil {
		return false
	}

	return active
}

func (c *Config) OutstandingCod() bool {
	active, err := strconv.ParseBool(os.Getenv(`OUTSTANDING_COD`))
	if err != nil {
		return false
	}

	return active
}

func (c *Config) ReleaseOutstandingCod() string {
	return os.Getenv(`RELEASE_OUTSTANDING_COD`)
}

func (c *Config) LimitMonthEligibleUpdateStatusByBookedAt() int {
	number, _ := strconv.Atoi(os.Getenv(`LIMIT_MONTH_ELIGIBLE_UPDATE_STATUS_BY_BOOKED_AT`))
	if number == 0 {
		number = -6
	}

	return number
}

func (c *Config) EnablePromoBookingShipment() bool {
	active, err := strconv.ParseBool(os.Getenv(`ENABLE_PROMO_BOOKING_SHIPMENT`))
	if err != nil {
		return false
	}

	return active
}

func (c *Config) ExcludeBlockedOutstandingCityCode() string {
	return strings.TrimSpace(os.Getenv(`EXCLUDE_CITY_BLOCKED_OUTSTANDING`))
}

func (c *Config) IsForceRtcInactive() bool {
	enabled, err := strconv.ParseBool(os.Getenv(`IS_FORCE_RTC_INACTIVE`))
	if err != nil {
		enabled = false
	}

	return enabled
}

func (c *Config) ForceInactiveRtcAfterSecond() int {
	second, err := strconv.Atoi(os.Getenv(`FORCE_INACTIVE_RTC_AFTER_SECOND`))

	if err != nil {
		second = 15
	}

	return second
}

func (c *Config) EnableRtcCargoRecommendationV2() bool {
	active, err := strconv.ParseBool(os.Getenv(`ENABLE_RTC_CARGO_RECOMMENDATION_V2`))
	if err != nil {
		return false
	}

	return active
}
func (c *Config) InitFlagManagementCloudBees() {
	c.flagManagement = flag_management.InitFlagManagementCloudBees()
}

func (c *Config) FlagManagement() *flag_management.FlagsModel {
	return c.flagManagement
}

func (c *Config) InitOpenFeature() {
	c.openFeature = flag_management.InitFlagManagementOpenFeature()
}

func (c *Config) OpenFeature() *flag_management.OpenFeatureFlag {
	return c.openFeature
}

func (c *Config) EnableValidationBagCargoRtc() bool {
	active, err := strconv.ParseBool(os.Getenv(`USE_VALIDATION_BAG_CARGO_RTC`))
	if err != nil {
		return false
	}

	return active
}

func (c *Config) IsUseValidationSttBagDestination() bool {
	active, err := strconv.ParseBool(os.Getenv(`IS_USE_VALIDATION_STTBAG_DESTINATION`))
	if err != nil {
		return false
	}

	return active
}

func (c *Config) IsUseValidationBaggingGroupStiSc() bool {
	active, err := strconv.ParseBool(os.Getenv(`IS_USE_VALIDATION_BAGGING_GROUP_STISC`))
	if err != nil {
		return false
	}

	return active
}

func (c *Config) LimitReport() int {
	limit, err := strconv.Atoi(os.Getenv(`LIMIT_REPORT`))

	if err != nil {
		return 1000
	}

	return limit
}

func (c *Config) IsUseUpsertRtcV2() bool {
	active, err := strconv.ParseBool(os.Getenv(`IS_USE_UPSERT_RTC_V2`))
	if err != nil {
		return false
	}

	return active
}

func (c *Config) GetMaxLoopRetrySelect() int {
	maxLoop, err := strconv.Atoi(os.Getenv(`MAX_LOOP_RETRY_SELECT`))
	if err != nil {
		return 5
	}

	return maxLoop
}

func (c *Config) RetrySelectDelayInMs() int {
	delay, err := strconv.Atoi(os.Getenv(`RETRY_SELECT_DELAY_IN_MS`))
	if err != nil {
		return 200
	}

	return delay
}

func (c *Config) IsRetryPodCommissionEnabled() bool {
	active, err := strconv.ParseBool(os.Getenv(`IS_RETRY_POD_COMMISSION_ENABLED`))
	if err != nil {
		return false
	}

	return active
}

func (c *Config) EnableFcmSrtcBrtcInactiveFcm() bool {
	enable, err := strconv.ParseBool(os.Getenv(`ENABLE_FCM_SRTC_BRTC_INACTIVE_FCM`))
	if err != nil {
		return false
	}

	return enable
}

func (c *Config) NewCalculateVolumeWeightBigpackDate() time.Time {
	date := os.Getenv(`NEW_CALCULATE_VOLUME_WEIGHT_BIGPACK_DATE`)
	parsed, err := time.Parse(FormatDate, date)
	if err != nil {
		parsed, _ = time.Parse(FormatDate, `2023-11-03`)
		return parsed
	}
	return parsed
}

func (c *Config) NewCalculateVolumeWeightBigpackDivider() int {
	divider, err := strconv.ParseInt(os.Getenv(`NEW_CALCULATE_VOLUME_WEIGHT_BIGPACK_DIVIDER`), 0, 32)
	if divider <= 0 || err != nil {
		return 6000
	}
	return int(divider)
}

func (c *Config) EnableNgenV4() bool {
	enable, err := strconv.ParseBool(os.Getenv(`ENABLE_NGEN_V4`))
	if err != nil {
		return false
	}

	return enable
}

func (c *Config) UseOldLuwjistikWebhook() bool {
	flagging, err := strconv.ParseBool(os.Getenv(`USE_OLD_LUWJISTIK_WEBHOOK`))
	if err != nil {
		return false
	}

	return flagging
}

func (c *Config) SchedulerWebhookLuwjistikSignature() string {
	return os.Getenv(`SCHEDULER_WEBHOOK_LUWJISTIK_SIGNATURE`)
}

func (c *Config) SchedulerLuwjistikActive() bool {
	active, err := strconv.ParseBool(os.Getenv(`SCHEDULER_LUWJISTIK_ACTIVE`))
	if err != nil {
		return false
	}

	return active
}

func (c *Config) RangeDateTimeSchedulerWebhookLuwjistik() int {
	rangeDate, err := strconv.Atoi(os.Getenv(`RANGE_DATE_TIME_SCHEDULER_WEBHOOK_LUWJISTIK`))
	if err != nil {
		return 5
	}

	return rangeDate
}

func (c *Config) LimitDataSchedulerWebhookLuwjistik() int {
	rangeDate, err := strconv.Atoi(os.Getenv(`LIMIT_DATA_SCHEDULER_WEBHOOK_LUWJISTIK`))
	if err != nil {
		return 100
	}

	return rangeDate
}

func (c *Config) UseRemoveProducerRtc() bool {
	flagging, err := strconv.ParseBool(os.Getenv(`USE_REMOVE_PRODUCER_RTC`))
	if err != nil {
		return false
	}

	return flagging
}

func (c *Config) NewCalculateVolumeWeightJumbopackDate() time.Time {
	date := os.Getenv(`NEW_CALCULATE_VOLUME_WEIGHT_JUMBOPACK_DATE`)
	parsed, err := time.Parse(FormatDate, date)
	if err != nil {
		parsed, _ = time.Parse(FormatDate, time.Now().Format(FormatDate))
		return parsed.AddDate(0, 0, 1)
	}
	return parsed
}

func (c *Config) NewCalculateVolumeWeighJumbopackkDivider() int {
	divider, err := strconv.ParseInt(os.Getenv(`NEW_CALCULATE_VOLUME_WEIGHT_JUMBOPACK_DIVIDER`), 0, 32)
	if divider <= 0 || err != nil {
		return 6000
	}
	return int(divider)
}

func (c *Config) IntervalWeekRemovingLogRtc() int {
	interval, err := strconv.Atoi(os.Getenv(`INTERVAL_WEEK_REMOVING_LOG_RTC`))
	if err != nil {
		return 2
	}

	return interval
}

func (c *Config) LimitRemovingLogRtc() int {
	interval, err := strconv.Atoi(os.Getenv(`LIMIT_REMOVING_LOG_RTC`))
	if err != nil {
		return 150
	}

	return interval
}

func (c *Config) LimitGetRetryCargos() int {
	limit, err := strconv.Atoi(os.Getenv(`LIMIT_GET_RETRY_CARGO`))
	if err != nil {
		return 1000
	}

	return limit
}

func (c *Config) TimeDiffGetRetryCargos() int {
	diff, err := strconv.Atoi(os.Getenv(`TIME_DIFF_GET_RETRY_CARGO`))
	if err != nil {
		return 120
	}

	return diff
}

func (c *Config) NgenDisableWebhook() bool {
	flagging, err := strconv.ParseBool(os.Getenv(`NGEN_DISABLE_WEBHOOK`))
	if err != nil {
		return false
	}

	return flagging
}

func (c *Config) MetabaseURL() string {
	return os.Getenv(`METABASE_URL`)
}

func (c *Config) MetabaseAuthUsername() string {
	return os.Getenv(`METABASE_AUTH_USERNAME`)
}

func (c *Config) MetabaseAuthPassword() string {
	return os.Getenv(`METABASE_AUTH_PASSWORD`)
}

func (c *Config) MetabasePathSttIncoming() string {
	return os.Getenv(`METABASE_PATH_STT_INCOMING`)
}

func (c *Config) DexAssessmentAssignListLimit() int {
	limit, err := strconv.Atoi(os.Getenv(`DEX_ASSESSMENT_ASSIGN_LIST_LIMIT`))
	if err != nil {
		return 250
	}

	return limit
}

func (c *Config) ProcessCargoV2CacheExpired() int {
	minute, err := strconv.Atoi(os.Getenv(`PROCESS_CARGO_V2_CACHE_EXPIRED`))
	if err != nil {
		return 5
	}

	return minute
}

func (c *Config) TimeLimitTo() int {
	ttl, _ := strconv.Atoi(os.Getenv(`TIME_LIMIT_TO`))
	if ttl == 0 {
		ttl = 10
	}

	return ttl
}

func (c *Config) IsDFODPasti() bool {
	isElexys, err := strconv.ParseBool(os.Getenv(`IS_DFOD_PASTI`))
	if err != nil {
		panic(err)
	}

	return isElexys
}

func (c *Config) IsValidationCourierCOD() bool {
	IsValidationCourierCOD, err := strconv.ParseBool(os.Getenv(`IS_VALIDATION_COURIER_COD`))
	if err != nil {
		panic(err)
	}

	return IsValidationCourierCOD
}

func (c *Config) MaxDepthRecursive() int {
	depth, _ := strconv.Atoi(os.Getenv(`MAX_DEPTH_RECURSIVE`))
	if depth == 0 {
		depth = 10
	}

	return depth
}

func (c *Config) EnableNgenNog() bool {
	enableNogNgen, err := strconv.ParseBool(os.Getenv(`ENABLE_NGEN_NOG`))
	if err != nil {
		return false
	}

	return enableNogNgen
}

func (c *Config) SummaryAnalyticDayInterval() int {
	interval, _ := strconv.Atoi(os.Getenv(`SUMMARY_ANALYTIC_DAY_INTERVAL`))
	if interval == 0 {
		interval = 7
	}
	return interval
}

func (c *Config) SttAssessmentTopicID() string {
	topicID := os.Getenv(`STT_ASSESSMENT_TOPIC_ID`)
	if topicID == `` {
		topicID = model.HydraSttAssessmentTopicID
	}

	return topicID
}

func (c *Config) IsSTTAssessment() bool {
	isSTTAssessment, err := strconv.ParseBool(os.Getenv(`IS_STT_ASSESSMENT`))
	if err != nil {
		return false
	}

	return isSTTAssessment
}

func (c *Config) PendingReconcileMaxPeriodeLookup() int {
	maxDayPending, _ := strconv.Atoi(os.Getenv(`PENDING_RECONCILE_MAX_PERIODE_LOOKUP`))
	if maxDayPending == 0 {
		maxDayPending = 1
	}

	return maxDayPending
}

func (c *Config) ReconcileDateRelease() time.Time {
	date := os.Getenv(`RECONCILE_DATE_RELEASE`)
	parsed, err := time.Parse(FormatDate, date)
	if err != nil {
		parsed, _ = time.Parse(FormatDate, `2024-10-10`)
		return parsed
	}
	return parsed
}

func (c *Config) SelectCargoDetailForGenerateSttDueDelay() int {
	delay, _ := strconv.Atoi(os.Getenv(`SELECT_CARGO_DETAIL_FOR_GENERATE_STT_DUE_DELAY`))
	if delay == 0 {
		delay = 300
	}

	return delay
}

func (c *Config) DexAssessmentUseDBMaster() bool {
	envStr := strings.TrimSpace(os.Getenv(`DEX_ASSESSMENT_USE_DB_MASTER`))
	envBool, err := strconv.ParseBool(envStr)
	if err != nil {
		return false
	}

	return envBool
}

func (c *Config) DexAssessmentIsUseQueryImprove() bool {
	envStr := strings.TrimSpace(os.Getenv(`DEX_ASSESSMENT_IS_USE_QUERY_IMPROVE`))
	envBool, err := strconv.ParseBool(envStr)
	if err != nil {
		return false
	}

	return envBool
}

func (c *Config) DefaultSelectLimitSttInDays() int {
	days, _ := strconv.Atoi(os.Getenv(`DEFAULT_SELECT_LIMIT_STT_IN_DAYS`))
	if days == 0 {
		days = 180 // 6 months
	}

	return days
}

func (c *Config) PtPosProduction() bool {
	v, _ := strconv.ParseBool(os.Getenv(`PT_POS_PRD`))

	return v
}

func (c *Config) PtPosURL() string {
	return os.Getenv(`PT_POS_URL`)
}

func (c *Config) PtPosBooking() bool {
	ptPosBooking, _ := strconv.ParseBool(os.Getenv(`PT_POS_BOOKING`))
	return ptPosBooking
}

func (c *Config) PtPosKey() string {
	return os.Getenv(`PT_POS_KEY`)
}

func (c *Config) PtPosSecret() string {
	return os.Getenv(`PT_POS_SECRET`)
}

func (c *Config) PtPosUserID() string {
	return os.Getenv(`PT_POS_USERID`)
}

func (c *Config) PtPosMemberID() string {
	return os.Getenv(`PT_POS_MEMBERID`)
}

func (c *Config) PtPosXPos() string {
	return os.Getenv(`PT_POS_X_POS`)
}

func (c *Config) PtPosXPassword() string {
	return os.Getenv(`PT_POS_X_PASSWORD`)
}

func (c *Config) PtPosXApiKey() string {
	return os.Getenv(`PT_POS_X_API_KEY`)
}

func (c *Config) PtPosServiceCode() string {
	serviceCode := os.Getenv(`PT_POS_SERVICE_CODE`)
	if serviceCode == `` {
		serviceCode = "240"
	}
	return serviceCode
}

func (c *Config) CancelRetryCargoCacheTime() int {
	cancelRetryCargoCacheTime, err := strconv.Atoi(os.Getenv(`CANCEL_RETRY_CARGO_CACHE_TIME`))
	if err != nil {
		return 10
	}
	return cancelRetryCargoCacheTime

}

func (c *Config) IsSTTPriorityTier() bool {
	isSTTPriorityTier, err := strconv.ParseBool(os.Getenv(`FEATURE_FLAG_STT_PRIORITY_TIER`))
	if err != nil {
		return false
	}

	return isSTTPriorityTier
}

func (c *Config) DexAssessmentCutoff() time.Time {
	date, err := time.Parse(time.RFC3339, os.Getenv(`DEX_ASSESSMENT_CUTOFF`))
	if err != nil {
		defaultDate, _ := time.Parse(time.RFC3339, "2024-01-01T00:00:00+07:00")
		return defaultDate
	}

	return date
}

func (c *Config) DexAssessmentFilterForDexAssessment() time.Time {
	date, err := time.Parse(time.RFC3339, os.Getenv(`START_FILTER_DEX_ASSESSMENT`))
	if err != nil {
		defaultDate, _ := time.Parse(time.RFC3339, "2024-11-28T00:00:00+07:00")
		return defaultDate
	}

	return date
}

func (c *Config) DexAssessmentBackfill() time.Time {
	date, err := time.Parse(time.RFC3339, os.Getenv(`DEX_ASSESSMENT_BACKFILL`))
	if err != nil {
		defaultDate, _ := time.Parse(time.RFC3339, "2024-11-23T00:00:00+07:00")
		return defaultDate
	}

	return date
}

func (c *Config) DexAssessmentUseV2Flow() bool {
	useV2Flow, err := strconv.ParseBool(os.Getenv(`DEX_ASSESSMENT_USE_V2_FLOW`))
	if err != nil {
		return false
	}

	return useV2Flow
}

type AssessmentRelabel struct {
	SttPrefixAllowed     []string
	ShortLinkPatern      string
	ShortLinkParamSecret string
}

func (c *Config) InitAssessmentRelabel() {
	assessmentRelabel := AssessmentRelabel{}
	list := os.Getenv(`STT_PREFIX_ALLOWED_TO_ASSESSMENT_RELABEL`)
	if list != `` {
		assessmentRelabel.SttPrefixAllowed = strings.Split(list, `,`)
	}
	assessmentRelabel.ShortLinkPatern = os.Getenv(`STT_ASSESSMENT_RELABEL_SHORT_LINK_PATERN`)
	if assessmentRelabel.ShortLinkPatern == `` {
		assessmentRelabel.ShortLinkPatern = "https://dev-lionparcel.com/relabel?code={code}"
	}
	assessmentRelabel.ShortLinkParamSecret = os.Getenv(`STT_ASSESSMENT_RELABEL_SHORT_LINK_PARAM_SECRET`)
	if assessmentRelabel.ShortLinkParamSecret == `` {
		assessmentRelabel.ShortLinkParamSecret = "LPsecretRelabel!"
	}
	c.assessmentRelabel = assessmentRelabel
}

func (c *Config) GetAssessmentRelabel() AssessmentRelabel {
	return c.assessmentRelabel
}

func (c *Config) HydraURL() string {
	return os.Getenv(`HYDRA_URL`)
}

func (c *Config) IsProofSignedUrl() bool {
	isProofSignedUrl, _ := strconv.ParseBool(os.Getenv("IS_PROOF_SIGNED_URL"))
	return isProofSignedUrl
}

func (c *Config) ExpiredDurationProofUrl() int {
	expiredDurationProof, _ := strconv.Atoi(os.Getenv(`TOKOPEDIA_PROOF_EXPIRED_DURATION_SECOND`))
	if expiredDurationProof == 0 {
		expiredDurationProof = 10
	}

	return expiredDurationProof
}

func (c *Config) IsReUploadVendorProof() bool {
	isReUploadVendorProof, _ := strconv.ParseBool(os.Getenv(`IS_REUPLOAD_VENDOR_PROOF`))
	return isReUploadVendorProof

}

func (c *Config) DelayGetFromSlave() int {
	delayGetFromSlave, err := strconv.Atoi(os.Getenv(`DELAY_GET_FROM_SLAVE`))
	if err != nil {
		return 150
	}
	return delayGetFromSlave
}

func (c *Config) DelayHideRequestPriorityDeivery() int {
	// in ms
	delayHideRequestPriorityDeiveryTime, err := strconv.Atoi(os.Getenv(`DELAY_HIDE_REQUEST_PRIORITY_DELIVERY`))
	if err != nil {
		return 150
	}
	return delayHideRequestPriorityDeiveryTime
}

func (c *Config) PtPosSchedulerTrackingInterval() int {
	v, err := strconv.Atoi(os.Getenv(`PT_POS_SCHEDULER_TRACKING_INTERVAL`))
	if err != nil {
		return 7
	}
	return v
}

func (c *Config) PtPosSchedulerTracking() bool {
	v, err := strconv.ParseBool(os.Getenv(`PT_POS_SCHEDULER_TRACKING`))
	if err != nil {
		return false
	}

	return v
}

func (c *Config) PtPosTrackingWorkers() int {
	wokers, err := strconv.Atoi(os.Getenv(`PT_POS_TRACKING_WORKERS`))
	if err != nil {
		return 10
	}
	return wokers
}

func (c *Config) PtPosUpdateSttBatchInterval() int {
	minutes, err := strconv.Atoi(os.Getenv(`PT_POS_TRACKING_BATCH_INTERVAL`))
	if err != nil {
		return 30
	}
	return minutes
}

func (c *Config) PtPosUpdateSttDuration() int {
	minutes, err := strconv.Atoi(os.Getenv(`PT_POS_UPDATE_STT_DURATION`))
	if err != nil {
		return 5
	}
	return minutes
}

func (c *Config) GetLimitedAssigned3LCRule() ([]LimitedAssigned3LCRule, error) {
	rule := []LimitedAssigned3LCRule{}

	ruleString := os.Getenv(`LIMITED_ASSIGNED_3LC_RULE`)
	if ruleString == "" {
		return rule, nil
	}

	err := json.Unmarshal([]byte(ruleString), &rule)
	if err != nil {
		return rule, err
	}

	return rule, nil
}

func (c *Config) GetDfodNewRuleCARetailEnable() bool {
	v, err := strconv.ParseBool(os.Getenv(`DFOD_NEW_RULE_CA_RETAIL_ENABLE`))
	if err != nil {
		return false
	}

	return v
}

func (c *Config) GetShipmentSubscriptionPriorities() map[string]bool {
	ssp := os.Getenv(`SHIPMENT_SUBSCRIPTIONS_PRIORITIES`)
	mapPriorities := make(map[string]bool)
	for _, priority := range strings.Split(ssp, ",") {
		mapPriorities[priority] = true
	}
	return mapPriorities
}

func (c *Config) GetShipmentSubscriptionPriorityDeliveryPrefixes() map[string]bool {
	prefixes := os.Getenv(`SHIPMENT_SUBSCRIPTIONS_PRIORITY_DELIVERY_PREFIXES`)
	mapPrefixes := make(map[string]bool)
	for _, prefix := range strings.Split(prefixes, ",") {
		mapPrefixes[prefix] = true
	}
	return mapPrefixes
}

// For Sabre
func (c *Config) SabreDomain() string {
	return os.Getenv(`SABRE_DOMAIN_URL`)
}

func (c *Config) SabreClientAuthURL() string {
	return c.SabreDomain() + os.Getenv(`SABRE_CLIENT_AUTH_URL`)
}

func (c *Config) SabreClientSearchFlight() string {
	return c.SabreDomain() + os.Getenv(`SABRE_CLIENT_SEARCH_FLIGHT`)
}

func (c *Config) SabreClientSecret() string {
	return os.Getenv(`SABRE_CLIENT_SECRET`)
}

func (c *Config) SabreSessionUsername() string {
	return os.Getenv(`SABRE_SESSION_USERNAME`)
}

func (c *Config) SabreSessionPassword() string {
	return os.Getenv(`SABRE_SESSION_PASSWORD`)
}

func (c *Config) SabreSessionCreateURL() string {
	return os.Getenv(`SABRE_SESSION_URL`)
}

func (c *Config) SabreItineraryCount() int {
	counter, err := strconv.Atoi(os.Getenv(`SABRE_ITINERARY_COUNT`))
	if err != nil || counter < 1 {
		counter = 4
	}
	return counter
}

func (c *Config) SabreAuthTimeLimit() int {
	timeout, err := strconv.Atoi(os.Getenv(`SABRE_AUTH_TIME_LIMIT`))
	if err != nil {
		return 5
	}
	return timeout
}

func (c *Config) SabreSearchAllowedFlightCode() map[string]bool {
	allowed := make(map[string]bool)
	allowedCodes := strings.Split(os.Getenv(`SABRE_ALLOWED_FLIGHT_CODE`), ",")
	if len(allowedCodes) == 0 {
		allowedCodes = append(allowedCodes, "JD")
	}
	for _, code := range allowedCodes {
		allowed[code] = true
	}
	return allowed
}

func (c *Config) HydraBookingCargoSabreTopicID() string {
	topicID := os.Getenv(`HYDRA_BOOKING_CARGO_SABRE`)
	if topicID == `` {
		topicID = model.HydraBookingCargoSabreTopicID
	}

	return topicID
}

func (c *Config) HydraBookingCargoSabreMaximumRetrying() int {
	retrying, _ := strconv.Atoi(os.Getenv(`HYDRA_BOOKING_CARGO_SABRE_MAXIMUM_RETRYING`))
	if retrying == 0 {
		retrying = 7
	}

	return retrying
}

func (c *Config) SabreDefaultValidationThreshold() int {
	threshold, _ := strconv.Atoi(os.Getenv(`SABRE_DEFAULT_VALIDATION_THRESHOLD`))
	if threshold == 0 {
		threshold = 720
	}

	return threshold
}

func (c *Config) SabreHitNTimesAdditionalSchedule() int {
	nTimesAdditinaonal, _ := strconv.Atoi(os.Getenv(`SABRE_N_TIMES_ADDITIONAL_SCHEDULE`))
	if nTimesAdditinaonal == 0 {
		nTimesAdditinaonal = 5
	}

	return nTimesAdditinaonal
}

func (c *Config) SabreScheduleCacheDuration() int {
	// in minute
	cacheDuration, _ := strconv.Atoi(os.Getenv(`SABRE_SCHEDULE_CACHE_DURATION`))
	if cacheDuration == 0 {
		cacheDuration = 60
	}

	return cacheDuration
}

func (c *Config) SabreScheduleFromPartyID() string {
	partyID := os.Getenv(`SABRE_SCHEDULE_FROM_PARTY_ID`)
	if partyID == "" {
		partyID = "LionAir"
	}

	return partyID
}

func (c *Config) SabreScheduleToPartyID() string {
	partyID := os.Getenv(`SABRE_SCHEDULE_TO_PARTY_ID`)
	if partyID == "" {
		partyID = "Sabre"
	}

	return partyID
}

func (c *Config) SabreDefaultTimeLimitSession() int {
	validationTime, _ := strconv.Atoi(os.Getenv(`SABRE_DEFAULT_TIMELIMIT_SESSION`))
	if validationTime == 0 {
		validationTime = 10
	}

	return validationTime
}

func (c *Config) SabreDefaultTimeLimitSessionLock() int {
	validationTime, _ := strconv.Atoi(os.Getenv(`SABRE_DEFAULT_TIMELIMIT_SESSION_LOCK`))
	if validationTime == 0 {
		validationTime = 5
	}

	return validationTime
}

func (c *Config) SabreMaxRetryCallback() int {
	retry, _ := strconv.Atoi(os.Getenv(`SABRE_MAX_RETRY_CALLBACK_SESSION`))
	if retry == 0 {
		retry = 3
	}
	return retry
}

func (c *Config) SabreCarrierCode() string {
	cd := os.Getenv(`SABRE_CARRIER_CODE`)
	if cd == "" {
		cd = "ID"
	}
	return cd
}

func (c *Config) NgenForceThreshold() bool {
	v, err := strconv.ParseBool(os.Getenv(`NGEN_FORCE_THRESHOLD`))
	if err != nil {
		return true
	}
	return v
}

func (c *Config) NgenForceThresholdTime() string {
	t := os.Getenv(`NGEN_FORCE_THRESHOLD_TIME`)
	_, err := strconv.Atoi(t)
	if err != nil {
		return "2"
	}
	return t
}

func (c *Config) GetMaxIterationFeedbackAndReview() int {
	maxIteration, err := strconv.Atoi(os.Getenv(`FEEDBACK_MAX_ITERATION`))
	if err != nil {
		maxIteration = 3
	}

	return maxIteration
}

func (c *Config) TrackingV1SttNoPrefixUseLatestHeader() map[string]bool {
	sttPrefix := map[string]bool{}
	prefixs := strings.ReplaceAll(os.Getenv(`TRACKING_V1_STT_NO_PREFIX_USE_LATEST_HEADER`), " ", "")
	if prefixs == "" {
		prefixs = "19,99,98,94,95,96"
	}

	for _, prefix := range strings.Split(prefixs, ",") {
		sttPrefix[prefix] = true
	}
	return sttPrefix
}

func (c *Config) TrackingV1ShipmentPrefixUseLatestHeader() map[string]bool {
	shipmentPrefix := map[string]bool{}
	prefixs := strings.ReplaceAll(os.Getenv(`TRACKING_V1_SHIPMENT_PREFIX_USE_LATEST_HEADER`), " ", "")
	if prefixs == "" {
		prefixs = "C1,C2"
	}

	for _, prefix := range strings.Split(prefixs, ",") {
		shipmentPrefix[prefix] = true
	}
	return shipmentPrefix
}

func (c *Config) GetStartDateAndEndDateDFODPasti() (time.Time, time.Time) {
	var startDateString, endDateString string
	configDate := os.Getenv(`START_DATE_END_DATE_DFOD_PASTI`)
	if configDate != `` {
		rangeDate := strings.Split(configDate, `,`)
		startDateString = rangeDate[0]
		endDateString = rangeDate[1]
	}
	startDate, err := time.Parse(FormatDate, startDateString)
	if err != nil {
		startDate, _ = time.Parse(FormatDate, "2025-03-10")
	}
	endDate, err := time.Parse(FormatDate, endDateString)
	if err != nil {
		endDate, _ = time.Parse(FormatDate, "2025-04-11")
	}

	return startDate, endDate
}

func (c *Config) IsUseCompareNgenFlight() bool {
	v, err := strconv.ParseBool(os.Getenv(`IS_USE_COMPARE_NGEN_FLIGHT`))
	if err != nil {
		return false
	}
	return v
}

func (c *Config) RTSFraudTriggerHistoryReasonCodes() map[string]bool {
	reasonCodes := map[string]bool{}
	configCodes := strings.ReplaceAll(os.Getenv(`RTS_FRAUD_TRIGGER_HISTORY_REASON_CODES`), " ", "")
	if configCodes == "" {
		configCodes = "RES38,RES55,RES56,RES78,RES96"
	}

	for _, prefix := range strings.Split(configCodes, ",") {
		reasonCodes[prefix] = true
	}
	return reasonCodes
}

func (c *Config) HydraBulkSTTRepairTopicID() string {
	topicID := os.Getenv(`HYDRA_BULK_STT_REPAIR_TOPIC_ID`)
	if topicID == `` {
		topicID = model.HydraBulkSTTRepairTopicID
	}

	return topicID
}

// AlgoPosAuthUsername ...
func (c *Config) AlgoPosAuthUsername() string {
	return os.Getenv(`ALGO_POS_USERNAME`)
}

// AlgoPosAuthPassword ...
func (c *Config) AlgoPosAuthPassword() string {
	return os.Getenv(`ALGO_POS_PASSWORD`)
}

// AlgoPosAuthRole ...
func (c *Config) AlgoPosAuthRole() string {
	return os.Getenv(`ALGO_POS_ROLE`)
}
func (c *Config) CargoDividerPlane() int {
	divider, _ := strconv.Atoi(os.Getenv(`CARGO_DIVIDER_PLANE`))
	if divider == 0 {
		divider = 5000
	}
	return divider
}

func (c *Config) CargoDividerNonPlane() int {
	divider, _ := strconv.Atoi(os.Getenv(`CARGO_DIVIDER_NON_PLANE`))
	if divider == 0 {
		divider = 6000
	}
	return divider
}

func (c *Config) IsActiveCargoRecalculate() bool {
	v, err := strconv.ParseBool(os.Getenv(`IS_ACTIVE_CARGO_RECALCULATE`))
	if err != nil {
		return true
	}
	return v
}

func (c *Config) CacheJsonPayloadCargoV2Duration() int {
	// in minute
	cacheDuration, _ := strconv.Atoi(os.Getenv(`CACHE_JSON_PAYLOAD_CARGOV2_DURATION`))
	if cacheDuration == 0 {
		cacheDuration = 5
	}

	return cacheDuration
}

func (c *Config) CustomerTypePTPosPickup() int {
	v, err := strconv.Atoi(os.Getenv(`CUSTOMER_TYPE_PTPOS_PICKUP`))
	if err != nil {
		return 7
	}
	return v
}

func (c *Config) ServiceTypePTPosPickup() int {
	v, err := strconv.Atoi(os.Getenv(`SERVICE_TYPE_PTPOS_PICKUP`))
	if err != nil {
		return 1
	}
	return v
}

func (c *Config) StiDestCreateToNinja() bool {
	v, err := strconv.ParseBool(os.Getenv(`STI_DEST_CREATE_TO_NINJA`))
	if err != nil {
		return true
	}
	return v
}

func (c *Config) StiDestCreateToJNE() bool {
	v, err := strconv.ParseBool(os.Getenv(`STI_DEST_CREATE_TO_JNE`))
	if err != nil {
		return true
	}
	return v
}

func (c *Config) StiDestCreateToPtPos() bool {
	v, err := strconv.ParseBool(os.Getenv(`STI_DEST_CREATE_TO_PT_POS`))
	if err != nil {
		return false
	}
	return v
}

func (c *Config) IsHandoverScanPartnerValidVendor() map[string]bool {
	// JNE,NINJA,PI,JNT
	v := os.Getenv(`IS_HANDOVER_SCAN_PARTNER_VALID_VENDOR`)
	if v == "" {
		return map[string]bool{
			"JNE":   true,
			"NINJA": true,
			"PI":    true,
			"JNT":   true,
		}
	}

	vMap := map[string]bool{}
	for _, vendorCode := range strings.Split(v, ",") {
		vMap[vendorCode] = true
	}

	return vMap
}

func (c *Config) IsValidHandoverVendorType() map[string]bool {
	// JNE,NINJA,PI,JNT,VENDORINTERNAL,LUWJISTIK
	v := os.Getenv(`IS_VALID_HANDOVER_VENDOR_TYPE`)
	if v == "" {
		return map[string]bool{
			"JNE":            true,
			"NINJA":          true,
			"PI":             true,
			"JNT":            true,
			"VENDORINTERNAL": true,
			"LUWJISTIK":      true,
		}
	}

	vMap := map[string]bool{}
	for _, vendorCode := range strings.Split(v, ",") {
		vMap[vendorCode] = true
	}

	return vMap
}

func (c *Config) TokopediaAuthToken() string {
	return os.Getenv(`TOKOPEDIA_AUTH_TOKEN`)
}

func (c *Config) ReasonDexExclude() []string {
	reasons := os.Getenv(`REASON_DEX_EXCLUDE`)

	if reasons == `` {
		return []string{}
	}

	return strings.Split(reasons, ",")
}

func (c *Config) ReasonDexMaskingDiff() string {
	reasons := os.Getenv(`REASON_DEX_MASKING_DIFF`)

	if reasons == `` {
		return "RES49"
	}

	return reasons
}
