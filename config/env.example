SERVICE_NAME=hydra
DEVELOPMENT=1
ENVIRONMENT=dev-genesis
PORT= 8081

DB_MASTER_HOST= $DB_MASTER_HOST
DB_MASTER_USERNAME= hydra
DB_MASTER_PASSWORD= $hydra_PASSWORD_DB
DB_MASTER_NAME= hydra
DB_MASTER_PORT= 3306

DB_SLAVE_HOST= $DB_SLAVE_HOST
DB_SLAVE_USERNAME= hydra
DB_SLAVE_PASSWORD= $hydra_PASSWORD_DB
DB_SLAVE_NAME= hydra
DB_SLAVE_PORT= 3306

DB_SLAVE_REPORT_HOST= localhost
DB_SLAVE_REPORT_USERNAME= root
DB_SLAVE_REPORT_PASSWORD= root
DB_SLAVE_REPORT_NAME=horde 
DB_SLAVE_REPORT_PORT= 8889

DB_HOLMES_HOST= localhost
DB_HOLMES_USERNAME=root
DB_HOLMES_PASSWORD=root
DB_HOLMES_NAME=holmes
DB_HOLMES_PORT=8889

DB_HOLMES_V2_HOST=
DB_HOLMES_V2_USERNAME=
DB_HOLMES_V2_PASSWORD=
DB_HOLMES_V2_NAME=
DB_HOLMES_V2_PORT=
STT_REPORTS_V2_TABLE_NAME=
DTPOL_REPORTS_TABLE_NAME=dtpol_reports

DB_SLAVE_EXTERNAL_HOST= localhost
DB_SLAVE_EXTERNAL_USERNAME= root
DB_SLAVE_EXTERNAL_PASSWORD= root
DB_SLAVE_EXTERNAL_NAME= hydra
DB_SLAVE_EXTERNAL_PORT= 3306

REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_DB=1
REDIS_PASSWORD=
REDIS_TLS=false

# token verification
REDIS_HOST_TOKEN_VERIFICATION=127.0.0.1:6379
REDIS_PASSWORD_TOKEN_VERIFICATION=
REDIS_DB_TOKEN_VERIFICATION=8
REDIS_MAX_RETRIES_TOKEN_VERIFICATION=0
REDIS_DIAL_TIMEOUT_SECOND_TOKEN_VERIFICATION=0
REDIS_INSECURE_SKIP_VERIFY_TOKEN_VERIFICATION=true

TOKEN_NAME="X-LionParcel-AccessToken"
TOKEN_TYPE=Bearer
TOKEN_TEMPORARY_LIFETIME=300
ACCESS_TOKEN_LIFETIME=24

ELASTIC_URL=https://de1f8276dcc843d6931ba5ca65508190.asia-southeast1.gcp.elastic-cloud.com:9243
ELASTIC_USERNAME=$ELASTIC_USERNAME
ELASTIC_PASSWORD=$ELASTIC_PASSWORD

EMAIL_SOURCE= <EMAIL>
EMAIL_NAME= Lion Parcel
EMAIL_URL= https://api.sendgrid.com
EMAIL_END_POINT= /v3/mail/send
EMAIL_API_KEY= $EMAIL_API_KEY

BULK_DEFAULT_PASSWORD=123456
FRONT_END_DEV_URL=https://dev-genesis.thelionparcel.com
FRONT_END_STAGING_URL=https://stg-genesis.thelionparcel.com
FRONT_END_PROD_URL=https://genesis.thelionparcel.com
EMAIL_TEMPLATE_ID_RESET_PASSWORD=d-0821524760f04ee9bb11515157a8a118
EMAIL_TEMPLATE_ID_RESI_SEND_EMAIL=d-ccf6b931232543b6953b62172cc44eaf

HORDE_URL=https://api.stg-genesis.lionparcel.com/horde
GOBER_URL=https://api.stg-genesis.lionparcel.com/gober

ALGO_URL=https://f2b56d2cf45554afce89f9d04de5e904.m.pipedream.net

NINJA_URL=
NINJA_CLIENT_ID=
NINJA_CLIENT_SECRET=
NINJA_COUNTRY=
NINJA_BOOKING=true

ALGO_AUTH_USERNAME=
ALGO_AUTH_PASSWORD=
ALGO_AUTH_ROLE=

PUBSUB_PROJECT_ID=
ENV_ALGO_PUBSUB= // dev, stg, prd

ELASTIC_APM_SERVICE_NAME=hydra
ELASTIC_APM_SERVER_URL=https://d98920e1473442b7a2fc31299b635b0d.apm.us-east-1.aws.cloud.es.io:443
ELASTIC_APM_SECRET_TOKEN=lxo2Tr1ZlTyM62no0w

POS_STT_DEL_INTERVAL=
POS_STT_DEX_INTERVAL=
STT_DASHBOARD_INSIGHT_INTERVAL=

NGEN_URL=https://book.aircargolion.com
USERNAME_NGEN=
PASSWORD_NGEN=

HYDRA_USERNAME=
HYDRA_PASSWORD=

HUB9_URL_API_SMS=
HUB9_SMS_PARAMS_SC=
HUB9_REGULER_SMS_UID=
HUB9_REGULER_SMS_PWD=
HUB9_PREMIUM_SMS_UID=
HUB9_PREMIUM_SMS_PWD=
HUB9_URL_API_WA=
HUB9_LONGNUMBER_WA_UID=
HUB9_LONGNUMBER_WA_PWD=

YM_URL_API_WA=
YM_PREMIUM_WA_TOKEN=
YM_PREMIUM_TTL_BODY=
YM_PREMIUM_TYPE_BODY=
YM_PREMIUM_NAMESPACE_HSM_BODY=
YM_PREMIUM_POLICY_LANGUAGE_HSM_BODY=
YM_PREMIUM_CODE_LANGUAGE_HSM_BODY=
YM_BOT_ID_LP_CUSTOMER=
YM_BOT_ID_LP_MITRA=
YM_BOT_ID_LP_KVP=

API_KEY_ELEXYS=
API_URL_ELEXYS=
INTERNAL_ELEXYS_POS_CODE=
ELEXYS_BASIC_AUTH=

SUBMIT_STT_TOPIC_ID=submit_stt_dummy  # submit_stt
UPDATE_STATUS_STT_TOPIC_ID=update_status_stt
ADJUSTMENT_STT_TOPIC_ID=adjustment_stt
GOBER_TRANSACTION_TOPIC_ID=
HYDRA_RETRY_CARGO_TOPIC_ID=hydra_retry_cargo
GOBER_REVERT_BOOKING_COD_TOPIC_ID=


MIDDLEWARE_URL=

WEBHOOK_SLACK=*******************************************************************************
SLACK_NOTIFIER=true
TOKEN_DISCORD=

MAX_RETRY_PICKUP_MANIFEST=3 # default max 3

NINJA_VALIDATE_REQUEST=false # true or false

ELEXYS_ACTIVE=true

STT_MANUAL_LIMIT=2000
# increase expiration when stt manual limit also increase
STT_MANUAL_EXPIRATION=2
STT_MANUAL_UPSERT=true
STT_MANUAL_NOSQL_ENABLE=true

TIMEOUT_VENDOR_IN_MINUTES= 5 # in minutes
TIMEOUT_COMMISSION=180 # in seconds
CUSTOM_TIMEOUT=40
MAX_CUSTOM_TIMEOUT_NGEN=10
CLOUD_SCHEDULER_AUTH=

BOOKING_TRANSACTION_SCHEDULER_LIMIT=1000
BOOKING_TRANSACTION_SCHEDULER_LIMIT_MULTIPLIER=1

JNE_URL=
JNE_USERNAME=
JNE_API_KEY=
JNE_BOOKING=
JNE_CUSTOMER=

JNE_BASIC_AUTH=

DEFAULT_LIMIT_PAGINATION=5

MONGODB_URI=mongodb://localhost:27017/?maxPoolSize=20&maxIdleTimeMS=60000
MONGODB_USER=
MONGODB_PW=
MONGODB_AUTH_SOURCE=hydra-dev
MONGODB_DATABASE=hydra

STT_REPORTS_TABLE_NAME=stt_reports_v2

DASHBOARD_DATE_DEFAULT=1
DASHBOARD_DATE_DEFAULT=1
RANGE_DATE_SCHEDULER_NGEN=2
SCHEDULER_NGEN_SIGNATURE=
SCHEDULER_MESSAGE_RETRY_SIGNATURE=

LIMIT_DTPOL_REPORTS=10000
REVERSE_DASHBOARD_SUMMARY_TABLE_NAME=reverse_outgoing_summary
REVERSE_DASHBOARD_TABLE_NAME=reverse_dashboard
REVERSE_INCOMING_SUMMARY_TABLE_NAME=reverse_incoming_summary
REVERSE_INCOMING_TABLE_NAME=reverse_incoming
GMA_DASHBOARD_TABLE_NAME=stt_gma_dashboard
LIMIT_TIER_X_REPORTS=1000

DELIVERY_FEE_DTPOL_ACTIVE_DATE=2022-07-01
PUBLISH_MESSAGE_RETRY=3

GOBER_STT_SHIPMENT_PAYMENT_NOTIFICATION_TOPIC_ID=gober_stt_shipment_payment_notification

ENABLE_FEATURE_HOLD_COMMISSION=true

DROPOFF_TRUCKING_DTPOL_ACTIVE_DATE=2022-08-01

CHECK_STT_NO_DUPLICATION_RETRY=5
ENABLE_CHECK_STT_NO_DUPLICATION_RETRY=true

PROGRESSIVE_COMMISSION_EXPIRATION=1
REVERSE_OUTGOING_TABLE_NAME=

ENABLE_AWB_RESERVE_SPECIFIC_CONSOLE=
ENABLE_RETRIEVE_AWB_RESERVE=

ENABLE_RETRIEVE_AWB_RESERVE=

HYDRA_PROGRESSIVE_COMMISSION_TO_GOBER_TOPIC_ID=

GOBER_PROGRESSIVE_COMMISSION_TOPIC_ID=

SCHEDULER_CLIENT_ID=2a6cc254-7ce6-477d-9e53-216dcc261514
SCHEDULER_CLIENT_SECRET=19bf96ec-dc21-4df4-9024-c985b83159f4

HYDRA_CALCULATE_RETAIL_TARIFF_TOPIC_ID=hydra_calculate_retail_tariff

GENERAL_RETRY=3
GENERAL_DELAY_RETRY=2

COD_DASHBOARD_TABLE_NAME=cod_dashboard


SCHEDULER_BOOKING_LUWJISTIK_RETRY_SIGNATURE=

LUWJISTIK_URL=
LUWJISTIK_API_SECRET_KEY=
LUWJISTIK_API_APP_ID=
LUWJISTIK_WEBHOOK_CLIENT_ID=
LUWJISTIK_STATUS=
LUWJISTIK_ACTIVE=true

CORPORATE_DASHBOARD_TABLE_NAME=corporate_dashboard
DEFAULT_LIMIT_ROW_QUERY_REDSHIFT=2000

CLAIM_HISTORY_TABLE_NAME=stt_claim_history

REVERSE_RTS_JOURNEY=true
REVERSE_CNX_JOURNEY=true

DEFAULT_DISTRICT_RTS_RTSHQ=DKI00102

RETRY_CARGO_ENABLE=true

RETRY_BOOKING_BASIC_AUTH=

USERNAME_BASIC_AUTHENTICATION=
PASSWORD_BASIC_AUTHENTICATION=

ALLOW_SET_TO_PODDEX=true

GOBER_POS_PARENT_COMMISSION_TOPIC_ID=gober_stt_pos_parent_commission

ALLOW_STI_DEST_PICKUP_TRUCKING=true

HYDRA_CROSS_DOCKING_GENERATE_STT_MANUAL_TOPIC_ID=hydra_cross_docking_generate_stt_manual

DEFAULT_LIMIT_STT_MANUAL_UNUSED = 100
GENERATE_TOTAL_STT=100

PEGASUS_USERNAME=
PEGASUS_PASSWORD=

ENABLE_CONFIRM_TO_CA=false

ENABLE_UPDATE_PROMO_TOTAL_AMOUNT=true
CHANNEL_NOTIFY_UPDATE_STT_PROMO=

STT_ID_PROMO_AFTER=


HYDRA_RTC_SET_INACTIVE_BATCH_PER_ROW=

SALESFORCE_AUTH_URL=https://test.salesforce.com
SALESFORCE_URL=https://lionparcel--finaluat.sandbox.my.salesforce.com
SF_CLIENT_ID=3MVG9epeuLMxKP_J7HBhkejolDF6bg3TxUIUnNYzKV.fjLhLhIl280P7x_ON4EwvJjbG38BlzGI0e75tfTCTS
SF_CLIENT_SECRET=E0B37BC37BB7D60117EBAF92CA9D4CF1EF7AF857AAD40C0B9020485266CDDBC0
SF_USERNAME=<EMAIL>
SF_PASSWORD=Salesforce2020Po44QUj0waCubPTHPD7QEdkFy
SF_MOCK_DATA_SUCCESS=true
GENESIS_SF_EXCLUDE_ACTOR=Genesis Integration User

GET_PARTNER_ID_CGK=
GET_PARTNER_CODE_CGK=
GET_PARTNER_NAME_CGK=

GENESIS_SF_USERNAME=genesis_lionparcel
GENESIS_SF_PASSWORD=genesis_lionparcel

NGEN_AWB_BOOKING_EXPIRED=

ENABLE_CONFIG_NGEN_CANCEL=false

RATE_VAT_SHIPMENT=1.1
RATE_VAT_COD=11.0

EXCLUDE_CITY_BLOCKED_OUTSTANDING=CGK;BDG;PLM
CLOUDBEES_KEY=64b519dbb8bd8b8ffad29cea
CLOUDBEES_DEV_MODE_KEY=

USE_VALIDATION_BAG_CARGO_RTC=false
IS_USE_VALIDATION_STTBAG_DESTINATION = false

IS_USE_VALIDATION_BAGGING_GROUP_STISC=false

LIMIT_REPORT=10000

NEW_CALCULATE_VOLUME_WEIGHT_BIGPACK_DATE = 2023-10-03
NEW_CALCULATE_VOLUME_WEIGHT_BIGPACK_DIVIDER = 6000

ENABLE_NGEN_V4=false

USE_OLD_LUWJISTIK_WEBHOOK=false

USE_REMOVE_PRODUCER_RTC=false

HYDRA_CARGO_RTC_REMOVE_TOPIC_ID=hydra_cargo_rtc_remove

INTERVAL_WEEK_REMOVING_LOG_RTC=2
LIMIT_REMOVING_LOG_RTC=150

METABASE_URL=http://*************:9300
METABASE_AUTH_USERNAME='lpdata'
METABASE_AUTH_PASSWORD='lpdatateams'
METABASE_PATH_STT_INCOMING='/get_data_testing'

PUBSUB_TRUCKING_MAX_OUTSTANDING_MESSAGES=100
IS_VALIDATION_COURIER_COD=false

MAX_DEPTH_RECURSIVE=10
DEX_ASSESSMENT_CUTOFF="2024-01-01T00:00:00+07:00"

ENABLE_NGEN_NOG=true

DEX_ASSESSMENT_USE_DB_MASTER=false
DEX_ASSESSMENT_IS_USE_QUERY_IMPROVE=true

SUMMARY_ANALYTIC_DAY_INTERVAL=7

PENDING_RECONCILE_MAX_PERIODE_LOOKUP=1
RECONCILE_DATE_RELEASE="2024-09-20"

PT_POS_URL=
PT_POS_BOOKING=true
PT_POS_KEY=
PT_POS_SECRET=
PT_POS_MEMBERID=
PT_POS_X_POS=
PT_POS_X_PASSWORD=
PT_POS_SERVICE_CODE=
CUSTOMER_TYPE_PTPOS_PICKUP=1
SERVICE_TYPE_PTPOS_PICKUP=1

KEY_ENCRYPTION_DATABASE=
STT_PREFIX_ALLOWED_TO_ASSESSMENT_RELABEL=10LP,11LP
STT_ASSESSMENT_RELABEL_SHORT_LINK_PATERN=https://dev-lionparcel.com/relabel?code={code}
STT_ASSESSMENT_RELABEL_SHORT_LINK_PARAM_SECRET=LPsecretRelabel!

HYDRA_URL=https://api.dev-genesis.lionparcel.com/hydra

IS_PROOF_SIGNED_URL=true
TOKOPEDIA_PROOF_EXPIRED_DURATION_SECOND=259200 # 3 days
IS_REUPLOAD_VENDOR_PROOF=true
DELAY_GET_FROM_SLAVE=150 // in millisecond

DFOD_NEW_RULE_CA_RETAIL_ENABLE=true # boolean (true/false)

SABRE_DOMAIN_URL=sabre
SABRE_CLIENT_AUTH_URL=/v2/auth/token/
SABRE_CLIENT_SEARCH_FLIGHT=/v4/dc/products/air/search?jipcc=IDDC
SABRE_CLIENT_SECRET=secret
SABRE_AUTH_TIME_LIMIT=0
SABRE_ITINERARY_COUNT=0
SABRE_ALLOWED_FLIGHT_CODE=JT,ID,IW,IU

SABRE_SESSION_USERNAME=234577
SABRE_SESSION_PASSWORD=SSUCS200
SABRE_SESSION_URL=https://webservices.cert.platform.sabre.com

SABRE_DEFAULT_TIMELIMIT_SESSION=13
SABRE_DEFAULT_TIMELIMIT_SESSION_LOCK=5
SABRE_MAX_RETRY_CALLBACK_SESSION=3

CARGO_SEARCH_FLIGHT_MAX_FLIGHT=4
LPTOOL_STATIC_ENCRYPTION_KEY=xFAxTaMTbfu3MR54xFAxTaMTbfu3MR54

ALGO_POS_USERNAME=genesis-server
ALGO_POS_PASSWORD=Sup3rsecret\$Passw0rd!
ALGO_POS_ROLE=GENESIS



STI_DEST_CREATE_TO_NINJA=true
STI_DEST_CREATE_TO_JNE=true
STI_DEST_CREATE_TO_PT_POS=false

TOKOPEDIA_AUTH_TOKEN=
REASON_DEX_EXCLUDE=RES1,RES2
