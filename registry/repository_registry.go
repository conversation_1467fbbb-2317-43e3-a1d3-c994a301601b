package registry

import (
	"sync"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/config/firebase"
	cod_dfod_fraud_detection "github.com/Lionparcel/hydra/src/modules/cod_dfod_fraud_detection/risk_classification/repository"
	repo "github.com/Lionparcel/hydra/src/modules/dex_assessment/dashboard_consolidator/repository"
	"github.com/Lionparcel/hydra/src/repository"
)

// RepositoryRegistry ...
type RepositoryRegistry interface {
	SttManual() repository.SttManualRepository
	Client() repository.ClientRepository
	Partner() repository.PartnerRepository
	Stt() repository.SttRepository
	Commodity() repository.CommodityRepository
	SttOptionalRate() repository.SttOptionalRateRepository
	District() repository.DistrictRepository
	CheckTariff() repository.CheckTariffRepository
	Account() repository.AccountRepository
	Product() repository.ProductTypeRepository
	SttPiece() repository.SttPiecesRepository
	City() repository.CityRepository
	EstimateSla() repository.EstimateSlaRepository
	ShipmentPacket() repository.ShipmentPackageRepository
	Shipment() repository.ShipmentRepository
	PickupManifest() repository.PickupManifestRepository
	SttPieceHistory() repository.SttPieceHistoryRepository
	Sti() repository.StiRepository
	StiDetail() repository.StiDetailRepository
	Driver() repository.DriverRepository
	Vehicle() repository.VehicleRepository
	Bag() repository.BagRepository
	BagVendor() repository.BagOrchestraRepository
	BagDetail() repository.BagDetailRepository
	StiDest() repository.StiDestRepository
	StiDestDetail() repository.StiDestDetailRepository
	CargoDetail() repository.CargoDetailRepository
	Handover() repository.HandoverRepository
	Cargo() repository.CargoRepository
	HandoverDetail() repository.HandoverDetailRepository
	RouteRepository() repository.RouteRepository
	CacheRepository() repository.CacheRepository
	Reason() repository.ReasonRepository
	DeliveryManifest() repository.DeliveryManifestRepository
	Delivery() repository.DeliveryRepository
	Pod() repository.PodRepository
	DeliveryManifestDetail() repository.DeliveryManifestDetailRepository
	ReasonMapping() repository.ReasonMappingRepository
	CustomProcess() repository.CustomProcessRepository
	ConfigurablePrice() repository.ConfigurablePriceRepository
	PartnerLog() repository.PartnerLogRepository
	BalanceLimit() repository.BalanceLimitRepository
	Wallet() repository.WalletRepository
	CargoProductType() repository.CargoProductTypeRepository
	CommodityGroup() repository.CommodityGroupRepository
	Ngen() repository.NgenRepository
	Airport() repository.AirportRepository
	DeliveryVendor() repository.DeliveryVendorRepository
	Ninja() repository.NinjaRepository
	SttVendor() repository.SttVendorRepository
	MessageGateway() repository.MessageGatewayRepository
	Elexys() repository.ElexysRepository
	SttElexys() repository.SttElexysRepository
	MiddlewareClient() repository.MiddlewareCLient
	Health() repository.HealthRepository
	Transaction() repository.TransactionRepository
	Report() repository.ReportRepository
	LogSttFailedElexys() repository.LogFailedSttElexysRepository
	LogElexysPickupManifest() repository.LogElexysPickupManifestRepository
	Util() repository.UtilRepository
	BulkDownload() repository.BulkDownloadRepository
	SttTransaction() repository.SttTransactionRepository
	LogFailedDTPOL() repository.LogFailedDTPOLRepository
	CargoFlight() repository.CargoFlightRepository
	PartnerLocation() repository.PartnerLocationRepository
	SttActivity() repository.SttActivityRepository
	SttReport() repository.STTReportRepository
	KejarcuanReport() repository.KejarcuanReportRepository
	OutgoingReport() repository.OutgoingReportRepository
	JNE() repository.JNERepository
	AlgoRepo() repository.AlgoRepository
	Storage() repository.StorageRepository
	Xlsx() repository.XlsxRepository
	Time() repository.TimeRepository
	MissrouteReport() repository.MissrouteReportRepository
	InstantBooking() repository.InstantBookingRepository
	InstantBookingDetail() repository.InstantBookingDetailRepository
	DtpolReports() repository.DtpolReportsRepository
	Checkout() repository.CheckoutRepository
	Dashboard() repository.DashboardRepository
	LogMessage() repository.LogMessageRepository
	Country() repository.CountryRepository
	ProgressiveCommissionReport() repository.ProgressiveCommissionReportRepository
	ReadSttPaid() repository.ReadSttPaidRepository
	PredefinedHoliday() repository.PredefinedHoliday
	RebuttalDex() repository.RebuttalDexRepository
	CustomProcessRole() repository.CustomProcessRoleRepository
	SttCODDashboardRepo() repository.SttCODDashboardRepository
	RetryPubsubRepo() repository.RetryPubsubRepository
	Slack() repository.SlackRepository
	Luwjistik() repository.LuwjistikRepository
	RetryLuwjistik() repository.RetryLuwjistikRepository
	ProgressiveCommissionConfig() repository.ProgressiveCommissionConfigRepository
	SttPaymentHistoryRepository() repository.SttPaymentHistoryRepository
	ExchangeRate() repository.ExchangeRateRepository
	BaggingGroupLocation() repository.BaggingGroupLocationRepository
	CorporateDashboardRepo() repository.CorporateDashboardRepository
	ClaimHistoryRepo() repository.ClaimHistoryRepository
	SttCorporateRepo() repository.SttCorporateRepository
	SttReverseJourney() repository.SttReverseJourneyRepository
	PosAffiliate() repository.PosAffiliateRepository
	HistoryManifest() repository.HistoryManifestRepository
	RetryCargo() repository.RetryCargoRepository
	RetryCargoDetail() repository.RetryCargoDetailRepository
	ReadCargo() repository.ReadCargoFlightRepository
	ReadyToCargo() repository.ReadyToCargoRepository
	BaggingReadyToCargo() repository.BaggingReadyToCargoRepository
	CargoConfiguration() repository.CargoConfigurationRepository
	BaggingReadyToCargoHistory() repository.BaggingReadyToCargoHistoryRepository
	BagVendorRepo() repository.BagVendorRepository
	CrossDockingFailedRepo() repository.CrossDockingFailedRepository
	HydraConfig() repository.HydraConfigRepository
	BagCargo() repository.BagCargoRepository
	BagCommodityGroup() repository.BagCommodityGroupRepository
	SttReadyToCargoRepo() repository.SttReadyToCargoRepository
	CargoReserve() repository.CargoReserveRepository
	SttReadyToCargoHistoryRepo() repository.SttReadyToCargoHistoryRepository
	CutOffReadyToCargoRepo() repository.CutOffReadyToCargoRepository
	Hub() repository.HubRepository
	StoSc() repository.StoScRepository
	ResolutionCentre() repository.ResolutionCentreRepository
	CaseCategory() repository.CaseCategoryRepository
	SalesforceRepository() repository.SalesForceRepository
	DiscussionForumRepository() repository.DiscussionForumRepository
	FirebaseRepository() repository.FirebaseRepository
	RtcCityGroupRepository() repository.RtcCityGroupRepository
	CodConfig() repository.CodConfigRepository
	NotificationPenalty() repository.NotificationPenaltyRepository
	DeliveryPICRepository() repository.DeliveryPicRepository

	EmbargoConfig() repository.EmbargoConfigRepository
	CityBalance() repository.CityBalanceRepository

	HoldBalanceHistory() repository.HoldBalanceHistoryRepository

	FlagManagementRepository() repository.FlagManagementRepository
	UploadRepository() repository.UploadRepository

	LogRtcRepository() repository.LogRtcRepository
	DexAssessmentRepo() repository.DexAssessmentRepository
	DexFakeDexConfiguration() repository.DexFakeDexConfigurationRepository
	DexAssessmentDashboardRepo() repository.DexAssessmentDashboardRepository
	DexAssessmentDashboardV2Repo() repository.DexAssessmentDashboardV2Repository
	ShortlinkRepository() repository.ShortlinkRepository
	CsAccountLogRepo() repository.CsAccountLogRepository
	ClearanceRepo() repository.ClearanceRepository
	ReleaseRepository() repository.ReleaseRepository
	ReleaseDetailRepository() repository.ReleaseDetailRepository
	DexAssessmentDashboardConsolidatorRepository() repo.DexAssessmentDashboardConsolidatorRepository
	ReasonVendorMapping() repository.ReasonVendorMappingRepository
	PickupManifestCbpRepo() repository.PickupManifestCbpRepository
	PriorityDeliveryRepository() repository.PriorityDeliveryRepository
	SttReportPostgresRepository() repository.SttReportPostgresRepository
	StiDestTemporaryRepository() repository.StiDestTemporaryRepository
	RequestPriorityDeliveryRepository() repository.RequestPriorityDeliveryRepository
	StiTemporaryRepository() repository.StiTemporaryRepository
	PickupCorporateRepository() repository.PickupCorporate
	DtpolRepository() repository.DtpolRepository
	SttDueRepository() repository.SttDueRepository
	SttDueHistoryRepository() repository.SttDueHistoryRepository
	InternationalDocumentRepository() repository.InternationalDocumentRepository
	SttAssessmentRepository() repository.SttAssessmentRepository
	CampaignSttQuoteRepository() repository.CampaignSttQuoteRepository
	PickupCorporateAddress() repository.PickupCorporateAddressRepository
	PtPos() repository.PtPosRepository
	StiSCRepository() repository.StiSCRepository
	Feedback() repository.FeedbackRepository
	NotificationRepository() repository.NotificationRepository
	PtposRequestRepository() repository.PtposRequestRepository
	SttCustomFlagRepository() repository.STTCustomFlagRepository
	CargoConfigurationSearchFlight() repository.CargoConfigurationSearchFlightRepository
	SabreRepository() repository.SabreContractRepository
	ConfigDfodPasti() repository.ConfigDfodPastiRepository
	ReadyToCargoHistory() repository.ReadyToCargoHistoryRepository
	RiskClassificationRepository() cod_dfod_fraud_detection.RiskClassificationRepository
	DispatchRepository() repository.DispatchRepository
	AlgoPosRepo() repository.AlgoPosRepository
	QuoteGroupRepository() repository.QuoteGroupRepository
	CustomerRepository() repository.CustomerRepository
	ConfigurableRule() repository.ConfigurableRuleRepository
}

type repositoryRegistry struct {
	cfg config.Config
}

// NewRepoRegistry ...
func NewRepoRegistry(cfg config.Config) RepositoryRegistry {
	var r repositoryRegistry
	var once sync.Once

	once.Do(func() {
		r = repositoryRegistry{cfg: cfg}
	})

	return r
}

func (r repositoryRegistry) Feedback() repository.FeedbackRepository {
	var once sync.Once
	var feedbackRepository repository.FeedbackRepository

	once.Do(func() {
		feedbackRepository = repository.NewFeedbackRepository(&r.cfg)
	})

	return feedbackRepository
}

func (r repositoryRegistry) Report() repository.ReportRepository {
	var once sync.Once
	var reportRepository repository.ReportRepository

	once.Do(func() {
		reportRepository = repository.NewReportRepository(&r.cfg)
	})

	return reportRepository
}

func (r repositoryRegistry) SttManual() repository.SttManualRepository {
	var once sync.Once
	var sttManualRepo repository.SttManualRepository

	once.Do(func() {
		sttManualRepo = repository.NewSttManualRepository(&r.cfg, r.PartnerLog())
	})

	return sttManualRepo
}

func (r repositoryRegistry) Country() repository.CountryRepository {
	var once sync.Once
	var countryRepo repository.CountryRepository

	once.Do(func() {
		countryRepo = repository.NewCountryRepository(&r.cfg)
	})

	return countryRepo
}

func (r repositoryRegistry) Client() repository.ClientRepository {
	var once sync.Once
	var client repository.ClientRepository

	once.Do(func() {
		client = repository.NewClientRepository(&r.cfg)
	})

	return client
}

func (r repositoryRegistry) Partner() repository.PartnerRepository {
	var once sync.Once
	var partner repository.PartnerRepository

	once.Do(func() {
		partner = repository.NewPartnerRepository(&r.cfg)
	})

	return partner
}

func (r repositoryRegistry) Stt() repository.SttRepository {
	var once sync.Once
	var stt repository.SttRepository

	once.Do(func() {
		stt = repository.NewSttRepository(
			&r.cfg,
			r.Elexys(),
			r.PartnerLog(),
		)
	})

	return stt
}

func (r repositoryRegistry) Commodity() repository.CommodityRepository {
	var once sync.Once
	var commodity repository.CommodityRepository

	once.Do(func() {
		commodity = repository.NewCommodityRepository(&r.cfg)
	})

	return commodity
}

func (r repositoryRegistry) Account() repository.AccountRepository {
	var once sync.Once
	var account repository.AccountRepository

	once.Do(func() {
		account = repository.NewAccountRepository(&r.cfg, r.CacheRepository())
	})

	return account
}

func (r repositoryRegistry) ConfigDfodPasti() repository.ConfigDfodPastiRepository {
	var once sync.Once
	var configDfodPasti repository.ConfigDfodPastiRepository

	once.Do(func() {
		configDfodPasti = repository.NewConfigDfodPastiRepository(&r.cfg)
	})

	return configDfodPasti
}

func (r repositoryRegistry) SttOptionalRate() repository.SttOptionalRateRepository {
	var once sync.Once
	var sttOptionalRate repository.SttOptionalRateRepository

	once.Do(func() {
		sttOptionalRate = repository.NewSttOptionalRateRepository(&r.cfg)
	})

	return sttOptionalRate
}

func (r repositoryRegistry) District() repository.DistrictRepository {
	var once sync.Once
	var district repository.DistrictRepository

	once.Do(func() {
		district = repository.NewDistrictRepository(&r.cfg)
	})

	return district
}

func (r repositoryRegistry) CheckTariff() repository.CheckTariffRepository {
	var once sync.Once
	var checkTariff repository.CheckTariffRepository

	once.Do(func() {
		checkTariff = repository.NewCheckTariffRepository(&r.cfg)
	})

	return checkTariff
}

func (r repositoryRegistry) Product() repository.ProductTypeRepository {
	var once sync.Once
	var product repository.ProductTypeRepository

	once.Do(func() {
		product = repository.NewProductTypeRepository(&r.cfg)
	})

	return product
}

func (r repositoryRegistry) City() repository.CityRepository {
	var once sync.Once
	var city repository.CityRepository

	once.Do(func() {
		city = repository.NewCityRepository(&r.cfg, r.CacheRepository())
	})

	return city
}

func (r repositoryRegistry) SttPiece() repository.SttPiecesRepository {
	var once sync.Once
	var sttPiece repository.SttPiecesRepository

	once.Do(func() {
		sttPiece = repository.NewSttPiecesRepository(&r.cfg)
	})

	return sttPiece
}

func (r repositoryRegistry) EstimateSla() repository.EstimateSlaRepository {
	var once sync.Once
	var EstimateSla repository.EstimateSlaRepository

	once.Do(func() {
		EstimateSla = repository.NewEstimateSlaRepository(&r.cfg)
	})

	return EstimateSla
}

func (r repositoryRegistry) ShipmentPacket() repository.ShipmentPackageRepository {
	var once sync.Once
	var shipmentPacket repository.ShipmentPackageRepository

	once.Do(func() {
		shipmentPacket = repository.NewShipmentPackageRepository(&r.cfg)
	})

	return shipmentPacket
}

func (r repositoryRegistry) Shipment() repository.ShipmentRepository {
	var once sync.Once
	var shipment repository.ShipmentRepository

	once.Do(func() {
		shipment = repository.NewShipmentRepository(&r.cfg)
	})

	return shipment
}

func (r repositoryRegistry) PickupManifest() repository.PickupManifestRepository {
	var once sync.Once
	var pickupManifest repository.PickupManifestRepository

	once.Do(func() {
		pickupManifest = repository.NewPickupManifestRepository(&r.cfg)
	})

	return pickupManifest
}

func (r repositoryRegistry) Sti() repository.StiRepository {
	var once sync.Once
	var sti repository.StiRepository

	once.Do(func() {
		sti = repository.NewStiRepository(&r.cfg)
	})

	return sti
}

func (r repositoryRegistry) SttPieceHistory() repository.SttPieceHistoryRepository {
	var once sync.Once
	var sttPieceHistory repository.SttPieceHistoryRepository

	once.Do(func() {
		sttPieceHistory = repository.NewSttPiecesHistoryRepository(&r.cfg)
	})

	return sttPieceHistory
}

func (r repositoryRegistry) StiDetail() repository.StiDetailRepository {
	var once sync.Once
	var stiDetail repository.StiDetailRepository

	once.Do(func() {
		stiDetail = repository.NewStiDetailRepository(&r.cfg)
	})

	return stiDetail

}

func (r repositoryRegistry) Driver() repository.DriverRepository {
	var once sync.Once
	var driver repository.DriverRepository

	once.Do(func() {
		driver = repository.NewDriverRepository(&r.cfg)
	})

	return driver
}

func (r repositoryRegistry) Vehicle() repository.VehicleRepository {
	var once sync.Once
	var vehicle repository.VehicleRepository

	once.Do(func() {
		vehicle = repository.NewVehicleRepository(&r.cfg)
	})

	return vehicle
}

func (r repositoryRegistry) Bag() repository.BagRepository {
	var once sync.Once
	var bag repository.BagRepository

	once.Do(func() {
		bag = repository.NewBagRepository(&r.cfg)
	})

	return bag
}

func (r repositoryRegistry) BagVendor() repository.BagOrchestraRepository {
	var once sync.Once
	var bagVendor repository.BagOrchestraRepository

	once.Do(func() {
		bagVendor = repository.NewBagOrchestraRepository(&r.cfg, r.SttPieceHistory())
	})

	return bagVendor
}

func (r repositoryRegistry) BagDetail() repository.BagDetailRepository {
	var once sync.Once
	var bagDetail repository.BagDetailRepository

	once.Do(func() {
		bagDetail = repository.NewBagDetailRepository(&r.cfg, r.SttPieceHistory())
	})

	return bagDetail
}

func (r repositoryRegistry) StiDest() repository.StiDestRepository {
	var once sync.Once
	var stiDest repository.StiDestRepository

	once.Do(func() {
		stiDest = repository.NewStiDestRepository(&r.cfg)
	})

	return stiDest
}

func (r repositoryRegistry) Handover() repository.HandoverRepository {
	var once sync.Once
	var handover repository.HandoverRepository

	once.Do(func() {
		handover = repository.NewHandoverRepository(&r.cfg)
	})

	return handover
}

func (r repositoryRegistry) Cargo() repository.CargoRepository {
	var once sync.Once
	var cargo repository.CargoRepository

	once.Do(func() {
		cargo = repository.NewCargoRepository(
			&r.cfg,
			r.SttPieceHistory(),
			r.BagCargo(),
			r.CargoReserve(),
		)
	})

	return cargo
}

func (r repositoryRegistry) StiDestDetail() repository.StiDestDetailRepository {
	var once sync.Once
	var stiDestDetail repository.StiDestDetailRepository

	once.Do(func() {
		stiDestDetail = repository.NewStiDestDetailRepository(&r.cfg, r.SttPieceHistory())
	})

	return stiDestDetail
}

func (r repositoryRegistry) CargoDetail() repository.CargoDetailRepository {
	var once sync.Once
	var cargoDetail repository.CargoDetailRepository

	once.Do(func() {
		cargoDetail = repository.NewCargoDetailRepository(&r.cfg)
	})

	return cargoDetail
}

func (r repositoryRegistry) HandoverDetail() repository.HandoverDetailRepository {
	var once sync.Once
	var handoverDetail repository.HandoverDetailRepository

	once.Do(func() {
		handoverDetail = repository.NewHandoverDetailRepository(&r.cfg, r.SttPieceHistory())
	})

	return handoverDetail
}

func (r repositoryRegistry) RouteRepository() repository.RouteRepository {
	var once sync.Once
	var routeRepository repository.RouteRepository

	once.Do(func() {
		routeRepository = repository.NewRouteRepository(&r.cfg)
	})

	return routeRepository
}

func (r repositoryRegistry) CacheRepository() repository.CacheRepository {
	var once sync.Once
	var cacheRepository repository.CacheRepository

	once.Do(func() {
		cacheRepository = repository.NewCacheRepository(&r.cfg)
	})

	return cacheRepository
}

func (r repositoryRegistry) DeliveryManifest() repository.DeliveryManifestRepository {
	var once sync.Once
	var deliveryManifest repository.DeliveryManifestRepository

	once.Do(func() {
		deliveryManifest = repository.NewDeliveryManifestRepository(&r.cfg)
	})

	return deliveryManifest
}

func (r repositoryRegistry) Reason() repository.ReasonRepository {
	var once sync.Once
	var reasonRepository repository.ReasonRepository

	once.Do(func() {
		reasonRepository = repository.NewReasonRepository(&r.cfg)
	})

	return reasonRepository
}

func (r repositoryRegistry) DeliveryManifestDetail() repository.DeliveryManifestDetailRepository {
	var once sync.Once
	var deliveryManifestDetail repository.DeliveryManifestDetailRepository

	once.Do(func() {
		deliveryManifestDetail = repository.NewDeliveryManifestDetailRepository(&r.cfg, r.SttPieceHistory())
	})

	return deliveryManifestDetail
}

func (r repositoryRegistry) Delivery() repository.DeliveryRepository {
	var once sync.Once
	var delivery repository.DeliveryRepository

	once.Do(func() {
		delivery = repository.NewDeliveryRepository(
			&r.cfg,
			r.SttPieceHistory(),
			r.PartnerLog(),
		)
	})

	return delivery
}

func (r repositoryRegistry) ReasonMapping() repository.ReasonMappingRepository {
	var once sync.Once
	var reasonMappingRepository repository.ReasonMappingRepository

	once.Do(func() {
		reasonMappingRepository = repository.NewReasonMappingRepository(&r.cfg)
	})

	return reasonMappingRepository
}

func (r repositoryRegistry) Pod() repository.PodRepository {
	var once sync.Once
	var podRepository repository.PodRepository

	once.Do(func() {
		podRepository = repository.NewPodRepository(&r.cfg, r.SttPieceHistory(), r.PartnerLog())
	})

	return podRepository
}

func (r repositoryRegistry) CustomProcess() repository.CustomProcessRepository {
	var once sync.Once
	var customProcessRepository repository.CustomProcessRepository

	once.Do(func() {
		customProcessRepository = repository.NewCustomProcessRepository(&r.cfg, r.SttPieceHistory())
	})

	return customProcessRepository
}

func (r repositoryRegistry) ConfigurablePrice() repository.ConfigurablePriceRepository {
	var once sync.Once
	var configurablePriceRepository repository.ConfigurablePriceRepository

	once.Do(func() {
		configurablePriceRepository = repository.NewConfigurablePriceRepository(&r.cfg)
	})

	return configurablePriceRepository
}

func (r repositoryRegistry) PartnerLog() repository.PartnerLogRepository {
	var once sync.Once
	var partnerLogRepository repository.PartnerLogRepository

	once.Do(func() {
		partnerLogRepository = repository.NewPartnerLogRepository(&r.cfg)
	})

	return partnerLogRepository
}

func (r repositoryRegistry) BalanceLimit() repository.BalanceLimitRepository {
	var once sync.Once
	var balanceLimitRepository repository.BalanceLimitRepository

	once.Do(func() {
		balanceLimitRepository = repository.NewBalanceLimitRepository(&r.cfg)
	})

	return balanceLimitRepository
}

func (r repositoryRegistry) Wallet() repository.WalletRepository {
	var once sync.Once
	var walletRepository repository.WalletRepository

	once.Do(func() {
		walletRepository = repository.NewWalletRepository(&r.cfg)
	})

	return walletRepository
}

func (r repositoryRegistry) CargoProductType() repository.CargoProductTypeRepository {
	var once sync.Once
	var cargoProductTypeRepository repository.CargoProductTypeRepository

	once.Do(func() {
		cargoProductTypeRepository = repository.NewCargoProductTypeRepository(&r.cfg)
	})

	return cargoProductTypeRepository
}

func (r repositoryRegistry) CommodityGroup() repository.CommodityGroupRepository {
	var once sync.Once
	var commodityGroupRepository repository.CommodityGroupRepository

	once.Do(func() {
		commodityGroupRepository = repository.NewCommodityGroupRepository(&r.cfg)
	})

	return commodityGroupRepository
}

func (r repositoryRegistry) Ngen() repository.NgenRepository {
	var once sync.Once
	var ngenRepository repository.NgenRepository

	once.Do(func() {
		ngenRepository = repository.NewNgenRepository(
			&r.cfg,
			repository.NewPartnerLogRepository(&r.cfg),
		)
	})

	return ngenRepository
}

func (r repositoryRegistry) Airport() repository.AirportRepository {
	var once sync.Once
	var airportRepository repository.AirportRepository

	once.Do(func() {
		airportRepository = repository.NewAirportRepository(&r.cfg)
	})

	return airportRepository
}

func (r repositoryRegistry) DeliveryVendor() repository.DeliveryVendorRepository {
	var once sync.Once
	var deliveryVendorRepository repository.DeliveryVendorRepository

	once.Do(func() {
		deliveryVendorRepository = repository.NewDeliveryVendorRepository(&r.cfg)
	})

	return deliveryVendorRepository
}

func (r repositoryRegistry) Ninja() repository.NinjaRepository {
	var once sync.Once
	var ninjaRepository repository.NinjaRepository

	once.Do(func() {
		ninjaRepository = repository.NewNinjaRepository(&r.cfg)
	})

	return ninjaRepository
}

func (r repositoryRegistry) MessageGateway() repository.MessageGatewayRepository {
	var once sync.Once
	var messageGatewayRepository repository.MessageGatewayRepository

	once.Do(func() {
		messageGatewayRepository = repository.NewMessageGatewayRepository(&r.cfg)
	})

	return messageGatewayRepository
}

func (r repositoryRegistry) SttVendor() repository.SttVendorRepository {
	var once sync.Once
	var sttVendorRepository repository.SttVendorRepository

	once.Do(func() {
		sttVendorRepository = repository.NewSttVendorRepository(&r.cfg)
	})

	return sttVendorRepository
}

func (r repositoryRegistry) Elexys() repository.ElexysRepository {
	var once sync.Once
	var elexysRepository repository.ElexysRepository

	once.Do(func() {
		elexysRepository = repository.NewElexysRepository(&r.cfg)
	})

	return elexysRepository
}

func (r repositoryRegistry) SttElexys() repository.SttElexysRepository {
	var once sync.Once
	var sttElexysRepository repository.SttElexysRepository

	once.Do(func() {
		sttElexysRepository = repository.NewSttElexysRepository(&r.cfg, r.CacheRepository())
	})

	return sttElexysRepository
}

func (r repositoryRegistry) MiddlewareClient() repository.MiddlewareCLient {
	var once sync.Once
	var middlewareRepository repository.MiddlewareCLient

	once.Do(func() {
		middlewareRepository = repository.NewMiddlewareCLient(r.PartnerLog(), r.Stt(), &r.cfg, r.Delivery(), r.Shipment(), r.SttPiece(), r.SttOptionalRate())
	})

	return middlewareRepository
}

func (r repositoryRegistry) Health() repository.HealthRepository {
	var once sync.Once
	var healthRepository repository.HealthRepository

	once.Do(func() {
		healthRepository = repository.NewHealthRepository(&r.cfg)
	})

	return healthRepository
}

func (r repositoryRegistry) Transaction() repository.TransactionRepository {
	var once sync.Once
	var transactionRepository repository.TransactionRepository

	once.Do(func() {
		transactionRepository = repository.NewTransactionRepository(&r.cfg)
	})

	return transactionRepository
}

func (r repositoryRegistry) LogSttFailedElexys() repository.LogFailedSttElexysRepository {
	var once sync.Once
	var logFailedSttElexysnRepository repository.LogFailedSttElexysRepository

	once.Do(func() {
		logFailedSttElexysnRepository = repository.NewLogFailedSttElexysRepository(&r.cfg)
	})

	return logFailedSttElexysnRepository
}

func (r repositoryRegistry) LogElexysPickupManifest() repository.LogElexysPickupManifestRepository {
	var once sync.Once
	var logElexysPickupManifestRepository repository.LogElexysPickupManifestRepository

	once.Do(func() {
		logElexysPickupManifestRepository = repository.NewLogElexysPickupManifestRepository(&r.cfg, r.CacheRepository())
	})

	return logElexysPickupManifestRepository
}

func (r repositoryRegistry) Util() repository.UtilRepository {
	var once sync.Once
	var utilRepository repository.UtilRepository

	once.Do(func() {
		utilRepository = repository.NewUtilRepository(&r.cfg)
	})

	return utilRepository
}

func (r repositoryRegistry) BulkDownload() repository.BulkDownloadRepository {
	var once sync.Once
	var bulkDownloadRepository repository.BulkDownloadRepository

	once.Do(func() {
		bulkDownloadRepository = repository.NewBulkDownloadRepository(&r.cfg)
	})

	return bulkDownloadRepository
}

func (r repositoryRegistry) LogFailedDTPOL() repository.LogFailedDTPOLRepository {
	var once sync.Once
	var logFailedDTPOLRepository repository.LogFailedDTPOLRepository

	once.Do(func() {
		logFailedDTPOLRepository = repository.NewLogFailedDTPOLRepository(&r.cfg)
	})

	return logFailedDTPOLRepository
}

func (r repositoryRegistry) SttTransaction() repository.SttTransactionRepository {
	var once sync.Once
	var sttTransactionRepository repository.SttTransactionRepository

	once.Do(func() {
		sttTransactionRepository = repository.NewSttTransactionRepository(&r.cfg)
	})

	return sttTransactionRepository
}

func (r repositoryRegistry) CargoFlight() repository.CargoFlightRepository {
	var once sync.Once
	var cargoFlightRepository repository.CargoFlightRepository

	once.Do(func() {
		cargoFlightRepository = repository.NewCargoFlightRepository(&r.cfg)
	})

	return cargoFlightRepository
}

func (r repositoryRegistry) PartnerLocation() repository.PartnerLocationRepository {
	var once sync.Once
	var partnerLocationRepository repository.PartnerLocationRepository

	once.Do(func() {
		partnerLocationRepository = repository.NewPartnerLocationRepository(&r.cfg)
	})

	return partnerLocationRepository

}

func (r repositoryRegistry) SttActivity() repository.SttActivityRepository {
	var once sync.Once
	var sttActivityRepository repository.SttActivityRepository

	once.Do(func() {
		sttActivityRepository = repository.NewSttActivityRepository(&r.cfg)
	})

	return sttActivityRepository
}

func (r repositoryRegistry) SttReport() repository.STTReportRepository {
	var once sync.Once
	var sttReportRepository repository.STTReportRepository

	once.Do(func() {
		sttReportRepository = repository.NewSttReportRepository(&r.cfg)
	})

	return sttReportRepository
}

func (r repositoryRegistry) KejarcuanReport() repository.KejarcuanReportRepository {
	var once sync.Once
	var kejarcuanReportRepository repository.KejarcuanReportRepository

	once.Do(func() {
		kejarcuanReportRepository = repository.NewKejarcuanReportRepository(&r.cfg)
	})

	return kejarcuanReportRepository
}

func (r repositoryRegistry) OutgoingReport() repository.OutgoingReportRepository {
	var once sync.Once
	var outgoingReportRepository repository.OutgoingReportRepository

	once.Do(func() {
		outgoingReportRepository = repository.NewOutgoingReportRepository(&r.cfg)
	})

	return outgoingReportRepository
}

func (r repositoryRegistry) JNE() repository.JNERepository {
	var once sync.Once
	var jneRepository repository.JNERepository

	once.Do(func() {
		jneRepository = repository.NewJNERepository(&r.cfg, r.PartnerLog())
	})

	return jneRepository
}

func (r repositoryRegistry) AlgoRepo() repository.AlgoRepository {
	var once sync.Once
	var algoRepository repository.AlgoRepository

	once.Do(func() {
		algoRepository = repository.NewAlgoRepository(&r.cfg)
	})

	return algoRepository
}

func (r repositoryRegistry) Storage() repository.StorageRepository {
	var once sync.Once
	var storageRepository repository.StorageRepository

	once.Do(func() {
		storageRepository = repository.NewStorageRepository(&r.cfg)
	})

	return storageRepository
}

func (r repositoryRegistry) Xlsx() repository.XlsxRepository {
	var once sync.Once
	var xlsxRepository repository.XlsxRepository

	once.Do(func() {
		xlsxRepository = repository.NewXlsxRepository()
	})

	return xlsxRepository
}

func (r repositoryRegistry) Time() repository.TimeRepository {
	var once sync.Once
	var timeRepository repository.TimeRepository

	once.Do(func() {
		timeRepository = repository.NewTimeRepository()
	})

	return timeRepository
}

func (r repositoryRegistry) MissrouteReport() repository.MissrouteReportRepository {
	var once sync.Once
	var missrouteReportRepository repository.MissrouteReportRepository

	once.Do(func() {
		missrouteReportRepository = repository.NewMissrouteReportRepository(&r.cfg)
	})

	return missrouteReportRepository
}

func (r repositoryRegistry) InstantBooking() repository.InstantBookingRepository {
	var once sync.Once
	var instantBooking repository.InstantBookingRepository

	once.Do(func() {
		instantBooking = repository.NewInstantBookingRepository(&r.cfg)
	})

	return instantBooking
}

func (r repositoryRegistry) InstantBookingDetail() repository.InstantBookingDetailRepository {
	var once sync.Once
	var re repository.InstantBookingDetailRepository

	once.Do(func() {
		re = repository.NewInstantBookingDetailRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) DtpolReports() repository.DtpolReportsRepository {
	var once sync.Once
	var re repository.DtpolReportsRepository

	once.Do(func() {
		re = repository.NewDtpolReportsRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) Checkout() repository.CheckoutRepository {
	var once sync.Once
	var re repository.CheckoutRepository

	once.Do(func() {
		re = repository.NewCheckoutRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) Dashboard() repository.DashboardRepository {
	var once sync.Once
	var dashboardRepository repository.DashboardRepository

	once.Do(func() {
		dashboardRepository = repository.NewDashboardRepository(&r.cfg)
	})

	return dashboardRepository
}

func (r repositoryRegistry) LogMessage() repository.LogMessageRepository {
	var once sync.Once
	var logMessageRepository repository.LogMessageRepository

	once.Do(func() {
		logMessageRepository = repository.NewLogMessageRepository(&r.cfg)
	})

	return logMessageRepository
}

func (r repositoryRegistry) ProgressiveCommissionReport() repository.ProgressiveCommissionReportRepository {
	var once sync.Once
	var progressiveCommissionReportRepository repository.ProgressiveCommissionReportRepository

	once.Do(func() {
		progressiveCommissionReportRepository = repository.NewProgressiveCommissionReportRepository(
			&r.cfg,
			r.PartnerLog(),
		)
	})

	return progressiveCommissionReportRepository
}

func (r repositoryRegistry) ReadSttPaid() repository.ReadSttPaidRepository {
	var once sync.Once
	var readSttPaid repository.ReadSttPaidRepository

	once.Do(func() {
		readSttPaid = repository.NewReadSttPaidRepository(&r.cfg)
	})

	return readSttPaid
}

func (r repositoryRegistry) PredefinedHoliday() repository.PredefinedHoliday {
	var once sync.Once
	var predefinedHolidayRepo repository.PredefinedHoliday

	once.Do(func() {
		predefinedHolidayRepo = repository.NewPredefinedHoliday(&r.cfg)
	})

	return predefinedHolidayRepo
}

func (r repositoryRegistry) RebuttalDex() repository.RebuttalDexRepository {
	var once sync.Once
	var rebuttalDexRepo repository.RebuttalDexRepository

	once.Do(func() {
		rebuttalDexRepo = repository.NewRebuttalDexRepository(&r.cfg)
	})

	return rebuttalDexRepo
}

func (r repositoryRegistry) CustomProcessRole() repository.CustomProcessRoleRepository {
	var once sync.Once
	var customProcessRoleRepository repository.CustomProcessRoleRepository

	once.Do(func() {
		customProcessRoleRepository = repository.NewCustomProcessRoleRepository(&r.cfg, r.SttPieceHistory())
	})

	return customProcessRoleRepository
}

func (r repositoryRegistry) SttCODDashboardRepo() repository.SttCODDashboardRepository {
	var once sync.Once
	var sttCODDashboardRepo repository.SttCODDashboardRepository

	once.Do(func() {
		sttCODDashboardRepo = repository.NewSttCODDashboardRepository(&r.cfg, r.PartnerLog())
	})

	return sttCODDashboardRepo
}

func (r repositoryRegistry) RetryPubsubRepo() repository.RetryPubsubRepository {
	var once sync.Once
	var retryPubsubRepo repository.RetryPubsubRepository

	once.Do(func() {
		retryPubsubRepo = repository.NewRetryPubsubRepository(&r.cfg, r.PartnerLog())
	})

	return retryPubsubRepo
}

func (r repositoryRegistry) Slack() repository.SlackRepository {
	var once sync.Once
	var slackRepository repository.SlackRepository

	once.Do(func() {
		slackRepository = repository.NewSlackRepository(&r.cfg)
	})

	return slackRepository
}

func (r repositoryRegistry) Luwjistik() repository.LuwjistikRepository {
	var once sync.Once
	var luwjistikRepository repository.LuwjistikRepository

	once.Do(func() {
		luwjistikRepository = repository.NewLuwjistikRepository(&r.cfg)
	})

	return luwjistikRepository
}

func (r repositoryRegistry) RetryLuwjistik() repository.RetryLuwjistikRepository {
	var once sync.Once
	var retryLuwjistikRepository repository.RetryLuwjistikRepository

	once.Do(func() {
		retryLuwjistikRepository = repository.NewRetryLuwjistikRepository(&r.cfg)
	})

	return retryLuwjistikRepository
}

func (r repositoryRegistry) ProgressiveCommissionConfig() repository.ProgressiveCommissionConfigRepository {
	var once sync.Once
	var progressiveCommissionConfigRepository repository.ProgressiveCommissionConfigRepository

	once.Do(func() {
		progressiveCommissionConfigRepository = repository.NewProgressiveCommissionConfigRepository(&r.cfg)
	})

	return progressiveCommissionConfigRepository
}

func (r repositoryRegistry) SttPaymentHistoryRepository() repository.SttPaymentHistoryRepository {
	var once sync.Once
	var sttPaymentHistoryRepository repository.SttPaymentHistoryRepository

	once.Do(func() {
		sttPaymentHistoryRepository = repository.NewSttPaymentHistoryRepository(&r.cfg)
	})

	return sttPaymentHistoryRepository
}

func (r repositoryRegistry) ExchangeRate() repository.ExchangeRateRepository {
	var once sync.Once
	var exchangeRateRepository repository.ExchangeRateRepository

	once.Do(func() {
		exchangeRateRepository = repository.NewExchangeRateRepository(&r.cfg)
	})

	return exchangeRateRepository
}

func (r repositoryRegistry) BaggingGroupLocation() repository.BaggingGroupLocationRepository {
	var once sync.Once
	var baggingGroupLocationRepository repository.BaggingGroupLocationRepository

	once.Do(func() {
		baggingGroupLocationRepository = repository.NewBaggingGroupLocationRepository(&r.cfg, r.CacheRepository())
	})

	return baggingGroupLocationRepository
}

func (r repositoryRegistry) CorporateDashboardRepo() repository.CorporateDashboardRepository {
	var once sync.Once
	var corporateDashboardRepo repository.CorporateDashboardRepository

	once.Do(func() {
		corporateDashboardRepo = repository.NewCorporateDashboardRepository(&r.cfg)
	})

	return corporateDashboardRepo
}

func (r repositoryRegistry) ClaimHistoryRepo() repository.ClaimHistoryRepository {
	var once sync.Once
	var claimHistoryRepo repository.ClaimHistoryRepository

	once.Do(func() {
		claimHistoryRepo = repository.NewClaimHistoryRepository(&r.cfg)
	})

	return claimHistoryRepo
}

func (r repositoryRegistry) SttCorporateRepo() repository.SttCorporateRepository {
	var once sync.Once
	var sttCorporateRepo repository.SttCorporateRepository

	once.Do(func() {
		sttCorporateRepo = repository.NewSttCorporateRepository(&r.cfg)
	})

	return sttCorporateRepo
}

func (r repositoryRegistry) PosAffiliate() repository.PosAffiliateRepository {
	var once sync.Once
	var posAffiliateRepo repository.PosAffiliateRepository

	once.Do(func() {
		posAffiliateRepo = repository.NewPosAffiliateRepository(
			&r.cfg,
		)
	})

	return posAffiliateRepo
}

func (r repositoryRegistry) HistoryManifest() repository.HistoryManifestRepository {
	var once sync.Once
	var re repository.HistoryManifestRepository

	once.Do(func() {
		re = repository.NewHistoryManifestRepository(
			&r.cfg,
		)
	})

	return re
}

func (r repositoryRegistry) SttReverseJourney() repository.SttReverseJourneyRepository {
	var once sync.Once
	var sttReverseJourneyRepository repository.SttReverseJourneyRepository

	once.Do(func() {
		sttReverseJourneyRepository = repository.NewSttReverseJourneyRepository(&r.cfg)
	})

	return sttReverseJourneyRepository
}

func (r repositoryRegistry) RetryCargo() repository.RetryCargoRepository {
	var once sync.Once
	var RetryCargoRepository repository.RetryCargoRepository

	once.Do(func() {
		RetryCargoRepository = repository.NewRetryCargoRepository(&r.cfg, r.CargoReserve(), r.Ngen(), r.PartnerLog())
	})

	return RetryCargoRepository
}

func (r repositoryRegistry) RetryCargoDetail() repository.RetryCargoDetailRepository {
	var once sync.Once
	var RetryCargoDetailRepository repository.RetryCargoDetailRepository

	once.Do(func() {
		RetryCargoDetailRepository = repository.NewRetryCargoDetailRepository(&r.cfg)
	})

	return RetryCargoDetailRepository
}

func (r repositoryRegistry) ReadCargo() repository.ReadCargoFlightRepository {
	var once sync.Once
	var ReadCargoFlightRepository repository.ReadCargoFlightRepository

	once.Do(func() {
		ReadCargoFlightRepository = repository.NewReadCargoRepository(&r.cfg)
	})

	return ReadCargoFlightRepository
}

func (r repositoryRegistry) ReadyToCargo() repository.ReadyToCargoRepository {
	var once sync.Once
	var ReadyToCargoRepository repository.ReadyToCargoRepository

	once.Do(func() {
		ReadyToCargoRepository = repository.NewReadyToCargoRepository(&r.cfg, r.PartnerLog(), r.ReadyToCargoHistory())
	})

	return ReadyToCargoRepository
}

func (r repositoryRegistry) BaggingReadyToCargo() repository.BaggingReadyToCargoRepository {
	var once sync.Once
	var BaggingReadyToCargoRepository repository.BaggingReadyToCargoRepository

	once.Do(func() {
		BaggingReadyToCargoRepository = repository.NewBaggingReadyToCargoRepository(&r.cfg)
	})

	return BaggingReadyToCargoRepository
}

func (r repositoryRegistry) CargoConfiguration() repository.CargoConfigurationRepository {
	var once sync.Once
	var CargoConfigurationRepository repository.CargoConfigurationRepository

	once.Do(func() {
		CargoConfigurationRepository = repository.NewCargoConfigurationRepository(&r.cfg)
	})

	return CargoConfigurationRepository
}

func (r repositoryRegistry) BaggingReadyToCargoHistory() repository.BaggingReadyToCargoHistoryRepository {
	var once sync.Once
	var BaggingReadyToCargoHistoryRepository repository.BaggingReadyToCargoHistoryRepository

	once.Do(func() {
		BaggingReadyToCargoHistoryRepository = repository.NewBaggingReadyToCargoHistoryRepository(&r.cfg)
	})

	return BaggingReadyToCargoHistoryRepository
}

func (r repositoryRegistry) BagVendorRepo() repository.BagVendorRepository {
	var once sync.Once
	var BagVendorRepository repository.BagVendorRepository

	once.Do(func() {
		BagVendorRepository = repository.NewBagVendorRepository(&r.cfg)
	})

	return BagVendorRepository
}

func (r repositoryRegistry) CrossDockingFailedRepo() repository.CrossDockingFailedRepository {
	var once sync.Once
	var CrossDockingFailedRepository repository.CrossDockingFailedRepository

	once.Do(func() {
		CrossDockingFailedRepository = repository.NewCrossDockingFailedRepository(&r.cfg)
	})

	return CrossDockingFailedRepository
}

func (r repositoryRegistry) HydraConfig() repository.HydraConfigRepository {
	var once sync.Once
	var HydraConfigRepository repository.HydraConfigRepository

	once.Do(func() {
		HydraConfigRepository = repository.NewHydraConfigRepository(&r.cfg)
	})

	return HydraConfigRepository
}

func (r repositoryRegistry) BagCargo() repository.BagCargoRepository {
	var once sync.Once
	var BagCargoRepository repository.BagCargoRepository

	once.Do(func() {
		BagCargoRepository = repository.NewBagCargo(&r.cfg)
	})

	return BagCargoRepository
}

func (r repositoryRegistry) BagCommodityGroup() repository.BagCommodityGroupRepository {
	var once sync.Once
	var BagCommodityGroupRepository repository.BagCommodityGroupRepository

	once.Do(func() {
		BagCommodityGroupRepository = repository.NewBagCommodityGroup(&r.cfg)
	})

	return BagCommodityGroupRepository
}

func (r repositoryRegistry) SttReadyToCargoRepo() repository.SttReadyToCargoRepository {
	var once sync.Once
	var sttReadyToCargoRepo repository.SttReadyToCargoRepository

	once.Do(func() {
		sttReadyToCargoRepo = repository.NewSttReadyToCargoRepository(&r.cfg)
	})

	return sttReadyToCargoRepo
}

func (r repositoryRegistry) SttReadyToCargoHistoryRepo() repository.SttReadyToCargoHistoryRepository {
	var once sync.Once
	var sttReadyToCargoHistoryRepo repository.SttReadyToCargoHistoryRepository

	once.Do(func() {
		sttReadyToCargoHistoryRepo = repository.NewSttReadyToCargoHistoryRepository(&r.cfg)
	})

	return sttReadyToCargoHistoryRepo
}

func (r repositoryRegistry) CargoReserve() repository.CargoReserveRepository {
	var once sync.Once
	var cr repository.CargoReserveRepository

	once.Do(func() {
		cr = repository.NewCargoReserveRepository(&r.cfg)
	})

	return cr
}

func (r repositoryRegistry) CutOffReadyToCargoRepo() repository.CutOffReadyToCargoRepository {
	var once sync.Once
	var cutOffReadyToCargoRepo repository.CutOffReadyToCargoRepository

	once.Do(func() {
		cutOffReadyToCargoRepo = repository.NewCutOffReadyToCargoRepository(&r.cfg)
	})

	return cutOffReadyToCargoRepo
}

func (r repositoryRegistry) Hub() repository.HubRepository {
	var once sync.Once
	var cr repository.HubRepository

	once.Do(func() {
		cr = repository.NewHubRepository(&r.cfg)
	})

	return cr
}

func (r repositoryRegistry) StoSc() repository.StoScRepository {
	var once sync.Once
	var cr repository.StoScRepository

	once.Do(func() {
		cr = repository.NewStoScRepository(
			&r.cfg,
			r.Sti(),
			r.SttPieceHistory(),
		)
	})

	return cr
}
func (r repositoryRegistry) ResolutionCentre() repository.ResolutionCentreRepository {
	var once sync.Once
	var rcRepo repository.ResolutionCentreRepository

	once.Do(func() {
		rcRepo = repository.NewResolutionCentreRepository(
			&r.cfg,
		)
	})

	return rcRepo
}

func (r repositoryRegistry) CaseCategory() repository.CaseCategoryRepository {
	var once sync.Once
	var ccRepo repository.CaseCategoryRepository

	once.Do(func() {
		ccRepo = repository.NewCaseCategoryRepository(
			&r.cfg,
			r.CacheRepository(),
		)
	})

	return ccRepo
}

func (r repositoryRegistry) SalesforceRepository() repository.SalesForceRepository {
	var once sync.Once
	var sfRepo repository.SalesForceRepository

	once.Do(func() {
		sfRepo = repository.NewSalesForceRepository(
			&r.cfg,
			r.CacheRepository(),
			r.PartnerLog(),
		)
	})

	return sfRepo
}

func (r repositoryRegistry) DiscussionForumRepository() repository.DiscussionForumRepository {
	var once sync.Once
	var dfRepo repository.DiscussionForumRepository

	once.Do(func() {
		dfRepo = repository.NewDiscussionForumRepository(
			&r.cfg,
		)
	})

	return dfRepo
}

func (r repositoryRegistry) FirebaseRepository() repository.FirebaseRepository {
	var once sync.Once
	var dfRepo repository.FirebaseRepository

	once.Do(func() {
		dfRepo = repository.NewFirebaseRepository(
			&r.cfg,
			firebase.NewFirebaseCloudMessaging(),
		)
	})

	return dfRepo
}

func (r repositoryRegistry) RtcCityGroupRepository() repository.RtcCityGroupRepository {
	var once sync.Once
	var rcg repository.RtcCityGroupRepository

	once.Do(func() {
		rcg = repository.NewRtcCityGroupRepository(
			&r.cfg,
			r.CacheRepository(),
		)
	})

	return rcg
}

func (r repositoryRegistry) CodConfig() repository.CodConfigRepository {
	var once sync.Once
	var ccoRepo repository.CodConfigRepository

	once.Do(func() {
		ccoRepo = repository.NewCodConfigRepository(
			&r.cfg,
			r.CacheRepository(),
		)
	})

	return ccoRepo
}

func (r repositoryRegistry) NotificationPenalty() repository.NotificationPenaltyRepository {
	var once sync.Once
	var ccoRepo repository.NotificationPenaltyRepository

	once.Do(func() {
		ccoRepo = repository.NewNotificationPenaltyRepository(
			&r.cfg,
		)
	})

	return ccoRepo
}

func (r repositoryRegistry) EmbargoConfig() repository.EmbargoConfigRepository {
	var once sync.Once
	var embargoConfig repository.EmbargoConfigRepository

	once.Do(func() {
		embargoConfig = repository.NewEmbargoConfigRepository(
			&r.cfg,
		)
	})

	return embargoConfig
}

func (r repositoryRegistry) CityBalance() repository.CityBalanceRepository {
	var once sync.Once
	var cityBalance repository.CityBalanceRepository

	once.Do(func() {
		cityBalance = repository.NewCityBalanceRepository(
			&r.cfg,
		)
	})

	return cityBalance
}

func (r repositoryRegistry) DeliveryPICRepository() repository.DeliveryPicRepository {
	var once sync.Once
	var dpRepo repository.DeliveryPicRepository

	once.Do(func() {
		dpRepo = repository.NewDeliveryPicRepository(
			&r.cfg,
		)
	})

	return dpRepo
}

func (r repositoryRegistry) HoldBalanceHistory() repository.HoldBalanceHistoryRepository {
	var once sync.Once
	var dpRepo repository.HoldBalanceHistoryRepository

	once.Do(func() {
		dpRepo = repository.NewHoldBalanceHistoryRepository(
			&r.cfg,
		)
	})

	return dpRepo
}

func (r repositoryRegistry) FlagManagementRepository() repository.FlagManagementRepository {
	var once sync.Once
	var re repository.FlagManagementRepository

	once.Do(func() {
		re = repository.NewFlagManagementRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) UploadRepository() repository.UploadRepository {
	var once sync.Once
	var re repository.UploadRepository

	once.Do(func() {
		re = repository.NewUploadRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) LogRtcRepository() repository.LogRtcRepository {
	var once sync.Once
	var re repository.LogRtcRepository

	once.Do(func() {
		re = repository.NewLogRtcRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) DexAssessmentRepo() repository.DexAssessmentRepository {
	var once sync.Once
	var re repository.DexAssessmentRepository

	once.Do(func() {
		re = repository.NewDexAssessmentRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) ShortlinkRepository() repository.ShortlinkRepository {
	var once sync.Once
	var re repository.ShortlinkRepository

	once.Do(func() {
		re = repository.NewShortlinkRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) DexFakeDexConfiguration() repository.DexFakeDexConfigurationRepository {
	var once sync.Once
	var dfdcRepo repository.DexFakeDexConfigurationRepository

	once.Do(func() {
		dfdcRepo = repository.NewDexFakeDexConfigurationRepository(
			&r.cfg,
			r.CacheRepository(),
		)
	})

	return dfdcRepo
}

func (r repositoryRegistry) DexAssessmentDashboardRepo() repository.DexAssessmentDashboardRepository {
	var once sync.Once
	var re repository.DexAssessmentDashboardRepository

	once.Do(func() {
		re = repository.NewDexAssessmentDashboardRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) DexAssessmentDashboardV2Repo() repository.DexAssessmentDashboardV2Repository {
	var once sync.Once
	var re repository.DexAssessmentDashboardV2Repository

	once.Do(func() {
		re = repository.NewDexAssessmentDashboardV2Repository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) CsAccountLogRepo() repository.CsAccountLogRepository {
	var once sync.Once
	var re repository.CsAccountLogRepository

	once.Do(func() {
		re = repository.NewCsAccountLogRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) ClearanceRepo() repository.ClearanceRepository {
	var once sync.Once
	var re repository.ClearanceRepository

	once.Do(func() {
		re = repository.NewClearanceRepository(&r.cfg)
	})

	return re
}

func (r repositoryRegistry) ReleaseRepository() repository.ReleaseRepository {
	var once sync.Once
	var releaseRepo repository.ReleaseRepository

	once.Do(func() {
		releaseRepo = repository.NewReleaseRepository(&r.cfg)
	})

	return releaseRepo
}

func (r repositoryRegistry) ReleaseDetailRepository() repository.ReleaseDetailRepository {
	var once sync.Once
	var releaseDetailRepo repository.ReleaseDetailRepository

	once.Do(func() {
		releaseDetailRepo = repository.NewReleaseDetailRepository(&r.cfg)
	})

	return releaseDetailRepo
}

func (r repositoryRegistry) DexAssessmentDashboardConsolidatorRepository() repo.DexAssessmentDashboardConsolidatorRepository {
	var once sync.Once
	var DexAssessmentDashboardConsolidatorRepo repo.DexAssessmentDashboardConsolidatorRepository

	once.Do(func() {
		DexAssessmentDashboardConsolidatorRepo = repo.NewDexAssessmentDashboardConsolidatorRepository(&r.cfg, r.PartnerLog())
	})

	return DexAssessmentDashboardConsolidatorRepo
}

func (r repositoryRegistry) ReasonVendorMapping() repository.ReasonVendorMappingRepository {
	var once sync.Once
	var reasonVendorMappingRepo repository.ReasonVendorMappingRepository

	once.Do(func() {
		reasonVendorMappingRepo = repository.NewReasonVendorMappingRepository(&r.cfg)
	})

	return reasonVendorMappingRepo
}

func (r repositoryRegistry) PickupManifestCbpRepo() repository.PickupManifestCbpRepository {
	var once sync.Once
	var pickupManifestCbpRepo repository.PickupManifestCbpRepository

	once.Do(func() {
		pickupManifestCbpRepo = repository.NewPickupManifestCbpRepository(&r.cfg)
	})

	return pickupManifestCbpRepo
}

func (r repositoryRegistry) PriorityDeliveryRepository() repository.PriorityDeliveryRepository {
	var once sync.Once
	var priorityDeliveryRepo repository.PriorityDeliveryRepository

	once.Do(func() {
		priorityDeliveryRepo = repository.NewPriorityDeliveryRepository(&r.cfg)
	})

	return priorityDeliveryRepo
}

func (r repositoryRegistry) SttReportPostgresRepository() repository.SttReportPostgresRepository {
	var once sync.Once
	var SttReportPostgresRepo repository.SttReportPostgresRepository

	once.Do(func() {
		SttReportPostgresRepo = repository.NewSttReportPostgresRepository(&r.cfg)
	})

	return SttReportPostgresRepo
}
func (r repositoryRegistry) StiDestTemporaryRepository() repository.StiDestTemporaryRepository {
	var once sync.Once
	var stiDestTemporaryRepo repository.StiDestTemporaryRepository

	once.Do(func() {
		stiDestTemporaryRepo = repository.NewStiDestTemporaryRepository(&r.cfg)
	})

	return stiDestTemporaryRepo
}

func (r repositoryRegistry) RequestPriorityDeliveryRepository() repository.RequestPriorityDeliveryRepository {
	var once sync.Once
	var requestPriorityDeliveryRepo repository.RequestPriorityDeliveryRepository

	once.Do(func() {
		requestPriorityDeliveryRepo = repository.NewRequestPriorityDeliveryRepository(&r.cfg)
	})

	return requestPriorityDeliveryRepo
}

func (r repositoryRegistry) StiTemporaryRepository() repository.StiTemporaryRepository {
	var once sync.Once
	var stiTemporaryRepo repository.StiTemporaryRepository

	once.Do(func() {
		stiTemporaryRepo = repository.NewStiTemporaryRepository(&r.cfg)
	})

	return stiTemporaryRepo
}

func (r repositoryRegistry) PickupCorporateRepository() repository.PickupCorporate {
	var once sync.Once
	var pickupCorporateRepo repository.PickupCorporate

	once.Do(func() {
		pickupCorporateRepo = repository.NewPickupCorporateRepo(&r.cfg, r.Account())
	})

	return pickupCorporateRepo
}

func (r repositoryRegistry) DtpolRepository() repository.DtpolRepository {
	var once sync.Once
	var dtpolRepo repository.DtpolRepository

	once.Do(func() {
		dtpolRepo = repository.NewDtpolRepository(&r.cfg)
	})

	return dtpolRepo
}

func (r repositoryRegistry) SttDueRepository() repository.SttDueRepository {
	var once sync.Once
	var sttDueRepo repository.SttDueRepository

	once.Do(func() {
		sttDueRepo = repository.NewSttDueRepository(
			repository.SetSTTDueRepoCFG(&r.cfg),
			repository.SetSTTDueRepoDB(r.cfg.DB()),
		)
	})

	return sttDueRepo
}

func (r repositoryRegistry) SttDueHistoryRepository() repository.SttDueHistoryRepository {
	var once sync.Once
	var sttDueHistoryRepo repository.SttDueHistoryRepository

	once.Do(func() {
		sttDueHistoryRepo = repository.NewSttDueHistoryRepository(
			repository.SetSTTDueHistoryRepoCFG(&r.cfg),
			repository.SetSTTDueHistoryRepoDB(r.cfg.DB()),
		)
	})

	return sttDueHistoryRepo
}

func (r repositoryRegistry) InternationalDocumentRepository() repository.InternationalDocumentRepository {
	var once sync.Once
	var intDocConfigRepo repository.InternationalDocumentRepository

	once.Do(func() {
		intDocConfigRepo = repository.NewInternationalDocumentRepository(&r.cfg)
	})

	return intDocConfigRepo
}

func (r repositoryRegistry) SttAssessmentRepository() repository.SttAssessmentRepository {
	var once sync.Once
	var sttAssessmentRepo repository.SttAssessmentRepository

	once.Do(func() {
		sttAssessmentRepo = repository.NewSttAssessmentRepository(&r.cfg)
	})

	return sttAssessmentRepo
}

func (r repositoryRegistry) CampaignSttQuoteRepository() repository.CampaignSttQuoteRepository {
	var once sync.Once
	var campaignSttQuote repository.CampaignSttQuoteRepository

	once.Do(func() {
		campaignSttQuote = repository.NewCampaignSttQuoteRepository(&r.cfg)
	})

	return campaignSttQuote
}

func (r repositoryRegistry) PickupCorporateAddress() repository.PickupCorporateAddressRepository {
	var once sync.Once
	var pickupCorporateAddress repository.PickupCorporateAddressRepository

	once.Do(func() {
		pickupCorporateAddress = repository.NewPickupCorporateAddressRepository(&r.cfg, r.CacheRepository())
	})

	return pickupCorporateAddress
}

func (r repositoryRegistry) NotificationRepository() repository.NotificationRepository {
	var once sync.Once
	var notifyRepo repository.NotificationRepository

	once.Do(func() {
		notifyRepo = repository.NewNotificationRepository(&r.cfg)
	})

	return notifyRepo
}

func (r repositoryRegistry) PtPos() repository.PtPosRepository {
	var once sync.Once
	var ptPosRepository repository.PtPosRepository

	once.Do(func() {
		ptPosRepository = repository.NewPtPosRepository(&r.cfg, r.PartnerLog())
	})

	return ptPosRepository
}

func (r repositoryRegistry) StiSCRepository() repository.StiSCRepository {
	var once sync.Once
	var stiSCRepo repository.StiSCRepository

	once.Do(func() {
		stiSCRepo = repository.NewStiSCRepository(
			repository.SetStiSCRepoConfig(&r.cfg),
			repository.SetStiSCRepoPartnerLogRepo(r.PartnerLog()),
		)
	})

	return stiSCRepo
}

func (r repositoryRegistry) PtposRequestRepository() repository.PtposRequestRepository {
	var once sync.Once
	var ptposRequestRepo repository.PtposRequestRepository

	once.Do(func() {
		ptposRequestRepo = repository.NewPtposRequestRepository(&r.cfg)
	})

	return ptposRequestRepo
}

func (r repositoryRegistry) SttCustomFlagRepository() repository.STTCustomFlagRepository {
	var once sync.Once
	var sttCustomFlagRepo repository.STTCustomFlagRepository

	once.Do(func() {
		sttCustomFlagRepo = repository.NewSTTCustomFlagRepository(
			repository.SetConfigSttCustomFlagRepository(&r.cfg),
			repository.SetDBSttCustomFlagRepository(r.cfg.DB()),
			repository.SetPartnerLogRepositorySttCustomFlagRepository(r.PartnerLog()),
		)
	})

	return sttCustomFlagRepo
}

func (r repositoryRegistry) CargoConfigurationSearchFlight() repository.CargoConfigurationSearchFlightRepository {
	var once sync.Once
	var repo repository.CargoConfigurationSearchFlightRepository

	once.Do(func() {
		repo = repository.NewCargoConfigurationSearchFlightRepository(
			repository.SetCargoConfigurationSearchFlightRepoCFG(&r.cfg),
			repository.SetCargoConfigurationSearchFlightRepoPartnerLog(r.PartnerLog()),
			repository.SetCargoConfigurationSearchFlightRepoCache(r.cfg.Cache()),
		)
	})

	return repo
}

func (r repositoryRegistry) SabreRepository() repository.SabreContractRepository {
	var once sync.Once
	var repo repository.SabreContractRepository
	once.Do(func() {
		repo = repository.NewSabreContractRepo(
			repository.SetSabreContractCacheRepo(r.cfg.Cache()),
			repository.SetSabreContractConfigRepo(&r.cfg),
			repository.SetSabreContractPartnerLogRepo(r.PartnerLog()),
			repository.SetSabreContractAirportRepo(r.Airport()),
			repository.SetSabreContractAccountRepo(r.Account()),
			repository.SetSabreContractCacheRepoRepo(r.CacheRepository()),
		)
	})

	return repo
}

func (r repositoryRegistry) ReadyToCargoHistory() repository.ReadyToCargoHistoryRepository {
	var once sync.Once
	var repo repository.ReadyToCargoHistoryRepository
	once.Do(func() {
		repo = repository.NewReadyToCargoHistoryContract(
			repository.SetRTCHistoryContractConfigRepo(&r.cfg),
			repository.SetRTCHistoryContractDBRepo(r.cfg.DB()),
			repository.SetRTCHistoryContractPartnerLogRepo(r.PartnerLog()),
		)
	})

	return repo
}

func (r repositoryRegistry) RiskClassificationRepository() cod_dfod_fraud_detection.RiskClassificationRepository {
	var once sync.Once
	var riskClassificationRepo cod_dfod_fraud_detection.RiskClassificationRepository

	once.Do(func() {
		riskClassificationRepo = cod_dfod_fraud_detection.NewRiskClassificationRepository(&r.cfg)
	})

	return riskClassificationRepo
}

func (r repositoryRegistry) DispatchRepository() repository.DispatchRepository {
	var once sync.Once
	var repo repository.DispatchRepository
	once.Do(func() {
		repo = repository.NewDispatchRepository(
			&r.cfg,
		)
	})

	return repo
}

func (r repositoryRegistry) AlgoPosRepo() repository.AlgoPosRepository {
	var once sync.Once
	var algoPosRepository repository.AlgoPosRepository

	once.Do(func() {
		algoPosRepository = repository.NewAlgoPosRepository(&r.cfg, r.Partner())
	})

	return algoPosRepository
}

func (r repositoryRegistry) QuoteGroupRepository() repository.QuoteGroupRepository {
	var once sync.Once
	var repo repository.QuoteGroupRepository
	once.Do(func() {
		repo = repository.NewQuoteGroupRepository(
			&r.cfg,
		)
	})

	return repo
}

func (r repositoryRegistry) CustomerRepository() repository.CustomerRepository {
	var once sync.Once
	var customerRepository repository.CustomerRepository

	once.Do(func() {
		customerRepository = repository.NewCustomerRepository(&r.cfg)
	})

	return customerRepository
}

func (r repositoryRegistry) ConfigurableRule() repository.ConfigurableRuleRepository {
	var once sync.Once
	var repo repository.ConfigurableRuleRepository
	once.Do(func() {
		repo = repository.NewConfigurableRuleRepository(
			&r.cfg,
			r.CacheRepository(),
		)
	})

	return repo
}
