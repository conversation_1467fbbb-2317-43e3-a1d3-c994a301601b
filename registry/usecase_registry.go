package registry

import (
	"github.com/Lionparcel/hydra/src/usecase"
	"sync"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/shared"
	risk_classification "github.com/Lionparcel/hydra/src/modules/cod_dfod_fraud_detection/risk_classification/usecase"
	"github.com/Lionparcel/hydra/src/repository"
	"github.com/Lionparcel/hydra/src/usecase/message_broker"
)

// UsecaseRegistry ...
type UsecaseRegistry interface {
	SttManual() usecase.SttManual
	Stt() usecase.Stt
	PickupManifest() usecase.PickupManifest
	Sti() usecase.Sti
	StiSc() usecase.StiSc
	Bag() usecase.Bag
	BagVendor() usecase.BagOrchestra
	StiDest() usecase.StiDest
	StiDestSc() usecase.StiDestSc
	Handover() usecase.Handover
	Shortland() usecase.Shortland
	Pod() usecase.Pod
	DeliveryManifest() usecase.DeliveryManifest
	Reason() usecase.Reason
	CustomProcess() usecase.CustomProcess
	GatewayStt() usecase.GatewayStt
	GatewaySttTracking() usecase.GatewaySttTracking
	GatewaySttStatus() usecase.GatewaySttStatus
	Cargo() usecase.Cargo
	DashboardDelivery() usecase.DashboardDelivery
	OnProcess() usecase.OnProcess
	Del() usecase.Del
	Ngen() usecase.Ngen
	Ninja() usecase.Ninja
	MessageGateway() usecase.MessageGateway
	Elexys() usecase.Elexys
	Report() usecase.Report
	Health() usecase.Health
	LogSttFailedElexys() usecase.LogFailedSttElexys
	SttElexys() usecase.SttElexys
	Util() usecase.Util
	PartnerLog() usecase.PartnerLog
	ReportV2() usecase.ReportV2
	ReportV3() usecase.ReportV3
	SttTransaction() usecase.SttTransaction
	CargoV2() usecase.CargoV2
	SttActivity() usecase.SttActivity
	Kejarcuan() usecase.Kejarcuan
	GatewayTrucking() usecase.GatewayTrucking
	JNE() usecase.JNE
	TruckingHandler() message_broker.TruckingHandler
	MessageHandler() message_broker.MessageHandler
	PodHandler() message_broker.PodHandler
	InstantBooking() usecase.InstantBooking
	SttV2() usecase.SttV2
	OnProcessV2() usecase.OnProcessV2
	DashboardV2() usecase.DashboardV2
	SttManualV2() usecase.SttManualV2
	BulkDownload() usecase.BulkDownload
	SttManualV3() usecase.SttManualV3
	PodV2() usecase.PodV2
	Checkout() usecase.Checkout
	IncomingOutgoing() usecase.IncomingOutgoing
	LogMessage() usecase.LogMessageService
	GatewayPayment() usecase.GatewayPayment
	SttPayment() usecase.SttPayment
	ProgressiveCommission() usecase.ProgressiveCommission
	ProgressiveCommissionHandler() message_broker.ProgressiveCommissionHandler
	CustomProcessRole() usecase.CustomProcessRole
	ReadSttPaid() usecase.ReadSttPaid
	RebuttalDex() usecase.RebuttalDex
	Webhook() usecase.Webhook
	ReportV4() usecase.ReportV4
	Luwjistik() usecase.Luwjistik
	CorporateDashboard() usecase.CorporateDashboard
	NinjaV2() usecase.NinjaV2
	ClaimStatus() usecase.ClaimStatus
	HistoryManifest() usecase.HistoryManifest
	RetryCargo() usecase.RetryCargo
	ReadyToCargo() usecase.ReadyToCargo
	SttPromo() usecase.SttPromo
	GatewayCargo() usecase.GatewayCargo
	CargoReserve() usecase.CargoReserve
	StoSc() usecase.StoSc
	ResolutionCentre() usecase.ResolutionCentre
	DiscussionForum() usecase.DiscussionForum
	Salesforce() usecase.Salesforce
	Firebase() usecase.FirebaseUcase
	NotificationPenalty() usecase.NotificationPenalty
	DeliveryPic() usecase.DeliveryPic
	HoldBalanceHistory() usecase.HoldBalanceHistory
	Upload() usecase.Upload
	LogRtc() usecase.LogRtc
	DexAssessment() usecase.DexAssessment
	DexAssessmentDashboard() usecase.DexAssessmentDashboard
	Shortlink() usecase.Shortlink
	Clearance() usecase.Clearance
	Release() usecase.Release
	Algo() usecase.Algo
	PickupManifestCbp() usecase.PickupManifestCbp
	RequestPriorityDelivery() usecase.RequestPriorityDelivery
	PriorityManagementDelivery() usecase.PriorityDeliveryManagement
	PickupCorporate() usecase.PickupCorporate
	SttDueUC() usecase.STTDue
	PtPosUC() usecase.PtPos
	Feedback() usecase.Feedback
	CargoSearchFlight() usecase.CargoSearchFlightUC
	RiskClassification() risk_classification.RiskClassification
	Dispatch() usecase.DispatchUC
	Kulioner() usecase.Kulioner
	QuoteGroup() usecase.QuoteGroupUC
}

type usecaseRegistry struct {
	repo RepositoryRegistry
	cfg  config.Config
}

// NewUsecaseRegistry ...
func NewUsecaseRegistry(cfg config.Config) (r UsecaseRegistry) {
	var ucRegistry usecaseRegistry
	var once sync.Once

	once.Do(func() {
		repoReg := NewRepoRegistry(cfg)
		ucRegistry = usecaseRegistry{repo: repoReg, cfg: cfg}
	})

	return &ucRegistry
}

func (u *usecaseRegistry) Feedback() usecase.Feedback {
	var ucFeedbackDelivery usecase.Feedback
	var once sync.Once
	once.Do(func() {
		ucFeedbackDelivery = usecase.NewFeedbackUc(
			u.repo.Feedback(),
			u.cfg.Cache(),
			u.cfg,
		)
	})
	return ucFeedbackDelivery
}

func (u *usecaseRegistry) DashboardDelivery() usecase.DashboardDelivery {
	var ucDashboardDelivery usecase.DashboardDelivery
	var once sync.Once
	once.Do(func() {
		ucDashboardDelivery = usecase.NewDashboardDeliveryUc(&u.cfg, u.repo.Delivery(), u.repo.Sti(), u.repo.StiDest(), u.repo.Cargo(), u.repo.Partner(), u.repo.SttCODDashboardRepo(), u.repo.Reason(), u.repo.CacheRepository(),
			u.repo.Account(), u.repo.DeliveryPICRepository(), u.repo.BulkDownload(), u.repo.Time(), u.repo.Storage(), u.repo.Xlsx(), u.repo.FlagManagementRepository(), u.repo.AlgoRepo())
	})
	return ucDashboardDelivery
}

func (u *usecaseRegistry) SttManual() usecase.SttManual {
	var ucSttManual usecase.SttManual
	var once sync.Once
	once.Do(func() {
		ucSttManual = usecase.NewSttManualUc(u.repo.SttManual(), u.repo.Client(), u.repo.Partner(), u.cfg, u.repo.District(), u.repo.Product(), u.repo.Time(), u.repo.CacheRepository(), u.repo.PartnerLog(), u.cfg.PUBSUB(), u.repo.HydraConfig(), u.repo.Slack(), u.repo.Account(), u.repo.CampaignSttQuoteRepository())
	})
	return ucSttManual
}

func (u *usecaseRegistry) Stt() usecase.Stt {
	var ucStt usecase.Stt
	var once sync.Once
	once.Do(func() {
		ucStt = usecase.NewSttUc(
			&u.cfg,
			u.repo.Stt(),
			u.repo.SttManual(),
			u.repo.Commodity(),
			u.repo.Partner(),
			u.repo.Client(),
			u.repo.SttOptionalRate(),
			u.repo.District(),
			u.repo.Account(),
			u.repo.CheckTariff(),
			u.repo.Product(),
			u.repo.SttPiece(),
			u.repo.City(),
			u.repo.EstimateSla(),
			u.repo.Shipment(),
			u.repo.ShipmentPacket(),
			u.repo.SttPieceHistory(),
			u.repo.RouteRepository(),
			u.repo.ConfigurablePrice(),
			u.repo.Reason(),
			u.GatewayStt(),
			u.GatewaySttStatus(),
			u.repo.Wallet(),
			u.repo.BalanceLimit(),
			u.MessageGateway(),
			u.repo.SttElexys(),
			u.repo.Account(),
			u.repo.CargoDetail(),
			u.repo.StiDest(),
			u.repo.LogFailedDTPOL(),
			u.SttActivity(),
			u.repo.CargoFlight(),
			u.repo.Transaction(),
			u.repo.AlgoRepo(),
			u.repo.Delivery(),
			u.repo.SttTransaction(),
			u.repo.Time(),
			u.CargoV2(),
			u.repo.Ngen(),
			u.repo.Cargo(),
			u.repo.Handover(),
			u.repo.HandoverDetail(),
			u.repo.Country(),
			u.repo.PredefinedHoliday(),
			u.repo.RebuttalDex(),
			u.repo.PartnerLog(),
			u.repo.RetryPubsubRepo(),
			u.repo.Checkout(),
			u.repo.SttCorporateRepo(),
			u.repo.CustomProcess(),
			u.repo.SttReverseJourney(),
			u.repo.ExchangeRate(),
			u.cfg.PUBSUB(),
			u.repo.PartnerLog(),
			u.repo.CrossDockingFailedRepo(),
			u.repo.Slack(),
			u.repo.BagVendorRepo(),
			shared.NewDictionaryError(),
			u.repo.CodConfig(),
			u.repo.EmbargoConfig(),
			u.repo.CityBalance(),
			u.repo.Bag(),
			u.BagVendor(),
			u.repo.FlagManagementRepository(),
			u.repo.PickupManifestCbpRepo(),
			u.ReadyToCargo(),
			u.RequestPriorityDelivery(),
			u.repo.InternationalDocumentRepository(),
			u.repo.DexAssessmentRepo(),
			u.repo.DtpolRepository(),
			u.repo.SttDueRepository(),
			u.repo.PartnerLocation(),
			u.repo.SttVendor(),
			u.repo.CampaignSttQuoteRepository(),
			u.repo.CacheRepository(),
			u.repo.ShortlinkRepository(),
			u.repo.SttAssessmentRepository(),
			u.repo.SttCustomFlagRepository(),
			u.repo.ConfigDfodPasti(),
			u.RiskClassification(),
			u.repo.CustomerRepository(),
			u.repo.MiddlewareClient(),
			u.repo.ConfigurableRule(),
		)
	})
	return ucStt
}

func (u *usecaseRegistry) PickupManifest() usecase.PickupManifest {
	var ucPickupManifest usecase.PickupManifest
	var once sync.Once
	once.Do(func() {
		ucPickupManifest = usecase.NewPickupManifestUc(
			u.repo.PickupManifest(),
			u.repo.Stt(),
			u.repo.SttPiece(),
			u.repo.SttOptionalRate(),
			u.repo.District(),
			u.repo.Commodity(),
			u.repo.SttPieceHistory(),
			u.repo.Driver(),
			u.repo.Vehicle(),
			u.repo.Partner(),
			u.repo.City(),
			u.repo.Client(),
			u.GatewaySttStatus(),
			u.Elexys(),
			u.repo.SttElexys(),
			u.SttActivity(),
			u.repo.Account(),
		)
	})
	return ucPickupManifest
}

func (u *usecaseRegistry) Sti() usecase.Sti {
	var ucSti usecase.Sti
	var once sync.Once
	once.Do(func() {
		ucSti = usecase.NewStiUc(
			u.repo.Account(),
			u.repo.Stt(),
			u.repo.SttPieceHistory(),
			u.repo.Sti(),
			u.repo.StiDetail(),
			u.repo.SttPiece(),
			u.repo.Partner(),
			u.repo.District(),
			u.repo.SttOptionalRate(),
			u.repo.Commodity(),
			u.repo.City(),
			u.repo.StiDetail(),
			u.repo.SttOptionalRate(),
			u.repo.Time(),
			u.repo.BagVendorRepo(),
			u.GatewaySttStatus(),
			u.SttActivity(),
			&u.cfg,
			u.JNE(),
			u.Ninja(),
			u.Stt(),
			u.repo.PickupManifest(),
			u.repo.BagDetail(),
			u.repo.BaggingGroupLocation(),
			u.repo.PriorityDeliveryRepository(),
			u.repo.StiTemporaryRepository(),
			u.repo.City(),
			u.ReadyToCargo(),
			u.repo.PredefinedHoliday(),
			u.repo.Xlsx(),
			u.repo.SttDueRepository(),
			u.repo.SttDueHistoryRepository(),
			u.repo.CacheRepository(),
			u.repo.SttAssessmentRepository(),
			u.SttDueUC(),
			u.PtPosUC(),
		)
	})
	return ucSti
}

func (u *usecaseRegistry) StiSc() usecase.StiSc {
	var ucStiSc usecase.StiSc
	var once sync.Once
	once.Do(func() {
		ucStiSc = usecase.NewStiScUc(
			usecase.SetStiScUCStiScRepo(u.repo.Sti()),
			usecase.SetStiScUCSttPieceRepo(u.repo.SttPiece()),
			usecase.SetStiScUCPartnerRepo(u.repo.Partner()),
			usecase.SetStiScUCDistrictRepo(u.repo.District()),
			usecase.SetStiScUCSttOptionalRateRepo(u.repo.SttOptionalRate()),
			usecase.SetStiScUCCommodityRepo(u.repo.Commodity()),
			usecase.SetStiScUCSttRepo(u.repo.Stt()),
			usecase.SetStiScUCSttDueHistoryRepo(u.repo.SttDueHistoryRepository()),
			usecase.SetStiScUCAccountRepo(u.repo.Account()),
			usecase.SetStiScUCStiDetailRepo(u.repo.StiDetail()),
			usecase.SetStiScUCOptionalRateRepo(u.repo.SttOptionalRate()),
			usecase.SetStiScUCCityRepo(u.repo.City()),
			usecase.SetStiScUCTimeRepo(u.repo.Time()),
			usecase.SetStiScUCGatewaySttStatusUc(u.GatewaySttStatus()),
			usecase.SetStiScUCSttActivityUc(u.SttActivity()),
			usecase.SetStiScUCCfg(u.cfg),
			usecase.SetStiScUCSttPieceHistoryRepo(u.repo.SttPieceHistory()),
			usecase.SetStiScUCBaggingGroupLocationRepo(u.repo.BaggingGroupLocation()),
			usecase.SetStiScUCPartnerLocationRepo(u.repo.PartnerLocation()),
			usecase.SetStiScUCPredefinedHolidayRepo(u.repo.PredefinedHoliday()),
			usecase.SetStiScUCSttDueRepo(u.repo.SttDueRepository()),
			usecase.SetStiScUCRtcUc(u.ReadyToCargo()),
			usecase.SetStiScUCCacheRepo(u.repo.CacheRepository()),
			usecase.SetStiScUCSttScRepo(u.repo.StiSCRepository()),
			usecase.SetStiScUCPartnerLogRepo(u.repo.PartnerLog()),
			usecase.SetStiScUCPriorityDeliveryRepo(u.repo.PriorityDeliveryRepository()),
		)
	})
	return ucStiSc
}

func (u *usecaseRegistry) Bag() usecase.Bag {
	var bag usecase.Bag
	var once sync.Once
	once.Do(func() {
		bag = usecase.NewBagUc(
			u.repo.Bag(),
			u.repo.BagDetail(),
			u.repo.Partner(),
			u.repo.City(),
			u.repo.District(),
			u.repo.SttPiece(),
			u.repo.Commodity(),
			u.repo.Stt(),
			u.repo.Account(),
			u.repo.SttOptionalRate(),
			u.repo.CargoConfiguration(),
			u.GatewaySttStatus(),
			u.repo.SttElexys(),
			u.SttActivity(),
			u.SttPayment(),
			u.repo.PartnerLog(),
			u.repo.BaggingGroupLocation(),
			u.ReadyToCargo(),
			u.repo.BagVendorRepo(),
			u.repo.Product(),
			u.repo.CommodityGroup(),
			u.repo.BagCargo(),
			u.repo.BaggingReadyToCargo(),
			u.repo.BagCommodityGroup(),
			u.repo.CacheRepository(),
			shared.NewDictionaryError(),
			u.repo.SttReadyToCargoRepo(),
			u.repo.Shipment(),
			u.repo.LogRtcRepository(),
			&u.cfg,
			u.repo.SttDueRepository(),
			u.repo.SttAssessmentRepository(),
			u.repo.SalesforceRepository(),
		)
	})

	return bag
}

func (u *usecaseRegistry) BagVendor() usecase.BagOrchestra {
	var bagVendor usecase.BagOrchestra
	var once sync.Once
	once.Do(func() {
		bagVendor = usecase.NewBagOrchestraUc(
			u.repo.Bag(),
			u.repo.BagDetail(),
			u.repo.City(),
			u.repo.District(),
			u.repo.SttPiece(),
			u.repo.Commodity(),
			u.repo.Stt(),
			u.GatewaySttStatus(),
			u.repo.PartnerLog(),
			u.repo.BaggingGroupLocation(),
			u.repo.BagVendor(),
			u.repo.CacheRepository(),
			u.repo.PartnerLog(),
			u.repo.BagVendorRepo(),
			u.repo.Account(),
			&u.cfg,
			u.repo.Product(),
			u.repo.CommodityGroup(),
			u.repo.SttDueRepository(),
		)
	})

	return bagVendor
}

func (u *usecaseRegistry) StiDest() usecase.StiDest {
	var stiDest usecase.StiDest
	var once sync.Once
	once.Do(func() {
		stiDest = usecase.NewStiDestUc(
			u.repo.Account(),
			u.repo.StiDest(),
			u.repo.SttPiece(),
			u.repo.SttPieceHistory(),
			u.repo.Stt(),
			u.repo.City(),
			u.repo.Partner(),
			u.repo.Bag(),
			u.repo.SttOptionalRate(),
			u.repo.Commodity(),
			u.repo.District(),
			u.repo.CargoDetail(),
			u.repo.CacheRepository(),
			u.repo.Cargo(),
			u.GatewaySttStatus(),
			u.Ninja(),
			u.cfg,
			u.repo.MiddlewareClient(),
			u.SttActivity(),
			u.JNE(),
			u.repo.RouteRepository(),
			u.repo.Time(),
			u.MessageGateway(),
			u.SttPayment(),
			u.Stt(),
			u.repo.BagVendorRepo(),
			u.repo.BagDetail(),
			u.ReadyToCargo(),
			u.repo.PriorityDeliveryRepository(),
			u.repo.StiDestTemporaryRepository(),
			u.repo.Xlsx(),
			u.repo.PredefinedHoliday(),
			u.repo.SttDueRepository(),
			u.repo.SttAssessmentRepository(),
			u.SttDueUC(),
			u.PtPosUC(),
		)
	})

	return stiDest
}

func (u *usecaseRegistry) StiDestSc() usecase.StiDestSc {
	var stiDestSc usecase.StiDestSc
	var once sync.Once

	once.Do(func() {
		stiDestSc = usecase.NewStiDestScUc(
			u.repo.SttPiece(),
			u.repo.Partner(),
			u.repo.District(),
			u.repo.SttOptionalRate(),
			u.repo.Commodity(),
			u.repo.Stt(),
			u.repo.StiDetail(),
			u.repo.SttPieceHistory(),
			u.repo.StiDest(),
			u.repo.City(),
			u.repo.StiDestDetail(),
			u.repo.Bag(),
			u.repo.CargoDetail(),
			u.repo.CacheRepository(),
			u.repo.Cargo(),
			u.GatewaySttStatus(),
			u.SttActivity(),
			u.repo.Time(),
			u.repo.RouteRepository(),
			u.SttPayment(),
			u.Stt(),
			u.repo.SttDueRepository(),
		)
	})

	return stiDestSc
}

func (u *usecaseRegistry) Handover() usecase.Handover {
	var handover usecase.Handover
	var once sync.Once

	once.Do(func() {
		handover = usecase.NewHandoverUc(
			u.repo.Account(),
			u.repo.Handover(),
			u.repo.HandoverDetail(),
			u.repo.Commodity(),
			u.repo.Stt(),
			u.repo.Partner(),
			u.repo.District(),
			u.repo.SttOptionalRate(),
			u.repo.CacheRepository(),
			u.repo.City(),
			u.repo.SttPiece(),
			u.GatewaySttStatus(),
			u.SttActivity(),
			u.repo.SttPieceHistory(),
			u.SttPayment(),
			u.repo.SttVendor(),
			u.cfg,
			u.Ninja(),
			u.JNE(),
			u.Luwjistik(),
			u.repo.Bag(),
			u.GatewayStt(),
			u.repo.Time(),
			u.ReadyToCargo(),
			u.repo.SttDueRepository(),
			u.PtPosUC(),
		)
	})

	return handover
}

// Shortland ...
func (u *usecaseRegistry) Shortland() usecase.Shortland {
	var shortland usecase.Shortland
	var once sync.Once

	once.Do(func() {
		shortland = usecase.NewShortlandUc(u.repo.Stt(), u.repo.SttPiece(), u.repo.SttPieceHistory(), u.repo.Commodity(), u.repo.StiDest(), u.repo.Partner(), u.repo.Cargo(), u.repo.City())
	})

	return shortland
}

// DeliveryManifest ...
func (u *usecaseRegistry) DeliveryManifest() usecase.DeliveryManifest {
	var deliveryManifest usecase.DeliveryManifest
	var once sync.Once

	once.Do(func() {
		deliveryManifest = usecase.NewDeliveryManifestUc(
			u.repo.DeliveryManifest(),
			u.repo.Partner(),
			u.repo.CacheRepository(),
			u.repo.Bag(),
			u.repo.SttOptionalRate(),
			u.repo.Commodity(),
			u.repo.City(),
			u.repo.CargoDetail(),
			u.repo.Stt(),
			u.repo.DeliveryManifestDetail(),
			u.repo.SttPiece(),
			u.repo.Time(),
			u.SttPayment(),
			u.repo.District(),
			u.repo.Delivery(),
			&u.cfg,
			u.repo.PriorityDeliveryRepository(),
			u.repo.SttAssessmentRepository(),
		)
	})

	return deliveryManifest
}

// Pod ...
func (u *usecaseRegistry) Pod() usecase.Pod {
	var pod usecase.Pod
	var once sync.Once

	once.Do(func() {
		pod = usecase.NewPodUc(
			u.repo.Reason(),
			u.repo.Delivery(),
			u.repo.Partner(),
			u.repo.Driver(),
			u.repo.Commodity(),
			u.repo.Pod(),
			u.repo.SttPiece(),
			u.GatewaySttStatus(),
			u.repo.Stt(),
			u.repo.SttPieceHistory(),
			&u.cfg,
			u.MessageGateway(),
			u.repo.Client(),
			u.repo.City(),
			u.SttActivity(),
			u.repo.AlgoRepo(),
			u.repo.Time(),
			u.SttPayment(),
			u.repo.RebuttalDex(),
			u.Stt(),
			shared.NewDictionaryError(),
			u.repo.CodConfig(),
			u.repo.Shipment(),
			u.repo.BulkDownload(),
			u.repo.Storage(),
			u.repo.Xlsx(),
			u.repo.FlagManagementRepository(),
			u.GatewayStt(),
			u.repo.District(),
			u.ReadyToCargo(),
			u.RequestPriorityDelivery(),
			u.Salesforce(),
			u.repo.CacheRepository(),
		)
	})

	return pod
}

// Reason ...
func (u *usecaseRegistry) Reason() usecase.Reason {
	var reason usecase.Reason
	var once sync.Once

	once.Do(func() {
		reason = usecase.NewReasonUc(u.repo.Reason(), u.repo.Stt(), u.cfg)
	})

	return reason
}

// CustomProcess ...
func (u *usecaseRegistry) CustomProcess() usecase.CustomProcess {
	var customProcess usecase.CustomProcess
	var once sync.Once

	once.Do(func() {
		customProcess = usecase.NewCustomProcess(
			u.repo.CustomProcess(),
			u.repo.Partner(),
			u.repo.SttPiece(),
			u.repo.Commodity(),
			u.repo.District(), u.repo.City(),
			u.GatewaySttStatus(),
			u.repo.MiddlewareClient(),
			u.SttActivity(),
			u.repo.SttPieceHistory(),
			u.repo.Delivery(),
			u.repo.CustomProcessRole(),
			u.MessageGateway(),
			u.SttPayment(),
			u.repo.Account(),
			u.Stt(),
			&u.cfg,
			u.repo.Client(),
			u.repo.PartnerLog(),
			u.repo.Reason(),
			u.repo.Stt(),
			u.repo.Product(),
			u.repo.Commodity(),
			u.repo.Bag(),
			u.repo.BagVendorRepo(),
			u.repo.Shipment(),
			u.repo.SttOptionalRate(),
			shared.NewDictionaryError(),
			u.GatewayStt(),
			u.repo.FlagManagementRepository(),
			u.repo.Time(),
			u.ReadyToCargo(),
			u.RequestPriorityDelivery(),
			u.repo.PriorityDeliveryRepository(),
			u.repo.SttDueRepository(),
			u.repo.DeliveryManifest(),
		)
	})

	return customProcess
}

// GatewaySttTracking ...
func (u *usecaseRegistry) GatewaySttTracking() usecase.GatewaySttTracking {
	var gatewaySttTracking usecase.GatewaySttTracking
	var once sync.Once

	once.Do(func() {
		gatewaySttTracking = usecase.NewGatewaySttTrackingUc(
			&u.cfg,
			u.repo.Stt(),
			u.repo.ShipmentPacket(),
			u.repo.City(),
			u.repo.SttOptionalRate(),
			u.repo.SttPiece(),
			u.repo.SttPieceHistory(),
			u.repo.Account(),
			u.repo.Client(),
			u.repo.Partner(),
			u.repo.Reason(),
			u.repo.Country(),
			u.repo.PredefinedHoliday(),
			u.repo.RebuttalDex(),
			u.repo.Shipment(),
			u.repo.District(),
			u.repo.Delivery(),
		)
	})

	return gatewaySttTracking
}

// GatewayStt ...
func (u *usecaseRegistry) GatewayStt() usecase.GatewayStt {
	var ucGatewayStt usecase.GatewayStt
	var once sync.Once
	once.Do(func() {
		ucGatewayStt = usecase.NewGatewaySttUc(
			usecase.ArgsGatewayStt{
				SttRepo:               u.repo.Stt(),
				CityRepo:              u.repo.City(),
				CommodityRepo:         u.repo.Commodity(),
				Cfg:                   &u.cfg,
				PubsubActivityLogRepo: u.repo.PartnerLog(),
				MiddlewareRepo:        u.repo.MiddlewareClient(),
				ClientRepo:            u.repo.Client(),
				SttTransactionRepo:    u.repo.SttTransaction(),
				AccountRepo:           u.repo.Account(),
				SttOptionalRateRepo:   u.repo.SttOptionalRate(),
				SttReportRepo:         u.repo.SttReport(),
				ShipmentRepo:          u.repo.Shipment(),
				DeliveryRepo:          u.repo.Delivery(),
				SttPieceHistory:       u.repo.SttPieceHistory(),
				SttPiecesRepository:   u.repo.SttPiece(),
				TimeRepo:              u.repo.Time(),
				PartnerRepo:           u.repo.Partner(),
				DistrictRepo:          u.repo.District(),
				PartnerLog:            u.repo.PartnerLog(),
				SlackRepo:             u.repo.Slack(),
				DexAssessmentRepo:     u.repo.DexAssessmentRepo(),
				SttAssessmentRepo:     u.repo.SttAssessmentRepository(),
				SttCustomFlagRepo:     u.repo.SttCustomFlagRepository(),
				NotificationRepo:      u.repo.NotificationRepository(),
				CacheRepo:             u.repo.CacheRepository(),
				PredefinedHolidayRepo: u.repo.PredefinedHoliday(),
			},
		)
	})
	return ucGatewayStt
}

// GatewaySttStatus ...
func (u *usecaseRegistry) GatewaySttStatus() usecase.GatewaySttStatus {
	var ucGatewaySttStatus usecase.GatewaySttStatus
	var once sync.Once
	once.Do(func() {
		ucGatewaySttStatus = usecase.NewGatewaySttStatusUc(
			&u.cfg,
			u.repo.PartnerLog(),
			u.repo.Delivery(),
			u.repo.Partner(),
			u.repo.Stt(),
			u.repo.Reason(),
			u.repo.SttPiece(),
			u.repo.Pod(),
			u.repo.DeliveryManifest(),
			u.repo.City(),
			u.MessageGateway(),
			u.repo.SttPieceHistory(),
			u.repo.MiddlewareClient(),
			u.repo.Client(),
			u.repo.CargoDetail(),
			u.repo.CargoFlight(),
			u.SttActivity(),
			u.repo.AlgoRepo(),
			u.SttPayment(),
			u.repo.RebuttalDex(),
			u.repo.Time(),
			u.repo.Slack(),
			u.repo.CodConfig(),
			u.repo.FlagManagementRepository(),
			u.repo.SttPiece(),
			u.repo.Shipment(),
			u.repo.PartnerLocation(),
			u.GatewayStt(),
			u.ReadyToCargo(),
			u.Salesforce(),
			u.RequestPriorityDelivery(),
			u.repo.PriorityDeliveryRepository(),
			u.repo.SttDueRepository(),
			u.SttDueUC(),
			u.repo.CacheRepository(),
			u.repo.SttCustomFlagRepository(),
			u.repo.Account(),
			u.repo.SttOptionalRate(),
		)
	})
	return ucGatewaySttStatus
}

// Cargo ...
func (u *usecaseRegistry) Cargo() usecase.Cargo {
	var cargo usecase.Cargo
	var once sync.Once

	once.Do(func() {
		cargo = usecase.NewCargoUc(
			u.repo.Cargo(),
			u.repo.CargoDetail(),
			u.repo.City(),
			u.repo.District(),
			u.repo.SttPieceHistory(),
			u.repo.Stt(),
			u.repo.SttPiece(),
			u.repo.Commodity(),
			u.repo.Product(),
			u.repo.CargoProductType(),
			u.repo.CommodityGroup(),
			u.repo.Partner(),
			u.repo.Bag(),
			u.repo.Ngen(),
			u.repo.Airport(),
			u.GatewaySttStatus(),
			u.repo.Account(),
			u.repo.CargoFlight(),
			u.SttActivity(),
			u.repo.SttActivity(),
			u.SttPayment(),
			u.repo.RetryCargoDetail(),
			u.repo.BaggingGroupLocation(),
			u.ReadyToCargo(),
			u.repo.BagVendorRepo(),
			u.repo.SttReadyToCargoRepo(),
			shared.NewDictionaryError(),
			u.repo.BagCargo(),
			usecase.NewCargoReserve(
				&u.cfg, u.repo.CargoReserve(),
				shared.NewDictionaryError(),
				u.repo.CacheRepository(),
				u.repo.Account(),
				u.repo.PartnerLog(),
				u.repo.Partner(),
				u.repo.Time(),
			),
			u.repo.Shipment(),
			u.repo.CacheRepository(),
			u.repo.RetryCargo(),
			u.repo.Time(),
			u.repo.CargoReserve(),
			u.RetryCargo(),
			u.repo.SttDueRepository(),
			u.repo.SttAssessmentRepository(),
			u.SttDueUC(),
			u.repo.PartnerLocation(),
			u.repo.PriorityDeliveryRepository(),
			u.repo.PartnerLog(),
			u.repo.SalesforceRepository(),
		)
	})

	return cargo
}

// On Process ...
func (u *usecaseRegistry) OnProcess() usecase.OnProcess {
	var onProcess usecase.OnProcess
	var once sync.Once

	once.Do(func() {
		onProcess = usecase.NewOnProcessUc(
			u.repo.Stt(),
			u.repo.SttOptionalRate(),
			u.repo.Commodity(),
			u.repo.SttElexys(),
			u.repo.Partner(),
			u.repo.Delivery(),
			u.repo.Reason(),
			u.repo.SttCustomFlagRepository(),
		)
	})

	return onProcess
}

// Del ...
func (u *usecaseRegistry) Del() usecase.Del {
	var del usecase.Del
	var once sync.Once

	once.Do(func() {
		del = usecase.NewDelUc(
			u.repo.Delivery(),
			u.repo.Stt(),
			u.repo.CacheRepository(),
			u.repo.Commodity(),
			u.repo.Partner(),
			u.repo.District(),
			u.repo.SttOptionalRate(),
			u.repo.Driver(),
			u.repo.Time(),
			&u.cfg,
			u.repo.DeliveryVendor(),
			u.repo.City(),
			u.GatewaySttStatus(),
			u.MessageGateway(),
			u.SttActivity(),
			u.SttPayment(),
			u.repo.SttPieceHistory(),
			u.repo.Handover(),
			u.Stt(),
			u.repo.Shipment(),
			u.repo.FlagManagementRepository(),
			u.ReadyToCargo(),
			u.repo.PriorityDeliveryRepository(),
			u.repo.SttDueRepository(),
			u.repo.AlgoRepo(),
			u.repo.SttAssessmentRepository(),
		)
	})

	return del
}

// Ngen ...
func (u *usecaseRegistry) Ngen() usecase.Ngen {
	var ngen usecase.Ngen
	var once sync.Once

	once.Do(func() {
		ngen = usecase.NewNgenUc(
			u.repo.Ngen(),
		)
	})

	return ngen
}

// Ninja ...
func (u *usecaseRegistry) Ninja() usecase.Ninja {
	var ninja usecase.Ninja
	var once sync.Once

	once.Do(func() {
		ninja = usecase.NewNinjaUc(&u.cfg,
			u.repo.Ninja(),
			u.repo.PartnerLog(),
			u.repo.District(),
			u.repo.StiDetail(),
			u.repo.SttPieceHistory(),
			u.repo.Stt(),
			u.GatewaySttStatus(),
			u.repo.City(),
			u.repo.Account(),
			u.repo.Delivery(),
			u.repo.Pod(),
			u.repo.Reason(),
			u.MessageGateway(),
			u.repo.SttVendor(),
			u.MessageGateway(),
			u.repo.Time(),
			u.repo.ReasonVendorMapping(),
			u.ReadyToCargo(),
			u.RequestPriorityDelivery(),
			u.repo.SttDueRepository(),
		)
	})

	return ninja
}

// MessageGateway ...
func (u *usecaseRegistry) MessageGateway() usecase.MessageGateway {
	var res usecase.MessageGateway
	var once sync.Once

	once.Do(func() {
		res = usecase.NewMessageGatewayUc(
			usecase.ParamMessageGatewayCtx{
				Cfg:                   &u.cfg,
				MessageGateway:        u.repo.MessageGateway(),
				EstimateSla:           u.repo.EstimateSla(),
				PartnerLog:            u.repo.PartnerLog(),
				DeliveryRepo:          u.repo.Delivery(),
				RepoLogMessageService: u.repo.LogMessage(),
				PredefinedHolidayRepo: u.repo.PredefinedHoliday(),
				Shortlink:             u.Shortlink(),
				FlagManagementRepo:    u.repo.FlagManagementRepository(),
				AccountRepo:           u.repo.Account(),
				ClientRepo:            u.repo.Client(),
				SttPieceHistoryRepo:   u.repo.SttPieceHistory(),
				PartnerRepo:           u.repo.Partner(),
				ReasonRepo:            u.repo.Reason(),
			},
		)
	})

	return res
}

// elexys ...
func (u usecaseRegistry) Elexys() usecase.Elexys {
	var res usecase.Elexys
	var once sync.Once

	once.Do(func() {
		res = usecase.NewElexysUc(
			u.repo.Stt(),
			&u.cfg,
			u.repo.PartnerLog(),
			u.repo.City(),
			u.repo.Elexys(),
			u.repo.Account(),
			u.repo.Commodity(),
			u.repo.District(),
			u.repo.Partner(),
			u.repo.Client(),
			u.repo.SttElexys(),
			u.repo.SttOptionalRate(),
			u.repo.Product(),
			u.repo.SttElexys(),
			u.repo.RouteRepository(),
			u.GatewayStt(),
			u.repo.CheckTariff(),
			u.repo.ConfigurablePrice(),
			u.MessageGateway(),
			u.repo.SttPiece(),
			u.repo.SttPieceHistory(),
			u.repo.LogElexysPickupManifest(),
			u.repo.LogSttFailedElexys(),
		)
	})

	return res
}

// report ...
func (u usecaseRegistry) Report() usecase.Report {
	var res usecase.Report
	var once sync.Once

	once.Do(func() {
		res = usecase.NewReportUc(
			u.repo.StiDest(),
			u.repo.Stt(),
			&u.cfg,
			u.repo.Client(),
			u.repo.SttManual(),
			u.repo.Partner(),
			u.repo.Cargo(),
			u.repo.City(),
			u.repo.SttElexys(),
			u.repo.District(),
			u.repo.Commodity(),
			u.repo.Transaction(),
			u.repo.SttOptionalRate(),
			u.repo.Bag(),
			u.repo.Delivery(),
			u.repo.SttVendor(),
			u.repo.SttPieceHistory(),
			u.repo.Report(),
			u.repo.SttPiece(),
			u.repo.SttPieceHistory(),
			u.repo.SttActivity(),
			u.repo.SttReport(),
			u.repo.Storage(),
			u.repo.Time(),
			u.repo.BulkDownload(),
			u.repo.Xlsx(),
			u.repo.FlagManagementRepository(),
			u.HoldBalanceHistory(),
			u.repo.SttReportPostgresRepository(),
			u.repo.PartnerLog(),
			u.repo.AlgoRepo(),
		)
	})

	return res
}

func (u *usecaseRegistry) Health() usecase.Health {
	var healthUc usecase.Health
	var once sync.Once
	once.Do(func() {
		healthUc = usecase.NewHealthUc(
			u.repo.Health(),
			u.cfg,
			u.repo.Time(),
		)
	})

	return healthUc
}

func (u *usecaseRegistry) LogSttFailedElexys() usecase.LogFailedSttElexys {
	var logFailedSttElexysUc usecase.LogFailedSttElexys
	var once sync.Once
	once.Do(func() {
		logFailedSttElexysUc = usecase.NewLogFailedSttElexysUc(
			u.repo.LogSttFailedElexys(),
		)
	})

	return logFailedSttElexysUc
}

func (u *usecaseRegistry) SttElexys() usecase.SttElexys {
	var sttElexysUc usecase.SttElexys
	var once sync.Once
	once.Do(func() {
		sttElexysUc = usecase.NewSttElexysUc(
			u.repo.SttElexys(),
		)
	})

	return sttElexysUc
}

func (u *usecaseRegistry) Util() usecase.Util {
	var utilUc usecase.Util
	var once sync.Once
	once.Do(func() {
		utilUc = usecase.NewUtilUc(
			u.cfg,
			u.repo.Util(),
		)
	})

	return utilUc
}

func (u *usecaseRegistry) PartnerLog() usecase.PartnerLog {
	var partnerLogUc usecase.PartnerLog
	var once sync.Once
	once.Do(func() {
		partnerLogUc = usecase.NewPartnerLogUc(
			u.repo.PartnerLog(),
			u.repo.Stt(),
		)
	})

	return partnerLogUc
}

// reportV2 ...
func (u usecaseRegistry) ReportV2() usecase.ReportV2 {
	var res usecase.ReportV2
	var once sync.Once

	once.Do(func() {
		res = usecase.NewReportV2Uc(
			u.repo.StiDest(),
			u.repo.Stt(),
			&u.cfg,
			u.repo.Client(),
			u.repo.SttManual(),
			u.repo.Partner(),
			u.repo.Cargo(),
			u.repo.City(),
			u.repo.SttElexys(),
			u.repo.District(),
			u.repo.Commodity(),
			u.repo.Transaction(),
			u.repo.SttOptionalRate(),
			u.repo.Bag(),
			u.repo.Delivery(),
			u.repo.SttVendor(),
			u.repo.SttPieceHistory(),
			u.repo.Report(),
			u.repo.SttPiece(),
			u.repo.SttPieceHistory(),
			u.repo.BulkDownload(),
			u.repo.PartnerLog(),
			u.repo.SttActivity(),
			u.repo.KejarcuanReport(),
			u.repo.ProgressiveCommissionReport(),
			u.repo.Xlsx(),
			u.repo.Storage(),
			u.repo.Time(),
			u.repo.DtpolReports(),
			u.repo.ProgressiveCommissionConfig(),
			u.repo.SttReport(),
			u.repo.PosAffiliate(),
			u.repo.FlagManagementRepository(),
			u.repo.SttReportPostgresRepository(),
		)
	})

	return res
}

// reportV3 ...
func (u usecaseRegistry) ReportV3() usecase.ReportV3 {
	var res usecase.ReportV3
	var once sync.Once

	once.Do(func() {
		res = usecase.NewReportV3Uc(
			u.repo.StiDest(),
			u.repo.Stt(),
			&u.cfg,
			u.repo.Client(),
			u.repo.SttManual(),
			u.repo.Partner(),
			u.repo.Cargo(),
			u.repo.City(),
			u.repo.SttElexys(),
			u.repo.District(),
			u.repo.Commodity(),
			u.repo.Transaction(),
			u.repo.SttOptionalRate(),
			u.repo.Bag(),
			u.repo.Delivery(),
			u.repo.SttVendor(),
			u.repo.SttPieceHistory(),
			u.repo.Report(),
			u.repo.SttPiece(),
			u.repo.SttPieceHistory(),
			u.repo.BulkDownload(),
			u.repo.SttReport(),
			u.repo.OutgoingReport(),
			u.repo.Storage(),
			u.repo.Xlsx(),
			u.repo.Time(),
			u.repo.MissrouteReport(),
			u.repo.FlagManagementRepository(),
		)
	})

	return res
}

func (u *usecaseRegistry) SttTransaction() usecase.SttTransaction {
	var sttTransactionUc usecase.SttTransaction
	var once sync.Once
	once.Do(func() {
		sttTransactionUc = usecase.NewSttTransactionUc(
			&u.cfg,
			u.repo.SttTransaction(),
			u.repo.Stt(),
			u.repo.BulkDownload(),
			u.repo.Partner(),
			u.repo.PartnerLog(),
			u.repo.SttOptionalRate(),
			u.repo.Commodity(),
			u.repo.Time(),
			u.repo.FlagManagementRepository(),
		)
	})

	return sttTransactionUc
}

// CargoV2 ...
func (u *usecaseRegistry) CargoV2() usecase.CargoV2 {
	var cargo usecase.CargoV2
	var once sync.Once

	once.Do(func() {
		cargo = usecase.NewCargoV2Uc(
			u.repo.Cargo(),
			u.repo.CargoDetail(),
			u.repo.City(),
			u.repo.District(),
			u.repo.SttPieceHistory(),
			u.repo.Stt(),
			u.repo.SttPiece(),
			u.repo.Commodity(),
			u.repo.Product(),
			u.repo.CargoProductType(),
			u.repo.CommodityGroup(),
			u.repo.Partner(),
			u.repo.Bag(),
			u.repo.Ngen(),
			u.repo.Airport(),
			u.GatewaySttStatus(),
			u.repo.Account(),
			u.repo.PartnerLocation(),
			u.repo.PartnerLog(),
			u.repo.CargoFlight(),
			u.repo.SttActivity(),
			u.SttActivity(),
			u.repo.Time(),
			u.Luwjistik(),
			u.repo.RetryCargo(),
			u.ReadyToCargo(),
			u.repo.CargoReserve(),
			u.CargoReserve(),
			u.repo.BagCargo(),
			u.repo.SttReadyToCargoRepo(),
			u.repo.BaggingReadyToCargoHistory(),
			u.repo.ReadyToCargo(),
			u.repo.CacheRepository(),
			u.repo.SttDueRepository(),
			u.repo.Hub(),
			u.SttDueUC(),
			u.RetryCargo(),
			u.CargoSearchFlight(),
			u.repo.ReadyToCargoHistory(),
		)
	})

	return cargo
}

func (u *usecaseRegistry) SttActivity() usecase.SttActivity {
	var sttActivityUc usecase.SttActivity
	var once sync.Once
	once.Do(func() {
		sttActivityUc = usecase.NewSttActivity(
			u.repo.SttActivity(),
		)
	})

	return sttActivityUc
}

func (u *usecaseRegistry) Kejarcuan() usecase.Kejarcuan {
	var kejarcuanUc usecase.Kejarcuan
	var once sync.Once
	once.Do(func() {
		kejarcuanUc = usecase.NewKejarcuanUc(
			u.repo.KejarcuanReport(),
			&u.cfg,
			u.repo.Account(),
			u.repo.Stt(),
			u.repo.Partner(),
			u.repo.PartnerLog(),
		)
	})

	return kejarcuanUc
}

func (u *usecaseRegistry) GatewayTrucking() usecase.GatewayTrucking {
	var gatewayTruckingUc usecase.GatewayTrucking
	var once sync.Once
	once.Do(func() {
		gatewayTruckingUc = usecase.NewGatewayTruckingUc(
			u.repo.Stt(),
			u.repo.Cargo(),
			u.repo.PartnerLog(),
			u.repo.Partner(),
			u.repo.SttPieceHistory(),
			u.SttActivity(),
			u.GatewaySttStatus(),
			u.repo.Account(),
			u.repo.Bag(),
			u.repo.City(),
			u.repo.Commodity(),
			u.repo.SttElexys(),
			u.repo.BagVendorRepo(),
			u.repo.Shipment(),
			u.SttDueUC(),
		)
	})

	return gatewayTruckingUc
}

func (u *usecaseRegistry) JNE() usecase.JNE {
	var jneUc usecase.JNE
	var once sync.Once
	once.Do(func() {
		jneUc = usecase.NewJneUc(
			&u.cfg,
			u.repo.JNE(),
			u.repo.PartnerLog(),
			u.repo.District(),
			u.repo.SttVendor(),
			u.repo.Stt(),
			u.repo.Delivery(),
			u.repo.Partner(),
			u.MessageGateway(),
			u.repo.Account(),
			u.repo.City(),
			u.repo.Reason(),
			u.repo.Pod(),
			u.GatewaySttStatus(),
			u.repo.SttPieceHistory(),
			u.repo.CustomProcess(),
			u.SttActivity(),
			u.repo.MiddlewareClient(),
			u.repo.Time(),
			u.repo.ReasonVendorMapping(),
			u.ReadyToCargo(),
			u.Salesforce(),
			u.RequestPriorityDelivery(),
			u.repo.SttDueRepository(),
			u.repo.NotificationRepository(),
		)
	})

	return jneUc
}

func (u *usecaseRegistry) TruckingHandler() message_broker.TruckingHandler {
	var ucTruckingHandler message_broker.TruckingHandler
	var once sync.Once
	once.Do(func() {
		ucTruckingHandler = message_broker.NewTruckingHandler(
			u.repo.PartnerLog(),
			u.GatewayTrucking(),
			u.cfg,
		)
	})
	return ucTruckingHandler
}

func (u *usecaseRegistry) MessageHandler() message_broker.MessageHandler {
	var ucMessageBroker message_broker.MessageHandler
	var once sync.Once
	once.Do(func() {
		ucMessageBroker = message_broker.NewMessageBroker(
			u.repo.PartnerLog(),
		)
	})
	return ucMessageBroker
}

func (u *usecaseRegistry) PodHandler() message_broker.PodHandler {
	var ucMessageBroker message_broker.PodHandler
	var once sync.Once
	once.Do(func() {
		ucMessageBroker = message_broker.NewPodHandler(
			u.repo.PartnerLog(),
			u.GatewayStt(),
			u.cfg,
		)
	})
	return ucMessageBroker
}

func (u *usecaseRegistry) InstantBooking() usecase.InstantBooking {
	var instantBooking usecase.InstantBooking
	var once sync.Once
	once.Do(func() {
		instantBooking = usecase.NewInstantBookingUc(
			u.cfg,
			u.repo.CacheRepository(),
			u.repo.InstantBooking(),
			u.repo.Time(),
			u.repo.InstantBookingDetail(),
			u.repo.ShipmentPacket(),
			u.repo.Partner(),
		)
	})
	return instantBooking
}

func (u *usecaseRegistry) SttV2() usecase.SttV2 {
	var sttV2 usecase.SttV2
	var once sync.Once
	once.Do(func() {
		sttV2 = usecase.NewSttV2Uc(
			u.Stt(),
			u.repo.Stt(),
			u.repo.CacheRepository(),
			u.repo.Account(),
			u.repo.Client(),
			u.cfg,
		)
	})
	return sttV2
}

// On Process ...
func (u *usecaseRegistry) OnProcessV2() usecase.OnProcessV2 {
	var onProcessV2 usecase.OnProcessV2
	var once sync.Once

	once.Do(func() {
		onProcessV2 = usecase.NewOnProcessV2(
			u.repo.Stt(),
			u.repo.SttOptionalRate(),
			u.repo.Commodity(),
			u.repo.SttElexys(),
			u.OnProcess(),
			&u.cfg,
		)
	})

	return onProcessV2
}

// Dashboard V2 ...
func (u *usecaseRegistry) DashboardV2() usecase.DashboardV2 {
	var dashboardV2 usecase.DashboardV2
	var once sync.Once

	once.Do(func() {
		dashboardV2 = usecase.NewDashboardV2Uc(
			&u.cfg,
			u.repo.Stt(),
			u.repo.Partner(),
			u.repo.BulkDownload(),
			u.repo.CacheRepository(),
			u.repo.Dashboard(),
			u.repo.Time(),
			u.repo.Delivery(),
			u.repo.FlagManagementRepository(),
		)
	})

	return dashboardV2
}

func (u *usecaseRegistry) SttManualV2() usecase.SttManualV2 {
	var sttManualV2 usecase.SttManualV2
	var once sync.Once
	once.Do(func() {
		sttManualV2 = usecase.NewSttManualV2Uc(
			u.repo.SttManual(),
			u.SttManual(),
			u.cfg,
		)
	})
	return sttManualV2
}

// BulkDownload ...
func (u *usecaseRegistry) BulkDownload() usecase.BulkDownload {
	var bulkDownload usecase.BulkDownload
	var once sync.Once

	once.Do(func() {
		bulkDownload = usecase.NewBulkDownload(
			u.repo.BulkDownload(),
			u.repo.Time(),
		)
	})

	return bulkDownload
}

func (u *usecaseRegistry) SttManualV3() usecase.SttManualV3 {
	var uc usecase.SttManualV3
	var once sync.Once
	once.Do(func() {
		uc = usecase.NewSttManualV3Uc(
			u.repo.SttManual(),
			u.SttManual(),
			u.cfg,
		)
	})
	return uc
}

// Dashboard V2 ...
func (u *usecaseRegistry) PodV2() usecase.PodV2 {
	var podV2 usecase.PodV2
	var once sync.Once

	once.Do(func() {
		podV2 = usecase.NewPodV2Uc(
			u.repo.Delivery(),
			u.repo.Partner(),
			u.repo.Stt(),
		)
	})

	return podV2
}

func (u *usecaseRegistry) Checkout() usecase.Checkout {
	var checkout usecase.Checkout
	var once sync.Once

	once.Do(func() {
		checkout = usecase.NewCheckoutUc(
			&u.cfg,
			u.repo.Stt(),
			u.repo.Checkout(),
		)
	})

	return checkout
}

func (u *usecaseRegistry) IncomingOutgoing() usecase.IncomingOutgoing {
	var incomingOutgoing usecase.IncomingOutgoing
	var once sync.Once

	once.Do(func() {
		incomingOutgoing = usecase.NewIncomingOutgoingUc(
			u.repo.StiDest(),
			u.repo.Bag(),
			u.repo.Partner(),
			u.repo.Cargo(),
		)
	})

	return incomingOutgoing
}

func (u *usecaseRegistry) LogMessage() usecase.LogMessageService {
	var logMessage usecase.LogMessageService
	var once sync.Once

	once.Do(func() {
		logMessage = usecase.NewLogMessageServiceUc(
			u.repo.LogMessage(),
			u.repo.MessageGateway(),
			u.repo.PartnerLog(),
		)
	})

	return logMessage
}

func (u *usecaseRegistry) GatewayPayment() usecase.GatewayPayment {
	var gatewayPayment usecase.GatewayPayment
	var once sync.Once

	once.Do(func() {
		gatewayPayment = usecase.NewGatewayPayment(
			&u.cfg,
			u.repo.Stt(),
			u.repo.PartnerLog(),
			u.repo.Partner(),
			u.repo.SttPieceHistory(),
			u.repo.SttPiece(),
			u.repo.Time(),
			u.GatewayStt(),
			u.Ninja(),
			u.JNE(),
			u.PtPosUC(),
		)
	})

	return gatewayPayment
}

func (u *usecaseRegistry) SttPayment() usecase.SttPayment {
	var sttPayment usecase.SttPayment
	var once sync.Once

	once.Do(func() {
		sttPayment = usecase.NewSttPaymentUc(
			u.repo.Stt(),
			u.repo.Account(),
			u.repo.Client(),
			u.repo.Commodity(),
			u.repo.SttOptionalRate(),
			u.repo.SttPaymentHistoryRepository(),
			u.repo.Time(),
			u.repo.BulkDownload(),
			u.repo.PartnerLog(),
			&u.cfg,
			u.repo.FlagManagementRepository(),
		)
	})

	return sttPayment
}

func (u *usecaseRegistry) ProgressiveCommission() usecase.ProgressiveCommission {
	var progressiveCommission usecase.ProgressiveCommission
	var once sync.Once

	once.Do(func() {
		progressiveCommission = usecase.NewProgressiveCommissionUc(
			&u.cfg,
			u.repo.Account(),
			u.repo.Stt(),
			u.repo.PartnerLog(),
			u.repo.Time(),
			u.repo.ProgressiveCommissionReport(),
			u.repo.Partner(),
			u.cfg.PUBSUB(),
			u.repo.MiddlewareClient(),
			u.repo.PartnerLog(),
			u.repo.Slack(),
		)
	})

	return progressiveCommission
}

func (u *usecaseRegistry) ProgressiveCommissionHandler() message_broker.ProgressiveCommissionHandler {
	var ucProgressiveCommissionHandler message_broker.ProgressiveCommissionHandler
	var once sync.Once
	once.Do(func() {
		ucProgressiveCommissionHandler = message_broker.NewProgressiveCommissionHandler(
			u.repo.PartnerLog(),
			u.ProgressiveCommission(),
			u.cfg,
		)
	})
	return ucProgressiveCommissionHandler
}
func (u *usecaseRegistry) CustomProcessRole() usecase.CustomProcessRole {
	var customProcessRole usecase.CustomProcessRole
	var once sync.Once

	once.Do(func() {
		customProcessRole = usecase.NewCustomProcessRole(
			u.repo.CustomProcessRole(),
		)
	})

	return customProcessRole
}

func (u *usecaseRegistry) ReadSttPaid() usecase.ReadSttPaid {
	var ucReadSttPaid usecase.ReadSttPaid
	var once sync.Once
	once.Do(func() {
		ucReadSttPaid = usecase.NewReadSttPaidUc(
			u.repo.ReadSttPaid(),
			u.repo.Stt(),
			u.repo.Account(),
		)
	})
	return ucReadSttPaid
}

func (u *usecaseRegistry) RebuttalDex() usecase.RebuttalDex {
	var ucRebuttalDex usecase.RebuttalDex
	var once sync.Once
	once.Do(func() {
		ucRebuttalDex = usecase.NewRebuttalDexUc(
			u.repo.RebuttalDex(),
		)
	})
	return ucRebuttalDex
}

func (u *usecaseRegistry) Webhook() usecase.Webhook {
	var ucWebhook usecase.Webhook
	var once sync.Once
	once.Do(func() {
		ucWebhook = usecase.NewWebhookUc(
			&u.cfg,
			u.repo.PartnerLog(),
			u.repo.RetryPubsubRepo(),
			u.repo.PosAffiliate(),
			u.repo.Partner(),
			u.repo.PartnerLog(),
			u.repo.Time(),
			u.repo.CacheRepository(),
			u.repo.Account(),
			u.repo.ReadyToCargo(),
			u.repo.CargoConfiguration(),
			u.repo.BaggingReadyToCargo(),
			u.repo.BaggingReadyToCargoHistory(),
			u.repo.SttReadyToCargoHistoryRepo(),
			u.repo.CutOffReadyToCargoRepo(),
			u.repo.SttReadyToCargoRepo(),
			u.repo.RtcCityGroupRepository(),
			u.repo.City(),
			u.repo.Bag(),
			u.repo.Stt(),
			u.repo.CargoReserve(),
			u.repo.RetryCargo(),
			u.repo.Cargo(),
			u.RetryCargo(),
			u.Firebase(),
			u.SttActivity(),
			u.GatewaySttStatus(),
			u.repo.Account(),
			u.repo.Client(),
			u.repo.Airport(),
			u.repo.LogRtcRepository(),
			u.ReadyToCargo(),
			u.SttDueUC(),
			u.repo.SttDueRepository(),
		)
	})
	return ucWebhook
}

// reportV3 ...
func (u usecaseRegistry) ReportV4() usecase.ReportV4 {
	var res usecase.ReportV4
	var once sync.Once

	once.Do(func() {
		res = usecase.NewReportV4Uc(
			&u.cfg,
			u.repo.Stt(),
			u.repo.Client(),
			u.repo.Partner(),
			u.repo.City(),
			u.repo.District(),
			u.repo.BulkDownload(),
			u.repo.SttReport(),
			u.repo.Storage(),
			u.repo.Xlsx(),
			u.repo.Time(),
			u.repo.FlagManagementRepository(),
			u.repo.SttReportPostgresRepository(),
		)
	})

	return res
}

func (u *usecaseRegistry) Luwjistik() usecase.Luwjistik {
	var ucLuwjistik usecase.Luwjistik
	var once sync.Once
	once.Do(func() {
		ucLuwjistik = usecase.NewLuwjistikUc(
			&u.cfg,
			u.repo.Luwjistik(),
			u.repo.RetryLuwjistik(),
			u.repo.SttVendor(),
			u.repo.PartnerLog(),
			u.repo.District(),
			u.repo.Commodity(),
			u.repo.Stt(),
			u.repo.Account(),
			u.repo.Delivery(),
			u.repo.City(),
			u.GatewaySttStatus(),
			u.repo.CustomProcess(),
			u.SttActivity(),
			u.repo.Reason(),
			u.repo.Pod(),
			u.repo.MiddlewareClient(),
			u.repo.SttPieceHistory(),
			u.repo.Time(),
			u.repo.Partner(),
			u.repo.RouteRepository(),
			u.repo.StiDest(),
			u.GatewaySttStatus(),
			u.repo.SttPiece(),
			u.repo.ExchangeRate(),
			u.MessageGateway(),
			u.repo.Handover(),
			u.repo.SttPieceHistory(),
			u.repo.HandoverDetail(),
			u.repo.SttPieceHistory(),
			u.repo.StiDetail(),
			u.repo.Sti(),
			u.repo.ReasonVendorMapping(),
			u.ReadyToCargo(),
			u.Salesforce(),
			u.RequestPriorityDelivery(),
			u.repo.SttDueRepository(),
		)
	})
	return ucLuwjistik
}

func (u *usecaseRegistry) CorporateDashboard() usecase.CorporateDashboard {
	var corporateDashboardUc usecase.CorporateDashboard
	var once sync.Once
	once.Do(func() {
		corporateDashboardUc = usecase.NewCorporateDashboardUc(
			u.repo.Client(),
			u.repo.CorporateDashboardRepo(),
			u.repo.Product(),
			&u.cfg,
			u.repo.Account(),
		)
	})

	return corporateDashboardUc
}

// Ninja ...
func (u *usecaseRegistry) NinjaV2() usecase.NinjaV2 {
	var ninjaV2 usecase.NinjaV2
	var once sync.Once

	once.Do(func() {
		ninjaV2 = usecase.NewNinjaV2Uc(
			u.repo.PartnerLog(),
			u.repo.Stt(),
			u.repo.SttPieceHistory(),
			u.GatewaySttStatus(),
			u.repo.City(),
			u.repo.Account(),
			u.repo.Delivery(),
			u.repo.Pod(),
			u.MessageGateway(),
			u.repo.Time(),
			u.repo.Reason(),
			u.repo.ReasonVendorMapping(),
			u.ReadyToCargo(),
			u.Salesforce(),
			u.RequestPriorityDelivery(),
			u.repo.SttDueRepository(),
		)
	})

	return ninjaV2
}

func (u *usecaseRegistry) ClaimStatus() usecase.ClaimStatus {
	var claimStatusUc usecase.ClaimStatus
	var once sync.Once
	once.Do(func() {
		claimStatusUc = usecase.NewClaimStatusUc(
			u.repo.Stt(),
			u.repo.SttOptionalRate(),
			u.repo.AlgoRepo(),
			u.repo.ClaimHistoryRepo(),
			&u.cfg,
			u.repo.CorporateDashboardRepo(),
			u.repo.Client(),
		)
	})

	return claimStatusUc
}

func (u *usecaseRegistry) HistoryManifest() usecase.HistoryManifest {
	var uc usecase.HistoryManifest
	var once sync.Once
	once.Do(func() {
		uc = usecase.NewHistoryManifestUc(
			repository.NewHistoryManifestRepository(
				&u.cfg,
			),
			repository.NewTimeRepository(),
		)
	})

	return uc
}

func (u *usecaseRegistry) RetryCargo() usecase.RetryCargo {
	var retryCargoUC usecase.RetryCargo
	var once sync.Once
	once.Do(func() {
		retryCargoUC = usecase.NewRetryCargoUc(
			u.repo.Ngen(),
			u.repo.RetryCargo(),
			u.repo.Cargo(),
			u.repo.Partner(),
			u.repo.ReadCargo(),
			&u.cfg,
			u.repo.City(),
			u.repo.CommodityGroup(),
			u.repo.PartnerLog(),
			usecase.NewCargoReserve(
				&u.cfg, u.repo.CargoReserve(),
				shared.NewDictionaryError(),
				u.repo.CacheRepository(),
				u.repo.Account(),
				u.repo.PartnerLog(),
				u.repo.Partner(),
				u.repo.Time(),
			),
			u.repo.CargoReserve(),
			u.repo.CacheRepository(),
			u.repo.Account(),
			shared.NewDictionaryError(),
			u.GatewaySttStatus(),
			u.SttActivity(),
			u.repo.Stt(),
			u.repo.Time(),
			u.Firebase(),
			u.repo.Bag(),
			u.repo.Shipment(),
			u.ReadyToCargo(),
			u.SttDueUC(),
			u.repo.SttDueRepository(),
			u.cfg.PUBSUB(),
			u.repo.ReadyToCargoHistory(),
			u.repo.Commodity(),
		)
	})

	return retryCargoUC
}

func (u *usecaseRegistry) ReadyToCargo() usecase.ReadyToCargo {
	var readyToCargoUC usecase.ReadyToCargo
	var once sync.Once
	once.Do(func() {
		readyToCargoUC = usecase.NewReadyToCargoUc(
			u.repo.ReadyToCargo(),
			u.repo.BaggingReadyToCargo(),
			u.repo.Commodity(),
			u.repo.CommodityGroup(),
			u.repo.BaggingGroupLocation(),
			u.repo.Bag(),
			&u.cfg,
			u.repo.PartnerLog(),
			u.repo.Partner(),
			u.repo.BagCargo(),
			u.repo.Stt(),
			u.repo.SttReadyToCargoRepo(),
			u.repo.BagCommodityGroup(),
			u.repo.Product(),
			u.repo.CutOffReadyToCargoRepo(),
			u.repo.Hub(),
			shared.NewDictionaryError(),
			u.repo.Time(),
			u.repo.CacheRepository(),
			u.repo.Shipment(),
			u.repo.BagVendorRepo(),
			u.repo.Account(),
			u.repo.LogRtcRepository(),
			u.repo.SttAssessmentRepository(),
		)
	})

	return readyToCargoUC
}

func (u *usecaseRegistry) SttPromo() usecase.SttPromo {
	var sttPromoUc usecase.SttPromo
	var once sync.Once
	once.Do(func() {
		sttPromoUc = usecase.NewSttPromoUc(
			u.repo.Stt(),
			u.repo.PartnerLog(),
			&u.cfg,
		)
	})

	return sttPromoUc
}

func (u *usecaseRegistry) GatewayCargo() usecase.GatewayCargo {
	var GatewayCargoUc usecase.GatewayCargo
	var once sync.Once
	once.Do(func() {
		GatewayCargoUc = usecase.NewGatewayCargoUc(
			&u.cfg,
			u.repo.Cargo(),
			u.repo.City(),
			u.repo.District(),
			u.repo.Stt(),
			u.repo.Commodity(),
			u.repo.CommodityGroup(),
			u.repo.Bag(),
			u.repo.CargoFlight(),
			u.repo.BagVendorRepo(),
			u.repo.Partner(),
			u.repo.CargoDetail(),
		)
	})

	return GatewayCargoUc
}

func (u *usecaseRegistry) CargoReserve() usecase.CargoReserve {
	var uc usecase.CargoReserve
	var once sync.Once
	once.Do(func() {
		uc = usecase.NewCargoReserve(
			&u.cfg,
			u.repo.CargoReserve(),
			shared.NewDictionaryError(),
			u.repo.CacheRepository(),
			u.repo.Account(),
			u.repo.PartnerLog(),
			u.repo.Partner(),
			u.repo.Time(),
		)
	})

	return uc
}

func (u *usecaseRegistry) StoSc() usecase.StoSc {
	var ucStoSc usecase.StoSc
	var once sync.Once
	once.Do(func() {
		ucStoSc = usecase.NewStoScUc(
			&u.cfg,
			u.repo.StoSc(),
			u.repo.Partner(),
			u.repo.SttPiece(),
			u.repo.City(),
			u.repo.Sti(),
			u.repo.Stt(),
			u.repo.SttPieceHistory(),
			u.repo.Time(),
			u.GatewaySttStatus(),
		)
	})
	return ucStoSc
}

func (u *usecaseRegistry) ResolutionCentre() usecase.ResolutionCentre {
	var rcUsecase usecase.ResolutionCentre
	var once sync.Once
	once.Do(func() {
		rcUsecase = usecase.NewResolutionCentreUc(
			&u.cfg,
			u.repo.CaseCategory(),
			u.repo.ResolutionCentre(),
			u.repo.DiscussionForumRepository(),
			u.repo.Stt(),
			u.repo.Shipment(),
			u.repo.SalesforceRepository(),
			u.repo.Partner(),
			u.repo.Account(),
			u.repo.CacheRepository(),
			u.repo.Time(),
			u.repo.District(),
			u.repo.FlagManagementRepository(),
			u.Algo(),
		)
	})
	return rcUsecase
}

func (u *usecaseRegistry) DiscussionForum() usecase.DiscussionForum {
	var dfUsecase usecase.DiscussionForum
	var once sync.Once
	once.Do(func() {
		dfUsecase = usecase.NewDiscussionForumUc(
			&u.cfg,
			u.repo.ResolutionCentre(),
			u.repo.DiscussionForumRepository(),
			u.repo.SalesforceRepository(),
			u.repo.Account(),
			u.repo.Time(),
			u.repo.FlagManagementRepository(),
		)
	})
	return dfUsecase
}

func (u *usecaseRegistry) Salesforce() usecase.Salesforce {
	var sfUsecase usecase.Salesforce
	var once sync.Once
	once.Do(func() {
		sfUsecase = usecase.NewSalesforceUc(
			&u.cfg,
			u.repo.ResolutionCentre(),
			u.repo.DiscussionForumRepository(),
			u.repo.Time(),
			u.repo.PartnerLog(),
			u.repo.Account(),
			u.repo.SalesforceRepository(),
			u.repo.Stt(),
			u.repo.Delivery(),
			u.repo.Reason(),
			u.repo.PriorityDeliveryRepository(),
			u.repo.Client(),
			u.repo.SttAssessmentRepository(),
			u.cfg.PUBSUB(),
			u.repo.SttPiece(),
			u.repo.SttPieceHistory(),
		)
	})
	return sfUsecase
}

func (u *usecaseRegistry) Firebase() usecase.FirebaseUcase {
	var FirebaseUcase usecase.FirebaseUcase
	var once sync.Once
	once.Do(func() {
		FirebaseUcase = usecase.NewFirebaseUcase(
			&u.cfg,
			u.repo.FirebaseRepository(),
			u.repo.PartnerLog(),
			u.repo.Account(),
			u.repo.ReadCargo(),
			u.repo.Time(),
		)
	})
	return FirebaseUcase
}

func (u *usecaseRegistry) NotificationPenalty() usecase.NotificationPenalty {
	var ucNotif usecase.NotificationPenalty
	var once sync.Once
	once.Do(func() {
		ucNotif = usecase.NewNotificationPenaltyUc(
			&u.cfg,
			u.repo.NotificationPenalty(),
			u.repo.PartnerLog(),
			u.repo.Time(),
		)
	})
	return ucNotif
}

func (u *usecaseRegistry) DeliveryPic() usecase.DeliveryPic {
	var dpUsecase usecase.DeliveryPic
	var once sync.Once
	once.Do(func() {
		dpUsecase = usecase.NewDeliveryPicUc(
			&u.cfg,
			u.repo.DeliveryPICRepository(),
			u.repo.Account(),
			u.repo.Time(),
		)
	})
	return dpUsecase
}

func (u *usecaseRegistry) HoldBalanceHistory() usecase.HoldBalanceHistory {
	var dpUsecase usecase.HoldBalanceHistory
	var once sync.Once
	once.Do(func() {
		dpUsecase = usecase.NewHoldBalanceHistoryUc(
			&u.cfg,
			u.repo.HoldBalanceHistory(),
		)
	})
	return dpUsecase
}

func (u *usecaseRegistry) Upload() usecase.Upload {
	var dpUsecase usecase.Upload
	var once sync.Once
	once.Do(func() {
		dpUsecase = usecase.NewUploadUc(
			&u.cfg,
			u.repo.UploadRepository(),
		)
	})
	return dpUsecase
}

func (u *usecaseRegistry) LogRtc() usecase.LogRtc {
	var dpUsecase usecase.LogRtc
	var once sync.Once
	once.Do(func() {
		dpUsecase = usecase.NewLogRtcUc(
			&u.cfg,
			u.repo.LogRtcRepository(),
			u.repo.CacheRepository(),
			u.repo.Time(),
		)
	})
	return dpUsecase
}

func (u *usecaseRegistry) DexAssessment() usecase.DexAssessment {
	var dexAssessmentUc usecase.DexAssessment
	var once sync.Once

	once.Do(func() {
		dexAssessmentUc = usecase.NewDexAssessmentUc(
			u.repo.City(),
			u.repo.CacheRepository(),
			u.repo.Account(),
			u.repo.DexAssessmentRepo(),
			u.repo.Time(),
			u.repo.Stt(),
			u.repo.DexFakeDexConfiguration(),
			u.repo.PartnerLog(),
			u.repo.PredefinedHoliday(),
			u.repo.Xlsx(),
			u.repo.FlagManagementRepository(),
			u.repo.BulkDownload(),
			u.repo.Storage(),
			u.repo.Delivery(),
			u.repo.RebuttalDex(),
			u.GatewaySttStatus(),
			&u.cfg,
		)
	})

	return dexAssessmentUc
}

func (u *usecaseRegistry) Shortlink() usecase.Shortlink {
	var dpUsecase usecase.Shortlink
	var once sync.Once
	once.Do(func() {
		dpUsecase = usecase.NewShortlinkUc(
			&u.cfg,
			u.repo.ShortlinkRepository(),
			u.repo.Time(),
			u.repo.Delivery(),
		)
	})
	return dpUsecase
}

func (u *usecaseRegistry) DexAssessmentDashboard() usecase.DexAssessmentDashboard {
	var dexAssessmentDashboardUc usecase.DexAssessmentDashboard
	var once sync.Once

	once.Do(func() {
		dexAssessmentDashboardUc = usecase.NewDexAssessmentDashboardUc(
			u.repo.CacheRepository(),
			u.repo.Account(),
			u.repo.DexAssessmentDashboardRepo(),
			u.repo.DexAssessmentDashboardV2Repo(),
			u.repo.Time(),
			u.repo.Stt(),
			u.repo.DexFakeDexConfiguration(),
			u.repo.PartnerLog(),
			u.repo.PredefinedHoliday(),
			u.repo.Xlsx(),
			u.repo.FlagManagementRepository(),
			u.repo.BulkDownload(),
			u.repo.Storage(),
			u.repo.Delivery(),
			u.repo.RebuttalDex(),
			u.GatewaySttStatus(),
			&u.cfg,
			u.repo.DexAssessmentRepo(),
			u.repo.CsAccountLogRepo(),
			u.repo.DexAssessmentDashboardConsolidatorRepository(),
		)
	})

	return dexAssessmentDashboardUc
}

func (u *usecaseRegistry) Clearance() usecase.Clearance {
	var clearanceUc usecase.Clearance
	var once sync.Once

	once.Do(func() {
		clearanceUc = usecase.NewClearanceUc(
			&u.cfg,
			u.repo.ClearanceRepo(),
			u.repo.Bag(),
			shared.NewDictionaryError(),
			u.repo.Stt(),
			u.repo.Time(),
			u.repo.Commodity(),
		)
	})

	return clearanceUc
}

func (u *usecaseRegistry) Release() usecase.Release {
	var releaseUsecase usecase.Release
	var once sync.Once
	once.Do(func() {
		releaseUsecase = usecase.NewReleaseUc(
			&u.cfg,
			u.repo.ReleaseRepository(),
			u.repo.ReleaseDetailRepository(),
			u.repo.Bag(),
			shared.NewDictionaryError(),
			u.repo.Stt(),
			u.repo.Time(),
			u.repo.Commodity(),
		)
	})
	return releaseUsecase
}

func (u *usecaseRegistry) Algo() usecase.Algo {
	var algoUsecase usecase.Algo
	var once sync.Once
	once.Do(func() {
		algoUsecase = usecase.NewAlgoUc(
			u.repo.AlgoRepo(),
		)
	})
	return algoUsecase
}

func (u *usecaseRegistry) PickupManifestCbp() usecase.PickupManifestCbp {
	var pickupManifestCbpUsecase usecase.PickupManifestCbp
	var once sync.Once
	once.Do(func() {
		pickupManifestCbpUsecase = usecase.NewPickupManifestCbpUc(
			&u.cfg,
			u.repo.PickupManifestCbpRepo(),
			u.repo.Client(),
			u.repo.Partner(),
		)
	})
	return pickupManifestCbpUsecase
}

func (u *usecaseRegistry) PriorityManagementDelivery() usecase.PriorityDeliveryManagement {
	var priorityManagementDeliveryUsecase usecase.PriorityDeliveryManagement
	var once sync.Once
	once.Do(func() {
		priorityManagementDeliveryUsecase = usecase.NewPriorityDeliveryManagementUc(
			&u.cfg,
			u.repo.PriorityDeliveryRepository(),
			u.repo.Delivery(),
			u.repo.Stt(),
			u.repo.Partner(),
			u.repo.BulkDownload(),
			u.repo.Time(),
			u.repo.FlagManagementRepository(),
			u.repo.Xlsx(),
		)
	})
	return priorityManagementDeliveryUsecase
}

func (u *usecaseRegistry) RequestPriorityDelivery() usecase.RequestPriorityDelivery {
	var requestPriorityDeliveryUsecase usecase.RequestPriorityDelivery
	var once sync.Once
	once.Do(func() {
		requestPriorityDeliveryUsecase = usecase.NewRequestPriorityDeliveryUc(
			&u.cfg,
			u.repo.RequestPriorityDeliveryRepository(),
			u.repo.BulkDownload(),
			u.repo.Time(),
			u.repo.Xlsx(),
			u.repo.FlagManagementRepository(),
			u.repo.Storage(),
			u.repo.PriorityDeliveryRepository(),
			u.repo.Stt(),
			u.repo.Reason(),
			u.repo.Delivery(),
			u.repo.PartnerLog(),
			u.GatewayStt(),
		)
	})
	return requestPriorityDeliveryUsecase
}

func (u *usecaseRegistry) PickupCorporate() usecase.PickupCorporate {
	var pickupCorporateUC usecase.PickupCorporate
	var once sync.Once
	once.Do(func() {
		pickupCorporateUC = usecase.NewPickupCorporateSrv(
			usecase.SetPickupCorporateCfg(&u.cfg),
			usecase.SetPickupCorporateRepo(u.repo.PickupCorporateRepository()),
			usecase.SetPickupCorporateClientRepo(u.repo.Client()),
			usecase.SetPickupCorporateAccountRepo(u.repo.Account()),
			usecase.SetPickupCorporateAlgoRepo(u.repo.AlgoRepo()),
			usecase.SetPickupCorporateCityRepo(u.repo.City()),
			usecase.SetPickupCorpAddressRepo(u.repo.PickupCorporateAddress()),
		)
	})

	return pickupCorporateUC
}

func (u *usecaseRegistry) SttDueUC() usecase.STTDue {
	var sttDueUC usecase.STTDue
	var once sync.Once
	once.Do(func() {
		sttDueUC = usecase.NewSTTDueUseCase(
			usecase.SetSTTDueUseCaseConfig(u.cfg),
			usecase.SetSTTDueUseCaseRepo(u.repo.SttDueRepository()),
			usecase.SetSTTDueUsecaseCacheRepo(u.repo.CacheRepository()),
			usecase.SetSTTDueUsecaseCargoDetailRepo(u.repo.CargoDetail()),
			usecase.SetSTTDueUsecasePartnerLocationRepo(u.repo.PartnerLocation()),
			usecase.SetSTTDueUsecasePartnerRepo(u.repo.Partner()),
			usecase.SetSTTDueUsecaseSttRepo(u.repo.Stt()),
			usecase.SetSTTDueUsecasePartnerLogRepo(u.repo.PartnerLog()),
			usecase.SetSTTDueUsecaseCargoRepo(u.repo.Cargo()),
			usecase.SetSTTDueUsecaseCargoFlightRepo(u.repo.CargoFlight()),
			usecase.SetSTTDueUsecaseCityRepo(u.repo.City()),
			usecase.SetSTTDueUsecaseSttPiecesRepo(u.repo.SttPiece()),
			usecase.SetSTTDueUsecaseSttPieceHistoryRepo(u.repo.SttPieceHistory()),
			usecase.SetSTTDueUsecaseAccountRepo(u.repo.Account()),
		)
	})
	return sttDueUC
}

func (u *usecaseRegistry) PtPosUC() usecase.PtPos {
	var ptPosUC usecase.PtPos
	var once sync.Once
	once.Do(func() {
		ptPosUC = usecase.NewPtPosUsecase(
			usecase.SetPtPosUsecaseConfig(u.cfg),
			usecase.SetPtPosUsecasePtPosRepo(u.repo.PtPos()),
			usecase.SetPtPosUsecasePartnerLogRepo(u.repo.PartnerLog()),
			usecase.SetPtPosUsecaseDistrictRepo(u.repo.District()),
			usecase.SetPtPosUsecaseSttPiecesRepo(u.repo.SttPiece()),
			usecase.SetPtPosUsecaseSttVendorRepo(u.repo.SttVendor()),
			usecase.SetPtPosUsecasePartnerRepo(u.repo.Partner()),
			usecase.SetPtposSttRepo(u.repo.Stt()),
			usecase.SetPtposSttHistoryRepo(u.repo.SttPieceHistory()),
			usecase.SetPtposPartnerLogRepo(u.repo.PartnerLog()),
			usecase.SetSPtposttDueRepo(u.repo.SttDueRepository()),
			usecase.SetPtposTimeRepository(u.repo.Time()),
			usecase.SetPtposRepoCity(u.repo.City()),
			usecase.SetPtposRepoAccount(u.repo.Account()),
			usecase.SetPtposGatewaySttStatus(u.GatewaySttStatus()),
			usecase.SetPtposDeliveryRepo(u.repo.Delivery()),
			usecase.SetPtposPodRepo(u.repo.Pod()),
			usecase.SetPtposMessageGatewayUc(u.MessageGateway()),
			usecase.SetPtposRtcUc(u.ReadyToCargo()),
			usecase.SetPtposSalesforce(u.Salesforce()),
			usecase.SetPtposReasonVendorRepo(u.repo.ReasonVendorMapping()),
			usecase.SetPtposReasonRepo(u.repo.Reason()),
			usecase.SetPtposRequestPriorityDeliveryUsecase(u.RequestPriorityDelivery()),
			usecase.SetPtposPtposRequestRepo(u.repo.PtposRequestRepository()),
			usecase.SetPtposCacheRepo(u.repo.CacheRepository()),
		)
	})
	return ptPosUC
}

func (u *usecaseRegistry) CargoSearchFlight() usecase.CargoSearchFlightUC {
	var (
		once  sync.Once
		csfUC usecase.CargoSearchFlightUC
	)

	once.Do(func() {
		csfUC = usecase.NewCargoSearchFlightUC(
			usecase.SetCargoSearchFlightUCCfg(&u.cfg),
			usecase.SetCargoSearchFlightUCCargoFlightRepo(u.repo.CargoConfigurationSearchFlight()),
			usecase.SetCargoSearchFlightUCCityRepo(u.repo.City()),
			usecase.SetCargoSearchFlightUCSabreRepo(u.repo.SabreRepository()),
			usecase.SetCargoSearchFlightUCPartnerRepo(u.repo.Partner()),
			usecase.SetCargoSearchFlightUCPartnerLogRepo(u.repo.PartnerLog()),
			usecase.SetCargoSearchFlightUCNgenRepo(u.repo.Ngen()),
		)
	})
	return csfUC
}

func (u *usecaseRegistry) RiskClassification() risk_classification.RiskClassification {
	var riskClassificationUc risk_classification.RiskClassification
	var once sync.Once

	once.Do(func() {
		riskClassificationUc = risk_classification.NewRiskClassificationUc(
			risk_classification.RiskClassificationCtx{
				Cfg:                    &u.cfg,
				RiskClassificationRepo: u.repo.RiskClassificationRepository(),
				TimeRepo:               u.repo.Time(),
				FlagManagementRepo:     u.repo.FlagManagementRepository(),
				PartnerLogRepo:         u.repo.PartnerLog(),
			},
		)
	})

	return riskClassificationUc
}

func (u *usecaseRegistry) Dispatch() usecase.DispatchUC {
	var (
		once sync.Once
		dUC  usecase.DispatchUC
	)

	once.Do(func() {
		dUC = usecase.NewDispatchUC(
			usecase.SetDispatchUcConfig(&u.cfg),
			usecase.SetDispatchUcDispatchRepo(u.repo.DispatchRepository()),
			usecase.SetDispatchUcDeliveryManifestRepo(u.repo.DeliveryManifest()),
			usecase.SetDispatchUcSttRepo(u.repo.Stt()),
			usecase.SetDispatchUcSttHistoryRepo(u.repo.SttPieceHistory()),
			usecase.SetDispatchUcGatewaySttStatus(u.GatewaySttStatus()),
			usecase.SetDispatchUcPartnerRepo(u.repo.Partner()),
			usecase.SetDispatchUcCityRepo(u.repo.City()),
			usecase.SetDispatchUcDistrictRepo(u.repo.District()),
			usecase.SetDispatchUcSttActivityUc(u.SttActivity()),
			usecase.SetDispatchUcMiddlewareRepo(u.repo.MiddlewareClient()),
			usecase.SetDispatchUcHubRepo(u.repo.Hub()),
			usecase.SetDispatchUcAccountRepo(u.repo.Account()),
			usecase.SetDispatchUcRtcUc(u.ReadyToCargo()),
		)
	})
	return dUC
}

func (u *usecaseRegistry) Kulioner() usecase.Kulioner {
	var kulioner usecase.Kulioner
	var once sync.Once
	once.Do(func() {
		kulioner = usecase.NewKulionerUc(
			&u.cfg,
			u.repo.AlgoPosRepo(),
			u.repo.Partner(),
			u.repo.Time(),
			u.repo.BulkDownload(),
			u.repo.Partner(),
			u.repo.FlagManagementRepository(),
			u.repo.PartnerLog(),
		)
	})

	return kulioner
}

func (u *usecaseRegistry) QuoteGroup() usecase.QuoteGroupUC {
	var (
		once sync.Once
		qgUC usecase.QuoteGroupUC
	)
	once.Do(func() {
		qgUC = usecase.NewQuoteGroupUc(
			u.repo.QuoteGroupRepository(),
			u.repo.Time(),
			u.repo.CampaignSttQuoteRepository(),
		)
	})
	return qgUC
}
