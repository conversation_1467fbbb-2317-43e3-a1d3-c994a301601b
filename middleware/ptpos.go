package middleware

import (
	"bytes"
	"encoding/json"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/labstack/echo"
	"io"
	"io/ioutil"
	"net/http"
	"os"
)

type (
	PTPOSMiddlewareRequest struct {
		Body   string
		Header map[string][]string
	}
)

// PTPOSVerify ...
func PTPOSVerify() echo.MiddlewareFunc {
	return func(next echo.HandlerFunc) echo.HandlerFunc {
		return func(c echo.Context) error {

			ptposBasicAuth := os.Getenv("PTPOS_BASIC_AUTH")
			secretAuthPTPOS := BasicAuth + ptposBasicAuth

			req := c.Request()
			header := req.Header
			auth := header.Get("Authorization")
			if auth == `` {
				return echo.NewHTTPError(http.StatusUnauthorized, "authorization is required")
			}

			if req.Body == nil {
				return echo.NewHTTPError(http.StatusBadRequest, "Request body is empty")
			}

			bodyInBytes, err := ioutil.ReadAll(req.Body)
			if err != nil {
				return echo.NewHTTPError(http.StatusBadRequest, "Request body is invalid")
			}

			generalReq := new(PTPOSMiddlewareRequest)
			if err := json.Unmarshal(bodyInBytes, generalReq); err != nil {
				logger.Ef("PTPOSMiddleware-Error Unmarshal %v", err)
			}

			generalReq.Body = string(bodyInBytes)
			generalReq.Header = header

			// Restore the io.ReadCloser to its original state
			req.Body = io.NopCloser(bytes.NewBuffer(bodyInBytes))

			if secretAuthPTPOS != auth {
				logger.Ef("PTPOSMiddleware-Error Auth %v", secretAuthPTPOS)
				return echo.NewHTTPError(http.StatusUnauthorized, "authorization is invalid")
			}

			return next(c)
		}
	}
}
