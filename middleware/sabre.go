package middleware

import (
	"bytes"
	"compress/gzip"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/Lionparcel/hydra/shared/json"
	"github.com/labstack/echo"
)

type (
	HydraResponseError struct {
		XMLName xml.Name `xml:"soap-env:Envelope" json:"-"`
		XMLNS   string   `xml:"xmlns:soap-env,attr" json:"xmlns"`
		Header  struct {
			XMLNS  string `xml:"xmlns:wsa,attr" json:"-"`
			Action string `xml:"wsa:Action" json:"action"`
		} `xml:"soap-env:Header" json:"header"`
		Body struct {
			Fault struct {
				FaultCode   string `xml:"faultcode" json:"fault_code"`
				FaultString string `xml:"faultstring" json:"fault_string"`
				Detail      string `xml:"detail>StackTrace" json:"detail"`
			} `xml:"soap-env:Fault" json:"fault"`
		} `xml:"soap-env:Body" json:"body"`
	}
)

func (e *HydraResponseError) Error() string {
	return e.Body.Fault.FaultString
}

func (e *HydraResponseError) SetCode(code string) *HydraResponseError {
	e.Body.Fault.FaultCode = code
	return e
}

func (e *HydraResponseError) SetMessage(msg string) *HydraResponseError {
	e.Body.Fault.FaultString = msg
	return e
}

func (e *HydraResponseError) SetDetail(detail string) *HydraResponseError {
	e.Body.Fault.Detail = detail
	return e
}

func (e *HydraResponseError) SetActionHeader(action string) *HydraResponseError {
	e.Header.Action = action
	return e
}

func (e *HydraResponseError) ToXML() []byte {
	res, _ := xml.Marshal(e)
	return res
}

func (e *HydraResponseError) ToJson() []byte {
	res, _ := json.Marshal(e)
	return res
}

// RequestProxy creates a middleware that proxies requests to a target URL.
// target: the base URL to proxy requests to.
// action: the action path prefix to strip from incoming requests.
// timeout: maximum time in seconds to wait for the proxied request.
func RequestProxy(target, action string, timeout int) echo.HandlerFunc {
	return func(c echo.Context) error {
		errV := &HydraResponseError{}
		errV.Header.Action = action

		urlTarget, err := url.Parse(target + c.Request().URL.Path[len(action):])
		if err != nil {
			errV.SetCode(fmt.Sprint(http.StatusBadRequest)).SetMessage("Invalid target URL")
			return responseProxy(c, http.StatusBadRequest, errV)
		}

		req, err := http.NewRequest(c.Request().Method, urlTarget.String(), c.Request().Body)
		if err != nil {
			errV.SetCode(fmt.Sprint(http.StatusInternalServerError)).SetMessage("Failed to create request")
			return responseProxy(c, http.StatusInternalServerError, errV)
		}

		for key, values := range c.Request().Header {
			for _, value := range values {
				req.Header.Add(key, value)
			}
		}

		client := &http.Client{Timeout: time.Duration(timeout) * time.Second}
		resp, err := client.Do(req)
		if err != nil {
			return responseProxy(c, http.StatusInternalServerError, errV.SetCode(fmt.Sprint(http.StatusInternalServerError)).SetMessage("Failed to send request"))
		}

		// Periksa Content-Encoding
		contentEncoding := resp.Header.Get("Content-Encoding")

		var reader io.ReadCloser
		switch contentEncoding {
		case "gzip":
			reader, err = gzip.NewReader(resp.Body)
			if err != nil {
				return responseProxy(c, http.StatusInternalServerError, errV.SetCode(fmt.Sprint(http.StatusInternalServerError)).SetMessage("Failed to decompress response"))
			}
			defer reader.Close()
		default:
			reader = resp.Body
		}

		buff := new(bytes.Buffer)
		buff.ReadFrom(reader)

		return c.Blob(resp.StatusCode, req.Header.Get("Content-Type"), buff.Bytes())
	}
}

func responseProxy(c echo.Context, statusCode int, message interface{}) error {
	// Check the Accept header for "xml" (you might also inspect the Content-Type header)
	if strings.Contains(c.Request().Header.Get("Accept"), "xml") {
		return c.XML(statusCode, message)
	}
	return c.JSON(statusCode, message)
}
