ALTER TABLE ready_to_cargo ADD COLUMN rtc_cc_max_total_piece INT NOT NULL AFTER rtc_status;
ALTER TABLE ready_to_cargo ADD COLUMN rtc_total_piece INT NOT NULL AFTER rtc_cc_max_total_piece;
ALTER TABLE ready_to_cargo ADD COLUMN rtc_total_stt INT NOT NULL AFTER rtc_total_piece;
ALTER TABLE ready_to_cargo ADD COLUMN rtc_cc_product_type TEXT NOT NULL AFTER rtc_total_stt;
ALTER TABLE ready_to_cargo ADD COLUMN rtc_cc_comodity_group TEXT NOT NULL AFTER rtc_cc_product_type;
ALTER TABLE ready_to_cargo ADD COLUMN rtc_cc_transport varchar(20) NOT NULL AFTER rtc_cc_comodity_group;
ALTER TABLE ready_to_cargo ADD COLUMN rtc_cc_flight TEXT NOT NULL AFTER rtc_cc_transport;