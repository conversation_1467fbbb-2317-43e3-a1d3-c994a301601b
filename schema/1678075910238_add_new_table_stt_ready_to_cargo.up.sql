CREATE TABLE IF NOT EXISTS stt_ready_to_cargo(
    `srtc_id` int(11) unsigned NOT NULL AUTO_INCREMENT,
    `srtc_ready_to_cargo_id` int(11) unsigned NOT NULL DEFAULT '0',
    `srtc_stt_id` int(11) unsigned NOT NULL DEFAULT '0',
    `srtc_status` enum('active','inactive') NOT NULL DEFAULT 'active',
    PRIMARY KEY (`srtc_id`),
    KEY `idx_srtc_id` (`srtc_id`),
    KEY `idx_srtc_ready_to_cargo_id` (`srtc_ready_to_cargo_id`),
    KEY `idx_srtc_stt_id` (`srtc_stt_id`),
    KEY `idx_srtc_status` (`srtc_status`)
)   ENGINE=InnoDB DEFAULT CHARSET=utf8;