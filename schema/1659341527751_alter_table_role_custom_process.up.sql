DROP TABLE IF EXISTS `role_custom_process`;
CREATE TABLE `role_custom_process` (
  `rcp_id` int NOT NULL AUTO_INCREMENT,
  `rcp_status` varchar(100) NOT NULL,
  `rcp_account_type` varchar(50) NOT NULL,
  `rcp_role_type` varchar(150) NOT NULL,
  PRIMARY KEY (`rcp_id`)
);

INSERT INTO `role_custom_process` (`rcp_id`, `rcp_status`, `rcp_account_type`, `rcp_role_type`) VALUES
('1', 'CLAIM', 'internal', 'Customer Care'),
('2', 'CI', 'internal', 'Customer Care'),
('3', 'MISSING', 'internal', 'OCT'),
('4', 'DAMAGE', 'internal', 'OCT'),
('5', 'SCRAP', 'internal', 'admin_finance_verify'),
('6', 'SCRAP', 'internal', 'admin_finance_approval'),
('7', 'SCRAP', 'internal', 'FinanceHQ'),
('8', 'SCRAP', 'internal', 'Pricing'),
('9', 'NOT RECEIVED', 'partner', 'console'),
('10', 'NOT RECEIVED', 'partner', 'sub-console'),
('11', 'RTSHQ', 'partner', 'console'),
('12', 'RTSHQ', 'partner', 'sub-console'),
('13', 'HAL', 'partner', 'console'),
('14', 'HAL', 'partner', 'sub-console'),
('15', 'ODA', 'partner', 'console'),
('16', 'ODA', 'partner', 'sub-console'),
('17', 'REJECTED', 'partner', 'console'),
('18', 'REJECTED', 'partner', 'sub-console'),
('19', 'RTS', 'partner', 'console'),
('20', 'RTS', 'partner', 'sub-console');
