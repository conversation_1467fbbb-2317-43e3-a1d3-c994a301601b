alter table reason
    add reason_is_not_shown boolean default false not null;
create index idx_reason_is_not_shown
    on reason (reason_is_not_shown);


INSERT INTO reason (reason_title, reason_description, reason_code, reason_external_code, reason_created_at,
                    reason_updated_at)
SELECT 'Salah Tempel Resi', 'Salah Tempel Resi', '', '', NOW(), NOW()
WHERE NOT EXISTS(SELECT reason_id FROM reason WHERE reason_title = 'Salah Tempel Resi');
UPDATE reason
SET reason_code = CONCAT('RES', reason_id)
WHERE reason_title = 'Salah Tempel Resi';

INSERT INTO reason_mapping (reason_mapping_reason_id, reason_mapping_status_code)
SELECT reason_id, 'CNX'
FROM reason
WHERE reason_title = 'Salah Tempel Resi'
  AND NOT EXISTS(SELECT reason_mapping_id
                 FROM reason_mapping
                 WHERE reason_mapping_status_code = 'CNX'
                   AND reason_mapping_reason_id = reason_id);