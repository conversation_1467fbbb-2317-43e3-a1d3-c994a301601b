CREATE TABLE IF NOT EXISTS sto_sc (
  `sto_sc_id` INT NOT NULL AUTO_INCREMENT,
  `sto_sc_no` VARCHAR(255) NOT NULL DEFAULT '',
  `sto_sc_partner_id` int(11) unsigned NOT NULL,
  `sto_sc_partner_code` VARCHAR(100) NOT NULL DEFAULT '',
  `sto_sc_partner_name` VARCHAR(255) NOT NULL DEFAULT '',
  `sto_sc_total_stt`  int(11) unsigned NOT NULL DEFAULT '0',
  `sto_sc_total_gross_weight` float unsigned NOT NULL DEFAULT '0',
  `sto_sc_total_volume_weight` float unsigned NOT NULL DEFAULT '0',
  `sto_sc_total_piece` int(11) unsigned NOT NULL DEFAULT '0',
  `sto_sc_created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `sto_sc_created_by` INT(11) NOT NULL DEFAULT '0',
  `sto_sc_created_name` VARCHA<PERSON>(255) NOT NULL DEFAULT '',
  `sto_sc_updated_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `sto_sc_updated_by` INT(11) NOT NULL DEFAULT '0',
  `sto_sc_updated_name` VARCHAR(255) NOT NULL DEFAULT '',
  PRIMARY KEY (`sto_sc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE INDEX idx_sto_sc_partner_id ON sto_sc (sto_sc_partner_id);
CREATE INDEX idx_sto_sc_no ON sto_sc (sto_sc_no);
CREATE INDEX idx_sto_sc_created_at ON sto_sc (sto_sc_created_at);

CREATE TABLE IF NOT EXISTS sto_sc_detail (
  `ssd_id` INT NOT NULL AUTO_INCREMENT,
  `ssd_sto_sc_id` int(11) unsigned NOT NULL,
  `ssd_sto_sc_stt_id` int(11) unsigned NOT NULL,
  `ssd_sto_sc_stt_piece_id` int(11) unsigned NOT NULL,
  PRIMARY KEY (`ssd_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE INDEX idx_ssd_sto_sc_id ON sto_sc_detail (ssd_sto_sc_id);
CREATE INDEX idx_ssd_sto_sc_stt_id ON sto_sc_detail (ssd_sto_sc_stt_id);
CREATE INDEX idx_ssd_sto_sc_stt_piece_id ON sto_sc_detail (ssd_sto_sc_stt_piece_id);

ALTER TABLE sti ADD sti_is_updated_sto_sc BOOLEAN DEFAULT FALSE NOT NULL AFTER sti_status_updated_at;
ALTER TABLE sti ADD INDEX `idx_sti_is_updated_sto_sc`(sti_is_updated_sto_sc);

