package usecase

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/slack"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/go-redis/redis"
)

func (c *luwjistikCtx) WebhookLuwjistik(ctx context.Context, form *model.ListenerLuwjistikStatus) error {
	var (
		opName              = "luwjistikCtx-WebhookLuwjistik"
		trace               = tracer.StartTrace(ctx, opName)
		selfCtx             = trace.Context()
		lastStatusLuwjistik = ``
		err                 error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": lastStatusLuwjistik, "error": err})

		go c.partnerLogRepo.Insert(context.Background(), &model.PartnerLog{
			Action:  model.PLSttLuwjistikUpdateStatus,
			RefID:   form.EventPayload.OrderCode,
			Request: form,
			Response: func() interface{} {
				if err != nil {
					return err.Error()
				}
				return model.LuwjistikWebhookResponse{Success: true}
			}(),
		})

		go func() {
			jsonParam, _ := json.Marshal(form)
			slack.SendNotification(slack.Notification{
				Title:     "Error Luwjistik Update Status " + lastStatusLuwjistik,
				Body:      string(jsonParam),
				Ctx:       "luwjistikCtx-UpdateStatus",
				Error:     err,
				Channel:   slack.GenesisUpdateStatusXLuwjistik,
				Indicator: slack.WarningIndicator})
		}()

	}()

	layout := "2006-01-02T15:04:05Z"
	str := form.EventPayload.Update.UpdateTimestamp
	utc, err := time.Parse(layout, str)
	form.EventPayload.Update.UpdateTimestampTime = shared.UTC7(utc)

	if err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Error Get Time",
			"id": "Gagal Mendapatkan Waktu",
		})
	}

	// get detail stt by order id
	sttDetail, err := c.sttRepo.SelectDetailV2(selfCtx, &model.SttSelectDetailParams{SttNo: form.EventPayload.OrderCode, IsNeedDecrypt: true})
	if err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Error Get Stt Detail",
			"id": "Gagal Mendapatkan Stt Detail",
		})
	}

	if len(sttDetail) == 0 {
		go func() {
			jsonParam, _ := json.Marshal(form)
			slack.SendNotification(slack.Notification{
				Title:     "Error Luwjistik Update Status",
				Body:      string(jsonParam),
				Ctx:       "luwjistikCtx-UpdateStatus",
				Error:     err,
				Channel:   slack.GenesisUpdateStatusXLuwjistik,
				Indicator: slack.WarningIndicator})
		}()
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT Vendor Number Not Found",
			"id": "Stt Number tidak Ditemukan",
		})
	}

	lastStatusLuwjistik = model.MappingLuwjistikStatus[form.EventPayload.Update.Status]
	form.EventPayload.Update.Status = lastStatusLuwjistik
	sttRow := sttDetail[0]

	if lastStatusLuwjistik == sttRow.SttLastStatusID {
		lastStatusLuwjistik = "same as before"
	}

	if model.INTERPACK != sttRow.SttProductType {
		lastStatusLuwjistik = "stt no interpack"
	}

	if model.IsLastStatusNotAllowToUpdate[sttRow.SttLastStatusID] && model.IsStatusLuwjisticUpdate[lastStatusLuwjistik] {
		go func() {
			jsonParam, _ := json.Marshal(form)
			slack.SendNotification(slack.Notification{
				Title:     "Error Luwjistik Update Status " + lastStatusLuwjistik,
				Body:      string(jsonParam),
				Ctx:       "luwjistikCtx-UpdateStatus",
				Error:     err,
				Channel:   slack.GenesisUpdateStatusXLuwjistik,
				Indicator: slack.WarningIndicator})
		}()
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf("Luwjistik Status Not Allowed To Update %s from %s with STT:%s", lastStatusLuwjistik, sttRow.SttLastStatusID, sttRow.SttNo),
			"id": fmt.Sprintf("Luwjistik Status Tidak Diijin Kan Untuk Diupdate %s dari %s dengan STT:%s", lastStatusLuwjistik, sttRow.SttLastStatusID, sttRow.SttNo),
		})
		return err
	}

	if model.NotAllowLuwjistikStatusToUpdate[sttRow.SttLastStatusID] {
		go func() {
			jsonParam, _ := json.Marshal(form)
			slack.SendNotification(slack.Notification{
				Title:     "Error Luwjistik Update Status " + lastStatusLuwjistik,
				Body:      string(jsonParam),
				Ctx:       "luwjistikCtx-UpdateStatus",
				Error:     err,
				Channel:   slack.GenesisUpdateStatusXLuwjistik,
				Indicator: slack.WarningIndicator})
		}()
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Luwjistik Status Not Allowed To Update",
			"id": "Luwjistik Status Tidak Diijin Kan Untuk Diupdate",
		})
	}

	stop := "STOP"
	if lastStatusLuwjistik == model.POD || lastStatusLuwjistik == model.RTS {
		allowedProcess := 1
		keySetTo := fmt.Sprintf(`%s-%s-%s`, `LW`, stop, sttRow.SttNo)
		cache, err := c.cfg.Cache().Incr(keySetTo)
		if err != nil || cache == 0 {
			lastStatusLuwjistik = "same as before"
		}

		if cache > int64(allowedProcess) {
			lastStatusLuwjistik = "same as before"
		}

		_, err = c.cfg.Cache().Expire(keySetTo, time.Minute)
		if err != nil {
			logger.Ef(`luwjistikCtx-Redis Del Error %s`, err.Error())
		}

	} else {
		time.Sleep(100 * time.Millisecond)
		keySetTo := fmt.Sprintf(`%s-%s-%s`, `LW`, stop, sttRow.SttNo)
		cache, err := c.cfg.Cache().Get(keySetTo)
		if !errors.Is(err, redis.Nil) || cache != "" {
			lastStatusLuwjistik = "same as before"
		}
	}

	if lastStatusLuwjistik == model.OCC || lastStatusLuwjistik == model.STIDEST || lastStatusLuwjistik == model.RTS {
		allowedProcess := 1
		keySetTo := fmt.Sprintf(`%s-%s-%s`, `LW`, lastStatusLuwjistik, sttRow.SttNo)
		cache, err := c.cfg.Cache().Incr(keySetTo)
		if err != nil || cache == 0 {
			lastStatusLuwjistik = "same as before"
		}

		if cache > int64(allowedProcess) {
			lastStatusLuwjistik = "same as before"
		}

		_, err = c.cfg.Cache().Expire(keySetTo, 10*time.Second)
		if err != nil {
			logger.Ef(`luwjistikCtx-Redis Del Error %s`, err.Error())
		}

	}

	// validate for stt last status ( CI and IsAllowLuwjistikStatusToUpdate ) or Last Status Luwjistik is RTS
	if (sttRow.SttLastStatusID == model.CI && model.IsAllowLuwjistikStatusToUpdate[lastStatusLuwjistik]) || lastStatusLuwjistik == model.RTS {
		err = fmt.Errorf("cannot update status Luwjistik when stt last status = %s and request status = %s", sttRow.SttLastStatusID, lastStatusLuwjistik)
		return nil
	}

	switch lastStatusLuwjistik {
	case model.DEL:
		errDel := c.setToDel(context.Background(), form, sttDetail)
		if errDel != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.DEL,
					Body:      string(jsonParam),
					Ctx:       "luwjistikCtx-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errDel
		}
	case model.DAMAGE:
		errDel := c.setToDAMAGE(context.Background(), form, sttDetail)
		if errDel != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.DAMAGE,
					Body:      string(jsonParam),
					Ctx:       "luwjistikCtx-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errDel
		}
	case model.MISSING:
		errDel := c.setToMISSING(context.Background(), form, sttDetail)
		if errDel != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.MISSING,
					Body:      string(jsonParam),
					Ctx:       "luwjistikCtx-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errDel
		}
	case model.DEX:
		errDex := c.setToDex(context.Background(), form, sttDetail)
		if errDex != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.DEX,
					Body:      string(jsonParam),
					Ctx:       "UsecaseLUWJISTIK-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errDex
		}
	case model.STIDEST:
		errStiDest := c.setToStiDest(context.Background(), form, sttDetail)
		if errStiDest != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.STIDEST,
					Body:      string(jsonParam),
					Ctx:       "UsecaseLUWJISTIK-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errStiDest
		}
	case model.POD:
		// Update status to POD
		errPod := c.setToPod(context.Background(), form, sttDetail)
		if errPod != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.POD,
					Body:      string(jsonParam),
					Ctx:       "UsecaseLuwjistik-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errPod
		}
	case model.INTHND:
		// Update status to INTHND
		errINTHND := c.setToINTHND(context.Background(), form, sttDetail)
		if errINTHND != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.INTHND,
					Body:      string(jsonParam),
					Ctx:       "UsecaseLuwjistik-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errINTHND
		}
	case model.INTSTI:
		// Update status to INTSTI
		errINTSTI := c.setToINTSTI(context.Background(), form, sttDetail)
		if errINTSTI != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.INTSTI,
					Body:      string(jsonParam),
					Ctx:       "UsecaseLuwjistik-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errINTSTI
		}
	case model.OCCEXP:
		// Update status to OCCEXP
		errOCCEXP := c.setToOCCEXP(context.Background(), form, sttDetail)
		if errOCCEXP != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.OCCEXP,
					Body:      string(jsonParam),
					Ctx:       "UsecaseLuwjistik-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errOCCEXP
		}
	case model.OCCIMP:
		// Update status to OCCIMP
		errOCCIMP := c.setToOCCIMP(context.Background(), form, sttDetail)
		if errOCCIMP != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.OCCIMP,
					Body:      string(jsonParam),
					Ctx:       "UsecaseLuwjistik-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errOCCIMP
		}
	case model.OCCHAL:
		// Update status to OCCHAL
		errOCCHAL := c.setToOCCHAL(context.Background(), form, sttDetail)
		if errOCCHAL != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.OCCHAL,
					Body:      string(jsonParam),
					Ctx:       "UsecaseLuwjistik-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errOCCHAL
		}
	case model.SCRAP:
		// Update status to SCRAP
		errSCRAP := c.setToSCRAP(context.Background(), form, sttDetail)
		if errSCRAP != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.SCRAP,
					Body:      string(jsonParam),
					Ctx:       "UsecaseLuwjistik-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errSCRAP
		}
	case model.TRANSIT:
		// Update status to Transit
		errTransit := c.setToTransit(context.Background(), form, sttDetail)
		if errTransit != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.TRANSIT,
					Body:      string(jsonParam),
					Ctx:       "UsecaseLuwjistik-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errTransit
		}
	case model.CARGOPLANE:
		// Update status to CargoPlane
		errCargoPlane := c.setToCargoPlane(context.Background(), form, sttDetail)
		if errCargoPlane != nil {
			go func() {
				jsonParam, _ := json.Marshal(form)
				slack.SendNotification(slack.Notification{
					Title:     "Error Luwjistik Update Status " + model.CARGOPLANE,
					Body:      string(jsonParam),
					Ctx:       "UsecaseLuwjistik-UpdateStatus",
					Error:     err,
					Channel:   slack.GenesisUpdateStatusXLuwjistik,
					Indicator: slack.WarningIndicator})
			}()
			return errCargoPlane
		}
	}

	return nil
}
