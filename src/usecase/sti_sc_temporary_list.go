package usecase

import (
	"context"
	"fmt"

	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/repository"
	"github.com/Lionparcel/hydra/src/usecase/sti_sc"
)

func (c *stiSc) ListSTISCTemporaryData(ctx context.Context, req *sti_sc.StiSCTemporaryListParams) (*sti_sc.StiSCTemporaryListResponse, error) {
	var (
		opName  = "UsecaseStiSc-ListSTISCTemporaryData"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		result        = new(sti_sc.StiSCTemporaryListResponse)
		err           error
		stiScTempData = make([]model.STISCTemporary, 0)
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": result, "error": err})
		_ = c.partnerLog.Insert(ctx, &model.PartnerLog{
			Action:  opName,
			RefID:   fmt.Sprint(req.PartnerType) + "-" + fmt.Sprint(req.PartnerID),
			Request: req,
			Response: map[string]interface{}{
				"result":           result,
				"error":            err,
				"sti_sc_temp_data": stiScTempData,
			},
		})
	}()

	if err = req.Validate(); err != nil {
		return nil, err
	}
	isActive := true
	stiScTempData, err = c.sttScRepo.SelectStiSCTemporaryData(ctx, &repository.SelectStiSCTemporaryDataParams{
		SttNo:      req.SttNo,
		BookedId:   req.BookedID,
		BookedType: req.BookedType,
		AccountID:  req.AccountID,
		IsActive:   &isActive,
	})
	if err != nil {
		return nil, err
	}

	return result.ParseData(stiScTempData...), nil
}
