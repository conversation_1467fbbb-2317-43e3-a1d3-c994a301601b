package usecase

import (
	"context"
	"encoding/json"
	"math"
	"strconv"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/stt"
	"github.com/abiewardani/dbr/v2"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

func (c *sttCtx) validateInternationalDocument(ctx context.Context, data stt.Stt, token string) error {
	tempCityOrigin, err := c.cityRepo.Get(ctx, data.SttOriginCityID, token)
	if err != nil || tempCityOrigin == nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed get data city origin",
			"id": "Gagal mendapatkan data city asal",
		})
	}
	tempCityDestination, err := c.cityRepo.Get(ctx, data.SttDestinationCityID, token)
	if err != nil || tempCityDestination == nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed get data city destination",
			"id": "Gagal mendapatkan data city tujuan",
		})
	}
	dataIntDocConfig, err := c.internationalDocumentRepo.GetDetailInternationalDocument(ctx, model.GetInternationalDocumentParams{
		Token:             token,
		OriginCityID:      tempCityOrigin.ID,
		DestinationCityID: tempCityDestination.ID,
	})
	if err != nil {
		return shared.ERR_GET_DATA
	}

	if errValidate := c.validateSttWithResponseDataInternationalDocument(ctx, data, dataIntDocConfig); errValidate != nil {
		return errValidate
	}
	return nil
}

func (c *sttCtx) validateSttWithResponseDataInternationalDocument(ctx context.Context, data stt.Stt, dataIntDocConfig *model.InternationalDocumentData) error {
	dataIntDocConfigDataEmpty := dataIntDocConfig == nil || (dataIntDocConfig.IdcID == 0 && dataIntDocConfig.IdcCountryID == 0)
	if dataIntDocConfigDataEmpty {
		return nil
	}

	if err := validateResponseInternationalDocumentReceiverData(dataIntDocConfig, data); err != nil {
		return err
	}
	if err := validateResponseInternationalDocumentSenderData(dataIntDocConfig, data); err != nil {
		return err
	}

	return nil
}

func validateResponseInternationalDocumentSenderData(dataIntDocConfig *model.InternationalDocumentData, data stt.Stt) error {
	if dataIntDocConfig.IdcIsKtpImage && data.SttKtpImage == "" {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt ktp image must be filled",
			"id": "Stt ktp image harus diisi",
		})
	}
	if dataIntDocConfig.IdcIsNpwpImage && data.SttTaxImage == "" {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt tax image must be filled",
			"id": "Stt tax image harus diisi",
		})
	}
	if dataIntDocConfig.IdcIsBeforeAndAfterPackingImage && len(data.SttAttachFiles) < 2 {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt before/after packing image must be filled",
			"id": "Stt before/after packing image harus diisi",
		})
	}
	return nil
}

func validateResponseInternationalDocumentReceiverData(dataIntDocConfig *model.InternationalDocumentData, data stt.Stt) error {
	/*
		delete this validation
		- dataIntDocConfig.IdcIsOtherCommodity && data.SttCommodityDetail == ""
		by this ticket
		- https://lionparcel.atlassian.net/browse/CS-27392
	*/
	if dataIntDocConfig.IdcIsReceiverEmail && data.SttRecipientEmail == "" {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt recipient email must be filled",
			"id": "Stt recipient email harus diisi",
		})
	}
	if dataIntDocConfig.IdcIsReceiverEmail && data.SttRecipientEmail != "" {
		if err := validation.Validate(data.SttRecipientEmail, validation.Required, validation.Match(shared.EmailRegex)); err != nil {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "recipient email not match format",
				"id": "recipient email tidak sesuai format",
			})
		}
	}
	return nil
}

func equalsInThreeDecimal(weightBefore float64, weightAfter float64) bool {
	before := int(math.Trunc(weightBefore * 1000))
	after := int(math.Trunc(weightAfter * 1000))
	return before == after
}

func (c *sttCtx) checkSttCanGetPriorityTier(stt model.Stt) bool {
	isSttRetail := model.IsPrefixSttRetail[shared.GetPrefixSttNo(stt.SttNo)] && stt.SttBookedForType == model.POS && stt.SttNoRefExternal == ``
	if isSttRetail {
		return true
	}
	if stt.SttSource == model.CA && stt.SttBilledTo == model.CUSTOMERAPPS {
		return true
	}
	return false
}

func (c *sttCtx) getProfilePriorityTier(selfCtx context.Context, partnerID int, stt model.Stt, token string) bool {
	if !c.cfg.IsSTTPriorityTier() {
		return false
	}

	if !c.checkSttCanGetPriorityTier(stt) {
		return false
	}

	isPriorityTier, err := c.checkIsPriorityTier(selfCtx, partnerID, token)
	if err != nil {
		return false
	}

	return isPriorityTier
}

func (c *sttCtx) getSttTotalAmount(clientIsDOPaymentTypeFree bool, checkTariff *model.CheckTariffResponse) float64 {
	if clientIsDOPaymentTypeFree {
		return 0
	}
	if checkTariff.Data.IsPromo {
		return checkTariff.Data.TotalTariffAfterDiscount
	}
	return checkTariff.Data.TotalTariff
}

func (c *sttCtx) getCommodityName(commodity *model.Commodity, cityOrigin *model.City) string {
	if cityOrigin.Country.Code != model.CountryID {
		return commodity.Data.CommodityNameEn
	}
	return commodity.Data.CommodityName
}

func (c *sttCtx) getBookingFee(reqCheckTariff *stt.CalculateTariffParams, checkTariff *model.CheckTariffResponse) float64 {
	if reqCheckTariff.RequestCalculateTariff.IsCod && checkTariff.Data.CodFeeAfterDiscount > 0 {
		return checkTariff.Data.TotalTariffAfterDiscount - checkTariff.Data.CodFeeAfterDiscount
	}
	return checkTariff.Data.TotalTariffAfterDiscount
}

func (c *sttCtx) getShipmentPrioritySubscription(shipment *model.Shipment) bool {
	shipmentMeta := shipment.ShipmentMetaToStruct()
	if shipmentMeta == nil {
		return false
	}
	if !shipmentMeta.PrioritySubscription {
		return false
	}

	sspConfig := c.cfg.GetShipmentSubscriptionPriorities()
	isShipmentPrioritySubscription := sspConfig["PRIORITY_DELIVERY"]
	if !isShipmentPrioritySubscription {
		return false
	}

	priorityPrefixConfig := c.cfg.GetShipmentSubscriptionPriorityDeliveryPrefixes()
	isShipmentPrioritySubscription = priorityPrefixConfig[shared.GetPrefixShipmentID(shipment.ShipmentAlgoID)]
	return isShipmentPrioritySubscription
}

type CheckDFODPastiInactivePeriodParam struct {
	stt        *model.Stt
	cityOrigin *model.City
	token      string
}

func (c *sttCtx) checkConfigDfodPastiInactivePeriod(ctx context.Context, param CheckDFODPastiInactivePeriodParam) *model.SttCustomFlag {
	prefixSttNo := shared.GetPrefixSttNo(param.stt.SttNo)
	if !model.IsPrefixSttEligibleDfodPasti[prefixSttNo] {
		return nil
	}

	loc, err := time.LoadLocation(shared.MappingTimzoneToTimeLocation[param.cityOrigin.Timezone])
	if err != nil {
		loc, _ = time.LoadLocation(shared.AsiaJakarta)
	}
	sttBookedAt := param.stt.SttBookedAt.In(loc)
	date := sttBookedAt.Format("2006-01-02")
	now := c.timeRepo.Now(time.Time{})

	scf := &model.SttCustomFlag{
		SCFSTTNo:     param.stt.SttNo,
		SCFKey:       "dfod_pasti",
		SCFValue:     "false",
		SCFCreatedAt: now,
	}

	if !c.flagManagementRepo.CloudBeesDFODPastiProgram(ctx) {
		return scf
	}

	config, err := c.configDfodPastiRepo.GetInactivePeriod(ctx, date, param.token)
	if err != nil {
		return scf
	}

	if config != nil && !config.Data.DfodPasti {
		return scf
	}

	exceptionMatchParams := &model.DfodPastiExceptionMatchRequest{
		ProductType:           param.stt.SttProductType,
		CommodityCode:         param.stt.SttCommodityCode,
		OriginCityID:          param.stt.SttOriginCityID,
		OriginDistrictID:      param.stt.SttOriginDistrictID,
		DestinationCityID:     param.stt.SttDestinationCityID,
		DestinationDistrictID: param.stt.SttDestinationDistrictID,
	}
	exceptionMatch, err := c.configDfodPastiRepo.CheckExceptionMatch(ctx, exceptionMatchParams, param.token)
	if err != nil {
		return scf
	}
	scf.SCFValue = strconv.FormatBool(exceptionMatch.Data.DfodPasti)

	return scf
}

func (c *sttCtx) addCustomFlagDfodPastiInactivePeriod(ctx context.Context, param CheckDFODPastiInactivePeriodParam, data model.SttCreateParams) model.SttCreateParams {
	sttParam := data.SttCreate[0].Stt
	sttParam.SttNo = param.stt.SttNo
	sttParam.SttBookedAt = param.stt.SttBookedAt
	param.stt = &sttParam
	configDfodPastiInactivePeriod := c.checkConfigDfodPastiInactivePeriod(ctx, param)
	if configDfodPastiInactivePeriod != nil {
		data.SttCreate[0].SttCustomFlag = append(data.SttCreate[0].SttCustomFlag, *configDfodPastiInactivePeriod)
	}
	return data
}

func (c *sttCtx) checkNewDfodRulesAndUpdateMetaSttCustomFlag(ctx context.Context, param CheckDfodRulesParam) []model.SttCustomFlag {
	if !c.cfg.GetDfodNewRuleCARetailEnable() {
		return nil
	}

	isSttCA := param.sttCreate.SttShipmentID != `` && model.MappingShipmentPrefixCustomerName[shared.GetPrefixShipmentID(param.sttCreate.SttShipmentID)] == model.CUSTOMERAPPS
	isSttRetail := model.IsPrefixSttRetail[param.sttCreate.SttNo[:2]]
	if !(isSttRetail || isSttCA) {
		return nil
	}

	if !(param.sttCreate.SttIsDFOD && param.sttCreate.SttIsCOD) {
		return nil
	}
	now := c.timeRepo.Now(time.Time{})

	sttCustomFlag := model.SttMetaSttCustomFlag{
		ScfKey:   "dfod_new_rules",
		ScfValue: "ca_retail_final_status",
	}
	param.sttMeta.SttCustomFlag = []model.SttMetaSttCustomFlag{sttCustomFlag}

	param.sttCreate.SttMeta = param.sttMeta.ToString()

	res := []model.SttCustomFlag{
		{
			SCFSTTNo:     param.sttCreate.SttNo,
			SCFKey:       sttCustomFlag.ScfKey,
			SCFValue:     sttCustomFlag.ScfValue,
			SCFCreatedAt: now,
		},
	}

	configDfodPastiInactivePeriod := c.checkConfigDfodPastiInactivePeriod(ctx, CheckDFODPastiInactivePeriodParam{
		stt:        param.sttCreate,
		cityOrigin: param.cityOrigin,
		token:      param.token,
	})

	if configDfodPastiInactivePeriod != nil {
		res = append(res, *configDfodPastiInactivePeriod)
	}

	return res
}

type GenerateSttCreateParam struct {
	stt         stt.Stt
	checkTariff *model.CheckTariffResponse
	request     *stt.CreateSttRequest
}

func generateSttCreate(stt stt.Stt, checkTariff *model.CheckTariffResponse, request *stt.CreateSttRequest, now time.Time) model.SttCreate {
	return model.SttCreate{
		Stt: generateSttModel(stt, checkTariff, request, now),
		SttPieceHistory: model.SttPieceHistory{
			HistoryStatus:      model.BKD,
			HistoryLocation:    stt.SttOriginCityID,
			HistoryActorName:   request.AccountRefName,
			HistoryActorID:     request.AccountRefID,
			HistoryCreatedAt:   now,
			HistoryCreatedBy:   int(request.AccountID),
			HistoryCreatedName: request.AccountName,
		},
		ElexysTariff: &model.SttElexys{
			SEElexysTotalTariff:              request.ElexysTariff.TotalTariff,
			SEElexysForwardRate:              request.ElexysTariff.ForwardRate,
			SEElexysPublishRate:              request.ElexysTariff.PublishRate,
			SEElexysShippingSurchargeRate:    request.ElexysTariff.ShippingSurchargeRate,
			SEElexysDocumentSurchargeRate:    request.ElexysTariff.DocumentSurchargeRate,
			SEElexysCommoditySurchargeRate:   request.ElexysTariff.CommoditySurchargeRate,
			SEElexysHeavywieghtSurchargeRate: request.ElexysTariff.HeavyWeightSurchargeRate,
			SEElexysInsuranceRate:            request.ElexysTariff.InsuranceRate,
			SEElexysWoodpackingRate:          request.ElexysTariff.WoodPackingRate,
			SEElexysEtd:                      request.ElexysTariff.ETD,
			SEElexysGrossWeight:              request.ElexysTariff.GrossWeight,
			SEElexysVolumeWeight:             request.ElexysTariff.VolumeWeight,
			SEElexysChargeableWeight:         request.ElexysTariff.ChargeableWeight,
			SEElexysIsCodArea:                request.ElexysTariff.IsCodArea,
			SEElexysWeight:                   request.ElexysTariff.Weight,
			SEElexysTotalNormalTariff:        request.ElexysTariff.TotalNormalTariff,
			SEElexysTotalBasicTariff:         request.ElexysTariff.TotalBasicTariff,
		},
	}
}

func generateSttModel(stt stt.Stt, checkTariff *model.CheckTariffResponse, request *stt.CreateSttRequest, now time.Time) model.Stt {
	return model.Stt{
		SttNo:                    stt.SttNo,
		SttShipmentID:            stt.SttShipmentID,
		SttTaxNumber:             stt.SttTaxNumber,
		SttGoodsEstimatePrice:    stt.SttGoodsEstimatePrice,
		SttGoodsStatus:           stt.SttGoodsStatus,
		SttNoRefExternal:         stt.SttNoRefExternal,
		SttOriginCityID:          stt.SttOriginCityID,
		SttDestinationCityID:     stt.SttDestinationCityID,
		SttOriginDistrictID:      stt.SttOriginDistrictID,
		SttDestinationDistrictID: stt.SttDestinationDistrictID,
		SttSenderName:            stt.SttSenderName,
		SttSenderAddress:         stt.SttSenderAddress,
		SttSenderPhone:           stt.SttSenderPhone,
		SttRecipientName:         stt.SttRecipientName,
		SttRecipientAddress:      stt.SttRecipientAddress,
		SttRecipientPhone:        stt.SttRecipientPhone,
		SttRecipientAddressType:  dbr.NewNullString(stt.SttRecipientAddressType),
		SttProductType:           stt.SttProductType,
		SttInsuranceType:         stt.SttInsuranceType,

		SttOriginDistrictRate:         checkTariff.Data.OriginDistrictRate,
		SttDestinationDistrictRate:    checkTariff.Data.DestinationDistrictRate,
		SttPublishRate:                checkTariff.Data.PublishRate,
		SttShippingSurchargeRate:      checkTariff.Data.ShippingSurchargeRate,
		SttDocumentSurchargeRate:      checkTariff.Data.DocumentSurcharge,
		SttCommoditySurchargeRate:     checkTariff.Data.CommoditySurcharge,
		SttHeavyweightSurchargeRate:   checkTariff.Data.HeavyWeightSurcharge,
		SttBMTaxRate:                  checkTariff.Data.BMTaxRate,
		SttPPNTaxRate:                 checkTariff.Data.PPNTaxRate,
		SttPPHTaxRate:                 checkTariff.Data.PPHTaxRate,
		SttGrossWeight:                checkTariff.Data.GrossWeight,
		SttVolumeWeight:               checkTariff.Data.VolumeWeight,
		SttChargeableWeight:           checkTariff.Data.ChargeableWeight,
		SttCommodityCode:              stt.SttCommodityCode,
		SttTotalPiece:                 stt.SttTotalPiece,
		SttLastStatusID:               model.BKD,
		SttClientSttID:                stt.SttClientSttID,
		SttVendorSttID:                stt.SttVendorSttID,
		SttIsCOD:                      stt.SttIsCOD,
		SttIsDFOD:                     stt.SttIsDFOD,
		SttIsDO:                       stt.SttIsDO,
		SttCounter:                    stt.SttTotalPiece,
		SttBookedAt:                   now,
		SttBookedBy:                   request.AccountRefID,
		SttBookedName:                 request.AccountRefName,
		SttCreatedAt:                  now,
		SttCreatedBy:                  int(request.AccountID),
		SttCreatedName:                request.AccountName,
		SttUpdatedAt:                  now,
		SttUpdatedBy:                  int(request.AccountID),
		SttUpdatedName:                request.AccountName,
		SttHeavyweightSurchargeRemark: checkTariff.Data.HeavyWeightSurchargeRemarks,
		SttUpdatedActorID:             dbr.NewNullInt64(request.AccountRefID),
		SttUpdatedActorName:           dbr.NewNullString(request.AccountRefName),
		SttNextCommodity:              stt.SttNextCommodity,
		SttPiecePerPack:               stt.SttPiecePerPack,
		SttCODAmount:                  checkTariff.Data.CodAmount,
		SttCODFee:                     checkTariff.Data.CodFee,
	}
}

func generateCreateSttCustomFlagFromSttMeta(sttMeta model.SttMeta) []model.SttCustomFlag {
	scf := []model.SttCustomFlag{}
	now := time.Now()

	if len(sttMeta.SttCIPL) > 0 {
		j, _ := json.Marshal(sttMeta.SttCIPL)

		scf = append(scf, model.SttCustomFlag{
			SCFKey:       model.ScfKeySttCIPL,
			SCFValue:     string(j),
			SCFCreatedAt: now,
		})
	}

	if len(sttMeta.SttFtzCIPL) > 0 {
		j, _ := json.Marshal(sttMeta.SttFtzCIPL)

		scf = append(scf, model.SttCustomFlag{
			SCFKey:       model.ScfKeySttFtzCIPL,
			SCFValue:     string(j),
			SCFCreatedAt: now,
		})
	}

	return scf
}

func (c *sttCtx) getSttCustomFlagNewForm(sttNo string, isNewForm bool, now time.Time) model.SttCustomFlag {
	return model.SttCustomFlag{
		SCFSTTNo:     sttNo,
		SCFKey:       model.ScfKeyIsNewForm,
		SCFValue:     strconv.FormatBool(isNewForm),
		SCFCreatedAt: now,
	}
}
