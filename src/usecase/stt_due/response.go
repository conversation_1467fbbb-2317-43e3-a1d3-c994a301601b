package stt_due

import (
	"time"

	"github.com/Lionparcel/hydra/src/model"
)

type (
	// STTDueListResponse ...
	STTDueListResponse struct {
		SDID                   int64     `json:"sd_id"`
		BagNo                  string    `json:"bag_no"`
		STTNo                  string    `json:"stt_no"`
		RefNo                  string    `json:"ref_no"`
		SttLastStatusID        string    `json:"stt_last_status_id"`
		SttProductType         string    `json:"stt_product_type"`
		SttTotalPiece          int       `json:"stt_total_piece"`
		SttGrossWeight         float64   `json:"stt_gross_weight"`
		SttDestinationCityID   string    `json:"stt_destination_city_id"`
		SttDestinationCityName string    `json:"stt_destination_city_name"`
		SttBookedAt            time.Time `json:"stt_booked_at"`
		SttCreatedName         string    `json:"stt_created_name"`
		SttBookedName          string    `json:"stt_booked_name"`
		Deadline               string    `json:"deadline"`
	}

	// STTDueStiDestDelResponse ...
	STTDueStiDestDelListResponse struct {
		SDID            int64     `json:"sd_id"`
		CargoType       string    `json:"cargo_type"`
		CargoNo         string    `json:"cargo_no"`
		SttNo           string    `json:"stt_no"`
		RefNo           string    `json:"ref_no"`
		SttLastStatusID string    `json:"stt_last_status_id"`
		SttProductType  string    `json:"stt_product_type"`
		SttTotalPiece   int       `json:"stt_total_piece"`
		SttGrossWeight  float64   `json:"stt_gross_weight"`
		SttOriginCityId string    `json:"stt_origin_city_id"`
		SttUpdatedAt    time.Time `json:"stt_updated_at"`
		SttUpdatedName  string    `json:"stt_updated_name"`
		Deadline        string    `json:"deadline"`
	}

	// STTDueListMetaResponse ...
	STTDueListMetaResponse struct {
		Page         int `json:"page"`
		Limit        int `json:"limit"`
		TotalRecords int `json:"total_records,omitempty"`
	}

	// STTDueDataResponse ...
	STTDueDataResponse struct {
		Data []STTDueListResponse   `json:"data"`
		Meta STTDueListMetaResponse `json:"meta"`
	}

	STTDueSummaryData struct {
		TotalData    int64 `json:"total"`
		TotalNow     int64 `json:"total_now"`
		TotalOverdue int64 `json:"total_overdue"`
		TotalBooked  int64 `json:"total_booked"`
	}

	STTDueBookedListResponse struct {
		BookedType string `json:"booked_type"`
		BookedID   uint64 `json:"booked_id"`
		BookedName string `json:"booked_name"`
	}

	STTDueBookedDataResponse struct {
		Data []STTDueBookedListResponse `json:"data"`
		Meta STTDueListMetaResponse     `json:"meta"`
	}

	// STTDueStiDestDelDataResponse ...
	STTDueStiDestDelDataResponse struct {
		Data []STTDueStiDestDelListResponse `json:"data"`
		Meta STTDueListMetaResponse         `json:"meta"`
	}

	STTDueBookedDropdownData struct {
		BookedID   uint64 `json:"booked_id"`
		BookedName string `json:"booked_name"`
		BookedType string `json:"booked_type"`
	}

	STTDueBookedDropdownResponse struct {
		Data []STTDueBookedDropdownData `json:"data"`
	}
)

func (r *STTDueSummaryData) ToResponse(data model.STTDueSummaryData) *STTDueSummaryData {
	r.TotalNow = data.TotalNow
	r.TotalData = data.Total
	r.TotalOverdue = data.TotalOverdue
	r.TotalBooked = data.TotalBooked

	return r
}

func (r *STTDueListMetaResponse) ChangeTotalRecords() *STTDueListMetaResponse {
	// Change total records to 0 if total records is less than 0
	if r.TotalRecords > 0 {
		r.TotalRecords = r.TotalRecords - ((r.Page - 1) * r.Limit)
	}
	return r
}

func (r *STTDueDataResponse) ToResponse(sttData ...model.STTDueWithSTTModel) *STTDueDataResponse {
	for _, v := range sttData {
		data := STTDueListResponse{
			SDID:                   v.SdID,
			BagNo:                  v.SdBagNo,
			STTNo:                  v.SdSttNo,
			RefNo:                  v.SdRefNo,
			SttLastStatusID:        v.SttLastStatusID,
			SttProductType:         v.SttProductType,
			SttTotalPiece:          v.SttTotalPiece,
			SttGrossWeight:         v.SttGrossWeight,
			SttDestinationCityID:   v.SttDestinationCityID,
			SttDestinationCityName: v.SttDestinationCityName,
			SttBookedAt:            v.SdSttBookedAt,
			SttCreatedName:         v.SttCreatedName,
			SttBookedName:          v.SdBookedName,
			Deadline:               v.Deadline,
		}
		r.Data = append(r.Data, data)
	}
	return r
}

func (r *STTDueDataResponse) ToMetaResponse(page, limit, totalRecords int, isTotalRecords bool) *STTDueDataResponse {
	r.Meta.Page = page
	r.Meta.Limit = limit

	if isTotalRecords {
		r.Meta.TotalRecords = totalRecords
		r.Meta.ChangeTotalRecords()
	}

	return r
}

func (r *STTDueBookedDataResponse) ToResponse(sttData ...model.STTDueModel) *STTDueBookedDataResponse {
	for _, v := range sttData {
		data := STTDueBookedListResponse{
			BookedID:   v.SdBookedID,
			BookedName: v.SdBookedName,
			BookedType: v.SdBookedType,
		}
		r.Data = append(r.Data, data)
	}
	return r
}

func (r *STTDueBookedDataResponse) ToMetaResponse(page, limit, totalRecords int, isTotalRecords bool) *STTDueBookedDataResponse {
	r.Meta.Page = page
	r.Meta.Limit = limit

	if isTotalRecords {
		r.Meta.TotalRecords = totalRecords
	}

	return r
}

func (r *STTDueStiDestDelDataResponse) ToResponse(sttData ...model.STTDueWithSTTAndPriorityDeliveryModel) *STTDueStiDestDelDataResponse {
	r.Data = make([]STTDueStiDestDelListResponse, 0)
	for _, v := range sttData {
		data := STTDueStiDestDelListResponse{
			SDID:            v.SdID,
			CargoType:       v.SdCargoType,
			CargoNo:         v.SdCargoNo,
			SttNo:           v.SdSttNo,
			RefNo:           v.SdRefNo,
			SttLastStatusID: v.SttLastStatusID,
			SttProductType:  v.SttProductType,
			SttTotalPiece:   v.SttTotalPiece,
			SttGrossWeight:  v.SttGrossWeight,
			SttOriginCityId: v.SttOriginCityID,
			SttUpdatedAt:    v.SttUpdatedAt,
			SttUpdatedName:  v.SttUpdatedName,
			Deadline:        v.Deadline,
		}
		r.Data = append(r.Data, data)
	}
	return r
}

func (r *STTDueStiDestDelDataResponse) ToMetaResponse(page, limit, totalRecords int, isTotalRecords bool) *STTDueStiDestDelDataResponse {
	r.Meta.Page = page
	r.Meta.Limit = limit

	if isTotalRecords {
		r.Meta.TotalRecords = totalRecords
		r.Meta.ChangeTotalRecords()
	}

	return r
}

func (r *STTDueBookedDropdownResponse) ToResponse(sttData ...model.STTDueModel) *STTDueBookedDropdownResponse {
	for _, v := range sttData {
		data := STTDueBookedDropdownData{
			BookedID:   v.SdBookedID,
			BookedName: v.SdBookedName,
			BookedType: v.SdBookedType,
		}
		r.Data = append(r.Data, data)
	}
	return r
}
