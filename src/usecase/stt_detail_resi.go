package usecase

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/Lionparcel/go-lptool/v2/lputils"

	"github.com/abiewardani/dbr/v2"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *sttCtx) DetailResi(ctx context.Context, req stt.DetailResiRequest) (*stt.DetailResiResponse, error) {
	opName := "UsecaseStt-DetailResi"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	detailResiResponse := new(stt.DetailResiResponse)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": detailResiResponse, "error": err})
	}()

	errQueryDB := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "An error occurred while querying db",
		"id": "Terjadi kesalahan pada saat query db",
	})
	errShipmentDataNotFound := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "An error occurred while getting shipment",
		"id": "Terjadi kesalahan pada saat query getting shipment",
	})

	var (
		responseSttPiecesArray []stt.DetailSttPiecesResponse
		insuranceRate          float64
		woodpackingRate        float64
		externalRef            string = `-`
		exchangeRate                  = new(model.ExchangeRateResponses)
		commodityDescription   string
	)

	// get detail stt
	sttDetail, err := c.getSttDetailRetryMaster(selfCtx, &model.SttViewDetailParams{
		Stt: model.Stt{SttID: int64(req.ID)},
	})
	if err != nil {
		return nil, errQueryDB
	}

	if sttDetail == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt Not Found",
			"id": "Stt tidak ditemukan",
		})
	}

	errInvalidAccount := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "Account Type invalid",
		"id": "Tipe akun tidak valid",
	})

	switch req.AccountRefType {
	case model.POS:
		if !(sttDetail.SttBookedBy == req.AccountRefID && sttDetail.SttBookedByType == model.POS) {
			return nil, errInvalidAccount
		}
	case model.CLIENT:

		var isInvalidAccountErr bool
		isInvalidAccountErr, err = c.validateClientAccount(selfCtx, sttDetail, req)
		if isInvalidAccountErr {
			return nil, errInvalidAccount
		}
		if err != nil {
			return nil, err
		}
	}

	c.sttRepo.GetCacheResi(ctx, req.ID, &detailResiResponse)
	isResiExists := detailResiResponse != nil && detailResiResponse.SttID > 0
	if isResiExists && len(detailResiResponse.DetailSttPiecesResponse) > 0 {
		return detailResiResponse, nil
	}

	// get stt pieces
	sttPieces, err := c.getSttPieceRetryMaster(selfCtx, &model.SttPiecesViewParam{SttID: int(sttDetail.SttID)})
	if err != nil {
		return nil, errQueryDB
	}

	// get estimate sla
	credentialRestAPI := &model.CredentialRestAPI{
		Token: ctx.Value(`tokenStr`).(string),
	}

	sttMeta := sttDetail.SttMetaToStruct()
	bookedByCountry := model.CountryID
	estimateSla := `-`
	reverseLastStatus := ``
	lastStatusBeforeSttReturn := ``
	var isReverseJourneyAfterCancel bool
	var isReverseRetail bool

	if sttMeta != nil {
		estimateSla = sttMeta.EstimateSLA
		bookedByCountry = sttMeta.GetSttBookedByCountry()
		if sttMeta.DetailSttReverseJourney != nil {
			isReverseJourneyAfterCancel, lastStatusBeforeSttReturn, reverseLastStatus, isReverseRetail = c.checkSttReverseJourney(sttDetail, sttMeta)
		}
	}

	// get commodity
	commodity, err := c.commodityRepo.GetCommodityByCode(
		ctx,
		sttDetail.SttCommodityCode,
		credentialRestAPI.Token,
	)
	if err != nil {
		return nil, errQueryDB
	}

	if commodity == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Commodity not found",
			"id": "Commodity tidak ditemukan",
		})
	}

	// get stt optional rate
	sttOptionalRates, err := c.getSttOptionRetryMaster(selfCtx, &model.SttOptionalRate{SttOptionalRateSttID: sttDetail.SttID})
	if err != nil {
		return nil, errQueryDB
	}

	sttIsWoodPacking := false
	if len(sttOptionalRates) > 0 {
		for _, v := range sttOptionalRates {
			if v.SttOptionalRateParams == model.INSURANCE {
				insuranceRate = v.SttOptionalRateRate
			}
			if v.SttOptionalRateParams == model.WOODPACKING {
				woodpackingRate = v.SttOptionalRateRate
				sttIsWoodPacking = true
			}
		}
	}

	for i, val := range sttPieces {
		responseSttPieces := stt.DetailSttPiecesResponse{}
		responseSttPieces.SttPieceID = val.SttPieceID
		responseSttPieces.SttID = val.SttPieceSttID
		responseSttPieces.SttPieceLength = val.SttPieceLength
		responseSttPieces.SttPieceWidth = val.SttPieceWidth
		responseSttPieces.SttPieceHeight = val.SttPieceHeight
		responseSttPieces.SttPieceGrossWeight = val.SttPieceGrossWeight
		responseSttPieces.SttPieceVolumeWeight = val.SttPieceVolumeWeight
		responseSttPieces.SttPieceWeight = fmt.Sprintf("%v/%v", shared.TruncateFloat(val.SttPieceGrossWeight, 2), shared.TruncateFloat(sttDetail.SttGrossWeight, 2))
		responseSttPieces.SttPieceCount = fmt.Sprintf("%v/%v", i+1, len(sttPieces))
		responseSttPieces.SttPieceDimension = fmt.Sprintf("%v/%v/%v", val.SttPieceLength, val.SttPieceWidth, val.SttPieceHeight)
		responseSttPieces.SttPieceNo = val.SttPieceNo

		responseSttPiecesArray = append(responseSttPiecesArray, responseSttPieces)
	}
	// get total surcharge
	sttTotalSurcharge := sttDetail.SttDocumentSurchargeRate + sttDetail.SttCommoditySurchargeRate + sttDetail.SttHeavyweightSurchargeRate + woodpackingRate

	// Get shipment source
	prefix := shared.GetShipmentSource(sttDetail.SttShipmentID)

	if sttDetail.SttNoRefExternal != `` {
		externalRef = sttDetail.SttNoRefExternal
	}

	// INTERPACK product type
	destinationCountryCode := ""
	destinationCurrencyCode := ""

	if sttDetail.SttProductType == model.INTERPACK && !sttDetail.SttIsCOD {
		if bookedByCountry != model.CountryMY {
			//Check Country Code
			destinationCity, err := c.cityRepo.Get(ctx, sttDetail.SttDestinationCityID, credentialRestAPI.Token)
			if err != nil {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Destination City Not Found",
					"id": "Kota tujuan tidak ditemukan",
				})
			}

			destinationCountry, err := c.countryRepo.Get(ctx, destinationCity.CountryID, credentialRestAPI.Token)
			if err != nil {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Destination Country Not Found",
					"id": "Negara tujuan tidak ditemukan",
				})
			}

			destinationCountryCode = destinationCountry.Code
			destinationCurrencyCode = destinationCountry.Currency

			exchangeRate, err = c.exchangeRateRepo.SelectExchangeRate(ctx, model.ViewExchangeRateRequest{
				From:  destinationCurrencyCode,
				To:    model.IDR,
				Page:  1,
				Limit: 1,
			}, req.Token)
			if err != nil {
				return nil, errQueryDB
			}
			isExchangeRateEmpty := (exchangeRate != nil && len(exchangeRate.Data) < 1)
			if exchangeRate == nil || isExchangeRateEmpty {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Exchange Rate Not Found",
					"id": "Exchange Rate tidak ditemukan",
				})
			}
		}
		commodityDescription = commodity.Data.Description
	}

	detailResiResponse = c.mapDetailResiResponse(sttDetail, sttMeta)
	detailResiResponse.SttEstimateSla = estimateSla
	detailResiResponse.SttShipmentSource = prefix
	detailResiResponse.SttNoRefExternal = externalRef
	detailResiResponse.SttWoodpackingAfterDiscount = woodpackingRate
	detailResiResponse.SttWoodpackingRate = woodpackingRate
	detailResiResponse.SttCommodityName = commodity.Data.CommodityName
	detailResiResponse.IsDangerousGoods = commodity.Data.IsDangerousGoods
	detailResiResponse.SttCommodityIsQuarantine = commodity.Data.CommodityIsQuarantine
	detailResiResponse.SttCommodityDescription = commodityDescription
	detailResiResponse.SttInsuranceRate = insuranceRate
	detailResiResponse.SttTotalSurcharge = sttTotalSurcharge
	detailResiResponse.DetailSttPiecesResponse = responseSttPiecesArray
	detailResiResponse.LastStatusSttReturn = reverseLastStatus
	detailResiResponse.LastStatusBeforeSttReturn = lastStatusBeforeSttReturn
	detailResiResponse.IsReverseJourneyAfterCancel = isReverseJourneyAfterCancel
	detailResiResponse.SttIsWoodPacking = sttIsWoodPacking
	detailResiResponse.SttDestinationCountryCode = destinationCountryCode
	detailResiResponse.CurrencyCode = destinationCurrencyCode
	detailResiResponse.CurrencyRate = func() float64 {
		if exchangeRate != nil && len(exchangeRate.Data) > 0 {
			currencyRate := math.Round(exchangeRate.Data[0].ExchangeRate*100) / 100
			return currencyRate
		}
		return 0
	}()

	detailResiResponse.SttBookedByCountry = bookedByCountry
	detailResiResponse.SttGoodsEstimatePrice = func() float64 {
		if exchangeRate != nil && len(exchangeRate.Data) > 0 {
			goodsEstimatePriceUSD, _ := strconv.ParseFloat(fmt.Sprintf("%.2f", sttDetail.SttGoodsEstimatePrice/exchangeRate.Data[0].ExchangeRate), 64)
			return goodsEstimatePriceUSD
		}

		if bookedByCountry == model.CountryMY {
			return sttDetail.SttGoodsEstimatePrice
		}
		return 0
	}()

	if sttMeta != nil && len(sttMeta.ListDiscount) > 0 {
		tarifAfterDiscount := sttMeta.ListDiscount[len(sttMeta.ListDiscount)-1]
		detailResiResponse.SttWoodpackingAfterDiscount = tarifAfterDiscount.Tariff.WoodpackingRates
		detailResiResponse.SttDocumentSurchargeAfterDiscount = tarifAfterDiscount.Tariff.DocumentSurcharge
		detailResiResponse.SttCommoditySurchargeAfterDiscount = tarifAfterDiscount.Tariff.CommoditySurcharge
		detailResiResponse.SttHeavyweightSurchargeAfterDiscount = tarifAfterDiscount.Tariff.HeavyWeightSurcharge
	}

	isShipmentExists := sttDetail.SttShipmentID != ``
	isShipmentC1 := isShipmentExists && shared.GetPrefixShipmentID(sttDetail.SttShipmentID) == model.C1
	isShipmentC2 := isShipmentExists && shared.GetPrefixShipmentID(sttDetail.SttShipmentID) == model.C2
	isShipmentCorporate := isShipmentC1 || isShipmentC2
	isShipmentCOD := isShipmentExists && sttDetail.SttIsCOD
	if isShipmentCOD && isShipmentCorporate {
		shipment, err := c.shipmentRepo.Get(selfCtx, &model.ShipmentViewParams{
			ShipmentAlgoID: sttDetail.SttShipmentID,
		})
		isShipmentEmpty := err != nil || shipment == nil || shipment.ShipmentPackets == nil || len(shipment.ShipmentPackets) == 0
		if isShipmentEmpty {
			return nil, errShipmentDataNotFound
		}
		if shipment.ShipmentMeta != nil {

			shipmentMetaStruct := shipment.ShipmentMetaToStruct()
			if shipmentMetaStruct != nil {
				detailResiResponse.CodHandling = shipmentMetaStruct.CodHandling
			}
		}
	}

	detailResiResponse = setSttMetaData(detailResiResponse, sttMeta)
	if sttMeta != nil && sttMeta.RetailTariff != nil {
		tariff := sttMeta.RetailTariff
		detailResiResponse.SttIsPromo = tariff.IsPromo

		// if there is no promo applied, price after discount will be equal to price without discount
		detailResiResponse.SttPublishRateAfterDiscount = sttDetail.SttPublishRate
		detailResiResponse.SttShippingSurchargeRateAfterDiscount = sttDetail.SttShippingSurchargeRate
		detailResiResponse.SttOriginDistrictRateAfterDiscount = sttDetail.SttOriginDistrictRate
		detailResiResponse.SttDestinationDistrictRateAfterDiscount = sttDetail.SttDestinationDistrictRate
		detailResiResponse.SttCODFeeAfterDiscount = sttDetail.SttCODFee
		detailResiResponse.SttInsuranceRatesAfterDiscount = insuranceRate
		detailResiResponse.SttTotalSurchargeAfterDiscount = sttTotalSurcharge
		detailResiResponse.SttTotalAmountBeforeDiscount = sttDetail.SttTotalAmount

		// if there is promo applied, price after discount will use price after discount
		detailResiResponse = setTariff(detailResiResponse, tariff)

	}

	shipmentPrefix := ``
	if sttDetail.SttShipmentID != `` {
		shipmentPrefix = shared.GetPrefixShipmentID(sttDetail.SttShipmentID)
	}

	if req.AccountType != model.CLIENT && model.IsValidShipmentPrefixCA[shipmentPrefix] && sttMeta != nil && sttMeta.ClientTariff != nil {
		tariff := sttMeta.ClientTariff.AfterDiscount
		detailResiResponse.SttIsPromo = tariff.IsPromo
		detailResiResponse.SttPublishRateAfterDiscount = tariff.PublishRate
		detailResiResponse.SttShippingSurchargeRateAfterDiscount = tariff.ShippingSurchargeRate
		detailResiResponse.SttOriginDistrictRateAfterDiscount = tariff.OriginDistrictRate
		detailResiResponse.SttDestinationDistrictRateAfterDiscount = tariff.DestinationDistrictRate
		detailResiResponse.SttCODFeeAfterDiscount = tariff.CodFee
		detailResiResponse.SttInsuranceRatesAfterDiscount = tariff.InsuranceRates
		detailResiResponse.SttTotalSurchargeAfterDiscount = tariff.CommoditySurcharge + tariff.DocumentSurcharge + tariff.HeavyWeightSurcharge + tariff.WoodpackingRates
		detailResiResponse.SttTotalAmountBeforeDiscount = sttMeta.ClientTariff.BeforeDiscount.TotalTariff
		detailResiResponse.SttTotalAmount = tariff.TotalTariff
	}

	// get origin city
	originCity, err := c.cityRepo.Get(ctx, sttDetail.SttOriginCityID, credentialRestAPI.Token)
	if err != nil {
		logger.E(err)
		return nil, errQueryDB
	}

	// get detination city
	destinationCity, err := c.cityRepo.Get(ctx, sttDetail.SttDestinationCityID, credentialRestAPI.Token)
	if err != nil {
		logger.E(err)
		return nil, errQueryDB
	}

	if originCity != nil && destinationCity != nil {
		detailResiResponse.SttOriginCityName = originCity.Name
		detailResiResponse.SttDestinationCityName = destinationCity.Name
		detailResiResponse.SttOriginCityTransit = originCity.OriginTransit
		detailResiResponse.SttDestinationCityTransit = destinationCity.DestinationTransit

		if originCity.FreeTradeZone == model.FTZ && destinationCity.FreeTradeZone == model.NONFTZ {
			detailResiResponse.SttBMTaxRate = fmt.Sprintf("%.f", sttDetail.SttBMTaxRate)
			detailResiResponse.SttPPNTaxRate = fmt.Sprintf("%.f", sttDetail.SttPPNTaxRate)
			detailResiResponse.SttPPHTaxRate = fmt.Sprintf("%.f", sttDetail.SttPPHTaxRate)

			if bookedByCountry == model.CountryMY {
				detailResiResponse.SttBMTaxRate = fmt.Sprint(sttDetail.SttBMTaxRate)
				detailResiResponse.SttPPNTaxRate = fmt.Sprint(sttDetail.SttPPNTaxRate)
				detailResiResponse.SttPPHTaxRate = fmt.Sprint(sttDetail.SttPPHTaxRate)
			}
		}
	}

	// get origin ursa code
	if originDistrict, _ := c.districtRepo.GetByCode(ctx, &model.CredentialRestAPI{
		Token: credentialRestAPI.Token,
	}, sttDetail.SttOriginDistrictID); originDistrict != nil && originDistrict.Data.City != nil {
		detailResiResponse.SttOriginDistrictName = fmt.Sprintf("%s, %s", originDistrict.Data.Name, originDistrict.Data.City.Name)
		detailResiResponse.SttOriginDistrictUrsaCode = originDistrict.Data.UrsaCode
	}

	// get district name
	if destinationDistrict, _ := c.districtRepo.GetByCode(ctx, &model.CredentialRestAPI{
		Token: credentialRestAPI.Token,
	}, sttDetail.SttDestinationDistrictID); destinationDistrict != nil && destinationDistrict.Data.City != nil {
		detailResiResponse.SttDestinationDistrictName = fmt.Sprintf("%s, %s", destinationDistrict.Data.Name, destinationDistrict.Data.City.Name)
		detailResiResponse.SttDestinationDistrictUrsaCode = destinationDistrict.Data.UrsaCode
		detailResiResponse.SttDistrictUrsaCode = destinationDistrict.Data.UrsaCode
		detailResiResponse.SttDistrictType = destinationDistrict.Data.Type

		message := func(text string) string {
			return fmt.Sprintf("Paket Diambil di kantor %s Setempat", text)
		}

		switch destinationDistrict.Data.Type {
		case model.TYPE_PTPOS:
			detailResiResponse.SttDestinationDistrictType = `P.`
		case model.TYPE_PTPOS_LANJUTAN:
			detailResiResponse.SttDestinationDistrictType = `PL.`
			detailResiResponse.SttInformationDistrictType = message("PT POS")
		case model.TYPE_VENDOR:
			detailResiResponse.SttDestinationDistrictType = destinationDistrict.Data.VendorCode
		case model.TYPE_VENDOR_LANJUTAN:
			detailResiResponse.SttDestinationDistrictType = destinationDistrict.Data.VendorCode
			detailResiResponse.SttInformationDistrictType = message(destinationDistrict.Data.VendorCode)
		}

		if model.IsDistrictTypeVendor[destinationDistrict.Data.Type] {
			detailResiResponse.SttVendorCode = destinationDistrict.Data.VendorCode
			detailResiResponse.SttVendorName = destinationDistrict.Data.VendorCode

			if model.IsValidNinjaVendorType[destinationDistrict.Data.VendorCode] {
				// TODO uncomment when genesis ninja prd account is ready
				// detailResiResponse.SttVendorReferenceNo = model.NinjaGenesisPrefix + sttDetail.SttNo
				// TODO comment when genesis ninja prd account is ready
				detailResiResponse.SttVendorReferenceNo = model.NinjaElexysPrefix + sttDetail.SttNo

			}
			if model.IsValidJNEVendorType[destinationDistrict.Data.VendorCode] {
				detailResiResponse.SttVendorReferenceNo = c.GetReferenceNoJne(selfCtx, c.cfg.JNEBookingVersion(), sttDetail.SttNo)
			}
		}
	}

	for _, shipperTicketCode := range sttMeta.OtherShipperTicketCode {
		detailResiResponse.SttOtherShipperTicketCode = append(detailResiResponse.SttOtherShipperTicketCode, stt.SttOtherShipperTicketCode{
			TicketCode: shipperTicketCode.TicketCode,
			Shipper:    shipperTicketCode.Shipper,
		})
	}

	isFromCA := model.MappingShipmentPrefixCustomerName[shipmentPrefix] == model.CUSTOMERAPPS || sttDetail.SttBilledTo == "CUSTOMER APPS"
	isRetail := model.IsPrefixSttRetail[sttDetail.SttNo[:2]]
	isBookedNameEmpty := sttDetail.SttBookedForName == ``
	isACRAndCRR := sttDetail.SttBilledTo == model.ACR || sttDetail.SttBilledTo == model.CCR
	isFromCARetailAndACRCCR := isFromCA || isRetail || isACRAndCRR

	if !(isFromCARetailAndACRCCR) || isBookedNameEmpty {
		if sttDetail.SttClientID > 0 {
			sttClient, err := c.clientRepo.GetByID(ctx, sttDetail.SttClientID, credentialRestAPI.Token)
			if err != nil || sttClient == nil {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Client Not Found",
					"id": "Client Tidak Ditemukan",
				})
			}

			if isBookedNameEmpty {
				sttDetail.SttBookedForName = sttClient.Data.ClientCompanyName
			}

			if !(isFromCA || isRetail) {
				var clientName string
				if sttClient.Data.ClientPartnerID > 0 {
					clientName = sttClient.Data.ClientParentName
				} else {
					clientName = sttClient.Data.ClientCompanyName
				}
				detailResiResponse.SttClientName = clientName
			}
		}
	}

	detailResiResponse.SttClientName = c.getClientName(detailResiResponse.SttClientName, sttDetail.SttBookedName, isFromCARetailAndACRCCR, isReverseRetail)
	detailResiResponse.IsPriority = c.isSttPriority(sttMeta)
	detailResiResponse.SttBookedForName = sttDetail.SttBookedForName
	// Get data stt elexys

	sttElexys, err := c.sttElexysRepo.Get(ctx, &model.SttElexysViewParam{SttNo: sttDetail.SttNo})
	if err != nil {
		return nil, errQueryDB
	}

	detailResiResponse.SttElexys = sttElexys.GenerateSttElexysResponse()

	// return infomation related reverse journey (if any)
	if sttMeta.DetailSttReverseJourney != nil {
		detailResiResponse.DetailSttReverseJourney = &stt.DetailSttReverseJourneyResi{
			RootReverseSttNo:         sttMeta.DetailSttReverseJourney.RootReverseSttNo,
			RootReverseShipmentID:    sttMeta.DetailSttReverseJourney.RootReverseShipmentID,
			RootReverseLastStatusStt: sttMeta.DetailSttReverseJourney.RootReverseLastStatusStt,
		}

		if sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` && (shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID) == model.C1 || shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID) == model.C2) {
			shipment, err := c.shipmentRepo.Get(selfCtx, &model.ShipmentViewParams{
				ShipmentAlgoID: sttMeta.DetailSttReverseJourney.RootReverseShipmentID,
			})
			if err != nil || shipment == nil || shipment.ShipmentPackets == nil || len(shipment.ShipmentPackets) == 0 {
				return nil, errShipmentDataNotFound
			}
			if shipment.ShipmentMeta != nil {
				shipmentMetaStruct := shipment.ShipmentMetaToStruct()
				if shipmentMetaStruct != nil {
					detailResiResponse.DetailSttReverseJourney.CodHandling = shipmentMetaStruct.CodHandling
				}
			}
		}
	}

	detailResiResponse.SttFlag = c.getSttFlag(selfCtx, sttDetail, reverseLastStatus)
	// get campaign stt quote
	campaignStt, err := c.getCampaignSttQuote(selfCtx, sttDetail.SttBookedAt, shared.UTC7(c.timeRepo.Now(time.Now())))
	if err != nil {
		return nil, err
	}
	detailResiResponse.CampaignSttQuote = campaignStt

	if detailResiResponse.SttProductType == model.INTERPACK {
		c.buildSttCIPL(detailResiResponse, sttMeta)
	}
	c.buildSttFtzCIPL(detailResiResponse, sttMeta)

	c.sttRepo.CreateCacheResi(ctx, int(sttDetail.SttID), &detailResiResponse)

	detailResiResponse.SttFtzRecipientEmail = sttMeta.SttFtzRecipientEmail

	return detailResiResponse, nil
}

func setTariff(detailResiResponse *stt.DetailResiResponse, tariff *model.CheckTariffBase) *stt.DetailResiResponse {
	if !detailResiResponse.SttIsPromo {
		return detailResiResponse
	}
	detailResiResponse.SttPublishRateAfterDiscount = tariff.PublishRateAfterDiscount
	detailResiResponse.SttShippingSurchargeRateAfterDiscount = tariff.ShippingSurchargeRateAfterDiscount
	detailResiResponse.SttOriginDistrictRateAfterDiscount = tariff.OriginDistrictRateAfterDiscount
	detailResiResponse.SttDestinationDistrictRateAfterDiscount = tariff.DestinationDistrictRateAfterDiscount
	detailResiResponse.SttCODFeeAfterDiscount = tariff.CodFeeAfterDiscount
	detailResiResponse.SttInsuranceRatesAfterDiscount = tariff.InsuranceRatesAfterDiscount
	detailResiResponse.SttTotalSurchargeAfterDiscount = tariff.TotalSurchargeAfterDiscount
	detailResiResponse.SttTotalAmountBeforeDiscount = tariff.TotalTariffBeforeDiscount
	detailResiResponse.SttTotalAmount = tariff.TotalTariffAfterDiscount
	return detailResiResponse
}

func setSttMetaData(detailResiResponse *stt.DetailResiResponse, sttMeta *model.SttMeta) *stt.DetailResiResponse {
	if sttMeta != nil {
		detailResiResponse.SttFtzRecipientEmail = sttMeta.SttFtzRecipientEmail
		detailResiResponse.SttRecipientEmail = sttMeta.SttRecipientEmail
	}
	return detailResiResponse
}

func (c *sttCtx) buildSttFtzCIPL(response *stt.DetailResiResponse, sttMeta *model.SttMeta) {
	sttFtzCIPL := make([]stt.SttCIPL, 0)

	for _, ftzCipl := range sttMeta.SttFtzCIPL {
		data := stt.SttCIPL{
			CommodityHsCode: ftzCipl.CommodityHsCode,
			ItemDetail:      ftzCipl.ItemDetail,
			ItemDetailEn:    ftzCipl.ItemDetailEn,
			ItemPrice:       float64(ftzCipl.ItemPrice),
			Quantity:        ftzCipl.Quantity,
		}

		sttFtzCIPL = append(sttFtzCIPL, data)
	}

	response.SttFtzCIPL = sttFtzCIPL
}

func (c *sttCtx) buildSttCIPL(response *stt.DetailResiResponse, sttMeta *model.SttMeta) {
	sttCIPL := make([]stt.SttCIPL, 0)
	for _, cipl := range sttMeta.SttCIPL {
		data := stt.SttCIPL{
			CommodityHsCode: cipl.CommodityHsCode,
			ItemDetail:      cipl.ItemDetail,
			ItemDetailEn:    cipl.ItemDetailEn,
			Quantity:        cipl.Quantity,
			ItemPrice:       float64(cipl.ItemPrice),
		}

		//Convert currency
		if response.CurrencyRate > 0 {
			newPrice := float64(cipl.ItemPrice) / response.CurrencyRate
			data.ItemPrice = math.Round(newPrice*100) / 100
		}

		sttCIPL = append(sttCIPL, data)
	}

	response.SttCIPL = sttCIPL
}

func (c *sttCtx) mapDetailResiResponse(sttDetail *model.Stt, sttMeta *model.SttMeta) *stt.DetailResiResponse {
	return &stt.DetailResiResponse{
		SttID:                    sttDetail.SttID,
		SttNo:                    sttDetail.SttNo,
		SttProductType:           sttDetail.SttProductType,
		SttCreatedAt:             sttDetail.SttCreatedAt,
		SttOriginCityID:          sttDetail.SttOriginCityID,
		SttDestinationCityID:     sttDetail.SttDestinationCityID,
		SttDestinationDistrictID: sttDetail.SttDestinationDistrictID,
		SttIdentityNumber:        sttMeta.SttIdentityNumber,

		SttPostalCodeDestination: sttDetail.SttMetaToStruct().PostalCodeDestination,

		SttSenderName:           sttDetail.SttSenderName,
		SttSenderPhone:          sttDetail.SttSenderPhone,
		SttSenderAddress:        sttDetail.SttSenderAddress,
		SttRecipientName:        sttDetail.SttRecipientName,
		SttRecipientAddress:     sttDetail.SttRecipientAddress,
		SttRecipientAddressType: sttDetail.SttRecipientAddressType.Value(),
		SttRecipientPhone:       sttDetail.SttRecipientPhone,

		SttDocumentSurchargeAfterDiscount:    sttDetail.SttDocumentSurchargeRate,
		SttCommoditySurchargeAfterDiscount:   sttDetail.SttCommoditySurchargeRate,
		SttHeavyweightSurchargeAfterDiscount: sttDetail.SttHeavyweightSurchargeRate,

		SttCommodityCode:    sttDetail.SttCommodityCode,
		SttBookedName:       sttDetail.SttBookedName,
		SttIsCOD:            sttDetail.SttIsCOD,
		SttIsDFOD:           sttDetail.SttIsDFOD,
		SttIsDO:             sttDetail.SttIsDO,
		SttCODAmount:        sttDetail.SttCODAmount,
		SttCODFee:           sttDetail.SttCODFee,
		SttTotalPiece:       sttDetail.SttTotalPiece,
		SttChargeableWeight: sttDetail.SttChargeableWeight,
		SttBMTaxRate:        `-`,
		SttPPNTaxRate:       `-`,
		SttPPHTaxRate:       `-`,

		SttDocumentSurchargeRate:    sttDetail.SttDocumentSurchargeRate,
		SttCommoditySurchargeRate:   sttDetail.SttCommoditySurchargeRate,
		SttHeavyweightSurchargeRate: sttDetail.SttHeavyweightSurchargeRate,

		SttPublishRate:             sttDetail.SttPublishRate,
		SttShippingSurchargeRate:   sttDetail.SttShippingSurchargeRate,
		SttOriginDistrictRate:      sttDetail.SttOriginDistrictRate,
		SttDestinationDistrictRate: sttDetail.SttDestinationDistrictRate,

		SttOriginDistrictID: sttDetail.SttOriginDistrictID,

		SttTotalAmount:               sttDetail.SttTotalAmount, //total amount
		SttTotalAmountBeforeDiscount: sttDetail.SttTotalAmount, //total amount

		SttGrossWeight:  sttDetail.SttGrossWeight,
		SttVolumeWeight: sttDetail.SttVolumeWeight,
		SttShipmentID:   sttDetail.SttShipmentID,
		SttClientID:     sttDetail.SttClientID,

		SttGoodsPrice: sttDetail.SttGoodsEstimatePrice,
		SttReturnFee: func(sm *model.SttMeta) float64 {
			if sm != nil {
				return sm.TotalTariffReturn * 0.5
			}
			return sm.TotalTariffReturn
		}(sttMeta),
	}
}

func (c *sttCtx) isSttPriority(meta *model.SttMeta) bool {
	isPriorityTier := c.cfg.IsSTTPriorityTier() && meta.GetIsPriorityTier()
	isPrioritySubscription := meta.GetIsPrioritySubscription()
	return isPriorityTier || isPrioritySubscription
}

func (c *sttCtx) checkIsPriorityTier(selfCtx context.Context, partnerID int, token string) (bool, error) {
	if !c.cfg.IsSTTPriorityTier() {
		return false, nil
	}

	var tier string

	key := fmt.Sprintf("%v:%v", model.CachePartnerTier, partnerID)
	c.cacheRepo.GetCache(selfCtx, key, tier)

	if tier != "" {
		return model.IsPriorityTier[strings.ToLower(tier)], nil
	}

	accountToken, err := c.accountRepo.GetTokenGenesis(selfCtx)
	if err != nil || accountToken == nil {
		return false, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to get token genesis",
			"id": "Gagal Mendapat Token Genesis",
		})
	}

	optionPartners := map[string]interface{}{
		"use_cache":             false,
		"no_attribute":          true,
		"is_show_tiering_level": true,
	}
	partner, err := c.partnerRepo.GetByIDWithOptions(selfCtx, partnerID, accountToken.Data.Token, optionPartners)
	if err != nil || partner == nil {
		return false, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to get Partner data",
			"id": "Gagal mendapatkan data Partner",
		})
	}

	tier = partner.Data.PartnerPosTieringLevel

	go c.cacheRepo.CreateCache(selfCtx, key, tier, c.getTierDuration())

	return model.IsPriorityTier[strings.ToLower(tier)], nil
}

func (c *sttCtx) getTierDuration() time.Duration {
	now := time.Now().UTC()
	currentTime := now
	targetTime := time.Date(now.Year(), now.Month(), now.Day(), 21, 0, 0, 0, time.UTC)

	// if target time already passed, add 1 day
	if currentTime.After(targetTime) {
		targetTime = targetTime.AddDate(0, 0, 1)
	}
	return targetTime.Sub(currentTime)
}

func (c *sttCtx) getSttDetailRetryMaster(ctx context.Context, params *model.SttViewDetailParams) (*model.Stt, error) {
	sttDetail, err := c.sttRepo.Get(ctx, params)
	if err != nil {
		return nil, err
	}

	if sttDetail != nil {
		return sttDetail, nil
	}

	params.GetFromMaster = true
	sttDetail, err = c.sttRepo.Get(ctx, params)

	if err != nil {
		return nil, err
	}
	return sttDetail, nil
}

func (c *sttCtx) getSttPieceRetryMaster(ctx context.Context, params *model.SttPiecesViewParam) ([]model.SttPiece, error) {
	sttPieces, err := c.sttPiecesRepo.Select(ctx, params)
	if len(sttPieces) == 0 || errors.Is(err, dbr.ErrNotFound) {
		params.GetFromMaster = true
		sttPieces, err = c.sttPiecesRepo.Select(ctx, params)
	}
	if err != nil {
		return sttPieces, err
	}

	return sttPieces, nil
}

func (c *sttCtx) getSttOptionRetryMaster(ctx context.Context, params *model.SttOptionalRate) ([]model.SttOptionalRate, error) {
	sttOptionalRates, err := c.sttOptionalRateRepo.Select(ctx, params)
	if len(sttOptionalRates) == 0 || errors.Is(err, dbr.ErrNotFound) {
		params.GetFromMaster = true
		sttOptionalRates, err = c.sttOptionalRateRepo.Select(ctx, params)
	}
	if err != nil {
		return sttOptionalRates, err
	}
	return sttOptionalRates, nil
}

func (c *sttCtx) getSttVendor(ctx context.Context, sttNo string) (string, error) {
	sttVendors, err := c.sttVendorRepo.SelectDetail(ctx, &model.SttVendorViewParams{
		SttNo: sttNo,
	})
	if err != nil {
		return "", shared.ERR_UNEXPECTED_DB
	}

	if len(sttVendors) == 0 {
		return "", nil
	}

	return sttVendors[0].SttVendorReferenceNo, nil
}

func (c *sttCtx) getSttFlag(ctx context.Context, params *model.Stt, reverseLastStatus string) []string {
	sttFlag := make([]string, 0)
	if params.SttRecipientAddressType.String == model.AddressTypeOffice {
		sttFlag = append(sttFlag, model.SttFlagKT)
	}

	if params.SttIsDO {
		sttFlag = append(sttFlag, model.SttFlagDO)
	}

	if params.SttProductType == model.JUMBOPACKH2H {
		sttFlag = append(sttFlag, model.SttFlagH2H)
	}

	switch shared.GetPrefixSttNo(params.SttNo) {
	case model.PrefixAutoReturnReverseJourneyMissbokingReroute:
		sttFlag = append(sttFlag, model.SttFlagRMB)
	case model.PrefixAutoReverseJourney, model.PrefixAutoClientCCR, model.PrefixAutoCAACR, model.PrefixAutoReturnReverseJourney:
		switch reverseLastStatus {
		case model.RTS:
			sttFlag = append(sttFlag, model.SttFlagRTS)
		case model.RTSHQ:
			sttFlag = append(sttFlag, model.SttFlagRHQ)
		case model.REROUTE:
			sttFlag = append(sttFlag, model.SttFlagRMR)
		case model.CNX:
			sttFlag = append(sttFlag, model.SttFlagCNX)
		}
	}

	return sttFlag
}

func (c *sttCtx) getCampaignSttQuote(ctx context.Context, sttBookedAt time.Time, now time.Time) (string, error) {
	campaignSttQuotes, err := c.campaignSttQuoteRepo.GetRandomCampaignSttQuote(ctx, sttBookedAt, now)
	if err != nil {
		return "", shared.ERR_UNEXPECTED_DB
	}

	return campaignSttQuotes.CsqQuote, nil
}

func (c *sttCtx) validateClientAccount(ctx context.Context, sttDetail *model.Stt, req stt.DetailResiRequest) (bool, error) {
	var clientParentID int
	if sttDetail.SttBookedForID != req.AccountRefID {
		// Fetch client if booked for ID doesn't match
		client, err := c.clientRepo.GetByID(ctx, sttDetail.SttBookedForID, req.Token)
		if err != nil {
			return false, shared.ERR_UNEXPECTED_DB
		}

		if client == nil {
			return false, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Client Not Found",
				"id": "Client Tidak Ditemukan",
			})
		}
		clientParentID = client.Data.ClientParentID
	}

	// Check if client parent ID matches the account reference ID
	accountIdIsEqual := sttDetail.SttBookedForID == req.AccountRefID || clientParentID == req.AccountRefID
	if !(accountIdIsEqual && sttDetail.SttBookedForType == model.CLIENT) {
		return true, nil
	}

	// If none of the conditions are satisfied, return false
	return false, nil
}

func (c *sttCtx) checkSttReverseJourney(sttDetail *model.Stt, sttMeta *model.SttMeta) (isReverseJourneyAfterCancel bool, lastStatusBeforeSttReturn string, reverseLastStatus string, isReverseRetail bool) {
	prefixStt := shared.GetPrefixSttNo(sttDetail.SttNo)
	prefixReverseStt66 := ``
	detailReverseJourney := sttMeta.DetailSttReverseJourney
	reverseLastStatus = detailReverseJourney.ReverseJourneyStatusStt
	if reverseLastStatus == model.REROUTE {
		lastStatusBeforeSttReturn = detailReverseJourney.ReverseLastStatusStt
	}
	isAllowed := prefixStt == model.PrefixAutoReverseJourney && model.IsAllowedStatusRegenerateSTT[detailReverseJourney.ReverseLastStatusStt]
	if isAllowed && reverseLastStatus == model.CNX {
		isReverseJourneyAfterCancel = true
	}
	prefixReverseStt66 = c.getRootPrefix(detailReverseJourney)
	prefixReverseStt := shared.GetPrefixSttNo(detailReverseJourney.ReverseSttNo)
	isReverseRetailPartner := model.IsReverseRetailPartner[prefixReverseStt]
	isReverseRTSHQ := prefixReverseStt66 == model.PrefixAutoPartner || prefixReverseStt66 == model.PrefixPartner
	isReverseRetail = isReverseRetailPartner || isReverseRTSHQ
	return
}

func (c *sttCtx) getRootPrefix(sttDetail *model.DetailSttReverseJourney) string {
	prefixReverseStt := ""
	rootStt := sttDetail.RootReverseSttNo
	if rootStt != `` {
		prefixReverseStt = shared.GetPrefixSttNo(rootStt)
	}
	return prefixReverseStt
}

func (c *sttCtx) GetReferenceNoJne(ctx context.Context, version int, sttNo string) string {
	if version == 1 {
		referenceNo, err := c.getSttVendor(ctx, sttNo)
		if err != nil {
			return ``
		}
		return referenceNo
	}

	return shared.GetSttNoWithoutLP(sttNo)
}

func (c *sttCtx) getClientName(currentSttClientName string, sttBookedName string, isFromCARetailAndACRCCR bool, isReverseRetail bool) string {
	sttClientName := currentSttClientName
	if isFromCARetailAndACRCCR || isReverseRetail {
		sttClientName = sttBookedName
	}
	return sttClientName
}

func (c *sttCtx) getPartnerTier(selfCtx context.Context, partnerID int) (partnerTier string, err error) {
	if !c.cfg.IsSTTPriorityTier() {
		return
	}

	key := fmt.Sprintf("%v:%v", model.CachePartnerTier, partnerID)
	c.cacheRepo.GetCache(selfCtx, key, partnerTier)

	if partnerTier != "" {
		return partnerTier, nil
	}

	accountToken, err := c.accountRepo.GetTokenGenesis(selfCtx)
	if err != nil || accountToken == nil {
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to get token genesis",
			"id": "Gagal Mendapat Token Genesis",
		})
		return
	}

	optionPartners := map[string]interface{}{
		"use_cache":             false,
		"no_attribute":          true,
		"is_show_tiering_level": true,
	}
	partner, err := c.partnerRepo.GetByIDWithOptions(selfCtx, partnerID, accountToken.Data.Token, optionPartners)
	if err != nil || partner == nil {
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to get Partner data",
			"id": "Gagal mendapatkan data Partner",
		})
		return
	}

	partnerTier = partner.Data.PartnerPosTieringLevel

	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.cacheRepo.CreateCache(goCtx, key, partnerTier, c.getTierDuration())
	})

	return
}
