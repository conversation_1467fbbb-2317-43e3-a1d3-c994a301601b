package usecase

import (
	"context"
	"fmt"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/stt"
	"strconv"
)

func (c *sttCtx) getValidatePartnerCreateSTT(ctx context.Context, params *stt.CreateSttRequest, sttData stt.Stt) (*model.Partner, error) {
	partnerBooking, err := c.partnerRepo.GetByID(ctx, params.AccountRefID, params.Token)
	if err != nil || partnerBooking == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner Not Found",
			"id": "Partner Tidak Ditemukan",
		})
	}
	if partnerBooking.Data.PartnerLocation == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner is not yet mapped with location",
			"id": "Partner belum dipetakan dengan lokasi",
		})
	}
	if sttData.SttProductType == model.VIPPACK && !partnerBooking.Data.PartnerIsAllowBookVIPPACK {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Product type is invalid",
			"id": "Tipe produk tidak valid",
		})
	}
	return partnerBooking, nil
}
func (c *sttCtx) validateProductTypeAndSourceCreateSTT(sttData stt.Stt, params *stt.CreateSttRequest, errInvalid error) error {
	if sttData.SttProductType == model.VIPPACK {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Product type is invalid",
			"id": "Tipe produk tidak valid",
		})
	}
	if params.Source == model.ALGO {
		return errInvalid
	}
	return nil
}

func (c *sttCtx) validateTariffAccountCreateSTT(ctx context.Context, params *stt.CreateSttRequest) error {
	if err := params.ElexysTariff.Validate(); err != nil && c.cfg.IsElexysConfig() {
		return err
	}

	validate, err := c.accountRepo.ValidateAccount(ctx, params.Token, true)
	if err != nil {
		return err
	}
	if !validate.IsAllow {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"error_type": validate.ErrorType,
			"en":         "Account is not eligible to create STT",
			"id":         "Akun tidak diperbolehkan untuk membuat STT",
		})
	}
	return nil
}

func (c *sttCtx) validateSenderPhone(ctx context.Context, data stt.Stt, source string) error {
	isSttPrefixCa := data.SttShipmentID != `` && model.IsShipmentPrefixEnableCOD[shared.GetPrefixShipmentID(data.SttShipmentID)]
	if !(source == model.MANUAL || isSttPrefixCa) {
		return nil
	}
	if errCheckPhone := c.checkSenderPhoneFraud(ctx, data.SttSenderPhone, data.SttIsCOD); errCheckPhone != nil {
		return errCheckPhone
	}
	return nil
}

func (c *sttCtx) validateSTTPriceIsCOD(data stt.Stt, config model.CodConfigBase) *shared.MultiStringBadRequestError {
	if data.SttGoodsEstimatePrice < config.CcoMinPrice {
		ccoMinPriceInCurrencyFormat := shared.CurrencyFormatter(strconv.FormatFloat(config.CcoMinPrice, 'f', 0, 64))
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf("Minimum goods price Rp%s", ccoMinPriceInCurrencyFormat),
			"id": fmt.Sprintf("Minimal Harga barang Rp%s", ccoMinPriceInCurrencyFormat),
		})
	}

	if data.SttGoodsEstimatePrice > config.CcoMaxPrice {
		ccoMaxPriceInCurrencyFormat := shared.CurrencyFormatter(strconv.FormatFloat(config.CcoMaxPrice, 'f', 0, 64))
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf("Maximum COD goods price Rp%s", ccoMaxPriceInCurrencyFormat),
			"id": fmt.Sprintf("Harga barang COD maksimum Rp%s", ccoMaxPriceInCurrencyFormat),
		})
	}

	return nil
}

func (c *sttCtx) validateSTTPriceIsCommon(data stt.Stt, config model.CodConfigBase, configName string) *shared.MultiStringBadRequestError {
	if configName == `` {
		configName = `COD`
	}
	if !data.SttIsDFOD && data.SttGoodsEstimatePrice < config.CcoMinPrice {
		ccoMinPriceInCurrencyFormat := shared.CurrencyFormatter(strconv.FormatFloat(config.CcoMinPrice, 'f', 0, 64))
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf("Minimum goods price Rp%s", ccoMinPriceInCurrencyFormat),
			"id": fmt.Sprintf("Minimal Harga barang Rp%s", ccoMinPriceInCurrencyFormat),
		})
	}

	if data.SttGoodsEstimatePrice > config.CcoMaxPrice {
		ccoMaxPriceInCurrencyFormat := shared.CurrencyFormatter(strconv.FormatFloat(config.CcoMaxPrice, 'f', 0, 64))
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf("Maximum %s goods price Rp%s", configName, ccoMaxPriceInCurrencyFormat),
			"id": fmt.Sprintf("Harga barang %s maksimum Rp%s", configName, ccoMaxPriceInCurrencyFormat),
		})
	}

	return nil
}
