package gateway_stt_tracking

import (
	"time"

	"github.com/Lionparcel/hydra/src/model"

	"github.com/Lionparcel/hydra/shared"
	validation "github.com/go-ozzo/ozzo-validation"
)

type SttTrackingBulkRequestV4 struct {
	SttNo         []string `json:"stts" form:"stts"`
	ReferenceID   int
	ReferenceType string
	IsTokopedia   bool   `json:"is_tokopedia"`
	Token         string `json:"-"`
}

type SttTrackingBulkResponseV4 struct {
	Stts []SttTrackingBulkV4 `json:"stts"`
}

type SttTrackingBulkV4 struct {
	SttNo            string         `json:"stt_no"`
	EstimationDate   string         `json:"estimation_date"`
	Sender           PersonObjectV4 `json:"sender"`
	Recipient        PersonObjectV4 `json:"recipient"`
	Origin           string         `json:"origin"`
	Destination      string         `json:"destination"`
	CurrentStatus    string         `json:"current_status"`
	ServiceType      string         `json:"service_type"`
	ProductType      string         `json:"product"`
	Pieces           int            `json:"pieces"`
	GrossWeight      float64        `json:"gross_weight"`
	VolumeWeight     float64        `json:"volume_weight"`
	ChargeableWeight float64        `json:"chargeable_weight"`
	ExternalID       string         `json:"external_id"`
	ShipmentID       string         `json:"shipment_id"`
	TotalAmount      float64        `json:"total_amount"`
	CodValue         float64        `json:"cod_value"`
	RebuttalDex      string         `json:"rebuttal_dex"`
	SttReturnRefNo   string         `json:"stt_return_ref_no"`
	History          []SttHistoryV4 `json:"history"`
}

type PersonObjectV4 struct {
	Name    string `json:"name"`
	Address string `json:"address"`
	Phone   string `json:"phone"`
}

type SttHistoryV4 struct {
	Row               int       `json:"row"`
	StatusCode        string    `json:"status_code"`
	Location          string    `json:"location"`
	City              string    `json:"city"`
	Remarks           string    `json:"remarks"`
	ShortStatus       string    `json:"short_status"`
	Datetime          time.Time `json:"datetime"`
	UpdatedBy         string    `json:"updated_by"`
	UpdatedOn         time.Time `json:"updated_on"`
	Attachment        []string  `json:"attachment"`
	GMT               string    `json:"GMT"`
	CountryCode       string    `json:"country_code"`
	ActorRole         string    `json:"actor_role"`
	ProblemReasonCode string    `json:"problem_reason_code"`

	Proof   ProofSttHistoryV4 `json:"proof"`
	Attempt int               `json:"attempt"`
}

type ProofSttHistoryV4 struct {
	AttachmentSigned []string `json:"attachment_signed"`
	Latitude         float64  `json:"latitude"`
	Longitude        float64  `json:"longitude"`
	Relation         string   `json:"relation"`
	Name             string   `json:"name"`
}

func (c *SttTrackingBulkRequestV4) Validate() error {
	if len(c.SttNo) == 0 {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Stt number cannot be empty",
			"id": "Nomor stt tidak boleh kosong",
		})
	}

	for _, val := range c.SttNo {
		if err := validation.Validate(val, validation.Required); err != nil {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Stt number cannot be empty",
				"id": "Nomor stt tidak boleh kosong",
			})
		}
	}

	return nil
}

func (h *SttHistoryV4) SetProof(historyRemark *model.RemarkPieceHistory, relation model.PodRecipientRelation) {
	var (
		latitude, longitude float64
		attachmentSigneds   = make([]string, 0)
		receiverName        string
	)
	if historyRemark != nil {
		if len(historyRemark.AttachmentSigneds) > 0 {
			attachmentSigneds = historyRemark.AttachmentSigneds
		}
		if len(historyRemark.SttWeightAttachFileSigneds) > 0 {
			attachmentSigneds = historyRemark.SttWeightAttachFileSigneds
		}
		latitude = historyRemark.Latitude
		longitude = historyRemark.Longitude
		receiverName = historyRemark.ReceiverName
	}
	h.Proof = ProofSttHistoryV4{
		AttachmentSigned: attachmentSigneds,
		Latitude:         latitude,
		Longitude:        longitude,
		Relation:         relation.Relation,
		Name:             receiverName,
	}
}
