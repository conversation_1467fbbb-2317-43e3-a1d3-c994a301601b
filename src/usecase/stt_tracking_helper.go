package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/Lionparcel/go-lptool/lputils"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"strings"
)

type reqGenerateSttTrackingType struct {
	isClient  bool
	isEnglish bool
}

func (u *reqGenerateSttTrackingType) SetClientType(cond bool) *reqGenerateSttTrackingType {
	u.isClient = cond
	return u
}

func (u *reqGenerateSttTrackingType) IsClient() bool {
	return u.isClient
}
func (u *reqGenerateSttTrackingType) IsNotClient() bool {
	return !u.isClient
}

func (u *reqGenerateSttTrackingType) SetEnglishType(cond bool) *reqGenerateSttTrackingType {
	u.isEnglish = cond
	return u
}

func (u *reqGenerateSttTrackingType) IsEnglish() bool {
	return u.isEnglish
}
func (u *reqGenerateSttTrackingType) IsNotEnglish() bool {
	return !u.isEnglish
}

type reqGenerateSttTrackingRequestBody struct {
	Ctx                              context.Context
	AccountType                      string
	SttStatus                        string
	SttPieceHistoryID                int
	HistoryIdWithListSttPieceHistory map[int][]model.SttPieceHistory
	Token                            string
	ProductType                      string
	CityDestinationCode              string
	DexCount                         int
	CountryName                      string
	CountryDestName                  string
	HistoryLocation                  string
	HistoryActorName                 string
	HistoryActorRole                 string
}

func (u *reqGenerateSttTrackingRequestBody) GetRemarkPieceHistory(sttStatus string) (historyRemark model.RemarkPieceHistory) {
	for _, val := range u.HistoryIdWithListSttPieceHistory[u.SttPieceHistoryID] {
		isNotHistoryStatusSttRemove := val.HistoryStatus != sttStatus
		isNotMatchHistoryID := val.HistoryID != u.SttPieceHistoryID
		if isNotHistoryStatusSttRemove && isNotMatchHistoryID {
			continue
		}
		if val.HistoryRemark != `` {
			json.Unmarshal([]byte(val.HistoryRemark), &historyRemark)
		}
	}
	return
}

type historyGeneratorFunc func(reqGenerateSttTrackingRequestBody) string
type generatorStatusFunc func(cond reqGenerateSttTrackingType) historyGeneratorFunc

func generateHistory(reqData reqGenerateSttTrackingRequestBody, cond reqGenerateSttTrackingType, f generatorStatusFunc) string {
	if f == nil {
		return ""
	}

	ff := f(cond)
	return ff(reqData)
}

func (c *sttCtx) generateSttTrackingDescription(ctx context.Context, accountType string, resType string, status string, hisID int, statusPieceWithArrayData map[int][]model.SttPieceHistory, token string, productType string, cityDestinationCode string, dexCount int, showHiddenStatus string) string {
	opName := "sttCtx-generateSttTrackingDescription"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	var statusHistory string = ""
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"result": statusHistory, "error": err})
	}()

	countryName := ""
	countyDestName := ""
	cityCountry, err := c.cityRepo.Get(selfCtx, cityDestinationCode, token)
	if err == nil || cityCountry != nil {
		countryName = cityCountry.Country.Name
		countyDestName = cityCountry.Name
	}

	if len(statusPieceWithArrayData[hisID]) < 1 {
		return statusHistory
	}

	historyLocation := statusPieceWithArrayData[hisID][0].HistoryLocation
	historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
	historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole
	if accountType == model.CLIENT {
		return c.generateSttTrackingDescriptionForClient(selfCtx, status, hisID, statusPieceWithArrayData, token, productType, countryName, countyDestName, historyActorName, historyActorRole, showHiddenStatus)
	}

	var condType reqGenerateSttTrackingType
	condType.SetClientType(false).SetEnglishType(false)
	regData := reqGenerateSttTrackingRequestBody{
		Ctx:                              selfCtx,
		AccountType:                      accountType,
		SttStatus:                        status,
		SttPieceHistoryID:                hisID,
		HistoryIdWithListSttPieceHistory: statusPieceWithArrayData,
		Token:                            token,
		ProductType:                      productType,
		HistoryLocation:                  historyLocation,
		CityDestinationCode:              cityDestinationCode,
		DexCount:                         dexCount,
		CountryName:                      countryName,
		CountryDestName:                  countyDestName,
	}

	var statusMapFunc map[string]generatorStatusFunc = map[string]generatorStatusFunc{
		model.BKD:         generatorBkdDescription,
		model.STI:         generatorStiDescription,
		model.PUP:         generatorPupAndPupcDescription,
		model.PUPC:        generatorPupAndPupcDescription,
		model.STISC:       generatorStiScDescription,
		model.STOSC:       generatorStoScDescription,
		model.BAGGING:     c.generatorBaggingDescription,
		model.CARGOPLANE:  c.generatorCargoPlaneDescription,
		model.STTREMOVE:   generatorSttRemoveDescription,
		model.CARGOTRUCK:  c.generatorCargoTruckDescription,
		model.CARGOSHIP:   c.generatorCargoShipDescription,
		model.REJECTED:    c.generatorRejectedDescription,
		model.CARGOTRAIN:  c.generatorCargoTrainDescription,
		model.SHORTLAND:   generatorShortlandDescription,
		model.TRANSIT:     generatorTransitDescription,
		model.MISROUTE:    generatorMisrouteDescription,
		model.REROUTE:     generatorRerouteDescription,
		model.STIDEST:     generatorStiDestDescription,
		model.STIDESTSC:   generatorStiDestScDescription,
		model.HND:         c.generatorHndDescription,
		model.INHUB:       c.generatorInHubDescription,
		model.OUTHUB:      c.generatorOutHubDescription,
		model.KONDISPATCH: c.generatorDispatchDescription,
		model.STLDISPATCH: c.generatorDispatchDescription,
	}

	statusHistory = generateHistory(regData, condType, statusMapFunc[status])

	switch status {
	case model.ODA:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyLocationName := ""
		historyDistrictName := ""

		hubId := 0
		hub3LC := ""
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyRemark := statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct()
			historyDistrictName = historyRemark.HistoryDistrictName
			historyLocationName = historyRemark.HistoryLocationName

			hubId = historyRemark.HubID
			hub3LC = historyRemark.HubOriginCity
			hubCityName = historyRemark.HubCityName
			hubDistrictName = historyRemark.HubDistrictName
		}

		if hubId != 0 {
			historyLocation3LC = hub3LC
			historyLocationName = hubCityName
			historyDistrictName = hubDistrictName
		}

		statusHistory = fmt.Sprintf(`Paket diluar area pengantaran Consolidator %s %s, %s`, historyLocation3LC, historyLocationName, historyDistrictName)
	case model.DEL:
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole
		historyDistrictName := ""
		isLuwjistik := productType == model.INTERPACK && strings.ToUpper(historyActorName) == model.Luwjistik && historyActorRole == model.VENDOR

		if isLuwjistik {
			statusHistory = fmt.Sprintf(`Paket dalam pengantaran oleh Vendor LUW di Negara %s ke alamat Penerima`, countryName)
			break
		}
		driverPhoneNumber := ""
		driverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryDel := val.HistoryStatus != model.DEL
			isNotMatchHistoryID := val.HistoryID != hisID
			if isNotHistoryDel && isNotMatchHistoryID {
				continue
			}
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			driverName = historyRemark.DriverName
			driverPhoneNumber = historyRemark.DriverPhone
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyLocationName := ""

		hubId := 0
		hub3LC := ""
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyRemark := statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct()
			historyDistrictName = historyRemark.HistoryDistrictName
			historyLocationName = historyRemark.HistoryLocationName

			hubId = historyRemark.HubID
			hub3LC = historyRemark.HubOriginCity
			hubCityName = historyRemark.HubCityName
			hubDistrictName = historyRemark.HubDistrictName
		}

		if hubId != 0 {
			historyLocation3LC = hub3LC
			historyLocationName = hubCityName
			historyDistrictName = hubDistrictName
		}

		switch strings.ToLower(statusPieceWithArrayData[hisID][0].HistoryActorRole) {
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Paket dalam pengantaran oleh %s %s dari Consolidator %s %s, %s ke alamat Penerima`, driverName, driverPhoneNumber, historyLocation3LC, historyLocationName, historyDistrictName)
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Paket dalam pengantaran oleh %s %s dari Subconsolidator %s %s ke alamat Penerima`, driverName, driverPhoneNumber, historyLocation3LC, historyActorName)
		case model.VENDOR:
			statusHistory = fmt.Sprintf(`Paket dalam pengantaran oleh Vendor %s ke alamat Penerima`, model.MaskingVendorName[strings.ToLower(historyActorName)])
		case model.POS:
			statusHistory = fmt.Sprintf(`Paket dalam pengantaran oleh %s %s dari POS %s ke alamat Penerima`, driverName, driverPhoneNumber, historyActorName)
		}

	case model.POD:
		receiverName := ""
		driverName := ""
		sttPieceID := 0
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryPod := val.HistoryStatus != model.POD
			isNotMatchHistoryID := val.HistoryID != hisID
			if isNotHistoryPod && isNotMatchHistoryID {
				continue
			}
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			receiverName = historyRemark.ReceiverName
			sttPieceID = int(val.SttPieceID)
		}
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName

		sttPieceData, _ := c.sttPiecesRepo.Get(selfCtx, &model.SttPiecesViewParam{
			SttPieceID: sttPieceID,
		})

		if sttPieceData != nil {
			deliveryData, _ := c.deliveryRepo.GetDelivery(selfCtx, &model.DeliveryViewParam{
				SttID:          int(sttPieceData.SttPieceSttID),
				FinishedStatus: model.POD,
			})
			if deliveryData != nil {
				driverName = deliveryData.DriverName
			}
		}

		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole
		isLuwjistik := productType == model.INTERPACK && strings.ToUpper(historyActorName) == model.Luwjistik && historyActorRole == model.VENDOR

		if isLuwjistik {
			statusHistory = "Paket telah diterima dari Vendor LUW"
			break
		}

		switch strings.ToLower(statusPieceWithArrayData[hisID][0].HistoryActorRole) {
		case model.VENDOR:
			statusHistory = fmt.Sprintf(`Paket telah diterima oleh %s dari Vendor %s`, receiverName, model.MaskingVendorName[strings.ToLower(historyActorName)])
		default:
			statusHistory = fmt.Sprintf(`Paket telah diterima oleh %s dari kurir %s`, receiverName, driverName)
		}
		if productType == model.JUMBOPACKH2H && strings.ToLower(statusPieceWithArrayData[hisID][0].HistoryActorRole) != model.VENDOR {
			statusHistory = fmt.Sprintf(`Paket telah diterima oleh %s`, receiverName)

		}

	case model.DEX:
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName

		res := &model.ReasonDetailResult{}
		sttPieceID := statusPieceWithArrayData[hisID][0].SttPieceID

		for _, val := range statusPieceWithArrayData[hisID] {
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			res.ReasonDescription = historyRemark.OtherReason
		}
		if statusPieceWithArrayData[hisID][0].HistoryReason != `` && statusPieceWithArrayData[hisID][0].HistoryReason != model.AnotherReason {
			res, _ = c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})
		}
		if res == nil {
			break
		}

		deliveryData := &model.Delivery{}
		sttPieceData, err := c.sttPiecesRepo.Get(selfCtx, &model.SttPiecesViewParam{
			SttPieceID: int(sttPieceID),
		})
		if err == nil {
			deliveryData, err = c.deliveryRepo.GetDelivery(selfCtx, &model.DeliveryViewParam{
				SttID:          int(sttPieceData.SttPieceSttID),
				FinishedStatus: model.DEX,
				Offset:         dexCount,
			})
			if err != nil || deliveryData == nil {
				deliveryData = &model.Delivery{}
			}
		}

		switch strings.ToLower(statusPieceWithArrayData[hisID][0].HistoryActorRole) {
		case model.VENDOR:
			statusHistory = fmt.Sprintf(`Paket akan coba diantarkan kembali (kondisional) karena %s oleh Vendor %s`, res.ReasonDescription, model.MaskingVendorName[strings.ToLower(historyActorName)])
		default:
			statusHistory = fmt.Sprintf(`Paket akan coba diantarkan kembali (kondisional) karena %s oleh kurir %s`, res.ReasonDescription, deliveryData.DriverName)

		}
	case model.CODREJ:
		res := &model.ReasonDetailResult{}
		if statusPieceWithArrayData[hisID][0].HistoryReason != `` {
			res, _ = c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})
		}
		if res == nil {
			break
		}

		statusHistory = fmt.Sprintf(`Paket akan dikembalikan ke Pengirim karena %s`, res.ReasonDescription)
	case model.HAL:
		statusHistory = generateSttTrackingDescriptionHal(statusPieceWithArrayData, hisID, statusHistory, resType)
	case model.RTS:
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole
		isLuwjistik := productType == model.INTERPACK && strings.ToUpper(historyActorName) == model.Luwjistik && historyActorRole == model.VENDOR

		if isLuwjistik {
			statusHistory = `Paket akan dikembalikan ke Pengirim oleh Vendor LUW`
			break
		}

		if statusPieceWithArrayData[hisID][0].HistoryActorRole == model.VENDOR {
			historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
			statusHistory = fmt.Sprintf(`Paket akan dikembalikan ke Pengirim oleh Vendor %s`, model.MaskingVendorName[strings.ToLower(historyActorName)])
			break
		}

		statusHistory = `Paket akan dikembalikan ke alamat pengirim.`
		newSttNumber := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			newSttNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		if newSttNumber != "" {
			statusHistory = fmt.Sprintf(`Paket akan dikembalikan ke pengirim dengan nomor STT baru %s`, newSttNumber)
		}
	case model.SCRAP:
		statusHistory = "Paket telah dimusnahkan karena alesan tertentu"
	case model.CLAIM:
		statusHistory = fmt.Sprintf(`Paket dalam proses klaim oleh %s karena Terlambat / Rusak / Hilang / Lainnya`, statusPieceWithArrayData[hisID][0].HistoryActorName)
	case model.CNX:
		username := statusPieceWithArrayData[hisID][0].HistoryCreatedName
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		historyDistrictName := ""
		sttNoReference := ""

		hubId := 0
		hub3LC := ""
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyRemark := statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct()
			historyDistrictName = historyRemark.HistoryDistrictName
			historyLocationName = historyRemark.HistoryLocationName
			sttNoReference = historyRemark.SttNoReference

			hubId = historyRemark.HubID
			hub3LC = historyRemark.HubOriginCity
			hubCityName = historyRemark.HubCityName
			hubDistrictName = historyRemark.HubDistrictName
		}

		if hubId != 0 {
			historyLocation3LC = hub3LC
			historyLocationName = hubCityName
			historyDistrictName = hubDistrictName
		}

		reason := ""
		res := &model.ReasonDetailResult{}
		if statusPieceWithArrayData[hisID][0].HistoryReason != `` {
			res, _ = c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})
		}

		if res != nil {
			reason = res.ReasonDescription
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Paket telah dibatalkan oleh %s - Subconsolidator %s %s karena alasan %s.`, username, historyLocation3LC, historyActorName, reason)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Paket telah dibatalkan oleh %s - Consolidator %s %s, %s karena alasan %s.`, username, historyLocation3LC, historyLocationName, historyDistrictName, reason)
		case model.POS:
			statusHistory = fmt.Sprintf(`Paket telah dibatalkan oleh %s - POS %s karena alasan %s.`, username, historyActorName, reason)
		case model.CLIENT:
			statusHistory = fmt.Sprintf(`Paket telah dibatalkan oleh %s - CLIENT %s karena alasan %s.`, username, historyActorName, reason)
		case model.INTERNAL:
			statusHistory = fmt.Sprintf(`Paket telah dibatalkan oleh %s - internal karena alasan %s.`, username, reason)
		}

		if sttNoReference != `` {
			statusHistory += fmt.Sprintf(` Cek nomor STT terbaru %s.`, sttNoReference)
		}

	case model.STTADJUSTED:
		username := statusPieceWithArrayData[hisID][0].HistoryCreatedName
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		historyDistrictName := ""

		hubId := 0
		hub3LC := ""
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyRemark := statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct()
			historyDistrictName = historyRemark.HistoryDistrictName
			historyLocationName = historyRemark.HistoryLocationName

			hubId = historyRemark.HubID
			hub3LC = historyRemark.HubOriginCity
			hubCityName = historyRemark.HubCityName
			hubDistrictName = historyRemark.HubDistrictName
		}

		if hubId != 0 {
			historyLocation3LC = hub3LC
			historyLocationName = hubCityName
			historyDistrictName = hubDistrictName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Berat Paket/Lainnya telah dirubah oleh %s - Subconsolidator %s %s`, username, historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Berat Paket/Lainnya telah dirubah oleh %s - Consolidator %s %s, %s`, username, historyLocation3LC, historyLocationName, historyDistrictName)
		case model.POS:
			statusHistory = fmt.Sprintf(`Berat Paket/Lainnya telah dirubah oleh %s - POS %s `, username, historyActorName)
		case model.CLIENT:
			statusHistory = fmt.Sprintf(`Berat Paket/Lainnya telah dirubah oleh %s - CLIENT %s `, username, historyActorName)
		default:
			statusHistory = fmt.Sprintf(`Berat Paket/Lainnya telah dirubah oleh %s - %s`, username, historyActorName)
		}
	case model.STTADJUSTEDPOD:
		username := statusPieceWithArrayData[hisID][0].HistoryCreatedName
		statusHistory = fmt.Sprintf(`Berat Paket/Lainnya telah dirubah oleh %s`, username)
	case model.PICKUPTRUCKING:
		statusHistory = "Paket telah di pick up menggunakan truk"
	case model.DROPOFFTRUCKING:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf("Paket telah di Drop Off menggunakan Truk di Subconsolidator %s %s", historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf("Paket telah di Drop Off menggunakan Truk di Consolidator %s %s", historyLocation3LC, historyLocationName)
		}
	case model.MISSING:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyDistrictName := ""
		historyLocationName := ""
		historyReason := ""

		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole
		isLuwjistik := productType == model.INTERPACK && strings.ToUpper(historyActorName) == model.Luwjistik && historyActorRole == model.VENDOR

		if isLuwjistik {
			statusHistory = `Paket hilang saat proses pengiriman dari Vendor LUW karena alasan tertentu`
			break
		}

		hubId := 0
		hub3LC := ""
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyRemark := statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct()
			historyDistrictName = historyRemark.HistoryDistrictName
			historyLocationName = historyRemark.HistoryLocationName

			hubId = historyRemark.HubID
			hub3LC = historyRemark.HubOriginCity
			hubCityName = historyRemark.HubCityName
			hubDistrictName = historyRemark.HubDistrictName
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

		if hubId != 0 {
			historyLocation3LC = hub3LC
			historyLocationName = hubCityName
			historyDistrictName = hubDistrictName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Paket hilang saat proses pengiriman dari Consolidator %s %s, %s karena %s`, historyLocation3LC, historyLocationName, historyDistrictName, historyReason)
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Paket hilang saat proses pengiriman dari Subconsolidator %s %s karena %s`, historyLocation3LC, historyActorName, historyReason)
		case model.VENDOR:
			statusHistory = fmt.Sprintf(`Paket hilang saat proses pengiriman dari Kota Transit %s karena %s`, historyLocation3LC, historyReason)
		}
	case model.DAMAGE:
		// statusHistory = "Paket dalam kondisi rusak saat dalam pengiriman ke konsol/subkonsol/kota transit"
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole
		isLuwjistik := productType == model.INTERPACK && strings.ToUpper(historyActorName) == model.Luwjistik && historyActorRole == model.VENDOR

		if isLuwjistik {
			statusHistory = `Paket dalam kondisi rusak saat proses pengiriman dari Vendor LUW karena alasan tertentu`
			break
		}

		if strings.EqualFold(historyActorName, model.TypeVendorNINJA) {
			statusHistory = `Paket dalam kondisi rusak saat proses pengiriman dari Vendor NX`
			break
		}

		historyDistrictName := ""
		historyLocationName := ""

		hubId := 0
		hub3LC := ""
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyRemark := statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct()
			historyDistrictName = historyRemark.HistoryDistrictName
			historyLocationName = historyRemark.HistoryLocationName

			hubId = historyRemark.HubID
			hub3LC = historyRemark.HubOriginCity
			hubCityName = historyRemark.HubCityName
			hubDistrictName = historyRemark.HubDistrictName
		}

		if hubId != 0 {
			historyLocation3LC = hub3LC
			historyLocationName = hubCityName
			historyDistrictName = hubDistrictName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf("Paket dalam kondisi rusak saat proses pengiriman dari Subconsolidator %s %s", historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf("Paket dalam kondisi rusak saat proses pengiriman dari Consolidator %s %s, %s", historyLocation3LC, historyLocationName, historyDistrictName)
		}
	case model.NOTRECEIVED:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyDistrictName := ""
		historyLocationName := ""

		hubId := 0
		hub3LC := ""
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyRemark := statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct()
			historyDistrictName = historyRemark.HistoryDistrictName
			historyLocationName = historyRemark.HistoryLocationName

			hubId = historyRemark.HubID
			hub3LC = historyRemark.HubOriginCity
			hubCityName = historyRemark.HubCityName
			hubDistrictName = historyRemark.HubDistrictName
		}

		if hubId != 0 {
			historyLocation3LC = hub3LC
			historyLocationName = hubCityName
			historyDistrictName = hubDistrictName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf("Paket tidak diterima oleh Subconsolidator %s %s", historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf("Paket tidak diterima oleh Consolidator %s %s, %s", historyLocation3LC, historyLocationName, historyDistrictName)
		}
	case model.CI:
		historyActorName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyActorName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		statusHistory = fmt.Sprintf(`Paket hilang/rusak & akan dibebankan ke mitra %s`, historyActorName)
	case model.RTSHQ:
		newSttNumber := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			newSttNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		statusHistory = fmt.Sprintf(`Paket dikembalikan ke Lion Parcel Pusat dengan nomor STT baru %s`, newSttNumber)

	case model.OCC:
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole
		isLuwjistik := productType == model.INTERPACK && strings.ToUpper(historyActorName) == model.Luwjistik && historyActorRole == model.VENDOR

		if isLuwjistik {
			statusHistory = "Paket dalam proses pengecekan Bea Cukai oleh vendor LUW"
			break
		}

		vendorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		if statusPieceWithArrayData[hisID][0].HistoryActorRole != model.VENDOR {
			vendorName = ""
		}
		statusHistory = fmt.Sprintf(`Paket dalam proses pengecekan Bea Cukai oleh vendor %s`, vendorName)
	case model.MISBOOKING:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		historyDistrictName := ""

		hubId := 0
		hub3LC := ""
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyRemark := statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct()
			historyDistrictName = historyRemark.HistoryDistrictName
			historyLocationName = historyRemark.HistoryLocationName

			hubId = historyRemark.HubID
			hub3LC = historyRemark.HubOriginCity
			hubCityName = historyRemark.HubCityName
			hubDistrictName = historyRemark.HubDistrictName
		}

		if hubId != 0 {
			historyLocation3LC = hub3LC
			historyLocationName = hubCityName
			historyDistrictName = hubDistrictName
		}

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Paket mengalami misbooking dan akan di kirim ulang ke kota tujuan dari Subconsolidator %s %s`, historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Paket mengalami misbooking dan akan dikirim ulang ke kota tujuan dari Consolidator %s %s, %s`, historyLocation3LC, historyLocationName, historyDistrictName)
		}
	case model.SCRAPCD:
		statusHistory = `STT Dimusnahkan karena bagging telah diterima`
	case model.TFDREQ:
		driverPhoneNumber := ""
		driverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusTFDREQ := val.HistoryStatus != model.TFDREQ
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusTFDREQ && isNotHistoryIdMatch {
				continue
			}

			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			driverName = historyRemark.DriverName
			driverPhoneNumber = historyRemark.DriverPhone
		}
		statusHistory = fmt.Sprintf(`Kurir %s %s telah mengajukan permintaan penggantian kurir karena alasan tertentu.`, driverName, driverPhoneNumber)

	case model.DELTRF:
		driverPhoneNumber := ""
		driverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusDELTRF := val.HistoryStatus != model.DELTRF
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusDELTRF && isNotHistoryIdMatch {
				continue
			}

			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			driverName = historyRemark.DriverName
			driverPhoneNumber = historyRemark.DriverPhone
		}
		statusHistory = fmt.Sprintf(`Permintaan penggantian kurir diterima %s %s dan paket akan segera diantarkan.`, driverName, driverPhoneNumber)

	case model.TFDCNC:
		driverPhoneNumber := ""
		driverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusTFDCNC := val.HistoryStatus != model.TFDCNC
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusTFDCNC && isNotHistoryIdMatch {
				continue
			}

			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			driverName = historyRemark.DriverName
			driverPhoneNumber = historyRemark.DriverPhone
		}
		statusHistory = fmt.Sprintf(`Permintaan penggantian kurir oleh %s %s telah dibatalkan.`, driverName, driverPhoneNumber)
	case model.HALCD:
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			statusHistory = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

	case model.CNXCD:
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			statusHistory = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

	case model.INTHND:
		historyLocation3LC := ""
		historyActorName := ""
		historyLocationName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusINTHND := val.HistoryStatus != model.INTHND
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusINTHND && isNotHistoryIdMatch {
				continue
			}

			historyLocation3LC = val.HistoryLocation
			historyActorName = val.HistoryActorName
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			historyLocationName = historyRemark.HistoryLocationName
		}
		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Paket telah diserahkan Subconsolidator %s %s ke Vendor LUW`, historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Paket telah diserahkan Consolidator %s %s ke Vendor LUW`, historyLocation3LC, historyLocationName)
		}

	case model.INTSTI:
		statusHistory = "Paket telah sampai di Vendor LUW"

	case model.OCCEXP:
		statusHistory = "Paket sedang diperiksa Bea Cukai oleh vendor LUW"

		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole

		if strings.ToUpper(historyActorName) != model.Luwjistik && historyActorRole != model.VENDOR {

			historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation

			switch historyActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket sedang diperiksa Bea Cukai oleh Subconsolidator %s %s`, historyLocation3LC, historyActorName)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket sedang diperiksa Bea Cukai oleh Consolidator %s`, historyLocation3LC)
			}
		}

	case model.OCCIMP:
		statusHistory = "Paket telah selesai diperiksa Bea Cukai oleh vendor LUW"

		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole
		if strings.ToUpper(historyActorName) == model.Luwjistik && historyActorRole == model.VENDOR {
			break
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		switch historyActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Paket telah selesai diperiksa Bea Cukai oleh Subconsolidator %s %s`, historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Paket telah selesai diperiksa Bea Cukai oleh Consolidator %s`, historyLocation3LC)
		}
	case model.OCCHAL:
		statusHistory = "Paket ditahan Bea Cukai oleh Vendor LUW"

		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole
		historyRemark := &model.RemarkPieceHistory{}
		if statusPieceWithArrayData[hisID][0].HistoryRemark != `` {
			json.Unmarshal([]byte(statusPieceWithArrayData[hisID][0].HistoryRemark), historyRemark)
		}
		reason := historyRemark.CustomProcessRemarks
		if strings.ToUpper(historyActorName) == model.Luwjistik && historyActorRole == model.VENDOR {
			break
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		cityCountry, err := c.cityRepo.Get(selfCtx, historyLocation3LC, token)
		if err == nil || cityCountry != nil {
			countryName = cityCountry.Country.Name
		}

		switch historyActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Paket ditahan bea cukai %s %s karena %s di Subconsolidator %s %s`, countryName, historyLocationName, reason, historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Paket ditahan bea cukai %s %s karena %s di Consolidator %s`, countryName, historyLocationName, reason, historyLocation3LC)
		}
	case model.OCRIMP, model.RCCIMP:
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		switch historyActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Paket telah selesai diperiksa Bea Cukai oleh Subconsolidator %s %s`, historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Paket telah selesai diperiksa Bea Cukai oleh Consolidator %s`, historyLocation3LC)
		}
	}

	return statusHistory
}

func generateSttTrackingDescriptionHal(statusPieceWithArrayData map[int][]model.SttPieceHistory, hisID int, statusHistory, resType string) string {
	historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
	historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
	historyHubDistrictInfo := []string{}
	historyLocationName := ""
	historyDistrictName := ""
	historyReason := ""

	hubId := 0
	hub3LC := ""
	hubCityName := ""
	hubDistrictName := ""

	if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
		historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		historyHubDistrictInfo = strings.Split(statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName, ", ")
		historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		historyDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName

		hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
		hub3LC = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubOriginCity
		hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
		hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
	}

	if hubId != 0 {
		historyLocation3LC = hub3LC
		historyLocationName = hubCityName
		historyDistrictName = hubDistrictName
	}

	switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
	case model.CONSOLE:
		statusHistory = fmt.Sprintf(`Paket ditahan karena %s di Consolidator %s %s, %s`, historyReason, historyLocation3LC, historyLocationName, historyDistrictName)
		if resType == model.EXTERNAL && len(historyHubDistrictInfo) == 3 {
			statusHistory = fmt.Sprintf(`Paket ditahan karena %s di Consolidator %s %s, %s %s, %s`, historyReason, historyLocation3LC, historyLocationName, historyDistrictName, historyHubDistrictInfo[2], historyHubDistrictInfo[1])
		}
	case model.SUBCONSOLE:
		statusHistory = fmt.Sprintf(`Paket ditahan karena %s di Subconsolidator %s %s, %s %s`, historyReason, historyLocation3LC, historyLocationName, historyDistrictName, historyActorName)
	case model.VENDOR:
		statusHistory = fmt.Sprintf(`Paket siap di ambil Penerima di Vendor %s`, model.MaskingVendorName[strings.ToLower(historyActorName)])
	}

	return statusHistory
}

func (c *sttCtx) generateSttTrackingDescriptionForClient(ctx context.Context, status string, hisID int, statusPieceWithArrayData map[int][]model.SttPieceHistory, token string, productType string, countryName string, countyDestName string, historyActorName string, historyActorRole string, showHiddenStatus string) string {
	opName := "sttCtx-generateSttTrackingDescriptionForClient"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"result": nil, "error": err})
	}()

	var statusHistory string = ""
	if len(statusPieceWithArrayData[hisID]) < 1 {
		return statusHistory
	}
	isLuwjistik := productType == model.INTERPACK && strings.ToUpper(historyActorName) == model.Luwjistik && historyActorRole == model.VENDOR

	switch status {
	case model.BKD:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu telah diproses oleh Agen Lion Parcel %s, %s`, cityName, districtName)
	case model.PUP, model.PUPC:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu akan diantar ke Gudang Lion Parcel %s, %s`, cityName, districtName)
	case model.STISC:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Transit Lion Parcel %s, %s`, cityName, districtName)
	case model.STOSC:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		statusHistory = fmt.Sprintf(`Paket telah dijemput oleh Shuttle dari Subconsolidator %s %s.`, historyLocation3LC, statusPieceWithArrayData[hisID][0].HistoryActorName)

	case model.STI:
		cityName := ""
		districtName := ""

		hubId := 0
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
			hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
			hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
			hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
		}

		if hubId != 0 {
			cityName = hubCityName
			districtName = hubDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Lion Parcel %s, %s`, cityName, districtName)
	case model.CARGOPLANE:
		cityName := ""
		districtName := ""
		actorExternalCode := ""

		hubId := 0
		hubCityName := ""
		hubDistrictName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
			actorExternalCode = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().ActorExternalCode

			hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
			hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
			hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
		}

		if hubId != 0 {
			districtName = hubDistrictName
			cityName = hubCityName
		}

		cityINTHND := ``
		districtINTHND := ``
		for _, sttPieceHistory := range statusPieceWithArrayData {
			if sttPieceHistory[0].HistoryStatus == model.INTHND && sttPieceHistory[0].RemarkPieceHistoryToStruct() != nil {
				cityINTHND = sttPieceHistory[0].RemarkPieceHistoryToStruct().HistoryLocationName
				districtINTHND = sttPieceHistory[0].RemarkPieceHistoryToStruct().HistoryDistrictName
				break
			}
		}

		statusHistory = fmt.Sprintf(`Paketmu akan diterbangkan dengan pesawat dari Kota %s, %s`, cityName, districtName)
		if actorExternalCode == model.LuwjistikName {
			statusHistory = fmt.Sprintf(`Paketmu akan diterbangkan dengan pesawat dari Kota %s, %s`, cityINTHND, districtINTHND)
		}
	case model.CARGOTRUCK:
		historyLocationName := ""
		districtName := ""

		hubId := 0
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName

			hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
			hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
			hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
		}

		if hubId != 0 {
			districtName = hubDistrictName
			historyLocationName = hubCityName
		}

		if productType == model.INTERPACK {
			statusHistory = fmt.Sprintf(`Paketmu akan diberangkatkan ke Negara %s dari kota %s, %s`, countryName, historyLocationName, districtName)
		} else {
			statusHistory = fmt.Sprintf(`Paketmu akan diberangkatkan dengan truk dari Kota %s, %s`, historyLocationName, districtName)
		}

	case model.CARGOTRAIN:
		cityName := ""
		districtName := ""

		hubId := 0
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName

			hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
			hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
			hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
		}

		if hubId != 0 {
			districtName = hubDistrictName
			cityName = hubCityName
		}

		statusHistory = fmt.Sprintf(`Paketmu akan diberangkatkan dengan kereta dari Kota %s, %s `, cityName, districtName)
	case model.CARGOSHIP:
		cityName := ""
		districtName := ""

		hubId := 0
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName

			hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
			hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
			hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
		}

		if hubId != 0 {
			districtName = hubDistrictName
			cityName = hubCityName
		}

		statusHistory = fmt.Sprintf(`Paketmu akan diberangkatkan dengan kapal dari Kota %s, %s `, cityName, districtName)
	case model.TRANSIT:
		historyLocationName := ""
		historyActorExternal := ""

		hubId := 0
		hubCityName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			historyActorExternal = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().ActorExternalCode

			hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
			hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
		}

		if hubId != 0 {
			historyLocationName = hubCityName
		}

		statusHistory = fmt.Sprintf(`Paketmu sedang transit di Kota %s`, historyLocationName)
		if historyActorExternal == model.LuwjistikName {
			statusHistory = "Paketmu sedang transit"
		}
	case model.STIDEST:
		if isLuwjistik {
			statusHistory = fmt.Sprintf(`Paket telah sampai di Negara %s`, countryName)
		} else {
			cityName := ""
			districtName := ""

			hubId := 0
			hubCityName := ""
			hubDistrictName := ""

			if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
				cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
				districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName

				hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
				hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
				hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
			}

			if hubId != 0 {
				districtName = hubDistrictName
				cityName = hubCityName
			}

			statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Lion Parcel %s, %s`, cityName, districtName)
		}

	case model.STIDESTSC:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Transit Lion Parcel %s, %s`, cityName, districtName)
	case model.DEL:
		driverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			driverName = historyRemark.DriverName
		}

		if isLuwjistik {
			statusHistory = `Paketmu diantar ke alamat penerima oleh Kurir. Pastikan nomor penerima dapat dihubungi oleh kurir`
			break
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.VENDOR:
			statusHistory = `Paketmu diantar oleh kurir ke alamat penerima. Pastikan kamu dapat dihubungi oleh kurir`
		default:
			statusHistory = fmt.Sprintf(`Paketmu diantar ke alamat penerima oleh kurir %s. Pastikan nomor penerima dapat dihubungi oleh kurir`, driverName)
		}
	case model.POD:
		receiverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			receiverName = historyRemark.ReceiverName
		}

		if isLuwjistik {
			statusHistory = `Paketmu telah sampai di tujuan & diterima`
			break
		}

		statusHistory = fmt.Sprintf(`Paketmu telah sampai di tujuan & diterima oleh  %s`, receiverName)
	case model.DEX:
		reasonDescription := ""
		res := &model.ReasonDetailResult{}

		if statusPieceWithArrayData[hisID][0].HistoryReason != `` && statusPieceWithArrayData[hisID][0].HistoryReason != model.AnotherReason {
			res, _ = c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})
			reasonDescription = res.ReasonDescription
		}

		if statusPieceWithArrayData[hisID][0].HistoryReason == model.AnotherReason {
			for _, val := range statusPieceWithArrayData[hisID] {
				historyRemark := &model.RemarkPieceHistory{}
				if val.HistoryRemark == `` {
					continue
				}

				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
				res.ReasonDescription = historyRemark.OtherReason
			}
		}

		statusHistory = fmt.Sprintf(`Paketmu gagal diantarkan karena %s. Percobaan pengiriman ulang akan dilakukan secara berkala. Pastikan alamat dan kontak Penerima sudah sesuai.`, reasonDescription)

	case model.CODREJ:
		res := &model.ReasonDetailResult{}
		if statusPieceWithArrayData[hisID][0].HistoryReason != `` {
			res, _ = c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})
		}
		if res != nil {
			statusHistory = fmt.Sprintf(`Paket COD mu dikembalikan ke alamat pengirim karena %s.`, res.ReasonDescription)
		}
	case model.HAL:
		cityName := ""
		districtName := ""

		historyReason := ""
		hubId := 0
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName

			hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
			hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
			hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
		}

		if hubId != 0 {
			districtName = hubDistrictName
			cityName = hubCityName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.VENDOR:
			statusHistory = fmt.Sprintf(`Paketmu berada di Gudang Lion Parcel %s, %s.`, cityName, districtName)
		default:
			statusHistory = fmt.Sprintf(`Paketmu berada di Gudang Lion Parcel %s, %s karena %s.`, cityName, districtName, historyReason)
		}

	case model.RTS:
		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.VENDOR:
			statusHistory = `Paketmu dikembalikan ke alamat pengirim karena alasan tertentu.`
		default:
			statusHistory = `Paket akan dikembalikan ke alamat pengirim.`
			newSttNumber := ""
			if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
				newSttNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
			}
			if newSttNumber != "" {
				statusHistory = fmt.Sprintf(`Paketmu akan dikembalikan ke alamat pengirim dengan nomor resi baru %s.`, newSttNumber)
			}
		}
	case model.CLAIM:
		claimNumber := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			claimNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().ClaimNo
		}

		statusHistory = fmt.Sprintf(`Paketmu dalam proses klaim. Silahkan cek nomor klaim kamu %s untuk detail klaim`, claimNumber)
	case model.CNX:
		res := &model.ReasonDetailResult{}
		if statusPieceWithArrayData[hisID][0].HistoryReason != `` {
			res, _ = c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})
		}

		sttNoReference := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			sttNoReference = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().SttNoReference
		}

		if res != nil {
			statusHistory = fmt.Sprintf(`Pengiriman paketmu telah dibatalkan karena alasan %s.`, res.ReasonDescription)

			if sttNoReference != `` {
				statusHistory += fmt.Sprintf(` Cek nomor resi terbaru %s.`, sttNoReference)
			}
		}
	case model.STTADJUSTED:
		statusHistory = fmt.Sprint(`Terjadi penyesuaian harga terkait berat & volume paketmu`)
	case model.STTADJUSTEDPOD:
		statusHistory = "Terjadi penyesuaian harga terkait berat & volume paketmu"
	case model.PICKUPTRUCKING:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu telah diberangkatkan dengan truk dari Kota %s, %s`, cityName, districtName)
	case model.DROPOFFTRUCKING:
		cityName := ""
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu telah sampai dengan truk di Kota %s, %s`, cityName, districtName)
	case model.REJECTED:
		historyReason := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

		if len(historyReason) > 0 {
			reason, _ := c.reasonRepo.GetDetail(ctx, &model.ReasonViewParams{
				ReasonTitle: historyReason,
			})
			if reason != nil {
				historyReason = reason.ReasonDescription
			}
		}

		statusHistory = fmt.Sprintf("Paketmu akan dialihkan menggunakan armada lain karena %s.", historyReason)
	case model.NOTRECEIVED:
		statusHistory = "Pengiriman paketmu masih dalam proses."
	case model.DAMAGE:
		if isLuwjistik {
			statusHistory = "Pengiriman paketmu mengalami kendala karena alasan tertentu."
			break
		}

		statusHistory = "Pengiriman paketmu mengalami kendala."
	case model.MISSING:
		if isLuwjistik {
			statusHistory = "Pengiriman paketmu mengalami kendala karena alasan tertentu."
			break
		}

		historyReason := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		statusHistory = fmt.Sprintf("Pengiriman paketmu mengalami kendala karena %s.", historyReason)
	case model.RTSHQ:
		newSttNumber := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			newSttNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		statusHistory = fmt.Sprintf(`Paket dikembalikan ke Lion Parcel Pusat dengan nomor resi baru %s.`, newSttNumber)
	case model.OCC:
		if productType == model.INTERPACK {
			statusHistory = fmt.Sprintf("Paketmu dalam proses pengecekan Bea Cukai untuk dikirim ke Negara %s.", countryName)
			break
		}

		statusHistory = fmt.Sprintf(`Paketmu dalam proses pengecekan Bea Cukai untuk dikirim ke tujuan %s.`, countryName)
	case model.REROUTE:
		statusHistory = `Paketmu akan dikirim kembali ke alamat tujuan yang sesuai.`
		newSttNumber := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			newSttNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		if newSttNumber != "" {
			statusHistory = fmt.Sprintf(`Paketmu akan dikirim kembali ke alamat tujuan yang sesuai dengan nomor resi terbaru %s`, newSttNumber)
		}
	case model.MISBOOKING:
		statusHistory = `Terjadi kesalahan pada paketmu saat diproses Agen.`
	case model.SCRAPCD:
		statusHistory = `STT Dimusnahkan karena bagging telah diterima`

	case model.TFDREQ:
		driverPhoneNumber := ""
		driverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusTFDREQ := val.HistoryStatus != model.TFDREQ
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusTFDREQ && isNotHistoryIdMatch {
				continue
			}
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			driverName = historyRemark.DriverName
			driverPhoneNumber = historyRemark.DriverPhone
		}
		statusHistory = fmt.Sprintf(`Kurir %s %s telah mengajukan permintaan penggantian kurir karena alasan tertentu.`, driverName, driverPhoneNumber)

	case model.DELTRF:
		driverPhoneNumber := ""
		driverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusDELTRF := val.HistoryStatus != model.DELTRF
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusDELTRF && isNotHistoryIdMatch {
				continue
			}
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			driverName = historyRemark.DriverName
			driverPhoneNumber = historyRemark.DriverPhone
		}
		statusHistory = fmt.Sprintf(`Permintaan penggantian kurir diterima %s %s dan paket akan segera diantarkan.`, driverName, driverPhoneNumber)

	case model.TFDCNC:
		driverPhoneNumber := ""
		driverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusTFDCNC := val.HistoryStatus != model.TFDCNC
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusTFDCNC && isNotHistoryIdMatch {
				continue
			}
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			driverName = historyRemark.DriverName
			driverPhoneNumber = historyRemark.DriverPhone
		}
		statusHistory = fmt.Sprintf(`Permintaan penggantian kurir oleh %s %s telah dibatalkan.`, driverName, driverPhoneNumber)

	case model.HALCD:
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			statusHistory = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

	case model.CNXCD:
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			statusHistory = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

	case model.INTSTI:
		cityINTHND := ``
		districtINTHND := ``

		for _, sttPieceHistory := range statusPieceWithArrayData {
			if sttPieceHistory[0].HistoryStatus == model.INTHND && sttPieceHistory[0].RemarkPieceHistoryToStruct() != nil {
				cityINTHND = sttPieceHistory[0].RemarkPieceHistoryToStruct().HistoryLocationName
				districtINTHND = sttPieceHistory[0].RemarkPieceHistoryToStruct().HistoryDistrictName
				break
			}
		}
		statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Transit Lion Parcel %s, %s`, cityINTHND, districtINTHND)

	case model.OCCEXP:
		statusHistory = fmt.Sprintf(`Paketmu sedang diperiksa Bea Cukai untuk dikirim ke negara %s`, countyDestName)
		if strings.ToUpper(historyActorName) != model.Luwjistik && historyActorRole != model.VENDOR {
			statusHistory = fmt.Sprintf(`Paketmu sedang diperiksa Bea Cukai untuk dikirim ke negara %s`, countryName)
		}
	case model.OCCIMP:
		statusHistory = fmt.Sprintf(`Paketmu telah selesai diperiksa Bea Cukai dan telah diterima di negara %s`, countyDestName)
		if strings.ToUpper(historyActorName) != model.Luwjistik && historyActorRole != model.VENDOR {
			statusHistory = fmt.Sprintf(`Paketmu telah selesai diperiksa Bea Cukai dan telah diterima di negara %s`, countryName)
		}
	case model.OCCHAL:
		statusHistory = fmt.Sprintf(`Paketmu masih berada di Gudang Lion Parcel %s`, countyDestName)

		if strings.ToUpper(historyActorName) == model.Luwjistik && historyActorRole == model.VENDOR {
			break
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		cityCountry, err := c.cityRepo.Get(selfCtx, historyLocation3LC, token)
		if err == nil || cityCountry != nil {
			countryName = cityCountry.Country.Name
		}

		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		historyReason := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		statusHistory = fmt.Sprintf(`Paketmu berada di Gudang Lion Parcel %s %s karena %s.`, countryName, cityName, historyReason)
	case model.OCRIMP, model.RCCIMP:
		statusHistory = fmt.Sprintf(`Paketmu telah selesai diperiksa Bea Cukai dan telah diterima di negara %s`, countryName)
	case model.STTREMOVE:
		statusHistory = "Pengiriman paketmu mengalami kendala."
	case model.INHUB:
		historyLocationName := ""
		historyDistrictName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			historyDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}
		statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Lion Parcel %s, %s`, historyLocationName, historyDistrictName)
	case model.OUTHUB:
		historyLocationName := ""
		historyDistrictName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			historyDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}
		statusHistory = fmt.Sprintf(`Paketmu telah diberangkatkan dari Gudang Lion Parcel %s, %s`, historyLocationName, historyDistrictName)
	case model.KONDISPATCH:
		statusHistory = `Paketmu telah ditugaskan ke kurir dan siap diantar.`
	}

	return statusHistory
}

func (c *sttCtx) generateSttTrackingDescriptionEnglish(ctx context.Context, accountType string, status string, hisID int, statusPieceWithArrayData map[int][]model.SttPieceHistory, token string, productType string, cityDestinationCode string, dexCount int) string {
	opName := "sttCtx-generateSttTrackingDescriptionEnglish"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"result": nil, "error": err})
	}()

	countryName := ""
	countyDestName := ""
	cityCountry, err := c.cityRepo.Get(selfCtx, cityDestinationCode, token)
	if err == nil || cityCountry != nil {
		countryName = cityCountry.Country.Name
		countyDestName = cityCountry.Name
	}

	var statusHistory string = ""
	if len(statusPieceWithArrayData[hisID]) < 1 {
		return statusHistory
	}

	if accountType == model.CLIENT {
		historyActorName := ""
		historyActorRole := ""
		if len(statusPieceWithArrayData[hisID]) != 0 {
			historyActorName = statusPieceWithArrayData[hisID][0].HistoryActorName
			historyActorRole = statusPieceWithArrayData[hisID][0].HistoryActorRole
		}
		return c.generateSttTrackingDescriptionForClientEnglish(selfCtx, status, hisID, statusPieceWithArrayData, token, productType, countryName, countyDestName, historyActorName, historyActorRole)
	}

	switch status {
	case model.BKD:
		statusHistory = fmt.Sprintf(`Package has been booked by %s %s in Lion Parcel system.`,
			model.MaskingActorRole[statusPieceWithArrayData[hisID][0].HistoryActorRole], statusPieceWithArrayData[hisID][0].HistoryActorName)
	case model.PUP, model.PUPC:
		statusHistory = fmt.Sprintf(`Package forwarded by POS %s.`, statusPieceWithArrayData[hisID][0].HistoryActorName)
	case model.STISC:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		statusHistory = fmt.Sprintf(`Package has arrived at Subconsolidator %s %s.`, historyLocation3LC, statusPieceWithArrayData[hisID][0].HistoryActorName)
	case model.STOSC:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		statusHistory = fmt.Sprintf(`Package has been Pick Up by Shuttle from Subconsolidator %s %s.`, historyLocation3LC, statusPieceWithArrayData[hisID][0].HistoryActorName)
	case model.STI:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		statusHistory = fmt.Sprintf(`Package has arrived at Consolidator %s %s.`, historyLocation3LC, historyLocationName)
	case model.BAGGING:
		bagNumber := []string{}
		for _, val := range statusPieceWithArrayData[hisID] {
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
				bagNumber = append(bagNumber, historyRemark.BagNumber)
			}
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package has been sorted by Subconsolidator %s %s with No. Bag %s`, historyLocation3LC, historyActorName, strings.Join(bagNumber, ","))
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package has been sorted by Consolidator %s %s with No. Bag %s`, historyLocation3LC, historyLocationName, strings.Join(bagNumber, ","))
		}
	case model.CARGOPLANE:
		cargoNumber := ""
		actorExternalCode := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusCARGOPLANE := val.HistoryStatus != model.CARGOPLANE
			isNotHistoryIdMatch := val.HistoryID == hisID
			if isNotHistoryStatusCARGOPLANE && isNotHistoryIdMatch {
				continue
			}
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			cargoNumber = historyRemark.CargoNumber
			actorExternalCode = historyRemark.ActorExternalCode
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		cargoData, err := c.cargoRepo.Get(selfCtx, &model.CargoViewParams{
			CargoNo: cargoNumber,
		})
		dest3LC := ""
		isCargoDataNilAndCargoNumberEmpty := cargoData != nil && cargoNumber != ""
		if err == nil && isCargoDataNilAndCargoNumberEmpty {
			dest3LC = cargoData.CargoDestinationCityCode
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package will be flown by Subconsolidator %s %s to %s City with AWB No. %s`, historyLocation3LC, historyActorName, dest3LC, cargoNumber)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package will be flown by Consolidator %s %s to %s City with AWB No. %s`, historyLocation3LC, historyLocationName, dest3LC, cargoNumber)
		}

		if actorExternalCode != model.LuwjistikName {
			break
		}

		historyActorRole := ""
		cityINTHND := ""
		historyLocation3LCINTHND := ""
		historyActorNameINTHND := ""

		for _, sttPieceHistory := range statusPieceWithArrayData {
			if sttPieceHistory[0].HistoryStatus == model.INTHND && sttPieceHistory[0].RemarkPieceHistoryToStruct() != nil {
				historyActorRole = sttPieceHistory[0].HistoryActorRole
				cityINTHND = sttPieceHistory[0].RemarkPieceHistoryToStruct().HistoryLocationName
				historyLocation3LCINTHND = sttPieceHistory[0].HistoryLocation
				historyActorNameINTHND = sttPieceHistory[0].HistoryActorName

				break
			}
		}
		switch historyActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package will be flown by Subconsolidator %s %s with Vendor LUW`, historyLocation3LCINTHND, historyActorNameINTHND)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package will be flown by Consolidator %s %s with Vendor LUW`, historyLocation3LCINTHND, cityINTHND)
		}
	case model.STTREMOVE:
		bagNumber := ""
		cargoNumber := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusSTTREMOVE := val.HistoryStatus != model.STTREMOVE
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusSTTREMOVE && isNotHistoryIdMatch {
				continue
			}
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			bagNumber = historyRemark.BagNumber
			cargoNumber = historyRemark.CargoNumber
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		numberCargoOrBag := ""
		if len(cargoNumber) > 0 {
			numberCargoOrBag = fmt.Sprintf(`of Cargo No. %s`, cargoNumber)
		}
		if bagNumber != "" {
			numberCargoOrBag = fmt.Sprintf(`of Bag No. %s`, bagNumber)
		}
		if len(cargoNumber) > 0 && bagNumber != "" {
			numberCargoOrBag = fmt.Sprintf(`of Bag No. %s or Cargo No. %s`, bagNumber, cargoNumber)
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package has left by Subconsolidator %s %s %s`, historyLocation3LC, historyActorName, numberCargoOrBag)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package has left by Consolidator %s %s %s`, historyLocation3LC, historyLocationName, numberCargoOrBag)
		}
	case model.REJECTED:
		historyReason := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

		if len(historyReason) > 0 {
			reason, _ := c.reasonRepo.GetDetail(ctx, &model.ReasonViewParams{
				ReasonTitle: historyReason,
			})
			if reason != nil {
				historyReason = reason.ReasonDescriptionEn
			}
		}

		statusHistory = fmt.Sprintf("Shipment fleet changed due to %s.", historyReason)
	case model.CARGOTRUCK:
		cargoNumber := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusCARGOTRUCK := val.HistoryStatus != model.CARGOTRUCK
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusCARGOTRUCK && isNotHistoryIdMatch {
				continue
			}
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			cargoNumber = historyRemark.CargoNumber
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		cargoData, err := c.cargoRepo.Get(selfCtx, &model.CargoViewParams{
			CargoNo: cargoNumber,
		})
		dest3LC := ""
		destCityName := ""
		isCargoDataNilAndCargoNumberEmpty := cargoData != nil && cargoNumber != ""
		if err == nil && isCargoDataNilAndCargoNumberEmpty {
			dest3LC = cargoData.CargoDestinationCityCode
			destCityName = cargoData.LoadRemark().DestinationCityName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package will be departed by Subconsolidator %s %s to %s %s City using Truck with Cargo No. %s`, historyLocation3LC, historyActorName, dest3LC, destCityName, cargoNumber)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package will be departed by Consolidator %s %s to %s %s City using Truck with Cargo No. %s`, historyLocation3LC, historyLocationName, dest3LC, destCityName, cargoNumber)
		}

	case model.CARGOSHIP:
		cargoNumber := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusCARGOSHIP := val.HistoryStatus != model.CARGOSHIP
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusCARGOSHIP && isNotHistoryIdMatch {
				continue
			}
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			cargoNumber = historyRemark.CargoNumber
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		cargoData, err := c.cargoRepo.Get(selfCtx, &model.CargoViewParams{
			CargoNo: cargoNumber,
		})
		dest3LC := ""
		destCityName := ""
		isCargoDataNilAndCargoNoEmpty := cargoData != nil && cargoNumber != ""
		if err == nil && isCargoDataNilAndCargoNoEmpty {
			dest3LC = cargoData.CargoDestinationCityCode
			destCityName = cargoData.LoadRemark().DestinationCityName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`The package will be departed by Subconsolidator %s %s to %s %s City using Ship with Cargo No. %s`, historyLocation3LC, historyActorName, dest3LC, destCityName, cargoNumber)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`The package will be departed by Consolidator %s %s to %s %s City using Ship with Cargo No. %s`, historyLocation3LC, historyLocationName, dest3LC, destCityName, cargoNumber)
		}

	case model.CARGOTRAIN:
		cargoNumber := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusCARGOTRAIN := val.HistoryStatus != model.CARGOTRAIN
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusCARGOTRAIN && isNotHistoryIdMatch {
				continue
			}
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			cargoNumber = historyRemark.CargoNumber
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName

		cargoData, err := c.cargoRepo.Get(selfCtx, &model.CargoViewParams{
			CargoNo: cargoNumber,
		})
		dest3LC := ""
		destCityName := ""
		isCargoDataNilAndCargoNoEmpty := cargoData != nil && cargoNumber != ""
		if err == nil && isCargoDataNilAndCargoNoEmpty {
			dest3LC = cargoData.CargoDestinationCityCode
			destCityName = cargoData.LoadRemark().DestinationCityName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package will be departed by Subconsolidator %s %s to %s %s City using Train with Cargo %s Number`, historyLocation3LC, historyActorName, dest3LC, destCityName, cargoNumber)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package will be departed by Consolidator %s to %s %s City using Train with Cargo %s Number`, historyLocation3LC, dest3LC, destCityName, cargoNumber)
		}
	case model.SHORTLAND:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package has not been received by Subconsolidator %s %s`, historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package has not been received by Consolidator %s %s`, historyLocation3LC, historyLocationName)
		}
	case model.TRANSIT:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		statusHistory = fmt.Sprintf(`Package has arrived at Transit City %s %s`, historyLocation3LC, historyLocationName)
	case model.MISROUTE:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package has been received Subconsolidator %s %s which is not suitable`, historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package has been received Consolidator %s %s which is not suitable`, historyLocation3LC, historyLocationName)
		}
	case model.REROUTE:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		newSttNumber := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			newSttNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Misrouted package will be redelivered from Subconsolidator %s %s with a new tracking number %s`, historyLocation3LC, historyActorName, newSttNumber)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Misrouted package will be redelivered from Consolidator %s %s with a new tracking number %s`, historyLocation3LC, historyLocationName, newSttNumber)
		}

		if newSttNumber == "" {
			switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`The package was misrouted and will redelivery from Subconsolidator %s, %s.`, districtName, cityName)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`The package was misrouted and will redelivery from Consolidator %s, %s.`, districtName, cityName)
			}
		}

	case model.STIDEST:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		statusHistory = fmt.Sprintf(`Package has arrived at Consolidator %s %s`, historyLocation3LC, historyLocationName)

	case model.STIDESTSC:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package has arrived at Subconsolidator %s %s`, historyLocation3LC, historyActorName)
		case model.VENDOR:
			statusHistory = fmt.Sprintf(`Package has arrived at Vendor %s`, model.MaskingVendorName[strings.ToLower(historyActorName)])
		}
	case model.HND:
		handOverTo := ""
		sttPieceID := 0
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusHND := val.HistoryStatus != model.HND
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusHND && isNotHistoryIdMatch {
				continue
			}
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			sttPieceID = int(val.SttPieceID)
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		var hndDetailData *model.HandoverDetail
		var err error
		if sttPieceID != 0 {
			hndDetailData, err = c.handoverDetailRepo.Get(selfCtx, &model.HandoverDetail{
				HandoverDetailSttPieceID: sttPieceID,
			})
		}

		var hndData *model.Handover
		if hndDetailData != nil && hndDetailData.HandoverDetailHandoverID != 0 {
			hndData, err = c.handoverRepo.Get(selfCtx, &model.HandoverViewParams{
				HandoverID: hndDetailData.HandoverDetailHandoverID,
			})
			if err != nil || hndData == nil {
				hndData = nil
			}
		}

		if hndData != nil {
			partner, err := c.partnerRepo.GetByID(selfCtx, hndData.HandoverDestinationPartnerID, token)

			isPartnerDataNotNilAndTypeSubconsole := partner != nil && partner.Data.Type == model.SUBCONSOLE
			if err == nil && isPartnerDataNotNilAndTypeSubconsole {
				handOverTo = fmt.Sprintf(`Subconsolidator %s %s`, hndData.HandoverArrivalCityCode, hndData.HandoverDestinationPartnerName)
			}

			isPartnerDataNotNilAndTypePOS := partner != nil && partner.Data.Type == model.POS
			if err == nil && isPartnerDataNotNilAndTypePOS {
				handOverTo = fmt.Sprintf(`POS %s`, hndData.HandoverDestinationPartnerName)
			}

			if hndData.HandoverDestinationPartnerID == 0 && hndData.HandoverVendorCode != model.TypeVendorInternal {
				handOverTo = fmt.Sprintf(`Vendor %s`, model.MaskingVendorName[strings.ToLower(hndData.HandoverVendorCode)])
			}

			vendorCode := ""
			if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
				vendorCode = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HandoverTo
			}
			if hndData.HandoverDestinationPartnerID == 0 && hndData.HandoverVendorCode == model.TypeVendorInternal {
				handOverTo = fmt.Sprintf(`Vendor %s`, vendorCode)
			}
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package has been handed over from Subconsolidator %s %s to %s`, historyLocation3LC, historyLocationName, handOverTo)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package has been handed over from Consolidator %s %s to %s`, historyLocation3LC, historyLocationName, handOverTo)
		}
	case model.ODA:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		statusHistory = fmt.Sprintf(`Package is outside the delivery area of Consolidator %s %s`, historyLocation3LC, historyLocationName)
	case model.DEL:
		driverPhoneNumber := ""
		driverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusDEL := val.HistoryStatus != model.DEL
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusDEL && isNotHistoryIdMatch {
				continue
			}

			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			driverName = historyRemark.DriverName
			driverPhoneNumber = historyRemark.DriverPhone
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		switch strings.ToLower(statusPieceWithArrayData[hisID][0].HistoryActorRole) {
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package in delivery by %s %s from Consolidator %s %s to the receiver address`, driverName, driverPhoneNumber, historyLocation3LC, historyLocationName)
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package in delivery by %s %s from Subconsolidator %s %s to the receiver address`, driverName, driverPhoneNumber, historyLocation3LC, historyActorName)
		case model.VENDOR:
			statusHistory = fmt.Sprintf(`Package in delivery by Vendor %s to the receiver address`, model.MaskingVendorName[strings.ToLower(historyActorName)])
		case model.POS:
			statusHistory = fmt.Sprintf(`Package in delivery by %s %s from POS %s to the receiver address`, driverName, driverPhoneNumber, historyActorName)
		}

	case model.POD:
		receiverName := ""
		driverName := ""
		sttPieceID := 0
		for _, val := range statusPieceWithArrayData[hisID] {
			isNotHistoryStatusPOD := val.HistoryStatus != model.POD
			isNotHistoryIdMatch := val.HistoryID != hisID
			if isNotHistoryStatusPOD && isNotHistoryIdMatch {
				continue
			}
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			receiverName = historyRemark.ReceiverName
			sttPieceID = int(val.SttPieceID)
		}
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName

		sttPieceData, _ := c.sttPiecesRepo.Get(selfCtx, &model.SttPiecesViewParam{
			SttPieceID: sttPieceID,
		})

		if sttPieceData != nil {
			deliveryData, _ := c.deliveryRepo.GetDelivery(selfCtx, &model.DeliveryViewParam{
				SttID:          int(sttPieceData.SttPieceSttID),
				FinishedStatus: model.POD,
			})
			if deliveryData != nil {
				driverName = deliveryData.DriverName
			}
		}

		switch strings.ToLower(statusPieceWithArrayData[hisID][0].HistoryActorRole) {
		case model.VENDOR:
			statusHistory = fmt.Sprintf(`Package has been received by %s from %s Vendor`, receiverName, model.MaskingVendorName[strings.ToLower(historyActorName)])
		default:
			statusHistory = fmt.Sprintf(`Package has been received by %s from courier %s`, receiverName, driverName)
		}

	case model.DEX:

		res := &model.ReasonDetailResult{}
		sttPieceID := statusPieceWithArrayData[hisID][0].SttPieceID

		if statusPieceWithArrayData[hisID][0].HistoryReason != `` && statusPieceWithArrayData[hisID][0].HistoryReason != model.AnotherReason {
			res, _ = c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})
		}
		if statusPieceWithArrayData[hisID][0].HistoryReason == model.AnotherReason {
			for _, val := range statusPieceWithArrayData[hisID] {
				historyRemark := &model.RemarkPieceHistory{}
				if val.HistoryRemark != `` {
					json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
				}
				res.ReasonDescriptionEn = historyRemark.OtherReason
			}
		}

		deliveryData := &model.Delivery{}
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		sttPieceData, err := c.sttPiecesRepo.Get(selfCtx, &model.SttPiecesViewParam{
			SttPieceID: int(sttPieceID),
		})
		if err == nil {
			deliveryData, err = c.deliveryRepo.GetDelivery(selfCtx, &model.DeliveryViewParam{
				SttID:          int(sttPieceData.SttPieceSttID),
				FinishedStatus: model.DEX,
				Offset:         dexCount,
			})
			if err != nil || deliveryData == nil {
				deliveryData = &model.Delivery{}
			}
		}

		if res == nil {
			break
		}

		switch strings.ToLower(statusPieceWithArrayData[hisID][0].HistoryActorRole) {
		case model.VENDOR:
			statusHistory = fmt.Sprintf(`Package will be re-delivered (conditionally) due to %s by Vendor %s`, res.ReasonDescriptionEn, model.MaskingVendorName[strings.ToLower(historyActorName)])
		default:
			statusHistory = fmt.Sprintf(`Package will be re-delivered (conditionally) due to %s by courier %s`, res.ReasonDescriptionEn, deliveryData.DriverName)

		}

	case model.CODREJ:
		res := &model.ReasonDetailResult{}
		if statusPieceWithArrayData[hisID][0].HistoryReason != `` {
			res, _ = c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})
		}
		if res != nil {
			statusHistory = fmt.Sprintf(`Package will be return to sender due to %s`, res.ReasonDescriptionEn)
		}
	case model.HAL:
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName

		historyReason := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package on hold due to %s in Consolidator %s, %s`, historyReason, districtName, cityName)
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package on hold due to %s in Subconsolidator %s, %s`, historyReason, districtName, cityName)
		case model.VENDOR:
			statusHistory = fmt.Sprintf(`Package on hold due to %s in Vendor %s`, historyReason, model.MaskingVendorName[strings.ToLower(historyActorName)])
		}
	case model.RTS:
		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.VENDOR:
			historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
			statusHistory = fmt.Sprintf(`Package will be returned to sender by Vendor %s`, model.MaskingVendorName[strings.ToLower(historyActorName)])
		default:
			// default if stt number not exist
			statusHistory = `The package is returned to shipper.`
			newSttNumber := ""
			if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
				newSttNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
			}

			if newSttNumber != "" {
				statusHistory = fmt.Sprintf(`Package will be returned to sender with a new tracking number %s`, newSttNumber)
			}
		}
	case model.SCRAP:
		statusHistory = "Package has been destroyed for some reason"
	case model.CLAIM:
		statusHistory = fmt.Sprintf(`Package is in the process of being claimed by %s due to Delaye / Damage / Loss / Other reason`, statusPieceWithArrayData[hisID][0].HistoryActorName)
	case model.CNX:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		username := statusPieceWithArrayData[hisID][0].HistoryCreatedName
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName

		reason := ""
		res := &model.ReasonDetailResult{}
		if statusPieceWithArrayData[hisID][0].HistoryReason != `` {
			res, _ = c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})
		}

		if res != nil {
			reason = res.ReasonDescriptionEn
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package canceled due to %s by %s - Subconsolidator %s, %s`, reason, username, districtName, cityName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package canceled due to %s by %s - Consolidator %s, %s`, reason, username, districtName, cityName)
		case model.POS:
			statusHistory = fmt.Sprintf(`Package canceled due to %s by %s - POS %s %s, %s`, reason, username, historyActorName, districtName, cityName)
		case model.CLIENT:
			statusHistory = fmt.Sprintf(`Package canceled due to %s by %s - CLIENT %s %s, %s`, reason, username, historyActorName, districtName, cityName)
		case model.INTERNAL:
			statusHistory = fmt.Sprintf(`Package canceled due to %s by %s - internal %s`, reason, username, historyActorName)
		}
	case model.STTADJUSTED:
		username := statusPieceWithArrayData[hisID][0].HistoryCreatedName
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package Weight / Other has been changed by %s - Subconsolidator %s %s`, username, historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package Weight / Other has been changed by %s - Consolidator %s %s`, username, historyLocation3LC, historyLocationName)
		case model.POS:
			statusHistory = fmt.Sprintf(`Package Weight / Other has been changed by %s - POS %s `, username, historyActorName)
		case model.CLIENT:
			statusHistory = fmt.Sprintf(`Package Weight / Other has been changed by %s - CLIENT %s `, username, historyActorName)
		default:
			statusHistory = fmt.Sprintf(`Package Weight / Other has been changed by %s - %s`, username, historyActorName)
		}
	case model.STTADJUSTEDPOD:
		username := statusPieceWithArrayData[hisID][0].HistoryCreatedName
		statusHistory = fmt.Sprintf(`Package Weight/Order has been changed by %s`, username)
	case model.PICKUPTRUCKING:
		statusHistory = "Package has been Pick Up using Truck"
	case model.DROPOFFTRUCKING:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf("Package has been Dropped Off using Truck at Subconsolidator %s %s", historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf("Package has been Dropped Off using Truck at Consolidator %s %s", historyLocation3LC, historyLocationName)
		}
	case model.MISSING:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName

		historyReason := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package is lost during shipping process from %s due to %s`, historyLocation3LC, historyReason)
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package is lost during shipping process from Subconsolidator %s %s due to %s`, historyLocation3LC, historyActorName, historyReason)
		case model.VENDOR:
			statusHistory = fmt.Sprintf(`Package is lost during shipping process from Transit City %s due to %s`, historyLocation3LC, historyReason)
		}

	case model.DAMAGE:
		// statusHistory = "Paket dalam kondisi rusak saat dalam pengiriman ke konsol/subkonsol/kota transit"
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf("Package is in damaged during shipping process from Subconsolidator %s %s", historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf("Package is in damaged during shipping process from Consolidator %s", historyLocation3LC)
		}
	case model.NOTRECEIVED:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf("The package not received by Subconsolidator %s, %s.", districtName, cityName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf("The package not received by Consolidator %s, %s.", districtName, cityName)
		}
	case model.CI:
		historyActorName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyActorName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		statusHistory = fmt.Sprintf(`Package lost/corrupted & will be charged to partner due to %s`, historyActorName)
	case model.RTSHQ:
		newSttNumber := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			newSttNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		statusHistory = fmt.Sprintf(`The package is returned to Lion Parcel Center with a new STT number %s`, newSttNumber)

	case model.OCC:
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf("Package in the process of Customs checking by Subconsolidator %s %s", historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf("Package in the process of Customs checking by Consolidator %s", historyLocation3LC)
		}

	case model.MISBOOKING:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}
		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package misbooking and will be redelivered from Subconsolidator %s, %s.`, districtName, cityName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package misbooking and will be redelivered from Consolidator %s, %s.`, districtName, cityName)
		}
	case model.SCRAPCD:
		statusHistory = `Package has been destroyed for some reason`

	case model.OCCEXP:
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole

		if strings.ToUpper(historyActorName) == model.Luwjistik && historyActorRole == model.VENDOR {
			break
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation

		switch historyActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package under Customs check by Subconsolidator %s %s`, historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package under Customs check by Consolidator %s`, historyLocation3LC)
		}

	case model.OCCIMP:
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole
		if strings.ToUpper(historyActorName) == model.Luwjistik && historyActorRole == model.VENDOR {
			break
		}

		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation

		switch historyActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package Customs inspection completed by Subconsolidator %s %s`, historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package Customs inspection completed by Consolidator %s`, historyLocation3LC)
		}
	case model.OCCHAL:
		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		switch historyActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`The package on hold customs in Subconsolidator %s, %s.`, districtName, cityName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`The package on hold customs in Consolidator %s, %s.`, districtName, cityName)
		}
	case model.OCRIMP:
		historyActorName := statusPieceWithArrayData[hisID][0].HistoryActorName
		historyActorRole := statusPieceWithArrayData[hisID][0].HistoryActorRole
		historyLocation3LC := statusPieceWithArrayData[hisID][0].HistoryLocation
		switch historyActorRole {
		case model.SUBCONSOLE:
			statusHistory = fmt.Sprintf(`Package Customs inspection completed by Subconsolidator %s %s`, historyLocation3LC, historyActorName)
		case model.CONSOLE:
			statusHistory = fmt.Sprintf(`Package Customs inspection completed by Consolidator %s`, historyLocation3LC)
		}
	case model.RCCIMP:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}
		statusHistory = fmt.Sprintf(`Package Customs inspection completed by Consolidator %s, %s.`, districtName, cityName)
	}

	return statusHistory
}

func (c *sttCtx) generateSttTrackingDescriptionForClientEnglish(ctx context.Context, status string, hisID int, statusPieceWithArrayData map[int][]model.SttPieceHistory, token string, productType string, countryName string, countyDestName string, historyActorName string, historyActorRole string) string {
	opName := "sttCtx-generateSttTrackingDescriptionForClientEnglish"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"result": nil, "error": err})
	}()

	var statusHistory string = ""
	if len(statusPieceWithArrayData[hisID]) < 1 {
		return statusHistory
	}
	switch status {
	case model.BKD:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Your package has been processed by Lion Parcel Agent in %s, %s`, cityName, districtName)
	case model.PUP, model.PUPC:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Your package will be delivered to Lion Parcel Warehouse in %s, %s`, cityName, districtName)
	case model.STISC:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Your package has arrived at Lion Parcel Warehouse %s, %s`, cityName, districtName)
	case model.STI:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Your package has arrived at Lion Parcel Warehouse %s, %s`, cityName, districtName)
	case model.CARGOPLANE:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		actorExternalCode := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			actorExternalCode = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().ActorExternalCode
		}

		cityINTHND := ``
		districtINTHND := ``
		for _, sttPieceHistory := range statusPieceWithArrayData {
			if sttPieceHistory[0].HistoryStatus == model.INTHND && sttPieceHistory[0].RemarkPieceHistoryToStruct() != nil {
				cityINTHND = sttPieceHistory[0].RemarkPieceHistoryToStruct().HistoryLocationName
				districtINTHND = sttPieceHistory[0].RemarkPieceHistoryToStruct().HistoryDistrictName
				break
			}
		}

		statusHistory = fmt.Sprintf(`Your package will be flown by plane from %s, %s`, cityName, districtName)
		if actorExternalCode == model.LuwjistikName {
			statusHistory = fmt.Sprintf(`Your package will be flown by plane from %s, %s`, cityINTHND, districtINTHND)
		}
	case model.CARGOTRUCK:
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}

		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Your package will be delivered using Truck from %s, %s`, historyLocationName, districtName)

	case model.CARGOTRAIN:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Your package will be delivered using Train from %s, %s `, cityName, districtName)
	case model.CARGOSHIP:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Your package will be delivered using a ship from %s, %s `, cityName, districtName)
	case model.TRANSIT:
		historyLocationName := ""
		historyActorExternal := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			historyActorExternal = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().ActorExternalCode
		}

		statusHistory = fmt.Sprintf(`Your package is currently in transit in %s`, historyLocationName)
		if historyActorExternal == model.LuwjistikName {
			statusHistory = "Your package is currently in transit"
		}
	case model.STIDEST:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}
		statusHistory = fmt.Sprintf(`Your package has arrived at Lion Parcel Warehouse in %s, %s`, cityName, districtName)

	case model.STIDESTSC:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Your package has arrived at Lion Parcel Transit Warehouse in %s, %s`, cityName, districtName)
	case model.DEL:
		driverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			driverName = historyRemark.DriverName
		}
		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.VENDOR:
			statusHistory = `Your package is being delivered to recipient's address by Courier. Please ensure the recipient's phone number is accessible to the courier.`
		default:
			statusHistory = fmt.Sprintf(`Your package is being delivered to recipient's address by Courier %s. Please ensure the recipient's phone number is accessible to the courier.`, driverName)
		}

	case model.POD:
		receiverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			receiverName = historyRemark.ReceiverName
		}

		statusHistory = fmt.Sprintf(`Your package has arrived at its destination and received by %s`, receiverName)

	case model.DEX:
		reasonDescription := ""
		res := &model.ReasonDetailResult{}
		if statusPieceWithArrayData[hisID][0].HistoryReason != `` && statusPieceWithArrayData[hisID][0].HistoryReason != model.AnotherReason {
			res, _ = c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})
			reasonDescription = res.ReasonDescriptionEn
		}
		if statusPieceWithArrayData[hisID][0].HistoryReason == model.AnotherReason {
			for _, val := range statusPieceWithArrayData[hisID] {
				historyRemark := &model.RemarkPieceHistory{}
				if val.HistoryRemark != `` {
					json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
				}
				res.ReasonDescriptionEn = historyRemark.OtherReason
			}
		}

		statusHistory = fmt.Sprintf(`Your package delivery failed due to %s. Please make sure the recipient's address and contact details are correct for re-delivery process.`, reasonDescription)

	case model.CODREJ:
		res := &model.ReasonDetailResult{}
		if statusPieceWithArrayData[hisID][0].HistoryReason != `` {
			res, _ = c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})
		}
		if res != nil {
			statusHistory = fmt.Sprintf(`Your COD package has been returned to sender's address due to %s. Immediately check your shipment to our customer service on Whatsapp.`, res.ReasonDescriptionEn)
		}
	case model.HAL:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.VENDOR:
			statusHistory = fmt.Sprintf(`Your package is in Lion Parcel Warehouse in %s, %s.`, cityName, districtName)
		default:
			statusHistory = fmt.Sprintf(`Your package is in Lion Parcel warehouse in %s, %s`, districtName, cityName)
		}

	case model.RTS:
		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.VENDOR:
			statusHistory = `Your package will be returned to sender's address for some reason.`
		default:
			// default if stt number not exist
			statusHistory = `The package is returned to shipper.`
			newSttNumber := ""
			if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
				newSttNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
			}

			if newSttNumber != "" {
				statusHistory = fmt.Sprintf(`Your package will be returned to sender's address with a new tracking number %s.`, newSttNumber)
			}
		}
	case model.CLAIM:
		claimNumber := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			claimNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().ClaimNo
		}

		statusHistory = fmt.Sprintf(`Your package is in claim process. Please check your claim number %s for details`, claimNumber)
	case model.CNX:
		res := &model.ReasonDetailResult{}
		if statusPieceWithArrayData[hisID][0].HistoryReason != `` {
			res, _ = c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})
		}
		if res != nil {
			statusHistory = fmt.Sprintf(`Shipment canceled due to %s`, res.ReasonDescriptionEn)
		}
	case model.STTADJUSTED:
		historyReason := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		statusHistory = fmt.Sprintf(`Information changes and price adjustments have occured on your package due to %s`, historyReason)
	case model.STTADJUSTEDPOD:
		statusHistory = "There has been a price adjustment related to the weight and volume of your package"
	case model.PICKUPTRUCKING:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Your package has been delivered using Truck from %s, %s`, cityName, districtName)
	case model.REJECTED:
		historyReason := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

		if len(historyReason) > 0 {
			reason, _ := c.reasonRepo.GetDetail(ctx, &model.ReasonViewParams{
				ReasonTitle: historyReason,
			})
			if reason != nil {
				historyReason = reason.ReasonDescriptionEn
			}
		}

		statusHistory = fmt.Sprintf("Shipment fleet changed due to %s", historyReason)
	case model.NOTRECEIVED:
		statusHistory = "Shipping issue occurred."
	case model.DAMAGE:
		statusHistory = "Your package delivery is in restraint. Immediately check your delivery with our customer service on Whatsapp"
	case model.MISSING:
		historyReason := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		statusHistory = fmt.Sprintf("Your package delivery is in restraint due to %s. Immediately check your delivery with our customer service on Whatsapp", historyReason)
	case model.RTSHQ:
		newSttNumber := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			newSttNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		statusHistory = fmt.Sprintf(`Package has been returned to Lion Parcel Central with a new tracking number %s.`, newSttNumber)
	case model.OCC:
		statusHistory = fmt.Sprintf(`Your package is in Customs checking process to be delivered to the destination %s. Immediately check your delivery with our customer service on WhatsApp`, countryName)
	case model.REROUTE:
		statusHistory = "The package destination changed."
	case model.MISBOOKING:
		statusHistory = `Shipping issue occurred`
	case model.OCCEXP:
		statusHistory = fmt.Sprintf(`Your package is under inspection Customs to be delivered to the destination country %s`, countyDestName)
		if strings.ToUpper(historyActorName) != model.Luwjistik && historyActorRole != model.VENDOR {
			statusHistory = fmt.Sprintf(`Your package is under inspection Customs to be delivered to the destination country %s`, countryName)
		}
	case model.OCCIMP:
		statusHistory = fmt.Sprintf(`Your package has been cleared Customs check and has been received in the destination country %s`, countyDestName)
		if strings.ToUpper(historyActorName) != model.Luwjistik && historyActorRole != model.VENDOR {
			statusHistory = fmt.Sprintf(`Your package has been cleared Customs check and has been received in the destination country %s`, countryName)
		}
	case model.OCCHAL:
		statusHistory = fmt.Sprintf(`Your packing is at Lion Parcel Warehouse in %s`, countyDestName)

		if strings.ToUpper(historyActorName) == model.Luwjistik && historyActorRole == model.VENDOR {
			break
		}

		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}
		statusHistory = fmt.Sprintf(`The package on hold customs in %s, %s.`, districtName, cityName)
	case model.OCRIMP:
		statusHistory = fmt.Sprintf(`Your package has been cleared Customs check and has been received in the destination country %s`, countryName)
	case model.RCCIMP:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}
		statusHistory = fmt.Sprintf(`Your package has been cleared Customs in %s, %s.`, districtName, cityName)
	}

	return statusHistory
}

/* Generator Description per status */

func generatorStiDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			hubCityName := ""
			hubId := 0
			hub3LC := ""
			hubDistrictName := ""

			historyLocation3LC := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryLocation
			historyLocationName := ""
			hystoryDistrictName := ""
			remarkPieceHistoryToStruct := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct()

			if remarkPieceHistoryToStruct != nil {
				hubId = remarkPieceHistoryToStruct.HubID
				hubCityName = remarkPieceHistoryToStruct.HubCityName

				historyLocationName = remarkPieceHistoryToStruct.HistoryLocationName
				hystoryDistrictName = remarkPieceHistoryToStruct.HistoryDistrictName

				hub3LC = remarkPieceHistoryToStruct.HubOriginCity
				hubDistrictName = remarkPieceHistoryToStruct.HubDistrictName
			}

			if hubId != 0 {
				historyLocation3LC, historyLocationName, hystoryDistrictName = hub3LC, hubDistrictName, hubCityName
			}

			return fmt.Sprintf(`Paket telah sampai di Consolidator %s %s, %s`, historyLocation3LC, historyLocationName, hystoryDistrictName)
		}
	}

	if cond.IsClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			return ""
		}
	}

	return f
}

func generatorBkdDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			return fmt.Sprintf(`Paket telah dibooking oleh %s %s di sistem Lion Parcel.`,
				model.MaskingActorRole[reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole], reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorName)
		}
	}

	return f
}

func generatorPupAndPupcDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			return fmt.Sprintf(`Paket diteruskan oleh POS %s.`, reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorName)
		}
	}

	return f
}

func generatorStiScDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			historyLocation3LC := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryLocation
			return fmt.Sprintf(`Paket telah sampai di Subconsolidator %s %s.`, historyLocation3LC, reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorName)
		}
	}

	return f
}

func generatorStoScDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			historyLocation3LC := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryLocation
			return fmt.Sprintf(`Paket telah dijemput oleh Shuttle dari Subconsolidator %s %s.`, historyLocation3LC, reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorName)
		}
	}

	return f
}

func generatorSttRemoveDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			statusHistory := ""
			historyRemark := reqData.GetRemarkPieceHistory(reqData.SttStatus)
			bagNumber := historyRemark.BagNumber
			cargoNumber := historyRemark.CargoNumber

			sttPieceHistories := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0]

			numberCargoOrBag := getWordingFromCargoOrBag(cargoNumber, bagNumber)

			historyLocation3LC := sttPieceHistories.HistoryLocation
			historyActorName := sttPieceHistories.HistoryActorName
			historyLocationName := historyRemark.HistoryLocationName
			historyDistrictName := historyRemark.HistoryDistrictName

			hubId := 0
			hub3LC := ""
			hubCityName := ""
			hubDistrictName := ""

			hubId = historyRemark.HubID
			hub3LC = historyRemark.HubOriginCity
			hubCityName = historyRemark.HubCityName
			hubDistrictName = historyRemark.HubDistrictName

			if hubId != 0 {
				historyLocation3LC = hub3LC
				historyLocationName = hubCityName
				historyDistrictName = hubDistrictName
			}

			switch reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket telah dikeluarkan oleh Subconsolidator %s %s %s`, historyLocation3LC, historyActorName, numberCargoOrBag)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket telah dikeluarkan oleh Consolidator %s %s, %s %s`, historyLocation3LC, historyLocationName, historyDistrictName, numberCargoOrBag)
			}
			return statusHistory
		}
	}

	return f
}

func (c *sttCtx) generatorRejectedDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			reason := `tidak dapat diterbangkan`
			remark := reqData.GetRemarkPieceHistory(reqData.SttStatus)
			if remark.CustomProcessRemarks != "" {
				reason = remark.CustomProcessRemarks
			}

			if len(reason) > 0 {
				ctx, cancel := lputils.CreateContext(30)
				defer cancel()

				result, _ := c.reasonRepo.GetDetail(ctx, &model.ReasonViewParams{
					ReasonTitle: reason,
				})
				if result != nil {
					reason = result.ReasonDescription
				}
			}

			return fmt.Sprintf(`Paket akan dialihkan menggunakan moda lain karena %s`, reason)
		}
	}

	return f
}

func generatorShortlandDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			statusHistory := ""
			historyLocation3LC := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryLocation
			historyActorName := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorName
			historyLocationName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryLocationName

			historyRemark := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct()
			historyDistrictName := historyRemark.HistoryDistrictName

			hubId := 0
			hub3LC := ""
			hubCityName := ""
			hubDistrictName := ""

			hubId = historyRemark.HubID
			hub3LC = historyRemark.HubOriginCity
			hubCityName = historyRemark.HubCityName
			hubDistrictName = historyRemark.HubDistrictName

			if hubId != 0 {
				historyLocation3LC = hub3LC
				historyLocationName = hubCityName
				historyDistrictName = hubDistrictName
			}

			switch reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket belum diterima oleh Subconsolidator %s %s`, historyLocation3LC, historyActorName)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket belum diterima oleh Consolidator %s %s, %s`, historyLocation3LC, historyLocationName, historyDistrictName)
			}

			return statusHistory
		}
	}

	return f
}

func generatorTransitDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			historyLocation3LC := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryLocation
			historyLocationName := ""
			statusHistory := ""
			actorExternalCode := ""
			historyDistrictName := ""

			hubId := 0
			hub3LC := ""
			hubCityName := ""
			hubDistrictName := ""

			if reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct() != nil {
				historyLocationName = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HistoryLocationName
				actorExternalCode = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().ActorExternalCode
				historyDistrictName = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HistoryDistrictName

				hubId = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HubID
				hub3LC = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HubOriginCity
				hubCityName = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HubCityName
				hubDistrictName = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HubDistrictName
			}

			if hubId != 0 {
				historyLocation3LC = hub3LC
				historyLocationName = hubCityName
				historyDistrictName = hubDistrictName
			}

			statusHistory = fmt.Sprintf(`Paket telah sampai di kota transit %s %s, %s`, historyLocation3LC, historyLocationName, historyDistrictName)
			if actorExternalCode == model.LuwjistikName {
				statusHistory = "Paket telah sampai di Kota Transit oleh Vendor LUW"
			}
			return statusHistory
		}
	}

	return f
}

func generatorMisrouteDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			statusHistory := ""
			historyLocation3LC := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryLocation
			historyActorName := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorName
			historyLocationName := ""
			historyDistrictName := ""

			hubId := 0
			hub3LC := ""
			hubCityName := ""
			hubDistrictName := ""

			if reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct() != nil {
				historyRemark := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct()
				historyDistrictName = historyRemark.HistoryDistrictName
				historyLocationName = historyRemark.HistoryLocationName

				hubId = historyRemark.HubID
				hub3LC = historyRemark.HubOriginCity
				hubCityName = historyRemark.HubCityName
				hubDistrictName = historyRemark.HubDistrictName
			}

			if hubId != 0 {
				historyLocation3LC = hub3LC
				historyLocationName = hubCityName
				historyDistrictName = hubDistrictName
			}

			switch reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket telah diterima Subconsolidator %s %s yang tidak sesuai`, historyLocation3LC, historyActorName)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket telah diterima Consolidator %s %s, %s yang tidak sesuai`, historyLocation3LC, historyLocationName, historyDistrictName)
			}

			return statusHistory
		}
	}

	return f
}

func generatorRerouteDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			statusHistory := ""
			historyLocation3LC := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryLocation
			historyActorName := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorName
			historyLocationName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryLocationName
			newSttNumber := reqData.GetRemarkPieceHistory(reqData.SttStatus).CustomProcessRemarks
			historyDistrictName := ""

			hubId := 0
			hub3LC := ""
			hubCityName := ""
			hubDistrictName := ""

			if reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct() != nil {
				historyRemark := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct()
				historyDistrictName = historyRemark.HistoryDistrictName
				historyLocationName = historyRemark.HistoryLocationName

				hubId = historyRemark.HubID
				hub3LC = historyRemark.HubOriginCity
				hubCityName = historyRemark.HubCityName
				hubDistrictName = historyRemark.HubDistrictName
			}

			if hubId != 0 {
				historyLocation3LC = hub3LC
				historyLocationName = hubCityName
				historyDistrictName = hubDistrictName
			}

			switch reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket misroute akan dikirim ulang dari Subconsolidator %s %s dengan nomor STT terbaru %s`, historyLocation3LC, historyActorName, newSttNumber)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket misroute akan dikirim ulang dari Consolidator %s %s, %s dengan nomor STT terbaru %s`, historyLocation3LC, historyLocationName, historyDistrictName, newSttNumber)
			}

			if newSttNumber == "" {
				switch reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole {
				case model.SUBCONSOLE:
					statusHistory = fmt.Sprintf(`Paket misroute akan dikirim ulang dari Subconsolidator %s %s.`, historyLocation3LC, historyActorName)
				case model.CONSOLE:
					statusHistory = fmt.Sprintf(`Paket misroute akan dikirim ulang dari Consolidator %s %s.`, historyLocation3LC, historyLocationName)
				}
			}

			return statusHistory
		}

	}

	return f
}

func generatorStiDestDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			statusHistory := ""
			if reqData.ProductType == model.INTERPACK {
				statusHistory = fmt.Sprintf(`Paket telah sampai di Negara %s`, reqData.CountryName)
				return statusHistory
			}
			historyLocation3LC := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryLocation
			historyLocationName := ""
			historyDistrictName := ""

			hubId := 0
			hub3LC := ""
			hubCityName := ""
			hubDistrictName := ""

			if reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct() != nil {
				historyLocationName = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HistoryLocationName
				historyDistrictName = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HistoryDistrictName

				hubId = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HubID
				hub3LC = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HubOriginCity
				hubCityName = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HubCityName
				hubDistrictName = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HubDistrictName
			}

			if hubId != 0 {
				historyLocation3LC = hub3LC
				historyLocationName = hubCityName
				historyDistrictName = hubDistrictName
			}

			statusHistory = fmt.Sprintf(`Paket telah sampai di Consolidator %s %s, %s`, historyLocation3LC, historyLocationName, historyDistrictName)

			return statusHistory
		}
	}

	return f
}

func generatorStiDestScDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			statusHistory := ""
			historyLocation3LC := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryLocation
			historyActorName := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorName

			switch reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket telah sampai di Subconsolidator %s %s`, historyLocation3LC, historyActorName)
			case model.VENDOR:
				statusHistory = fmt.Sprintf(`Paket telah sampai di Vendor %s`, model.MaskingVendorName[strings.ToLower(historyActorName)])
			}
			return statusHistory
		}
	}

	return f
}

func (c *sttCtx) generatorCargoTrainDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			historyRemark := reqData.GetRemarkPieceHistory(reqData.SttStatus)
			cargoNumber := historyRemark.CargoNumber
			statusHistory := ""

			sttPieceHistories := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0]

			historyLocation3LC := sttPieceHistories.HistoryLocation
			historyActorName := sttPieceHistories.HistoryActorName
			historyDistrictName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryDistrictName
			historyLocationName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryLocationName

			hubId := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubID
			hub3LC := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubOriginCity
			hubDistrictName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubDistrictName
			hubCityName := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HubCityName

			if hubId != 0 {
				historyLocation3LC = hub3LC
				historyDistrictName = hubDistrictName
				historyLocationName = hubCityName
			}

			dest3LC, destCityName := c.getDest3lcAndCityName(reqData.Ctx, cargoNumber)

			switch reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diberangkatkan oleh Subconsolidator %s %s, %s %s ke kota %s %s menggunakan kereta dengan No. Cargo %s`, historyLocation3LC, historyLocationName, historyDistrictName, historyActorName, dest3LC, destCityName, cargoNumber)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diberangkatkan oleh Consolidator %s %s, %s ke kota %s %s menggunakan kereta dengan No. Cargo %s`, historyLocation3LC, historyLocationName, historyDistrictName, dest3LC, destCityName, cargoNumber)
			}
			return statusHistory
		}
	}

	return f
}

func (c *sttCtx) generatorCargoShipDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			cargoNumber := reqData.GetRemarkPieceHistory(reqData.SttStatus).CargoNumber
			statusHistory := ""

			sttPieceHistories := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0]

			historyLocation3LC := sttPieceHistories.HistoryLocation
			historyActorName := sttPieceHistories.HistoryActorName
			historyLocationName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryLocationName
			historyDistrictName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryDistrictName

			hubId := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubID
			hub3LC := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubOriginCity
			hubCityName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubCityName
			hubDistrictName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubDistrictName

			if hubId != 0 {
				historyLocation3LC = hub3LC
				historyLocationName = hubCityName
				historyDistrictName = hubDistrictName
			}

			dest3LC, destCityName := c.getDest3lcAndCityName(reqData.Ctx, cargoNumber)

			switch reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diberangkatkan oleh Subconsolidator %s %s, %s %s ke Kota %s menggunakan kapal dengan No. Cargo %s`, historyLocation3LC, historyLocationName, historyDistrictName, historyActorName, reqData.CountryName, cargoNumber)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diberangkatkan oleh Consolidator %s %s, %s ke Kota %s menggunakan kapal dengan No. Cargo %s`, historyLocation3LC, historyLocationName, historyDistrictName, reqData.CountryName, cargoNumber)
			}

			if reqData.ProductType == model.INTERPACK {
				return statusHistory
			}

			switch reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diberangkatkan oleh Subconsolidator %s %s, %s %s ke kota %s %s menggunakan Kapal dengan No. Cargo %s`, historyLocation3LC, historyLocationName, historyDistrictName, historyActorName, dest3LC, destCityName, cargoNumber)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diberangkatkan oleh Consolidator %s %s, %s ke kota %s %s menggunakan Kapal dengan No. Cargo %s`, historyLocation3LC, historyLocationName, historyDistrictName, dest3LC, destCityName, cargoNumber)
			}

			return statusHistory
		}
	}

	return f
}

func (c *sttCtx) generatorCargoTruckDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			cargoNumber := reqData.GetRemarkPieceHistory(reqData.SttStatus).CargoNumber
			statusHistory := ""

			sttPieceHistories := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0]

			historyLocation3LC := sttPieceHistories.HistoryLocation
			historyActorName := sttPieceHistories.HistoryActorName
			historyLocationName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryLocationName
			historyDistrictName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryDistrictName

			hubId := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubID
			hub3LC := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubOriginCity
			hubCityName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubCityName
			hubDistrictName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubDistrictName

			if hubId != 0 {
				historyLocation3LC = hub3LC
				historyLocationName = hubCityName
				historyDistrictName = hubDistrictName
			}

			dest3LC, destCityName := c.getDest3lcAndCityName(reqData.Ctx, cargoNumber)

			switch reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diberangkatkan oleh Subconsolidator %s %s, %s %s ke Negara %s dengan No. Cargo %s`, historyLocation3LC, historyLocationName, historyDistrictName, historyActorName, reqData.CountryName, cargoNumber)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diberangkatkan oleh Consolidator %s %s, %s ke Negara %s dengan No. Cargo %s`, historyLocation3LC, historyLocationName, historyDistrictName, reqData.CountryName, cargoNumber)
			}

			if reqData.ProductType == model.INTERPACK {
				return statusHistory
			}

			switch reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diberangkatkan oleh Subconsolidator %s %s, %s %s ke kota %s %s menggunakan Truk dengan No. Cargo %s`, historyLocation3LC, historyLocationName, historyDistrictName, historyActorName, dest3LC, destCityName, cargoNumber)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diberangkatkan oleh Consolidator %s %s, %s ke kota %s %s menggunakan Truk dengan No. Cargo %s`, historyLocation3LC, historyLocationName, historyDistrictName, dest3LC, destCityName, cargoNumber)
			}
			return statusHistory
		}

	}

	return f
}

func (c *sttCtx) generatorBaggingDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			statusHistory := ""
			bagNumber := getBagNumberList(reqData)

			sttPieceHistories := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0]

			historyLocation3LC := sttPieceHistories.HistoryLocation
			historyActorName := sttPieceHistories.HistoryActorName
			historyLocationName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryLocationName
			historyDistrictName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryDistrictName

			hubId := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubID
			hub3LC := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubOriginCity
			hubCityName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubCityName
			hubDistrictName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubDistrictName

			if hubId != 0 {
				historyLocation3LC = hub3LC
				historyLocationName = hubCityName
				historyDistrictName = hubDistrictName
			}

			switch sttPieceHistories.HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket telah disortir oleh Subconsolidator %s %s, %s %s dengan No. Bag %s`, historyLocation3LC, historyLocationName, historyDistrictName, historyActorName, strings.Join(bagNumber, ","))
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket telah disortir oleh Consolidator %s %s, %s dengan No. Bag %s`, historyLocation3LC, historyLocationName, historyDistrictName, strings.Join(bagNumber, ","))
			case model.INTERNAL:
				bagVendorNo := bagNumber[0]
				bagVendor, err := c.bagVendorRepo.Get(context.Background(), model.BagVendor{
					BvBagNo: bagNumber[0],
				})
				if err == nil || bagVendor != nil {
					bagVendorNo = bagVendor.BvBagVendorNo
				}
				statusHistory = fmt.Sprintf(`Paket telah disortir oleh LILO %s %s dengan No. Bag %s`, historyLocation3LC, historyLocationName, bagVendorNo)
			}

			return statusHistory
		}
	}

	return f
}

func (c *sttCtx) generatorCargoPlaneDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			cargoNumber := reqData.GetRemarkPieceHistory(reqData.SttStatus).CargoNumber
			actorExternalCode := reqData.GetRemarkPieceHistory(reqData.SttStatus).ActorExternalCode
			statusHistory := ""

			sttPieceHistories := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0]

			historyLocation3LC := sttPieceHistories.HistoryLocation
			historyActorName := sttPieceHistories.HistoryActorName
			historyLocationName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryLocationName
			historyDistrictName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryDistrictName

			hubId := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubID
			hub3LC := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubOriginCity
			hubCityName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubCityName
			hubDistrictName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubDistrictName

			if hubId != 0 {
				historyLocation3LC = hub3LC
				historyLocationName = hubCityName
				historyDistrictName = hubDistrictName
			}

			dest3LC := c.getDest3LC(reqData.Ctx, cargoNumber)

			switch reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diterbangkan oleh Subconsolidator %s %s, %s %s ke kota %s dengan No. AWB %s`, historyLocation3LC, historyLocationName, historyDistrictName, historyActorName, dest3LC, cargoNumber)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diterbangkan oleh Consolidator %s %s, %s ke kota %s dengan No. AWB %s`, historyLocation3LC, historyLocationName, historyDistrictName, dest3LC, cargoNumber)
			}
			if actorExternalCode != model.LuwjistikName {
				return statusHistory
			}

			historyActorRole, cityINTHND, historyLocation3LCINTHND, historyActorNameINTHND := getDataCargoPlane(reqData)
			switch historyActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diterbangkan oleh Subconsolidator %s %s dengan Vendor LUW`, historyLocation3LCINTHND, historyActorNameINTHND)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket akan diterbangkan oleh Consolidator %s %s dengan Vendor LUW`, historyLocation3LCINTHND, cityINTHND)
			}

			return statusHistory
		}

	}

	return f
}

func (c *sttCtx) generatorHndDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {
			statusHistory := ""
			handOverTo := ""
			sttPieceID := 0
			for _, val := range reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID] {
				isNotHistoryHnd := val.HistoryStatus != model.HND
				isNotMatchHistoryID := val.HistoryID != reqData.SttPieceHistoryID
				if isNotHistoryHnd && isNotMatchHistoryID {
					continue
				}
				historyRemark := &model.RemarkPieceHistory{}
				if val.HistoryRemark != `` {
					json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
				}
				sttPieceID = int(val.SttPieceID)
			}

			hubId, hubCityName, hubDistrictName, hub3LC := 0, "", "", ""

			historyLocation3LC := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryLocation
			historyActorName := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorName
			historyDistrictName, historyLocationName := "", ""

			history := reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct()

			if history != nil {
				historyLocationName, historyDistrictName = history.HistoryLocationName, history.HistoryDistrictName
				hubId, hub3LC, hubCityName, hubDistrictName = history.HubID, history.HubOriginCity, history.HubCityName, history.HubDistrictName
			}

			if hubId != 0 {
				historyLocation3LC, historyLocationName, historyDistrictName = hub3LC, hubCityName, hubDistrictName
			}

			var hndDetailData *model.HandoverDetail
			var err error
			if sttPieceID != 0 {
				hndDetailData = &model.HandoverDetail{
					HandoverDetailSttPieceID: sttPieceID,
				}
				hndDetailData, _ = c.handoverDetailRepo.Get(reqData.Ctx, hndDetailData)
			}

			var hndData *model.Handover
			if hndDetailData != nil && hndDetailData.HandoverDetailHandoverID != 0 {
				getParams := &model.HandoverViewParams{}
				getParams.HandoverID = hndDetailData.HandoverDetailHandoverID
				hndData, _ = c.handoverRepo.Get(reqData.Ctx, getParams)
			}

			switch reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].HistoryActorRole {
			case model.SUBCONSOLE:
				statusHistory = fmt.Sprintf(`Paket telah diserahkan Subconsolidator %s %s, %s %s ke %s`, historyLocation3LC, historyLocationName, historyDistrictName, historyActorName, handOverTo)
			case model.CONSOLE:
				statusHistory = fmt.Sprintf(`Paket telah diserahkan Consolidator %s %s, %s ke %s`, historyLocation3LC, historyLocationName, historyDistrictName, handOverTo)
			}

			if hndData == nil {
				return statusHistory
			}

			partner, err := c.partnerRepo.GetByID(reqData.Ctx, hndData.HandoverDestinationPartnerID, reqData.Token)
			isPartnerNotNilAndSubconsoleType := partner != nil && partner.Data.Type == model.SUBCONSOLE
			if err == nil && isPartnerNotNilAndSubconsoleType {
				handOverTo = fmt.Sprintf(`Subconsolidator %s %s`, hndData.HandoverArrivalCityCode, hndData.HandoverDestinationPartnerName)
			}

			isPartnerNotNilAndPosType := partner != nil && partner.Data.Type == model.POS
			if err == nil && isPartnerNotNilAndPosType {
				handOverTo = fmt.Sprintf(`POS %s`, hndData.HandoverDestinationPartnerName)
			}
			if hndData.HandoverDestinationPartnerID == 0 && hndData.HandoverVendorCode != model.TypeVendorInternal {
				handOverTo = fmt.Sprintf(`Vendor %s`, model.MaskingVendorName[strings.ToLower(hndData.HandoverVendorCode)])
			}

			vendorCode := ""
			if reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct() != nil {
				vendorCode = reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID][0].RemarkPieceHistoryToStruct().HandoverTo
			}
			if hndData.HandoverDestinationPartnerID == 0 && hndData.HandoverVendorCode == model.TypeVendorInternal {
				handOverTo = fmt.Sprintf(`Vendor %s`, vendorCode)
			}

			statusHistory = fmt.Sprintf("%s %s", statusHistory, handOverTo)

			return statusHistory
		}
	}

	return f
}

func (c *sttCtx) generatorInHubDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {

			hubId := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubID
			hub3LC := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubOriginCity
			hubName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubName
			historyLocationName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryLocationName
			historyDistrictName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryDistrictName

			desc := fmt.Sprintf(`Paket telah sampai di Consolidator %s, %s`, historyLocationName, historyDistrictName)
			if hubId != 0 {
				desc = fmt.Sprintf(`Paket telah sampai di Hub %s %s`, hub3LC, hubName)
			}

			if len(reqData.GetRemarkPieceHistory(reqData.SttStatus).CustomProcessRemarks) != 0 {
				vehicleNumber := reqData.GetRemarkPieceHistory(reqData.SttStatus).CustomProcessRemarks
				desc = fmt.Sprintf("%s dengan nomor kendaraan %s", desc, vehicleNumber)
			}

			return desc
		}
	}

	return f
}

func (c *sttCtx) generatorOutHubDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {

			hubId := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubID
			hub3LC := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubOriginCity
			hubName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubName
			hubType := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubDestinationType
			hubDestinationID := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubDestinationID
			hubDestinationCity := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubDestinationCity
			hubDestinationName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubDestinationName
			historyLocationName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryLocationName
			historyDistrictName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryDistrictName
			partnerDesCity, partnerDesDistrict := c.getPartnerDes(reqData.Ctx, hubDestinationCity, reqData.Token)

			desc := fmt.Sprintf(`Paket telah berangkat dari Consolidator %s, %s ke Consolidator %s, %s`, historyLocationName, historyDistrictName, partnerDesCity, partnerDesDistrict)
			if hubId != 0 && hubType == model.HubTypeHub {
				desc = fmt.Sprintf(`Paket telah berangkat dari Hub %s %s ke Hub %s %s`, hub3LC, hubName, hubDestinationCity, hubDestinationName)
			} else if hubId != 0 && hubType == model.HubTypeConsole {
				desc = fmt.Sprintf(`Paket telah berangkat dari Hub %s %s ke Consolidator %s, %s`, hub3LC, hubName, partnerDesCity, partnerDesDistrict)
			} else if hubId == 0 && hubDestinationID != 0 {
				desc = fmt.Sprintf(`Paket telah berangkat dari Consolidator %s, %s ke Hub %s %s`, historyLocationName, historyDistrictName, hubDestinationCity, hubDestinationName)
			}

			if len(reqData.GetRemarkPieceHistory(reqData.SttStatus).CustomProcessRemarks) != 0 {
				vehicleNumber := reqData.GetRemarkPieceHistory(reqData.SttStatus).CustomProcessRemarks
				desc = fmt.Sprintf("%s dengan nomor kendaraan %s", desc, vehicleNumber)
			}

			return desc
		}
	}

	return f
}

func (c *sttCtx) generatorDispatchDescription(cond reqGenerateSttTrackingType) historyGeneratorFunc {
	f := func(reqData reqGenerateSttTrackingRequestBody) string { return "" }

	if cond.IsNotClient() && cond.IsNotEnglish() {
		f = func(reqData reqGenerateSttTrackingRequestBody) string {

			isActorHub := reqData.GetRemarkPieceHistory(reqData.SttStatus).HubID != 0

			consoleType := `Konsol`
			city3LC := reqData.HistoryLocation
			cityName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryLocationName
			districtName := reqData.GetRemarkPieceHistory(reqData.SttStatus).HistoryDistrictName
			if isActorHub {
				consoleType = `HUB`
				city3LC = reqData.GetRemarkPieceHistory(reqData.SttStatus).HubOriginCity
				cityName = reqData.GetRemarkPieceHistory(reqData.SttStatus).HubCityName
				districtName = reqData.GetRemarkPieceHistory(reqData.SttStatus).HubDistrictName
			}

			// KONDISPATCH
			desc := fmt.Sprintf(`Paket telah keluar dari %s %s %s,%s dan ditugaskan kepada Shuttle atau Kurir`, consoleType, city3LC, cityName, districtName)
			if reqData.SttStatus == model.STLDISPATCH {
				// expected STLDISPATCH
				desc = fmt.Sprintf(`Paket telah sampai di lokasi shuttle %s %s %s,%s dan sedang diserahkan ke kurir untuk pengantaran.`, consoleType, city3LC, cityName, districtName)
			}

			return desc
		}
	}

	return f
}

/* ========================== End of Generator Description per status ======================================== */

/* Support Function for generator */

func (c *sttCtx) getDest3lcAndCityName(ctx context.Context, cargoNumber string) (dest3LC string, destCityName string) {
	cargoData, err := c.cargoRepo.Get(ctx, &model.CargoViewParams{
		CargoNo: cargoNumber,
	})

	isCargoDataNilAndCargoNoEmpty := cargoData != nil && cargoNumber != ""
	if err == nil && isCargoDataNilAndCargoNoEmpty {
		dest3LC = cargoData.CargoDestinationCityCode
		destCityName = cargoData.LoadRemark().DestinationCityName
	}
	return dest3LC, destCityName
}

func (c *sttCtx) getDest3LC(ctx context.Context, cargoNumber string) (dest3LC string) {
	cargoData, err := c.cargoRepo.Get(ctx, &model.CargoViewParams{
		CargoNo: cargoNumber,
	})
	isCargoDataNotNilAndCargoNoNotEmpty := cargoData != nil && cargoNumber != ""
	if err == nil && isCargoDataNotNilAndCargoNoNotEmpty {
		dest3LC = cargoData.CargoDestinationCityCode
	}
	return dest3LC
}

func getDataCargoPlane(reqData reqGenerateSttTrackingRequestBody) (historyActorRole string, cityINTHND string, historyLocation3LCINTHND string, historyActorNameINTHND string) {

	for _, sttPieceHistory := range reqData.HistoryIdWithListSttPieceHistory {
		if sttPieceHistory[0].HistoryStatus == model.INTHND && sttPieceHistory[0].RemarkPieceHistoryToStruct() != nil {
			historyActorRole = sttPieceHistory[0].HistoryActorRole
			cityINTHND = sttPieceHistory[0].RemarkPieceHistoryToStruct().HistoryLocationName
			historyLocation3LCINTHND = sttPieceHistory[0].HistoryLocation
			historyActorNameINTHND = sttPieceHistory[0].HistoryActorName

			break
		}
	}
	return historyActorRole, cityINTHND, historyLocation3LCINTHND, historyActorNameINTHND
}

func getWordingFromCargoOrBag(cargoNumber string, bagNumber string) string {
	numberCargoOrBag := ""

	if len(cargoNumber) > 0 {
		numberCargoOrBag = fmt.Sprintf(`dari No. Cargo %s`, cargoNumber)
	}

	if bagNumber != "" {
		numberCargoOrBag = fmt.Sprintf(`dari No.Bag %s`, bagNumber)
	}

	if len(cargoNumber) > 0 && bagNumber != "" {
		numberCargoOrBag = fmt.Sprintf(`dari No.Bag %s atau No. Cargo %s`, bagNumber, cargoNumber)
	}

	return numberCargoOrBag
}

func getBagNumberList(reqData reqGenerateSttTrackingRequestBody) (bagNumber []string) {
	for _, val := range reqData.HistoryIdWithListSttPieceHistory[reqData.SttPieceHistoryID] {
		historyRemark := &model.RemarkPieceHistory{}
		if val.HistoryRemark != `` {
			json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			bagNumber = append(bagNumber, historyRemark.BagNumber)
		}
	}
	return
}

func (c *sttCtx) getPartnerDes(ctx context.Context, destinationCityCode, token string) (string, string) {
	partnerDes, err := c.partnerLocationRepo.Get(ctx, &model.PartnerLocationParamsGet{
		CityCode:         destinationCityCode,
		OnlyParnerActive: true,
	}, token)
	if err != nil || partnerDes == nil {
		return ``, ``
	}

	return c.resolvePartnerParent(ctx, partnerDes, token)
}

func (c *sttCtx) resolvePartnerParent(ctx context.Context, partnerDes *model.PartnerLocation, token string) (string, string) {
	partnerType := partnerDes.Data.Partner.Type
	partnerParentID := partnerDes.Data.Partner.ParentID

	if partnerType == model.CONSOLE || partnerParentID == 0 {
		return partnerDes.Data.City.Name, partnerDes.Data.District.Name
	}

	for i := 0; i < 2; i++ {
		partner, err := c.partnerRepo.GetByID(ctx, partnerParentID, token)
		if err == nil && partner != nil {
			partnerType = partner.Data.Type
			partnerParentID = partner.Data.ParentID
		}

		if partnerType == model.CONSOLE {
			return partner.Data.PartnerLocation.City.Name, partner.Data.PartnerLocation.District.Name
		}
	}

	return partnerDes.Data.City.Name, partnerDes.Data.District.Name
}

/* ======================= End of Support Function for generator ========================================= */
