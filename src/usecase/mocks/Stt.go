// Code generated by mockery v2.46.3. DO NOT EDIT.

package mocks

import (
	context "context"

	echo "github.com/labstack/echo"
	mock "github.com/stretchr/testify/mock"

	model "github.com/Lionparcel/hydra/src/model"

	shared "github.com/Lionparcel/hydra/shared"

	stt "github.com/Lionparcel/hydra/src/usecase/stt"
)

// Stt is an autogenerated mock type for the Stt type
type Stt struct {
	mock.Mock
}

// AssessmentRelabel provides a mock function with given fields: ctx, params
func (_m *Stt) AssessmentRelabel(ctx context.Context, params *stt.AssessmentRelabelRequest) (*stt.AssessmentRelabelResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for AssessmentRelabel")
	}

	var r0 *stt.AssessmentRelabelResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.AssessmentRelabelRequest) (*stt.AssessmentRelabelResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.AssessmentRelabelRequest) *stt.AssessmentRelabelResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.AssessmentRelabelResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.AssessmentRelabelRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CancelStt provides a mock function with given fields: ctx, params
func (_m *Stt) CancelStt(ctx context.Context, params *stt.RequestCancelStt) (*stt.ResponseCancelStt, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for CancelStt")
	}

	var r0 *stt.ResponseCancelStt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.RequestCancelStt) (*stt.ResponseCancelStt, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.RequestCancelStt) *stt.ResponseCancelStt); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.ResponseCancelStt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.RequestCancelStt) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CorporateCrossDockingRetry provides a mock function with given fields: ctx, params
func (_m *Stt) CorporateCrossDockingRetry(ctx context.Context, params *stt.CorporateCrossDockingRetryRequest) (*stt.CorporateCrossDockingRetryResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for CorporateCrossDockingRetry")
	}

	var r0 *stt.CorporateCrossDockingRetryResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.CorporateCrossDockingRetryRequest) (*stt.CorporateCrossDockingRetryResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.CorporateCrossDockingRetryRequest) *stt.CorporateCrossDockingRetryResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.CorporateCrossDockingRetryResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.CorporateCrossDockingRetryRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateSTT provides a mock function with given fields: ctx, params
func (_m *Stt) CreateSTT(ctx context.Context, params *stt.CreateSttRequest) (*stt.CreateSttResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for CreateSTT")
	}

	var r0 *stt.CreateSttResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.CreateSttRequest) (*stt.CreateSttResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.CreateSttRequest) *stt.CreateSttResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.CreateSttResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.CreateSttRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateSTTBookingNonCrossDocking provides a mock function with given fields: ctx, params
func (_m *Stt) CreateSTTBookingNonCrossDocking(ctx context.Context, params *stt.PublishSttBookingNonCrossDocking) (*stt.CreateSttResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for CreateSTTBookingNonCrossDocking")
	}

	var r0 *stt.CreateSttResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.PublishSttBookingNonCrossDocking) (*stt.CreateSttResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.PublishSttBookingNonCrossDocking) *stt.CreateSttResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.CreateSttResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.PublishSttBookingNonCrossDocking) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateSTTForClient provides a mock function with given fields: ctx, params
func (_m *Stt) CreateSTTForClient(ctx context.Context, params *stt.CreateSttManualForClient) (*stt.CreateSttResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for CreateSTTForClient")
	}

	var r0 *stt.CreateSttResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.CreateSttManualForClient) (*stt.CreateSttResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.CreateSttManualForClient) *stt.CreateSttResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.CreateSttResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.CreateSttManualForClient) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateSTTManualForTokopedia provides a mock function with given fields: ctx, params
func (_m *Stt) CreateSTTManualForTokopedia(ctx context.Context, params *stt.PublishSttBookingTokopedia) (*stt.CreateSttResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for CreateSTTManualForTokopedia")
	}

	var r0 *stt.CreateSttResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.PublishSttBookingTokopedia) (*stt.CreateSttResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.PublishSttBookingTokopedia) *stt.CreateSttResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.CreateSttResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.PublishSttBookingTokopedia) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateSTTManualForTokopediaFailed provides a mock function with given fields: ctx, params
func (_m *Stt) CreateSTTManualForTokopediaFailed(ctx context.Context, params *stt.PublishSttBookingTokopediaFailed) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for CreateSTTManualForTokopediaFailed")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.PublishSttBookingTokopediaFailed) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateSTTReverseJourney provides a mock function with given fields: ctx, params
func (_m *Stt) CreateSTTReverseJourney(ctx context.Context, params *stt.CreateSttManualReverseJourney) (*stt.CreateSttReverseJourneyResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for CreateSTTReverseJourney")
	}

	var r0 *stt.CreateSttReverseJourneyResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.CreateSttManualReverseJourney) (*stt.CreateSttReverseJourneyResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.CreateSttManualReverseJourney) *stt.CreateSttReverseJourneyResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.CreateSttReverseJourneyResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.CreateSttManualReverseJourney) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateSTTV2 provides a mock function with given fields: ctx, params
func (_m *Stt) CreateSTTV2(ctx context.Context, params *stt.CreateSttRequest) (*stt.CreateSttResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for CreateSTTV2")
	}

	var r0 *stt.CreateSttResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.CreateSttRequest) (*stt.CreateSttResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.CreateSttRequest) *stt.CreateSttResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.CreateSttResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.CreateSttRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DetailResi provides a mock function with given fields: ctx, req
func (_m *Stt) DetailResi(ctx context.Context, req stt.DetailResiRequest) (*stt.DetailResiResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for DetailResi")
	}

	var r0 *stt.DetailResiResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, stt.DetailResiRequest) (*stt.DetailResiResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, stt.DetailResiRequest) *stt.DetailResiResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.DetailResiResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, stt.DetailResiRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DetailShipmentAlgoByID provides a mock function with given fields: ctx, params
func (_m *Stt) DetailShipmentAlgoByID(ctx context.Context, params *stt.ViewDetailShipmentAlgoRequest) (*shared.Pagination, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for DetailShipmentAlgoByID")
	}

	var r0 *shared.Pagination
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewDetailShipmentAlgoRequest) (*shared.Pagination, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewDetailShipmentAlgoRequest) *shared.Pagination); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*shared.Pagination)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.ViewDetailShipmentAlgoRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DetailStt provides a mock function with given fields: ctx, params
func (_m *Stt) DetailStt(ctx context.Context, params *stt.SttDetailRequest) (*stt.SttDetailResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for DetailStt")
	}

	var r0 *stt.SttDetailResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.SttDetailRequest) (*stt.SttDetailResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.SttDetailRequest) *stt.SttDetailResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.SttDetailResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.SttDetailRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DetailSttTracking provides a mock function with given fields: ctx, params
func (_m *Stt) DetailSttTracking(ctx context.Context, params *stt.SttDetailTrackingRequest) (*stt.SttDetailTrackingResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for DetailSttTracking")
	}

	var r0 *stt.SttDetailTrackingResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.SttDetailTrackingRequest) (*stt.SttDetailTrackingResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.SttDetailTrackingRequest) *stt.SttDetailTrackingResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.SttDetailTrackingResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.SttDetailTrackingRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DetailSttTrackingTokopedia provides a mock function with given fields: ctx, params
func (_m *Stt) DetailSttTrackingTokopedia(ctx context.Context, params *stt.DetailSttTrackingTokopediaRequest) (*stt.SttDetailTrackingResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for DetailSttTrackingTokopedia")
	}

	var r0 *stt.SttDetailTrackingResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.DetailSttTrackingTokopediaRequest) (*stt.SttDetailTrackingResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.DetailSttTrackingTokopediaRequest) *stt.SttDetailTrackingResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.SttDetailTrackingResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.DetailSttTrackingTokopediaRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// DetailSttTrackingV2 provides a mock function with given fields: ctx, params
func (_m *Stt) DetailSttTrackingV2(ctx context.Context, params *stt.SttDetailTrackingRequest) (*stt.SttDetailTrackingResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for DetailSttTrackingV2")
	}

	var r0 *stt.SttDetailTrackingResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.SttDetailTrackingRequest) (*stt.SttDetailTrackingResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.SttDetailTrackingRequest) *stt.SttDetailTrackingResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.SttDetailTrackingResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.SttDetailTrackingRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ExportSttManifest provides a mock function with given fields: ctx, params, e
func (_m *Stt) ExportSttManifest(ctx context.Context, params *stt.ViewSttManifestRequest, e echo.Context) error {
	ret := _m.Called(ctx, params, e)

	if len(ret) == 0 {
		panic("no return value specified for ExportSttManifest")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewSttManifestRequest, echo.Context) error); ok {
		r0 = rf(ctx, params, e)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// GenerateRequestViewStt provides a mock function with given fields: ctx, params
func (_m *Stt) GenerateRequestViewStt(ctx context.Context, params *stt.ViewSttRequest) (*model.SttViewParams, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GenerateRequestViewStt")
	}

	var r0 *model.SttViewParams
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewSttRequest) (*model.SttViewParams, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewSttRequest) *model.SttViewParams); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.SttViewParams)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.ViewSttRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GenerateSignedURLs provides a mock function with given fields: ctx, sttDetail, attachment
func (_m *Stt) GenerateSignedURLs(ctx context.Context, sttDetail *model.Stt, attachment string) ([]string, error) {
	ret := _m.Called(ctx, sttDetail, attachment)

	if len(ret) == 0 {
		panic("no return value specified for GenerateSignedURLs")
	}

	var r0 []string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Stt, string) ([]string, error)); ok {
		return rf(ctx, sttDetail, attachment)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.Stt, string) []string); ok {
		r0 = rf(ctx, sttDetail, attachment)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]string)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.Stt, string) error); ok {
		r1 = rf(ctx, sttDetail, attachment)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDetailSttShipment provides a mock function with given fields: ctx, param
func (_m *Stt) GetDetailSttShipment(ctx context.Context, param *stt.GetDetailSttShipmentRequest) (*stt.GetDetailSttShipmentResponse, error) {
	ret := _m.Called(ctx, param)

	if len(ret) == 0 {
		panic("no return value specified for GetDetailSttShipment")
	}

	var r0 *stt.GetDetailSttShipmentResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.GetDetailSttShipmentRequest) (*stt.GetDetailSttShipmentResponse, error)); ok {
		return rf(ctx, param)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.GetDetailSttShipmentRequest) *stt.GetDetailSttShipmentResponse); ok {
		r0 = rf(ctx, param)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.GetDetailSttShipmentResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.GetDetailSttShipmentRequest) error); ok {
		r1 = rf(ctx, param)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDfodPasti provides a mock function with given fields: ctx, params
func (_m *Stt) GetDfodPasti(ctx context.Context, params *stt.SttDfodPastiRequest) (*stt.SttDfodPastiResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetDfodPasti")
	}

	var r0 *stt.SttDfodPastiResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.SttDfodPastiRequest) (*stt.SttDfodPastiResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.SttDfodPastiRequest) *stt.SttDfodPastiResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.SttDfodPastiResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.SttDfodPastiRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetOldestSTT provides a mock function with given fields: ctx, params
func (_m *Stt) GetOldestSTT(ctx context.Context, params *stt.GetOldestSttRequest) (*model.Stt, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetOldestSTT")
	}

	var r0 *model.Stt
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.GetOldestSttRequest) (*model.Stt, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.GetOldestSttRequest) *model.Stt); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Stt)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.GetOldestSttRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSttRelabel provides a mock function with given fields: ctx, params
func (_m *Stt) GetSttRelabel(ctx context.Context, params *stt.GetSttRelabelRequest) (*stt.GetSttRelabelResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetSttRelabel")
	}

	var r0 *stt.GetSttRelabelResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.GetSttRelabelRequest) (*stt.GetSttRelabelResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.GetSttRelabelRequest) *stt.GetSttRelabelResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.GetSttRelabelResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.GetSttRelabelRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ManualUpdateTariffRetailSTT provides a mock function with given fields: ctx, params
func (_m *Stt) ManualUpdateTariffRetailSTT(ctx context.Context, params *stt.RequestManualUpdateCalculateTariff) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ManualUpdateTariffRetailSTT")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.RequestManualUpdateCalculateTariff) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MigrateAdditionalData provides a mock function with given fields: ctx
func (_m *Stt) MigrateAdditionalData(ctx context.Context) error {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for MigrateAdditionalData")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context) error); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// MockeryShipmentGenerate provides a mock function with given fields: ctx, prefix
func (_m *Stt) MockeryShipmentGenerate(ctx context.Context, prefix string) (string, error) {
	ret := _m.Called(ctx, prefix)

	if len(ret) == 0 {
		panic("no return value specified for MockeryShipmentGenerate")
	}

	var r0 string
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) (string, error)); ok {
		return rf(ctx, prefix)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) string); ok {
		r0 = rf(ctx, prefix)
	} else {
		r0 = ret.Get(0).(string)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, prefix)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// PubsubSTTClaim provides a mock function with given fields: ctx, params
func (_m *Stt) PubsubSTTClaim(ctx context.Context, params *stt.RequestPubSubUpdateSttClaim) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PubsubSTTClaim")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.RequestPubSubUpdateSttClaim) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// PubsubTariffRetailSTT provides a mock function with given fields: ctx, params
func (_m *Stt) PubsubTariffRetailSTT(ctx context.Context, params *stt.RequestPubSubUpdateCalculateTariff) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for PubsubTariffRetailSTT")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.RequestPubSubUpdateCalculateTariff) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// RenameNoRef provides a mock function with given fields: ctx, params
func (_m *Stt) RenameNoRef(ctx context.Context, params *stt.RenameNoRefRequest) (*stt.RenameNoRefResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for RenameNoRef")
	}

	var r0 *stt.RenameNoRefResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.RenameNoRefRequest) (*stt.RenameNoRefResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.RenameNoRefRequest) *stt.RenameNoRefResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.RenameNoRefResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.RenameNoRefRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ResiSendEmail provides a mock function with given fields: ctx, req
func (_m *Stt) ResiSendEmail(ctx context.Context, req *stt.ResiSendEmailRequest) error {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ResiSendEmail")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ResiSendEmailRequest) error); ok {
		r0 = rf(ctx, req)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// ReverseDestination provides a mock function with given fields: ctx, params
func (_m *Stt) ReverseDestination(ctx context.Context, params *stt.ReverseDestinationParams) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ReverseDestination")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ReverseDestinationParams) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SttAdjustmentConfig provides a mock function with given fields: ctx, params
func (_m *Stt) SttAdjustmentConfig(ctx context.Context, params *stt.SttAdjustmentConfigRequest) (*stt.SttAdjustmentConfigResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SttAdjustmentConfig")
	}

	var r0 *stt.SttAdjustmentConfigResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.SttAdjustmentConfigRequest) (*stt.SttAdjustmentConfigResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.SttAdjustmentConfigRequest) *stt.SttAdjustmentConfigResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.SttAdjustmentConfigResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.SttAdjustmentConfigRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateSTT provides a mock function with given fields: ctx, params
func (_m *Stt) UpdateSTT(ctx context.Context, params *stt.UpdateSttRequest) (*stt.UpdateSttResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for UpdateSTT")
	}

	var r0 *stt.UpdateSttResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.UpdateSttRequest) (*stt.UpdateSttResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.UpdateSttRequest) *stt.UpdateSttResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.UpdateSttResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.UpdateSttRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateTariffRetailSTT provides a mock function with given fields: ctx, params
func (_m *Stt) UpdateTariffRetailSTT(ctx context.Context, params *stt.RequestUpdateCalculateTariff) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTariffRetailSTT")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.RequestUpdateCalculateTariff) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// Validate provides a mock function with given fields: ctx, params
func (_m *Stt) Validate(ctx context.Context, params *stt.ValidateRequest) (*stt.ValidateResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for Validate")
	}

	var r0 *stt.ValidateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ValidateRequest) (*stt.ValidateResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ValidateRequest) *stt.ValidateResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.ValidateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.ValidateRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ValidatePhoneNumber provides a mock function with given fields: ctx, params
func (_m *Stt) ValidatePhoneNumber(ctx context.Context, params *stt.ValidatePhoneNumberRequest) (*stt.ValidatePhoneNumberResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ValidatePhoneNumber")
	}

	var r0 *stt.ValidatePhoneNumberResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ValidatePhoneNumberRequest) (*stt.ValidatePhoneNumberResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ValidatePhoneNumberRequest) *stt.ValidatePhoneNumberResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.ValidatePhoneNumberResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.ValidatePhoneNumberRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ValidateStatusIncomingLILO provides a mock function with given fields: ctx, params
func (_m *Stt) ValidateStatusIncomingLILO(ctx context.Context, params *stt.ValidateShipmentLiloRequest) (*stt.ValidateShipmentLiloResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ValidateStatusIncomingLILO")
	}

	var r0 *stt.ValidateShipmentLiloResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ValidateShipmentLiloRequest) (*stt.ValidateShipmentLiloResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ValidateShipmentLiloRequest) *stt.ValidateShipmentLiloResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.ValidateShipmentLiloResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.ValidateShipmentLiloRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ViewBulkStt provides a mock function with given fields: ctx, req
func (_m *Stt) ViewBulkStt(ctx context.Context, req stt.ViewBulkSttRequest) ([]stt.ViewBulkSttResponse, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ViewBulkStt")
	}

	var r0 []stt.ViewBulkSttResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, stt.ViewBulkSttRequest) ([]stt.ViewBulkSttResponse, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, stt.ViewBulkSttRequest) []stt.ViewBulkSttResponse); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]stt.ViewBulkSttResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, stt.ViewBulkSttRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ViewCargoPlaneStatusARR provides a mock function with given fields: ctx, params
func (_m *Stt) ViewCargoPlaneStatusARR(ctx context.Context, params *stt.ViewCargoPlaneStatusARRRequest) (*stt.ViewCargoPlaneStatusARRResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ViewCargoPlaneStatusARR")
	}

	var r0 *stt.ViewCargoPlaneStatusARRResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewCargoPlaneStatusARRRequest) (*stt.ViewCargoPlaneStatusARRResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewCargoPlaneStatusARRRequest) *stt.ViewCargoPlaneStatusARRResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.ViewCargoPlaneStatusARRResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.ViewCargoPlaneStatusARRRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ViewDTPOLStartEndPoints provides a mock function with given fields: ctx, params
func (_m *Stt) ViewDTPOLStartEndPoints(ctx context.Context, params *stt.ViewDTPOLStartEndPointsRequest) (*stt.ViewDTPOLStartEndPointsResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ViewDTPOLStartEndPoints")
	}

	var r0 *stt.ViewDTPOLStartEndPointsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewDTPOLStartEndPointsRequest) (*stt.ViewDTPOLStartEndPointsResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewDTPOLStartEndPointsRequest) *stt.ViewDTPOLStartEndPointsResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.ViewDTPOLStartEndPointsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.ViewDTPOLStartEndPointsRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ViewDTPOLStartEndPointsV2 provides a mock function with given fields: ctx, params
func (_m *Stt) ViewDTPOLStartEndPointsV2(ctx context.Context, params *stt.ViewDTPOLStartEndPointsRequest) (*stt.ViewDTPOLStartEndPointsResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ViewDTPOLStartEndPointsV2")
	}

	var r0 *stt.ViewDTPOLStartEndPointsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewDTPOLStartEndPointsRequest) (*stt.ViewDTPOLStartEndPointsResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewDTPOLStartEndPointsRequest) *stt.ViewDTPOLStartEndPointsResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.ViewDTPOLStartEndPointsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.ViewDTPOLStartEndPointsRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ViewDTPOLStartEndPointsV3 provides a mock function with given fields: ctx, params
func (_m *Stt) ViewDTPOLStartEndPointsV3(ctx context.Context, params *stt.ViewDTPOLStartEndPointsRequest) (*stt.ViewDTPOLStartEndPointsResponse, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ViewDTPOLStartEndPointsV3")
	}

	var r0 *stt.ViewDTPOLStartEndPointsResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewDTPOLStartEndPointsRequest) (*stt.ViewDTPOLStartEndPointsResponse, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewDTPOLStartEndPointsRequest) *stt.ViewDTPOLStartEndPointsResponse); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*stt.ViewDTPOLStartEndPointsResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.ViewDTPOLStartEndPointsRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ViewStt provides a mock function with given fields: ctx, req
func (_m *Stt) ViewStt(ctx context.Context, req stt.ViewSttRequest) (*shared.Pagination, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ViewStt")
	}

	var r0 *shared.Pagination
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, stt.ViewSttRequest) (*shared.Pagination, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, stt.ViewSttRequest) *shared.Pagination); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*shared.Pagination)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, stt.ViewSttRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ViewSttForManifest provides a mock function with given fields: ctx, params
func (_m *Stt) ViewSttForManifest(ctx context.Context, params *stt.ViewSttForManifestRequest) (*shared.Pagination, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ViewSttForManifest")
	}

	var r0 *shared.Pagination
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewSttForManifestRequest) (*shared.Pagination, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewSttForManifestRequest) *shared.Pagination); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*shared.Pagination)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.ViewSttForManifestRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ViewSttManifest provides a mock function with given fields: ctx, req
func (_m *Stt) ViewSttManifest(ctx context.Context, req stt.ViewSttManifestRequest) (*shared.Pagination, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ViewSttManifest")
	}

	var r0 *shared.Pagination
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, stt.ViewSttManifestRequest) (*shared.Pagination, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, stt.ViewSttManifestRequest) *shared.Pagination); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*shared.Pagination)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, stt.ViewSttManifestRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ViewSttManifestBag provides a mock function with given fields: ctx, req
func (_m *Stt) ViewSttManifestBag(ctx context.Context, req stt.ViewSttManifestBagRequest) (*shared.Pagination, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ViewSttManifestBag")
	}

	var r0 *shared.Pagination
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, stt.ViewSttManifestBagRequest) (*shared.Pagination, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, stt.ViewSttManifestBagRequest) *shared.Pagination); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*shared.Pagination)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, stt.ViewSttManifestBagRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ViewSttPlain provides a mock function with given fields: ctx, req
func (_m *Stt) ViewSttPlain(ctx context.Context, req *stt.ViewSttPlainRequest) (*shared.Pagination, error) {
	ret := _m.Called(ctx, req)

	if len(ret) == 0 {
		panic("no return value specified for ViewSttPlain")
	}

	var r0 *shared.Pagination
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewSttPlainRequest) (*shared.Pagination, error)); ok {
		return rf(ctx, req)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewSttPlainRequest) *shared.Pagination); ok {
		r0 = rf(ctx, req)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*shared.Pagination)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.ViewSttPlainRequest) error); ok {
		r1 = rf(ctx, req)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ViewSttSenderTracking provides a mock function with given fields: ctx, params
func (_m *Stt) ViewSttSenderTracking(ctx context.Context, params *stt.ViewSttTrackingRequest) (*shared.Pagination, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ViewSttSenderTracking")
	}

	var r0 *shared.Pagination
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewSttTrackingRequest) (*shared.Pagination, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewSttTrackingRequest) *shared.Pagination); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*shared.Pagination)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.ViewSttTrackingRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ViewSttShipmentPrefix provides a mock function with given fields: ctx
func (_m *Stt) ViewSttShipmentPrefix(ctx context.Context) []stt.ViewSttShipmentPrefixResponse {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for ViewSttShipmentPrefix")
	}

	var r0 []stt.ViewSttShipmentPrefixResponse
	if rf, ok := ret.Get(0).(func(context.Context) []stt.ViewSttShipmentPrefixResponse); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]stt.ViewSttShipmentPrefixResponse)
		}
	}

	return r0
}

// ViewSttTrackingList provides a mock function with given fields: ctx, params
func (_m *Stt) ViewSttTrackingList(ctx context.Context, params *stt.ViewSttTrackingListRequest) (*shared.Pagination, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ViewSttTrackingList")
	}

	var r0 *shared.Pagination
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewSttTrackingListRequest) (*shared.Pagination, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *stt.ViewSttTrackingListRequest) *shared.Pagination); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*shared.Pagination)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *stt.ViewSttTrackingListRequest) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// NewStt creates a new instance of Stt. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewStt(t interface {
	mock.TestingT
	Cleanup(func())
}) *Stt {
	mock := &Stt{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
