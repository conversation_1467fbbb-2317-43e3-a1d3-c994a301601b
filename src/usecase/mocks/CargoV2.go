// Code generated by mockery v2.20.2. DO NOT EDIT.

package mocks

import (
	context "context"

	cargo_v2 "github.com/Lionparcel/hydra/src/usecase/cargo_v2"

	mock "github.com/stretchr/testify/mock"
)

// CargoV2 is an autogenerated mock type for the CargoV2 type
type CargoV2 struct {
	mock.Mock
}

// CreateCargoV2 provides a mock function with given fields: ctx, params
func (_m *CargoV2) CreateCargoV2(ctx context.Context, params *cargo_v2.CreateCargoV2Request) (*cargo_v2.CreateCargoV2Response, error) {
	ret := _m.Called(ctx, params)

	var r0 *cargo_v2.CreateCargoV2Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *cargo_v2.CreateCargoV2Request) (*cargo_v2.CreateCargoV2Response, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *cargo_v2.CreateCargoV2Request) *cargo_v2.CreateCargoV2Response); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*cargo_v2.CreateCargoV2Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *cargo_v2.CreateCargoV2Request) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CreateCargoV2Sabre provides a mock function with given fields: ctx, params
func (_m *CargoV2) CreateCargoV2Sabre(ctx context.Context, params *cargo_v2.CreateCargoV2Request) (*cargo_v2.CreateCargoV2Response, error) {
	ret := _m.Called(ctx, params)

	var r0 *cargo_v2.CreateCargoV2Response
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *cargo_v2.CreateCargoV2Request) (*cargo_v2.CreateCargoV2Response, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *cargo_v2.CreateCargoV2Request) *cargo_v2.CreateCargoV2Response); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*cargo_v2.CreateCargoV2Response)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *cargo_v2.CreateCargoV2Request) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ForceUpdateDepArrCargoFlight provides a mock function with given fields: ctx, params
func (_m *CargoV2) ForceUpdateDepArrCargoFlight(ctx context.Context, params *cargo_v2.ForceUpdateDepArrCargoRequest) error {
	ret := _m.Called(ctx, params)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *cargo_v2.ForceUpdateDepArrCargoRequest) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SchedulerUpdateEstimationTimeCargoNgenV2 provides a mock function with given fields: ctx
func (_m *CargoV2) SchedulerUpdateEstimationTimeCargoNgenV2(ctx context.Context) {
	_m.Called(ctx)
}

// UpdateCargoPlaneStatus provides a mock function with given fields: ctx, params
func (_m *CargoV2) UpdateCargoPlaneStatus(ctx context.Context, params *cargo_v2.UpdateCargoPlaneStatusParams) error {
	ret := _m.Called(ctx, params)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *cargo_v2.UpdateCargoPlaneStatusParams) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewCargoV2 interface {
	mock.TestingT
	Cleanup(func())
}

// NewCargoV2 creates a new instance of CargoV2. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewCargoV2(t mockConstructorTestingTNewCargoV2) *CargoV2 {
	mock := &CargoV2{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
