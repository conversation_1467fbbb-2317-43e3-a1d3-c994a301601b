package usecase

import (
	"github.com/Lionparcel/hydra/config"
	"reflect"
	"testing"

	"github.com/Lionparcel/go-lptool/v2/lputils"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/repository"
	"github.com/Lionparcel/hydra/src/repository/mocks"
	"github.com/Lionparcel/hydra/src/usecase/reason"
	"github.com/abiewardani/dbr/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func TestViewReason(t *testing.T) {
	mockReasonRepo := new(mocks.ReasonRepository)
	ctx, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()

	uc := reasonCtx{
		reasonRepo: mockReasonRepo,
	}

	t.Run("Defer", func(t *testing.T) {

		uc.ViewReason(ctx, nil)
	})

	t.Run("ViewReason Error db", func(t *testing.T) {

		mockParams := &reason.ViewReasonRequest{
			StatusCode:   "DEX",
			ReasonStatus: "RES22",
		}

		mockReasonRepo.On("SelectDetail", mock.Anything, mock.Anything).Return(nil, dbr.ErrNotSupported).Once()

		res, err := uc.ViewReason(ctx, mockParams)
		assert.Equal(t, res, []reason.ViewReasonResponse{})
		assert.NotNil(t, err)
		assert.Equal(t, err.Error(), `{"code":400,"message":{"en":"An error occurred while http request","id":"Terjadi kesalahan pada saat request http"}}`)
	})

	t.Run("ViewReason Empty Result", func(t *testing.T) {

		mockParams := &reason.ViewReasonRequest{
			StatusCode:   "DEX",
			ReasonStatus: "RES22",
		}
		mockReasonRepoResults := []model.ReasonDetailResult{}

		mockReasonRepo.On("SelectDetail", mock.Anything, mock.Anything).Return(mockReasonRepoResults, nil).Once()

		res, err := uc.ViewReason(ctx, mockParams)
		assert.Equal(t, res, []reason.ViewReasonResponse{})
		assert.Nil(t, err)
	})

	t.Run("ViewReason Success", func(t *testing.T) {

		mockParams := &reason.ViewReasonRequest{
			StatusCode: "DEX",
		}

		mockReasonRepoResults := []model.ReasonDetailResult{}

		mockReasonRepoResult := model.ReasonDetailResult{}
		mockReasonRepoResult.ReasonID = 1
		mockReasonRepoResult.ReasonCanGenerateStt = true
		mockReasonRepoResult.ReasonCode = "RES21"
		mockReasonRepoResult.ReasonDescription = "Alamat tidak ketemu"
		mockReasonRepoResult.ReasonTitle = "Alamat tidak ketemu"
		mockReasonRepoResults = append(mockReasonRepoResults, mockReasonRepoResult)

		mockReasonRepoResult.ReasonID = 2
		mockReasonRepoResult.ReasonCanGenerateStt = true
		mockReasonRepoResult.ReasonCode = "RES22"
		mockReasonRepoResult.ReasonDescription = "Kantor tutup"
		mockReasonRepoResult.ReasonTitle = "Kantor tutup"
		mockReasonRepoResults = append(mockReasonRepoResults, mockReasonRepoResult)

		mockReasonRepo.On("SelectDetail", mock.Anything, mock.Anything).Return(mockReasonRepoResults, nil).Once()

		res, err := uc.ViewReason(ctx, mockParams)
		assert.Equal(t, len(res), 2)
		assert.Equal(t, res[0].ReasonDescription, "Alamat tidak ketemu")
		assert.Equal(t, res[1].ReasonDescription, "Kantor tutup")
		assert.Nil(t, err)
	})
}

func TestNewReasonUc(t *testing.T) {
	type args struct {
		reasonRepo repository.ReasonRepository
		sttRepo    repository.SttRepository
		cfg        config.Config
	}
	tests := []struct {
		name string
		args args
		want Reason
	}{
		{
			name: "Test NewReasonUc Success",
			args: args{
				reasonRepo: new(mocks.ReasonRepository),
				sttRepo:    new(mocks.SttRepository),
				cfg:        config.Config{},
			},
			want: &reasonCtx{
				reasonRepo: new(mocks.ReasonRepository),
				sttRepo:    new(mocks.SttRepository),
				cfg:        config.Config{},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewReasonUc(tt.args.reasonRepo, tt.args.sttRepo, tt.args.cfg); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewReasonUc() = %v, want %v", got, tt.want)
			}
		})
	}
}
