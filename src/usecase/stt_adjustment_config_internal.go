package usecase

import (
	"context"

	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *sttCtx) configSttInternal(ctx context.Context, data checkSttPrefix) *stt.SttAdjustmentConfigResponse {
	if !(c.isSttValidType(data) || data.isSttReverseJourney) {
		return &stt.SttAdjustmentConfigResponse{}
	}

	currentStatus := data.dataStt.SttLastStatusID
	isAfterPup := c.isAfterStatusPup(data.sttHistories, currentStatus)
	rules := c.getSttFieldRulesInternal(data, isAfterPup)

	res := &stt.SttAdjustmentConfigResponse{
		Config: c.buildSttConfigInternal(rules, isAfterPup),
	}

	c.checkInterpackDocumentInternational(ctx, data, res)
	c.populateRespInterpackAndFtzPos(res, data.cityOrigin.FreeTradeZone)
	return res
}

func (c *sttCtx) isSttCodDfod(data checkSttPrefix) string {
	rulesCod := stt.EditableConfig
	if data.dataStt.SttIsCOD || data.dataStt.SttIsDFOD {
		rulesCod = stt.NonEditableConfig
	}
	return rulesCod
}

func (c *sttCtx) getSttFieldRulesInternal(data checkSttPrefix, isAfterPup bool) sttFieldRules {
	commodityRule := stt.EditableConfig
	if isAfterPup {
		commodityRule = c.adjustStatusSttCommodityRule(data)
	}
	rulesCod := c.isSttCodDfod(data)

	defaultRule := sttFieldRules{
		SenderRule:          stt.EditableConfig,
		ReceiverRule:        stt.EditableConfig,
		ReceiverAddressRule: stt.EditableConfig,
		CommodityRule:       commodityRule,
		TariffRule:          stt.ShowConfig,
		WeightRule:          stt.EditableConfig,
		CodDfodRule:         rulesCod,
	}

	switch {
	case data.isSttRetail:
		return defaultRule
	case data.isSttClient:
		return sttFieldRules{SenderRule: stt.EditableConfig, ReceiverRule: stt.EditableConfig, ReceiverAddressRule: c.adjustEditableRule(isAfterPup, stt.EditableConfig), CommodityRule: commodityRule, TariffRule: stt.NoShowConfig, WeightRule: stt.EditableConfig, CodDfodRule: rulesCod}
	case data.isSttCA:
		return sttFieldRules{SenderRule: stt.EditableConfig, ReceiverRule: stt.EditableConfig, ReceiverAddressRule: stt.EditableConfig, CommodityRule: commodityRule, TariffRule: stt.ShowConfig, WeightRule: stt.EditableConfig, CodDfodRule: rulesCod}
	case data.isSttTokopedia:
		return sttFieldRules{SenderRule: stt.NonEditableConfig, ReceiverRule: stt.NonEditableConfig, ReceiverAddressRule: stt.NonEditableConfig, CommodityRule: stt.EditableConfig, TariffRule: stt.NoShowConfig, WeightRule: stt.EditableConfig, CodDfodRule: stt.NonEditableConfig}
	case data.isSttBukalapak:
		return sttFieldRules{SenderRule: stt.NonEditableConfig, ReceiverRule: stt.NonEditableConfig, ReceiverAddressRule: stt.NonEditableConfig, CommodityRule: stt.NonEditableConfig, TariffRule: stt.NoShowConfig, WeightRule: stt.NonEditableConfig, CodDfodRule: stt.NonEditableConfig}
	case data.isSttReverseJourney:
		return sttFieldRules{SenderRule: stt.EditableConfig, ReceiverRule: stt.EditableConfig, ReceiverAddressRule: stt.EditableConfig, CommodityRule: commodityRule, TariffRule: stt.ShowConfig, WeightRule: stt.EditableConfig, CodDfodRule: rulesCod}
	default:
		return defaultRule
	}
}

func (c *sttCtx) adjustStatusSttCommodityRule(data checkSttPrefix) string {
	stidestIntra := data.dataStt.SttLastStatusID == model.STIDEST && data.dataStt.SttOriginCityID == data.dataStt.SttDestinationCityID
	if model.IsAllowAdjustmentSttAfterPUP[data.dataStt.SttLastStatusID] || stidestIntra {
		return stt.EditableConfig
	}
	return stt.NonEditableConfig
}

func (c *sttCtx) buildSttConfigInternal(r sttFieldRules, isAfterPup bool) stt.Config {
	return stt.Config{
		Client:              newFieldRule(true, stt.NonEditableConfig),
		PaymentMethod:       newFieldRule(true, stt.NonEditableConfig),
		ShipmentID:          newFieldRule(true, stt.NonEditableConfig),
		ManualSttNo:         newFieldRule(false, stt.NonEditableConfig),
		SttNoRefExternal:    newFieldRule(false, stt.NonEditableConfig),
		SenderName:          newFieldRule(true, c.adjustEditableRule(isAfterPup, r.SenderRule)),
		SenderPhone:         newFieldRule(true, c.adjustEditableRule(isAfterPup, r.SenderRule)),
		SenderAddress:       newFieldRule(true, c.adjustEditableRule(isAfterPup, r.SenderRule)),
		SenderDistrict:      newFieldRule(true, stt.NonEditableConfig),
		SenderPostalCode:    newFieldRule(false, stt.NonEditableConfig),
		SenderSave:          newFieldRule(false, stt.NonEditableConfig),
		RecipientName:       newFieldRule(true, c.adjustEditableRule(isAfterPup, r.ReceiverRule)),
		RecipientPhone:      newFieldRule(true, c.adjustEditableRule(isAfterPup, r.ReceiverRule)),
		RecipientAddress:    newFieldRule(true, r.ReceiverAddressRule),
		RecipientDistrict:   newFieldRule(true, c.adjustEditableRule(isAfterPup, r.CodDfodRule)),
		RecipientPostalCode: newFieldRule(false, c.adjustEditableRule(isAfterPup, r.CodDfodRule)),
		RecipientSave:       newFieldRule(false, stt.NonEditableConfig),
		Commodity:           newFieldRule(true, r.CommodityRule),
		ProductType:         newFieldRule(true, stt.NonEditableConfig),
		GoodsPrice:          newFieldRule(true, stt.NonEditableConfig),
		Insurance:           newFieldRule(false, stt.NonEditableConfig),
		CODAmount:           newFieldRule(false, stt.NonEditableConfig),
		GrossWeight:         newFieldRule(true, r.WeightRule),
		Dimension:           newFieldRule(true, r.WeightRule),
		ChargeableWeight:    newFieldRule(false, stt.NonEditableConfig),
		AdjustPiece:         newFieldRule(false, stt.NoShowConfig),
		ServiceType:         newFieldRule(true, stt.NonEditableConfig),
		Tariff:              newFieldRule(true, r.TariffRule),
	}
}
