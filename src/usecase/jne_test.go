package usecase

import (
	"context"
	"errors"
	"fmt"
	"math"
	"reflect"
	"strconv"
	"testing"
	"time"

	"github.com/Lionparcel/go-lptool/v2/lputils"
	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/repository/mocks"
	customProcess "github.com/Lionparcel/hydra/src/usecase/custom_process"
	"github.com/Lionparcel/hydra/src/usecase/delivery"
	"github.com/Lionparcel/hydra/src/usecase/jne"
	ucMocks "github.com/Lionparcel/hydra/src/usecase/mocks"
	ucmock "github.com/Lionparcel/hydra/src/usecase/mocks"
	"github.com/Lionparcel/hydra/src/usecase/pod"
	"github.com/Lionparcel/hydra/src/usecase/stt_activity"
	"github.com/abiewardani/dbr/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func Test_NewJneUc(t *testing.T) {
	cfg := &config.Config{}
	mockJneRepo := new(mocks.JNERepository)
	mockPartnerLogRepo := new(mocks.PartnerLogRepository)
	mockDistrictRepo := new(mocks.DistrictRepository)
	mockSttVendorRepo := new(mocks.SttVendorRepository)
	mockSttRepo := new(mocks.SttRepository)
	mockDeliveryRepo := new(mocks.DeliveryRepository)
	mockPartnerRepo := new(mocks.PartnerRepository)
	mockMessageGatewayUc := new(ucMocks.MessageGateway)
	mockAccountRepo := new(mocks.AccountRepository)
	mockCityRepo := new(mocks.CityRepository)
	mockReasonRepo := new(mocks.ReasonRepository)
	mockPodRepo := new(mocks.PodRepository)
	mockGatewaySttStatusRepo := new(ucMocks.GatewaySttStatus)
	mockSttHistoryRepo := new(mocks.SttPieceHistoryRepository)
	mockCustomProcessRepo := new(mocks.CustomProcessRepository)
	mockSttActivityUc := new(ucMocks.SttActivity)
	mockMiddlewareRepo := new(mocks.MiddlewareCLient)
	mockTimeRepo := new(mocks.TimeRepository)
	mockReasonMappingVendor := new(mocks.ReasonVendorMappingRepository)
	mockReadyToCargoUc := new(ucMocks.ReadyToCargo)
	mockSalesforce := new(ucMocks.Salesforce)
	mockRequestPriorityDelivery := new(ucMocks.RequestPriorityDelivery)
	mockSttDueRepo := new(mocks.SttDueRepository)
	mockNotificationRepo := new(mocks.NotificationRepository)

	want := &jneCtx{
		cfg:                     cfg,
		jneRepo:                 mockJneRepo,
		partnerLog:              mockPartnerLogRepo,
		repoDistrict:            mockDistrictRepo,
		sttVendorRepo:           mockSttVendorRepo,
		sttRepo:                 mockSttRepo,
		deliveryRepo:            mockDeliveryRepo,
		partnerRepo:             mockPartnerRepo,
		messageGatewayUc:        mockMessageGatewayUc,
		repoAccount:             mockAccountRepo,
		repoCity:                mockCityRepo,
		reasonRepo:              mockReasonRepo,
		podRepo:                 mockPodRepo,
		gatewaySttStatus:        mockGatewaySttStatusRepo,
		sttHistoryRepo:          mockSttHistoryRepo,
		customProcessRepo:       mockCustomProcessRepo,
		sttActivityUc:           mockSttActivityUc,
		middlewareRepo:          mockMiddlewareRepo,
		timeRepo:                mockTimeRepo,
		reasonVendorMapping:     mockReasonMappingVendor,
		rtcUc:                   mockReadyToCargoUc,
		salesforce:              mockSalesforce,
		requestPriorityDelivery: mockRequestPriorityDelivery,
		sttDueRepo:              mockSttDueRepo,
		notificationRepo:        mockNotificationRepo,
	}

	got := NewJneUc(
		cfg,
		mockJneRepo,
		mockPartnerLogRepo,
		mockDistrictRepo,
		mockSttVendorRepo,
		mockSttRepo,
		mockDeliveryRepo,
		mockPartnerRepo,
		mockMessageGatewayUc,
		mockAccountRepo,
		mockCityRepo,
		mockReasonRepo,
		mockPodRepo,
		mockGatewaySttStatusRepo,
		mockSttHistoryRepo,
		mockCustomProcessRepo,
		mockSttActivityUc,
		mockMiddlewareRepo,
		mockTimeRepo,
		mockReasonMappingVendor,
		mockReadyToCargoUc,
		mockSalesforce,
		mockRequestPriorityDelivery,
		mockSttDueRepo,
		mockNotificationRepo,
	)
	if !reflect.DeepEqual(got, want) {
		t.Errorf("NewJneUc() got = %v, want = %v\n", got, want)
	}
}

func GetTokenMock() string {
	return `eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.hCx-8ABm3IJbUMvAy8LlQWHhaIaoBCxP4Y-zfg25hS4ATKTRGaoLOnWBPNQuKMoSMHsf5xvuk4yjco-q28L1pIi6jMfEIJov_6_ZaJbeht30glu67gm8iPs73DdkzeA2iNNAWRJb_60ddJ1wIro7lGNr-e1H29Va3pncnVJAes8pudpx2bZss9gxWQT_WrNPN_3nitpRLZqp9swSr9iN7po_3exHr7Obd33PPWtTBEmc0rPN9Oml09P3JD1VL2wVAKbZfvbTBYgctP-UlgjI7T4FtLlsiE1brJA8A65m6wa5W0OT38m1_VWYk3cWfyI_-0OSvgue1aReBXsCl5vb4A`
}

// TestJneCreateAirwaybillSuccess ...
func TestJneCreateAirwaybill(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *model.JneCreateAirwaybillRequest
	}

	mockJNERepo := new(mocks.JNERepository)
	mockDistrictRepo := new(mocks.DistrictRepository)
	mockSttVendorRepo := new(mocks.SttVendorRepository)
	mockPartnerLogRepo := new(mocks.PartnerLogRepository)

	ctx, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()
	errorDetail := errors.New("ERROR")

	// mock District
	mockDistrict := &model.District{
		Data: model.DistrictData{
			Code:                  `DKI17`,
			Name:                  `KEBON JERUK, JAKARTA BARAT`,
			Type:                  model.VENDOR,
			VendorCode:            model.TypeVendorJNE,
			VendorOriginCode:      `CGK10000`,
			VendorDestinationCode: `CGK10000`,
			VendorBranchCode:      `CGK000`,
		},
	}

	// mock Stt
	mockStt := &model.Stt{
		SttID:                      1,
		SttNo:                      `88LP1643733701299`,
		SttShipmentID:              `ADO9ZB4U`,
		SttDestinationCityID:       `CGK`,
		SttDestinationCityName:     `JAKARTA`,
		SttDestinationDistrictID:   mockDistrict.Data.Code,
		SttDestinationDistrictName: mockDistrict.Data.Name,
		SttRecipientName:           `Spongebob`,
		SttRecipientAddress:        `Jalan Jeruk Manis III`,
		SttRecipientPhone:          `6283827014867`,
		SttTotalPiece:              1,
		SttChargeableWeight:        1,
		SttGoodsEstimatePrice:      100000,
	}

	// mock Partner
	mockPartner := &model.Partner{
		Data: model.PartnerBase{
			Address: `JALAN AGAVE RAYA NO 55, KEDOYA SELATAN, JAKARTA BARAT`,
			PartnerLocation: &model.PartnerLocationBase{
				City: &model.City{
					Name: `JAKARTA`,
				},
			},
		},
	}

	// Mock Create Airwailbill
	mockCreateAirwailbill := &model.CreateAirwaybillResponse{
		Status: true,
		Detail: []model.DetailResponse{
			{
				Status:  model.JNEStatusSuccess,
				CnoteNo: `0101162200324602`,
			},
		},
	}

	// params
	params := &model.JneCreateAirwaybillRequest{
		Partner: mockPartner,
		Stt:     *mockStt,
		Token:   GetTokenMock(),
	}

	response := &model.JneCreateAirwaybillResponse{
		Cnote: "0101162200324602",
	}

	sttVendor := &model.SttVendor{
		SttVendorVendorCode:  model.TypeVendorJNE,
		SttVendorReferenceNo: mockCreateAirwailbill.Detail[0].CnoteNo,
		SttVendorSttNo:       params.Stt.SttNo,
		SttVendorSttID:       int(params.Stt.SttID),
	}

	tests := []struct {
		name       string
		args       args
		want       *model.JneCreateAirwaybillResponse
		errRes     error
		wantErr    bool
		beforeFunc func() *jneCtx
	}{
		{
			name: "TestJneCreateAirwaybill-Success",
			args: args{
				ctx:    ctx,
				params: params,
			},
			want:    response,
			errRes:  nil,
			wantErr: false,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{
					jneRepo:       mockJNERepo,
					repoDistrict:  mockDistrictRepo,
					sttVendorRepo: mockSttVendorRepo,
					partnerLog:    mockPartnerLogRepo,
				}

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, params.Stt.SttDestinationDistrictID).Return(mockDistrict, nil).Once()
				mockSttVendorRepo.On("Get", mock.Anything, &model.SttVendorViewParams{SttID: mockStt.SttID}).Return(nil, nil).Once()
				mockJNERepo.On("CreateAirwaybill", mock.Anything, &params.CreateAirwaybillRequest).Return(mockCreateAirwailbill, nil).Once()
				mockSttVendorRepo.On("Create", mock.Anything, sttVendor).Return(nil).Once()
				mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
		{
			name: "TestJneCreateAirwaybill-Success",
			args: args{
				ctx: ctx,
				params: &model.JneCreateAirwaybillRequest{
					Partner: mockPartner,
					Stt: model.Stt{
						SttID:                      1,
						SttNo:                      `88LP1643733701299`,
						SttShipmentID:              `ADO9ZB4U`,
						SttDestinationCityID:       `CGK`,
						SttDestinationCityName:     `JAKARTA`,
						SttDestinationDistrictID:   mockDistrict.Data.Code,
						SttDestinationDistrictName: mockDistrict.Data.Name,
						SttRecipientName:           `Spongebob`,
						SttRecipientAddress:        `Jalan Jeruk Manis III`,
						SttRecipientPhone:          `6283827014867`,
						SttTotalPiece:              1,
						SttChargeableWeight:        1,
						SttGoodsEstimatePrice:      100000,
						SttPaymentStatus:           model.UNPAID,
					},
					Token: GetTokenMock(),
				},
			},
			want:    nil,
			errRes:  nil,
			wantErr: false,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{}

				return c
			},
		},
		{
			name: "TestJneCreateAirwaybill-destinationDistrict_Error",
			args: args{
				ctx:    ctx,
				params: params,
			},
			want:    nil,
			errRes:  fmt.Errorf(`JneCreateAirwaybillV1-failed getting destination district. %v, err : %v`, params.Stt.SttDestinationDistrictID, errorDetail.Error()),
			wantErr: true,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{
					repoDistrict: mockDistrictRepo,
					partnerLog:   mockPartnerLogRepo,
				}

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, params.Stt.SttDestinationDistrictID).Return(mockDistrict, errorDetail).Once()
				mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
		{
			name: "TestJneCreateAirwaybillV1-destinationDistrict_Nil",
			args: args{
				ctx:    ctx,
				params: params,
			},
			want:    nil,
			errRes:  fmt.Errorf(`JneCreateAirwaybillV1-destination district. %v, not found`, params.Stt.SttDestinationDistrictID),
			wantErr: true,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{
					repoDistrict: mockDistrictRepo,
					partnerLog:   mockPartnerLogRepo,
				}

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, params.Stt.SttDestinationDistrictID).Return(nil, nil).Once()
				mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
		{
			name: "TestJneCreateAirwaybill-DistrictType_NotVendor",
			args: args{
				ctx:    ctx,
				params: params,
			},
			want:    nil,
			errRes:  nil,
			wantErr: false,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{
					repoDistrict: mockDistrictRepo,
					partnerLog:   mockPartnerLogRepo,
				}

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, params.Stt.SttDestinationDistrictID).Return(&model.District{
					Data: model.DistrictData{
						Code:                  `DKI17`,
						Name:                  `KEBON JERUK, JAKARTA BARAT`,
						Type:                  "type",
						VendorCode:            model.TypeVendorJNE,
						VendorOriginCode:      `CGK10000`,
						VendorDestinationCode: `CGK10000`,
						VendorBranchCode:      `CGK000`,
					},
				}, nil).Once()
				mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
		{
			name: "TestJneCreateAirwaybill-params.Stt.SttIsCOD_true",
			args: args{
				ctx: ctx,
				params: &model.JneCreateAirwaybillRequest{
					Partner: mockPartner,
					Stt: model.Stt{
						SttID:                      1,
						SttNo:                      `88LP1643733701299`,
						SttShipmentID:              `ADO9ZB4U`,
						SttDestinationCityID:       `CGK`,
						SttDestinationCityName:     `JAKARTA`,
						SttDestinationDistrictID:   mockDistrict.Data.Code,
						SttDestinationDistrictName: mockDistrict.Data.Name,
						SttRecipientName:           `Spongebob`,
						SttRecipientAddress:        `Jalan Jeruk Manis III`,
						SttRecipientPhone:          `6283827014867`,
						SttTotalPiece:              1,
						SttChargeableWeight:        1,
						SttGoodsEstimatePrice:      100000,
						SttIsCOD:                   true,
					},
					Token: GetTokenMock(),
				},
			},
			want:    nil,
			errRes:  nil,
			wantErr: false,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{
					repoDistrict: mockDistrictRepo,
					partnerLog:   mockPartnerLogRepo,
				}

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, params.Stt.SttDestinationDistrictID).Return(mockDistrict, nil).Once()
				mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
		{
			name: "TestJneCreateAirwaybillV1-VendorOriginCode_Empty",
			args: args{
				ctx:    ctx,
				params: params,
			},
			want:    nil,
			errRes:  fmt.Errorf(`JneCreateAirwaybillV1-district %s has not been mapped with location origin jne`, params.Stt.SttDestinationCityID),
			wantErr: true,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{
					repoDistrict: mockDistrictRepo,
					partnerLog:   mockPartnerLogRepo,
				}

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, params.Stt.SttDestinationDistrictID).Return(&model.District{
					Data: model.DistrictData{
						Code:                  `DKI17`,
						Name:                  `KEBON JERUK, JAKARTA BARAT`,
						Type:                  model.TYPE_VENDOR,
						VendorCode:            model.TypeVendorJNE,
						VendorOriginCode:      ``,
						VendorDestinationCode: `CGK10000`,
						VendorBranchCode:      `CGK000`,
					},
				}, nil).Once()
				mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
		{
			name: "TestJneCreateAirwaybillV1-VendorBranchCode_Empty",
			args: args{
				ctx:    ctx,
				params: params,
			},
			want:    nil,
			errRes:  fmt.Errorf(`JneCreateAirwaybillV1-district %s has not been mapped with branch jne`, params.Stt.SttDestinationCityID),
			wantErr: true,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{
					repoDistrict: mockDistrictRepo,
					partnerLog:   mockPartnerLogRepo,
				}

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, params.Stt.SttDestinationDistrictID).Return(&model.District{
					Data: model.DistrictData{
						Code:                  `DKI17`,
						Name:                  `KEBON JERUK, JAKARTA BARAT`,
						Type:                  model.VENDOR,
						VendorCode:            model.TypeVendorJNE,
						VendorOriginCode:      `CGK10000`,
						VendorDestinationCode: `CGK10000`,
						VendorBranchCode:      ``,
					},
				}, nil).Once()
				mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
		{
			name: "TestJneCreateAirwaybillV1-VendorDestinationCode_Empty",
			args: args{
				ctx:    ctx,
				params: params,
			},
			want:    nil,
			errRes:  fmt.Errorf(`JneCreateAirwaybillV1-district %s has not been mapped with location destination jne`, params.Stt.SttDestinationCityID),
			wantErr: true,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{
					repoDistrict: mockDistrictRepo,
					partnerLog:   mockPartnerLogRepo,
				}

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, params.Stt.SttDestinationDistrictID).Return(&model.District{
					Data: model.DistrictData{
						Code:                  `DKI17`,
						Name:                  `KEBON JERUK, JAKARTA BARAT`,
						Type:                  model.VENDOR,
						VendorCode:            model.TypeVendorJNE,
						VendorOriginCode:      `CGK10000`,
						VendorDestinationCode: ``,
						VendorBranchCode:      `CGK000`,
					},
				}, nil).Once()
				mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
		{
			name: "TestJneCreateAirwaybillV1-sttVendor_Error",
			args: args{
				ctx:    ctx,
				params: params,
			},
			want:    nil,
			errRes:  fmt.Errorf(`JneCreateAirwaybillV1-failed getting stt vendor. stt no %s, err : %v`, params.Stt.SttNo, errorDetail.Error()),
			wantErr: true,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{
					repoDistrict:  mockDistrictRepo,
					sttVendorRepo: mockSttVendorRepo,
					partnerLog:    mockPartnerLogRepo,
				}

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, params.Stt.SttDestinationDistrictID).Return(mockDistrict, nil).Once()
				mockSttVendorRepo.On("Get", mock.Anything, &model.SttVendorViewParams{SttID: mockStt.SttID}).Return(nil, errorDetail).Once()
				mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
		{
			name: "TestJneCreateAirwaybillV1-airwaybill_Error",
			args: args{
				ctx:    ctx,
				params: params,
			},
			want:    nil,
			errRes:  fmt.Errorf(`JneCreateAirwaybillV1-failed create airwaybill. error : %s`, shared.CheckErrorNil(errorDetail)),
			wantErr: true,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{
					jneRepo:       mockJNERepo,
					repoDistrict:  mockDistrictRepo,
					sttVendorRepo: mockSttVendorRepo,
					partnerLog:    mockPartnerLogRepo,
				}

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, params.Stt.SttDestinationDistrictID).Return(mockDistrict, nil).Once()
				mockSttVendorRepo.On("Get", mock.Anything, &model.SttVendorViewParams{SttID: mockStt.SttID}).Return(nil, nil).Once()
				mockJNERepo.On("CreateAirwaybill", mock.Anything, &params.CreateAirwaybillRequest).Return(mockCreateAirwailbill, errorDetail).Once()
				mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
		{
			name: "TestJneCreateAirwaybillV1-airwaybill.Status_false",
			args: args{
				ctx:    ctx,
				params: params,
			},
			want:    nil,
			errRes:  fmt.Errorf(`JneCreateAirwaybillV1-failed create airwaybill. airwaybill error : %v`, mockCreateAirwailbill.Error),
			wantErr: true,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{
					jneRepo:       mockJNERepo,
					repoDistrict:  mockDistrictRepo,
					sttVendorRepo: mockSttVendorRepo,
					partnerLog:    mockPartnerLogRepo,
				}

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, params.Stt.SttDestinationDistrictID).Return(mockDistrict, nil).Once()
				mockSttVendorRepo.On("Get", mock.Anything, &model.SttVendorViewParams{SttID: mockStt.SttID}).Return(nil, nil).Once()
				mockJNERepo.On("CreateAirwaybill", mock.Anything, &params.CreateAirwaybillRequest).Return(&model.CreateAirwaybillResponse{
					Status: false,
					Detail: []model.DetailResponse{},
				}, nil).Once()
				mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
		{
			name: "TestJneCreateAirwaybillV1-airwaybill.Detail[0].Status_NotEqual_model.JNEStatusSuccess",
			args: args{
				ctx:    ctx,
				params: params,
			},
			want:    nil,
			errRes:  fmt.Errorf(`JneCreateAirwaybillV1-failed create airwaybill. airwaybill detail reason : %v`, mockCreateAirwailbill.Detail[0].Reason),
			wantErr: true,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{
					jneRepo:       mockJNERepo,
					repoDistrict:  mockDistrictRepo,
					sttVendorRepo: mockSttVendorRepo,
					partnerLog:    mockPartnerLogRepo,
				}

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, params.Stt.SttDestinationDistrictID).Return(mockDistrict, nil).Once()
				mockSttVendorRepo.On("Get", mock.Anything, &model.SttVendorViewParams{SttID: mockStt.SttID}).Return(nil, nil).Once()
				mockJNERepo.On("CreateAirwaybill", mock.Anything, &params.CreateAirwaybillRequest).Return(&model.CreateAirwaybillResponse{
					Status: true,
					Detail: []model.DetailResponse{
						{
							Status:  "status",
							CnoteNo: `0101162200324602`,
						},
					},
				}, nil).Once()
				mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
		{
			name: "TestJneCreateAirwaybillV1-Create_Error",
			args: args{
				ctx:    ctx,
				params: params,
			},
			want:    nil,
			errRes:  fmt.Errorf(`JneCreateAirwaybillV1-failed create stt vendor with order. cNoteNo: %s, err: %s`, `0101162200324602`, shared.CheckErrorNil(errorDetail)),
			wantErr: true,
			beforeFunc: func() *jneCtx {
				c := &jneCtx{
					jneRepo:       mockJNERepo,
					repoDistrict:  mockDistrictRepo,
					sttVendorRepo: mockSttVendorRepo,
					partnerLog:    mockPartnerLogRepo,
				}

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, params.Stt.SttDestinationDistrictID).Return(mockDistrict, nil).Once()
				mockSttVendorRepo.On("Get", mock.Anything, &model.SttVendorViewParams{SttID: mockStt.SttID}).Return(nil, nil).Once()
				mockJNERepo.On("CreateAirwaybill", mock.Anything, &params.CreateAirwaybillRequest).Return(mockCreateAirwailbill, nil).Once()
				mockSttVendorRepo.On("Create", mock.Anything, sttVendor).Return(errorDetail).Once()
				mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			uc := tt.beforeFunc()
			got, err := uc.JneCreateAirwaybill(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr || !assert.Equal(t, err, tt.errRes) {
				t.Errorf("jneCtx.JneCreateAirwaybill(); err = %v, ErrorResponse = %v, got = %v, want = %v\n", err, tt.errRes, got, tt.want)
				return
			}
			if !assert.Equal(t, got, tt.want) {
				t.Errorf("jneCtx.JneCreateAirwaybill(); got = %v, want = %v\n", got, tt.want)
			}
		})
	}
}

// TestJneRetryAirwaybillSuccess ...
func TestJneRetryAirwaybillSuccess(t *testing.T) {
	mockJNERepo := new(mocks.JNERepository)
	mockDistrictRepo := new(mocks.DistrictRepository)
	mockSttVendorRepo := new(mocks.SttVendorRepository)
	mockPartnerLogRepo := new(mocks.PartnerLogRepository)
	mockSttRepo := new(mocks.SttRepository)
	mockPartnerRepo := new(mocks.PartnerRepository)

	s := &jneCtx{
		jneRepo:       mockJNERepo,
		repoDistrict:  mockDistrictRepo,
		sttVendorRepo: mockSttVendorRepo,
		partnerLog:    mockPartnerLogRepo,
		partnerRepo:   mockPartnerRepo,
		sttRepo:       mockSttRepo,
	}

	// mock District
	mockDistrict := &model.District{
		Data: model.DistrictData{
			Code:                  `DKI17`,
			Name:                  `KEBON JERUK, JAKARTA BARAT`,
			Type:                  model.VENDOR,
			VendorCode:            model.TypeVendorJNE,
			VendorOriginCode:      `CGK10000`,
			VendorDestinationCode: `CGK10000`,
			VendorBranchCode:      `CGK000`,
		},
	}

	// mock Stt
	mockStt := &model.Stt{
		SttID:                      1,
		SttNo:                      `11LP1643979402446`,
		SttShipmentID:              `ADO9ZB4U`,
		SttDestinationCityID:       `CGK`,
		SttDestinationCityName:     `JAKARTA`,
		SttDestinationDistrictID:   mockDistrict.Data.Code,
		SttDestinationDistrictName: mockDistrict.Data.Name,
		SttRecipientName:           `Spongebob`,
		SttRecipientAddress:        `Jalan Jeruk Manis III`,
		SttRecipientPhone:          `6283827014867`,
		SttTotalPiece:              1,
		SttChargeableWeight:        1,
		SttGoodsEstimatePrice:      100000,
	}

	// mock Partner
	mockPartner := &model.Partner{
		Data: model.PartnerBase{
			ID:          41,
			Name:        `Console Kedoya`,
			PhoneNumber: `0876543212456`,
			Address:     `JALAN AGAVE RAYA NO 55, KEDOYA SELATAN, JAKARTA BARAT`,
			PartnerLocation: &model.PartnerLocationBase{
				City: &model.City{
					Name: `JAKARTA`,
				},
			},
		},
	}

	// params
	params := &jne.JneRetryRequest{
		PartnerID: 41,
		SttNo:     `11LP1643979402446`,
		Token:     GetTokenMock(),
	}

	// split shipper address
	shipperAddress := shared.SplitCharacters(mockPartner.Data.Address, 30, 3)
	// split receiver address
	receiverAddress := shared.SplitCharacters(mockStt.SttRecipientAddress, 30, 3)

	payloadCreateAirwaybill := &model.JneCreateAirwaybillRequest{
		Stt:     *mockStt,
		Partner: mockPartner,
		Token:   params.Token,
		CreateAirwaybillRequest: model.CreateAirwaybillRequest{

			OLShopBranch:      mockDistrict.Data.VendorBranchCode,
			OLShopOrderID:     mockStt.SttNo,
			OLShopShipperName: fmt.Sprintf(`Lion Parcel %s`, mockStt.SttDestinationCityName),
			OLShopShipperAddr1: func() string {
				add1 := shared.GetValueSliceByIndex(shipperAddress, 0)
				if add1 != nil {
					return add1.(string)
				}
				return ``
			}(),
			OLShopShipperAddr2: func() string {
				add2 := shared.GetValueSliceByIndex(shipperAddress, 1)
				if add2 != nil {
					return add2.(string)
				}
				return ``
			}(),
			OLShopShipperAddr3: func() string {
				add3 := shared.GetValueSliceByIndex(shipperAddress, 2)
				if add3 != nil {
					return add3.(string)
				}
				return ``
			}(),
			OLShopShipperCity:  mockPartner.Data.PartnerLocation.City.Name,
			OLShopShipperZip:   model.JNEZipcodeDefault,
			OLShopShipperPhone: mockPartner.Data.PhoneNumber,
			OLShopReceiverName: mockStt.SttRecipientName,
			OLShopReceiverAddr1: func() string {
				add1 := shared.GetValueSliceByIndex(receiverAddress, 0)
				if add1 != nil {
					return add1.(string)
				}
				return ``
			}(),
			OLShopReceiverAddr2: func() string {
				add2 := shared.GetValueSliceByIndex(receiverAddress, 1)
				if add2 != nil {
					return add2.(string)
				}
				return ``
			}(),
			OLShopReceiverAddr3: func() string {
				add3 := shared.GetValueSliceByIndex(receiverAddress, 2)
				if add3 != nil {
					return add3.(string)
				}
				return ``
			}(),
			OLShopReceiverCity:  mockStt.SttDestinationCityName,
			OLShopReceiverZip:   model.JNEZipcodeDefault,
			OLShopReceiverPhone: mockStt.SttRecipientPhone,
			OLShopQty:           strconv.Itoa(mockStt.SttTotalPiece),
			OLShopweight:        fmt.Sprintf(`%.f`, math.Trunc(mockStt.SttChargeableWeight)),
			OLShopGoodsdesc:     mockStt.SttCommodityName,
			OLShopGoodsvalue:    fmt.Sprintf(`%.2f`, mockStt.SttGoodsEstimatePrice),
			OLShopGoodstype:     model.JNEGoodsTypePackage,
			OLShopInsFlag:       model.JNEInsFlagNo,
			OLShopOrig:          mockDistrict.Data.VendorOriginCode,
			OLShopDest:          mockDistrict.Data.VendorDestinationCode,
			OLShopService:       model.JNEServiceTypeREG,
			OLShopCodFlag:       model.JNEIsCodNo,
			OLShopCodAmount:     `0`,
		},
	}

	// mockJneCreateAirwaybillResponse := &model.JneCreateAirwaybillResponse{
	// 	Cnote: `0101162200324602`,
	// }

	mockJneRetryAirwaybillResponse := &jne.JneRetryResponse{
		Cnote: "0101162200324602",
	}

	mockPartnerRepo.On("GetByID", mock.Anything, params.PartnerID, params.Token).Return(mockPartner, nil).Once()
	mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{Stt: model.Stt{SttNo: params.SttNo}}).Return(mockStt, nil).Once()

	mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: params.Token}, payloadCreateAirwaybill.Stt.SttDestinationDistrictID).Return(mockDistrict, nil).Once()

	// Mock Create Airwailbill
	mockCreateAirwailbill := &model.CreateAirwaybillResponse{
		Status: true,
		Detail: []model.DetailResponse{
			{
				Status:  model.JNEStatusSuccess,
				CnoteNo: `0101162200324602`,
			},
		},
	}

	mockSttVendorRepo.On("Get", mock.Anything, &model.SttVendorViewParams{SttID: mockStt.SttID}).Return(nil, nil).Once()

	mockJNERepo.On("CreateAirwaybill", mock.Anything, &payloadCreateAirwaybill.CreateAirwaybillRequest).Return(mockCreateAirwailbill, nil).Once()

	sttVendor := &model.SttVendor{
		SttVendorVendorCode:  model.TypeVendorJNE,
		SttVendorReferenceNo: mockCreateAirwailbill.Detail[0].CnoteNo,
		SttVendorSttNo:       payloadCreateAirwaybill.Stt.SttNo,
		SttVendorSttID:       int(payloadCreateAirwaybill.Stt.SttID),
	}
	mockSttVendorRepo.On("Create", mock.Anything, sttVendor).Return(nil).Once()
	mockPartnerLogRepo.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

	t.Run("TestJneRetryAirwaybillSuccess", func(t *testing.T) {

		ctxBg, cancel := lputils.CreateContext(60) // 60 seconds timeout
		defer cancel()
		jneRetryAirwaybillResp, err := s.JneRetryAirwaybill(ctxBg, params)

		assert.NoError(t, err)

		if !reflect.DeepEqual(mockJneRetryAirwaybillResponse, jneRetryAirwaybillResp) {
			t.Errorf("TestJneRetryAirwaybillSuccess() got = %v, want %v", jneRetryAirwaybillResp, mockJneRetryAirwaybillResponse)
		}
	})
}

func Test_jneCtx_setToPod(t *testing.T) {
	type args struct {
		ctx       context.Context
		params    *jne.JneWebhookRequest
		sttDetail []model.SttDetailResult
	}

	ctx, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()
	token := "token"
	errorDetail := errors.New("error")
	now := time.Now()
	nowUTC7, _ := shared.ParseUTC7(shared.FormatDateTime, now.Format(shared.FormatDateTime))

	tests := []struct {
		name       string
		args       args
		wantErr    bool
		errResp    error
		beforeFunc func() *jneCtx
	}{
		{
			name: "error_panic",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					partnerLog: mockPartnerLogRepo,
				}

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEPod,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_get_token_genesis",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{{}},
			},
			wantErr: true,
			errResp: errors.New("Failed to get token genesis"),
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					repoAccount: mockAccountRepo,
					partnerLog:  mockPartnerLogRepo,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEPod,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("Failed to get token genesis").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_get_destination_city",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("An error occurred while querying db"),
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)

				c := &jneCtx{
					repoAccount:    mockAccountRepo,
					deliveryRepo:   mockDeliveryRepo,
					repoCity:       mockCityRepo,
					partnerLog:     mockPartnerLogRepo,
					sttHistoryRepo: mockSttPieceHistoryRepo,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
					},
				}, nil).Once()

				mockSttPieceHistoryRepo.On("SelectBySttNo", mock.Anything, &model.SttPieceHistoryViewParam{
					SttNoIn:   []string{"11LP1234512345"},
					SortBy:    `sph.history_id`,
					OrderDesc: true,
				}).Return([]model.SttPieceHistoryCustom{
					{HistoryStatus: model.DEL},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEPod,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("An error occurred while querying db").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "destination_city_not_found",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("City destination is not found"),
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)

				c := &jneCtx{
					repoAccount:    mockAccountRepo,
					deliveryRepo:   mockDeliveryRepo,
					repoCity:       mockCityRepo,
					partnerLog:     mockPartnerLogRepo,
					sttHistoryRepo: mockSttPieceHistoryRepo,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
					},
				}, nil).Once()

				mockSttPieceHistoryRepo.On("SelectBySttNo", mock.Anything, &model.SttPieceHistoryViewParam{
					SttNoIn:   []string{"11LP1234512345"},
					SortBy:    `sph.history_id`,
					OrderDesc: true,
				}).Return([]model.SttPieceHistoryCustom{
					{HistoryStatus: model.DEL},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(nil, nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEPod,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("City destination is not found").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "last_status_not_allowed_to_pod",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.POD,
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("STT cannot be updated"),
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockSttPiecesHistoryRepo := new(mocks.SttPieceHistoryRepository)

				c := &jneCtx{
					repoAccount:    mockAccountRepo,
					deliveryRepo:   mockDeliveryRepo,
					repoCity:       mockCityRepo,
					partnerLog:     mockPartnerLogRepo,
					sttHistoryRepo: mockSttPiecesHistoryRepo,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
						SttLastStatusID:      model.POD,
					},
				}, nil).Once()

				mockSttPiecesHistoryRepo.On("SelectBySttNo", mock.Anything, &model.SttPieceHistoryViewParam{
					SttNoIn:   []string{"11LP1234512345"},
					SortBy:    `sph.history_id`,
					OrderDesc: true,
				}).Return([]model.SttPieceHistoryCustom{
					{HistoryStatus: model.POD},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEPod,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("STT cannot be updated").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_create",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEL,
						},
					},
				},
			},
			wantErr: true,
			errResp: shared.ErrUnexpected,
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockTimeRepo := new(mocks.TimeRepository)
				mockPodRepo := new(mocks.PodRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockSttPiecesHistoryRepo := new(mocks.SttPieceHistoryRepository)

				c := &jneCtx{
					repoAccount:    mockAccountRepo,
					deliveryRepo:   mockDeliveryRepo,
					repoCity:       mockCityRepo,
					podRepo:        mockPodRepo,
					timeRepo:       mockTimeRepo,
					partnerLog:     mockPartnerLogRepo,
					sttHistoryRepo: mockSttPiecesHistoryRepo,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttID:                1,
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
						SttLastStatusID:      model.DEL,
					},
				}, nil).Once()

				mockSttPiecesHistoryRepo.On("SelectBySttNo", mock.Anything, &model.SttPieceHistoryViewParam{
					SttNoIn:   []string{"11LP1234512345"},
					SortBy:    `sph.history_id`,
					OrderDesc: true,
				}).Return([]model.SttPieceHistoryCustom{
					{HistoryStatus: model.DEL},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPodRepo.On("Create", mock.Anything, &pod.CreatePodDexParams{
					Status:    model.POD,
					SttID:     1,
					ActorID:   model.AccountJNE.ActorID,
					ActorName: model.AccountJNE.ActorName,
					ActorRole: model.VENDOR,
					Remarks: func() string {
						remarks := model.DeliveryRemarks{
							CreatedBy: model.AccountJNE.ActorName,
						}
						return remarks.ToString()
					}(),
					DeliveryID: 1,
					UpdatedAt: func() time.Time {
						ua, _ := shared.ParseUTC7(shared.FormatDateTime, now.Format(shared.FormatDateTime))
						return ua
					}(),
					Histories: []model.SttPieceHistory{
						{
							HistoryStatus:    model.POD,
							HistoryLocation:  "JOG",
							HistoryActorID:   model.AccountJNE.ActorID,
							HistoryActorName: model.AccountJNE.ActorName,
							HistoryActorRole: model.VENDOR,
							HistoryCreatedBy: int(model.AccountJNE.ActorID),
							HistoryCreatedAt: func() time.Time {
								ca, _ := shared.ParseUTC7(shared.FormatDateTime, now.Format(shared.FormatDateTime))
								return ca
							}(),
							HistoryCreatedName: model.AccountJNE.ActorName,
							HistoryRemark: func() string {
								hr := model.RemarkPieceHistory{
									HistoryLocationName: "JOGJAKARTA",
									Attactments:         []string{"", ""},
								}
								return hr.ToString()
							}(),
						},
					},
				}).Return(errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEPod,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: shared.ErrUnexpected.Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "success_book_internal_for_client",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEL,
							SttClientID:          2,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockTimeRepo := new(mocks.TimeRepository)
				mockPodRepo := new(mocks.PodRepository)
				mockGatewaySttStatusUc := new(ucMocks.GatewaySttStatus)
				mockMessegeGatewayUc := new(ucMocks.MessageGateway)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockRtcUc := new(ucMocks.ReadyToCargo)
				mockRequestPriorityDelivery := new(ucmock.RequestPriorityDelivery)
				mockSttPiecesHistoryRepo := new(mocks.SttPieceHistoryRepository)

				c := &jneCtx{
					repoAccount:             mockAccountRepo,
					deliveryRepo:            mockDeliveryRepo,
					repoCity:                mockCityRepo,
					podRepo:                 mockPodRepo,
					timeRepo:                mockTimeRepo,
					gatewaySttStatus:        mockGatewaySttStatusUc,
					messageGatewayUc:        mockMessegeGatewayUc,
					partnerLog:              mockPartnerLogRepo,
					rtcUc:                   mockRtcUc,
					requestPriorityDelivery: mockRequestPriorityDelivery,
					sttHistoryRepo:          mockSttPiecesHistoryRepo,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttID:                1,
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
						SttLastStatusID:      model.DEL,
						SttClientID:          2,
					},
				}, nil).Once()

				mockSttPiecesHistoryRepo.On("SelectBySttNo", mock.Anything, &model.SttPieceHistoryViewParam{
					SttNoIn:   []string{"11LP1234512345"},
					SortBy:    `sph.history_id`,
					OrderDesc: true,
				}).Return([]model.SttPieceHistoryCustom{
					{HistoryStatus: model.DEL},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code:     "JOG",
					Name:     "JOGJAKARTA",
					Timezone: shared.AsiaJakarta,
				}, nil).Once()

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPodRepo.On("Create", mock.Anything, &pod.CreatePodDexParams{
					Status:    model.POD,
					SttID:     1,
					ActorID:   model.AccountJNE.ActorID,
					ActorName: model.AccountJNE.ActorName,
					ActorRole: model.VENDOR,
					Remarks: func() string {
						remarks := model.DeliveryRemarks{
							CreatedBy: model.AccountJNE.ActorName,
						}
						return remarks.ToString()
					}(),
					DeliveryID: 1,
					UpdatedAt:  nowUTC7,
					Histories: []model.SttPieceHistory{
						{
							HistoryStatus:      model.POD,
							HistoryLocation:    "JOG",
							HistoryActorID:     model.AccountJNE.ActorID,
							HistoryActorName:   model.AccountJNE.ActorName,
							HistoryActorRole:   model.VENDOR,
							HistoryCreatedBy:   int(model.AccountJNE.ActorID),
							HistoryCreatedAt:   nowUTC7,
							HistoryCreatedName: model.AccountJNE.ActorName,
							HistoryRemark: func() string {
								hr := model.RemarkPieceHistory{
									HistoryLocationName: "JOGJAKARTA",
									Attactments:         []string{"", ""},
								}
								return hr.ToString()
							}(),
						},
					},
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("StatusSubmit", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo:       "11LP1234512345",
						Datetime:    nowUTC7.UTC(),
						StatusCode:  model.POD,
						Location:    "JOG",
						Remarks:     fmt.Sprintf(`Paket diterima oleh %s`, ""),
						City:        "JOGJAKARTA",
						UpdatedBy:   model.JNEName,
						UpdatedOn:   nowUTC7.UTC(),
						Attachments: []string{""},
					},
					ServiceType: model.PACKAGESERVICE,
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("GoberCodCommission", mock.Anything, &model.GoberCodCommission{
					SttNo:            "11LP1234512345",
					SttStatus:        model.POD,
					SttBookedType:    model.INTERNAL,
					SttBookedForID:   2,
					SttBookedForType: model.CLIENT,
					SttActorPodBy:    model.AccountJNE.ActorID,
					SttActorPodType:  model.AccountJNE.ActorName,
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("TriggerReleasePosParentCommission", mock.Anything, mock.Anything)

				mockGatewaySttStatusUc.On("GoberDTPOLCommission", mock.Anything, mock.Anything).Return(nil).Once()

				mockMessegeGatewayUc.On("SendMessage", mock.Anything, &model.SendMessageRequest{
					PackageType: model.NonCOD,
					EventStatus: model.POD,
					Token:       token,
					IdDelivery:  1,
				}, &model.Stt{
					SttID:                1,
					SttNo:                "11LP1234512345",
					SttDestinationCityID: "JOG",
					SttLastStatusID:      model.DEL,
					SttClientID:          2,
				}).Return(nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEPod,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				mockRtcUc.On("UpdateInactiveRTCBySttId", mock.Anything, 1).Return(nil).Once()

				mockRequestPriorityDelivery.On("UpdateIsShowToZero", mock.Anything, "11LP1234512345").Return(nil).Once()

				return c
			},
		},
		{
			name: "success_book_other_for_client",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEL,
							SttBookedBy:          1,
							SttClientID:          2,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockTimeRepo := new(mocks.TimeRepository)
				mockPodRepo := new(mocks.PodRepository)
				mockGatewaySttStatusUc := new(ucMocks.GatewaySttStatus)
				mockMessegeGatewayUc := new(ucMocks.MessageGateway)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockRtcUc := new(ucMocks.ReadyToCargo)
				mockRequestPriorityDelivery := new(ucmock.RequestPriorityDelivery)
				mockSttPiecesHistoryRepo := new(mocks.SttPieceHistoryRepository)

				c := &jneCtx{
					repoAccount:             mockAccountRepo,
					deliveryRepo:            mockDeliveryRepo,
					repoCity:                mockCityRepo,
					podRepo:                 mockPodRepo,
					timeRepo:                mockTimeRepo,
					gatewaySttStatus:        mockGatewaySttStatusUc,
					messageGatewayUc:        mockMessegeGatewayUc,
					partnerLog:              mockPartnerLogRepo,
					rtcUc:                   mockRtcUc,
					requestPriorityDelivery: mockRequestPriorityDelivery,
					sttHistoryRepo:          mockSttPiecesHistoryRepo,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttID:                1,
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
						SttLastStatusID:      model.DEL,
						SttBookedBy:          1,
						SttClientID:          2,
						SttIsCOD:             true,
					},
				}, nil).Once()

				mockSttPiecesHistoryRepo.On("SelectBySttNo", mock.Anything, &model.SttPieceHistoryViewParam{
					SttNoIn:   []string{"11LP1234512345"},
					SortBy:    `sph.history_id`,
					OrderDesc: true,
				}).Return([]model.SttPieceHistoryCustom{
					{HistoryStatus: model.DEL},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code:     "JOG",
					Name:     "JOGJAKARTA",
					Timezone: shared.AsiaJakarta,
				}, nil).Once()

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPodRepo.On("Create", mock.Anything, &pod.CreatePodDexParams{
					Status:    model.POD,
					SttID:     1,
					ActorID:   model.AccountJNE.ActorID,
					ActorName: model.AccountJNE.ActorName,
					ActorRole: model.VENDOR,
					Remarks: func() string {
						remarks := model.DeliveryRemarks{
							CreatedBy: model.AccountJNE.ActorName,
						}
						return remarks.ToString()
					}(),
					DeliveryID: 1,
					UpdatedAt:  nowUTC7,
					Histories: []model.SttPieceHistory{
						{
							HistoryStatus:      model.POD,
							HistoryLocation:    "JOG",
							HistoryActorID:     model.AccountJNE.ActorID,
							HistoryActorName:   model.AccountJNE.ActorName,
							HistoryActorRole:   model.VENDOR,
							HistoryCreatedBy:   int(model.AccountJNE.ActorID),
							HistoryCreatedAt:   nowUTC7,
							HistoryCreatedName: model.AccountJNE.ActorName,
							HistoryRemark: func() string {
								hr := model.RemarkPieceHistory{
									HistoryLocationName: "JOGJAKARTA",
									Attactments:         []string{"", ""},
								}
								return hr.ToString()
							}(),
						},
					},
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("StatusSubmit", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo:       "11LP1234512345",
						Datetime:    nowUTC7.UTC(),
						StatusCode:  model.POD,
						Location:    "JOG",
						Remarks:     fmt.Sprintf(`Paket diterima oleh %s`, ""),
						City:        "JOGJAKARTA",
						UpdatedBy:   model.JNEName,
						UpdatedOn:   nowUTC7.UTC(),
						Attachments: []string{""},
					},
					ServiceType: model.PACKAGESERVICE,
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("TriggerReleasePosParentCommission", mock.Anything, mock.Anything)

				mockGatewaySttStatusUc.On("GoberCodCommission", mock.Anything, &model.GoberCodCommission{
					SttNo:            "11LP1234512345",
					SttStatus:        model.POD,
					SttBookedBy:      1,
					SttBookedForID:   2,
					SttBookedForType: model.CLIENT,
					SttActorPodBy:    model.AccountJNE.ActorID,
					SttActorPodType:  model.AccountJNE.ActorName,
					SttIsCod:         true,
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("TriggerReleasePosParentCommission", mock.Anything, mock.Anything)

				mockGatewaySttStatusUc.On("GoberDTPOLCommission", mock.Anything, mock.Anything).Return(nil).Once()

				mockMessegeGatewayUc.On("SendMessage", mock.Anything, &model.SendMessageRequest{
					PackageType: model.COD,
					EventStatus: model.POD,
					Token:       token,
					IdDelivery:  1,
				}, &model.Stt{
					SttID:                1,
					SttNo:                "11LP1234512345",
					SttDestinationCityID: "JOG",
					SttLastStatusID:      model.DEL,
					SttBookedBy:          1,
					SttClientID:          2,
					SttIsCOD:             true,
				}).Return(nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEPod,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				mockRtcUc.On("UpdateInactiveRTCBySttId", mock.Anything, 1).Return(nil).Once()

				mockRequestPriorityDelivery.On("UpdateIsShowToZero", mock.Anything, "11LP1234512345").Return(nil).Once()

				return c
			},
		},
		{
			name: "success_book_other_for_pos",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEL,
							SttBookedBy:          1,
							SttPosID:             2,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockTimeRepo := new(mocks.TimeRepository)
				mockPodRepo := new(mocks.PodRepository)
				mockGatewaySttStatusUc := new(ucMocks.GatewaySttStatus)
				mockMessegeGatewayUc := new(ucMocks.MessageGateway)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockRtcUc := new(ucMocks.ReadyToCargo)
				mockRequestPriorityDelivery := new(ucmock.RequestPriorityDelivery)
				mockSttPiecesHistoryRepo := new(mocks.SttPieceHistoryRepository)

				c := &jneCtx{
					repoAccount:             mockAccountRepo,
					deliveryRepo:            mockDeliveryRepo,
					repoCity:                mockCityRepo,
					podRepo:                 mockPodRepo,
					timeRepo:                mockTimeRepo,
					gatewaySttStatus:        mockGatewaySttStatusUc,
					messageGatewayUc:        mockMessegeGatewayUc,
					partnerLog:              mockPartnerLogRepo,
					rtcUc:                   mockRtcUc,
					requestPriorityDelivery: mockRequestPriorityDelivery,
					sttHistoryRepo:          mockSttPiecesHistoryRepo,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttID:                1,
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
						SttLastStatusID:      model.DEL,
						SttBookedBy:          1,
						SttPosID:             2,
						SttIsCOD:             true,
					},
				}, nil).Once()

				mockSttPiecesHistoryRepo.On("SelectBySttNo", mock.Anything, &model.SttPieceHistoryViewParam{
					SttNoIn:   []string{"11LP1234512345"},
					SortBy:    `sph.history_id`,
					OrderDesc: true,
				}).Return([]model.SttPieceHistoryCustom{
					{HistoryStatus: model.DEL},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code:     "JOG",
					Name:     "JOGJAKARTA",
					Timezone: shared.AsiaJakarta,
				}, nil).Once()

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPodRepo.On("Create", mock.Anything, &pod.CreatePodDexParams{
					Status:    model.POD,
					SttID:     1,
					ActorID:   model.AccountJNE.ActorID,
					ActorName: model.AccountJNE.ActorName,
					ActorRole: model.VENDOR,
					Remarks: func() string {
						remarks := model.DeliveryRemarks{
							CreatedBy: model.AccountJNE.ActorName,
						}
						return remarks.ToString()
					}(),
					DeliveryID: 1,
					UpdatedAt:  nowUTC7,
					Histories: []model.SttPieceHistory{
						{
							HistoryStatus:      model.POD,
							HistoryLocation:    "JOG",
							HistoryActorID:     model.AccountJNE.ActorID,
							HistoryActorName:   model.AccountJNE.ActorName,
							HistoryActorRole:   model.VENDOR,
							HistoryCreatedBy:   int(model.AccountJNE.ActorID),
							HistoryCreatedAt:   nowUTC7,
							HistoryCreatedName: model.AccountJNE.ActorName,
							HistoryRemark: func() string {
								hr := model.RemarkPieceHistory{
									HistoryLocationName: "JOGJAKARTA",
									Attactments:         []string{"", ""},
								}
								return hr.ToString()
							}(),
						},
					},
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("StatusSubmit", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo:       "11LP1234512345",
						Datetime:    nowUTC7.UTC(),
						StatusCode:  model.POD,
						Location:    "JOG",
						Remarks:     fmt.Sprintf(`Paket diterima oleh %s`, ""),
						City:        "JOGJAKARTA",
						UpdatedBy:   model.JNEName,
						UpdatedOn:   nowUTC7.UTC(),
						Attachments: []string{""},
					},
					ServiceType: model.PACKAGESERVICE,
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("GoberCodCommission", mock.Anything, &model.GoberCodCommission{
					SttNo:            "11LP1234512345",
					SttStatus:        model.POD,
					SttBookedBy:      1,
					SttBookedForID:   2,
					SttBookedForType: model.POS,
					SttActorPodBy:    model.AccountJNE.ActorID,
					SttActorPodType:  model.AccountJNE.ActorName,
					SttIsCod:         true,
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("TriggerReleasePosParentCommission", mock.Anything, mock.Anything)

				mockGatewaySttStatusUc.On("GoberDTPOLCommission", mock.Anything, mock.Anything).Return(nil).Once()

				mockMessegeGatewayUc.On("SendMessage", mock.Anything, &model.SendMessageRequest{
					PackageType: model.COD,
					EventStatus: model.POD,
					Token:       token,
					IdDelivery:  1,
				}, &model.Stt{
					SttID:                1,
					SttNo:                "11LP1234512345",
					SttDestinationCityID: "JOG",
					SttLastStatusID:      model.DEL,
					SttBookedBy:          1,
					SttPosID:             2,
					SttIsCOD:             true,
				}).Return(nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEPod,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				mockRtcUc.On("UpdateInactiveRTCBySttId", mock.Anything, 1).Return(nil).Once()

				mockRequestPriorityDelivery.On("UpdateIsShowToZero", mock.Anything, "11LP1234512345").Return(nil).Once()

				return c
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.beforeFunc()

			err := c.setToPod(tt.args.ctx, tt.args.params, tt.args.sttDetail)
			if (err != nil) != tt.wantErr || !assert.Equal(t, err, tt.errResp) {
				t.Errorf("Test_jneCtx_setToPod; err = %v, ErrorResponse = %v\n", err, tt.errResp)
				return
			}
		})
	}
}

func Test_jneCtx_setToDex(t *testing.T) {
	t.SkipNow()
	type args struct {
		ctx       context.Context
		params    *jne.JneWebhookRequest
		sttDetail []model.SttDetailResult
	}

	ctx, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()
	token := "token"
	errorDetail := errors.New("error")
	now := time.Now()
	nowUTC7, _ := shared.ParseUTC7(shared.FormatDateTime, now.Format(shared.FormatDateTime))

	tests := []struct {
		name       string
		args       args
		wantErr    bool
		errResp    error
		beforeFunc func() *jneCtx
	}{
		{
			name: "error_panic",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					partnerLog: mockPartnerLogRepo,
				}

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDex,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_get_token_genesis",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{{}},
			},
			wantErr: true,
			errResp: errors.New("Failed to get token genesis"),
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					repoAccount: mockAccountRepo,
					partnerLog:  mockPartnerLogRepo,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDex,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("Failed to get token genesis").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_get_destination_city",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("An error occurred while querying db"),
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					repoAccount:  mockAccountRepo,
					deliveryRepo: mockDeliveryRepo,
					repoCity:     mockCityRepo,
					partnerLog:   mockPartnerLogRepo,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDex,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("An error occurred while querying db").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "destination_city_not_found",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("City destination is not found"),
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					repoAccount:  mockAccountRepo,
					deliveryRepo: mockDeliveryRepo,
					repoCity:     mockCityRepo,
					partnerLog:   mockPartnerLogRepo,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(nil, nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDex,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("City destination is not found").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "last_status_not_allowed_to_dex",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.SCRAP,
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("STT cannot be updated"),
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					repoAccount:  mockAccountRepo,
					deliveryRepo: mockDeliveryRepo,
					repoCity:     mockCityRepo,
					partnerLog:   mockPartnerLogRepo,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
						SttLastStatusID:      model.POD,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDex,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("STT cannot be updated").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "reason_not_found",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
					History: []jne.HistoryJNE{
						{
							StatusCode: model.DEL,
						},
					},
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEX,
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("STT cannot be updated"),
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockTimeRepo := new(mocks.TimeRepository)
				mockReasonRepo := new(mocks.ReasonRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockReasonVendorMapping := new(mocks.ReasonVendorMappingRepository)

				c := &jneCtx{
					repoAccount:         mockAccountRepo,
					deliveryRepo:        mockDeliveryRepo,
					repoCity:            mockCityRepo,
					timeRepo:            mockTimeRepo,
					reasonRepo:          mockReasonRepo,
					partnerLog:          mockPartnerLogRepo,
					reasonVendorMapping: mockReasonVendorMapping,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
						SttLastStatusID:      model.DEX,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockReasonVendorMapping.On("GetDetail", mock.Anything, &model.ReasonVendorMappingViewParams{
					RvmVendor: model.JNE,
					RvmStatus: model.DEL,
				}).Return(&model.ReasonVendorMapping{
					RvmID:          1,
					RvmVendor:      model.JNE,
					RvmReasonTitle: model.JNEDefaultDEXReason,
					RvmStatus:      model.DEL,
					RvmDesc:        "",
				}, nil).Once()

				mockReasonRepo.On("GetDetail", mock.Anything, &model.ReasonViewParams{
					ReasonTitle: model.JNEDefaultDEXReason,
				}).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDex,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
						History: []jne.HistoryJNE{
							{
								StatusCode: model.DEL,
							},
						},
					},
					Response: errors.New("STT cannot be updated").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_create",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
					History: []jne.HistoryJNE{
						{
							StatusCode: model.DEL,
						},
					},
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEL,
						},
					},
				},
			},
			wantErr: true,
			errResp: shared.ErrUnexpected,
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockTimeRepo := new(mocks.TimeRepository)
				mockReasonRepo := new(mocks.ReasonRepository)
				mockPodRepo := new(mocks.PodRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockReasonVendorMapping := new(mocks.ReasonVendorMappingRepository)

				c := &jneCtx{
					repoAccount:         mockAccountRepo,
					deliveryRepo:        mockDeliveryRepo,
					repoCity:            mockCityRepo,
					podRepo:             mockPodRepo,
					timeRepo:            mockTimeRepo,
					reasonRepo:          mockReasonRepo,
					partnerLog:          mockPartnerLogRepo,
					reasonVendorMapping: mockReasonVendorMapping,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttID:                1,
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
						SttLastStatusID:      model.DEL,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockReasonVendorMapping.On("GetDetail", mock.Anything, &model.ReasonVendorMappingViewParams{
					RvmVendor: model.JNE,
					RvmStatus: model.DEL,
				}).Return(&model.ReasonVendorMapping{
					RvmID:          1,
					RvmVendor:      model.JNE,
					RvmReasonTitle: model.JNEDefaultDEXReason,
					RvmStatus:      model.DEL,
					RvmDesc:        "",
				}, nil).Once()

				mockReasonRepo.On("GetDetail", mock.Anything, &model.ReasonViewParams{
					ReasonTitle: model.JNEDefaultDEXReason,
				}).Return(&model.ReasonDetailResult{}, nil).Once()

				mockPodRepo.On("Create", mock.Anything, &pod.CreatePodDexParams{
					Status:    model.DEX,
					SttID:     1,
					ActorID:   model.AccountJNE.ActorID,
					ActorName: model.AccountJNE.ActorName,
					ActorRole: model.VENDOR,
					Remarks: func() string {
						remarks := model.DeliveryRemarks{
							CreatedBy: model.AccountJNE.ActorName,
						}
						return remarks.ToString()
					}(),
					DeliveryID: 1,
					UpdatedAt: func() time.Time {
						ua, _ := shared.ParseUTC7(shared.FormatDateTime, now.Format(shared.FormatDateTime))
						return ua
					}(),
					Histories: []model.SttPieceHistory{
						{
							HistoryStatus:      model.DEX,
							HistoryLocation:    "JOG",
							HistoryActorID:     model.AccountJNE.ActorID,
							HistoryActorName:   model.AccountJNE.ActorName,
							HistoryActorRole:   model.VENDOR,
							HistoryCreatedBy:   int(model.AccountJNE.ActorID),
							HistoryCreatedAt:   nowUTC7,
							HistoryCreatedName: model.AccountJNE.ActorName,
							HistoryRemark: func() string {
								hr := model.RemarkPieceHistory{
									HistoryLocationName: "JOGJAKARTA",
									Attactments:         []string{""},
								}
								return hr.ToString()
							}(),
						},
					},
				}).Return(errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDex,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
						History: []jne.HistoryJNE{
							{
								StatusCode: model.DEL,
							},
						},
					},
					Response: shared.ErrUnexpected.Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "success_non_cod",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
					History: []jne.HistoryJNE{
						{
							StatusCode: model.DEL,
						},
					},
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEL,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockReasonVendorMapping := new(mocks.ReasonVendorMappingRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockTimeRepo := new(mocks.TimeRepository)
				mockReasonRepo := new(mocks.ReasonRepository)
				mockPodRepo := new(mocks.PodRepository)
				mockGatewaySttStatusUc := new(ucMocks.GatewaySttStatus)
				mockMessegeGatewayUc := new(ucMocks.MessageGateway)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockSalesforce := new(ucMocks.Salesforce)
				mockRtcUc := new(ucMocks.ReadyToCargo)
				mockRequestPriorityDelivery := new(ucmock.RequestPriorityDelivery)

				c := &jneCtx{
					repoAccount:             mockAccountRepo,
					deliveryRepo:            mockDeliveryRepo,
					repoCity:                mockCityRepo,
					podRepo:                 mockPodRepo,
					timeRepo:                mockTimeRepo,
					reasonRepo:              mockReasonRepo,
					gatewaySttStatus:        mockGatewaySttStatusUc,
					messageGatewayUc:        mockMessegeGatewayUc,
					partnerLog:              mockPartnerLogRepo,
					reasonVendorMapping:     mockReasonVendorMapping,
					rtcUc:                   mockRtcUc,
					salesforce:              mockSalesforce,
					requestPriorityDelivery: mockRequestPriorityDelivery,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttID:                1,
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
						SttLastStatusID:      model.DEL,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockReasonVendorMapping.On("GetDetail", mock.Anything, &model.ReasonVendorMappingViewParams{
					RvmVendor: model.JNE,
					RvmStatus: model.DEL,
				}).Return(&model.ReasonVendorMapping{
					RvmID:          1,
					RvmVendor:      model.JNE,
					RvmReasonTitle: model.JNEDefaultDEXReason,
					RvmStatus:      model.DEX,
					RvmDesc:        "",
				}, nil).Once()

				mockReasonRepo.On("GetDetail", mock.Anything, &model.ReasonViewParams{
					ReasonTitle: model.JNEDefaultDEXReason,
				}).Return(&model.ReasonDetailResult{}, nil).Once()

				mockPodRepo.On("Create", mock.Anything, &pod.CreatePodDexParams{
					Status:    model.DEX,
					SttID:     1,
					ActorID:   model.AccountJNE.ActorID,
					ActorName: model.AccountJNE.ActorName,
					ActorRole: model.VENDOR,
					Remarks: func() string {
						remarks := model.DeliveryRemarks{
							CreatedBy: model.AccountJNE.ActorName,
						}
						return remarks.ToString()
					}(),
					DeliveryID: 1,
					UpdatedAt: func() time.Time {
						ua, _ := shared.ParseUTC7(shared.FormatDateTime, now.Format(shared.FormatDateTime))
						return ua
					}(),
					Histories: []model.SttPieceHistory{
						{
							HistoryStatus:      model.DEX,
							HistoryLocation:    "JOG",
							HistoryActorID:     model.AccountJNE.ActorID,
							HistoryActorName:   model.AccountJNE.ActorName,
							HistoryActorRole:   model.VENDOR,
							HistoryCreatedBy:   int(model.AccountJNE.ActorID),
							HistoryCreatedAt:   nowUTC7,
							HistoryCreatedName: model.AccountJNE.ActorName,
							HistoryRemark: func() string {
								hr := model.RemarkPieceHistory{
									HistoryLocationName: "JOGJAKARTA",
									Attactments:         []string{""},
								}
								return hr.ToString()
							}(),
						},
					},
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("StatusSubmit", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo:       "11LP1234512345",
						Datetime:    nowUTC7.UTC(),
						StatusCode:  model.DEX,
						Location:    "JOG",
						Remarks:     `Paket gagal dikirim`,
						City:        "JOGJAKARTA",
						UpdatedBy:   model.JNEName,
						UpdatedOn:   nowUTC7.UTC(),
						Attachments: []string{""},
					},
					ServiceType: model.PACKAGESERVICE,
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("GoberDTPOLCommission", mock.Anything, mock.Anything).Return(nil).Once()

				mockMessegeGatewayUc.On("SendMessage", mock.Anything, &model.SendMessageRequest{
					PackageType: model.NonCOD,
					EventStatus: model.DEX,
					Token:       token,
					IdDelivery:  1,
				}, &model.Stt{
					SttID:                1,
					SttNo:                "11LP1234512345",
					SttDestinationCityID: "JOG",
					SttLastStatusID:      model.DEL,
				}).Return(nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDex,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
						History: []jne.HistoryJNE{
							{
								StatusCode: model.DEL,
							},
						},
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				mockRtcUc.On("UpdateInactiveRTCBySttId", mock.Anything, 1).Return(nil).Once()

				mockRequestPriorityDelivery.On("UpdateIsShowToZero", mock.Anything, "11LP1234512345").Return(nil).Once()

				mockSalesforce.On("SendPubsub", mock.Anything, model.PubsubCreateRTSParams{
					SttNo: "11LP1234512345",
				}).Return().Once()

				mockGatewaySttStatusUc.On("GetDeliveryAttachment", mock.Anything, true).Return("test")

				mockDeliveryRepo.On("CountSttDex", mock.Anything, mock.Anything).Return(1, nil)

				mockGatewaySttStatusUc.On("PublishCreateDexAssessment", mock.Anything, mock.Anything).Return(nil)

				return c
			},
		},
		{
			name: "success_cod",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEL,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockAccountRepo := new(mocks.AccountRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockTimeRepo := new(mocks.TimeRepository)
				mockReasonRepo := new(mocks.ReasonRepository)
				mockPodRepo := new(mocks.PodRepository)
				mockGatewaySttStatusUc := new(ucMocks.GatewaySttStatus)
				mockMessegeGatewayUc := new(ucMocks.MessageGateway)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockReasonVendorMapping := new(mocks.ReasonVendorMappingRepository)
				mockSalesforce := new(ucMocks.Salesforce)

				c := &jneCtx{
					repoAccount:         mockAccountRepo,
					deliveryRepo:        mockDeliveryRepo,
					repoCity:            mockCityRepo,
					podRepo:             mockPodRepo,
					timeRepo:            mockTimeRepo,
					reasonRepo:          mockReasonRepo,
					gatewaySttStatus:    mockGatewaySttStatusUc,
					messageGatewayUc:    mockMessegeGatewayUc,
					partnerLog:          mockPartnerLogRepo,
					reasonVendorMapping: mockReasonVendorMapping,
					salesforce:          mockSalesforce,
				}

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockDeliveryRepo.On("GetDetail", mock.Anything, &model.DeliveryViewParam{
					SttNo:                   "11LP1234512345",
					OrderBy:                 `delivery_id`,
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
					PartnerType:             model.AccountJNE.ActorName,
				}).Return(&model.DeliveryDetailResult{
					DeliveryID: 1,
					Stt: model.Stt{
						SttID:                1,
						SttNo:                "11LP1234512345",
						SttDestinationCityID: "JOG",
						SttLastStatusID:      model.DEL,
						SttIsCOD:             true,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockReasonVendorMapping.On("Select", mock.Anything, &model.ReasonVendorMappingViewParams{
					RvmVendor: model.JNE,
					RvmStatus: model.DEL,
				}).Return([]model.ReasonVendorMappingViewParams{
					{
						RvmID:          1,
						RvmVendor:      model.JNE,
						RvmReasonTitle: model.JNEDefaultDEXReason,
						RvmStatus:      model.DEL,
						RvmDesc:        "",
					},
				}, nil).Once()

				mockReasonRepo.On("GetDetail", mock.Anything, &model.ReasonViewParams{
					ReasonTitle: model.JNEDefaultDEXReason,
				}).Return(&model.ReasonDetailResult{}, nil).Once()

				mockPodRepo.On("Create", mock.Anything, &pod.CreatePodDexParams{
					Status:    model.DEX,
					SttID:     1,
					ActorID:   model.AccountJNE.ActorID,
					ActorName: model.AccountJNE.ActorName,
					ActorRole: model.VENDOR,
					Remarks: func() string {
						remarks := model.DeliveryRemarks{
							CreatedBy: model.AccountJNE.ActorName,
						}
						return remarks.ToString()
					}(),
					DeliveryID: 1,
					UpdatedAt: func() time.Time {
						ua, _ := shared.ParseUTC7(shared.FormatDateTime, now.Format(shared.FormatDateTime))
						return ua
					}(),
					Histories: []model.SttPieceHistory{
						{
							HistoryStatus:      model.DEX,
							HistoryLocation:    "JOG",
							HistoryActorID:     model.AccountJNE.ActorID,
							HistoryActorName:   model.AccountJNE.ActorName,
							HistoryActorRole:   model.VENDOR,
							HistoryCreatedBy:   int(model.AccountJNE.ActorID),
							HistoryCreatedAt:   nowUTC7,
							HistoryCreatedName: model.AccountJNE.ActorName,
							HistoryRemark: func() string {
								hr := model.RemarkPieceHistory{
									HistoryLocationName: "JOGJAKARTA",
									Attactments:         []string{""},
								}
								return hr.ToString()
							}(),
						},
					},
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("StatusSubmit", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo:       "11LP1234512345",
						Datetime:    nowUTC7.UTC(),
						StatusCode:  model.DEX,
						Location:    "JOG",
						Remarks:     `Paket gagal dikirim`,
						City:        "JOGJAKARTA",
						UpdatedBy:   model.JNEName,
						UpdatedOn:   nowUTC7.UTC(),
						Attachments: []string{""},
					},
					ServiceType: model.PACKAGESERVICE,
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("GoberDTPOLCommission", mock.Anything, mock.Anything).Return(nil).Once()

				mockMessegeGatewayUc.On("SendMessage", mock.Anything, &model.SendMessageRequest{
					PackageType: model.COD,
					EventStatus: model.DEX,
					Token:       token,
					IdDelivery:  1,
				}, &model.Stt{
					SttID:                1,
					SttNo:                "11LP1234512345",
					SttDestinationCityID: "JOG",
					SttLastStatusID:      model.DEL,
					SttIsCOD:             true,
				}).Return(nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDex,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.beforeFunc()

			err := c.setToDex(tt.args.ctx, tt.args.params, tt.args.sttDetail)
			if (err != nil) != tt.wantErr || !assert.Equal(t, err, tt.errResp) {
				t.Errorf("Test_jneCtx_setToDex; err = %v, ErrorResponse = %v\n", err, tt.errResp)
				return
			}
		})
	}
}

func Test_jneCtx_setToDel(t *testing.T) {
	type args struct {
		ctx       context.Context
		params    *jne.JneWebhookRequest
		sttDetail []model.SttDetailResult
	}

	ctx, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()
	token := "token"
	errorDetail := errors.New("error")
	now := time.Now()
	nowUTC7, _ := shared.ParseUTC7(shared.FormatDateTime, now.Format(shared.FormatDateTime))

	tests := []struct {
		name       string
		args       args
		wantErr    bool
		errResp    error
		beforeFunc func() *jneCtx
	}{
		{
			name: "error_panic",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					partnerLog: mockPartnerLogRepo,
				}

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDel,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "stt_last_status_can_not_be_updated_to_del",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEL,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("STT cannot be updated"),
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:   mockTimeRepo,
					partnerLog: mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDel,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("STT cannot be updated").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_get_token_genesis",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.STISC,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: shared.ErrUnexpected,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:    mockTimeRepo,
					repoAccount: mockAccountRepo,
					partnerLog:  mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDel,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: shared.ErrUnexpected.Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_get_city",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.STISC,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: shared.ErrUnexpected,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:    mockTimeRepo,
					repoAccount: mockAccountRepo,
					repoCity:    mockCityRepo,
					partnerLog:  mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDel,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: shared.ErrUnexpected.Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "city_not_found",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.STISC,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("City destination is not found"),
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:    mockTimeRepo,
					repoAccount: mockAccountRepo,
					repoCity:    mockCityRepo,
					partnerLog:  mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(nil, nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDel,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("City destination is not found").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_create",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.STISC,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: shared.ErrUnexpected,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:     mockTimeRepo,
					repoAccount:  mockAccountRepo,
					repoCity:     mockCityRepo,
					deliveryRepo: mockDeliveryRepo,
					partnerLog:   mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockDeliveryRepo.On("Create", mock.Anything, &delivery.CreateDeliveryParams{
					DeliveryParams: model.Delivery{
						DriverPhone: model.JNEPhone,
						DriverName:  model.JNEName,
						PartnerID:   model.AccountJNE.ActorID,
						PartnerType: model.AccountJNE.ActorName,
						SttNo:       "11LP1234512345",
						SttID:       1,
						Remarks: func() string {
							tempRemarks := model.DeliveryRemarks{
								CreatedBy: model.AccountJNE.ActorName,
							}
							return tempRemarks.ToString()
						}(),
						FinishedRole: model.DRIVER,
					},
					Histories: []model.SttPieceHistory{
						{
							HistoryStatus:    model.DEL,
							HistoryLocation:  "JOG",
							HistoryActorID:   model.AccountJNE.ActorID,
							HistoryActorName: model.AccountJNE.ActorName,
							HistoryActorRole: model.VENDOR,
							HistoryCreatedAt: nowUTC7,
							HistoryCreatedBy: model.AccountSystem.ActorID,
							HistoryRemark: func() string {
								tempRemarksPieceHistory := model.RemarkPieceHistory{
									HistoryLocationName: "JOGJAKARTA",
								}
								return tempRemarksPieceHistory.ToString()
							}(),
							HistoryCreatedName: model.AccountJNE.ActorName,
						},
					},
				}).Return(errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDel,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: shared.ErrUnexpected.Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_increment_delivery",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.STISC,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: shared.ErrUnexpected,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockSttRepo := new(mocks.SttRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:     mockTimeRepo,
					repoAccount:  mockAccountRepo,
					repoCity:     mockCityRepo,
					deliveryRepo: mockDeliveryRepo,
					sttRepo:      mockSttRepo,
					partnerLog:   mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockDeliveryRepo.On("Create", mock.Anything, &delivery.CreateDeliveryParams{
					DeliveryParams: model.Delivery{
						DriverPhone: model.JNEPhone,
						DriverName:  model.JNEName,
						PartnerID:   model.AccountJNE.ActorID,
						PartnerType: model.AccountJNE.ActorName,
						SttNo:       "11LP1234512345",
						SttID:       1,
						Remarks: func() string {
							tempRemarks := model.DeliveryRemarks{
								CreatedBy: model.AccountJNE.ActorName,
							}
							return tempRemarks.ToString()
						}(),
						FinishedRole: model.DRIVER,
					},
					Histories: []model.SttPieceHistory{
						{
							HistoryStatus:    model.DEL,
							HistoryLocation:  "JOG",
							HistoryActorID:   model.AccountJNE.ActorID,
							HistoryActorName: model.AccountJNE.ActorName,
							HistoryActorRole: model.VENDOR,
							HistoryCreatedAt: nowUTC7,
							HistoryCreatedBy: model.AccountSystem.ActorID,
							HistoryRemark: func() string {
								tempRemarksPieceHistory := model.RemarkPieceHistory{
									HistoryLocationName: "JOGJAKARTA",
								}
								return tempRemarksPieceHistory.ToString()
							}(),
							HistoryCreatedName: model.AccountJNE.ActorName,
						},
					},
				}).Return(nil).Once()

				mockSttRepo.On("IncrementDeliveryAttempt", mock.Anything, &model.Stt{
					SttID:              1,
					SttDeliveryAttempt: 0,
				}).Return(errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDel,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: shared.ErrUnexpected.Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "sucess_cod",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.STISC,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockSttRepo := new(mocks.SttRepository)
				mockGatewaySttStatusUc := new(ucMocks.GatewaySttStatus)
				mockMessegeGatewayUc := new(ucMocks.MessageGateway)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:         mockTimeRepo,
					repoAccount:      mockAccountRepo,
					repoCity:         mockCityRepo,
					deliveryRepo:     mockDeliveryRepo,
					sttRepo:          mockSttRepo,
					gatewaySttStatus: mockGatewaySttStatusUc,
					messageGatewayUc: mockMessegeGatewayUc,
					partnerLog:       mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockDeliveryRepo.On("Create", mock.Anything, &delivery.CreateDeliveryParams{
					DeliveryParams: model.Delivery{
						DriverPhone: model.JNEPhone,
						DriverName:  model.JNEName,
						PartnerID:   model.AccountJNE.ActorID,
						PartnerType: model.AccountJNE.ActorName,
						SttNo:       "11LP1234512345",
						SttID:       1,
						Remarks: func() string {
							tempRemarks := model.DeliveryRemarks{
								CreatedBy: model.AccountJNE.ActorName,
							}
							return tempRemarks.ToString()
						}(),
						FinishedRole: model.DRIVER,
					},
					Histories: []model.SttPieceHistory{
						{
							HistoryStatus:    model.DEL,
							HistoryLocation:  "JOG",
							HistoryActorID:   model.AccountJNE.ActorID,
							HistoryActorName: model.AccountJNE.ActorName,
							HistoryActorRole: model.VENDOR,
							HistoryCreatedAt: nowUTC7,
							HistoryCreatedBy: model.AccountSystem.ActorID,
							HistoryRemark: func() string {
								tempRemarksPieceHistory := model.RemarkPieceHistory{
									HistoryLocationName: "JOGJAKARTA",
								}
								return tempRemarksPieceHistory.ToString()
							}(),
							HistoryCreatedName: model.AccountJNE.ActorName,
						},
					},
				}).Return(nil).Once()

				mockSttRepo.On("IncrementDeliveryAttempt", mock.Anything, &model.Stt{
					SttID:              1,
					SttDeliveryAttempt: 0,
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("StatusSubmit", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo:      "11LP1234512345",
						Datetime:   nowUTC7.UTC(),
						StatusCode: model.DEL,
						Location:   "JOG",
						Remarks:    fmt.Sprintf(`Paket dikirim oleh %s`, model.JNEName),
						City:       "JOGJAKARTA",
						UpdatedBy:  model.JNEName,
						UpdatedOn:  nowUTC7.UTC(),
					},
					ServiceType: model.PACKAGESERVICE,
				}).Return(nil).Once()

				mockMessegeGatewayUc.On("SendMessage", mock.Anything, &model.SendMessageRequest{
					PackageType:       model.COD,
					EventStatus:       model.DEL,
					DriverName:        model.JNEName,
					DriverPhoneNumber: model.JNEPhone,
					Token:             token,
				}, &model.Stt{
					SttID:                1,
					SttNo:                "11LP1234512345",
					SttDestinationCityID: "JOG",
					SttLastStatusID:      model.STISC,
					SttIsCOD:             true,
					SttIsDO:              true,
				}).Return(nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDel,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "sucess_non_cod",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.STISC,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockDeliveryRepo := new(mocks.DeliveryRepository)
				mockSttRepo := new(mocks.SttRepository)
				mockGatewaySttStatusUc := new(ucMocks.GatewaySttStatus)
				mockMessegeGatewayUc := new(ucMocks.MessageGateway)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:         mockTimeRepo,
					repoAccount:      mockAccountRepo,
					repoCity:         mockCityRepo,
					deliveryRepo:     mockDeliveryRepo,
					sttRepo:          mockSttRepo,
					gatewaySttStatus: mockGatewaySttStatusUc,
					messageGatewayUc: mockMessegeGatewayUc,
					partnerLog:       mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockDeliveryRepo.On("Create", mock.Anything, &delivery.CreateDeliveryParams{
					DeliveryParams: model.Delivery{
						DriverPhone: model.JNEPhone,
						DriverName:  model.JNEName,
						PartnerID:   model.AccountJNE.ActorID,
						PartnerType: model.AccountJNE.ActorName,
						SttNo:       "11LP1234512345",
						SttID:       1,
						Remarks: func() string {
							tempRemarks := model.DeliveryRemarks{
								CreatedBy: model.AccountJNE.ActorName,
							}
							return tempRemarks.ToString()
						}(),
						FinishedRole: model.DRIVER,
					},
					Histories: []model.SttPieceHistory{
						{
							HistoryStatus:    model.DEL,
							HistoryLocation:  "JOG",
							HistoryActorID:   model.AccountJNE.ActorID,
							HistoryActorName: model.AccountJNE.ActorName,
							HistoryActorRole: model.VENDOR,
							HistoryCreatedAt: nowUTC7,
							HistoryCreatedBy: model.AccountSystem.ActorID,
							HistoryRemark: func() string {
								tempRemarksPieceHistory := model.RemarkPieceHistory{
									HistoryLocationName: "JOGJAKARTA",
								}
								return tempRemarksPieceHistory.ToString()
							}(),
							HistoryCreatedName: model.AccountJNE.ActorName,
						},
					},
				}).Return(nil).Once()

				mockSttRepo.On("IncrementDeliveryAttempt", mock.Anything, &model.Stt{
					SttID:              1,
					SttDeliveryAttempt: 0,
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("StatusSubmit", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo:      "11LP1234512345",
						Datetime:   nowUTC7.UTC(),
						StatusCode: model.DEL,
						Location:   "JOG",
						Remarks:    fmt.Sprintf(`Paket dikirim oleh %s`, model.JNEName),
						City:       "JOGJAKARTA",
						UpdatedBy:  model.JNEName,
						UpdatedOn:  nowUTC7.UTC(),
					},
					ServiceType: model.PACKAGESERVICE,
				}).Return(nil).Once()

				mockMessegeGatewayUc.On("SendMessage", mock.Anything, &model.SendMessageRequest{
					PackageType:       model.NonCOD,
					EventStatus:       model.DEL,
					DriverName:        model.JNEName,
					DriverPhoneNumber: model.JNEPhone,
					Token:             token,
				}, &model.Stt{
					SttID:                1,
					SttNo:                "11LP1234512345",
					SttDestinationCityID: "JOG",
					SttLastStatusID:      model.STISC,
				}).Return(nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEDel,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.beforeFunc()

			err := c.setToDel(tt.args.ctx, tt.args.params, tt.args.sttDetail)
			if (err != nil) != tt.wantErr || !assert.Equal(t, err, tt.errResp) {
				t.Errorf("Test_jneCtx_setToDel; err = %v, ErrorResponse = %v\n", err, tt.errResp)
				return
			}
		})
	}
}

func Test_jneCtx_setToStiDestSc(t *testing.T) {
	type args struct {
		ctx       context.Context
		params    *jne.JneWebhookRequest
		sttDetail []model.SttDetailResult
	}

	ctx, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()
	token := "token"
	errorDetail := errors.New("error")
	now := time.Now()
	nowUTC7, _ := shared.ParseUTC7(shared.FormatDateTime, now.Format(shared.FormatDateTime))

	logger.InitZap()

	tests := []struct {
		name       string
		args       args
		wantErr    bool
		errResp    error
		beforeFunc func() *jneCtx
	}{
		{
			name: "error_panic",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					partnerLog: mockPartnerLogRepo,
				}

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEStiDestSc,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "stt_last_status_can_not_be_updated_to_stidestsc",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.POS,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("This STT not allowed update to be STI DEST SC"),
			beforeFunc: func() *jneCtx {
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					partnerLog: mockPartnerLogRepo,
				}

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEStiDestSc,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("This STT not allowed update to be STI DEST SC").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_create_in_batches",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.STI,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("Failed update to STI DEST SC"),
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockSttHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:       mockTimeRepo,
					sttHistoryRepo: mockSttHistoryRepo,
					partnerLog:     mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockSttHistoryRepo.On("CreateInBatches", mock.Anything, []model.SttPieceHistory{
					{
						HistoryStatus:      model.STIDESTSC,
						HistoryLocation:    "JOG",
						HistoryActorID:     model.AccountJNE.ActorID,
						HistoryActorName:   model.AccountJNE.ActorName,
						HistoryActorRole:   model.VENDOR,
						HistoryCreatedAt:   nowUTC7,
						HistoryCreatedBy:   model.AccountSystem.ActorID,
						HistoryCreatedName: model.AccountJNE.ActorName,
						HistoryRemark: func() string {
							tempRemarksPieceHistory := model.RemarkPieceHistory{
								HistoryLocationName: "JOG",
							}

							return tempRemarksPieceHistory.ToString()
						}(),
					},
				}, 1).Return(errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEStiDestSc,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("Failed update to STI DEST SC").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_get_token_genesis",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.STI,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockSttHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:       mockTimeRepo,
					sttHistoryRepo: mockSttHistoryRepo,
					repoAccount:    mockAccountRepo,
					partnerLog:     mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockSttHistoryRepo.On("CreateInBatches", mock.Anything, []model.SttPieceHistory{
					{
						HistoryStatus:      model.STIDESTSC,
						HistoryLocation:    "JOG",
						HistoryActorID:     model.AccountJNE.ActorID,
						HistoryActorName:   model.AccountJNE.ActorName,
						HistoryActorRole:   model.VENDOR,
						HistoryCreatedAt:   nowUTC7,
						HistoryCreatedBy:   model.AccountSystem.ActorID,
						HistoryCreatedName: model.AccountJNE.ActorName,
						HistoryRemark: func() string {
							tempRemarksPieceHistory := model.RemarkPieceHistory{
								HistoryLocationName: "JOG",
							}

							return tempRemarksPieceHistory.ToString()
						}(),
					},
				}, 1).Return(nil).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEStiDestSc,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_get_city",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.STI,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockSttHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:       mockTimeRepo,
					sttHistoryRepo: mockSttHistoryRepo,
					repoAccount:    mockAccountRepo,
					repoCity:       mockCityRepo,
					partnerLog:     mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockSttHistoryRepo.On("CreateInBatches", mock.Anything, []model.SttPieceHistory{
					{
						HistoryStatus:      model.STIDESTSC,
						HistoryLocation:    "JOG",
						HistoryActorID:     model.AccountJNE.ActorID,
						HistoryActorName:   model.AccountJNE.ActorName,
						HistoryActorRole:   model.VENDOR,
						HistoryCreatedAt:   nowUTC7,
						HistoryCreatedBy:   model.AccountSystem.ActorID,
						HistoryCreatedName: model.AccountJNE.ActorName,
						HistoryRemark: func() string {
							tempRemarksPieceHistory := model.RemarkPieceHistory{
								HistoryLocationName: "JOG",
							}

							return tempRemarksPieceHistory.ToString()
						}(),
					},
				}, 1).Return(nil).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEStiDestSc,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "success",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.STI,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockSttHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockGatewaySttStatusUc := new(ucMocks.GatewaySttStatus)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:         mockTimeRepo,
					sttHistoryRepo:   mockSttHistoryRepo,
					repoAccount:      mockAccountRepo,
					repoCity:         mockCityRepo,
					gatewaySttStatus: mockGatewaySttStatusUc,
					partnerLog:       mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockSttHistoryRepo.On("CreateInBatches", mock.Anything, []model.SttPieceHistory{
					{
						HistoryStatus:      model.STIDESTSC,
						HistoryLocation:    "JOG",
						HistoryActorID:     model.AccountJNE.ActorID,
						HistoryActorName:   model.AccountJNE.ActorName,
						HistoryActorRole:   model.VENDOR,
						HistoryCreatedAt:   nowUTC7,
						HistoryCreatedBy:   model.AccountSystem.ActorID,
						HistoryCreatedName: model.AccountJNE.ActorName,
						HistoryRemark: func() string {
							tempRemarksPieceHistory := model.RemarkPieceHistory{
								HistoryLocationName: "JOG",
							}

							return tempRemarksPieceHistory.ToString()
						}(),
					},
				}, 1).Return(nil).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockGatewaySttStatusUc.On("StatusSubmit", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo:      "11LP1234512345",
						Datetime:   nowUTC7.UTC(),
						StatusCode: model.STIDESTSC,
						Location:   "JOG",
						Remarks:    fmt.Sprintf(`Station transit in subconsolidator destination updated by %s`, model.JNEName),
						City:       "JOGJAKARTA",
						UpdatedBy:  model.JNEName,
						UpdatedOn:  nowUTC7.UTC(),
					},
					ServiceType: model.PACKAGESERVICE,
				}).Return(nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEStiDestSc,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.beforeFunc()

			err := c.setToStiDestSc(tt.args.ctx, tt.args.params, tt.args.sttDetail)
			if (err != nil) != tt.wantErr || !assert.Equal(t, err, tt.errResp) {
				t.Errorf("Test_jneCtx_setToStiDestSc; err = %v, ErrorResponse = %v\n", err, tt.errResp)
				return
			}
		})
	}
}

func Test_jneCtx_setToRTS(t *testing.T) {
	type args struct {
		ctx       context.Context
		params    *jne.JneWebhookRequest
		sttDetail []model.SttDetailResult
	}

	ctx, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()
	token := "token"
	errorDetail := errors.New("error")
	now := time.Now()
	nowUTC7, _ := shared.ParseUTC7(shared.FormatDateTime, now.Format(shared.FormatDateTime))

	logger.InitZap()

	tests := []struct {
		name       string
		args       args
		wantErr    bool
		errResp    error
		beforeFunc func() *jneCtx
	}{
		{
			name: "error_panic",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					partnerLog: mockPartnerLogRepo,
				}

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNERts,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "can_not_update_to_rts",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.RTS,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("STT cannot be updated"),
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:   mockTimeRepo,
					partnerLog: mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNERts,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("STT cannot be updated").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_get_token_genesis",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEX,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: shared.ErrUnexpected,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:    mockTimeRepo,
					repoAccount: mockAccountRepo,
					partnerLog:  mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNERts,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: shared.ErrUnexpected.Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_get_city",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEX,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: shared.ErrUnexpected,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:    mockTimeRepo,
					repoAccount: mockAccountRepo,
					repoCity:    mockCityRepo,
					partnerLog:  mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNERts,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: shared.ErrUnexpected.Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "city_not_found",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEX,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("City destination is not found"),
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:    mockTimeRepo,
					repoAccount: mockAccountRepo,
					repoCity:    mockCityRepo,
					partnerLog:  mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(nil, nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNERts,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("City destination is not found").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_create_history",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEX,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: shared.ErrUnexpected,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockSttHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:       mockTimeRepo,
					repoAccount:    mockAccountRepo,
					repoCity:       mockCityRepo,
					sttHistoryRepo: mockSttHistoryRepo,
					partnerLog:     mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockSttHistoryRepo.On("CreateInBatches", mock.Anything, []model.SttPieceHistory{
					{
						HistoryStatus:      model.RTS,
						HistoryLocation:    "JOG",
						HistoryActorID:     model.AccountJNE.ActorID,
						HistoryActorName:   model.AccountJNE.ActorName,
						HistoryActorRole:   model.VENDOR,
						HistoryCreatedAt:   nowUTC7,
						HistoryCreatedBy:   model.AccountSystem.ActorID,
						HistoryCreatedName: model.AccountJNE.ActorName,
					},
				}, 1).Return(errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNERts,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: shared.ErrUnexpected.Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "success",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEX,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockSttHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockGatewaySttStatusUc := new(ucMocks.GatewaySttStatus)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockSttDueRepo := new(mocks.SttDueRepository)

				c := &jneCtx{
					timeRepo:         mockTimeRepo,
					repoAccount:      mockAccountRepo,
					repoCity:         mockCityRepo,
					sttHistoryRepo:   mockSttHistoryRepo,
					gatewaySttStatus: mockGatewaySttStatusUc,
					partnerLog:       mockPartnerLogRepo,
					sttDueRepo:       mockSttDueRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockSttHistoryRepo.On("CreateInBatches", mock.Anything, []model.SttPieceHistory{
					{
						HistoryStatus:      model.RTS,
						HistoryLocation:    "JOG",
						HistoryActorID:     model.AccountJNE.ActorID,
						HistoryActorName:   model.AccountJNE.ActorName,
						HistoryActorRole:   model.VENDOR,
						HistoryCreatedAt:   nowUTC7,
						HistoryCreatedBy:   model.AccountSystem.ActorID,
						HistoryCreatedName: model.AccountJNE.ActorName,
					},
				}, 1).Return(nil).Once()

				mockGatewaySttStatusUc.On("StatusSubmit", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo:      "11LP1234512345",
						Datetime:   nowUTC7.UTC(),
						StatusCode: model.RTS,
						Location:   "JOG",
						Remarks:    fmt.Sprintf(`Paket diupdate oleh %s`, model.JNEName),
						City:       "JOGJAKARTA",
						UpdatedBy:  model.JNEName,
						UpdatedOn:  nowUTC7.UTC(),
					},
					ServiceType: model.PACKAGESERVICE,
				}).Return(nil).Once()

				mockSttDueRepo.On("UpdateIsShowBulk", mock.Anything, &model.STTDueUpdateIsShow{STTNos: []string{"11LP1234512345"}}).Return(nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNERts,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.beforeFunc()

			err := c.setToRTS(tt.args.ctx, tt.args.params, tt.args.sttDetail)
			if (err != nil) != tt.wantErr || !assert.Equal(t, err, tt.errResp) {
				t.Errorf("Test_jneCtx_setToRTS; err = %v, ErrorResponse = %v\n", err, tt.errResp)
				return
			}
		})
	}
}

func Test_jneCtx_setToHal(t *testing.T) {
	type args struct {
		ctx       context.Context
		params    *jne.JneWebhookRequest
		sttDetail []model.SttDetailResult
	}

	ctx, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()
	token := "token"
	errorDetail := errors.New("error")
	now := time.Now()
	nowUTC7, _ := shared.ParseUTC7(shared.FormatDateTime, now.Format(shared.FormatDateTime))

	logger.InitZap()

	tests := []struct {
		name       string
		args       args
		wantErr    bool
		errResp    error
		beforeFunc func() *jneCtx
	}{
		{
			name: "error_panic",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					partnerLog: mockPartnerLogRepo,
				}

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEHal,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "laststatus_can_not_update_to_hal",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.POD,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("Stt Status not allowed"),
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:   mockTimeRepo,
					partnerLog: mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEHal,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("Stt Status not allowed").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "laststatus_trucking_can_not_update_to_hal",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID:   "order_id",
					SttStatus: model.SCRAP,
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.PICKUPTRUCKING,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("Stt Status not allowed"),
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:   mockTimeRepo,
					partnerLog: mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEHal,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID:   "order_id",
						SttStatus: model.SCRAP,
					},
					Response: errors.New("Stt Status not allowed").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_get_token_genesis",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEX,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: shared.ErrUnexpected,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:    mockTimeRepo,
					repoAccount: mockAccountRepo,
					partnerLog:  mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEHal,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: shared.ErrUnexpected.Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_get_city_or_not_found",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEX,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("City destination is not found"),
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:    mockTimeRepo,
					repoAccount: mockAccountRepo,
					repoCity:    mockCityRepo,
					partnerLog:  mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEHal,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("City destination is not found").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "error_create_custome_process",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID: "order_id",
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEX,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: true,
			errResp: errors.New("An error occurred while create Custom Process"),
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCustomeProcessRepo := new(mocks.CustomProcessRepository)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)

				c := &jneCtx{
					timeRepo:          mockTimeRepo,
					repoAccount:       mockAccountRepo,
					repoCity:          mockCityRepo,
					customProcessRepo: mockCustomeProcessRepo,
					partnerLog:        mockPartnerLogRepo,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code: "JOG",
					Name: "JOGJAKARTA",
				}, nil).Once()

				mockCustomeProcessRepo.On("Create", mock.Anything, &customProcess.InsertCustomProcessData{
					CustomProcess: &model.CustomProcess{
						CustomProcessTotalSTT:     1,
						CustomProcessTotalPiece:   1,
						CustomProcessLatestStatus: model.HAL,
						CustomProcessCreatedAt:    nowUTC7,
						CustomProcessCreatedBy:    model.AccountJNE.ActorID,
						CustomProcessCreatedName:  model.AccountJNE.ActorName,
						CustomProcessUpdatedAt:    nowUTC7,
						CustomProcessUpdatedName:  model.AccountJNE.ActorName,
						CustomProcessPartnerID:    model.AccountJNE.ActorID,
						CustomProcessPartnerCode:  model.AccountJNE.ActorCode,
						CustomProcessPartnerName:  model.AccountJNE.ActorName,
					},
					SttHistories: []customProcess.SttHistory{
						{
							SttHistory: model.SttPieceHistory{
								HistoryStatus:      model.HAL,
								HistoryLocation:    "JOG",
								HistoryActorID:     model.AccountJNE.ActorID,
								HistoryActorName:   model.AccountJNE.ActorName,
								HistoryActorRole:   model.VENDOR,
								HistoryCreatedAt:   nowUTC7,
								HistoryCreatedBy:   model.AccountJNE.ActorID,
								HistoryCreatedName: model.AccountJNE.ActorName,
							},
							SttID: 1,
						},
					},
				}).Return(nil, errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEHal,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID: "order_id",
					},
					Response: errors.New("An error occurred while create Custom Process").Error(),
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "success_with_stt_elexys_and_params_stt_status_hal",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID:   "order_id",
					SttStatus: model.HAL,
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttElexysNo:          dbr.NewNullString("stt_elexys_no"),
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEX,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCustomeProcessRepo := new(mocks.CustomProcessRepository)
				mockSttActivityUc := new(ucMocks.SttActivity)
				mockGatewaySttStatusUc := new(ucMocks.GatewaySttStatus)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockRequestPriorityDelivery := new(ucmock.RequestPriorityDelivery)

				c := &jneCtx{
					timeRepo:                mockTimeRepo,
					repoAccount:             mockAccountRepo,
					repoCity:                mockCityRepo,
					customProcessRepo:       mockCustomeProcessRepo,
					sttActivityUc:           mockSttActivityUc,
					gatewaySttStatus:        mockGatewaySttStatusUc,
					partnerLog:              mockPartnerLogRepo,
					requestPriorityDelivery: mockRequestPriorityDelivery,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code:     "JOG",
					Name:     "JOGJAKARTA",
					Timezone: shared.AsiaJakarta,
				}, nil).Once()

				mockCustomeProcessRepo.On("Create", mock.Anything, &customProcess.InsertCustomProcessData{
					CustomProcess: &model.CustomProcess{
						CustomProcessTotalSTT:     1,
						CustomProcessTotalPiece:   1,
						CustomProcessLatestStatus: model.HAL,
						CustomProcessCreatedAt:    nowUTC7,
						CustomProcessCreatedBy:    model.AccountJNE.ActorID,
						CustomProcessCreatedName:  model.AccountJNE.ActorName,
						CustomProcessUpdatedAt:    nowUTC7,
						CustomProcessUpdatedName:  model.AccountJNE.ActorName,
						CustomProcessPartnerID:    model.AccountJNE.ActorID,
						CustomProcessPartnerCode:  model.AccountJNE.ActorCode,
						CustomProcessPartnerName:  model.AccountJNE.ActorName,
					},
					SttHistories: []customProcess.SttHistory{
						{
							SttHistory: model.SttPieceHistory{
								HistoryStatus:      model.HAL,
								HistoryLocation:    "JOG",
								HistoryActorID:     model.AccountJNE.ActorID,
								HistoryActorName:   model.AccountJNE.ActorName,
								HistoryActorRole:   model.VENDOR,
								HistoryCreatedAt:   nowUTC7,
								HistoryCreatedBy:   model.AccountJNE.ActorID,
								HistoryCreatedName: model.AccountJNE.ActorName,
							},
							SttID: 1,
						},
					},
				}).Return(&customProcess.InsertCustomProcessResponse{
					CustomProcess: &model.CustomProcess{},
				}, nil).Once()

				mockSttActivityUc.On("UpdateSttTime", mock.Anything, &stt_activity.SttActivityRequest{
					ListSttData: []stt_activity.SttActivityRequestDetail{
						{
							SttNo:         "11LP1234512345",
							SttStatus:     model.HAL,
							SttStatusTime: nowUTC7,
						},
					},
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("StatusSubmit", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo:      "stt_elexys_no",
						Datetime:   nowUTC7.UTC(),
						StatusCode: model.HAL,
						Location:   "JOG",
						Remarks:    fmt.Sprintf(`Paket diupdate oleh %s`, model.AccountJNE.ActorName),
						City:       "JOGJAKARTA",
						UpdatedBy:  model.AccountJNE.ActorName,
						UpdatedOn:  nowUTC7.UTC(),
					},
					ServiceType: model.PACKAGESERVICE,
				}).Return(nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code:     "JOG",
					Name:     "JOGJAKARTA",
					Timezone: shared.AsiaJakarta,
				}, nil).Once()

				mockGatewaySttStatusUc.On("GoberDTPOLCommission", mock.Anything, &model.GoberDTPOLCommission{
					SttNo:         "11LP1234512345",
					SttLastStatus: model.HAL,
				}).Return(nil).Once()

				mockRequestPriorityDelivery.On("UpdateIsShowToZero", mock.Anything, "11LP1234512345").Return(nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEHal,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID:   "order_id",
						SttStatus: model.HAL,
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "success_without_stt_elexys_and_params_stt_status_halcd",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID:   "order_id",
					SttStatus: model.HALCD,
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.DEX,
							SttIsCOD:             true,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCustomeProcessRepo := new(mocks.CustomProcessRepository)
				mockSttActivityUc := new(ucMocks.SttActivity)
				mockGatewaySttStatusUc := new(ucMocks.GatewaySttStatus)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockRequestPriorityDelivery := new(ucmock.RequestPriorityDelivery)

				c := &jneCtx{
					timeRepo:                mockTimeRepo,
					repoAccount:             mockAccountRepo,
					repoCity:                mockCityRepo,
					customProcessRepo:       mockCustomeProcessRepo,
					sttActivityUc:           mockSttActivityUc,
					gatewaySttStatus:        mockGatewaySttStatusUc,
					partnerLog:              mockPartnerLogRepo,
					requestPriorityDelivery: mockRequestPriorityDelivery,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code:     "JOG",
					Name:     "JOGJAKARTA",
					Timezone: shared.AsiaJakarta,
				}, nil).Once()

				mockCustomeProcessRepo.On("Create", mock.Anything, &customProcess.InsertCustomProcessData{
					CustomProcess: &model.CustomProcess{
						CustomProcessTotalSTT:     1,
						CustomProcessTotalPiece:   1,
						CustomProcessLatestStatus: model.HAL,
						CustomProcessCreatedAt:    nowUTC7,
						CustomProcessCreatedBy:    model.AccountJNE.ActorID,
						CustomProcessCreatedName:  model.AccountJNE.ActorName,
						CustomProcessUpdatedAt:    nowUTC7,
						CustomProcessUpdatedName:  model.AccountJNE.ActorName,
						CustomProcessPartnerID:    model.AccountJNE.ActorID,
						CustomProcessPartnerCode:  model.AccountJNE.ActorCode,
						CustomProcessPartnerName:  model.AccountJNE.ActorName,
					},
					SttHistories: []customProcess.SttHistory{
						{
							SttHistory: model.SttPieceHistory{
								HistoryStatus:      model.HAL,
								HistoryLocation:    "JOG",
								HistoryActorID:     model.AccountJNE.ActorID,
								HistoryActorName:   model.AccountJNE.ActorName,
								HistoryActorRole:   model.VENDOR,
								HistoryCreatedAt:   nowUTC7,
								HistoryCreatedBy:   model.AccountJNE.ActorID,
								HistoryCreatedName: model.AccountJNE.ActorName,
							},
							SttID: 1,
						},
					},
				}).Return(&customProcess.InsertCustomProcessResponse{
					CustomProcess: &model.CustomProcess{},
				}, nil).Once()

				mockSttActivityUc.On("UpdateSttTime", mock.Anything, &stt_activity.SttActivityRequest{
					ListSttData: []stt_activity.SttActivityRequestDetail{
						{
							SttNo:         "11LP1234512345",
							SttStatus:     model.HALCD,
							SttStatusTime: nowUTC7,
						},
					},
				}).Return(nil).Once()

				mockGatewaySttStatusUc.On("StatusSubmit", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo:      "11LP1234512345",
						Datetime:   nowUTC7.UTC(),
						StatusCode: model.HALCD,
						Location:   "JOG",
						Remarks:    fmt.Sprintf(`Paket diupdate oleh %s`, model.AccountJNE.ActorName),
						City:       "JOGJAKARTA",
						UpdatedBy:  model.AccountJNE.ActorName,
						UpdatedOn:  nowUTC7.UTC(),
					},
					ServiceType: model.PACKAGESERVICE,
				}).Return(nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEHal,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID:   "order_id",
						SttStatus: model.HALCD,
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				mockRequestPriorityDelivery.On("UpdateIsShowToZero", mock.Anything, "11LP1234512345").Return(nil).Once()

				return c
			},
		},
		{
			name: "error_sumbit_data_to_middleware_with_stt_elexys_and_params_stt_status_dex_for_client",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID:   "order_id",
					SttStatus: model.DEX,
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttElexysNo:          dbr.NewNullString("stt_elexys_no"),
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.CNXCD,
							SttIsCOD:             true,
							SttBookedForType:     model.CLIENT,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCustomeProcessRepo := new(mocks.CustomProcessRepository)
				mockSttActivityUc := new(ucMocks.SttActivity)
				mockGatewaySttStatusUc := new(ucMocks.GatewaySttStatus)
				mockMiddlewareRepo := new(mocks.MiddlewareCLient)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockRequestPriorityDelivery := new(ucmock.RequestPriorityDelivery)

				c := &jneCtx{
					timeRepo:                mockTimeRepo,
					repoAccount:             mockAccountRepo,
					repoCity:                mockCityRepo,
					customProcessRepo:       mockCustomeProcessRepo,
					sttActivityUc:           mockSttActivityUc,
					gatewaySttStatus:        mockGatewaySttStatusUc,
					middlewareRepo:          mockMiddlewareRepo,
					partnerLog:              mockPartnerLogRepo,
					requestPriorityDelivery: mockRequestPriorityDelivery,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code:     "JOG",
					Name:     "JOGJAKARTA",
					Timezone: shared.AsiaJakarta,
				}, nil).Once()

				mockCustomeProcessRepo.On("Create", mock.Anything, &customProcess.InsertCustomProcessData{
					CustomProcess: &model.CustomProcess{
						CustomProcessTotalSTT:     1,
						CustomProcessTotalPiece:   1,
						CustomProcessLatestStatus: model.HAL,
						CustomProcessCreatedAt:    nowUTC7,
						CustomProcessCreatedBy:    model.AccountJNE.ActorID,
						CustomProcessCreatedName:  model.AccountJNE.ActorName,
						CustomProcessUpdatedAt:    nowUTC7,
						CustomProcessUpdatedName:  model.AccountJNE.ActorName,
						CustomProcessPartnerID:    model.AccountJNE.ActorID,
						CustomProcessPartnerCode:  model.AccountJNE.ActorCode,
						CustomProcessPartnerName:  model.AccountJNE.ActorName,
					},
					SttHistories: []customProcess.SttHistory{
						{
							SttHistory: model.SttPieceHistory{
								HistoryStatus:      model.HAL,
								HistoryLocation:    "JOG",
								HistoryActorID:     model.AccountJNE.ActorID,
								HistoryActorName:   model.AccountJNE.ActorName,
								HistoryActorRole:   model.VENDOR,
								HistoryCreatedAt:   nowUTC7,
								HistoryCreatedBy:   model.AccountJNE.ActorID,
								HistoryCreatedName: model.AccountJNE.ActorName,
							},
							SttID: 1,
						},
					},
				}).Return(&customProcess.InsertCustomProcessResponse{
					CustomProcess: &model.CustomProcess{},
				}, nil).Once()

				mockSttActivityUc.On("UpdateSttTime", mock.Anything, &stt_activity.SttActivityRequest{
					ListSttData: []stt_activity.SttActivityRequestDetail{
						{
							SttNo:         "11LP1234512345",
							SttStatus:     model.DEX,
							SttStatusTime: nowUTC7,
						},
					},
				}).Return(nil).Once()

				mockMiddlewareRepo.On("SubmitDataToMiddleware", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttID:      1,
						SttNo:      "stt_elexys_no",
						Datetime:   nowUTC7.UTC(),
						StatusCode: model.DEX,
						Location:   "JOG",
						Remarks:    fmt.Sprintf(`Paket diupdate oleh %s`, model.AccountJNE.ActorName),
						City:       "JOGJAKARTA",
						UpdatedBy:  model.AccountJNE.ActorName,
						UpdatedOn:  nowUTC7.UTC(),
					},
					ServiceType: model.PACKAGESERVICE,
				}).Return(errorDetail).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEHal,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID:   "order_id",
						SttStatus: model.DEX,
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				mockRequestPriorityDelivery.On("UpdateIsShowToZero", mock.Anything, "11LP1234512345").Return(nil).Once()

				return c
			},
		},
		{
			name: "success_without_stt_elexys_and_params_stt_status_dex_for_client",
			args: args{
				ctx: ctx,
				params: &jne.JneWebhookRequest{
					OrderID:   "order_id",
					SttStatus: model.DEX,
				},
				sttDetail: []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttID:                1,
							SttNo:                "11LP1234512345",
							SttDestinationCityID: "JOG",
							SttLastStatusID:      model.CNXCD,
							SttIsCOD:             true,
							SttBookedForType:     model.CLIENT,
						},
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *jneCtx {
				mockTimeRepo := new(mocks.TimeRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCustomeProcessRepo := new(mocks.CustomProcessRepository)
				mockSttActivityUc := new(ucMocks.SttActivity)
				mockGatewaySttStatusUc := new(ucMocks.GatewaySttStatus)
				mockMiddlewareRepo := new(mocks.MiddlewareCLient)
				mockPartnerLogRepo := new(mocks.PartnerLogRepository)
				mockRequestPriorityDelivery := new(ucmock.RequestPriorityDelivery)

				c := &jneCtx{
					timeRepo:                mockTimeRepo,
					repoAccount:             mockAccountRepo,
					repoCity:                mockCityRepo,
					customProcessRepo:       mockCustomeProcessRepo,
					sttActivityUc:           mockSttActivityUc,
					gatewaySttStatus:        mockGatewaySttStatusUc,
					middlewareRepo:          mockMiddlewareRepo,
					partnerLog:              mockPartnerLogRepo,
					requestPriorityDelivery: mockRequestPriorityDelivery,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockAccountRepo.On("GetTokenGenesis", mock.Anything).Return(&model.AccountTokenResponse{
					Data: model.TokenGenesis{
						Token: token,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "JOG", token).Return(&model.City{
					Code:     "JOG",
					Name:     "JOGJAKARTA",
					Timezone: shared.AsiaJakarta,
				}, nil).Once()

				mockCustomeProcessRepo.On("Create", mock.Anything, &customProcess.InsertCustomProcessData{
					CustomProcess: &model.CustomProcess{
						CustomProcessTotalSTT:     1,
						CustomProcessTotalPiece:   1,
						CustomProcessLatestStatus: model.HAL,
						CustomProcessCreatedAt:    nowUTC7,
						CustomProcessCreatedBy:    model.AccountJNE.ActorID,
						CustomProcessCreatedName:  model.AccountJNE.ActorName,
						CustomProcessUpdatedAt:    nowUTC7,
						CustomProcessUpdatedName:  model.AccountJNE.ActorName,
						CustomProcessPartnerID:    model.AccountJNE.ActorID,
						CustomProcessPartnerCode:  model.AccountJNE.ActorCode,
						CustomProcessPartnerName:  model.AccountJNE.ActorName,
					},
					SttHistories: []customProcess.SttHistory{
						{
							SttHistory: model.SttPieceHistory{
								HistoryStatus:      model.HAL,
								HistoryLocation:    "JOG",
								HistoryActorID:     model.AccountJNE.ActorID,
								HistoryActorName:   model.AccountJNE.ActorName,
								HistoryActorRole:   model.VENDOR,
								HistoryCreatedAt:   nowUTC7,
								HistoryCreatedBy:   model.AccountJNE.ActorID,
								HistoryCreatedName: model.AccountJNE.ActorName,
							},
							SttID: 1,
						},
					},
				}).Return(&customProcess.InsertCustomProcessResponse{
					CustomProcess: &model.CustomProcess{},
				}, nil).Once()

				mockSttActivityUc.On("UpdateSttTime", mock.Anything, &stt_activity.SttActivityRequest{
					ListSttData: []stt_activity.SttActivityRequestDetail{
						{
							SttNo:         "11LP1234512345",
							SttStatus:     model.DEX,
							SttStatusTime: nowUTC7,
						},
					},
				}).Return(nil).Once()

				mockMiddlewareRepo.On("SubmitDataToMiddleware", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttID:      1,
						SttNo:      "11LP1234512345",
						Datetime:   nowUTC7.UTC(),
						StatusCode: model.DEX,
						Location:   "JOG",
						Remarks:    fmt.Sprintf(`Paket diupdate oleh %s`, model.AccountJNE.ActorName),
						City:       "JOGJAKARTA",
						UpdatedBy:  model.AccountJNE.ActorName,
						UpdatedOn:  nowUTC7.UTC(),
					},
					ServiceType: model.PACKAGESERVICE,
				}).Return(nil).Once()

				mockPartnerLogRepo.On("Insert", mock.Anything, &model.PartnerLog{
					Action: model.PLSttJNEHal,
					RefID:  "order_id",
					Request: &jne.JneWebhookRequest{
						OrderID:   "order_id",
						SttStatus: model.DEX,
					},
					Response: jne.JneWebhookResponse{Success: true},
				}).Return(nil).Once()

				mockRequestPriorityDelivery.On("UpdateIsShowToZero", mock.Anything, "11LP1234512345").Return(nil).Once()

				return c
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.beforeFunc()

			err := c.setToHal(tt.args.ctx, tt.args.params, tt.args.sttDetail)
			if (err != nil) != tt.wantErr || !assert.Equal(t, err, tt.errResp) {
				t.Errorf("Test_jneCtx_setToHal; err = %v, ErrorResponse = %v\n", err, tt.errResp)
				return
			}
		})
	}
}
