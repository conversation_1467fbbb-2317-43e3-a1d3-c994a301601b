package usecase

import (
	"context"
	"fmt"
	"os"
	"testing"

	"github.com/Lionparcel/go-lptool/v2/lputils"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/repository/mocks"
	"github.com/Lionparcel/hydra/src/usecase/stt"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

func Test_sttCtx_DetailStt(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *stt.SttDetailRequest
	}
	ctxBg, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()
	tests := []struct {
		name       string
		args       args
		want       *stt.SttDetailResponse
		wantErr    bool
		errResp    error
		beforeFunc func() *sttCtx
	}{
		{
			name: "TestDetailStt-GetDetailSttWithSttReverseShipmentIDC1",
			args: args{
				ctx: ctxBg,
				params: &stt.SttDetailRequest{
					SttID:          1,
					AccountType:    "partner",
					AccountRefType: "pos",
					AccountRefID:   10,
					Token:          "token",
				},
			},
			want: &stt.SttDetailResponse{
				SttID:                      int64(1),
				SttNo:                      "77LP123",
				SttOriginAddress:           "Jakarta, Jakarta",
				SttOriginDistrictID:        "CGK",
				SttOriginDistrictName:      "Jakarta",
				SttOriginCityID:            "CGK",
				SttOriginCityName:          "Jakarta",
				SttDestinationAddress:      "Medan, Medan",
				SttDestinationCityID:       "KNO",
				SttDestinationCityName:     "Medan",
				SttDestinationDistrictID:   "KNO",
				SttDestinationDistrictName: "Medan",
				SttCommodityID:             1,
				SttCommodityName:           "TEST",
				SttCommodityCode:           "TEST",
				SttInsuranceName:           "insurance",
				SttInsuranceRate:           1,
				SttIsCOD:                   false,
				SttPieces: []stt.SttPieces{
					{
						SttID:                int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				},
				SttLastStatus:            "POD",
				SttLastStatusDescription: "Paket sudah diterima.",
				SttIsAllowEdit:           false,
				SttRegionID:              "KNO",
				SttRegionName:            "Medan",
				SttCOD:                   "No",
				SttReturnDO:              "No",
				SttBookedById:            10,
				SttBookedByType:          "pos",
				CheckoutPaymentMethod:    "cod",
				LastStatusSttReturn:      model.RTS,
				DetailSttReverseJourney: &stt.DetailSttReverseJourney{
					RootReverseSttNo:         `88LP1691461672142`,
					RootReverseLastStatusStt: `TRANSIT`,
					CodHandling:              "standardcod",
					RootReverseShipmentID:    "C1RVVABC",
				},
				SttAttachFiles:       []string{},
				ListDiscount:         []model.PromoDiscount{},
				SttBookedByCountry:   model.CountryID,
				SttWeightAttachFiles: []string{},
				GoodsNames:           []string{},
				SttCipl:              []model.SttCIPL{},
				SttFtzCipl:           []model.SttCIPL{},
				SttFtzAttachFiles:    []string{},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *sttCtx {
				mockSttRepo := new(mocks.SttRepository)
				mockSttPiecesRepo := new(mocks.SttPiecesRepository)
				mockSttOptionalRateRepo := new(mocks.SttOptionalRateRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCommodityRepo := new(mocks.CommodityRepository)
				mockCheckoutRepo := new(mocks.CheckoutRepository)
				mockShipmentRepo := new(mocks.ShipmentRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockSttCustomFlagRepo := new(mocks.STTCustomFlagRepository)

				c := &sttCtx{
					sttRepo:             mockSttRepo,
					sttPiecesRepo:       mockSttPiecesRepo,
					sttOptionalRateRepo: mockSttOptionalRateRepo,
					commodityRepo:       mockCommodityRepo,
					districtRepo:        mockDistrictRepo,
					cityRepo:            mockCityRepo,
					checkoutRepo:        mockCheckoutRepo,
					shipmentRepo:        mockShipmentRepo,
					accountRepo:         mockAccountRepo,
					sttCustomFlagRepo:   mockSttCustomFlagRepo,
				}

				existingConfig := os.Getenv("LIMITED_ASSIGNED_3LC_RULE")
				defer func() {
					os.Setenv("LIMITED_ASSIGNED_3LC_RULE", existingConfig)
				}()
				os.Setenv("LIMITED_ASSIGNED_3LC_RULE", "[{\"type\":\"internal\",\"role\":\"Data Entry\"}]")

				mockAccountRepo.On("GetProfileWithParams", mock.Anything, &model.GetProfileRequest{Token: "token", NoGetTieringPos: true}).
					Return(&model.AccountDetail{
						Data: model.AccountDetailBase{
							AccountID:   123,
							RoleType:    "Data Entry",
							AccountType: "internal",
							Meta: map[string]interface{}{
								"assigned_3lc": "ALL",
							},
						},
					}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{SttID: int64(1)},
				}).Return(&model.Stt{
					SttID:                    int64(1),
					SttNo:                    "77LP123",
					SttBookedBy:              10,
					SttIsCOD:                 false,
					SttBookedByType:          "pos",
					SttCommodityCode:         "TEST",
					SttOriginDistrictID:      "CGK",
					SttDestinationDistrictID: "KNO",
					SttDestinationCityID:     "KNO",
					SttLastStatusID:          model.POD,
					SttMeta:                  `{"client_payment_method":"split_bill","client_cod_config_amount":"","detail_stt_reverse_journey": { "reverse_stt_no": "88LP1691461672142", "reverse_stt_shipment_id": "C1RVVABC", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "88LP1691461672142", "root_reverse_stt_shipment_id": "C1RVVABC", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "6282336440033", "root_sender_address": "kedoyaaa" }}`,
				}, nil).Once()

				mockSttPiecesRepo.On("SelectDetail", mock.Anything, mock.Anything).
					Return([]model.SttDetailResult{}, nil).Once()

				mockSttPiecesRepo.On("Select", mock.Anything, &model.SttPiecesViewParam{
					SttID: 1,
				}).Return([]model.SttPiece{
					{
						SttPieceSttID:        int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				}, nil).Once()

				mockSttOptionalRateRepo.On("Select", mock.Anything, &model.SttOptionalRate{
					SttOptionalRateSttID: int64(1),
				}).Return([]model.SttOptionalRate{
					{
						SttOptionalRateParams: "insurance",
						SttOptionalRateName:   "insurance",
						SttOptionalRateRate:   1,
					},
				}, nil).Once()

				mockCommodityRepo.On("GetCommodityByCode", mock.Anything, "TEST", "token").Return(&model.Commodity{
					Data: model.CommodityBase{
						CommodityID:   1,
						CommodityName: "TEST",
						CommodityCode: "TEST",
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "CGK").Return(&model.District{
					Data: model.DistrictData{
						Name: "Jakarta",
						Code: "CGK",
						City: &model.City{
							Name: "Jakarta",
							Code: "CGK",
						},
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "KNO").Return(&model.District{
					Data: model.DistrictData{
						Name: "Medan",
						Code: "KNO",
						City: &model.City{
							Name: "Medan",
							Code: "KNO",
						},
					},
				}, nil).Once()

				mockCityRepo.On("GetRegionCity", mock.Anything, "KNO", "token").Return(&model.RespRegionCity{
					Data: model.RegionCity{
						RegionCode: "KNO",
						RegionName: "Medan",
					},
				}, nil).Once()

				mockSttCustomFlagRepo.On("Find", mock.Anything, &model.STTCustomFlagFindParams{
					SttNo: "77LP123",
					Key:   []string{model.ScfKeyIsNewForm},
				}).Return(nil, nil).Once()

				mockCheckoutRepo.On("GetDetailStt", mock.Anything, &model.CheckoutGetDetailSttParams{
					SttNo: "77LP123",
					Token: "token",
				}).Return(&model.CheckoutGetDetailSttResponse{
					Data: model.CheckoutGetDetailStt{
						CheckoutPaymentMethodName: "cod",
					},
				}, nil).Once()

				sttMeta := `{"cod_handling": "standardcod"}`
				mockShipmentRepo.On("Get", mock.Anything, mock.Anything).Return(&model.Shipment{
					ShipmentID: 1,
					ShipmentPackets: []model.ShipmentPacket{
						{
							ShipmentPacketID: 1,
						},
					},
					ShipmentMeta: &sttMeta}, nil).Once()
				return c
			},
		},
		{
			name: "TestDetailStt-GetDetailSttWithSttIsCodTrue",
			args: args{
				ctx: ctxBg,
				params: &stt.SttDetailRequest{
					SttID:          1,
					AccountType:    "partner",
					AccountRefType: "pos",
					AccountRefID:   10,
					Token:          "token",
				},
			},
			want: &stt.SttDetailResponse{
				SttID:                      int64(1),
				SttNo:                      "77LP123",
				SttOriginAddress:           "Jakarta, Jakarta",
				SttOriginDistrictID:        "CGK",
				SttOriginDistrictName:      "Jakarta",
				SttOriginCityID:            "CGK",
				SttOriginCityName:          "Jakarta",
				SttDestinationAddress:      "Medan, Medan",
				SttDestinationCityID:       "KNO",
				SttDestinationCityName:     "Medan",
				SttDestinationDistrictID:   "KNO",
				SttDestinationDistrictName: "Medan",
				SttCommodityID:             1,
				SttCommodityName:           "TEST",
				SttCommodityCode:           "TEST",
				SttInsuranceName:           "insurance",
				SttInsuranceRate:           1,
				SttPieces: []stt.SttPieces{
					{
						SttID:                int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				},
				SttLastStatus:            "POD",
				SttLastStatusDescription: "Paket sudah diterima.",
				SttIsAllowEdit:           false,
				SttRegionID:              "KNO",
				SttRegionName:            "Medan",
				SttCOD:                   "Yes",
				SttReturnDO:              "No",
				SttBookedById:            10,
				SttBookedByType:          "pos",
				CheckoutPaymentMethod:    "cod",
				LastStatusSttReturn:      model.RTS,
				SttIsCOD:                 true,
				SttIsDFOD:                false,
				DetailSttReverseJourney: &stt.DetailSttReverseJourney{
					RootReverseSttNo:         `88LP1691461672142`,
					RootReverseLastStatusStt: `TRANSIT`,
					CodHandling:              "standardcod",
					RootReverseShipmentID:    "C1RVVABC",
				},
				SttAttachFiles:       []string{},
				ListDiscount:         []model.PromoDiscount{},
				SttBookedByCountry:   model.CountryID,
				SttWeightAttachFiles: []string{},
				GoodsNames:           []string{},
				SttCipl:              []model.SttCIPL{},
				SttFtzCipl:           []model.SttCIPL{},
				SttFtzAttachFiles:    []string{},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *sttCtx {
				mockSttRepo := new(mocks.SttRepository)
				mockSttPiecesRepo := new(mocks.SttPiecesRepository)
				mockSttOptionalRateRepo := new(mocks.SttOptionalRateRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCommodityRepo := new(mocks.CommodityRepository)
				mockCheckoutRepo := new(mocks.CheckoutRepository)
				mockShipmentRepo := new(mocks.ShipmentRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockSttCustomFlagRepo := new(mocks.STTCustomFlagRepository)

				c := &sttCtx{
					sttRepo:             mockSttRepo,
					sttPiecesRepo:       mockSttPiecesRepo,
					sttOptionalRateRepo: mockSttOptionalRateRepo,
					commodityRepo:       mockCommodityRepo,
					districtRepo:        mockDistrictRepo,
					cityRepo:            mockCityRepo,
					checkoutRepo:        mockCheckoutRepo,
					shipmentRepo:        mockShipmentRepo,
					accountRepo:         mockAccountRepo,
					sttCustomFlagRepo:   mockSttCustomFlagRepo,
				}

				existingConfig := os.Getenv("LIMITED_ASSIGNED_3LC_RULE")
				defer func() {
					os.Setenv("LIMITED_ASSIGNED_3LC_RULE", existingConfig)
				}()
				os.Setenv("LIMITED_ASSIGNED_3LC_RULE", "[{\"type\":\"internal\",\"role\":\"Data Entry\"}]")

				mockAccountRepo.On("GetProfileWithParams", mock.Anything, &model.GetProfileRequest{Token: "token", NoGetTieringPos: true}).
					Return(&model.AccountDetail{
						Data: model.AccountDetailBase{
							AccountID:   123,
							RoleType:    "Data Entry",
							AccountType: "internal",
							Meta: map[string]interface{}{
								"assigned_3lc": "ALL",
							},
						},
					}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{SttID: int64(1)},
				}).Return(&model.Stt{
					SttID:                    int64(1),
					SttNo:                    "77LP123",
					SttBookedBy:              10,
					SttIsCOD:                 true,
					SttBookedByType:          "pos",
					SttCommodityCode:         "TEST",
					SttOriginDistrictID:      "CGK",
					SttDestinationDistrictID: "KNO",
					SttDestinationCityID:     "KNO",
					SttLastStatusID:          model.POD,
					SttMeta:                  `{"client_payment_method":"split_bill","client_cod_config_amount":"","detail_stt_reverse_journey": { "reverse_stt_no": "88LP1691461672142", "reverse_stt_shipment_id": "C1RVVABC", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "88LP1691461672142", "root_reverse_stt_shipment_id": "C1RVVABC", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "6282336440033", "root_sender_address": "kedoyaaa" }}`,
				}, nil).Once()
				mockSttPiecesRepo.On("SelectDetail", mock.Anything, mock.Anything).
					Return([]model.SttDetailResult{}, nil).Once()

				mockSttPiecesRepo.On("Select", mock.Anything, &model.SttPiecesViewParam{
					SttID: 1,
				}).Return([]model.SttPiece{
					{
						SttPieceSttID:        int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				}, nil).Once()

				mockSttOptionalRateRepo.On("Select", mock.Anything, &model.SttOptionalRate{
					SttOptionalRateSttID: int64(1),
				}).Return([]model.SttOptionalRate{
					{
						SttOptionalRateParams: "insurance",
						SttOptionalRateName:   "insurance",
						SttOptionalRateRate:   1,
					},
				}, nil).Once()

				mockCommodityRepo.On("GetCommodityByCode", mock.Anything, "TEST", "token").Return(&model.Commodity{
					Data: model.CommodityBase{
						CommodityID:   1,
						CommodityName: "TEST",
						CommodityCode: "TEST",
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "CGK").Return(&model.District{
					Data: model.DistrictData{
						Name: "Jakarta",
						Code: "CGK",
						City: &model.City{
							Name: "Jakarta",
							Code: "CGK",
						},
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "KNO").Return(&model.District{
					Data: model.DistrictData{
						Name: "Medan",
						Code: "KNO",
						City: &model.City{
							Name: "Medan",
							Code: "KNO",
						},
					},
				}, nil).Once()

				mockCityRepo.On("GetRegionCity", mock.Anything, "KNO", "token").Return(&model.RespRegionCity{
					Data: model.RegionCity{
						RegionCode: "KNO",
						RegionName: "Medan",
					},
				}, nil).Once()

				mockSttCustomFlagRepo.On("Find", mock.Anything, &model.STTCustomFlagFindParams{
					SttNo: "77LP123",
					Key:   []string{model.ScfKeyIsNewForm},
				}).Return(nil, nil).Once()

				mockCheckoutRepo.On("GetDetailStt", mock.Anything, &model.CheckoutGetDetailSttParams{
					SttNo: "77LP123",
					Token: "token",
				}).Return(&model.CheckoutGetDetailSttResponse{
					Data: model.CheckoutGetDetailStt{
						CheckoutPaymentMethodName: "cod",
					},
				}, nil).Once()

				sttMeta := `{"cod_handling": "standardcod"}`
				mockShipmentRepo.On("Get", mock.Anything, mock.Anything).Return(&model.Shipment{
					ShipmentID: 1,
					ShipmentPackets: []model.ShipmentPacket{
						{
							ShipmentPacketID: 1,
						},
					},
					ShipmentMeta: &sttMeta}, nil).Once()
				return c
			},
		},
		{
			name: "TestDetailStt-GetDetailSttWithSttIsDFODTrue",
			args: args{
				ctx: ctxBg,
				params: &stt.SttDetailRequest{
					SttID:          1,
					AccountType:    "partner",
					AccountRefType: "pos",
					AccountRefID:   10,
					Token:          "token",
				},
			},
			want: &stt.SttDetailResponse{
				SttID:                      int64(1),
				SttNo:                      "77LP123",
				SttOriginAddress:           "Jakarta, Jakarta",
				SttOriginDistrictID:        "CGK",
				SttOriginDistrictName:      "Jakarta",
				SttOriginCityID:            "CGK",
				SttOriginCityName:          "Jakarta",
				SttDestinationAddress:      "Medan, Medan",
				SttDestinationCityID:       "KNO",
				SttDestinationCityName:     "Medan",
				SttDestinationDistrictID:   "KNO",
				SttDestinationDistrictName: "Medan",
				SttCommodityID:             1,
				SttCommodityName:           "TEST",
				SttCommodityCode:           "TEST",
				SttInsuranceName:           "insurance",
				SttInsuranceRate:           1,
				SttPieces: []stt.SttPieces{
					{
						SttID:                int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				},
				SttLastStatus:            "POD",
				SttLastStatusDescription: "Paket sudah diterima.",
				SttIsAllowEdit:           false,
				SttRegionID:              "KNO",
				SttRegionName:            "Medan",
				SttCOD:                   "No",
				SttReturnDO:              "No",
				SttBookedById:            10,
				SttBookedByType:          "pos",
				CheckoutPaymentMethod:    "cod",
				LastStatusSttReturn:      model.RTS,
				SttIsCOD:                 false,
				SttIsDFOD:                true,
				DetailSttReverseJourney: &stt.DetailSttReverseJourney{
					RootReverseSttNo:         `88LP1691461672142`,
					RootReverseLastStatusStt: `TRANSIT`,
					CodHandling:              "standardcod",
					RootReverseShipmentID:    "C1RVVABC",
				},
				SttAttachFiles:       []string{},
				ListDiscount:         []model.PromoDiscount{},
				SttBookedByCountry:   model.CountryID,
				SttWeightAttachFiles: []string{},
				GoodsNames:           []string{},
				SttCipl:              []model.SttCIPL{},
				SttFtzCipl:           []model.SttCIPL{},
				SttFtzAttachFiles:    []string{},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *sttCtx {
				mockSttRepo := new(mocks.SttRepository)
				mockSttPiecesRepo := new(mocks.SttPiecesRepository)
				mockSttOptionalRateRepo := new(mocks.SttOptionalRateRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCommodityRepo := new(mocks.CommodityRepository)
				mockCheckoutRepo := new(mocks.CheckoutRepository)
				mockShipmentRepo := new(mocks.ShipmentRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockSttCustomFlagRepo := new(mocks.STTCustomFlagRepository)

				c := &sttCtx{
					sttRepo:             mockSttRepo,
					sttPiecesRepo:       mockSttPiecesRepo,
					sttOptionalRateRepo: mockSttOptionalRateRepo,
					commodityRepo:       mockCommodityRepo,
					districtRepo:        mockDistrictRepo,
					cityRepo:            mockCityRepo,
					checkoutRepo:        mockCheckoutRepo,
					shipmentRepo:        mockShipmentRepo,
					accountRepo:         mockAccountRepo,
					sttCustomFlagRepo:   mockSttCustomFlagRepo,
				}

				existingConfig := os.Getenv("LIMITED_ASSIGNED_3LC_RULE")
				defer func() {
					os.Setenv("LIMITED_ASSIGNED_3LC_RULE", existingConfig)
				}()
				os.Setenv("LIMITED_ASSIGNED_3LC_RULE", "[{\"type\":\"internal\",\"role\":\"Data Entry\"}]")

				mockAccountRepo.On("GetProfileWithParams", mock.Anything, &model.GetProfileRequest{Token: "token", NoGetTieringPos: true}).
					Return(&model.AccountDetail{
						Data: model.AccountDetailBase{
							AccountID:   123,
							RoleType:    "Data Entry",
							AccountType: "internal",
							Meta: map[string]interface{}{
								"assigned_3lc": "ALL",
							},
						},
					}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{SttID: int64(1)},
				}).Return(&model.Stt{
					SttID:                    int64(1),
					SttNo:                    "77LP123",
					SttBookedBy:              10,
					SttIsCOD:                 false,
					SttIsDFOD:                true,
					SttBookedByType:          "pos",
					SttCommodityCode:         "TEST",
					SttOriginDistrictID:      "CGK",
					SttDestinationDistrictID: "KNO",
					SttDestinationCityID:     "KNO",
					SttLastStatusID:          model.POD,
					SttMeta:                  `{"client_payment_method":"split_bill","client_cod_config_amount":"","detail_stt_reverse_journey": { "reverse_stt_no": "88LP1691461672142", "reverse_stt_shipment_id": "C1RVVABC", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "88LP1691461672142", "root_reverse_stt_shipment_id": "C1RVVABC", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "6282336440033", "root_sender_address": "kedoyaaa" }}`,
				}, nil).Once()

				mockSttPiecesRepo.On("SelectDetail", mock.Anything, mock.Anything).
					Return([]model.SttDetailResult{}, nil).Once()

				mockSttPiecesRepo.On("Select", mock.Anything, &model.SttPiecesViewParam{
					SttID: 1,
				}).Return([]model.SttPiece{
					{
						SttPieceSttID:        int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				}, nil).Once()

				mockSttOptionalRateRepo.On("Select", mock.Anything, &model.SttOptionalRate{
					SttOptionalRateSttID: int64(1),
				}).Return([]model.SttOptionalRate{
					{
						SttOptionalRateParams: "insurance",
						SttOptionalRateName:   "insurance",
						SttOptionalRateRate:   1,
					},
				}, nil).Once()

				mockCommodityRepo.On("GetCommodityByCode", mock.Anything, "TEST", "token").Return(&model.Commodity{
					Data: model.CommodityBase{
						CommodityID:   1,
						CommodityName: "TEST",
						CommodityCode: "TEST",
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "CGK").Return(&model.District{
					Data: model.DistrictData{
						Name: "Jakarta",
						Code: "CGK",
						City: &model.City{
							Name: "Jakarta",
							Code: "CGK",
						},
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "KNO").Return(&model.District{
					Data: model.DistrictData{
						Name: "Medan",
						Code: "KNO",
						City: &model.City{
							Name: "Medan",
							Code: "KNO",
						},
					},
				}, nil).Once()

				mockCityRepo.On("GetRegionCity", mock.Anything, "KNO", "token").Return(&model.RespRegionCity{
					Data: model.RegionCity{
						RegionCode: "KNO",
						RegionName: "Medan",
					},
				}, nil).Once()

				mockSttCustomFlagRepo.On("Find", mock.Anything, &model.STTCustomFlagFindParams{
					SttNo: "77LP123",
					Key:   []string{model.ScfKeyIsNewForm},
				}).Return(nil, nil).Once()

				mockCheckoutRepo.On("GetDetailStt", mock.Anything, &model.CheckoutGetDetailSttParams{
					SttNo: "77LP123",
					Token: "token",
				}).Return(&model.CheckoutGetDetailSttResponse{
					Data: model.CheckoutGetDetailStt{
						CheckoutPaymentMethodName: "cod",
					},
				}, nil).Once()

				sttMeta := `{"cod_handling": "standardcod"}`
				mockShipmentRepo.On("Get", mock.Anything, mock.Anything).Return(&model.Shipment{
					ShipmentID: 1,
					ShipmentPackets: []model.ShipmentPacket{
						{
							ShipmentPacketID: 1,
						},
					},
					ShipmentMeta: &sttMeta}, nil).Once()
				return c
			},
		},
		{
			name: "TestDetailStt-GetDetailSttWithSttReverseShipmentIDC1-ErrorGetShipment",
			args: args{
				ctx: ctxBg,
				params: &stt.SttDetailRequest{
					SttID:          1,
					AccountType:    "partner",
					AccountRefType: "pos",
					AccountRefID:   10,
					Token:          "token",
				},
			},
			want:    nil,
			wantErr: true,
			errResp: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting shipment",
				"id": "Terjadi kesalahan pada saat query getting shipment",
			}),
			beforeFunc: func() *sttCtx {
				mockSttRepo := new(mocks.SttRepository)
				mockSttPiecesRepo := new(mocks.SttPiecesRepository)
				mockSttOptionalRateRepo := new(mocks.SttOptionalRateRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCommodityRepo := new(mocks.CommodityRepository)
				mockCheckoutRepo := new(mocks.CheckoutRepository)
				mockShipmentRepo := new(mocks.ShipmentRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockSttCustomFlagRepo := new(mocks.STTCustomFlagRepository)

				c := &sttCtx{
					sttRepo:             mockSttRepo,
					sttPiecesRepo:       mockSttPiecesRepo,
					sttOptionalRateRepo: mockSttOptionalRateRepo,
					commodityRepo:       mockCommodityRepo,
					districtRepo:        mockDistrictRepo,
					cityRepo:            mockCityRepo,
					checkoutRepo:        mockCheckoutRepo,
					shipmentRepo:        mockShipmentRepo,
					accountRepo:         mockAccountRepo,
					sttCustomFlagRepo:   mockSttCustomFlagRepo,
				}

				existingConfig := os.Getenv("LIMITED_ASSIGNED_3LC_RULE")
				defer func() {
					os.Setenv("LIMITED_ASSIGNED_3LC_RULE", existingConfig)
				}()
				os.Setenv("LIMITED_ASSIGNED_3LC_RULE", "[{\"type\":\"internal\",\"role\":\"Data Entry\"}]")

				mockAccountRepo.On("GetProfileWithParams", mock.Anything, &model.GetProfileRequest{Token: "token", NoGetTieringPos: true}).
					Return(&model.AccountDetail{
						Data: model.AccountDetailBase{
							AccountID:   123,
							RoleType:    "Data Entry",
							AccountType: "internal",
							Meta: map[string]interface{}{
								"assigned_3lc": "ALL",
							},
						},
					}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{SttID: int64(1)},
				}).Return(&model.Stt{
					SttID:                    int64(1),
					SttNo:                    "77LP123",
					SttBookedBy:              10,
					SttIsCOD:                 true,
					SttBookedByType:          "pos",
					SttCommodityCode:         "TEST",
					SttOriginDistrictID:      "CGK",
					SttDestinationDistrictID: "KNO",
					SttDestinationCityID:     "KNO",
					SttLastStatusID:          model.POD,
					SttMeta:                  `{"client_payment_method":"split_bill","client_cod_config_amount":"","detail_stt_reverse_journey": { "reverse_stt_no": "88LP1691461672142", "reverse_stt_shipment_id": "C1RVVABC", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "88LP1691461672142", "root_reverse_stt_shipment_id": "C1RVVABC", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "6282336440033", "root_sender_address": "kedoyaaa" }}`,
				}, nil).Once()

				mockSttPiecesRepo.On("Select", mock.Anything, &model.SttPiecesViewParam{
					SttID: 1,
				}).Return([]model.SttPiece{
					{
						SttPieceSttID:        int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				}, nil).Once()

				mockSttPiecesRepo.On("SelectDetail", mock.Anything, mock.Anything).
					Return([]model.SttDetailResult{}, nil).Once()

				mockSttOptionalRateRepo.On("Select", mock.Anything, &model.SttOptionalRate{
					SttOptionalRateSttID: int64(1),
				}).Return([]model.SttOptionalRate{
					{
						SttOptionalRateParams: "insurance",
						SttOptionalRateName:   "insurance",
						SttOptionalRateRate:   1,
					},
				}, nil).Once()

				mockCommodityRepo.On("GetCommodityByCode", mock.Anything, "TEST", "token").Return(&model.Commodity{
					Data: model.CommodityBase{
						CommodityID:   1,
						CommodityName: "TEST",
						CommodityCode: "TEST",
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "CGK").Return(&model.District{
					Data: model.DistrictData{
						Name: "Jakarta",
						Code: "CGK",
						City: &model.City{
							Name: "Jakarta",
							Code: "CGK",
						},
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "KNO").Return(&model.District{
					Data: model.DistrictData{
						Name: "Medan",
						Code: "KNO",
						City: &model.City{
							Name: "Medan",
							Code: "KNO",
						},
					},
				}, nil).Once()

				mockCityRepo.On("GetRegionCity", mock.Anything, "KNO", "token").Return(&model.RespRegionCity{
					Data: model.RegionCity{
						RegionCode: "KNO",
						RegionName: "Medan",
					},
				}, nil).Once()

				mockSttCustomFlagRepo.On("Find", mock.Anything, &model.STTCustomFlagFindParams{
					SttNo: "77LP123",
					Key:   []string{model.ScfKeyIsNewForm},
				}).Return(nil, nil).Once()

				mockCheckoutRepo.On("GetDetailStt", mock.Anything, &model.CheckoutGetDetailSttParams{
					SttNo: "77LP123",
					Token: "token",
				}).Return(&model.CheckoutGetDetailSttResponse{
					Data: model.CheckoutGetDetailStt{
						CheckoutPaymentMethodName: "cod",
					},
				}, nil).Once()

				mockShipmentRepo.On("Get", mock.Anything, mock.Anything).Return(nil, fmt.Errorf("error")).Once()
				return c
			},
		},
		{
			name: "TestDetailStt-GetDetailSttWithShipmentC1",
			args: args{
				ctx: ctxBg,
				params: &stt.SttDetailRequest{
					SttID:          1,
					AccountType:    "partner",
					AccountRefType: "pos",
					AccountRefID:   10,
					Token:          "token",
				},
			},
			want: &stt.SttDetailResponse{
				SttID:                      int64(1),
				SttNo:                      "88LP123",
				SttShipmentID:              "C1RVVABC",
				SttOriginAddress:           "Jakarta, Jakarta",
				SttOriginDistrictID:        "CGK",
				SttOriginDistrictName:      "Jakarta",
				SttOriginCityID:            "CGK",
				SttOriginCityName:          "Jakarta",
				SttDestinationAddress:      "Medan, Medan",
				SttDestinationCityID:       "KNO",
				SttDestinationCityName:     "Medan",
				SttDestinationDistrictID:   "KNO",
				SttDestinationDistrictName: "Medan",
				SttCommodityID:             1,
				SttCommodityName:           "TEST",
				SttCommodityCode:           "TEST",
				SttInsuranceName:           "insurance",
				SttInsuranceRate:           1,
				SttIsCOD:                   false,
				SttPieces: []stt.SttPieces{
					{
						SttID:                int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				},
				CodHandling:              "",
				SttLastStatus:            "POD",
				SttLastStatusDescription: "Paket sudah diterima.",
				SttIsAllowEdit:           false,
				SttRegionID:              "KNO",
				SttRegionName:            "Medan",
				SttCOD:                   "No",
				SttReturnDO:              "No",
				SttBookedById:            10,
				SttBookedByType:          "pos",
				CheckoutPaymentMethod:    "cod",
				SttAttachFiles:           []string{},
				ListDiscount: []model.PromoDiscount{
					{
						Config: model.PromoDiscountConfiguration{
							PdcID: 1863,
						},
					},
				},
				SttBookedByCountry:   model.CountryID,
				SttWeightAttachFiles: []string{},
				GoodsNames:           []string{},
				SttCipl:              []model.SttCIPL{},
				SttFtzCipl:           []model.SttCIPL{},
				SttFtzAttachFiles:    []string{},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *sttCtx {
				mockSttRepo := new(mocks.SttRepository)
				mockSttPiecesRepo := new(mocks.SttPiecesRepository)
				mockSttOptionalRateRepo := new(mocks.SttOptionalRateRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCommodityRepo := new(mocks.CommodityRepository)
				mockCheckoutRepo := new(mocks.CheckoutRepository)
				mockShipmentRepo := new(mocks.ShipmentRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockSttCustomFlagRepo := new(mocks.STTCustomFlagRepository)

				c := &sttCtx{
					sttRepo:             mockSttRepo,
					sttPiecesRepo:       mockSttPiecesRepo,
					sttOptionalRateRepo: mockSttOptionalRateRepo,
					commodityRepo:       mockCommodityRepo,
					districtRepo:        mockDistrictRepo,
					cityRepo:            mockCityRepo,
					checkoutRepo:        mockCheckoutRepo,
					shipmentRepo:        mockShipmentRepo,
					accountRepo:         mockAccountRepo,
					sttCustomFlagRepo:   mockSttCustomFlagRepo,
				}

				existingConfig := os.Getenv("LIMITED_ASSIGNED_3LC_RULE")
				defer func() {
					os.Setenv("LIMITED_ASSIGNED_3LC_RULE", existingConfig)
				}()
				os.Setenv("LIMITED_ASSIGNED_3LC_RULE", "[{\"type\":\"internal\",\"role\":\"Data Entry\"}]")

				mockAccountRepo.On("GetProfileWithParams", mock.Anything, &model.GetProfileRequest{Token: "token", NoGetTieringPos: true}).
					Return(&model.AccountDetail{
						Data: model.AccountDetailBase{
							AccountID:   123,
							RoleType:    "Data Entry",
							AccountType: "internal",
							Meta: map[string]interface{}{
								"assigned_3lc": "ALL",
							},
						},
					}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{SttID: int64(1)},
				}).Return(&model.Stt{
					SttID:                    int64(1),
					SttNo:                    "88LP123",
					SttShipmentID:            "C1RVVABC",
					SttBookedBy:              10,
					SttBookedByType:          "pos",
					SttCommodityCode:         "TEST",
					SttOriginDistrictID:      "CGK",
					SttDestinationDistrictID: "KNO",
					SttDestinationCityID:     "KNO",
					SttIsCOD:                 false,
					SttLastStatusID:          model.POD,
					SttMeta:                  `{"client_payment_method":"split_bill","client_cod_config_amount":"","list_discount":[{"config":{"pdc_id":1863}}]}`,
				}, nil).Once()

				mockSttPiecesRepo.On("SelectDetail", mock.Anything, mock.Anything).
					Return([]model.SttDetailResult{}, nil).Once()

				mockSttPiecesRepo.On("Select", mock.Anything, &model.SttPiecesViewParam{
					SttID: 1,
				}).Return([]model.SttPiece{
					{
						SttPieceSttID:        int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				}, nil).Once()

				mockSttOptionalRateRepo.On("Select", mock.Anything, &model.SttOptionalRate{
					SttOptionalRateSttID: int64(1),
				}).Return([]model.SttOptionalRate{
					{
						SttOptionalRateParams: "insurance",
						SttOptionalRateName:   "insurance",
						SttOptionalRateRate:   1,
					},
				}, nil).Once()

				mockCommodityRepo.On("GetCommodityByCode", mock.Anything, "TEST", "token").Return(&model.Commodity{
					Data: model.CommodityBase{
						CommodityID:   1,
						CommodityName: "TEST",
						CommodityCode: "TEST",
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "CGK").Return(&model.District{
					Data: model.DistrictData{
						Name: "Jakarta",
						Code: "CGK",
						City: &model.City{
							Name: "Jakarta",
							Code: "CGK",
						},
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "KNO").Return(&model.District{
					Data: model.DistrictData{
						Name: "Medan",
						Code: "KNO",
						City: &model.City{
							Name: "Medan",
							Code: "KNO",
						},
					},
				}, nil).Once()

				mockCityRepo.On("GetRegionCity", mock.Anything, "KNO", "token").Return(&model.RespRegionCity{
					Data: model.RegionCity{
						RegionCode: "KNO",
						RegionName: "Medan",
					},
				}, nil).Once()

				mockSttCustomFlagRepo.On("Find", mock.Anything, &model.STTCustomFlagFindParams{
					SttNo: "88LP123",
					Key:   []string{model.ScfKeyIsNewForm},
				}).Return(nil, nil).Once()

				mockCheckoutRepo.On("GetDetailStt", mock.Anything, &model.CheckoutGetDetailSttParams{
					SttNo: "88LP123",
					Token: "token",
				}).Return(&model.CheckoutGetDetailSttResponse{
					Data: model.CheckoutGetDetailStt{
						CheckoutPaymentMethodName: "cod",
					},
				}, nil).Once()

				sttMeta := `{"cod_handling": "standardcod"}`
				mockShipmentRepo.On("Get", mock.Anything, mock.Anything).Return(&model.Shipment{
					ShipmentID:     1,
					ShipmentAlgoID: "88LP123",
					ShipmentPackets: []model.ShipmentPacket{
						{
							ShipmentPacketID: 1,
						},
					},
					ShipmentMeta: &sttMeta}, nil).Once()
				return c
			},
		},
		{
			name: "TestDetailStt-GetDetailSttWithShipmentAPAS",
			args: args{
				ctx: ctxBg,
				params: &stt.SttDetailRequest{
					SttID:          1,
					AccountType:    "partner",
					AccountRefType: "pos",
					AccountRefID:   10,
					Token:          "token",
				},
			},
			want: &stt.SttDetailResponse{
				SttID:                      int64(1),
				SttNo:                      "88LP123",
				SttShipmentID:              "ASRVVABC",
				SttOriginAddress:           "Jakarta, Jakarta",
				SttOriginDistrictID:        "CGK",
				SttOriginDistrictName:      "Jakarta",
				SttOriginCityID:            "CGK",
				SttOriginCityName:          "Jakarta",
				SttDestinationAddress:      "Medan, Medan",
				SttDestinationCityID:       "KNO",
				SttDestinationCityName:     "Medan",
				SttDestinationDistrictID:   "KNO",
				SttDestinationDistrictName: "Medan",
				SttCommodityID:             1,
				SttCommodityName:           "TEST",
				SttCommodityCode:           "TEST",
				SttInsuranceName:           "insurance",
				SttInsuranceRate:           1,
				SttIsCOD:                   false,
				SttPieces: []stt.SttPieces{
					{
						SttID:                int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				},
				CodHandling:              "",
				SttLastStatus:            "POD",
				SttLastStatusDescription: "Paket sudah diterima.",
				SttIsAllowEdit:           false,
				SttRegionID:              "KNO",
				SttRegionName:            "Medan",
				SttCOD:                   "No",
				SttReturnDO:              "No",
				SttBookedById:            10,
				SttBookedByType:          "pos",
				CheckoutPaymentMethod:    "cod",
				SttAttachFiles:           []string{},
				ListDiscount: []model.PromoDiscount{
					{
						Config: model.PromoDiscountConfiguration{
							PdcID: 0,
						},
					},
				},
				SttBookedByCountry:   model.CountryID,
				SttWeightAttachFiles: []string{},
				GoodsNames:           []string{},
				SttCipl:              []model.SttCIPL{},
				SttFtzCipl:           []model.SttCIPL{},
				SttFtzAttachFiles:    []string{},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *sttCtx {
				mockSttRepo := new(mocks.SttRepository)
				mockSttPiecesRepo := new(mocks.SttPiecesRepository)
				mockSttOptionalRateRepo := new(mocks.SttOptionalRateRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCommodityRepo := new(mocks.CommodityRepository)
				mockCheckoutRepo := new(mocks.CheckoutRepository)
				mockShipmentRepo := new(mocks.ShipmentRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockSttCustomFlagRepo := new(mocks.STTCustomFlagRepository)

				c := &sttCtx{
					sttRepo:             mockSttRepo,
					sttPiecesRepo:       mockSttPiecesRepo,
					sttOptionalRateRepo: mockSttOptionalRateRepo,
					commodityRepo:       mockCommodityRepo,
					districtRepo:        mockDistrictRepo,
					cityRepo:            mockCityRepo,
					checkoutRepo:        mockCheckoutRepo,
					shipmentRepo:        mockShipmentRepo,
					accountRepo:         mockAccountRepo,
					sttCustomFlagRepo:   mockSttCustomFlagRepo,
				}

				existingConfig := os.Getenv("LIMITED_ASSIGNED_3LC_RULE")
				defer func() {
					os.Setenv("LIMITED_ASSIGNED_3LC_RULE", existingConfig)
				}()
				os.Setenv("LIMITED_ASSIGNED_3LC_RULE", "[{\"type\":\"internal\",\"role\":\"Data Entry\"}]")

				mockAccountRepo.On("GetProfileWithParams", mock.Anything, &model.GetProfileRequest{Token: "token", NoGetTieringPos: true}).
					Return(&model.AccountDetail{
						Data: model.AccountDetailBase{
							AccountID:   123,
							RoleType:    "Data Entry",
							AccountType: "internal",
							Meta: map[string]interface{}{
								"assigned_3lc": "ALL",
							},
						},
					}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{SttID: int64(1)},
				}).Return(&model.Stt{
					SttID:                    int64(1),
					SttNo:                    "88LP123",
					SttShipmentID:            "ASRVVABC",
					SttBookedBy:              10,
					SttBookedByType:          "pos",
					SttCommodityCode:         "TEST",
					SttOriginDistrictID:      "CGK",
					SttDestinationDistrictID: "KNO",
					SttDestinationCityID:     "KNO",
					SttIsCOD:                 false,
					SttLastStatusID:          model.POD,
					SttMeta:                  `{"client_payment_method":"split_bill","client_cod_config_amount":"","list_discount":[{"config":{"pdc_id":0}}]}`,
				}, nil).Once()
				mockSttPiecesRepo.On("SelectDetail", mock.Anything, mock.Anything).
					Return([]model.SttDetailResult{}, nil).Once()

				mockSttPiecesRepo.On("Select", mock.Anything, &model.SttPiecesViewParam{
					SttID: 1,
				}).Return([]model.SttPiece{
					{
						SttPieceSttID:        int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				}, nil).Once()

				mockSttOptionalRateRepo.On("Select", mock.Anything, &model.SttOptionalRate{
					SttOptionalRateSttID: int64(1),
				}).Return([]model.SttOptionalRate{
					{
						SttOptionalRateParams: "insurance",
						SttOptionalRateName:   "insurance",
						SttOptionalRateRate:   1,
					},
				}, nil).Once()

				mockCommodityRepo.On("GetCommodityByCode", mock.Anything, "TEST", "token").Return(&model.Commodity{
					Data: model.CommodityBase{
						CommodityID:   1,
						CommodityName: "TEST",
						CommodityCode: "TEST",
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "CGK").Return(&model.District{
					Data: model.DistrictData{
						Name: "Jakarta",
						Code: "CGK",
						City: &model.City{
							Name: "Jakarta",
							Code: "CGK",
						},
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "KNO").Return(&model.District{
					Data: model.DistrictData{
						Name: "Medan",
						Code: "KNO",
						City: &model.City{
							Name: "Medan",
							Code: "KNO",
						},
					},
				}, nil).Once()

				mockCityRepo.On("GetRegionCity", mock.Anything, "KNO", "token").Return(&model.RespRegionCity{
					Data: model.RegionCity{
						RegionCode: "KNO",
						RegionName: "Medan",
					},
				}, nil).Once()

				mockSttCustomFlagRepo.On("Find", mock.Anything, &model.STTCustomFlagFindParams{
					SttNo: "88LP123",
					Key:   []string{model.ScfKeyIsNewForm},
				}).Return(nil, nil).Once()

				mockCheckoutRepo.On("GetDetailStt", mock.Anything, &model.CheckoutGetDetailSttParams{
					SttNo: "88LP123",
					Token: "token",
				}).Return(&model.CheckoutGetDetailSttResponse{
					Data: model.CheckoutGetDetailStt{
						CheckoutPaymentMethodName: "cod",
					},
				}, nil).Once()

				sttMeta := `{"cod_handling": "standardcod"}`
				mockShipmentRepo.On("Get", mock.Anything, mock.Anything).Return(&model.Shipment{
					ShipmentID:     1,
					ShipmentAlgoID: "88LP123",
					ShipmentPackets: []model.ShipmentPacket{
						{
							ShipmentPacketID: 1,
						},
					},
					ShipmentMeta: &sttMeta}, nil).Once()
				return c
			},
		},
		{
			name: "TestDetailStt-GetDetailSttWithShipmentC1-ErrorGetShipmentData",
			args: args{
				ctx: ctxBg,
				params: &stt.SttDetailRequest{
					SttID:          1,
					AccountType:    "partner",
					AccountRefType: "pos",
					AccountRefID:   10,
					Token:          "token",
				},
			},
			wantErr: true,
			errResp: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting shipment",
				"id": "Terjadi kesalahan pada saat query getting shipment",
			}),
			beforeFunc: func() *sttCtx {
				mockSttRepo := new(mocks.SttRepository)
				mockSttPiecesRepo := new(mocks.SttPiecesRepository)
				mockSttOptionalRateRepo := new(mocks.SttOptionalRateRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCommodityRepo := new(mocks.CommodityRepository)
				mockCheckoutRepo := new(mocks.CheckoutRepository)
				mockShipmentRepo := new(mocks.ShipmentRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockSttCustomFlagRepo := new(mocks.STTCustomFlagRepository)

				c := &sttCtx{
					sttRepo:             mockSttRepo,
					sttPiecesRepo:       mockSttPiecesRepo,
					sttOptionalRateRepo: mockSttOptionalRateRepo,
					commodityRepo:       mockCommodityRepo,
					districtRepo:        mockDistrictRepo,
					cityRepo:            mockCityRepo,
					checkoutRepo:        mockCheckoutRepo,
					shipmentRepo:        mockShipmentRepo,
					accountRepo:         mockAccountRepo,
					sttCustomFlagRepo:   mockSttCustomFlagRepo,
				}

				existingConfig := os.Getenv("LIMITED_ASSIGNED_3LC_RULE")
				defer func() {
					os.Setenv("LIMITED_ASSIGNED_3LC_RULE", existingConfig)
				}()
				os.Setenv("LIMITED_ASSIGNED_3LC_RULE", "[{\"type\":\"internal\",\"role\":\"Data Entry\"}]")

				mockAccountRepo.On("GetProfileWithParams", mock.Anything, &model.GetProfileRequest{Token: "token", NoGetTieringPos: true}).
					Return(&model.AccountDetail{
						Data: model.AccountDetailBase{
							AccountID:   123,
							RoleType:    "Data Entry",
							AccountType: "internal",
							Meta: map[string]interface{}{
								"assigned_3lc": "ALL",
							},
						},
					}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{SttID: int64(1)},
				}).Return(&model.Stt{
					SttID:                    int64(1),
					SttNo:                    "88LP123",
					SttShipmentID:            "C1RVVABC",
					SttBookedBy:              10,
					SttBookedByType:          "pos",
					SttCommodityCode:         "TEST",
					SttOriginDistrictID:      "CGK",
					SttDestinationDistrictID: "KNO",
					SttDestinationCityID:     "KNO",
					SttIsCOD:                 true,
					SttLastStatusID:          model.POD,
					SttMeta:                  `{"client_payment_method":"split_bill","client_cod_config_amount":""}`,
				}, nil).Once()

				mockSttPiecesRepo.On("SelectDetail", mock.Anything, mock.Anything).
					Return([]model.SttDetailResult{}, nil).Once()

				mockSttPiecesRepo.On("Select", mock.Anything, &model.SttPiecesViewParam{
					SttID: 1,
				}).Return([]model.SttPiece{
					{
						SttPieceSttID:        int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				}, nil).Once()

				mockSttOptionalRateRepo.On("Select", mock.Anything, &model.SttOptionalRate{
					SttOptionalRateSttID: int64(1),
				}).Return([]model.SttOptionalRate{
					{
						SttOptionalRateParams: "insurance",
						SttOptionalRateName:   "insurance",
						SttOptionalRateRate:   1,
					},
				}, nil).Once()

				mockCommodityRepo.On("GetCommodityByCode", mock.Anything, "TEST", "token").Return(&model.Commodity{
					Data: model.CommodityBase{
						CommodityID:   1,
						CommodityName: "TEST",
						CommodityCode: "TEST",
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "CGK").Return(&model.District{
					Data: model.DistrictData{
						Name: "Jakarta",
						Code: "CGK",
						City: &model.City{
							Name: "Jakarta",
							Code: "CGK",
						},
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "KNO").Return(&model.District{
					Data: model.DistrictData{
						Name: "Medan",
						Code: "KNO",
						City: &model.City{
							Name: "Medan",
							Code: "KNO",
						},
					},
				}, nil).Once()

				mockCityRepo.On("GetRegionCity", mock.Anything, "KNO", "token").Return(&model.RespRegionCity{
					Data: model.RegionCity{
						RegionCode: "KNO",
						RegionName: "Medan",
					},
				}, nil).Once()

				mockSttCustomFlagRepo.On("Find", mock.Anything, &model.STTCustomFlagFindParams{
					SttNo: "88LP123",
					Key:   []string{model.ScfKeyIsNewForm},
				}).Return(nil, nil).Once()

				mockCheckoutRepo.On("GetDetailStt", mock.Anything, &model.CheckoutGetDetailSttParams{
					SttNo: "88LP123",
					Token: "token",
				}).Return(&model.CheckoutGetDetailSttResponse{
					Data: model.CheckoutGetDetailStt{
						CheckoutPaymentMethodName: "cod",
					},
				}, nil).Once()

				mockShipmentRepo.On("Get", mock.Anything, mock.Anything).Return(nil, fmt.Errorf("error")).Once()
				return c
			},
		},
		{
			name: "TestDetailStt-GetDetailSttWithShipmentIDARAARB",
			args: args{
				ctx: ctxBg,
				params: &stt.SttDetailRequest{
					SttID:          1,
					AccountType:    "partner",
					AccountRefType: "pos",
					AccountRefID:   10,
					Token:          "token",
				},
			},
			want: &stt.SttDetailResponse{
				SttID:                      int64(1),
				SttNo:                      "11LP123",
				SttShipmentID:              "ARA123",
				SttOriginAddress:           "Jakarta, Jakarta",
				SttOriginDistrictID:        "CGK",
				SttOriginDistrictName:      "Jakarta",
				SttOriginCityID:            "CGK",
				SttOriginCityName:          "Jakarta",
				SttDestinationAddress:      "Medan, Medan",
				SttDestinationCityID:       "KNO",
				SttDestinationCityName:     "Medan",
				SttDestinationDistrictID:   "KNO",
				SttDestinationDistrictName: "Medan",
				SttCommodityID:             1,
				SttCommodityName:           "TEST",
				SttCommodityCode:           "TEST",
				SttInsuranceName:           "insurance",
				SttInsuranceRate:           1,
				SttPieces: []stt.SttPieces{
					{
						SttID:                int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				},
				SttLastStatus:             "POD",
				SttLastStatusDescription:  "Paket sudah diterima.",
				SttIsAllowEdit:            false,
				SttRegionID:               "KNO",
				SttRegionName:             "Medan",
				SttCOD:                    "No",
				SttReturnDO:               "No",
				SttBookedById:             10,
				SttBookedByType:           "pos",
				CheckoutPaymentMethod:     "cod",
				SttAttachFiles:            []string{},
				IsPromo:                   true,
				SttDiscount:               50,
				TotalDiscount:             25000,
				TotalTariffAfterDiscount:  25000,
				TotalTariffBeforeDiscount: 50000,
				SttTotalTariff:            50000,
				ListDiscount:              []model.PromoDiscount{},
				SttBookedByCountry:        model.CountryID,
				SttWeightAttachFiles:      []string{},
				GoodsNames:                []string{},
				IsNewForm:                 true,
				SttCipl:                   []model.SttCIPL{},
				SttFtzCipl:                []model.SttCIPL{},
				SttFtzAttachFiles:         []string{},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *sttCtx {
				mockSttRepo := new(mocks.SttRepository)
				mockSttPiecesRepo := new(mocks.SttPiecesRepository)
				mockSttOptionalRateRepo := new(mocks.SttOptionalRateRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCommodityRepo := new(mocks.CommodityRepository)
				mockCheckoutRepo := new(mocks.CheckoutRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockSttCustomFlagRepo := new(mocks.STTCustomFlagRepository)

				c := &sttCtx{
					sttRepo:             mockSttRepo,
					sttPiecesRepo:       mockSttPiecesRepo,
					sttOptionalRateRepo: mockSttOptionalRateRepo,
					commodityRepo:       mockCommodityRepo,
					districtRepo:        mockDistrictRepo,
					cityRepo:            mockCityRepo,
					checkoutRepo:        mockCheckoutRepo,
					accountRepo:         mockAccountRepo,
					sttCustomFlagRepo:   mockSttCustomFlagRepo,
				}

				existingConfig := os.Getenv("LIMITED_ASSIGNED_3LC_RULE")
				defer func() {
					os.Setenv("LIMITED_ASSIGNED_3LC_RULE", existingConfig)
				}()
				os.Setenv("LIMITED_ASSIGNED_3LC_RULE", "[{\"type\":\"internal\",\"role\":\"Data Entry\"}]")

				mockAccountRepo.On("GetProfileWithParams", mock.Anything, &model.GetProfileRequest{Token: "token", NoGetTieringPos: true}).
					Return(&model.AccountDetail{
						Data: model.AccountDetailBase{
							AccountID:   123,
							RoleType:    "Data Entry",
							AccountType: "internal",
							Meta: map[string]interface{}{
								"assigned_3lc": "ALL",
							},
						},
					}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{SttID: int64(1)},
				}).Return(&model.Stt{
					SttID:                    int64(1),
					SttNo:                    "11LP123",
					SttShipmentID:            "ARA123",
					SttBookedBy:              10,
					SttBookedByType:          "pos",
					SttCommodityCode:         "TEST",
					SttOriginDistrictID:      "CGK",
					SttDestinationDistrictID: "KNO",
					SttDestinationCityID:     "KNO",
					SttLastStatusID:          model.POD,
					SttMeta:                  `{"client_payment_method":"split_bill","client_cod_config_amount":"","client_tariff":{"before_discount":{"total_tariff":50000},"after_discount":{"total_tariff":25000,"discount":50,"discount_type":"percentage","is_promo":true,"is_discount_exceed_max_promo":false,"total_discount":25000}}}`,
				}, nil).Once()

				mockSttPiecesRepo.On("SelectDetail", mock.Anything, mock.Anything).
					Return([]model.SttDetailResult{}, nil).Once()

				mockSttPiecesRepo.On("Select", mock.Anything, &model.SttPiecesViewParam{
					SttID: 1,
				}).Return([]model.SttPiece{
					{
						SttPieceSttID:        int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				}, nil).Once()

				mockSttOptionalRateRepo.On("Select", mock.Anything, &model.SttOptionalRate{
					SttOptionalRateSttID: int64(1),
				}).Return([]model.SttOptionalRate{
					{
						SttOptionalRateParams: "insurance",
						SttOptionalRateName:   "insurance",
						SttOptionalRateRate:   1,
					},
				}, nil).Once()

				mockCommodityRepo.On("GetCommodityByCode", mock.Anything, "TEST", "token").Return(&model.Commodity{
					Data: model.CommodityBase{
						CommodityID:   1,
						CommodityName: "TEST",
						CommodityCode: "TEST",
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "CGK").Return(&model.District{
					Data: model.DistrictData{
						Name: "Jakarta",
						Code: "CGK",
						City: &model.City{
							Name: "Jakarta",
							Code: "CGK",
						},
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "KNO").Return(&model.District{
					Data: model.DistrictData{
						Name: "Medan",
						Code: "KNO",
						City: &model.City{
							Name: "Medan",
							Code: "KNO",
						},
					},
				}, nil).Once()

				mockCityRepo.On("GetRegionCity", mock.Anything, "KNO", "token").Return(&model.RespRegionCity{
					Data: model.RegionCity{
						RegionCode: "KNO",
						RegionName: "Medan",
					},
				}, nil).Once()

				mockSttCustomFlagRepo.On("Find", mock.Anything, &model.STTCustomFlagFindParams{
					SttNo: "11LP123",
					Key:   []string{model.ScfKeyIsNewForm},
				}).Return([]model.SttCustomFlag{
					{
						SCFKey:   model.ScfKeyIsNewForm,
						SCFValue: "true",
					},
				}, nil).Once()

				mockCheckoutRepo.On("GetDetailStt", mock.Anything, &model.CheckoutGetDetailSttParams{
					SttNo: "11LP123",
					Token: "token",
				}).Return(&model.CheckoutGetDetailSttResponse{
					Data: model.CheckoutGetDetailStt{
						CheckoutPaymentMethodName: "cod",
					},
				}, nil).Once()
				return c
			},
		},
		{
			name: "TestDetailStt-GetDetailSttWithSttReverseShipmentIDARAARB",
			args: args{
				ctx: ctxBg,
				params: &stt.SttDetailRequest{
					SttID:          1,
					AccountType:    "partner",
					AccountRefType: "pos",
					AccountRefID:   10,
					Token:          "token",
				},
			},
			want: &stt.SttDetailResponse{
				SttID:                      int64(1),
				SttNo:                      "11LP123",
				SttOriginAddress:           "Jakarta, Jakarta",
				SttOriginDistrictID:        "CGK",
				SttOriginDistrictName:      "Jakarta",
				SttOriginCityID:            "CGK",
				SttOriginCityName:          "Jakarta",
				SttDestinationAddress:      "Medan, Medan",
				SttDestinationCityID:       "KNO",
				SttDestinationCityName:     "Medan",
				SttDestinationDistrictID:   "KNO",
				SttDestinationDistrictName: "Medan",
				SttCommodityID:             1,
				SttCommodityName:           "TEST",
				SttCommodityCode:           "TEST",
				SttInsuranceName:           "insurance",
				SttInsuranceRate:           1,
				SttPieces: []stt.SttPieces{
					{
						SttID:                int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				},
				SttLastStatus:            "POD",
				SttLastStatusDescription: "Paket sudah diterima.",
				SttIsAllowEdit:           false,
				SttRegionID:              "KNO",
				SttRegionName:            "Medan",
				SttCOD:                   "No",
				SttReturnDO:              "No",
				SttBookedById:            10,
				SttBookedByType:          "pos",
				CheckoutPaymentMethod:    "cod",
				LastStatusSttReturn:      model.RTS,
				DetailSttReverseJourney: &stt.DetailSttReverseJourney{
					RootReverseSttNo:         "11LP1691461672142",
					RootReverseShipmentID:    "ARA123",
					RootReverseLastStatusStt: "TRANSIT",
				},
				SttAttachFiles:       []string{},
				ListDiscount:         []model.PromoDiscount{},
				SttBookedByCountry:   model.CountryID,
				SttWeightAttachFiles: []string{},
				GoodsNames:           []string{},
				SttCipl:              []model.SttCIPL{},
				SttFtzCipl:           []model.SttCIPL{},
				SttFtzAttachFiles:    []string{},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *sttCtx {
				mockSttRepo := new(mocks.SttRepository)
				mockSttPiecesRepo := new(mocks.SttPiecesRepository)
				mockSttOptionalRateRepo := new(mocks.SttOptionalRateRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCommodityRepo := new(mocks.CommodityRepository)
				mockCheckoutRepo := new(mocks.CheckoutRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockSttCustomFlagRepo := new(mocks.STTCustomFlagRepository)

				c := &sttCtx{
					sttRepo:             mockSttRepo,
					sttPiecesRepo:       mockSttPiecesRepo,
					sttOptionalRateRepo: mockSttOptionalRateRepo,
					commodityRepo:       mockCommodityRepo,
					districtRepo:        mockDistrictRepo,
					cityRepo:            mockCityRepo,
					checkoutRepo:        mockCheckoutRepo,
					accountRepo:         mockAccountRepo,
					sttCustomFlagRepo:   mockSttCustomFlagRepo,
				}

				existingConfig := os.Getenv("LIMITED_ASSIGNED_3LC_RULE")
				defer func() {
					os.Setenv("LIMITED_ASSIGNED_3LC_RULE", existingConfig)
				}()
				os.Setenv("LIMITED_ASSIGNED_3LC_RULE", "[{\"type\":\"internal\",\"role\":\"Data Entry\"}]")

				mockAccountRepo.On("GetProfileWithParams", mock.Anything, &model.GetProfileRequest{Token: "token", NoGetTieringPos: true}).
					Return(&model.AccountDetail{
						Data: model.AccountDetailBase{
							AccountID:   123,
							RoleType:    "Data Entry",
							AccountType: "internal",
							Meta: map[string]interface{}{
								"assigned_3lc": "ALL",
							},
						},
					}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{SttID: int64(1)},
				}).Return(&model.Stt{
					SttID:                    int64(1),
					SttNo:                    "11LP123",
					SttBookedBy:              10,
					SttBookedByType:          "pos",
					SttCommodityCode:         "TEST",
					SttOriginDistrictID:      "CGK",
					SttDestinationDistrictID: "KNO",
					SttDestinationCityID:     "KNO",
					SttLastStatusID:          model.POD,
					SttMeta:                  `{"client_payment_method":"split_bill","client_cod_config_amount":"","detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "ARA123", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "ARA123", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "6282336440033", "root_sender_address": "kedoyaaa" }}`,
				}, nil).Once()
				mockSttPiecesRepo.On("SelectDetail", mock.Anything, mock.Anything).
					Return([]model.SttDetailResult{}, nil).Once()

				mockSttPiecesRepo.On("Select", mock.Anything, &model.SttPiecesViewParam{
					SttID: 1,
				}).Return([]model.SttPiece{
					{
						SttPieceSttID:        int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				}, nil).Once()

				mockSttOptionalRateRepo.On("Select", mock.Anything, &model.SttOptionalRate{
					SttOptionalRateSttID: int64(1),
				}).Return([]model.SttOptionalRate{
					{
						SttOptionalRateParams: "insurance",
						SttOptionalRateName:   "insurance",
						SttOptionalRateRate:   1,
					},
				}, nil).Once()

				mockCommodityRepo.On("GetCommodityByCode", mock.Anything, "TEST", "token").Return(&model.Commodity{
					Data: model.CommodityBase{
						CommodityID:   1,
						CommodityName: "TEST",
						CommodityCode: "TEST",
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "CGK").Return(&model.District{
					Data: model.DistrictData{
						Name: "Jakarta",
						Code: "CGK",
						City: &model.City{
							Name: "Jakarta",
							Code: "CGK",
						},
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "KNO").Return(&model.District{
					Data: model.DistrictData{
						Name: "Medan",
						Code: "KNO",
						City: &model.City{
							Name: "Medan",
							Code: "KNO",
						},
					},
				}, nil).Once()

				mockCityRepo.On("GetRegionCity", mock.Anything, "KNO", "token").Return(&model.RespRegionCity{
					Data: model.RegionCity{
						RegionCode: "KNO",
						RegionName: "Medan",
					},
				}, nil).Once()

				mockSttCustomFlagRepo.On("Find", mock.Anything, &model.STTCustomFlagFindParams{
					SttNo: "11LP123",
					Key:   []string{model.ScfKeyIsNewForm},
				}).Return(nil, nil).Once()

				mockCheckoutRepo.On("GetDetailStt", mock.Anything, &model.CheckoutGetDetailSttParams{
					SttNo: "11LP123",
					Token: "token",
				}).Return(&model.CheckoutGetDetailSttResponse{
					Data: model.CheckoutGetDetailStt{
						CheckoutPaymentMethodName: "cod",
					},
				}, nil).Once()
				return c
			},
		},
		{
			name: "TestDetailStt-GetDetailSttWithSttInterpack",
			args: args{
				ctx: ctxBg,
				params: &stt.SttDetailRequest{
					SttID:          1,
					AccountType:    "partner",
					AccountRefType: "pos",
					AccountRefID:   10,
					Token:          "token",
				},
			},
			want: &stt.SttDetailResponse{
				SttID:                      int64(1),
				SttNo:                      "11LP123",
				SttShipmentID:              "ARA123",
				SttOriginAddress:           "Jakarta, Jakarta",
				SttOriginDistrictID:        "CGK",
				SttOriginDistrictName:      "Jakarta",
				SttOriginCityID:            "CGK",
				SttOriginCityName:          "Jakarta",
				SttDestinationAddress:      "Medan, Medan",
				SttDestinationCityID:       "KNO",
				SttDestinationCityName:     "Medan",
				SttDestinationDistrictID:   "KNO",
				SttDestinationDistrictName: "Medan",
				SttCommodityID:             1,
				SttCommodityName:           "TEST",
				SttCommodityCode:           "TEST",
				SttInsuranceName:           "insurance",
				SttInsuranceRate:           1,
				SttPieces: []stt.SttPieces{
					{
						SttID:                int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				},
				SttLastStatus:            "POD",
				SttLastStatusDescription: "Paket sudah diterima.",
				SttIsAllowEdit:           false,
				SttRegionID:              "KNO",
				SttRegionName:            "Medan",
				SttCOD:                   "No",
				SttReturnDO:              "No",
				SttBookedById:            10,
				SttBookedByType:          "pos",
				CheckoutPaymentMethod:    "cod",
				SttProductTypeName:       model.INTERPACK,
				SttAttachFiles:           []string{"url.com"},
				SttCommodityDetail:       "test",
				ListDiscount:             []model.PromoDiscount{},
				SttBookedByCountry:       model.CountryID, SttWeightAttachFiles: []string{},
				GoodsNames:        []string{},
				SttCipl:           []model.SttCIPL{},
				SttFtzCipl:        []model.SttCIPL{},
				SttFtzAttachFiles: []string{},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *sttCtx {
				mockSttRepo := new(mocks.SttRepository)
				mockSttPiecesRepo := new(mocks.SttPiecesRepository)
				mockSttOptionalRateRepo := new(mocks.SttOptionalRateRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCommodityRepo := new(mocks.CommodityRepository)
				mockCheckoutRepo := new(mocks.CheckoutRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockSttCustomFlagRepo := new(mocks.STTCustomFlagRepository)

				c := &sttCtx{
					sttRepo:             mockSttRepo,
					sttPiecesRepo:       mockSttPiecesRepo,
					sttOptionalRateRepo: mockSttOptionalRateRepo,
					commodityRepo:       mockCommodityRepo,
					districtRepo:        mockDistrictRepo,
					cityRepo:            mockCityRepo,
					checkoutRepo:        mockCheckoutRepo,
					accountRepo:         mockAccountRepo,
					sttCustomFlagRepo:   mockSttCustomFlagRepo,
				}

				existingConfig := os.Getenv("LIMITED_ASSIGNED_3LC_RULE")
				defer func() {
					os.Setenv("LIMITED_ASSIGNED_3LC_RULE", existingConfig)
				}()
				os.Setenv("LIMITED_ASSIGNED_3LC_RULE", "[{\"type\":\"internal\",\"role\":\"Data Entry\"}]")

				mockAccountRepo.On("GetProfileWithParams", mock.Anything, &model.GetProfileRequest{Token: "token", NoGetTieringPos: true}).
					Return(&model.AccountDetail{
						Data: model.AccountDetailBase{
							AccountID:   123,
							RoleType:    "Data Entry",
							AccountType: "internal",
							Meta: map[string]interface{}{
								"assigned_3lc": "ALL",
							},
						},
					}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{SttID: int64(1)},
				}).Return(&model.Stt{
					SttID:                    int64(1),
					SttNo:                    "11LP123",
					SttShipmentID:            "ARA123",
					SttBookedBy:              10,
					SttBookedByType:          "pos",
					SttCommodityCode:         "TEST",
					SttOriginDistrictID:      "CGK",
					SttDestinationDistrictID: "KNO",
					SttDestinationCityID:     "KNO",
					SttLastStatusID:          model.POD,
					SttProductType:           model.INTERPACK,
					SttMeta:                  `{"client_payment_method":"split_bill","client_cod_config_amount":"","stt_attach_files":["url.com"],"stt_commodity_detail":"test"}`,
				}, nil).Once()
				mockSttPiecesRepo.On("SelectDetail", mock.Anything, mock.Anything).
					Return([]model.SttDetailResult{}, nil).Once()
				mockSttPiecesRepo.On("Select", mock.Anything, &model.SttPiecesViewParam{
					SttID: 1,
				}).Return([]model.SttPiece{
					{
						SttPieceSttID:        int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				}, nil).Once()

				mockSttOptionalRateRepo.On("Select", mock.Anything, &model.SttOptionalRate{
					SttOptionalRateSttID: int64(1),
				}).Return([]model.SttOptionalRate{
					{
						SttOptionalRateParams: "insurance",
						SttOptionalRateName:   "insurance",
						SttOptionalRateRate:   1,
					},
				}, nil).Once()

				mockCommodityRepo.On("GetCommodityByCode", mock.Anything, "TEST", "token").Return(&model.Commodity{
					Data: model.CommodityBase{
						CommodityID:   1,
						CommodityName: "TEST",
						CommodityCode: "TEST",
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "CGK").Return(&model.District{
					Data: model.DistrictData{
						Name: "Jakarta",
						Code: "CGK",
						City: &model.City{
							Name: "Jakarta",
							Code: "CGK",
						},
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "KNO").Return(&model.District{
					Data: model.DistrictData{
						Name: "Medan",
						Code: "KNO",
						City: &model.City{
							Name: "Medan",
							Code: "KNO",
						},
					},
				}, nil).Once()

				mockCityRepo.On("GetRegionCity", mock.Anything, "KNO", "token").Return(&model.RespRegionCity{
					Data: model.RegionCity{
						RegionCode: "KNO",
						RegionName: "Medan",
					},
				}, nil).Once()

				mockSttCustomFlagRepo.On("Find", mock.Anything, &model.STTCustomFlagFindParams{
					SttNo: "11LP123",
					Key:   []string{model.ScfKeyIsNewForm},
				}).Return(nil, nil).Once()

				mockCheckoutRepo.On("GetDetailStt", mock.Anything, &model.CheckoutGetDetailSttParams{
					SttNo: "11LP123",
					Token: "token",
				}).Return(&model.CheckoutGetDetailSttResponse{
					Data: model.CheckoutGetDetailStt{
						CheckoutPaymentMethodName: "cod",
					},
				}, nil).Once()
				return c
			},
		},
		{
			name: "TestDetailStt-GetDetailSttWithSttInterpackWithSttInterTaxAndIdentityAndCommodityQuarantine",
			args: args{
				ctx: ctxBg,
				params: &stt.SttDetailRequest{
					SttID:          1,
					AccountType:    "partner",
					AccountRefType: "pos",
					AccountRefID:   10,
					Token:          "token",
				},
			},
			want: &stt.SttDetailResponse{
				SttID:                      int64(1),
				SttNo:                      "11LP123",
				SttShipmentID:              "ARA123",
				SttOriginAddress:           "Jakarta, Jakarta",
				SttOriginDistrictID:        "CGK",
				SttOriginDistrictName:      "Jakarta",
				SttOriginCityID:            "CGK",
				SttOriginCityName:          "Jakarta",
				SttDestinationAddress:      "Medan, Medan",
				SttDestinationCityID:       "KNO",
				SttDestinationCityName:     "Medan",
				SttDestinationDistrictID:   "KNO",
				SttDestinationDistrictName: "Medan",
				SttCommodityID:             1,
				SttCommodityName:           "TEST",
				SttCommodityCode:           "TEST",
				SttInsuranceName:           "insurance",
				SttInsuranceRate:           1,
				SttPieces: []stt.SttPieces{
					{
						SttID:                int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				},
				SttLastStatus:            "POD",
				SttLastStatusDescription: "Paket sudah diterima.",
				SttIsAllowEdit:           false,
				SttRegionID:              "KNO",
				SttRegionName:            "Medan",
				SttCOD:                   "No",
				SttReturnDO:              "No",
				SttBookedById:            10,
				SttBookedByType:          "pos",
				CheckoutPaymentMethod:    "cod",
				SttProductTypeName:       model.INTERPACK,
				SttAttachFiles:           []string{"url.com"},
				SttCommodityDetail:       "test",
				ListDiscount:             []model.PromoDiscount{},
				SttBookedByCountry:       model.CountryID, SttWeightAttachFiles: []string{},
				GoodsNames:               []string{},
				SttCommodityIsQuarantine: true,
				SttInterTaxNumber:        "123",
				SttIdentityNumber:        "123",
				SttCipl:                  []model.SttCIPL{},
				SttFtzCipl:               []model.SttCIPL{},
				SttFtzAttachFiles:        []string{},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *sttCtx {
				mockSttRepo := new(mocks.SttRepository)
				mockSttPiecesRepo := new(mocks.SttPiecesRepository)
				mockSttOptionalRateRepo := new(mocks.SttOptionalRateRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockCommodityRepo := new(mocks.CommodityRepository)
				mockCheckoutRepo := new(mocks.CheckoutRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockSttCustomFlagRepo := new(mocks.STTCustomFlagRepository)

				c := &sttCtx{
					sttRepo:             mockSttRepo,
					sttPiecesRepo:       mockSttPiecesRepo,
					sttOptionalRateRepo: mockSttOptionalRateRepo,
					commodityRepo:       mockCommodityRepo,
					districtRepo:        mockDistrictRepo,
					cityRepo:            mockCityRepo,
					checkoutRepo:        mockCheckoutRepo,
					accountRepo:         mockAccountRepo,
					sttCustomFlagRepo:   mockSttCustomFlagRepo,
				}

				existingConfig := os.Getenv("LIMITED_ASSIGNED_3LC_RULE")
				defer func() {
					os.Setenv("LIMITED_ASSIGNED_3LC_RULE", existingConfig)
				}()
				os.Setenv("LIMITED_ASSIGNED_3LC_RULE", "[{\"type\":\"internal\",\"role\":\"Data Entry\"}]")

				mockAccountRepo.On("GetProfileWithParams", mock.Anything, &model.GetProfileRequest{Token: "token", NoGetTieringPos: true}).
					Return(&model.AccountDetail{
						Data: model.AccountDetailBase{
							AccountID:   123,
							RoleType:    "Data Entry",
							AccountType: "internal",
							Meta: map[string]interface{}{
								"assigned_3lc": "ALL",
							},
						},
					}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{SttID: int64(1)},
				}).Return(&model.Stt{
					SttID:                    int64(1),
					SttNo:                    "11LP123",
					SttShipmentID:            "ARA123",
					SttBookedBy:              10,
					SttBookedByType:          "pos",
					SttCommodityCode:         "TEST",
					SttOriginDistrictID:      "CGK",
					SttDestinationDistrictID: "KNO",
					SttDestinationCityID:     "KNO",
					SttLastStatusID:          model.POD,
					SttProductType:           model.INTERPACK,
					SttMeta:                  `{"client_payment_method":"split_bill","client_cod_config_amount":"","stt_attach_files":["url.com"],"stt_commodity_detail":"test","stt_inter_tax_number":"123","stt_identity_number":"123"}`,
				}, nil).Once()
				mockSttPiecesRepo.On("SelectDetail", mock.Anything, mock.Anything).
					Return([]model.SttDetailResult{}, nil).Once()
				mockSttPiecesRepo.On("Select", mock.Anything, &model.SttPiecesViewParam{
					SttID: 1,
				}).Return([]model.SttPiece{
					{
						SttPieceSttID:        int64(1),
						SttPieceID:           int64(1),
						SttPieceLength:       10,
						SttPieceWidth:        10,
						SttPieceHeight:       10,
						SttPieceGrossWeight:  10,
						SttPieceVolumeWeight: 10,
					},
				}, nil).Once()

				mockSttOptionalRateRepo.On("Select", mock.Anything, &model.SttOptionalRate{
					SttOptionalRateSttID: int64(1),
				}).Return([]model.SttOptionalRate{
					{
						SttOptionalRateParams: "insurance",
						SttOptionalRateName:   "insurance",
						SttOptionalRateRate:   1,
					},
				}, nil).Once()

				mockCommodityRepo.On("GetCommodityByCode", mock.Anything, "TEST", "token").Return(&model.Commodity{
					Data: model.CommodityBase{
						CommodityID:           1,
						CommodityName:         "TEST",
						CommodityCode:         "TEST",
						CommodityIsQuarantine: true,
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "CGK").Return(&model.District{
					Data: model.DistrictData{
						Name: "Jakarta",
						Code: "CGK",
						City: &model.City{
							Name: "Jakarta",
							Code: "CGK",
						},
					},
				}, nil).Once()

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{
					Token: "token",
				}, "KNO").Return(&model.District{
					Data: model.DistrictData{
						Name: "Medan",
						Code: "KNO",
						City: &model.City{
							Name: "Medan",
							Code: "KNO",
						},
					},
				}, nil).Once()

				mockCityRepo.On("GetRegionCity", mock.Anything, "KNO", "token").Return(&model.RespRegionCity{
					Data: model.RegionCity{
						RegionCode: "KNO",
						RegionName: "Medan",
					},
				}, nil).Once()

				mockSttCustomFlagRepo.On("Find", mock.Anything, &model.STTCustomFlagFindParams{
					SttNo: "11LP123",
					Key:   []string{model.ScfKeyIsNewForm},
				}).Return(nil, nil).Once()

				mockCheckoutRepo.On("GetDetailStt", mock.Anything, &model.CheckoutGetDetailSttParams{
					SttNo: "11LP123",
					Token: "token",
				}).Return(&model.CheckoutGetDetailSttResponse{
					Data: model.CheckoutGetDetailStt{
						CheckoutPaymentMethodName: "cod",
					},
				}, nil).Once()
				return c
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.beforeFunc()
			got, err := c.DetailStt(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr || !assert.Equal(t, err, tt.errResp) {
				t.Errorf("sttCtx.DetailStt(); err = %v, ErrorResponse = %v, got = %v, want = %v\n", err, tt.errResp, got, tt.want)
				return
			}
			if !assert.Equal(t, got, tt.want) {
				t.Errorf("sttCtx.DetailStt(); got = %+v, want = %+v\n", got, tt.want)
			}
		})
	}
}
