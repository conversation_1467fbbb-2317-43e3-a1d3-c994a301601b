package usecase

import (
	"context"
	"fmt"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *sttCtx) DetailStt(ctx context.Context, params *stt.SttDetailRequest) (*stt.SttDetailResponse, error) {
	opName := "UsecaseStt-DetailStt"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": nil, "error": err})
	}()

	sttDetail, err := c.getSttDetail(selfCtx, params)
	if err != nil {
		return nil, err
	}

	sttHistories, err := c.getSttHistories(selfCtx, sttDetail, params)
	if err != nil {
		return nil, err
	}

	var req SttDetailDataRequest
	req.setCtx(selfCtx).setSttCtx(c).setParams(params).setSttDetail(sttDetail).setSttMeta(sttDetail).setSttHistories(sttHistories).setDataFromSttMeta().setSttWeightAttachFiles().checkAccountEligibility()
	if err = req.getError(); err != nil {
		return nil, err
	}

	req.checkAccountAccess().setSttClient().setSttPieces().setInsuranceAndWoodPackingRate().setCommodity().setDistrict().setRegion().setShipmentPrefix().getSttCustomFlag()
	if err = req.getError(); err != nil {
		return nil, err
	}

	req.checkSttRefundRemark()

	sttDetailResponse, err := req.getSttDetailResponse()
	if err != nil {
		return nil, err
	}

	return sttDetailResponse, nil
}

type SttDetailDataRequest struct {
	Ctx                  context.Context
	SttCtx               *sttCtx
	Params               *stt.SttDetailRequest
	Commodity            *model.Commodity
	SttDetail            *model.Stt
	SttMeta              *model.SttMeta
	SttClient            *model.ClientBase
	SttHistories         []model.SttPieceHistory
	Pieces               []stt.SttPieces
	SttWeightAttachFiles []string
	RefundRemarks        string

	DestinationDistrict *model.District
	OriginDistrict      *model.District
	Region              *model.RespRegionCity

	InsuranceName    string
	InsuranceRate    float64
	WoodpackingRate  float64
	SttIsWoodPacking bool

	PostalCodeDestination string
	ReverseLastStatus     string
	BookedByCountry       string
	SttReturnFee          float64
	ShipmentPrefix        string

	err                                                   error
	IsAllowToEdit, IsAllowToCancel, IsShipmentMarketplace bool

	IsNewFormSttCustomFlag bool
}

func (c *SttDetailDataRequest) setCtx(ctx context.Context) *SttDetailDataRequest {
	c.Ctx = ctx
	return c
}

func (c *SttDetailDataRequest) setSttCtx(sttCtx *sttCtx) *SttDetailDataRequest {
	c.SttCtx = sttCtx
	return c
}

func (c *SttDetailDataRequest) setParams(params *stt.SttDetailRequest) *SttDetailDataRequest {
	c.Params = params
	return c
}

func (c *SttDetailDataRequest) setSttDetail(sttDetail *model.Stt) *SttDetailDataRequest {
	c.SttDetail = sttDetail
	return c
}

func (c *SttDetailDataRequest) setSttMeta(sttDetail *model.Stt) *SttDetailDataRequest {
	c.SttMeta = sttDetail.SttMetaToStruct()
	return c
}

func (c *SttDetailDataRequest) setSttHistories(sttHistories []model.SttPieceHistory) *SttDetailDataRequest {
	c.SttHistories = sttHistories
	return c
}

func (req *SttDetailDataRequest) checkAccountEligibility() *SttDetailDataRequest {
	if req.getError() != nil {
		return req
	}
	params := req.getParams()
	c := req.SttCtx
	sttDetail := req.getSttDetail()

	switch params.AccountType {
	case model.CLIENT:
		c.validateAccountClient(req)
		if params.IsWithEligibility {
			c.checkClientEligibility(req)
		}
	case model.PARTNER:
		req.isPartnerDeniedAccess()

		req.IsShipmentMarketplace = sttDetail.SttShipmentID != `` && model.IsShipmentMarketplace[shared.GetPrefixShipmentID(sttDetail.SttShipmentID)]

		if params.IsWithEligibility {
			c.checkPartnerConsoleEligibility(req)
			c.checkPartnerPOSEligibility(req)
		}
	case model.INTERNAL, model.CUSTOMERSERVICE:
		c.getInternalAccountAccess(req)
	default:
		req.err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Account Type invalid",
			"id": "Tipe akun tidak valid",
		})
		return req
	}
	return req
}

func (c *SttDetailDataRequest) getSttDetail() *model.Stt {
	return c.SttDetail
}

func (c *SttDetailDataRequest) getSttMeta() *model.SttMeta {
	return c.SttMeta
}

func (c *SttDetailDataRequest) getError() error {
	return c.err
}

func (c *SttDetailDataRequest) getCtx() context.Context {
	return c.Ctx
}

func (c *SttDetailDataRequest) getParams() *stt.SttDetailRequest {
	return c.Params
}

func (c *SttDetailDataRequest) getSttHistories() []model.SttPieceHistory {
	return c.SttHistories
}

func (c *SttDetailDataRequest) checkSttAdjustedAccessByHiestories() {
	for _, history := range c.SttHistories {
		if !model.IsAllowToCancelByPos[history.HistoryStatus] {
			c.IsAllowToEdit = false
			c.IsAllowToCancel = false
			break
		}
	}
}

func (c *SttDetailDataRequest) setDataFromSttMeta() *SttDetailDataRequest {
	c.BookedByCountry = model.CountryID
	sttMeta := c.getSttMeta()
	if sttMeta == nil {
		return c
	}

	c.PostalCodeDestination = sttMeta.PostalCodeDestination
	c.BookedByCountry = sttMeta.GetSttBookedByCountry()

	if sttMeta.DetailSttReverseJourney != nil {
		c.ReverseLastStatus = sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt
	}

	if sttMeta.TotalTariffReturn != 0 {
		c.SttReturnFee = sttMeta.TotalTariffReturn * 0.5
	}
	return c
}

func (c *SttDetailDataRequest) checkAccountAccess() *SttDetailDataRequest {
	sttDetail := c.SttDetail
	sttMeta := c.SttMeta
	/*
	 * is allow validation
	 */

	// always disable edit button when shipment is
	// B1, B2
	if sttDetail.SttShipmentID != `` && model.MappingShipmentDisableEditStt[shared.GetPrefixShipmentID(sttDetail.SttShipmentID)] {
		c.IsAllowToEdit = false
	}

	if c.hasAccessCAshipmentPOD(sttDetail) {
		c.IsAllowToEdit = false
	}

	if c.hasAccessCAReverseShipmentPOD(sttDetail, sttMeta) {
		c.IsAllowToEdit = false
	}

	// SCRAPCD
	if sttDetail.SttLastStatusID == model.SCRAPCD {
		c.IsAllowToEdit = false
		c.IsAllowToCancel = false
	}

	// CNXCD
	if sttDetail.SttLastStatusID == model.CNXCD {
		c.IsAllowToEdit = false
		c.IsAllowToCancel = false
	}

	// client payment method
	isAllowEditForSplitBillStt := sttDetail.SttIsCOD && sttDetail.SttLastStatusID == model.POD && sttMeta != nil && sttMeta.ClientPaymentMethod == model.ClientPaymentMethodSplitBill
	if isAllowEditForSplitBillStt {
		c.IsAllowToEdit = false
	}

	if sttMeta.ClientPaymentMethod == model.GoodsPriceTotalTarif && sttDetail.SttIsCOD {
		c.IsAllowToEdit = false
	}

	return c
}

func (c *SttDetailDataRequest) hasAccessCAshipmentPOD(sttDetail *model.Stt) bool {
	if sttDetail.SttShipmentID == `` {
		return false
	}

	return model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(sttDetail.SttShipmentID)] && sttDetail.SttLastStatusID == model.POD
}

func (c *SttDetailDataRequest) hasAccessCAReverseShipmentPOD(sttDetail *model.Stt, sttMeta *model.SttMeta) bool {
	return sttMeta.HasShipmentReverseJourney() && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.ReverseShipmentID)] && sttDetail.SttLastStatusID == model.POD
}

func (c *SttDetailDataRequest) isPartnerDeniedAccess() {
	params := c.getParams()
	sttDetail := c.getSttDetail()
	if params.AccountRefType != model.POS || !params.IsAuth {
		return
	}

	if sttDetail.SttBookedBy != params.AccountRefID || sttDetail.SttBookedByType != model.POS {
		c.err = shared.ERR_STT_NOT_FOUND
		return
	}

	return
}

func (c *SttDetailDataRequest) setSttClient() *SttDetailDataRequest {
	if c.getError() != nil {
		return c
	}

	params := c.getParams()
	sttDetail := c.getSttDetail()

	if sttDetail.SttClientID == 0 {
		c.SttClient = &model.ClientBase{}
		return c
	}

	client, err := c.SttCtx.clientRepo.GetByID(c.getCtx(), sttDetail.SttClientID, params.Token)
	if err != nil || client == nil {
		c.err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Booked Client is not found",
			"id": "Booked Client tidak ditemukan",
		})
		return c
	}

	c.SttClient = &client.Data
	return c
}

func (req *SttDetailDataRequest) setSttPieces() *SttDetailDataRequest {
	if req.getError() != nil {
		return req
	}

	// get stt piece
	sttPieces, err := req.SttCtx.sttPiecesRepo.Select(req.getCtx(), &model.SttPiecesViewParam{
		SttID: req.Params.SttID,
	})
	if err != nil {
		req.err = shared.ERR_UNEXPECTED_DB
		return req
	}
	pieces := []stt.SttPieces{}
	for _, v := range sttPieces {
		piece := stt.SttPieces{}
		piece.SttID = v.SttPieceSttID
		piece.SttPieceID = v.SttPieceID
		piece.SttPieceLength = v.SttPieceLength
		piece.SttPieceWidth = v.SttPieceWidth
		piece.SttPieceHeight = v.SttPieceHeight
		piece.SttPieceGrossWeight = v.SttPieceGrossWeight
		piece.SttPieceVolumeWeight = v.SttPieceVolumeWeight
		pieces = append(pieces, piece)
	}

	req.Pieces = pieces
	return req
}

func (req *SttDetailDataRequest) setShipmentPrefix() *SttDetailDataRequest {
	sttDetail := req.getSttDetail()
	if sttDetail.SttShipmentID != `` {
		req.ShipmentPrefix = shared.GetPrefixShipmentID(sttDetail.SttShipmentID)
	}
	return req
}

func (req *SttDetailDataRequest) setInsuranceAndWoodPackingRate() *SttDetailDataRequest {
	if req.getError() != nil {
		return req
	}

	c := req.SttCtx
	sttDetail := req.getSttDetail()

	// get stt optional rate
	sttOptionalRates, err := c.sttOptionalRateRepo.Select(req.getCtx(), &model.SttOptionalRate{
		SttOptionalRateSttID: sttDetail.SttID,
	})
	if err != nil {
		req.err = shared.ERR_UNEXPECTED_DB
		return req

	}

	for _, v := range sttOptionalRates {
		if v.SttOptionalRateParams == model.INSURANCE {
			req.InsuranceName = v.SttOptionalRateName
			req.InsuranceRate = v.SttOptionalRateRate
		}
		if v.SttOptionalRateParams == model.WOODPACKING {
			req.WoodpackingRate = v.SttOptionalRateRate
			req.SttIsWoodPacking = true
		}
	}
	return req
}

func (req *SttDetailDataRequest) setCommodity() *SttDetailDataRequest {
	if req.getError() != nil {
		return req
	}

	// get commodity
	c := req.SttCtx
	var err error
	req.Commodity, err = c.commodityRepo.GetCommodityByCode(
		req.getCtx(),
		req.SttDetail.SttCommodityCode,
		req.Params.Token,
	)
	if req.Commodity == nil || err != nil {
		req.err = shared.ERR_GET_DATA
	}

	return req
}

func (req *SttDetailDataRequest) setDistrict() *SttDetailDataRequest {
	if req.getError() != nil {
		return req
	}

	c := req.SttCtx
	sttDetail := req.getSttDetail()
	params := req.getParams()
	selfCtx := req.getCtx()
	var err error

	credentialRestAPI := &model.CredentialRestAPI{
		Token: params.Token,
	}

	// get district-city
	req.OriginDistrict, err = c.districtRepo.GetByCode(selfCtx, credentialRestAPI, sttDetail.SttOriginDistrictID)
	if req.OriginDistrict == nil || err != nil {
		req.err = shared.ERR_GET_DATA
	}
	req.DestinationDistrict, err = c.districtRepo.GetByCode(selfCtx, credentialRestAPI, sttDetail.SttDestinationDistrictID)
	if req.DestinationDistrict == nil || err != nil {
		req.err = shared.ERR_GET_DATA
	}

	return req
}

func (req *SttDetailDataRequest) setRegion() *SttDetailDataRequest {
	if req.getError() != nil {
		return req
	}

	c := req.SttCtx
	sttDetail := req.getSttDetail()
	params := req.getParams()
	var err error
	req.Region, err = c.cityRepo.GetRegionCity(req.getCtx(), sttDetail.SttDestinationCityID, params.Token)
	if err != nil || req.Region == nil {
		req.Region = &model.RespRegionCity{}
	}
	return req
}

func (req *SttDetailDataRequest) setSttWeightAttachFiles() *SttDetailDataRequest {
	c := req.SttCtx
	selfCtx := req.getCtx()
	sttDetail := req.getSttDetail()

	// get is detailPieces exist
	detailPieces, err := c.sttPiecesRepo.SelectDetail(selfCtx, &model.SttPiecesViewParam{
		SttID: int(sttDetail.SttID),
	})
	if err != nil {
		req.err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting STT and Piece",
			"id": "Terjadi kesalahan pada saat getting STT and Piece",
		})
		return req
	}
	var histories []model.SttPieceHistory

	if len(detailPieces) > 0 {
		histories, err = c.sttPieceHistoryRepo.Select(selfCtx, &model.SttPieceHistoryViewParam{
			SttPieceHistorySttPieceID:    detailPieces[0].SttPieceID,
			Order:                        true,
			OrderDesc:                    true,
			SttPieceHistoryStatusWhereIn: []string{model.BKD, model.STTADJUSTED},
		})

		if err != nil {
			req.err = shared.ERR_UNEXPECTED_DB
			return req
		}
	}

	req.SttWeightAttachFiles = []string{}

	if len(histories) > 0 && histories[0].RemarkPieceHistoryToStruct() != nil {
		historyRemarks := histories[0].RemarkPieceHistoryToStruct()
		req.SttWeightAttachFiles = historyRemarks.SttWeightAttachFiles
	}

	return req
}

func (req *SttDetailDataRequest) getOriginDistrict() *model.District {
	return req.OriginDistrict
}

func (req *SttDetailDataRequest) getDestinationDistrict() *model.District {
	return req.DestinationDistrict
}

func (req *SttDetailDataRequest) getRegion() *model.RespRegionCity {
	return req.Region
}

func (req *SttDetailDataRequest) getSttDetailResponse() (*stt.SttDetailResponse, error) {
	sttDetail := req.getSttDetail()
	sttMeta := req.getSttMeta()

	sttDetailResponse := req.initSttDetailResponse()
	sttDetailResponse.SttWeightAttachFiles = sttDetailResponse.GetSttWeightAttachFiles(req.SttWeightAttachFiles)

	if req.SttClient != nil && req.SttClient.ClientID > 0 {
		sttDetailResponse.SttClient = req.SttClient
		sttDetailResponse.SttClient.ClientPaymentMethod = sttDetailResponse.SttClient.GetClientPaymentMethod(sttMeta)
	}

	req.setCODResponse(sttDetailResponse).setPaymentMethodResponse(sttDetailResponse).setResponseReason(sttDetailResponse)
	if err := req.getError(); err != nil {
		return nil, err
	}

	sttDetailResponse.SttAttachFiles = []string{}
	if sttMeta != nil && sttDetail.SttProductType == model.INTERPACK {
		// interpack detail
		sttDetailResponse.SttCommodityDetail = sttMeta.SttCommodityDetail
		sttDetailResponse.SttAttachFiles = append(sttDetailResponse.SttAttachFiles, sttMeta.SttAttachFiles...)
		sttDetailResponse.SttRecipientEmail = sttMeta.SttRecipientEmail
		sttDetailResponse.SttKtpImage = sttMeta.SttKtpImage
		sttDetailResponse.SttTaxImage = sttMeta.SttTaxImage
	}

	req.setRetailPromoTariffResponse(sttDetailResponse).setResponseInternalAccountAccess(sttDetailResponse).setSttReverseDataResponse(sttDetailResponse)
	if err := req.getError(); err != nil {
		return nil, err
	}

	req.setDiscountResponse(sttDetailResponse).setAccessAccountInternal(sttDetailResponse).setGoodsNames(sttDetailResponse).setInterTaxAndIdentityNumber(sttDetailResponse)

	return sttDetailResponse, nil
}

func (req *SttDetailDataRequest) initSttDetailResponse() *stt.SttDetailResponse {
	originDistrict := req.getOriginDistrict()
	destinationDistrict := req.getDestinationDistrict()
	region := req.getRegion()
	sttDetail := req.getSttDetail()

	bookedForId := sttDetail.SttBookedForID
	bookedForType := sttDetail.SttBookedForType
	bookedForCode := sttDetail.SttBookedForCode
	bookedById := sttDetail.SttBookedBy
	bookedByType := sttDetail.SttBookedByType
	bookedByCode := sttDetail.SttBookedByCode

	cityRate := sttDetail.SttPublishRate + sttDetail.SttShippingSurchargeRate
	forwardRate := sttDetail.SttOriginDistrictRate + sttDetail.SttDestinationDistrictRate
	sttTaxRate := sttDetail.SttBMTaxRate + sttDetail.SttPPNTaxRate + sttDetail.SttPPHTaxRate
	return &stt.SttDetailResponse{
		SttID:                      sttDetail.SttID,
		SttNo:                      sttDetail.SttNo,
		SttShipmentID:              sttDetail.SttShipmentID,
		SttOriginAddress:           fmt.Sprint(originDistrict.Data.Name, ", ", originDistrict.Data.City.Name),
		SttOriginCityID:            originDistrict.Data.City.Code,
		SttOriginCityName:          originDistrict.Data.City.Name,
		SttOriginCity:              req.OriginDistrict.Data.City.ID,
		SttOriginDistrictID:        originDistrict.Data.Code,
		SttOriginDistrictName:      originDistrict.Data.Name,
		SttDestinationAddress:      fmt.Sprint(destinationDistrict.Data.Name, ", ", destinationDistrict.Data.City.Name),
		SttDestinationCityID:       destinationDistrict.Data.City.Code,
		SttDestinationCityName:     destinationDistrict.Data.City.Name,
		SttDestinationCity:         req.DestinationDistrict.Data.City.ID,
		SttDestinationDistrictID:   destinationDistrict.Data.Code,
		SttDestinationDistrictName: destinationDistrict.Data.Name,
		SttNoRefExternal:           sttDetail.SttNoRefExternal,

		//ursa code
		SttOriginDistrictUrsaCode:      originDistrict.Data.UrsaCode,
		SttDestinationDistrictUrsaCode: destinationDistrict.Data.UrsaCode,

		SttClient:        nil,
		SttReasonMapping: nil,

		SttSenderName:    sttDetail.SttSenderName,
		SttSenderPhone:   sttDetail.SttSenderPhone,
		SttSenderAddress: sttDetail.SttSenderAddress,

		SttRecipientName:    sttDetail.SttRecipientName,
		SttRecipientAddress: sttDetail.SttRecipientAddress,
		SttRecipientPhone:   sttDetail.SttRecipientPhone,

		SttCommodityID:           req.Commodity.Data.CommodityID,
		SttCommodityName:         req.Commodity.Data.CommodityName,
		SttCommodityCode:         req.Commodity.Data.CommodityCode,
		SttCommodityIsQuarantine: req.Commodity.Data.CommodityIsQuarantine,
		SttProductTypeName:       sttDetail.SttProductType,
		SttTotalPiece:            sttDetail.SttTotalPiece,
		SttTotalChargeableWeight: sttDetail.SttChargeableWeight,
		SttTotalGross:            sttDetail.SttGrossWeight,
		SttTotalVolume:           sttDetail.SttVolumeWeight,
		SttGoodsEstimatePrice:    sttDetail.SttGoodsEstimatePrice,
		SttTaxNumber:             sttDetail.SttTaxNumber,
		SttInsuranceName:         req.InsuranceName,
		SttBookedBy:              sttDetail.SttBookedName,
		SttBookedFor:             sttDetail.SttBilledTo,
		SttBilledTo:              sttDetail.SttBilledTo,
		SttSource:                sttDetail.SttSource,
		SttCOD:                   model.MappingBoolStatus[sttDetail.SttIsCOD],
		SttReturnDO:              model.MappingBoolStatus[sttDetail.SttIsDO],

		SttShipmentPrice:            cityRate + forwardRate,
		SttCityRate:                 cityRate,
		SttForwardRate:              forwardRate,
		SttShippingSurchargeRate:    sttDetail.SttShippingSurchargeRate,
		SttDocumentSurchargeRate:    sttDetail.SttDocumentSurchargeRate,
		SttCommoditySurchargeRate:   sttDetail.SttCommoditySurchargeRate,
		SttHeavyweightSurchargeRate: sttDetail.SttHeavyweightSurchargeRate,
		SttInsuranceRate:            req.InsuranceRate,
		SttWoodpackingRate:          req.WoodpackingRate,
		SttTaxRate:                  sttTaxRate,

		SttTotalTariff: sttDetail.SttTotalAmount,

		SttPieces:                   req.Pieces,
		SttSenderStatus:             sttDetail.SttGoodsStatus,
		SttLastStatus:               sttDetail.SttLastStatusID,
		SttLastStatusDescription:    model.SttStatus[sttDetail.SttLastStatusID],
		SttLastStatusCounter:        sttDetail.SttCounter,
		SttWarningStatus:            sttDetail.SttWarningStatus,
		SttWarningStatusDescription: model.SttStatus[sttDetail.SttWarningStatus],
		SttCreatedAt:                sttDetail.SttCreatedAt,
		SttCreatedBy:                sttDetail.SttCreatedName,
		SttUpdatedAt:                sttDetail.SttUpdatedAt,
		SttUpdatedBy:                sttDetail.SttUpdatedName,
		SttIsAllowEdit:              req.IsAllowToEdit,
		SttIsAllowCancel:            req.IsAllowToCancel,
		SttNextCommodity:            sttDetail.SttNextCommodity,
		SttPiecePerPack:             sttDetail.SttPiecePerPack,

		SttBookedById:    bookedById,
		SttBookedByType:  bookedByType,
		SttBookedByCode:  bookedByCode,
		SttBookedForId:   bookedForId,
		SttBookedForType: bookedForType,
		SttBookedForCode: bookedForCode,

		SttRecipientAddressType: sttDetail.SttRecipientAddressType.Value(),
		PostalCodeDestination:   req.PostalCodeDestination,
		LastStatusSttReturn:     req.ReverseLastStatus,
		SttBookedByCountry:      req.BookedByCountry,

		SttRegionID:      region.Data.RegionCode,
		SttRegionName:    region.Data.RegionName,
		SttReturnFee:     req.SttReturnFee,
		SttIsCOD:         sttDetail.SttIsCOD,
		SttIsDFOD:        sttDetail.SttIsDFOD,
		SttIsWoodPacking: req.SttIsWoodPacking,
		RefundRemarks:    req.RefundRemarks,
		IsDangerousGoods: req.Commodity.Data.IsDangerousGoods,
		IsNewForm:        req.IsNewFormSttCustomFlag,
	}
}

func (req *SttDetailDataRequest) setCODResponse(sttDetailResponse *stt.SttDetailResponse) *SttDetailDataRequest {
	sttDetail := req.getSttDetail()
	c := req.SttCtx

	if !sttDetail.SttIsCOD {
		return req
	}

	sttDetailResponse.SttCODAmount = sttDetail.SttCODAmount
	sttDetailResponse.SttCODFee = sttDetail.SttCODFee

	if sttDetail.SttShipmentID == `` {
		return req
	}

	if shared.GetPrefixShipmentID(sttDetail.SttShipmentID) != model.C1 && shared.GetPrefixShipmentID(sttDetail.SttShipmentID) != model.C2 {
		return req
	}

	// check cod handling for shipment C1
	shipment, err := c.shipmentRepo.Get(req.getCtx(), &model.ShipmentViewParams{
		ShipmentAlgoID: sttDetail.SttShipmentID,
	})
	if err != nil || shipment.IsShipmentNotFound() {
		req.err = c.errShipmentNotFound()
		return req
	}
	if shipment.ShipmentMeta == nil {
		return req
	}
	shipmentMetaStruct := shipment.ShipmentMetaToStruct()
	if shipmentMetaStruct != nil {
		sttDetailResponse.CodHandling = shipmentMetaStruct.CodHandling
	}
	return req
}

func (req *SttDetailDataRequest) setResponseReason(sttDetailResponse *stt.SttDetailResponse) *SttDetailDataRequest {
	sttDetail := req.getSttDetail()
	c := req.SttCtx
	selfCtx := req.getCtx()

	if req.getError() != nil || !model.IsStatusWithReason[sttDetail.SttLastStatusID] {
		return req
	}
	sttHistory, err := c.sttPieceHistoryRepo.Get(selfCtx, &model.SttPieceHistoryViewParam{
		SttPieceHistorySttPieceID: req.Pieces[0].SttPieceID,
		OrderDesc:                 true,
		Order:                     true,
	})

	if err != nil || sttHistory == nil {
		req.err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT History is not found",
			"id": "STT History tidak ditemukan",
		})
		return req
	}

	reasonSttStatus, err := c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
		ReasonCode: sttHistory.HistoryReason,
	})

	if err != nil || reasonSttStatus == nil {
		req.err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Reason status is not found",
			"id": "Reason status tidak ditemukan",
		})
		return req
	}

	sttDetailResponse.SttReasonMapping = &reasonSttStatus.Reason
	return req
}

func (req *SttDetailDataRequest) setPaymentMethodResponse(sttDetailResponse *stt.SttDetailResponse) *SttDetailDataRequest {
	sttDetail := req.getSttDetail()
	c := req.SttCtx
	params := req.getParams()
	if req.getError() != nil {
		return req
	}

	// Get checkout payment method
	checkoutPaymentMethod, err := c.checkoutRepo.GetDetailStt(req.getCtx(), &model.CheckoutGetDetailSttParams{
		SttNo: sttDetail.SttNo,
		Token: params.Token,
	})
	if err != nil {
		req.err = shared.NewMultiStringBadRequestError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "An error occurred while get payment method",
			"id": "Terjadi kesalahan pada saat mengambil metode pembayaran",
		})
		return req
	}
	sttDetailResponse.CheckoutPaymentMethod = checkoutPaymentMethod.Data.CheckoutPaymentMethodName
	return req
}

func (req *SttDetailDataRequest) setSttReverseDataResponse(sttDetailResponse *stt.SttDetailResponse) {
	sttMeta := req.getSttMeta()
	c := req.SttCtx
	selfCtx := req.getCtx()

	if sttMeta.DetailSttReverseJourney == nil {
		return
	}
	// infomation related reverse journey (if any)
	sttDetailResponse.DetailSttReverseJourney = &stt.DetailSttReverseJourney{
		RootReverseSttNo:         sttMeta.DetailSttReverseJourney.RootReverseSttNo,
		RootReverseShipmentID:    sttMeta.DetailSttReverseJourney.RootReverseShipmentID,
		RootReverseLastStatusStt: sttMeta.DetailSttReverseJourney.RootReverseLastStatusStt,
	}

	isReverseJourneyC1 := sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` && (shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID) == model.C1 || shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID) == model.C2)
	if !isReverseJourneyC1 {
		return
	}
	shipment, err := c.shipmentRepo.Get(selfCtx, &model.ShipmentViewParams{
		ShipmentAlgoID: sttMeta.DetailSttReverseJourney.RootReverseShipmentID,
	})

	if err != nil || shipment.IsShipmentNotFound() {
		req.err = c.errShipmentNotFound()
		return
	}
	if shipment.ShipmentMeta != nil {
		shipmentMetaStruct := shipment.ShipmentMetaToStruct()
		if shipmentMetaStruct != nil {
			sttDetailResponse.DetailSttReverseJourney.CodHandling = shipmentMetaStruct.CodHandling
		}
	}
}

func (req *SttDetailDataRequest) setRetailPromoTariffResponse(sttDetailResponse *stt.SttDetailResponse) *SttDetailDataRequest {
	// retail promo tarif
	sttMeta := req.getSttMeta()
	if sttMeta != nil && sttMeta.RetailTariff != nil {
		sttDetailResponse.IsPromo = sttMeta.RetailTariff.IsPromo
		sttDetailResponse.SttDiscount = sttMeta.RetailTariff.Discount
		sttDetailResponse.TotalDiscount = sttMeta.RetailTariff.TotalDiscount
		sttDetailResponse.TotalTariffAfterDiscount = sttMeta.RetailTariff.TotalTariffAfterDiscount
		sttDetailResponse.TotalTariffBeforeDiscount = sttMeta.RetailTariff.TotalTariffBeforeDiscount
		sttDetailResponse.SttInsuranceAdminFee = sttMeta.RetailTariff.InsuranceAdminFee

		if sttMeta.RetailTariff.IsPromo {
			sttDetailResponse.SttTotalTariff = sttMeta.RetailTariff.TotalTariffBeforeDiscount
		}
	}

	return req
}

func (req *SttDetailDataRequest) setDiscountResponse(sttDetailResponse *stt.SttDetailResponse) *SttDetailDataRequest {
	sttMeta := req.getSttMeta()

	sttDetailResponse.ListDiscount = []model.PromoDiscount{}
	if sttMeta != nil && len(sttMeta.ListDiscount) > 0 {
		sttDetailResponse.ListDiscount = sttMeta.ListDiscount

		if model.IsShipmentPrefixFavorite[req.ShipmentPrefix] && sttMeta.ListDiscount[len(sttMeta.ListDiscount)-1].Config.PdcID == 0 {
			sttDetailResponse.SttDiscountFavoritePercentage = float64(sttMeta.ListDiscount[len(sttMeta.ListDiscount)-1].Config.PdcPromoDiscount)
		}
	}

	return req
}

func (req *SttDetailDataRequest) setAccessAccountInternal(sttDetailResponse *stt.SttDetailResponse) *SttDetailDataRequest {
	params := req.getParams()
	sttMeta := req.getSttMeta()
	sttDetail := req.getSttDetail()
	checkAccountRoleInternal := params.AccountRefType != model.CONSOLE && sttMeta.DetailSttReverseJourney != nil
	if checkAccountRoleInternal {
		sttDetailResponse.SttIsAllowCancel = model.IsAllowToCancelByAccountRole[params.AccountRoleName] && model.IsAllowToCancelReverseJourney[sttDetail.SttLastStatusID]
		// handle Cancel STT Intracity at status STI Dest
		if sttDetail.SttLastStatusID == model.STIDEST && sttDetail.SttOriginCityID != sttDetail.SttDestinationCityID {
			sttDetailResponse.SttIsAllowCancel = false
		}
	}
	return req
}

func (req *SttDetailDataRequest) setResponseInternalAccountAccess(sttDetailResponse *stt.SttDetailResponse) *SttDetailDataRequest {
	sttMeta := req.getSttMeta()
	params := req.getParams()

	getCATariff := params.AccountType != model.CLIENT && model.IsValidShipmentPrefixCA[req.ShipmentPrefix] && sttMeta != nil && sttMeta.ClientTariff != nil
	if !getCATariff {
		return req
	}

	sttDetailResponse.IsPromo = sttMeta.ClientTariff.AfterDiscount.IsPromo
	sttDetailResponse.SttDiscount = sttMeta.ClientTariff.AfterDiscount.Discount
	sttDetailResponse.TotalDiscount = sttMeta.ClientTariff.AfterDiscount.TotalDiscount
	sttDetailResponse.TotalTariffAfterDiscount = sttMeta.ClientTariff.AfterDiscount.TotalTariff
	sttDetailResponse.TotalTariffBeforeDiscount = sttMeta.ClientTariff.BeforeDiscount.TotalTariff

	if sttMeta.ClientTariff.AfterDiscount.IsPromo {
		sttDetailResponse.SttTotalTariff = sttMeta.ClientTariff.BeforeDiscount.TotalTariff
	}

	return req
}

func (req *SttDetailDataRequest) setGoodsNames(sttDetailResponse *stt.SttDetailResponse) *SttDetailDataRequest {
	sttMeta := req.getSttMeta()

	sttDetailResponse.GoodsNames = []string{}
	if sttMeta != nil && len(sttMeta.GoodsNames) > 0 {
		sttDetailResponse.GoodsNames = sttMeta.GoodsNames
	}

	return req
}

func (c *sttCtx) getInternalAccountAccess(req *SttDetailDataRequest) {
	params := req.getParams()
	sttDetail := req.getSttDetail()
	sttHistories := req.getSttHistories()

	if !params.IsWithEligibility {
		return
	}

	req.IsAllowToEdit, req.IsAllowToCancel = c.IsAllowEditAndCancelStt(req.getCtx(), model.SttDetailResult{Stt: *sttDetail}, req.SttMeta)

	if sttDetail.SttLastStatusID == model.STTADJUSTED {

		for _, history := range sttHistories {
			if !model.IsInternalAfterStatusPUP[history.HistoryStatus] {
				req.IsAllowToCancel = false
				break
			}
		}
	}

	req.checkSamedayEditAccess()
}

func (c *sttCtx) validateAccountClient(req *SttDetailDataRequest) bool {
	params := req.getParams()
	sttDetail := req.getSttDetail()

	if !params.IsAuth {
		return true
	}
	client, err := c.clientRepo.GetByID(req.getCtx(), sttDetail.SttClientID, params.Token)
	if err != nil || client == nil {
		req.err = c.errClientNotFound()
		return true
	}

	if client.Data.ClientParentID != params.AccountRefID && sttDetail.SttClientID != params.AccountRefID {
		req.err = shared.ERR_STT_NOT_FOUND
		return true
	}

	return true
}

func (c *sttCtx) checkClientEligibility(req *SttDetailDataRequest) bool {
	params := req.getParams()
	sttDetail := req.getSttDetail()

	clientID := sttDetail.SttClientID
	if sttDetail.SttClientID == params.AccountRefID {
		clientID = params.AccountRefID
	}
	client, err := c.clientRepo.GetByID(req.getCtx(), clientID, params.Token)
	if err != nil || client == nil {
		req.err = c.errClientNotFound()
		return true
	}

	if c.checkClientByAccountRef(req, client) {
		return true
	}

	if sttDetail.SttLastStatusID == model.BKD {
		req.IsAllowToEdit = true
	}

	if sttDetail.SttLastStatusID == model.STTADJUSTED {
		req.IsAllowToEdit = true
		req.IsAllowToCancel = true
		c.checkPosAccessBySttHistories(req)
	}

	if sttDetail.SttBookedBy != params.AccountRefID {
		req.IsAllowToEdit = false
	}

	return true
}

func (c *sttCtx) checkClientByAccountRef(req *SttDetailDataRequest, client *model.Client) bool {
	params := req.getParams()
	sttDetail := req.getSttDetail()

	if sttDetail.SttClientID == params.AccountRefID {
		if model.IsAllowToCancelByClientBranch[sttDetail.SttLastStatusID] {
			req.IsAllowToCancel = true
		}
		return false
	}

	if client.Data.ClientParentID != params.AccountRefID {
		req.err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid Account Type format",
			"id": "Format Account Type tidak valid",
		})
		return true
	}

	if model.IsAllowToCancelByClientParent[sttDetail.SttLastStatusID] {
		req.IsAllowToCancel = true
	}
	return false
}

func (c *sttCtx) checkPosAccessBySttHistories(req *SttDetailDataRequest) {
	sttHistories := req.getSttHistories()
	for _, history := range sttHistories {
		if !model.IsAllowToCancelByPos[history.HistoryStatus] {
			req.IsAllowToEdit = false
			req.IsAllowToCancel = false
			break
		}
	}
}

func (c *sttCtx) checkPartnerConsoleEligibility(req *SttDetailDataRequest) {
	params := req.getParams()
	sttDetail := req.getSttDetail()
	sttMeta := req.getSttMeta()
	var err error

	if req.getError() != nil || params.AccountRefType != model.CONSOLE {
		return
	}

	partnerParent, err := c.partnerRepo.CheckPartnerParent(req.getCtx(), sttDetail.SttBookedBy, params.AccountRefID, params.Token)
	if err != nil || partnerParent == nil {
		req.err = shared.ERR_UNEXPECTED_DB
		return
	}

	isNotHasAccess := (!partnerParent.Data.IsValid || sttDetail.SttBookedByType != model.POS) && params.IsAuth
	if isNotHasAccess {
		req.err = shared.ERR_STT_NOT_FOUND
		return
	}

	c.checkPartnerConsoleSTIorREJECTED(req)
	if err = req.getError(); err != nil {
		return
	}

	// handle Cancel STT Intracity at status STI Dest
	if sttDetail.SttLastStatusID == model.STIDEST && sttDetail.SttOriginCityID == sttDetail.SttDestinationCityID {
		req.IsAllowToCancel = true
	}

	// check detail stt reverse journey and last status stt not rejected
	if sttMeta.DetailSttReverseJourney != nil && sttDetail.SttLastStatusID != model.REJECTED {
		req.IsAllowToCancel = false
	}

	hasNotEditAccess := req.IsShipmentMarketplace && sttDetail.SttLastStatusID == model.STI && !shipmentPrefixAllowEdit(sttDetail)
	if hasNotEditAccess {
		req.IsAllowToEdit = false
	}

	req.checkSamedayEditAccess()
}

func (c *sttCtx) checkPartnerConsoleSTIorREJECTED(req *SttDetailDataRequest) {
	params := req.getParams()
	sttDetail := req.getSttDetail()
	sttHistories := req.getSttHistories()
	if !(sttDetail.SttLastStatusID == model.STI || sttDetail.SttLastStatusID == model.REJECTED) {
		return
	}

	partnerConsole, err := c.partnerRepo.GetByID(req.getCtx(), params.AccountRefID, params.Token)
	if err != nil || partnerConsole.IsEmptyParterLocation() {
		req.err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner is not found",
			"id": "Partner tidak ditemukan",
		})
		return
	}

	historySTI := sttHistories[0].HistoryActorID == params.AccountRefID && sttHistories[0].HistoryStatus == model.STI
	hasAccessForSTI := partnerConsole.Data.PartnerLocation.CityCode == sttDetail.SttOriginCityID || historySTI
	if hasAccessForSTI {
		req.IsAllowToEdit = true
		req.IsAllowToCancel = true
	}

	if sttHistories[0].HistoryStatus == model.REJECTED && sttHistories[0].HistoryActorID == params.AccountRefID {
		req.IsAllowToEdit = false
		req.IsAllowToCancel = true
	}

}

func (c *sttCtx) checkPartnerPOSEligibility(req *SttDetailDataRequest) {
	params := req.getParams()

	if req.getError() != nil {
		return
	}

	if params.AccountRefType != model.POS {
		return
	}

	if req.checkPartnerPOSEligibilityByLastStatus().getError() != nil {
		return
	}

	req.checkSamedayEditAccess()
}

func (req *SttDetailDataRequest) checkPartnerPOSEligibilityByLastStatus() *SttDetailDataRequest {
	sttDetail := req.getSttDetail()
	switch sttDetail.SttLastStatusID {
	case model.BKD:
		req.IsAllowToEdit = true
		req.checkShipmentMarketplaceDontAllowEdit()
		req.IsAllowToCancel = true
	case model.PUP:
		req.checkShipmentMarketplaceDontAllowEdit()
		req.IsAllowToCancel = true
	case model.STTADJUSTED:
		req.IsAllowToEdit = true
		req.checkShipmentMarketplaceDontAllowEdit()
		req.IsAllowToCancel = true
		req.checkSttAdjustedAccessByHiestories()
	case model.PUPC:
		req.checkPartnerPOSPUPAccess()
	}
	return req
}

func (req *SttDetailDataRequest) checkPartnerPOSPUPAccess() {
	c := req.SttCtx
	params := req.getParams()
	pos, err := c.partnerRepo.GetByID(req.getCtx(), params.AccountRefID, params.Token)
	if err != nil {
		req.err = shared.ERR_UNEXPECTED_DB
		return
	}
	if pos.Data.ID < 1 {
		req.err = shared.ERR_STT_NOT_FOUND
		return
	}
	if pos.Data.PartnerPosParentID > 0 {
		req.IsAllowToCancel = true
	}
}

func (req *SttDetailDataRequest) checkSamedayEditAccess() {
	if req.getSttDetail().SttProductType == model.SAMEDAY {
		req.IsAllowToEdit = false
	}
}

func (req *SttDetailDataRequest) checkShipmentMarketplaceDontAllowEdit() {
	if req.IsShipmentMarketplace && !shipmentPrefixAllowEdit(req.getSttDetail()) {
		req.IsAllowToEdit = false
	}
}

func (c *sttCtx) errShipmentNotFound() *shared.MultiStringBadRequestError {
	return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "An error occurred while getting shipment",
		"id": "Terjadi kesalahan pada saat query getting shipment",
	})
}

func (c *sttCtx) errClientNotFound() *shared.MultiStringValidationError {
	return shared.NewMultiStringValidationError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "Failed get data client",
		"id": "Gagal memperoleh data client",
	})
}

func (c *sttCtx) getSttHistories(selfCtx context.Context, sttDetail *model.Stt, params *stt.SttDetailRequest) ([]model.SttPieceHistory, error) {
	sttHistories := []model.SttPieceHistory{}
	if !params.IsWithEligibility {
		return sttHistories, nil
	}
	sttPiece, err := c.sttPiecesRepo.Get(selfCtx, &model.SttPiecesViewParam{
		SttID: int(sttDetail.SttID),
	})
	if err != nil || sttPiece == nil {
		return sttHistories, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt Piece is not found",
			"id": "Stt Piece tidak ditemukan",
		})
	}

	sttHistories, err = c.sttPieceHistoryRepo.Select(selfCtx, &model.SttPieceHistoryViewParam{
		Order:                     true,
		OrderDesc:                 true,
		SttPieceHistorySttPieceID: sttPiece.SttPieceID,
	})

	if err != nil || len(sttHistories) == 0 {
		return sttHistories, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt History is not found",
			"id": "Stt History tidak ditemukan",
		})
	}

	return sttHistories, nil
}

func shipmentPrefixAllowEdit(sttDetail *model.Stt) bool {

	if shared.IsLiloPrefix(sttDetail.SttNoRefExternal) && !shared.IsBagNoReff(sttDetail.SttNoRefExternal) {
		return true
	}

	if len(sttDetail.SttShipmentID) > 4 {
		return model.MappingAllowedEditMapping[shared.GetPrefixShipmentID(sttDetail.SttShipmentID)]
	}

	if model.MappingAllowedEditMapping[shared.GetPrefixSttNo(sttDetail.SttNo)] {
		return true
	}

	return false
}

func (req *SttDetailDataRequest) checkSttRefundRemark() {
	for _, history := range req.SttHistories {
		if !(history.HistoryStatus == model.REJECTED && req.SttDetail.SttLastStatusID == model.REJECTED) {
			continue
		}

		remarkPieceHistory := history.RemarkPieceHistoryToStruct()
		if remarkPieceHistory != nil {
			req.RefundRemarks = remarkPieceHistory.CustomProcessRemarks
		}
	}
}
