package usecase

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	gatewaySttTracking "github.com/Lionparcel/hydra/src/usecase/gateway_stt_tracking"
	"github.com/Lionparcel/hydra/src/usecase/predefined_holiday"
)

func (c *gatewaySttTrackingCtx) SttTrackingBulkV4WithReverse(ctx context.Context, params *gatewaySttTracking.SttTrackingBulkRequestV4) (*gatewaySttTracking.SttTrackingBulkResponseV4, error) {
	var err error
	respSttTracking := new(gatewaySttTracking.SttTrackingBulkResponseV4)
	opName := "STTV4Usecase-SttTrackingBulkV4"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": respSttTracking, "error": err})
	}()

	if err = params.Validate(); err != nil {
		return nil, err
	}

	errQueryDB := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "An error occurred while querying db", "id": "Terjadi kesalahan pada saat query db",
	})

	// Create response stt tracking
	respSttTracking.Stts = make([]gatewaySttTracking.SttTrackingBulkV4, 0)
	cityMapping := make(map[string]*model.City)
	for _, sttNo := range params.SttNo {
		sttNo = strings.TrimSpace(sttNo)
		if sttNo == `` {
			continue
		}

		// Start
		sttNumber := sttNo
		bkdDate := ""
		var rootStt *model.SttDetailResult
		internalCodeCache := new(sync.Map)
		allHistories := make([]gatewaySttTracking.SttHistoryV4, 0)
		for i := 0; i <= 2; i++ {

			dataStt, err := c.sttPiecesRepo.GetDetail(selfCtx, &model.SttViewParams{
				Search:            sttNumber,
				IsSearchByPattern: true,
			})
			if err != nil {
				return nil, errQueryDB
			}

			if dataStt == nil {
				//check again data with bookingID or shipmentID
				if !shared.IsShipmentIDFormat(sttNumber) {
					continue
				}
				switch shared.GetPrefixShipmentID(sttNumber) {
				case model.TKLP, model.TSLP:
					shipment, err := c.shipmentRepo.GetShipment(ctx, &model.ShipmentViewParams{
						ShipmentBookingID: sttNumber,
					})
					if err != nil {
						return nil, errQueryDB
					}
					if shipment != nil {
						dataStt, err = c.sttPiecesRepo.GetDetail(ctx, &model.SttViewParams{
							Search:            shipment.ShipmentShipmentID,
							IsSearchByPattern: true,
						})
						if err != nil {
							return nil, errQueryDB
						}
					}
				case model.T1:
					shipment, err := c.shipmentRepo.GetShipment(ctx, &model.ShipmentViewParams{
						ShipmentShipmentID: sttNumber,
					})
					if err != nil {
						return nil, errQueryDB
					}
					if shipment != nil {
						dataStt, err = c.sttPiecesRepo.GetDetail(ctx, &model.SttViewParams{
							Search:            shipment.ShipmentBookingID,
							IsSearchByPattern: true,
						})
						if err != nil {
							return nil, errQueryDB
						}
					}
				}
				if dataStt == nil {
					continue
				}
			}

			if i == 0 {
				rootStt = dataStt
			}

			productType := dataStt.SttProductType

			cityCountry, err := c.cityRepo.Get(ctx, rootStt.SttDestinationCityID, params.Token)
			if err != nil || cityCountry == nil {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Failed to get location city data ", "id": "Gagal mendapatkan data kota lokasi",
				})
			}

			countryName := cityCountry.Country.Name

			sttHistory, err := c.sttPieceHistoryRepo.Select(selfCtx, &model.SttPieceHistoryViewParam{
				SttPieceHistorySttPieceID: dataStt.SttPieceID,
				Order:                     true,
			})
			if err != nil {
				return nil, errQueryDB
			}

			dataDelivery, err := c.deliveryRepo.Select(ctx, &model.DeliveryViewParam{SttNoIn: []string{sttNumber}, OrderBy: "delivery.id", SortBy: model.SortByAsc})
			if err != nil {
				return nil, errQueryDB
			}

			// Mapping data sttHistory
			listSttPieceHistoryByHistoryID := map[int][]model.SttPieceHistory{}
			for _, historyData := range sttHistory {
				listSttPieceHistoryByHistoryID[historyData.HistoryID] = append(listSttPieceHistoryByHistoryID[historyData.HistoryID], historyData)
			}
			iDataDelivery := 0
			histories := make([]gatewaySttTracking.SttHistoryV4, 0)
			for j, val := range sttHistory {
				// parsing history remark
				historyRemark := val.RemarkPieceHistoryToStruct()
				podRecipientRelation := model.PodRecipientRelation{}
				if iDataDelivery < len(dataDelivery) {
					finishedStatus := dataDelivery[iDataDelivery].FinishedStatus
					if finishedStatus != nil && *finishedStatus == val.HistoryStatus {
						podRecipientRelation = dataDelivery[iDataDelivery].LoadRemark().PodRecipientRelation
						iDataDelivery++
					}
				}
				countryNameHistory := ""
				countryCode := ""
				timeZoneCity := ""
				if val.HistoryLocation != "" {
					city, err := c.cityRepo.Get(ctx, val.HistoryLocation, params.Token)
					if err != nil || city == nil {
						return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
							"en": "Failed to get location city data ",
							"id": "Gagal mendapatkan data kota lokasi",
						})
					}

					countryCode = city.Country.Code
					timeZoneCity = city.Timezone
					countryNameHistory = city.Country.Name
					if historyRemark == nil {
						historyRemark = &model.RemarkPieceHistory{HistoryLocationName: city.Name}
					} else {
						if historyRemark.HistoryLocationName == "" {
							historyRemark.HistoryLocationName = city.Name
						}
					}
					if historyRemark.ActorExternalCode == model.LuwjistikName {
						if model.IsGetCityFromHistoryLocation[val.HistoryStatus] {
							historyRemark.HistoryLocationName = city.Name
						}
					}
				}
				if val.HistoryStatus == model.DEL && val.HistoryActorID > 0 {
					if historyRemark.ActorExternalCode == `` {
						partner, err := c.partnerRepo.GetByID(selfCtx, val.HistoryActorID, params.Token)
						if err != nil || partner == nil {
							return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
								"en": "Failed to get partner data ",
								"id": "Gagal mendapatkan data partner",
							})
						}
						historyRemark.ActorExternalCode = partner.Data.PartnerExternalCode
					}
				}
				// Checking status allow to be show
				// This usecase is only used by KR Tokopedia and will be validated in Pegasus Service
				//if model.SttStatusForSttTrackingClient[val.HistoryStatus] == "" {
				//	continue
				//}
				// get bkd date
				if val.HistoryStatus == model.BKD && j == 0 {
					bkdDate = val.HistoryCreatedAt.Format(shared.FormatDateTime)
				}
				internalCodeCache.Store("status", val.HistoryStatus)
				historyData := gatewaySttTracking.SttHistoryV4{
					Row:         len(allHistories) + j + 1,
					StatusCode:  strings.ToUpper(val.HistoryStatus),
					Location:    strings.ToUpper(val.HistoryLocation),
					City:        strings.ToUpper(historyRemark.HistoryLocationName),
					Remarks:     c.generateSttTrackingDescriptionForClient(val.HistoryStatus, val.HistoryID, listSttPieceHistoryByHistoryID, params.Token, productType, countryName, params.ReferenceType, countryNameHistory),
					ShortStatus: model.SttStatusForSttTrackingClient[val.HistoryStatus],
					Datetime:    val.HistoryCreatedAt.UTC(),
					UpdatedBy:   strings.ToUpper(val.HistoryCreatedName),
					UpdatedOn:   val.HistoryCreatedAt.UTC(),
					CountryCode: countryCode,
					GMT:         timeZoneCity,
					Attachment: func() []string {
						if len(historyRemark.Attactments) > 0 {
							return historyRemark.Attactments
						}
						return []string{}
					}(),
					ProblemReasonCode: c.getReasonCode(selfCtx, &val, sttNumber),
				}

				historyData.Remarks = c.maskingDescriptionDex(historyData.ProblemReasonCode, historyData.Remarks)
				historyData.SetProof(historyRemark, podRecipientRelation)
				histories = append(histories, historyData)
			}

			if len(histories) > 0 {
				allHistories = append(allHistories, histories...)
			}

			dataSttRef, err := c.sttPiecesRepo.GetDetail(ctx, &model.SttViewParams{SttNoRefExternal: dataStt.SttNo})
			if err != nil {
				break
			}
			if dataSttRef != nil && model.IsPrefixValidReverseJourney[shared.GetPrefixSttNo(dataSttRef.SttNo)] {
				sttNumber = dataSttRef.SttNo
			} else {
				break
			}

		}

		sttMeta := rootStt.SttMetaToStruct()
		if sttMeta == nil {
			var originCity *model.City
			if cityMapping[rootStt.SttOriginCityID] == nil {
				originCity, err := c.cityRepo.Get(selfCtx, rootStt.SttOriginCityID, params.Token)
				if err != nil || originCity == nil {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Failed to get origin city data ",
						"id": "Gagal mendapatkan data kota asal",
					})
				}
				cityMapping[rootStt.SttOriginCityID] = originCity
			}
			originCity = cityMapping[rootStt.SttOriginCityID]

			var destinationCity *model.City
			if cityMapping[rootStt.SttDestinationCityID] == nil {
				destinationCity, err := c.cityRepo.Get(selfCtx, rootStt.SttDestinationCityID, params.Token)
				if err != nil || destinationCity == nil {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Failed to get destination city data ",
						"id": "Gagal mendapatkan data kota tujuan",
					})
				}
				cityMapping[rootStt.SttDestinationCityID] = destinationCity
			}
			destinationCity = cityMapping[rootStt.SttDestinationCityID]

			// parsing stt remark
			sttMeta = &model.SttMeta{
				OriginCityName:      originCity.Name,
				DestinationCityName: destinationCity.Name,
			}
		}

		// Get estimate sla
		estimateSla := `-`
		if sttMeta != nil {
			estimateSla = sttMeta.EstimateSLA
		}
		// Trimming estimateSla string format
		estimateSlaMin := ""
		estimateSlaMax := ""
		trimEstimateSlString := strings.Replace(estimateSla, " ", "", -1)
		trimEstimateSlString = strings.TrimSpace(trimEstimateSlString)
		splitEstimateSlaTrim := strings.Split(trimEstimateSlString, "-")
		if len(splitEstimateSlaTrim) > 1 {
			estimateSlaMin = splitEstimateSlaTrim[0]
			estimateSlaMax = strings.Replace(splitEstimateSlaTrim[1], "Hari", "", -1)
		}

		/*
		 * Checking rebuttal dex status
		 */
		rebuttalDex := ""
		if rootStt.SttLastStatusID == model.DEX {
			dataRebuttalDex, err := c.rebuttalDexRepo.GetBySttNo(ctx, rootStt.SttNo)
			if err == nil && dataRebuttalDex != nil && dataRebuttalDex.Status != "" {
				rebuttalDex = model.Rebuttal
			}
		}

		var sttNoRef string
		if model.IsSttReturnForRtsRtshq[rootStt.SttLastStatusID] {
			dataSttRef, err := c.sttPiecesRepo.GetDetail(ctx, &model.SttViewParams{
				SttNoRefExternal: rootStt.SttNo,
			})
			if err != nil {
				return nil, errQueryDB
			}
			if dataSttRef != nil {
				sttNoRef = dataSttRef.SttNo
			}
		}
		prefix := shared.GetPrefixSttNo(rootStt.SttNo)
		if model.IsPrefixReverseJourney[prefix] {
			sttNoRef = rootStt.SttNoRefExternal
		}

		sttTracking := &gatewaySttTracking.SttTrackingBulkV4{
			SttNo: strings.ToUpper(func() string {
				if rootStt.SttElexysNo.Valid {
					return rootStt.SttElexysNo.Value()
				}
				return rootStt.SttNo
			}()),
			Sender: gatewaySttTracking.PersonObjectV4{
				Name:    strings.ToUpper(rootStt.SttSenderName),
				Address: rootStt.SttSenderAddress,
				Phone:   rootStt.SttSenderPhone,
			},
			Recipient: gatewaySttTracking.PersonObjectV4{
				Name:    strings.ToUpper(rootStt.SttRecipientName),
				Address: rootStt.SttRecipientAddress,
				Phone:   rootStt.SttRecipientPhone,
			},
			Origin:           strings.ToUpper(fmt.Sprintf(`%s (%s)`, sttMeta.OriginCityName, rootStt.SttOriginCityID)),
			Destination:      strings.ToUpper(fmt.Sprintf(`%s (%s)`, sttMeta.DestinationCityName, rootStt.SttDestinationCityID)),
			CurrentStatus:    strings.ToUpper(rootStt.GetSttMetaDetailReverseJourney().ReverseLastStatusStt),
			ChargeableWeight: rootStt.SttChargeableWeight,
			ServiceType:      model.PACKAGESERVICE,
			ProductType:      rootStt.SttProductType,
			Pieces:           rootStt.SttTotalPiece,
			GrossWeight:      rootStt.SttGrossWeight,
			VolumeWeight:     rootStt.SttVolumeWeight,
			ShipmentID:       rootStt.SttShipmentID,
			TotalAmount:      rootStt.SttTotalAmount,
			CodValue: func() float64 {
				if rootStt.SttIsCOD {
					return rootStt.SttCODAmount
				}
				return 0
			}(),
			ExternalID: func() string {
				if rootStt.SttIsDO {
					return rootStt.SttNoRefExternal
				}
				if rootStt.SttMetaToStruct() != nil {
					if rootStt.SttMetaToStruct().TicketCode != `` {
						return rootStt.SttMetaToStruct().TicketCode
					}
				}
				return ""
			}(),
			History:        allHistories,
			RebuttalDex:    rebuttalDex,
			SttReturnRefNo: sttNoRef,
		}

		/*
		 * Generate exact date from `estimateSla` string
		 */
		estimateSlaResp, err := c.predefinedHolidayRepo.CheckDate(ctx, &model.CredentialRestAPI{
			Token: params.Token,
		}, predefined_holiday.RequestSundayAndHolidayCheck{
			StartDate:   bkdDate,
			Min:         estimateSlaMin,
			Max:         estimateSlaMax,
			ProductType: rootStt.SttProductType,
		})
		if err == nil {
			min, _ := time.Parse(shared.NgenFormatDateTimeZone, estimateSlaResp.Min)
			max, _ := time.Parse(shared.NgenFormatDateTimeZone, estimateSlaResp.Max)
			sttTracking.EstimationDate = fmt.Sprintf("%s - %s", min.Format(shared.FormatDate), max.Format(shared.FormatDate))
		}

		if val, ok := internalCodeCache.Load("status"); ok && val != nil {
			sttTracking.CurrentStatus = val.(string)
		}

		respSttTracking.Stts = append(respSttTracking.Stts, *sttTracking)
	}

	calculateAttemptSttBulkWithReverse(respSttTracking)
	return respSttTracking, nil
}

func (c *gatewaySttTrackingCtx) getReasonCode(ctx context.Context, param *model.SttPieceHistory, sttNo string) (historyReason string) {
	switch param.HistoryStatus {
	case model.DEX, model.CODREJ, model.CNX, model.REJECTED:
		historyReason = param.HistoryReason
		if param.HistoryReasonOrigin != "" {
			historyReason = param.HistoryReasonOrigin
		}
	case model.RTS:
		sttPieceHistoryStatusDex, _ := c.sttPieceHistoryRepo.SelectBySttNo(ctx, &model.SttPieceHistoryViewParam{
			SttNoIn:                      []string{sttNo},
			SttPieceHistoryStatusWhereIn: []string{model.DEX, model.CODREJ},
			SortBy:                       `sph.history_created_at`,
			OrderDesc:                    true,
			Limit:                        1,
		})
		if len(sttPieceHistoryStatusDex) > 0 {
			historyReason = sttPieceHistoryStatusDex[0].HistoryReason
		}
	}

	return
}

func (c *gatewaySttTrackingCtx) maskingDescriptionDex(reasonCode string, remarks string) string {

	if c.cfg.ReasonDexMaskingDiff() != reasonCode {
		return remarks
	}

	oldValue := `Percobaan pengiriman ulang akan dilakukan secara berkala. Pastikan alamat dan kontak Penerima sudah sesuai.`
	newValue := `Silakan hubungi CS untuk mendapatkan informasi lebih lanjut.`
	return strings.ReplaceAll(remarks, oldValue, newValue)

}
