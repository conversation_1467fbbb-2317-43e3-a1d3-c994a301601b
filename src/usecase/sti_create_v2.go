package usecase

import (
	"context"
	"strconv"
	"strings"
	"time"

	"github.com/abiewardani/dbr/v2"

	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/src/usecase/sti_temporary"
	"github.com/Lionparcel/hydra/src/usecase/stt_due"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/predefined_holiday"
	"github.com/Lionparcel/hydra/src/usecase/sti"
	"github.com/Lionparcel/hydra/src/usecase/sti_sc"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *stiCtx) CreateSTIV2(ctx context.Context, data *sti.CreateStiRequest) (result sti.ResponseCreateV2, err error) {
	token := ctx.Value(`tokenStr`).(string)
	result = sti.ResponseCreateV2{}
	opName := "UsecaseSti-CreateSTIV2"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": data, "result": result})
	}()

	now := c.timeRepo.Now(time.Now())
	bagNoVendor := c.generateBagVendorLilo(data.BagOrSttNo)
	isBagLilo := bagNoVendor != ""

	fnGetSttDetail := map[bool]func(ctx context.Context, bagNo string, useValidateSttBagLilo bool) ([]model.SttDetailResult, []string, error){
		true:  c.getSttDetailByBagLilo,
		false: c.getSttDetailBySttPatern,
	}

	sttDetail, stiSttNo, err := fnGetSttDetail[isBagLilo](selfCtx, data.BagOrSttNo, !isBagLilo)
	if err != nil {
		return result, err
	}

	data.StiSttNo = append(data.StiSttNo, stiSttNo...)

	// validate lilo
	sttNoIsCheckedLilo := map[string]bool{}
	for _, sttRow := range sttDetail {
		if sttNoIsCheckedLilo[sttRow.SttNo] {
			continue
		}

		sttNoIsCheckedLilo[sttRow.SttNo] = true

		validationRes, err := c.sttUc.ValidateStatusIncomingLILO(selfCtx, &stt.ValidateShipmentLiloRequest{
			Stt:          sttRow.Stt,
			SttPieceID:   sttRow.SttPieceID,
			UpdateStatus: model.STI,
		})
		if err != nil {
			return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting status incoming LILO",
				"id": "Terjadi kesalahan pada saat status incoming LILO",
			})
		}

		if !validationRes.IsBaggingAfterBkd && !model.IsAllowUpdateToSTI[sttRow.SttLastStatusID] {
			return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "STT can not be processed",
				"id": "STT tidak dapat diproses",
			})
		}

		// check if already update to STI before
		if sttRow.SttPieceLastStatusID == model.CLAIM {
			history, err := c.repoSTTPieceHistory.Get(selfCtx, &model.SttPieceHistoryViewParam{
				SttPieceHistorySttPieceID: sttRow.SttPieceID,
				SttPieceHistoryStatus:     model.STI,
			})

			if err != nil || history != nil {
				return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "STT can not be processed",
					"id": "STT tidak dapat diproses",
				})
			}
		}

		// if last status STT ADJUSTED
		if sttRow.SttLastStatusID == model.STTADJUSTED {
			sttHistory, err := c.repoSTTPieceHistory.Get(selfCtx, &model.SttPieceHistoryViewParam{
				SttPieceHistorySttPieceID: sttRow.SttPieceID,
				SttPieceHistoryStatus:     model.STI,
			})
			if err != nil {
				return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "An error occurred while getting STT Piece History",
					"id": "Terjadi kesalahan pada saat getting STT Piece History",
				})
			}

			// if stt adjustment after STI
			if sttHistory != nil {
				return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "STT can not be processd STI 2 times",
					"id": "STT Tidak dapat diproses STI 2 kali",
				})
			}
		}
	}

	if err = data.Validate(); err != nil {
		return result, err
	}

	uniqueSttNo := shared.Unique(data.StiSttNo)

	// validation stt is exist
	sttNoIsPaid := map[string]bool{}
	for k := 0; k < len(uniqueSttNo); k++ {
		stt, err := c.repoStt.Get(selfCtx, &model.SttViewDetailParams{
			Stt: model.Stt{
				SttNo: uniqueSttNo[k],
			},
		})
		if err != nil || stt == nil {
			return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting stt no",
				"id": "Terjadi kesalahan pada saat stt no",
			})
		}

		isPaid := false
		if stt.SttPaymentStatus == "" || stt.SttPaymentStatus == model.PAID {
			isPaid = true
		}
		if shared.GetPrefixSttNo(stt.SttNo) == model.PrefixAutoCA && stt.SttShipmentID != `` && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(stt.SttShipmentID)] {
			isPaid = true
		}

		if !isPaid && stt.SttOriginCityID == stt.SttDestinationCityID {
			return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "STT unpaid/underpaid",
				"id": "STT belum/kurang bayar",
			})
		}
		sttNoIsPaid[stt.SttNo] = isPaid
	}

	isStiDestList := make(map[string]int)
	mappingMeta := make(map[string]model.StiTemporaryMeta)
	listPdDeadlineReturn := make(map[string]time.Time)

	req, err := c.getCreateStiRequestParams(selfCtx, data, token, now, false)
	if err != nil {
		return result, err
	}

	if len(req.ListSttSuccess) > 0 {
		// STI Params Request
		STIData := &model.Sti{
			StiPartnerID:         req.PartnerID,
			StiPartnerCode:       req.PartnerCode,
			StiPartnerName:       req.PartnerName,
			StiOriginCityCode:    req.OriginCityCode,
			StiOriginCityName:    req.OriginCityName,
			StiTotalStt:          len(uniqueSttNo),
			StiTotalGrossWeight:  req.TotalGrossWeight,
			StiTotalVolumeWeight: req.TotalVolumeWeight,
			StiTotalPieces:       req.TotalPieces,
			StiStatusType:        model.STI,
			StiStatusUpdatedAt:   now,
			StiCreatedAt:         now,
			StiCreatedBy:         req.STICreatedBy,
			StiCreatedName:       req.STICreatedByName,
			StiUpdatedAt:         &req.STIUpdatedAt,
		}

		// STI Detail Params
		stiDetailRequest := &sti_sc.CreateStiScDetailParams{
			StiDetail: req.StiDetail,
			History:   req.SttPieceHistory,
			Stt:       req.Stt,
			Sti:       &model.Sti{StiID: STIData.StiID},
		}

		// Create STI Detail, History, Update Last Status
		err = c.repoStiDetail.UpdateSttAndPieceToSTI(selfCtx, stiDetailRequest)
		if err != nil {
			// DELETE STI
			c.repoSti.Delete(selfCtx, &model.StiViewParams{
				StiID: int(STIData.StiID),
			})

			return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed Created STI Manifest Detail",
				"id": "Gagal Membuat STI Manifest Detail",
			})
		}
	} else {
		return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed To STI",
			"id": "Gagal Di STI",
		})
	}

	sttNoDue := make([]string, 0)
	sttBookedResult := make(map[string]sti.BookingSTTResult)
	for k := 0; k < len(uniqueSttNo); k++ {
		stt, err := c.repoStt.Get(selfCtx, &model.SttViewDetailParams{
			Stt: model.Stt{
				SttNo: uniqueSttNo[k],
			},
		})
		if err != nil || stt == nil {
			return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting bag vendor",
				"id": "Terjadi kesalahan pada saat bag vendor",
			})
		}
		if shared.IsBagNoReff(stt.SttNoRefExternal) {
			continue
		}

		meta := model.PriorityDeliveryMeta{}

		sttMeta := stt.SttMetaToStruct()
		var deadlineReturn time.Time

		// Logic SLA
		if sttMeta != nil {
			estimateSLA := strings.TrimSpace(strings.ToLower(sttMeta.EstimateSLA))
			estimateSLA = strings.ReplaceAll(estimateSLA, "hari", "")
			splitEstimateSLA := strings.Split(estimateSLA, "-")

			estimateSlaMin := ""
			estimateSlaMax := ""
			if len(splitEstimateSLA) > 1 {
				estimateSlaMin = strings.TrimSpace(splitEstimateSLA[0])
				estimateSlaMax = strings.TrimSpace(splitEstimateSLA[1])
			}

			estimateSlaResp, err := c.predefinedHolidayRepo.CheckDate(selfCtx, &model.CredentialRestAPI{
				Token: data.Token,
			}, predefined_holiday.RequestSundayAndHolidayCheck{
				StartDate:   stt.SttBookedAt.Format(shared.FormatDateTime),
				Min:         estimateSlaMin,
				Max:         estimateSlaMax,
				ProductType: stt.SttProductType,
			})
			if err == nil {
				max, _ := time.Parse(shared.NgenFormatDateTimeZone, estimateSlaResp.Max)
				deadlineReturn, _ = shared.ParseUTC7(shared.FormatDate, max.Format(shared.FormatDate))
			}
		}

		listPdDeadlineReturn[stt.SttNo] = deadlineReturn
		isStiDest := 0
		if stt.SttOriginCityID == stt.SttDestinationCityID {
			isStiDest = 1
		}

		priorityDelivery, err := c.PriorityDeliveryRepo.GetBySttNo(selfCtx, stt.SttNo)
		if err != nil {
			return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting Priority Delivery",
				"id": "Terjadi kesalahan pada saat Priority Delivery",
			})
		}
		var pdData *model.PriorityDelivery

		if isStiDest == 1 {
			pdData = &model.PriorityDelivery{
				PdSttNo:          stt.SttNo,
				PdDeadlineReturn: deadlineReturn,
				PdStatusReturn:   "POD",
				PdFlag:           "",
				PdPriority:       50,
				PdMeta:           meta.ToString(),
				PdIsShow:         1,
				PdUpdatedAt:      now,
			}
			if stt.SttProductType == model.JUMBOPACKH2H {
				pdData.PdStatusReturn = "Diambil penerima"
				pdData.PdFlag = "Diambil penerima"
			}

			if priorityDelivery == nil {
				pdData.PdCreatedAt = now
				err = c.PriorityDeliveryRepo.Insert(selfCtx, pdData)
				if err != nil {
					return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "An error occurred while inserting Priority Delivery",
						"id": "Terjadi kesalahan pada saat insertPriority Delivery",
					})
				}
				priorityDelivery = pdData
			}
			if priorityDelivery != nil && priorityDelivery.PdIsShow == 0 {
				pdData.PdID = priorityDelivery.PdID
				pdData.PdCreatedAt = priorityDelivery.PdCreatedAt
				pdData.PdCreatedBy = priorityDelivery.PdCreatedBy
				pdData.PdCreatedName = priorityDelivery.PdCreatedName
				err = c.PriorityDeliveryRepo.Update(selfCtx, pdData)
				if err != nil {
					return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "An error occurred while update Priority Delivery",
						"id": "Terjadi kesalahan pada saat update Priority Delivery",
					})
				}
				priorityDelivery = pdData
			}
		}

		bagDetail, _ := c.bagDetailRepo.Get(selfCtx, &model.BagDetailViewParams{
			BagDetail: model.BagDetail{
				BagDetailSttID: &stt.SttID,
			},
		})

		bagNo := ""
		if bagDetail != nil && bagDetail.BagCode != nil {
			bagNo = *bagDetail.BagCode
		}

		if bagNoVendor != "" {
			bagNo = bagNoVendor
		}

		refNo := ""
		if stt.SttShipmentID != "" {
			refNo = stt.SttShipmentID
		} else {
			refNo = stt.SttNoRefExternal
		}

		if model.IsNoRefExternal[shared.GetPrefixShipmentID(data.BagOrSttNo)] {
			refNo = data.BagOrSttNo
		}

		sttCommodity, er := c.repoCommodity.GetCommodityByCode(ctx, stt.SttCommodityCode, token)
		if er != nil {
			return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting commodity",
				"id": "Terjadi kesalahan pada saat commodity",
			})
		}

		metaStiTemporary := model.StiTemporaryMeta{
			BagNo:            bagNo,
			IsStiDest:        isStiDest,
			Pieces:           stt.SttTotalPiece,
			RefNo:            refNo,
			StatusReturn:     "-",
			IsPaid:           sttNoIsPaid[stt.SttNo],
			DeadlineReturn:   deadlineReturn.Format(shared.FormatDateTime),
			GrossWeight:      stt.SttGrossWeight,
			IsDangerousGoods: sttCommodity.Data.IsDangerousGoods,
		}

		if c.cfg.IsSTTAssessment() {
			sttAssessment, _ := c.sttAssessmentRepo.Get(ctx, &model.SttAssessmentParams{
				SttNo: stt.SttNo,
			})
			if sttAssessment != nil {
				metaStiTemporary.SttAssessmentStatus = sttAssessment.SttAssessmentStatus
			}
		}
		mappingMeta[stt.SttNo] = metaStiTemporary

		if priorityDelivery != nil && isStiDest == 1 {
			isLessThanToday := !isLestThenToday(deadlineReturn) && !isToday(deadlineReturn)
			isPdPod := priorityDelivery.PdFlag == `` && priorityDelivery.PdStatusReturn == model.POD

			if isLessThanToday && isPdPod {
				metaStiTemporary.StatusReturn = "-"
				metaStiTemporary.Flag = ""
				mappingMeta[stt.SttNo] = metaStiTemporary
			} else {
				metaStiTemporary.StatusReturn = c.getStatusReturn(stt.SttProductType, priorityDelivery.PdStatusReturn, priorityDelivery.PdIsShow)
				metaStiTemporary.Flag = priorityDelivery.PdFlag
				mappingMeta[stt.SttNo] = metaStiTemporary
				metaStiTemporary.Flag = c.flaggingResponse(mappingMeta, stt.SttNo, priorityDelivery.PdDeadlineReturn, stt.SttProductType, priorityDelivery.PdIsShow)
				mappingMeta[stt.SttNo] = metaStiTemporary
			}
		}

		if priorityDelivery != nil && priorityDelivery.PdStatusReturn == model.HAL {
			metaStiTemporary.StatusReturn = c.getStatusReturn(stt.SttProductType, priorityDelivery.PdStatusReturn, priorityDelivery.PdIsShow)
			metaStiTemporary.Flag = priorityDelivery.PdFlag
			mappingMeta[stt.SttNo] = metaStiTemporary
			metaStiTemporary.Flag = c.flaggingResponse(mappingMeta, stt.SttNo, priorityDelivery.PdDeadlineReturn, stt.SttProductType, priorityDelivery.PdIsShow)
			mappingMeta[stt.SttNo] = metaStiTemporary
		}

		isStiDestList[stt.SttNo] = isStiDest

		temp, _ := c.InsertSTITemporary(selfCtx, sti_temporary.StiTemporaryCreateParams{
			STIData:          *data,
			STTModel:         *stt,
			DeadLineReturn:   deadlineReturn,
			StiTemporaryMeta: metaStiTemporary,
		})

		sttBookedR := sttBookedResult[stt.SttNo]
		sttBookedR.BookedTypeResult = temp.StBookedType.Value()
		sttBookedR.BookedIDResult = temp.StBookedID
		sttBookedR.BookedNameResult = temp.StBookedName.Value()

		sttBookedResult[stt.SttNo] = sttBookedR
		if !shared.IsBagNoReff(stt.SttNoRefExternal) {
			sttNoDue = append(sttNoDue, stt.SttNo)
		}

	}

	go c.updateSttDueIsShowBulk(context.Background(), model.STTDueIsShowFalse, sttNoDue...)

	c.createStiPublishMessage(req)

	result, err = c.GenerateResponseV2(selfCtx, sti.GenerateResponseV2Params{
		Data:                 data,
		IsStiDest:            isStiDestList,
		ListPdDeadlineReturn: listPdDeadlineReturn,
		Token:                token,
		MappingMeta:          mappingMeta,
		BookingResult:        sttBookedResult,
		SttIsNoPaid:          sttNoIsPaid,
	})

	go func(stts map[string]model.Stt) {
		countStt := 0
		for i, stt := range stts {
			if !shared.IsBagNoReff(stts[i].SttNoRefExternal) {
				countStt += 1
			}

			c.sttDueUc.GenerateSttDueTargetDEL(context.Background(), stt_due.GenerateSttDueTargetDEL{Stt: &stt, Token: data.Token})

			if stts[i].SttOriginCityID != stts[i].SttDestinationCityID {
				continue
			}
			c.rtcUc.UpdateInactiveRTCBySttId(context.Background(), int(stts[i].SttID))
		}

		bookedID := ``
		bookedType := ``
		for _, stt := range sttBookedResult {
			bookedID = strconv.Itoa(stt.BookedIDResult)
			bookedType = stt.BookedTypeResult
			break
		}

		var count int
		// {account_id}:{hub_id}:{booked_type}:{booked_id}
		keyCache := model.SetCacheHydraScanSti(data.AccountID, data.HubID, bookedType, bookedID)
		c.cacheRepo.GetCache(context.Background(), keyCache, &count)

		if count <= 0 {
			c.cacheRepo.CreateCache(context.Background(), keyCache, countStt, model.CacheHydraScanStiExpired)
		}
	}(req.ListSttSuccess)

	return result, nil
}

func (c *stiCtx) generateBagVendorLilo(bagNo string) (bagNoVendor string) {
	isBagLilo := shared.IsBagNoReff(bagNo)
	if isBagLilo {
		bagNoVendor = bagNo
	}
	return
}

func (c *stiCtx) getStatusReturn(sttProductType, pdStatusReturn string, isShow int) string {
	if sttProductType == model.JUMBOPACKH2H {
		return pdStatusReturn
	}
	if pdStatusReturn == "HAL" && isShow == 0 {
		return "-"
	}

	return "Segera " + pdStatusReturn
}

func (c *stiCtx) flaggingResponse(mapingMeta map[string]model.StiTemporaryMeta, sttNo string, pdDeadlineReturn time.Time, sttProductType string, isShow int) string {
	if mapingMeta[sttNo].Flag == "Kiriman Penting" {
		return "Kiriman Penting"
	}

	if mapingMeta[sttNo].Flag == "Delivery Ulang" {
		return "Delivery Ulang"
	}

	slaHariIni := mapingMeta[sttNo].Flag == "" && mapingMeta[sttNo].StatusReturn == "Segera POD" && isToday(pdDeadlineReturn)
	if slaHariIni {
		return "SLA Hari Ini"
	}

	slaKemaren := mapingMeta[sttNo].Flag == "" && mapingMeta[sttNo].StatusReturn == "Segera POD" && isLestThenToday(pdDeadlineReturn)
	if slaKemaren {
		return "Lewat Batas SLA"
	}

	rtsSekarang := mapingMeta[sttNo].StatusReturn == "Segera RTS" && isToday(pdDeadlineReturn)
	if rtsSekarang {
		return "RTS Sekarang"
	}

	rtsBesok := mapingMeta[sttNo].StatusReturn == "Segera RTS" && isMoreThanToday(pdDeadlineReturn)
	if rtsBesok {
		return "RTS Besok"
	}

	rtsKemaren := mapingMeta[sttNo].StatusReturn == "Segera RTS" && isLestThenToday(pdDeadlineReturn)
	if rtsKemaren {
		return "Lewat Batas RTS"
	}

	HalToday := mapingMeta[sttNo].StatusReturn == "Segera HAL" && isShow == 1
	if HalToday {
		return "HAL Sekarang"
	}

	if sttProductType == model.JUMBOPACKH2H {
		return "Diambil penerima"
	}

	return ""
}

// IsToday checks if the given date is today
func isToday(inputDate time.Time) bool {
	// Get the current date
	now := time.Now()

	// Extract the year, month, and day from the current date and input date
	currentYear, currentMonth, currentDay := now.Date()
	inputYear, inputMonth, inputDay := inputDate.Date()

	// Compare the dates
	return currentYear == inputYear && currentMonth == inputMonth && currentDay == inputDay
}

// IsToday checks if the given date is today
func isLestThenToday(inputDate time.Time) bool {
	// Get the current date
	now := time.Now()

	// Extract the year, month, and day from the current date and input date
	currentYear, currentMonth, currentDay := now.Date()
	inputYear, inputMonth, inputDay := inputDate.Date()

	// Compare the dates
	if inputYear < currentYear {
		return true
	} else if inputYear == currentYear {
		if inputMonth < currentMonth {
			return true
		} else if inputMonth == currentMonth {
			if inputDay < currentDay {
				return true
			}
		}
	}
	return false
}

// IsToday checks if the given date is today
func isMoreThanToday(inputDate time.Time) bool {
	// Get the current date
	now := time.Now()

	// Extract the year, month, and day from the current date and input date
	currentYear, currentMonth, currentDay := now.Date()
	inputYear, inputMonth, inputDay := inputDate.Date()

	// Compare the dates
	if inputYear > currentYear {
		return true
	} else if inputYear == currentYear {
		if inputMonth > currentMonth {
			return true
		} else if inputMonth == currentMonth {
			if inputDay > currentDay {
				return true
			}
		}
	}
	return false
}

func (c *stiCtx) GenerateResponseV2(ctx context.Context, params sti.GenerateResponseV2Params) (sti.ResponseCreateV2, error) {
	result := sti.ResponseCreateV2{}

	if shared.IsBagNoReff(params.Data.BagOrSttNo) {
		result.Stt = nil
		bagVendor, _ := c.bagVendorRepo.SelectWithSttDetailResult(ctx, &model.BagVendorViewParams{
			BagVendorNo: params.Data.BagOrSttNo,
		})
		for j := 0; j < len(bagVendor); j++ {
			stt, _ := c.repoStt.Get(ctx, &model.SttViewDetailParams{
				Search: bagVendor[j].SttNo,
			})

			regionDestinationName := ""
			regionOriginName := ""

			regionDestination, _ := c.cityRepo.GetRegionCity(ctx, stt.SttDestinationCityID, params.Token)
			regionOrigin, _ := c.cityRepo.GetRegionCity(ctx, stt.SttOriginCityID, params.Token)

			if regionDestination != nil {
				regionDestinationName = regionDestination.Data.RegionName
			}

			if regionOrigin != nil {
				regionOriginName = regionOrigin.Data.RegionName
			}

			sttPiece, _ := c.repoSttPiece.Select(ctx, &model.SttPiecesViewParam{
				SttID: int(stt.SttID),
			})

			sttPieceResponse := []sti.PieceV2{}
			for i := 0; i < len(sttPiece); i++ {
				sttPieceResponse = append(sttPieceResponse, sti.PieceV2{
					SttPieceID:           int(sttPiece[i].SttPieceID),
					SttPieceGrossWeight:  sttPiece[i].SttPieceGrossWeight,
					SttPieceVolumeWeight: sttPiece[i].SttPieceVolumeWeight,
					SttPieceNo:           sttPiece[i].SttPieceNo,
					SttPieceLastStatusID: sttPiece[i].SttPieceLastStatusID,
				})
			}

			pdFlag := params.MappingMeta[stt.SttNo].Flag
			pdStatusReturn := params.MappingMeta[stt.SttNo].StatusReturn
			refNo := params.MappingMeta[stt.SttNo].RefNo
			isDangerousGoods := params.MappingMeta[stt.SttNo].IsDangerousGoods
			bookedSTTForBag := params.BookingResult[stt.SttNo]

			var sttAssessmentStatus string
			if c.cfg.IsSTTAssessment() {
				sttAssessment, _ := c.sttAssessmentRepo.Get(ctx, &model.SttAssessmentParams{
					SttNo: stt.SttNo,
				})
				if sttAssessment != nil {
					sttAssessmentStatus = sttAssessment.SttAssessmentStatus
				}
			}

			result.Bag = append(result.Bag, sti.Bag{
				IsPaid:                         true,
				Flag:                           pdFlag,
				StatusReturn:                   pdStatusReturn,
				DeadlineReturn:                 params.ListPdDeadlineReturn[stt.SttNo],
				BagNo:                          params.Data.BagOrSttNo,
				IsStiDest:                      params.IsStiDest[stt.SttNo],
				RefNo:                          refNo,
				SttID:                          int(stt.SttID),
				SttNo:                          stt.SttNo,
				SttProductType:                 stt.SttProductType,
				SttTotalPiece:                  stt.SttTotalPiece,
				SttDestinationCityID:           stt.SttDestinationCityID,
				SttDestinationCityName:         stt.SttDestinationCityName,
				SttOriginCityID:                stt.SttOriginCityID,
				SttOriginCityName:              stt.SttOriginCityName,
				SttWoodPacking:                 "",
				SttCommodityCode:               stt.SttCommodityCode,
				SttCommodityName:               stt.SttCommodityName,
				SttGrossWeight:                 stt.SttGrossWeight,
				SttVolumeWeight:                stt.SttVolumeWeight,
				SttChargeableWeight:            stt.SttChargeableWeight,
				SttLastStatusID:                stt.SttLastStatusID,
				SttPaymentStatus:               stt.SttPaymentStatus,
				BookingID:                      stt.SttShipmentID,
				SttShipmentID:                  stt.SttShipmentID,
				SttNoRefExternal:               stt.SttNoRefExternal,
				Piece:                          sttPieceResponse,
				SttDestinationDistrictUrsacode: "",
				PackageNumber:                  nil,
				SttRegionID:                    "",
				SttRegionName:                  "",
				SttDestinationRegionName:       regionDestinationName,
				SttOriginRegionName:            regionOriginName,
				StiDestOneBagScan:              false,
				IsDangerousGoods:               isDangerousGoods,
				BookedID:                       bookedSTTForBag.BookedIDResult,
				BookedName:                     bookedSTTForBag.BookedNameResult,
				BookedType:                     bookedSTTForBag.BookedTypeResult,
				SttAssessmentStatus:            sttAssessmentStatus,
			})
		}
	} else {
		result.Bag = nil
		stt, err := c.repoStt.Get(ctx, &model.SttViewDetailParams{
			Search:          params.Data.BagOrSttNo,
			SearchByPattern: true,
		})

		if err != nil || stt == nil {
			return result, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting stt",
				"id": "Terjadi kesalahan pada saat stt",
			})
		}

		regionDestinationName := ""
		regionOriginName := ""

		regionDestination, _ := c.cityRepo.GetRegionCity(ctx, stt.SttDestinationCityID, params.Token)
		regionOrigin, _ := c.cityRepo.GetRegionCity(ctx, stt.SttOriginCityID, params.Token)

		if regionDestination != nil {
			regionDestinationName = regionDestination.Data.RegionName
		}

		if regionOrigin != nil {
			regionOriginName = regionOrigin.Data.RegionName
		}

		sttPiece, _ := c.repoSttPiece.Select(ctx, &model.SttPiecesViewParam{
			SttID: int(stt.SttID),
		})

		sttPieceResponse := []sti.PieceV2{}
		for i := 0; i < len(sttPiece); i++ {
			sttPieceResponse = append(sttPieceResponse, sti.PieceV2{
				SttPieceID:           int(sttPiece[i].SttPieceID),
				SttPieceGrossWeight:  sttPiece[i].SttPieceGrossWeight,
				SttPieceVolumeWeight: sttPiece[i].SttPieceVolumeWeight,
				SttPieceNo:           sttPiece[i].SttPieceNo,
				SttPieceLastStatusID: sttPiece[i].SttPieceLastStatusID,
			})
		}

		pdFlag := params.MappingMeta[stt.SttNo].Flag
		pdStatusReturn := params.MappingMeta[stt.SttNo].StatusReturn
		refNo := params.MappingMeta[stt.SttNo].RefNo
		isDangerousGoods := params.MappingMeta[stt.SttNo].IsDangerousGoods
		bookedSTT := params.BookingResult[stt.SttNo]
		sttNoPaidStatus := params.SttIsNoPaid[stt.SttNo]

		bagDetail, _ := c.bagDetailRepo.Get(ctx, &model.BagDetailViewParams{
			BagDetail: model.BagDetail{
				BagDetailSttID: &stt.SttID,
			},
		})

		bagNo := ""
		if bagDetail != nil && bagDetail.BagCode != nil {
			bagNo = *bagDetail.BagCode
		}

		var sttAssessmentStatus string
		if c.cfg.IsSTTAssessment() {
			sttAssessment, _ := c.sttAssessmentRepo.Get(ctx, &model.SttAssessmentParams{
				SttNo: stt.SttNo,
			})
			if sttAssessment != nil {
				sttAssessmentStatus = sttAssessment.SttAssessmentStatus
			}
		}

		result.Stt = &sti.Stt{
			IsPaid:                         sttNoPaidStatus,
			Flag:                           pdFlag,
			StatusReturn:                   pdStatusReturn,
			DeadlineReturn:                 params.ListPdDeadlineReturn[stt.SttNo],
			BagNo:                          bagNo,
			IsStiDest:                      params.IsStiDest[stt.SttNo],
			RefNo:                          refNo,
			SttID:                          int(stt.SttID),
			SttNo:                          stt.SttNo,
			SttProductType:                 stt.SttProductType,
			SttTotalPiece:                  stt.SttTotalPiece,
			SttDestinationCityID:           stt.SttDestinationCityID,
			SttDestinationCityName:         stt.SttDestinationCityName,
			SttOriginCityID:                stt.SttOriginCityID,
			SttOriginCityName:              stt.SttOriginCityName,
			SttWoodPacking:                 "",
			SttCommodityCode:               stt.SttCommodityCode,
			SttCommodityName:               stt.SttCommodityName,
			SttGrossWeight:                 stt.SttGrossWeight,
			SttVolumeWeight:                stt.SttVolumeWeight,
			SttChargeableWeight:            stt.SttChargeableWeight,
			SttLastStatusID:                stt.SttLastStatusID,
			SttPaymentStatus:               stt.SttPaymentStatus,
			BookingID:                      stt.SttShipmentID,
			SttShipmentID:                  stt.SttShipmentID,
			SttNoRefExternal:               stt.SttNoRefExternal,
			Piece:                          sttPieceResponse,
			SttDestinationDistrictUrsacode: "",
			PackageNumber:                  nil,
			SttRegionID:                    "",
			SttRegionName:                  "",
			SttDestinationRegionName:       regionDestinationName,
			SttOriginRegionName:            regionOriginName,
			StiDestOneBagScan:              false,
			IsDangerousGoods:               isDangerousGoods,
			BookedID:                       bookedSTT.BookedIDResult,
			BookedName:                     bookedSTT.BookedNameResult,
			BookedType:                     bookedSTT.BookedTypeResult,
			SttAssessmentStatus:            sttAssessmentStatus,
			SttNeedToRelabel:               stt.GetSttNeedRelabel(),
		}
	}

	return result, nil
}

func (c *stiCtx) getSttDetailByBagLilo(ctx context.Context, bagNo string, useValidateSttBagLilo bool) ([]model.SttDetailResult, []string, error) {
	var (
		sttDetail   = []model.SttDetailResult{}
		stiSttNo    = []string{}
		bagNoVendor = bagNo
		limit       = 20
		lastPieceID = int64(0)
	)

	for {
		bagVendor, err := c.bagVendorRepo.SelectWithSttDetailResult(ctx, &model.BagVendorViewParams{
			BasedFilter: model.BasedFilter{
				Limit:   limit,
				OrderBy: `stt_piece.stt_piece_id`,
				SortBy:  model.SortByAsc,
			},
			BagVendorNo:  bagNoVendor,
			PieceIDAfter: lastPieceID,
		})

		if err != nil {
			return sttDetail, stiSttNo, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting Bag Vendor",
				"id": "Terjadi kesalahan pada saat getting Bag Vendor",
			})
		}

		for _, bvData := range bagVendor {
			sttDetail = append(sttDetail, bvData.SttDetailResult)
			stiSttNo = append(stiSttNo, bvData.SttNo)
		}

		if len(bagVendor) < limit || len(bagVendor) < 1 {
			break
		}

		lastPieceID = bagVendor[len(bagVendor)-1].SttPieceID
	}

	if len(sttDetail) < 1 {
		return sttDetail, stiSttNo, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Bag vendor not found",
			"id": "Bag vendor tidak ditemukan",
		})
	}

	sttDummy, stiSttNoDummy, err := c.getSttDetailBySttPatern(ctx, bagNo, useValidateSttBagLilo)
	if err != nil {
		return sttDetail, stiSttNo, err
	}

	sttDetail = append(sttDetail, sttDummy...)
	stiSttNo = append(stiSttNo, stiSttNoDummy...)

	return sttDetail, stiSttNo, nil
}

func (c *stiCtx) getSttDetailBySttPatern(ctx context.Context, sttNo string, useValidateSttBagLilo bool) ([]model.SttDetailResult, []string, error) {
	var (
		sttDetail = []model.SttDetailResult{}
		stiSttNo  = []string{}
	)

	stt, err := c.repoStt.Get(ctx, &model.SttViewDetailParams{
		Search:          sttNo,
		SearchByPattern: true,
	})

	if err != nil || stt == nil {
		return sttDetail, stiSttNo, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting stt",
			"id": "Terjadi kesalahan pada saat stt",
		})
	}

	err = c.validateSttBagLilo(stt, useValidateSttBagLilo)
	if err != nil {
		return sttDetail, stiSttNo, err
	}

	sttDetail, err = c.repoStt.SelectDetail(ctx, 0, stt.SttNo, false)

	if err != nil || len(sttDetail) == 0 {
		return sttDetail, stiSttNo, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting stt detail",
			"id": "Terjadi kesalahan pada saat stt detail",
		})
	}

	stiSttNo = append(stiSttNo, sttDetail[0].SttNo)

	return sttDetail, stiSttNo, nil
}

func (c *stiCtx) validateSttBagLilo(stt *model.Stt, useValidateSttBagLilo bool) error {
	if !useValidateSttBagLilo {
		return nil
	}

	if stt.SttNoRefExternal != "" && shared.IsBagNoReff(strings.ToUpper(stt.SttNoRefExternal)) {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt TKP01-BAG can not be processed",
			"id": "Stt TKP01-BAG tidak dapat diprocess",
		})
	}

	sttMeta := stt.SttMetaToStruct()
	sttInBagLilo := sttMeta != nil && sttMeta.IsSttCrossdocking && stt.SttLastStatusID == model.BAGGING
	if sttInBagLilo {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt in Bag LILO can not be processed",
			"id": "Stt in Bag LILO tidak dapat diproses",
		})
	}

	return nil
}

func (c *stiCtx) InsertSTITemporary(ctx context.Context, params sti_temporary.StiTemporaryCreateParams) (model.StiTemporary, error) {
	var (
		opName  = "UsecaseSti-InsertSTITemporary"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		err error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "error": err})
	}()
	stiTemp := model.StiTemporary{
		StAccountID:      int(params.STIData.AccountID),
		StIsActive:       1,
		StSttNo:          params.STTModel.SttNo,
		StProduct:        params.STTModel.SttProductType,
		StOrigin:         params.STTModel.SttOriginCityID,
		StDestination:    params.STTModel.SttDestinationCityID,
		StDeadlineReturn: params.DeadLineReturn,
		StRegionID:       "",
		StMeta:           params.StiTemporaryMeta.ToString(),
		StCreatedAt:      time.Now(),
		HubID:            params.STIData.HubID,
	}
	switch params.STTModel.SttBookedByType {
	case model.INTERNAL:
		stiTemp.StBookedType = dbr.NewNullString(params.STTModel.SttBookedForType)
		stiTemp.StBookedID = params.STTModel.SttBookedForID
		stiTemp.StBookedName = dbr.NewNullString(params.STTModel.SttBookedForName)
	case model.POS, model.CLIENT:
		stiTemp.StBookedType = dbr.NewNullString(params.STTModel.SttBookedByType)
		stiTemp.StBookedID = params.STTModel.SttBookedBy
		stiTemp.StBookedName = dbr.NewNullString(params.STTModel.SttBookedName)
	}

	show := true
	sttDue, _ := c.sttDueRepo.GetBySttNo(selfCtx, &model.GetBySttNoParams{
		SttNo:        params.STTModel.SttNo,
		TargetStatus: model.STI,
		IsShow:       &show,
		BookedType:   model.SUBCONSOLE,
	})

	if sttDue != nil {
		stiTemp.StBookedType = dbr.NewNullString(sttDue.SdBookedType)
		stiTemp.StBookedID = int(sttDue.SdBookedID)
		stiTemp.StBookedName = dbr.NewNullString(sttDue.SdBookedName)
	}

	err = c.stiTemporaryRepo.Create(selfCtx, &stiTemp)
	return stiTemp, err
}

func (c *stiCtx) updateSttDueIsShowBulk(ctx context.Context, isShow int, sttNo ...string) error {
	var (
		opName  = "UsecaseSti-updateSttDueIsShowBulk"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		err error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"sttNo": sttNo, "isShow": isShow, "error": err})
	}()
	if len(sttNo) < 1 {
		return nil
	}
	err = c.sttDueRepo.UpdateIsShowBulk(selfCtx, &model.STTDueUpdateIsShow{
		IsShow:       isShow,
		TargetStatus: model.STI,
		STTNos:       sttNo,
	})
	if err != nil {
		logger.Ef("error update stt due is show bulk: %v", err)
		return err
	}
	return nil
}
