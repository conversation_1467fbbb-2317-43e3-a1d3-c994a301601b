package usecase

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	gatewaySttTracking "github.com/Lionparcel/hydra/src/usecase/gateway_stt_tracking"
	"github.com/Lionparcel/hydra/src/usecase/general"
	sttUc "github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *gatewaySttTrackingCtx) SttDetail(ctx context.Context, query string) (res *gatewaySttTracking.GatewaySttDetail, err error) {
	var (
		token   = ctx.Value("token").(string)
		opName  = "GatewaySttTracking-SttDetail"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		trace.Finish(map[string]interface{}{"param": query, "result": res, "error": err})
	}()

	query = strings.TrimSpace(query)
	if query == "" {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Query param q/stt_no required",
			"id": "Query param q/stt_no wajib diisi",
		})
	}

	stt, err := c.sttRepo.Get(ctx, &model.SttViewDetailParams{
		Search:          query,
		SearchByPattern: true,
	})
	if err != nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while querying db",
			"id": "Terjadi kesalahan pada saat query db",
		})
	}

	if stt == nil || stt.SttID == 0 {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorNotFound, map[string]string{
			"en": "STT Not Found",
			"id": "STT tidak ditemukan",
		})
	}

	sttOptionaleRate, err := c.sttOptionalRate.Select(ctx, &model.SttOptionalRate{SttOptionalRateSttID: stt.SttID})
	if err != nil || len(sttOptionaleRate) == 0 {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while querying db, get data stt optional",
			"id": "Terjadi kesalahan pada saat query db, ambil data stt optional",
		})
	}

	credentialRestAPI := &model.CredentialRestAPI{
		Token: token,
	}
	destinationDistrict, err := c.districtRepo.GetByCode(ctx, credentialRestAPI, stt.SttDestinationDistrictID)
	if destinationDistrict == nil || err != nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting data",
			"id": "Terjadi kesalahan pada saat getting data",
		})
	}

	sttPieces, err := c.sttPiecesRepo.Select(ctx, &model.SttPiecesViewParam{
		SttID: int(stt.SttID),
	})
	if err != nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while querying db",
			"id": "Terjadi kesalahan pada saat query db",
		})
	}
	pieces := []sttUc.SttPieces{}
	for _, v := range sttPieces {
		piece := sttUc.SttPieces{}
		piece.SttID = v.SttPieceSttID
		piece.SttPieceID = v.SttPieceID
		piece.SttPieceLength = v.SttPieceLength
		piece.SttPieceWidth = v.SttPieceWidth
		piece.SttPieceHeight = v.SttPieceHeight
		piece.SttPieceGrossWeight = v.SttPieceGrossWeight
		piece.SttPieceVolumeWeight = v.SttPieceVolumeWeight
		pieces = append(pieces, piece)
	}

	isWoodPackingOrIsInsurance := map[string]bool{}
	woodPackingRateOrInsuranceRate := map[string]float64{}
	isWoodPackingOrIsInsurance[model.WOODPACKING] = false
	isWoodPackingOrIsInsurance[model.INSURANCE] = false

	for _, val := range sttOptionaleRate {
		if val.SttOptionalRateParams == model.INSURANCE {
			if stt.SttInsuranceType != model.INSURANCEFREE {
				isWoodPackingOrIsInsurance[model.INSURANCE] = true
				woodPackingRateOrInsuranceRate[model.INSURANCE] = val.SttOptionalRateRate
			}
		}
		if val.SttOptionalRateParams == model.WOODPACKING {
			isWoodPackingOrIsInsurance[model.WOODPACKING] = true
			woodPackingRateOrInsuranceRate[model.WOODPACKING] = val.SttOptionalRateRate
		}
	}

	//get client code elexys
	client := new(model.Client)
	if stt.SttClientID > 0 {
		client, err = c.clientRepo.GetByID(ctx, stt.SttClientID, token)
		if err != nil || client == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Client not found",
				"id": "Klien tidak ditemukan",
			})
		}
	}

	sttMeta := stt.SttMetaToStruct()

	clientParentID := 0
	clientParentName := ""
	vendorName := destinationDistrict.Data.VendorCode
	// if stt_booked_for_type = client, get client parent by stt_booked_for_id
	if stt.SttBookedForType == model.CLIENT && stt.SttBookedForID > 0 {
		clientParent, err := c.clientRepo.GetByID(ctx, stt.SttBookedForID, token)
		if err != nil || clientParent == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Client parent not found",
				"id": "Klien induk tidak ditemukan",
			})
		}
		clientParentID = clientParent.Data.ClientParentID
		clientParentName = clientParent.Data.ClientParentName
	}

	res = &gatewaySttTracking.GatewaySttDetail{
		SttNo: func() string {
			if stt.SttElexysNo.Valid {
				return stt.SttElexysNo.Value()
			}
			return stt.SttNo
		}(),
		Sender: gatewaySttTracking.Contact{
			Name:    stt.SttSenderName,
			Address: stt.SttSenderAddress,
			Phone:   stt.SttSenderPhone,
		},
		Recipient: gatewaySttTracking.Contact{
			Name:    stt.SttRecipientName,
			Address: stt.SttRecipientAddress,
			Phone:   stt.SttRecipientPhone,
		},
		Product:                  stt.SttProductType,
		Pieces:                   stt.SttTotalPiece,
		GrossWeight:              stt.SttGrossWeight,
		VolumeWeight:             stt.SttVolumeWeight,
		ChargeableWeight:         stt.SttChargeableWeight,
		PublishRate:              stt.SttPublishRate,
		ForwardRate:              stt.SttOriginDistrictRate + stt.SttDestinationDistrictRate,
		ServiceType:              model.PACKAGESERVICE,
		ShippingSurchargeRate:    stt.SttShippingSurchargeRate,
		CommoditySurchargeRate:   stt.SttCommoditySurchargeRate,
		HeavyWeightSurchargeRate: stt.SttHeavyweightSurchargeRate,
		GoodsValue:               stt.SttGoodsEstimatePrice,
		CurrentStatus:            stt.SttLastStatusID,
		ShipmentID:               stt.SttShipmentID,
		IsCashOut:                false,
		CashCollected:            0,
		IsWoodPacking:            isWoodPackingOrIsInsurance[model.WOODPACKING],
		IsInsurance:              isWoodPackingOrIsInsurance[model.INSURANCE],
		BookedForName:            stt.SttBookedForName,
		OriginCode:               stt.SttOriginCityID,
		DestinationCode:          stt.SttDestinationCityID,
		Dimention:                pieces,
		TotalTariff:              stt.SttTotalAmount,

		//tariff before discount for algo
		TotalTariffBeforeDiscount: func() float64 {
			if sttMeta != nil {
				if sttMeta.RetailTariff != nil && sttMeta.RetailTariff.IsPromo {
					return sttMeta.RetailTariff.TotalTariffBeforeDiscount
				}

				if sttMeta.ClientTariff != nil && sttMeta.ClientTariff.AfterDiscount.IsPromo {
					return sttMeta.ClientTariff.BeforeDiscount.TotalTariff
				}
			}
			return stt.SttTotalAmount
		}(),
		PublishRateBeforeDiscount:             stt.SttPublishRate,
		ShippingSurchargeRateBeforeDiscount:   stt.SttShippingSurchargeRate,
		OriginDistrictRateBeforeDiscount:      stt.SttOriginDistrictRate,
		DestinationDistrictRateBeforeDiscount: stt.SttDestinationDistrictRate,
		DocumentSurchargeBeforeDiscount:       stt.SttDocumentSurchargeRate,
		CommoditySurchargeBeforeDiscount:      stt.SttCommoditySurchargeRate,
		HeavyWeightSurchargeBeforeDiscount:    stt.SttHeavyweightSurchargeRate,
		WoodpackingRateBeforeDiscount:         woodPackingRateOrInsuranceRate[model.WOODPACKING],
		InsuranceRatesBeforeDiscount:          woodPackingRateOrInsuranceRate[model.INSURANCE],
		ForwardRateBeforeDiscount:             stt.SttOriginDistrictRate + stt.SttDestinationDistrictRate,

		DestinationDistrictUrsaCode: destinationDistrict.Data.UrsaCode,
		ExternalID: func() string {
			if stt.SttIsDO {
				return stt.SttNoRefExternal
			}
			if sttMeta != nil {
				if sttMeta.TicketCode != `` {
					return sttMeta.TicketCode
				}
			}
			return ""
		}(),
		IsCod: stt.SttIsCOD,
		CodValue: func() float64 {
			var codValue float64
			if stt.SttIsCOD {
				codValue = stt.SttCODAmount
			}
			if stt.SttIsDFOD {
				codValue = 0
			}
			return codValue
		}(),
		CodFee: func() float64 {
			if stt.SttIsCOD {
				return stt.SttCODFee
			}
			return 0
		}(),
		WoodPackingRate: woodPackingRateOrInsuranceRate[model.WOODPACKING],
		InsuranceRate:   woodPackingRateOrInsuranceRate[model.INSURANCE],
		CreatedOn: func() string {
			tmp := shared.UTC0(stt.SttCreatedAt)
			return tmp.Format(time.RFC3339)
		}(),
		UpdatedOn: func() string {
			tmp := shared.UTC0(stt.SttUpdatedAt)
			return tmp.Format(time.RFC3339)
		}(),
		DocumentSurcharge: stt.SttDocumentSurchargeRate,
		ClientCodeElexys: func() string {
			if client != nil {
				return client.Data.ClientElexysCode
			}
			return ``
		}(),
		ClientCodeGenesis: func() string {
			if client != nil {
				return client.Data.ClientCode
			}
			return ``
		}(),
		ReturnDetails: func() *general.ReturnDetails {
			if stt.SttIsCOD && shared.GetPrefixSttNo(stt.SttNo) != model.PrefixAutoCA {
				return client.GenerateReturnDetailsResponse()
			}
			return nil
		}(),
		IsDo:             stt.SttIsDO,
		ClientParentID:   clientParentID,
		ClientParentName: clientParentName,
		VendorName:       vendorName,
		IsDFOD:           stt.SttIsDFOD,
	}

	rebuttalDex := ""
	if res.CurrentStatus == model.DEX {
		data, err := c.rebuttalDexRepo.GetBySttNo(ctx, res.SttNo)
		if err == nil && data != nil && data.Status != "" {
			rebuttalDex = model.Rebuttal
		}
	}
	res.RebuttalDex = rebuttalDex

	sttMeta, err = c.initiateMetaIfNotExist(ctx, sttMeta, stt, token)
	if err != nil {
		return nil, err
	}

	if sttMeta != nil && sttMeta.RetailTariff != nil {
		dataPromo := sttMeta.RetailTariff
		res.IsPromo = dataPromo.IsPromo
		if res.IsPromo {
			res.Discount = dataPromo.Discount
			res.TotalDiscount = dataPromo.TotalDiscount
			res.PublishRate = dataPromo.PublishRateAfterDiscount
			res.ShippingSurchargeRate = dataPromo.ShippingSurchargeRateAfterDiscount
			res.OriginDistrictRate = dataPromo.OriginDistrictRateAfterDiscount
			res.DestinationDistrictRate = dataPromo.DestinationDistrictRateAfterDiscount
			res.TotalTariff = dataPromo.TotalTariffAfterDiscount
			res.ForwardRate = res.OriginDistrictRate + res.DestinationDistrictRate
			res.InsuranceRate = dataPromo.InsuranceRatesAfterDiscount
			res.WoodPackingRate = dataPromo.WoodpackingRatesAfterDiscount
			res.DocumentSurcharge = dataPromo.DocumentSurchargeAfterDiscount
			res.CommoditySurchargeRate = dataPromo.CommoditySurchargeAfterDiscount
			res.HeavyWeightSurchargeRate = dataPromo.HeavyWeightSurchargeAfterDiscount
		}
	}

	res.Origin = fmt.Sprintf("%s (%s)", sttMeta.OriginDistrictName, stt.SttOriginCityID)
	res.Destination = fmt.Sprintf("%s (%s)", sttMeta.DestinationDistrictName, stt.SttDestinationCityID)

	// flag invoice algo
	if sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.ReverseShipmentID != "" && model.IsPrefixShipmentAllowFlagInvoice[shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.ReverseShipmentID)] {
		res.ShipmentIdReferenceReverseJourney = sttMeta.DetailSttReverseJourney.ReverseShipmentID
		res.SttNoReferenceReverseJourney = sttMeta.DetailSttReverseJourney.ReverseSttNo
		if model.IsPrefixShipmentCAToAlgoRTS[shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.ReverseShipmentID)] {
			res.IsInvoiceCreated = true
		}
	}

	isTopupCa := false
	percentageCodFee := 0.0
	minCodFee := 0.0

	if sttMeta != nil {
		if sttMeta.CodRetailDetail != nil || sttMeta.CodConfiguration != nil {
			codConfig := sttMeta.CodRetailDetail
			if codConfig == nil {
				codConfig = sttMeta.CodConfiguration
			}

			percentageCodFee = codConfig.PercentageCodFee
			minCodFee = codConfig.MinCodFee
		}

		if stt.SttIsDFOD && sttMeta.DfodConfiguration != nil {
			percentageCodFee = sttMeta.DfodConfiguration.PercentageCodFee
			minCodFee = sttMeta.DfodConfiguration.MinCodFee
		}

		if sttMeta.RetailTariff != nil {
			res.CodAmount = sttMeta.RetailTariff.CodAmount
			res.ShippingCost = sttMeta.RetailTariff.ShippingCost
		}

		if sttMeta.ClientTariff != nil {
			rateAfterDiscount := sttMeta.ClientTariff.AfterDiscount
			c.setRatesAfterDiscount(res, rateAfterDiscount)
		}
	}

	isPrefixCODCustomerAppRetail := stt.SttShipmentID != `` && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(stt.SttShipmentID)]
	isPrefixShipmentFavorite := stt.SttShipmentID != `` && model.IsShipmentPrefixFavorite[shared.GetPrefixShipmentID(stt.SttShipmentID)]

	if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil {
		res.DetailSttReverseJourney = &gatewaySttTracking.DetailSttReverseJourneyResponse{
			ReverseSttNo:                sttMeta.DetailSttReverseJourney.ReverseSttNo,
			ReverseSttShipmentID:        sttMeta.DetailSttReverseJourney.ReverseShipmentID,
			ReverseSttLastStatusID:      sttMeta.DetailSttReverseJourney.ReverseLastStatusStt,
			ReverseStatusID:             sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt,
			RootReverseSttNo:            sttMeta.DetailSttReverseJourney.RootReverseSttNo,
			RootReverseSttShipmentID:    sttMeta.DetailSttReverseJourney.RootReverseShipmentID,
			RootReverseSttLastStatusID:  sttMeta.DetailSttReverseJourney.RootReverseLastStatusStt,
			ReverseChargedPosID:         sttMeta.DetailSttReverseJourney.ReverseChargedPosID,
			ReverseChargedConsoleID:     sttMeta.DetailSttReverseJourney.ReverseChargedConsoleID,
			RootReverseCodHandling:      sttMeta.DetailSttReverseJourney.RootReverseCodHandling,
			RootReverseSttNoRefExternal: sttMeta.DetailSttReverseJourney.RootReverseSttNoRefExternal,
		}
	}

	res.SttIsPriority = c.isSttPriority(sttMeta)

	if stt.SttShipmentID != "" && (shared.GetPrefixShipmentID(stt.SttShipmentID) == model.C1 || shared.GetPrefixShipmentID(stt.SttShipmentID) == model.C2) {
		isTopupCa = false
	}

	if stt.SttIsCOD && ((stt.SttBookedByType == model.POS && stt.SttBookedForType == model.POS) || isPrefixCODCustomerAppRetail || isPrefixShipmentFavorite) {
		isTopupCa = true
	}

	if stt.SttIsCOD {
		res.CodValue = stt.SttGoodsEstimatePrice - stt.SttCODFee
	}

	res.CodAmount = stt.SttCODAmount

	if stt.SttIsDFOD {
		isTopupCa = false
		res.CodValue = stt.SttCODAmount - stt.SttCODFee
	}

	res.IsTopupCa = isTopupCa
	res.PercentageCodFee = percentageCodFee
	res.MinCodFee = minCodFee
	res.IsDFOD = stt.SttIsDFOD
	res.RecipientDistrictCode = stt.SttDestinationDistrictID
	c.setTaxAndFeeDetails(stt, sttMeta, res)
	delivery, err := c.deliveryRepo.GetDetail(selfCtx, &model.DeliveryViewParam{
		SttNo: stt.SttNo,
	})
	if err != nil {
		return res, err
	}

	if delivery != nil {
		res.PaymentMethod = delivery.PaymentMethod
	}

	return res, nil
}

func (c *gatewaySttTrackingCtx) setRatesAfterDiscount(res *gatewaySttTracking.GatewaySttDetail, rateAfterDiscount model.ClientTariffDetailWithDiscount) {
	res.PublishRate = rateAfterDiscount.PublishRate
	res.ForwardRate = rateAfterDiscount.ForwardRates
	res.ShippingSurchargeRate = rateAfterDiscount.ShippingSurchargeRate
	res.CommoditySurchargeRate = rateAfterDiscount.CommoditySurcharge
	res.HeavyWeightSurchargeRate = rateAfterDiscount.HeavyWeightSurcharge
	res.DocumentSurcharge = rateAfterDiscount.DocumentSurcharge
	res.InsuranceRate = rateAfterDiscount.InsuranceRates
	res.WoodPackingRate = rateAfterDiscount.WoodpackingRates
	res.OriginDistrictRate = rateAfterDiscount.OriginDistrictRate
	res.DestinationDistrictRate = rateAfterDiscount.DestinationDistrictRate
	res.TotalTariff = rateAfterDiscount.TotalTariff
	res.Discount = rateAfterDiscount.Discount
	res.TotalDiscount = rateAfterDiscount.TotalDiscount

	if !res.IsPromo {
		res.IsPromo = rateAfterDiscount.IsPromo
	}
}

func (c *gatewaySttTrackingCtx) setTaxAndFeeDetails(stt *model.Stt, sttMeta *model.SttMeta, res *gatewaySttTracking.GatewaySttDetail) {
	res.BmTaxRate = stt.SttBMTaxRate
	res.PpnTaxRate = stt.SttPPNTaxRate
	res.PphTaxRate = stt.SttPPHTaxRate
	if sttMeta.RetailTariff != nil {
		res.InsuranceAdminFee = sttMeta.RetailTariff.InsuranceAdminFee
	}
	res.ReturnFeeBasic = sttMeta.TotalTariffReturn
	res.ReturnFee = sttMeta.TotalTariffReturn * 0.5
}
