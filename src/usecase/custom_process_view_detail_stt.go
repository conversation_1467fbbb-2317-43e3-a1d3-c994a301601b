package usecase

import (
	"context"
	"strings"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"

	"github.com/Lionparcel/hydra/src/model"
	customProcess "github.com/Lionparcel/hydra/src/usecase/custom_process"
	"github.com/Lionparcel/hydra/src/usecase/general"
	"github.com/Lionparcel/hydra/src/usecase/release"
)

// ViewDetailStt
func (c *customProcessCtx) ViewDetailStt(ctx context.Context, params *customProcess.DetailCustomProcessRequest) (res *customProcess.DetailSttCustomProcessResponse, err error) {
	res = &customProcess.DetailSttCustomProcessResponse{}
	opName := "customProcessCtx-ViewDetailStt"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": err})
	}()
	if err = params.Validate(); err != nil {
		return nil, err
	}

	partnerLuw, countryCode, err := c.viewDetailSttValidateAccount(selfCtx, params)
	if err != nil {
		return nil, err
	}

	pieceData, err := c.viewDetailSttValidateSttPiece(selfCtx, params, countryCode)
	if err != nil {
		return nil, err
	}
	if pieceData.ErrorMessage != `` {
		res.ErrorMessage = pieceData.ErrorMessage
		return res, nil
	}

	detailPiece := pieceData.Pieces[0]
	errorMessage, err := c.viewDetailSttValidateCustomProcess(selfCtx, &customProcess.RequestValidateCustomProcess{
		PartnerID:           params.PartnerID,
		PartnerType:         params.PartnerType,
		CountryCode:         countryCode,
		CustomProcessStatus: params.CustomProcessStatus,
		HubID:               params.HubID,
		HubDestinationID:    params.HubDestinationID,
		HubDestinationCity:  params.HubDestinationCity,
		HubDestinationType:  params.HubDestinationType,
		Token:               params.Token,
	}, detailPiece, partnerLuw)
	if err != nil {
		return nil, err
	}
	if errorMessage != `` {
		res.IsAllowUpdateStatus = false
		res.ErrorMessage = errorMessage
		return res, nil
	}
	return c.generateDetailSttCustomProcessResponse(selfCtx, params, pieceData.Pieces)
}

func (c *customProcessCtx) viewDetailSttValidateCustomProcess(ctx context.Context, req *customProcess.RequestValidateCustomProcess, detailPiece model.SttDetailResult, partnerLuw model.Partner) (errorMessage string, err error) {
	opName := "customProcessCtx-viewDetailSttValidateCustomProcess"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": nil, "error": err})
	}()

	ErrSTTCannotBeProccessed := c.DictionaryError.ErrSTTCannotBeProccessed(req.CountryCode)
	if model.IsSttReturnForRtsRtshq[req.CustomProcessStatus] {
		// RTS, RTSHQ, REROUTE
		sttHistory, err := GetSttStatusHistoryBeforeAdjusment(ctx, c.sttPieceHistoryRepo, detailPiece.SttNo, []string{model.INHUB, model.OUTHUB})
		if err != nil || sttHistory == nil {
			return ``, err
		}

		detailPiece.Stt.SttLastStatusID = sttHistory.HistoryStatus

		errorMessage, err = c.viewDetailSttValidateSttReturn(selfCtx, req, detailPiece)
		if err != nil {
			return ``, err
		}
		if errorMessage != `` {
			return errorMessage, nil
		}
	} else if model.IsNotAllowUpdateCustomProcess[detailPiece.SttLastStatusID] {
		return ErrSTTCannotBeProccessed, nil
	}

	// for interpack
	errorMessage = c.viewDetailSttValidateCustomProcessForInterpack(selfCtx, req, detailPiece, partnerLuw)
	if errorMessage != `` {
		return errorMessage, nil
	}

	return c.validateCustomProcessStatus(selfCtx, detailPiece, req)
}

func (c *customProcessCtx) viewDetailSttValidateAccount(ctx context.Context, params *customProcess.DetailCustomProcessRequest) (partner model.Partner, countryCode string, err error) {
	opName := "customProcessCtx-viewDetailSttValidateAccount"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": partner, "error": err})
	}()

	countryCode, err = c.viewDetailSttCheckAccountRoleValid(selfCtx, params)
	if err != nil {
		return partner, ``, err
	}

	mapAccountType := map[string]bool{
		model.INTERNAL:        true,
		model.CUSTOMERSERVICE: true,
	}
	if mapAccountType[params.AccountType] || model.IsForInterpack[params.CustomProcessStatus] {
		partnerData, err := c.partnerRepo.GetByID(selfCtx, params.PartnerID, params.Token)
		if err != nil {
			return partner, ``, shared.ERR_UNEXPECTED_DB
		}
		if partnerData == nil {
			return partner, ``, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner not found",
				"id": "Partner tidak ditemukan",
			})
		}
		partner = *partnerData
		params.PartnerType = partner.Data.Type
	}

	// validate partner type
	if !model.IsAllowedAccountTypeCustomProcess[params.PartnerType] {
		return partner, ``, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner Type Invalid",
			"id": "Tipe Partner tidak valid",
		})
	}

	return partner, countryCode, nil
}

func (c *customProcessCtx) viewDetailSttCheckAccountRoleValid(ctx context.Context, params *customProcess.DetailCustomProcessRequest) (countryCode string, err error) {
	opName := "customProcessCtx-viewDetailSttCheckAccountRoleValid"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": countryCode, "error": err})
	}()

	roleAccount, err := c.customProcessRoleRepo.Select(selfCtx, &model.CustomProcessRoleViewParams{
		AccountRoleType: params.AccountRoleType,
		AccountType:     params.AccountType,
	})
	if err != nil {
		return ``, shared.ERR_UNEXPECTED_DB
	}
	if len(roleAccount) < 1 {
		return ``, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Account Type Invalid",
			"id": "Tipe Akun tidak valid",
		})
	}

	isStatusValid := map[string]bool{}
	for _, val := range roleAccount {
		isStatusValid[val.RoleCustomProcessStatus] = true
	}
	if !isStatusValid[params.CustomProcessStatus] {
		return ``, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Status is not valid",
			"id": "Status tidak valid",
		})
	}

	account, err := c.accountRepo.GetProfileWithParams(selfCtx, &model.GetProfileRequest{Token: params.Token, NoGetTieringPos: true})
	if err != nil || account == nil {
		return ``, c.DictionaryError.ErrFailedToRetrieveData("Account profile")
	}
	countryCode = account.GetPartnerCountryCode()
	return countryCode, nil
}

func (c *customProcessCtx) viewDetailSttValidateSttPiece(ctx context.Context, params *customProcess.DetailCustomProcessRequest, countryCode string) (data *customProcess.DetailCustomProcessSttPieceValidation, err error) {
	opName := "customProcessCtx-viewDetailSttValidateSttPiece"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	data = &customProcess.DetailCustomProcessSttPieceValidation{}
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": data, "error": err})
	}()

	// get is detailPieces exist
	pieces, err := c.getSttDetail(selfCtx, params.SttNo)
	if err != nil {
		return nil, err
	}
	if len(pieces) == 0 {
		data.ErrorMessage = c.DictionaryError.ErrDataNotFoundString(countryCode, "STT")
		return data, nil
	}
	data.Pieces = pieces

	if pieces[0].Stt.SttLastStatusID == model.CNXCD {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt Cannot Be Processed Cause CNXCD status is the final status",
			"id": "Nomor STT tidak dapat diproses Karena status CNXCD adalah status akhir",
		})
	}
	// validation stt product type jumbopack h2h
	if pieces[0].Stt.SttProductType == model.JUMBOPACKH2H {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "The STT failed to scan because the package will be given POD status after it is received by the recipient",
			"id": "STT gagal discan karena paket akan diberi status POD setelah diambil oleh penerima.",
		})
	}

	if err = c.validateCustomProcessCrossDocking(pieces[0], params.CustomProcessStatus); err != nil {
		return nil, err
	}

	if !model.IsForInterpack[params.CustomProcessStatus] {
		return data, err
	}

	data.ErrorMessage, err = c.viewDetailSttCheckhPieceHistoryValid(selfCtx, pieces)
	if err != nil {
		return nil, err
	}
	return data, nil
}

func (c *customProcessCtx) getSttDetail(ctx context.Context, sttNo string) (data []model.SttDetailResult, err error) {
	opName := "customProcessCtx-getSttDetail"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	data = []model.SttDetailResult{}
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": sttNo, "result": data, "error": err})
	}()

	pieces, err := c.sttPieceRepo.SelectDetail(selfCtx, &model.SttPiecesViewParam{
		SttNo: sttNo,
	})
	if err != nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting STT and Piece",
			"id": "Terjadi kesalahan pada saat getting STT and Piece",
		})
	}
	return pieces, nil
}

func (c *customProcessCtx) validateCustomProcessCrossDocking(detailPiece model.SttDetailResult, customProcessStatus string) (err error) {
	mapCustomStatusAllowedCrossdocking := map[string]bool{
		model.SCRAPCD: true,
		model.HALCD:   true,
	}

	sttMeta := detailPiece.Stt.SttMetaToStruct()
	isSttCrossdocking := sttMeta != nil && sttMeta.IsSttCrossdocking && detailPiece.Stt.SttLastStatusID == model.BAGGING
	if isSttCrossdocking && !mapCustomStatusAllowedCrossdocking[customProcessStatus] {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt No or Bagging No Cannot Be Processed",
			"id": "Nomor STT atau Nomor Bagging tidak dapat diproses",
		})
	}

	var errMsg error
	var errSttNotAllowed error
	isNotAllowProcessStt := false
	mapSttLastStatus := map[string]bool{
		detailPiece.Stt.SttLastStatusID: true,
	}
	isNotSttRefExtTKP01 := (detailPiece.Stt.SttNoRefExternal == `` || !shared.IsLiloPrefix(detailPiece.Stt.SttNoRefExternal))
	switch {
	case customProcessStatus == model.CNXCD:
		isNotAllowProcessStt = isNotSttRefExtTKP01
		errSttNotAllowed = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt No Cannot Be Proccess Cause STT is not TKP01",
			"id": "Nomor STT tidak dapat diproses karena STT bukan TKP01",
		})

		mapSttLastStatus = map[string]bool{
			model.HALCD: true,
		}
		errMsg = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt No Cannot Be Proccess Cause last status not HALCD",
			"id": "Nomor STT tidak dapat diproses karena status akhir bukan HALCD",
		})

	// HALCD Validate
	case customProcessStatus == model.HALCD:
		isNotAllowProcessStt = !model.IsSttAllowUpdateHALCD[detailPiece.Stt.SttNo[:2]] || isNotSttRefExtTKP01
		errSttNotAllowed = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt Cannot Be Proccess",
			"id": "Nomor STT tidak dapat diproses",
		})

		mapSttLastStatus = model.IsStatusbeforeSTI
		errMsg = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt No Cannot Be Proccess Cause last status not BKD or Bagging",
			"id": "Nomor STT tidak dapat diproses karena status akhir bukan BKD atau Bagging",
		})
	}

	if isNotAllowProcessStt {
		return errSttNotAllowed
	}

	if !mapSttLastStatus[detailPiece.Stt.SttLastStatusID] {
		return errMsg
	}
	return nil
}

func (c *customProcessCtx) viewDetailSttCheckhPieceHistoryValid(ctx context.Context, pieces []model.SttDetailResult) (errorMessage string, err error) {
	opName := "customProcessCtx-viewDetailSttValidateSttPiece"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": errorMessage, "error": err})
	}()

	for _, piece := range pieces {
		histories, err := c.sttPieceHistoryRepo.Select(selfCtx, &model.SttPieceHistoryViewParam{
			SttPieceHistorySttPieceID: piece.SttPieceID,
			Order:                     true,
			OrderDesc:                 true,
		})
		if len(histories) == 0 || err != nil {
			return ``, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "STT Piece History is not found",
				"id": "STT Piece History tidak ditemukan",
			})
		}

		mapHistoryStatus := map[string]bool{}
		for _, val := range histories {
			mapHistoryStatus[val.HistoryStatus] = true
		}
		if mapHistoryStatus[model.INTHND] {
			errorMessage = `Status "INT - HND" ke "OCC - EXP/IMP/HAL" hanya bisa diperbarui oleh Vendor. Tunggu atau hubungi Vendor`
			return errorMessage, nil
		}
	}
	return ``, nil
}

func (c *customProcessCtx) viewDetailSttValidateSttReturn(ctx context.Context, req *customProcess.RequestValidateCustomProcess, detailPiece model.SttDetailResult) (errorMessage string, err error) {
	opName := "customProcessCtx-viewDetailSttValidateSttReturn"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": errorMessage, "error": err})
	}()

	ErrSTTCannotBeProccessed := c.DictionaryError.ErrSTTCannotBeProccessed(req.CountryCode)
	isNotAllowedProcessStt := false

	detailReverseJourney := detailPiece.Stt.GetSttMetaDetailReverseJourney()
	var lastStatusSttReturn = detailReverseJourney.ReverseJourneyStatusStt
	var sttNoReverseJourney = detailReverseJourney.ReverseSttNo

	switch {
	case req.CustomProcessStatus == lastStatusSttReturn:
		isNotAllowedProcessStt = true
	case req.CustomProcessStatus == model.RTSHQ:
		if !model.IsAllowCustomStatusRTSHQ[detailPiece.SttLastStatusID] {
			isNotAllowedProcessStt = true
			break
		}
		isAllow, err := c.viewDetailSttValidateSttReturnRtsHq(selfCtx, detailPiece, sttNoReverseJourney)
		if err != nil {
			return ``, err
		}

		isNotAllowedProcessStt = !isAllow
	default:
		isNotAllowedProcessStt = !c.viewDetailSttCheckIsAllowCustomProcessReturn(req, detailPiece, lastStatusSttReturn)
	}

	if isNotAllowedProcessStt {
		return ErrSTTCannotBeProccessed, nil
	}

	return ``, c.waitClientConfirmation(selfCtx, req, detailPiece.Stt)
}

func (c *customProcessCtx) viewDetailSttCheckIsAllowCustomProcessReturn(req *customProcess.RequestValidateCustomProcess, detailPiece model.SttDetailResult, lastStatusSttReturn string) (isAllow bool) {

	isNotAllowedProcessStt := false
	switch {
	case !model.IsAllowedStatusReturnToRtsRtshq[detailPiece.SttLastStatusID]:
		isNotAllowedProcessStt = true
	case detailPiece.SttLastStatusID == model.MISBOOKING:
		isNotAllowedProcessStt = detailPiece.SttUpdatedActorID.Int64 != int64(req.PartnerID) || detailPiece.SttUpdatedActorRole.String != req.PartnerType
	case req.CustomProcessStatus == model.REROUTE:
		isNotAllowedProcessStt = !model.IsAllowCustomStatusReroute[detailPiece.SttLastStatusID]
	case req.CustomProcessStatus == model.RTS:
		isNotRerouteFromConsole := (req.PartnerType != model.CONSOLE && lastStatusSttReturn == model.REROUTE)
		isNotAllowedProcessStt = lastStatusSttReturn == model.RTSHQ || isNotRerouteFromConsole
	}
	return !isNotAllowedProcessStt
}

func (c *customProcessCtx) viewDetailSttValidateSttReturnRtsHq(ctx context.Context, detailPiece model.SttDetailResult, sttNoReverseJourney string) (isAllowProcessStt bool, err error) {
	opName := "customProcessCtx-viewDetailSttValidateSttReturnRtsHq"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": isAllowProcessStt, "error": err})
	}()

	isNotAllowUpdateToRTSHQ := true
	if sttNoReverseJourney != `` {
		sttReverseDetails, err := c.sttPieceRepo.SelectDetail(selfCtx, &model.SttPiecesViewParam{
			SttNo: sttNoReverseJourney,
		})
		if err != nil {
			return false, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting STT reverse journey",
				"id": "Terjadi kesalahan pada saat mengambil data STT reverse journey",
			})
		}
		if len(sttReverseDetails) > 0 {
			detailPiece = sttReverseDetails[0]
		}
		isNotAllowUpdateToRTSHQ = detailPiece.SttBookedForType != model.CLIENT && model.IsNotAllowSttReverseJourneyUpdateToRTSHQ[detailPiece.SttLastStatusID]
	}

	checkShipmentIDCodCARetail := len(detailPiece.SttShipmentID) >= 4 && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(detailPiece.SttShipmentID)]

	checkShipmentIDCodApAs := len(detailPiece.SttShipmentID) >= 4 && model.MappingShipmentPrefixApAs[shared.GetPrefixShipmentID(detailPiece.SttShipmentID)] && (detailPiece.SttIsCOD || detailPiece.SttIsDFOD)

	checkSttIsCodDfod := detailPiece.SttBookedForType != model.CLIENT && (detailPiece.SttIsCOD || detailPiece.SttIsDFOD)

	isNotAllowProcessStt := (checkShipmentIDCodCARetail || checkShipmentIDCodApAs) || (checkSttIsCodDfod && isNotAllowUpdateToRTSHQ)
	return !isNotAllowProcessStt, nil
}

func (c *customProcessCtx) viewDetailSttValidateCustomProcessForInterpack(ctx context.Context, req *customProcess.RequestValidateCustomProcess, detailPiece model.SttDetailResult, partnerLuw model.Partner) (errorMessage string) {
	opName := "customProcessCtx-viewDetailSttValidateCustomProcessForInterpack"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var errLog error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": errorMessage, "error": errLog})
	}()

	if !model.IsForInterpack[req.CustomProcessStatus] {
		return ``
	}

	if !strings.EqualFold(detailPiece.Stt.SttProductType, model.INTERPACK) {
		return c.DictionaryError.ErrInterpackOnlyAllowed(req.CountryCode)
	}

	if partnerLuw.Data.PartnerLocation == nil {
		return c.DictionaryError.ErrDataNotFoundString(req.CountryCode, "Partner Location")
	}

	// Return error if SttLastStatus is one of OCCEXP, OCCIMP, OCCHAL
	if detailPiece.Stt.SttLastStatusID == req.CustomProcessStatus {
		return c.DictionaryError.ErrSTTCannotBeProccessed(req.CountryCode)
	}

	if req.CustomProcessStatus == model.RCCIMP && !release.ReleaseUpdateBulkLastStatusAllowed[detailPiece.Stt.SttLastStatusID] {
		return c.DictionaryError.ErrSTTCannotBeProccessed(req.CountryCode)

	}

	partnerCityCode := partnerLuw.Data.PartnerLocation.CityCode
	switch req.CustomProcessStatus {
	case model.OCCEXP:
		return c.validateCustomClearanceExport(selfCtx, req, detailPiece, partnerCityCode)
	case model.OCCIMP:
		if partnerCityCode == detailPiece.Stt.SttOriginCityID {
			return c.DictionaryError.ErrStatusNotValidWithStatusSuggest(req.CountryCode, `OCC - EXP`)
		}
	}

	return ``
}

func (c *customProcessCtx) validateCustomClearanceExport(ctx context.Context, req *customProcess.RequestValidateCustomProcess, detailPiece model.SttDetailResult, partnerCityCode string) (errorMessage string) {
	opName := "customProcessCtx-validateCustomClearanceExport"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": errorMessage})
	}()

	ErrSTTCannotBeProccessed := c.DictionaryError.ErrSTTCannotBeProccessed(req.CountryCode)
	isNotFound := func(v interface{}, err error) bool {
		return err != nil || shared.IsEmpty(v)
	}

	cityCountryPartner, err := c.cityRepo.Get(selfCtx, partnerCityCode, req.Token)
	if isNotFound(cityCountryPartner, err) {
		return ErrSTTCannotBeProccessed
	}
	cityCountrySttOrigin, err := c.cityRepo.Get(selfCtx, detailPiece.Stt.SttOriginCityID, req.Token)
	if isNotFound(cityCountrySttOrigin, err) {
		return ErrSTTCannotBeProccessed
	}

	if detailPiece.Stt.SttLastStatusID == model.OCCIMP && cityCountryPartner.Country.Name == cityCountrySttOrigin.Country.Name {
		return ErrSTTCannotBeProccessed
	}

	if cityCountryPartner.Country.Name != cityCountrySttOrigin.Country.Name {
		return c.DictionaryError.ErrStatusNotValidWithStatusSuggest(req.CountryCode, `OCC - IMP`)
	}
	return ``
}

func (c *customProcessCtx) validateCustomProcessStatus(ctx context.Context, detailPiece model.SttDetailResult, req *customProcess.RequestValidateCustomProcess) (errorMessage string, err error) {
	opName := "customProcessCtx-validateCustomProcessStatus"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var errLog error
	customProcessStatus := req.CustomProcessStatus
	countryCode := req.CountryCode

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": errorMessage, "error": errLog})
	}()

	lastStatus := detailPiece.Stt.SttLastStatusID
	ErrSTTCannotBeProccessed := c.DictionaryError.ErrSTTCannotBeProccessed(countryCode)
	isNotAllowUpdateCustomProcess := model.IsAllowedStatusCustomProcessNew[customProcessStatus] && model.IsNotAllowUpdateCustomProcessNew[lastStatus]

	switch customProcessStatus {
	case model.SCRAPCD:
		isNotAllowUpdateCustomProcess = !model.IsAllowUpdateCustomStatusScrapCD[lastStatus]
	case model.OCC:
		isNotAllowUpdateCustomProcess = model.IsNotAllowedStatusCustomProcessOCC[detailPiece.SttLastStatusID]
	case model.MISBOOKING:
		isNotAllowUpdateCustomProcess = !model.IsAllowUpdateMisbooking[detailPiece.SttLastStatusID]
	case model.HAL:
		isNotAllowUpdateCustomProcess = model.IsNotAllowUpdateToHAL[detailPiece.SttLastStatusID]
	case model.INHUB, model.OUTHUB:
		isNotAllow, err := c.validateCustomProcessToHUB(selfCtx, req, detailPiece)
		if err != nil {
			return ``, err
		}
		isNotAllowUpdateCustomProcess = isNotAllow
	case model.NOTRECEIVED:
		isNotAllowUpdateCustomProcess = model.IsNotAllowNotReceived[detailPiece.SttLastStatusID]
	default:
		isAllow, err := c.validateCustomProcessClaimAndRejected(selfCtx, detailPiece, customProcessStatus)
		if err != nil {
			return ``, err
		}
		isNotAllowUpdateCustomProcess = !isAllow
	}

	if isNotAllowUpdateCustomProcess {
		return ErrSTTCannotBeProccessed, nil
	}
	return c.validateLastStatus(detailPiece, customProcessStatus, countryCode), nil
}

func (c *customProcessCtx) validateCustomProcessToHUB(ctx context.Context, req *customProcess.RequestValidateCustomProcess, sttDetail model.SttDetailResult) (bool, error) {
	// get last status before adjustment (adjusted)
	sttHistory, err := GetSttStatusHistoryBeforeAdjusment(ctx, c.sttPieceHistoryRepo, sttDetail.SttNo, []string{})
	if err != nil {
		return true, err
	}

	sttLastStatus := sttHistory.HistoryStatus
	isAllowWithTheSameStatus := c.ValidateToTheSameStatus(ctx, &ValidateToTheSameStatusParams{
		Status:             req.CustomProcessStatus,
		HubID:              req.HubID,
		HubDestinationID:   req.HubDestinationID,
		HubDestinationCity: req.HubDestinationCity,
		SttHistory:         sttHistory,
		PartnerId:          req.PartnerID,
	})
	if !isAllowWithTheSameStatus {
		return true, nil
	}

	rules, ok := model.IsAllowUpdateHub[req.CustomProcessStatus]
	if !ok {
		return true, nil
	}

	if !rules.Allowed[sttLastStatus] {
		return true, nil
	}

	if rules.NotAllowed[sttLastStatus] {
		return true, nil
	}

	return false, nil
}

func (c *customProcessCtx) validateCustomProcessClaimAndRejected(ctx context.Context, detailPiece model.SttDetailResult, customProcessStatus string) (isAllowProcessStt bool, err error) {
	opName := "customProcessCtx-validateCustomProcessClaimAndRejected"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var errLog error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": isAllowProcessStt, "error": errLog})
	}()

	isNotAllowUpdateCustomProcess := false
	switch customProcessStatus {
	case model.CLAIM:
		historyClaim, err := c.sttPieceHistoryRepo.Get(selfCtx, &model.SttPieceHistoryViewParam{
			SttPieceHistorySttPieceID: detailPiece.SttPieceID,
			SttPieceHistoryStatus:     model.CLAIM,
		})
		if err != nil {
			return false, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed get data history status",
				"id": "Gagal ambil data histori status",
			})
		}
		isNotAllowUpdateCustomProcess = historyClaim != nil

	case model.REJECTED:
		/**
		 * Check last status stt eligible or not to update to REJECTED
		 */
		isNotAllowUpdateCustomProcess = !model.IsAllowUpdateRejected[detailPiece.SttLastStatusID]
		switch {
		case detailPiece.SttLastStatusID == model.STTREMOVE:
			history, err := c.sttPieceHistoryRepo.Select(selfCtx, &model.SttPieceHistoryViewParam{
				SttPieceHistorySttPieceID: detailPiece.SttPieceID,
				Order:                     true,
				OrderDesc:                 true,
			})
			if err != nil || len(history) < 2 {
				return false, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
					"en": "STT Piece History is not found",
					"id": "STT Piece History tidak ditemukan",
				})
			}

			isNotAllowUpdateCustomProcess = history[1].HistoryStatus != model.CARGOPLANE
		}
	}
	return !isNotAllowUpdateCustomProcess, nil
}

func (c *customProcessCtx) validateLastStatus(detailPiece model.SttDetailResult, customProcessStatus string, countryCode string) (errorMessage string) {
	ErrSTTCannotBeProccessed := c.DictionaryError.ErrSTTCannotBeProccessed(countryCode)
	lastStatus := detailPiece.Stt.SttLastStatusID

	/**  Validate last status REROUTE */
	isNotAllowUpdateCustomProcess := false

	statusIsTheSame := lastStatus == customProcessStatus && !model.IsAllowToTheSameStatus[customProcessStatus]
	switch {
	case lastStatus == model.REROUTE || statusIsTheSame:
		isNotAllowUpdateCustomProcess = true
	case customProcessStatus == model.CI:
		isNotAllowUpdateCustomProcess = detailPiece.SttLastStatusID != model.MISSING
	case lastStatus == model.CI:
		/**  Validate last status CI */
		isNotAllowUpdateCustomProcess = !model.IsAllowedLastStatusCIUpdateCustomProcess[customProcessStatus]
	case lastStatus == model.SCRAPCD:
		/**  Validate last status SCRAP-CD */
		isNotAllowUpdateCustomProcess = model.IsNotAllowUpdateAfterScrapCDLastStatus[customProcessStatus]
	case model.TruckingStatusMapping[lastStatus]:
		isNotAllowUpdateCustomProcess = model.IsNotAllowedCustomProcessStatusForLastStatusTrucking[customProcessStatus]
	default:
		isNotAllowUpdateCustomProcess = c.validateSttNoRefLastStatus(detailPiece, customProcessStatus)
	}

	if isNotAllowUpdateCustomProcess {
		return ErrSTTCannotBeProccessed
	}
	return ``
}

func (c *customProcessCtx) validateSttNoRefLastStatus(detailPiece model.SttDetailResult, customProcessStatus string) (isNotAllowUpdateCustomProcess bool) {
	lastStatus := detailPiece.Stt.SttLastStatusID
	if !shared.IsBagNoReff(detailPiece.Stt.SttNoRefExternal) {
		switch {
		case lastStatus == model.MISBOOKING:
			isNotAllowUpdateCustomProcess = !model.IsAllowUpdateAfterMisbookingLastStatus[customProcessStatus]
		case lastStatus == model.CODREJ:
			// validate if last status CODREJ
			isNotAllowUpdateCustomProcess = !model.IsAllowedLastStatusCodRejUpdateCustomProcess[customProcessStatus]
		case lastStatus == model.DAMAGE:
			/**  Validate last status damage */
			isNotAllowUpdateCustomProcess = model.IsNotAllowedLastStatusDamageUpdateCustomProcess[customProcessStatus]
		case lastStatus == model.NOTRECEIVED:
			/**  Validate last status not received */
			isNotAllowUpdateCustomProcess = model.IsNotAllowedLastStatusNotReceiveUpdateCustomProcess[customProcessStatus]
		case lastStatus == model.MISROUTE:
			/**  Validate last status MISROUTE */
			isNotAllowUpdateCustomProcess = !model.IsAllowedLastStatusMisrouteUpdateCustomProcess[customProcessStatus]
		}

		if customProcessStatus == model.SCRAPCD {
			isNotAllowUpdateCustomProcess = true
		}
	}
	return isNotAllowUpdateCustomProcess
}

func (c *customProcessCtx) generateDetailSttCustomProcessResponse(ctx context.Context, params *customProcess.DetailCustomProcessRequest, detailPieces []model.SttDetailResult) (res *customProcess.DetailSttCustomProcessResponse, err error) {
	opName := "customProcessCtx-generateDetailSttCustomProcessResponse"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res = &customProcess.DetailSttCustomProcessResponse{}
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": res, "error": err})
	}()

	detailPiece := detailPieces[0]
	res.IsAllowUpdateStatus = !model.IsNotAllowUpdateCustomProcess[detailPiece.SttLastStatusID]
	if model.IsSttReturnForRtsRtshq[params.CustomProcessStatus] {
		res.IsAllowUpdateStatus = model.IsAllowedStatusCustomProcessRTSHQ[detailPiece.SttLastStatusID]
	}
	if detailPiece.SttLastStatusID == model.CI {
		res.IsAllowUpdateStatus = model.IsAllowedLastStatusCIUpdateCustomProcess[params.CustomProcessStatus]
	}

	res.IsPaid = detailPiece.GetSttPaymentStatus() == model.PAID
	if shared.GetPrefixSttNo(detailPiece.SttNo) == model.PrefixAutoCA && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(detailPiece.SttShipmentID)] {
		res.IsPaid = true
	}
	if !res.IsPaid && model.IsEligibleSttUnpaidUpdateCustomProcess[params.CustomProcessStatus] {
		res.IsPaid = true
	}

	return c.buildDetailSttCustomProcessResponse(selfCtx, params, detailPieces, res)
}

func (c *customProcessCtx) buildDetailSttCustomProcessResponse(ctx context.Context, params *customProcess.DetailCustomProcessRequest, detailPieces []model.SttDetailResult, tmpData *customProcess.DetailSttCustomProcessResponse) (res *customProcess.DetailSttCustomProcessResponse, err error) {
	opName := "customProcessCtx-buildDetailSttCustomProcessResponse"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": res, "error": err})
	}()

	detailStts := map[string]*general.SttResponseCustomProcess{}
	for _, piece := range detailPieces {
		if _, ok := detailStts[piece.SttNo]; !ok {
			detailReverseJourney := piece.GetSttMetaDetailReverseJourney()
			lastStatusSttReturn := detailReverseJourney.ReverseJourneyStatusStt
			// SttResponseCustomProcess
			detailStts[piece.SttNo] = &general.SttResponseCustomProcess{
				SttResponse: general.SttResponse{
					SttID:                  piece.SttID,
					SttNo:                  piece.SttNo,
					SttProductType:         piece.SttProductType,
					SttGrossWeight:         piece.SttGrossWeight,
					SttVolumeWeight:        piece.SttVolumeWeight,
					SttLastStatusID:        piece.SttLastStatusID,
					SttCODAmount:           piece.SttCODAmount,
					SttDestinationCityID:   piece.SttDestinationCityID,
					SttDestinationCityName: piece.SttDestinationCityName,
					SttOriginCityID:        piece.SttOriginCityID,
					SttOriginCityName:      piece.SttOriginCityName,
					SttChargeableWeight:    piece.SttChargeableWeight,
					SttTotalPiece:          piece.SttTotalPiece,
					BookedAt:               &piece.SttCreatedAt,
					POSName:                piece.SttBookedName,
					SttElexysNo:            piece.SttElexysNo.Value(),
					BookedForID:            piece.SttBookedForID,
					BookedForName:          piece.SttBookedForName,
					BookedForType:          piece.SttBookedByType,
				},
				SttReturnLastStatus: lastStatusSttReturn,
			}

			recipientDetail, err := c.getCustomProcessRTSRecipientDetail(selfCtx, piece.Stt, params)
			if err != nil {
				return nil, err
			}
			if recipientDetail != nil {
				detailStts[piece.SttNo].SttReturnCityCode = recipientDetail.SttReturnCityCode
				detailStts[piece.SttNo].SttReturnCityName = recipientDetail.SttReturnCityName
				detailStts[piece.SttNo].SttReturnDistrictCode = recipientDetail.SttReturnDistrictCode
				detailStts[piece.SttNo].SttReturnDistrictName = recipientDetail.SttReturnDistrictName
				detailStts[piece.SttNo].SttReturnReceiptName = recipientDetail.SttReturnReceiptName
				detailStts[piece.SttNo].SttReturnReceiptPhone = recipientDetail.SttReturnReceiptPhone
				detailStts[piece.SttNo].SttReturnReceiptAddress = recipientDetail.SttReturnReceiptAddress
				detailStts[piece.SttNo].SttReturnReceiptAddressType = recipientDetail.SttReturnReceiptAddressType
				detailStts[piece.SttNo].IsReturnToOriginAddress = recipientDetail.IsReturnToOriginAddress
			}

			c.getCustomProcessDetailReverseDestination(selfCtx, &detailStts, piece.Stt, params)

			tmpData.Stt = append(tmpData.Stt, detailStts[piece.SttNo])
		}

		detailStts[piece.SttNo].Piece = append(detailStts[piece.SttNo].Piece, general.SttPieceResponse{
			SttPieceID:           piece.SttPieceID,
			SttPieceNo:           piece.SttPieceNo,
			SttPieceGrossWeight:  piece.SttPieceGrossWeight,
			SttPieceVolumeWeight: piece.SttPieceVolumeWeight,
			SttPieceLastStatusID: piece.SttPieceLastStatusID,
		})
		// build detail piece(s) each of stt
	}

	res = tmpData
	return res, nil
}

func (c *customProcessCtx) getCustomProcessRTSRecipientDetail(ctx context.Context, stt model.Stt, params *customProcess.DetailCustomProcessRequest) (res *customProcess.CustomProcessRTSRecipientDetail, err error) {
	opName := "customProcessCtx-getCustomProcessRTSRecipientDetail"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var errLog error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": res, "error": errLog})
	}()

	if params.CustomProcessStatus != model.RTS {
		return nil, nil
	}

	detailReverseJourney := stt.GetSttMetaDetailReverseJourney()
	lastStatusSttReturn := detailReverseJourney.ReverseJourneyStatusStt

	if lastStatusSttReturn == model.REROUTE {
		sttDataRef, err := c.sttRepo.Get(selfCtx, &model.SttViewDetailParams{
			Stt: model.Stt{
				SttNo: detailReverseJourney.RootReverseSttNo,
			},
		})
		if err != nil || sttDataRef == nil {
			errLog = err
			return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to get Stt Reverse data",
				"id": "Gagal mendapatkan data Stt Reverse",
			})
		}
		res = &customProcess.CustomProcessRTSRecipientDetail{}
		res.SttReturnCityCode = sttDataRef.SttOriginCityID
		res.SttReturnCityName = sttDataRef.SttOriginCityName
		res.SttReturnDistrictCode = sttDataRef.SttOriginDistrictID
		res.SttReturnDistrictName = sttDataRef.SttOriginDistrictName
		res.SttReturnReceiptName = sttDataRef.SttSenderName
		res.SttReturnReceiptPhone = sttDataRef.SttSenderPhone
		res.SttReturnReceiptAddress = sttDataRef.SttSenderAddress
		res.SttReturnReceiptAddressType = sttDataRef.SttRecipientAddressType.String
		res.IsReturnToOriginAddress = true
		stt = *sttDataRef
	}

	resp, err := c.getCustomProcessRTSCODRecipientDetail(selfCtx, stt, params.Token)
	if err != nil {
		return nil, err
	}
	if resp != nil {
		res = resp
	}
	return res, nil
}

func (c *customProcessCtx) getCustomProcessDetailReverseDestination(ctx context.Context, detailStts *map[string]*general.SttResponseCustomProcess, stt model.Stt, params *customProcess.DetailCustomProcessRequest) {
	if params.CustomProcessStatus != model.REROUTE {
		return
	}

	sttMetaStruct := stt.SttMetaToStruct()
	if sttMetaStruct != nil && sttMetaStruct.ReverseDestination != nil {
		(*detailStts)[stt.SttNo].IsUpdatedByInternal = sttMetaStruct.ReverseDestination.IsMisbookingByInternal

		(*detailStts)[stt.SttNo].ReverseDestination = general.ReverseDestination{
			Remarks:                  sttMetaStruct.ReverseDestination.Remarks,
			SttProductType:           sttMetaStruct.ReverseDestination.SttProductType,
			ReturnCityCode:           sttMetaStruct.ReverseDestination.ReturnCityCode,
			ReturnCityName:           sttMetaStruct.ReverseDestination.ReturnCityName,
			ReturnDistrictCode:       sttMetaStruct.ReverseDestination.ReturnDistrictCode,
			ReturnDistrictName:       sttMetaStruct.ReverseDestination.ReturnDistrictName,
			ReturnReceiptAddress:     sttMetaStruct.ReverseDestination.ReturnReceiptAddress,
			ReturnReceiptAddressType: sttMetaStruct.ReverseDestination.ReturnReceiptAddressType,
			ReturnReceiptName:        sttMetaStruct.ReverseDestination.ReturnReceiptName,
			ReturnReceiptPhone:       sttMetaStruct.ReverseDestination.ReturnReceiptPhone,
			SttDestinationZipCode:    sttMetaStruct.ReverseDestination.SttDestinationZipCode,
			SttCommodityCode:         sttMetaStruct.ReverseDestination.SttCommodityCode,
			SttGoodsStatus:           sttMetaStruct.ReverseDestination.SttGoodsStatus,
			SttTaxNumber:             sttMetaStruct.ReverseDestination.SttTaxNumber,
			SttPiecePerPack:          sttMetaStruct.ReverseDestination.SttPiecePerPack,
			SttNextCommodity:         sttMetaStruct.ReverseDestination.SttNextCommodity,
		}
	}
}

func (c *customProcessCtx) getCustomProcessRTSCODRecipientDetail(ctx context.Context, stt model.Stt, token string) (res *customProcess.CustomProcessRTSRecipientDetail, err error) {
	opName := "customProcessCtx-getCustomProcessRTSCODRecipientDetail"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var errLog error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": res, "error": errLog})
	}()

	isSttCOD, err := c.checkShipmentIsCODValid(selfCtx, stt)
	if err != nil {
		return nil, err
	}

	if isSttCOD {
		res = &customProcess.CustomProcessRTSRecipientDetail{
			SttReturnCityCode:           stt.SttOriginCityID,
			SttReturnCityName:           stt.SttOriginCityName,
			SttReturnDistrictCode:       stt.SttOriginDistrictID,
			SttReturnDistrictName:       stt.SttOriginDistrictName,
			SttReturnReceiptName:        stt.SttSenderName,
			SttReturnReceiptPhone:       stt.SttSenderPhone,
			SttReturnReceiptAddressType: stt.SttRecipientAddressType.String,
			IsReturnToOriginAddress:     true,
		}

		partner, err := c.partnerRepo.GetByID(selfCtx, stt.SttBookedBy, token)
		if err != nil || partner == nil {
			errLog = err
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to get partner data",
				"id": "Gagal mendapatkan data partner",
			})
		}
		res.SttReturnReceiptAddress = partner.Data.Address
		res.SttReturnReceiptName = partner.Data.PartnerContactName
		res.SttReturnReceiptPhone = partner.Data.PartnerContactPhoneNumbers

	}
	return res, nil
}

func (c *customProcessCtx) checkShipmentIsCODValid(ctx context.Context, stt model.Stt) (isShipmentCODValid bool, err error) {

	sttShipmentID := stt.SttShipmentID
	var isC1SpecialCOD, isShipmentFavoritePrefix, isPrefixCODCustomerAppRetail bool
	shipmentPrefix := ``
	if sttShipmentID != `` {
		shipmentPrefix = shared.GetPrefixShipmentID(sttShipmentID)
	}

	if isC1SpecialCOD, err = c.isShipmentSpecialCOD(ctx, sttShipmentID); err != nil {
		return false, err
	}
	isShipmentFavoritePrefix = model.IsShipmentFavorite[shipmentPrefix]
	isPrefixCODCustomerAppRetail = model.MappingShipmentPrefixCODCustomerAppsRetail[shipmentPrefix]

	isShipmentCODValid = isPrefixCODCustomerAppRetail || isC1SpecialCOD || isShipmentFavoritePrefix
	isSttCOD := (stt.SttIsCOD && (stt.SttBookedByType == model.POS && stt.SttBookedForType == model.POS)) || isShipmentCODValid
	return isSttCOD, nil
}

func (c *customProcessCtx) waitClientConfirmation(ctx context.Context, req *customProcess.RequestValidateCustomProcess, stt model.Stt) error {
	var customProcessConfirmation = map[string]bool{
		model.RTS:   true,
		model.RTSHQ: true,
	}
	if !customProcessConfirmation[req.CustomProcessStatus] {
		return nil
	}

	priorityDel, err := c.priorityDeliveryRepo.GetBySttNo(ctx, stt.SttNo)
	if err != nil {
		return shared.ERR_UNEXPECTED_DB
	}

	priorityMeta := priorityDel.MetaToStruct()
	priorityIsNotShow := priorityDel != nil && priorityDel.PdIsShow == 0
	isTicketSfIDNotEmpty := priorityMeta != nil && priorityMeta.TicketSfID != ""

	isWaitSalesforceConfirmation := priorityIsNotShow && isTicketSfIDNotEmpty
	if isWaitSalesforceConfirmation {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT cannot be updated because it has to wait for customer/client confirmation.",
			"id": "STT tidak bisa di update karena harus menunggu konfirmasi customer/klien.",
		})
	}

	return nil
}
