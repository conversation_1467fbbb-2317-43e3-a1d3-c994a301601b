package usecase

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/abiewardani/dbr/v2"
	"github.com/google/uuid"

	"github.com/Lionparcel/go-lptool/lputils"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/gateway_stt"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *sttCtx) CreateSTTForClient(ctx context.Context, params *stt.CreateSttManualForClient) (*stt.CreateSttResponse, error) {
	opName := "UsecaseStt-CreateSTTForClient"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res := stt.CreateSttResponse{}
	commodityGroupCode := ""
	var errors error
	params.ReplaceInputCharNonASCII()
	defer func() {
		if params.Stt.SttNoRefExternal != "" {
			c.sttRepo.DeleteCacheOnce(selfCtx, fmt.Sprintf("%v:%v", model.CacheRefNoExternal, params.Stt.SttNoRefExternal))
		}
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": errors})
	}()

	/*
		Initialize Error Definition ================================================
	*/

	errClientNotFound := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "Client is not found",
		"id": "Client tidak ditemukan",
	})

	errDistrictCity := func(source string) error {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf("%s District mapped with City is not found", source),
			"id": fmt.Sprintf("%s District yang dipetakan dengan City tidak ditemukan", source),
		})
	}

	errDistrictCityInactive := func(source string) error {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf("%s District mapped with City is inactive", source),
			"id": fmt.Sprintf("%s District yang dipetakan dengan City tidak active", source),
		})
	}

	errCityInactive := func(source string) error {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf("%s City is inactive", source),
			"id": fmt.Sprintf("%s City sedang tidak active", source),
		})
	}

	errCity := func(source string) error {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf("%s City is not available", source),
			"id": fmt.Sprintf("%s City yang dipilih tidak tersedia", source),
		})
	}

	errInsuranceInactive := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
		"en": "Insurance is inactive",
		"id": "Insurance tidak aktif",
	})

	extNumberUsed := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "The external reference number is already in use, please check and change it.",
		"id": "Nomor referensi eksternal sudah terpakai, cek & ubah lagi.",
	})
	/* ========================================================================== */

	if err := c.validateAccountForCreateSttClient(selfCtx, params.Token); err != nil {
		return nil, err
	}

	if err := params.Validation(); err != nil {
		errors = err
		return nil, err
	}

	if err := params.ElexysTariff.Validate(); err != nil && c.cfg.IsElexysConfig() {
		return nil, err
	}

	/**
	 *	Check if the SttNoRefExternal has prefix TKP01-xxxxx
	 * 	then set isKurirRekomendasi to true
	 */
	isKurirRekomendasi := false
	if shared.IsLiloPrefix(params.Stt.SttNoRefExternal) {
		isKurirRekomendasi = true
	}

	bookedClient, err := c.clientRepo.GetByID(selfCtx, params.SttClient, params.Token)
	if err != nil || bookedClient == nil {
		errors = err
		return nil, errClientNotFound
	}

	if bookedClient.Data.ClientIsBanned || strings.ToLower(bookedClient.Data.ClientStatusClient) != model.APPROVED {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"error_type": "ERROR_CLIENT_IS_BANNED",
			"en":         "Client is banned",
			"id":         "Client telah diblokir",
		})
	}

	if bookedClient.Data.ClientIsDO && params.Stt.SttNo != `` {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Client cannot use STT manual",
			"id": "Client tidak bisa menggunakan STT manual",
		})
	}

	if bookedClient.Data.ClientIsDO && params.Stt.SttProductType == model.MIXPACK {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Client cannot use MIXPACK product type",
			"id": "Client tidak bisa menggunakan produk tipe MIXPACK",
		})
	}

	// for Internal/POS/Client for Client, C1, C2; COD Amount can be different from Goods Price
	if !params.Stt.SttIsDFOD && params.Stt.IsCodFeeNotValid(bookedClient) {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "If Cod is true, goods price and cod amount can't 0 and must be same value",
			"id": "Jika Cod true, maka goods price dan cod amount tidak boleh 0 dan harus bernilai sama",
		})
	}

	params.Stt.SttIsDO = bookedClient.Data.ClientIsDO
	if err = c.validateClientCreateSTTForClient(bookedClient, params); err != nil {
		return nil, err
	}
	now, _ := shared.ParseUTC7(shared.FormatDateTime, c.timeRepo.Now(time.Now()).Format(shared.FormatDateTime))
	bookedBy, bookedName, bookedRole, bookedCode, actorExternalCode, bookedByActorDetail, err := c.generateBookedSTTCreateSTTForClient(selfCtx, params, bookedClient)
	if err != nil {
		errors = err
		return nil, err
	}

	commodity, err := c.commodityRepo.GetCommodityByCode(selfCtx, params.Stt.SttCommodityCode, params.Token)
	if err != nil || commodity == nil {
		errors = err
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"error_type": "ERROR_COMMODITY_NOT_FOUND",
			"en":         "Commodity is not found",
			"id":         "Commodity tidak ditemukan",
		})
	}
	if commodity.Data.CommodityStatus != model.ACTIVE {
		return nil, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"error_type": "ERROR_COMMODITY_INACTIVE",
			"en":         "Commodity is inactive",
			"id":         "Commodity tidak aktif",
		})
	}

	if commodity.Data.CommodityCode != "" && commodity.Data.CommodityName != "" {
		commodityGroupCode = commodity.Data.CommodityGroupCode
	}

	if err = c.checkProductTypes(selfCtx, params); err != nil {
		errors = err
		return nil, err
	}

	districtOrigin, err := c.districtRepo.GetByCode(selfCtx, &model.CredentialRestAPI{
		Token: params.Token,
	}, params.Stt.SttOriginDistrictID)

	isDistrictOriginObjectAndDistrictOriginCityObjectNil := districtOrigin == nil || districtOrigin.Data.City == nil
	if err != nil || isDistrictOriginObjectAndDistrictOriginCityObjectNil {
		errors = err
		return nil, errDistrictCity("Origin")
	}

	if strings.ToLower(districtOrigin.Data.Status) != model.ACTIVE {
		return nil, errDistrictCityInactive("Origin")
	}

	cityOrigin, err := c.cityRepo.Get(selfCtx, districtOrigin.Data.City.Code, params.Token)
	if err != nil || cityOrigin == nil {
		errors = err
		return nil, errCity("Origin")
	}

	if strings.ToLower(cityOrigin.IsActive) != model.ACTIVE {
		return nil, errCityInactive("Origin")
	}

	// Check is account is in Limited Assigned 3LC rule
	if err = c.checkIsInsideAssigned3LC(selfCtx, params, cityOrigin.Code); err != nil {
		return nil, err
	}

	districtDestination, err := c.districtRepo.GetByCode(selfCtx, &model.CredentialRestAPI{
		Token: params.Token,
	}, params.Stt.SttDestinationDistrictID)

	isDistrictDestinationObjectAndDistrictDestinationCityObjectNil := districtDestination == nil || districtDestination.Data.City == nil
	if err != nil || isDistrictDestinationObjectAndDistrictDestinationCityObjectNil {
		errors = err
		return nil, errDistrictCity("Destination")
	}

	if strings.ToLower(districtDestination.Data.Status) != model.ACTIVE {
		return nil, errDistrictCityInactive("Destination")
	}

	sttVendor := (districtDestination.Data.Type == model.TYPE_VENDOR || districtDestination.Data.Type == model.TYPE_VENDOR_LANJUTAN)
	sttVendorNinjaOrPI := districtDestination.Data.VendorCode == model.TypeVendorNINJA || districtDestination.Data.VendorCode == model.TypeVendorPTPOS
	sttVendorNotAllowPiecesMoreThan1 := sttVendor && sttVendorNinjaOrPI && len(params.Stt.SttPieces) > 1
	if sttVendorNotAllowPiecesMoreThan1 {
		return nil, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "District destination type doesn't support delivery item that have more than 1 koli wihtin 1 STT",
			"id": "Tipe district destinasi tidak mendukung pengiriman barang yang memiliki lebih dari 1 koli dalam satu STT",
		})
	}

	//validate district for STT DO
	if err := c.validateDistrictTypeSttDO(ctx, params.Stt.SttIsDO, districtDestination); err != nil {
		return nil, err
	}

	cityDestination, err := c.cityRepo.Get(selfCtx, districtDestination.Data.City.Code, params.Token)
	if err != nil || cityDestination == nil {
		errors = err
		return nil, errCity("Destination")
	}

	if strings.ToLower(cityDestination.IsActive) != model.ACTIVE {
		return nil, errCityInactive("Destination")
	}

	params.Stt.SttOriginCityID = districtOrigin.Data.City.Code
	params.Stt.SttDestinationCityID = districtDestination.Data.City.Code

	configPrices, err := c.configurablePriceRepo.Select(selfCtx, &model.ConfigurablePriceViewParams{
		ConfigurablePriceStatus: model.ACTIVE,
		Token:                   params.Token,
	})

	isConfigPriceNil := configPrices == nil || len(configPrices.Data) == 0
	if err != nil || isConfigPriceNil {
		errors = err
		return nil, errInsuranceInactive
	}

	if params.Stt.SttNoRefExternal != `` {
		flag := c.sttRepo.GetCacheRefExternalFlag(selfCtx, params.Stt.SttNoRefExternal)
		if flag {
			return nil, extNumberUsed
		}

		sttCheck, err := c.sttRepo.Get(selfCtx, &model.SttViewDetailParams{
			Stt: model.Stt{SttNoRefExternal: params.Stt.SttNoRefExternal},
		})
		if err != nil {
			errors = err
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to retrieved stt",
				"id": "Gagal mendapatkan stt",
			})
		}

		isSttNotNil := (sttCheck != nil && sttCheck.SttID > 0)
		if isSttNotNil || !c.sttRepo.CreateCacheRefExternalFlag(selfCtx, params.Stt.SttNoRefExternal) {
			return nil, extNumberUsed
		}
	}

	isInsuraceActive := false
	for _, configPrice := range configPrices.Data {
		if configPrice.ConfigurablePriceType == model.INSURANCE {
			isInsuraceActive = true
			break
		}
	}

	if !isInsuraceActive {
		return nil, errInsuranceInactive
	}

	isMixpack := params.Stt.SttProductType == model.MIXPACK
	IsSttManual := false
	sttManual := new(model.SttManual)
	if params.Stt.SttNo != `` {
		checkSTT, err := c.getSttManual(selfCtx, params, isMixpack)
		if err != nil {
			errors = err
			return nil, err
		}

		sttManual = checkSTT
		IsSttManual = true
	}

	if params.Stt.SttNo == `` {
		newTime := c.timeRepo.Now(time.Now()).Add(time.Millisecond * time.Duration(0))
		/**
		 * generate stt by account type
		 */
		sttType := model.SttAutoTypeClient
		if !params.Stt.SttIsDO {
			sttType = model.SttAutoTypeBookingForClient
		}

		sttNo := shared.GenerateSttID(sttType, isMixpack, false, params.Stt.SttIsDO, &newTime)
		sttNo, err = c.checkSttNoDuplication(selfCtx, stt.CheckSttNoDuplicationRequest{
			SttNo: sttNo,
			GenerateSttNoFunc: func() string {
				now := c.timeRepo.Now(time.Time{}).Add(time.Millisecond * time.Duration(0))
				sttNoFunc := shared.GenerateSttID(sttType, isMixpack, false, params.Stt.SttIsDO, &now)
				return sttNoFunc
			},
		})
		if err != nil {
			return nil, err
		}
		params.Stt.SttNo = sttNo
	}

	// validate insurance type
	if params.Stt.SttGoodsEstimatePrice >= model.MIN_INSURANCE_BASIC {
		params.Stt.SttInsuranceType = model.INSURANCEBASIC
	}

	isDisablePromo := !c.checkCalculateTariffPromoElligible(bookedRole, model.CLIENT, params.Source)

	/**
	 * get check tariff
	 */
	reqCheckTariff := &stt.CalculateTariffParams{
		RequestCalculateTariff: &stt.RequestCalculateTariff{
			OriginID:       params.Stt.SttOriginDistrictID,
			DestinationID:  params.Stt.SttDestinationDistrictID,
			ProductType:    params.Stt.SttProductType,
			CommodityID:    commodity.Data.CommodityID,
			InsuranceType:  params.Stt.SttInsuranceType,
			GoodsPrice:     params.Stt.SttGoodsEstimatePrice,
			CodAmount:      params.Stt.SttCODAmount,
			IsCod:          params.Stt.SttIsCOD,
			IsDfod:         params.Stt.SttIsDFOD,
			IsWoodpacking:  params.Stt.SttIsWoodpacking,
			AccountType:    model.CLIENT,
			AccountRefID:   params.SttClient,
			IsHaveTaxID:    false,
			IsDisablePromo: isDisablePromo,
			ShipmentPrefix: shared.GetPrefixFromShipmentOrNoRefExt(params.Stt.SttShipmentID, params.Stt.SttNoRefExternal),
		},
		Token: params.Token,
	}

	if params.Stt.SttTaxNumber != `` {
		reqCheckTariff.RequestCalculateTariff.IsHaveTaxID = true
	}

	reqCheckTariff.RequestCalculateTariff.GeneratePiecesCalculateTariff(params.Stt.SttPieces)
	totalTarif := 0.0

	clientPaymentMethod := ""
	clientCodConfigAmount := ""
	clientCodShipmentDiscount := 0.0
	rateVatShipment := 0.0
	rateVatCod := 0.0

	if reqCheckTariff.RequestCalculateTariff.IsCod {
		rateVatShipment = c.cfg.RateVatShipment()
		rateVatCod = c.cfg.RateVatCod()
		clientCodConfigAmount = bookedClient.Data.ClientCodConfigAmount
		clientPaymentMethod = "invoice"

		if bookedClient.Data.ClientCodConfigAmount == model.GoodsPriceTotalTarif {

			clientParent, err := c.clientRepo.GetByID(selfCtx, bookedClient.Data.ClientParentID, params.Token)
			if err != nil || clientParent == nil {
				errors = err
				return nil, errClientNotFound
			}

			clientPaymentMethod = clientParent.Data.ClientPaymentMethod
			clientCodShipmentDiscount = bookedClient.Data.ClientCodShipmentDiscount
		}
	}

	if params.Stt.SttIsDFOD {
		clientPaymentMethod = model.ClientPaymentMethodSplitBill
		clientCodShipmentDiscount = bookedClient.Data.ClientCodShipmentDiscount
	}

	reqCheckTariff.RequestCalculateTariff.CodAmount += totalTarif

	checkTariff, err := c.checkTariffRepo.TariffCalculation(selfCtx, reqCheckTariff)
	if err != nil || checkTariff == nil {
		errors = err
		return nil, err
	}

	//get cod fee and cod amount from check tarif
	params.Stt.SttCODFee = checkTariff.Data.CodFee
	params.Stt.SttCODAmount = checkTariff.Data.CodAmount

	// validate total stt volume weight and total stt gross weight
	if err := stt.ValidateTotalWeight(checkTariff.Data.VolumeWeight, checkTariff.Data.GrossWeight, params.Stt.SttShipmentID, params.Stt.SttNoRefExternal); err != nil {
		return nil, err
	}
	signedSttWeightAttachFile, _ := c.generateWeightSignedProof(ctx, params)
	// build remarks piece history
	tempRemarksPieceHistory := model.RemarkPieceHistory{
		ActorExternalCode:   bookedClient.Data.ClientElexysCode,
		ActorExternalType:   model.CLIENT,
		HistoryLocationName: cityOrigin.Name,
		// booking fee need to be excluded cod_fee to calculate penalty adjustment
		BookingFee: func() float64 {
			if reqCheckTariff.RequestCalculateTariff.IsCod && checkTariff.Data.CodFee > 0 {
				return checkTariff.Data.TotalTariff - checkTariff.Data.CodFee
			}
			return checkTariff.Data.TotalTariff
		}(),
		BookingFeeAfterDiscount: func() float64 {
			if reqCheckTariff.RequestCalculateTariff.IsCod && checkTariff.Data.CodFeeAfterDiscount > 0 {
				return checkTariff.Data.TotalTariffAfterDiscount - checkTariff.Data.CodFeeAfterDiscount
			}
			return checkTariff.Data.TotalTariffAfterDiscount
		}(),
		ClientCodBookingDiscount: checkTariff.Data.CODBookingDiscount,
		ChargeableWeight:         checkTariff.Data.ChargeableWeight,
		HistoryDistrictCode:      districtOrigin.Data.Code,
		HistoryDistrictName:      districtOrigin.Data.Name,
		HistoryDataAdjustment: &model.HistoryDataAdjustment{
			IsPromo:                  checkTariff.Data.IsPromo,
			TotalTariff:              checkTariff.Data.TotalTariff,
			TotalTariffAfterDiscount: checkTariff.Data.TotalTariffAfterDiscount,
		},
		SttWeightAttachFiles:       params.SttWeightAttachFiles,
		SttWeightAttachFileSigneds: signedSttWeightAttachFile,
	}

	// set STT meta
	sttMeta := model.SttMeta{
		EstimateSLA:             c.generateEstimaeSlaByProductType(checkTariff.Data.EstimateSLA, params.Stt.SttProductType, now, cityOrigin.Timezone),
		OriginCityName:          cityOrigin.Name,
		OriginDistrictName:      districtOrigin.Data.Name,
		DestinationCityName:     cityDestination.Name,
		DestinationDistrictName: districtDestination.Data.Name,
		DetailCalculateRetailTariff: []model.DetailCalculateRetailTariff{
			{
				Status:       model.BKD,
				IsCalculated: false,
			},
		},
		PostalCodeDestination: params.Stt.PostalCodeDestination,

		ClientPaymentMethod:        clientPaymentMethod,
		ClientCodConfigAmount:      clientCodConfigAmount,
		ClientCodShipmentDiscount:  clientCodShipmentDiscount,
		RateVatShipment:            rateVatShipment,
		RateVatCod:                 rateVatCod,
		SttNoRefExternalAdditional: params.Stt.SttNoRefExternalAdditional,

		SttFtzIdentityNumber: params.Stt.SttFtzIdentityNumber,
		SttFtzRecipientEmail: params.Stt.SttFtzRecipientEmail,
		SttFtzCIPL:           params.Stt.SttFtzCIPL,
		PostalCodeOrigin:     params.Stt.PostalCodeOrigin,
		SttFtzAttachFiles:    params.Stt.SttFtzAttachFiles,
		SttFtzKtpImage:       params.Stt.SttFtzKtpImage,
		SttFtzTaxImage:       params.Stt.SttFtzTaxImage,
	}
	/**
	 * isKurirRekomendasi = if the SttNoRefExternal has prefix TKP01-xxxxx then true
	 * Add some parameter on stt meta if isKurirRekomendasi == true
	 */
	if isKurirRekomendasi {
		sttMeta.TicketCode = params.TicketCode
		sttMeta.OtherShipperTicketCode = params.OtherShipperTicketCode
	}

	/**
	* if stt is cross docking
	 */
	if params.IsSttCrossDocking {
		sttMeta.IsSttCrossdocking = true
	}

	// if stt is interpack
	sttMeta = c.generateSttMetaProductTypeInterpack(params, sttMeta)

	/**
	 * Add goods names
	 */
	if len(params.GoodsNames) > 0 {
		sttMeta.GoodsNames = params.GoodsNames
	}

	/**
	 * General stt field for booking
	 */
	sttCreate := stt.GenerateSttCreate(&stt.SttCreateParams{
		Stt:                   &params.Stt,
		CheckTariff:           &checkTariff.Data,
		Now:                   &now,
		BookedBy:              bookedBy,
		BookedName:            bookedName,
		BookedRole:            bookedRole,
		BookedCode:            bookedCode,
		IsSttManual:           IsSttManual,
		AccountID:             int(params.AccountID),
		AccountName:           params.AccountName,
		Remarks:               tempRemarksPieceHistory.EncodeToString(),
		DestinationDistrict:   districtDestination,
		OriginDistrict:        districtOrigin,
		Commodity:             commodity,
		BookedForExternalCode: bookedClient.Data.ClientElexysCode,
		BookedByExternalCode:  actorExternalCode,
		ElexysTariff:          &params.ElexysTariff,
		BookedForActor: &model.Actor{
			ID:   bookedClient.Data.ClientID,
			Name: bookedClient.Data.ClientCompanyName,
			Code: bookedClient.Data.ClientCode,
			Type: model.CLIENT,
		},
		SttManual: sttManual,
	})

	// Allowed Cod
	if err := c.checkIsAllowCOD(selfCtx, &stt.CheckIsAllowCOD{
		DestinationDistrict: districtDestination,
		SttCreate:           sttCreate,
		BookedForActor: &model.Actor{
			ID:    bookedClient.Data.ClientID,
			IsCod: bookedClient.Data.ClientIsCOD,
			Name:  bookedClient.Data.ClientCompanyName,
			Code:  bookedClient.Data.ClientCode,
			Type:  model.CLIENT,
		},
	}); err != nil {
		return nil, err
	}

	createSttCustomeFlag := []model.SttCustomFlag{}

	/**
	 * Additional stt field booked for client
	 */
	c.createAdditionalSttField(sttCreate, bookedClient)
	sttCreate.Stt.SttMeta = sttMeta.ToString()
	sttCreate.Stt.SttUpdatedActorID = dbr.NewNullInt64(bookedBy)
	sttCreate.Stt.SttUpdatedActorRole = dbr.NewNullString(bookedRole)
	sttCreate.Stt.SttUpdatedActorName = dbr.NewNullString(bookedName)
	sttCreate.Stt.SttCommodityID = commodity.Data.CommodityID
	createSttCustomeFlag = append(createSttCustomeFlag, c.getSttCustomFlagNewForm(sttCreate.Stt.SttNo, params.Stt.IsNewForm, now))
	sttCreate.CreateSttCustomFlag = createSttCustomeFlag

	sttDue, err := c.GenerateSttDue(selfCtx, &sttCreate.Stt, params.Token)
	if err != nil {
		return nil, err
	}

	sttCreate.SttDue = sttDue

	generatedStt, err := c.createSttClient(selfCtx, sttCreate)
	if err != nil {
		errors = err
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to create STT Booking, Please resubmit again",
			"id": "Gagal membuat STT Booking, Tolong dikirim ulang",
		})
	}

	if errCreatePickupManifest := c.createpickupManifest(selfCtx, params, generatedStt, sttCreate); errCreatePickupManifest != nil {
		errors = errCreatePickupManifest
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to create Pickup Manifest Cbp, Please resubmit again",
			"id": "Gagal membuat Pickup Manifest Cbp, Tolong dikirim ulang",
		})
	}

	var wg sync.WaitGroup
	/* publish message to calculate retail tariff */
	if sttCreate.Stt.SttBookedByType == model.POS && sttCreate.Stt.SttBookedForType == model.CLIENT {
		wg.Add(1)

		payloadCalculateRetailTariff := setPayloadCalculateRetailTariff(reqCheckTariff, sttCreate, isDisablePromo)

		go lputils.TrackGoroutine(func(goCtx context.Context) {
			defer wg.Done()
			c.CalculateRetailTariff(goCtx, &payloadCalculateRetailTariff)
		})
	}

	go func() {
		// count goroutine
		uuid := uuid.New().String()
		shared.IncreaseTotalGoRoutineCount(uuid)
		defer shared.DecreaseTotalGoRoutineCount(uuid)

		isHoldCommision := false
		if sttCreate.Stt.SttBookedByType == model.POS {
			isHoldCommision = true
		}

		c.gatewaySttUc.BookingCommission(context.Background(), &gateway_stt.BookingCommissionRequest{
			SttCreate: func(params model.SttCreate) *model.SttCreate {
				createStt := params

				// total_tariff need to be deducted by cod_fee when check_tariff with is_cod true
				// when check_tariff with is_cod true, total_tariff will be added with cod_fee
				isSttCodAndSttShipmentIDEmpty := createStt.Stt.SttIsCOD && createStt.Stt.SttShipmentID == ``
				isSttCodFeeHigherThanZero := createStt.Stt.SttCODFee > 0
				if isSttCodAndSttShipmentIDEmpty && isSttCodFeeHigherThanZero {
					createStt.CheckTariff.TotalTariff = createStt.CheckTariff.TotalTariff - createStt.Stt.SttCODFee
				}

				createStt.CheckTariff.TotalTariff = createStt.CheckTariff.TotalTariff - createStt.CheckTariff.CODBookingDiscount

				return &createStt
			}(*sttCreate),
			SttCreateRequest: &params.Stt,
			Token:            params.Token,
			AccountRefType:   bookedRole,
			AccountRefID:     bookedBy,

			PartnerPosParentID:         bookedByActorDetail.PosParentID(),
			PartnerPosBranchCommission: bookedByActorDetail.PosBranchCommission(),
			ClientPaymentMethod:        clientPaymentMethod, ClientCodConfigAmount: clientCodConfigAmount, ClientCodShipmentDiscount: clientCodShipmentDiscount, IsHoldCommision: isHoldCommision,
		})
	}()

	go c.gatewaySttUc.SttSubmit(context.Background(), &gateway_stt.SttSubmitRequest{SttCreate: sttCreate, Token: params.Token})

	go func() {

		ctxBaground := context.Background()
		c.messageGatewayUc.SendMessage(ctxBaground, &model.SendMessageRequest{
			RecieverNumber: model.RecieverNumber{
				PackageSender:   sttCreate.Stt.SttSenderPhone,
				PackageReceiver: sttCreate.Stt.SttRecipientPhone,
			},
			PackageType:       shared.GetPackageType(sttCreate.Stt.SttIsCOD, sttCreate.Stt.SttIsDFOD),
			EventStatus:       model.BKD,
			DriverName:        sttCreate.Stt.SttSenderName,
			DriverPhoneNumber: sttCreate.Stt.SttSenderPhone,
			Token:             params.Token,
		}, &sttCreate.Stt)
	}()

	// publish stt assessment
	go func() {
		// count goroutine
		uuid := uuid.New().String()
		shared.IncreaseTotalGoRoutineCount(uuid)
		defer shared.DecreaseTotalGoRoutineCount(uuid)

		publishAssessmentParams, isPublishAssessment := c.checkSttForClientNeedAssessment(params, sttCreate, bookedClient)

		c.publishAssessment(publishAssessmentData{
			sttID: generatedStt[0].SttID,
			sttNo: generatedStt[0].SttNo,
			clientAssessment: stt.ClientAssessment{
				ClientID:               params.SttClient,
				ClientIsNeedAssessment: bookedClient.Data.ClientParentIsNeedAssessment,
			},
			isNeedAssessment: isPublishAssessment,
		}, publishAssessmentParams)

	}()

	wg.Wait()
	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.saveCustomerSttCorporate(ctx, params, sttCreate.Stt, dataDistrictOriginDestination{districtOrigin: districtOrigin, districtDestination: districtDestination})
	})

	res.Stt = generatedStt
	createSttCorporateSetResponse(generatedStt, &res, paramSetResponseSttCorporate{paramCreateStt: params.Stt, commodityGroupCode: commodityGroupCode, listDiscount: checkTariff.Data.ListDiscount, sttDestinationDistrictName: sttCreate.Stt.SttDestinationDistrictName, chargeableWeight: sttCreate.Stt.SttChargeableWeight})
	//Publish
	c.publishBulkSTTRepair(ctx, paramPubBulkSTTRepair{bulkID: params.BulkID, bulkSTTRowNumber: params.BulkSTTRowNumber, sttModel: sttCreate.Stt, generatedStt: generatedStt, isSttManual: IsSttManual})
	c.createSttSetResponse(selfCtx, &res, params.Token, sttCreate.Stt)
	return &res, nil
}

func setPayloadCalculateRetailTariff(reqCheckTariff *stt.CalculateTariffParams, sttCreate *model.SttCreate, isDisablePromo bool) stt.CalculateRetailTariffRequest {
	return stt.CalculateRetailTariffRequest{
		OriginID:          reqCheckTariff.RequestCalculateTariff.OriginID,
		DestinationID:     reqCheckTariff.RequestCalculateTariff.DestinationID,
		CommodityID:       reqCheckTariff.RequestCalculateTariff.CommodityID,
		ProductType:       reqCheckTariff.RequestCalculateTariff.ProductType,
		AccountType:       sttCreate.Stt.SttBookedByType,
		AccountRefID:      sttCreate.Stt.SttBookedBy,
		GoodsPrice:        reqCheckTariff.RequestCalculateTariff.GoodsPrice,
		CodAmount:         reqCheckTariff.RequestCalculateTariff.CodAmount,
		IsCod:             false,
		InsuranceType:     reqCheckTariff.RequestCalculateTariff.InsuranceType,
		IsWoodpacking:     reqCheckTariff.RequestCalculateTariff.IsWoodpacking,
		IsHaveTaxID:       reqCheckTariff.RequestCalculateTariff.IsHaveTaxID,
		Pieces:            reqCheckTariff.RequestCalculateTariff.Pieces,
		IsBookingPurposes: reqCheckTariff.RequestCalculateTariff.IsBookingPurposes,
		HistoryStatus:     []string{model.BKD},
		SttNo:             sttCreate.Stt.SttNo,
		IsDisablePromo:    isDisablePromo,
	}
}

func (c *sttCtx) validateAccountForCreateSttClient(ctx context.Context, token string) error {
	validate, err := c.accountRepo.ValidateAccount(ctx, token, true)
	if err != nil {
		return err
	}

	if !validate.IsAllow {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"error_type": validate.ErrorType,
			"en":         "Account is not eligible to create STT",
			"id":         "Akun tidak diperbolehkan untuk membuat STT",
		})
	}
	return nil
}

func (c *sttCtx) createAdditionalSttField(sttCreate *model.SttCreate, bookedClient *model.Client) {
	sttCreate.Stt.SttSource = model.MANUAL
	sttCreate.Stt.SttShipmentID = ""
	sttCreate.Stt.SttBilledTo = bookedClient.Data.ClientCompanyName
	sttCreate.Stt.SttClientID = bookedClient.Data.ClientID
	sttCreate.Stt.SttIsDO = bookedClient.Data.ClientIsDO
	sttCreate.Stt.SttPosID = 0
	sttCreate.Stt.SttClientID = bookedClient.Data.ClientID
}

func (c *sttCtx) generateWeightSignedProof(ctx context.Context, params *stt.CreateSttManualForClient) ([]string, error) {
	sttDetailSigned := model.Stt{
		SttShipmentID:    params.Stt.SttShipmentID,
		SttNoRefExternal: params.Stt.SttNoRefExternal,
	}
	return c.GenerateSignedURLs(ctx, &sttDetailSigned, strings.Join(params.SttWeightAttachFiles, ","))
}

func (c *sttCtx) createSttClient(ctx context.Context, sttCreate *model.SttCreate) ([]stt.CreateSttResult, error) {
	generatedStt, err := c.sttRepo.Create(ctx, &model.SttCreateParams{
		SttCreate: []model.SttCreate{
			*sttCreate,
		},
		IsElexys: c.cfg.IsElexysConfig(),
	})

	if err != nil {
		return nil, err
	}

	return generatedStt, nil
}
