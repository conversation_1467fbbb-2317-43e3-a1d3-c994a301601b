package sti_temporary

import (
	"time"

	"github.com/Lionparcel/hydra/src/model"
)

type StiTemporaryListV2Request struct {
	SttOrBag     string `json:"stt_or_bag_no" query:"stt_or_bag_no" params:"stt_or_bag_no"`
	IsStiDest    *bool  `json:"is_sti_dest" query:"is_sti_dest" params:"is_sti_dest"`
	StatusReturn string `json:"status_return" query:"status_return" params:"status_return"`
	NoRef        string `json:"no_ref" query:"no_ref" params:"no_ref"`
	HubId        int    `json:"hub_id" form:"hub_id" query:"hub_id"`
	BookedType   string `json:"booked_type" form:"booked_type" query:"booked_type"`
	BookedID     int    `json:"booked_id" form:"booked_id" query:"booked_id"`

	IsActive  *bool
	SttNo     string
	BagNo     string
	AccountID int64
}

type StiTemporaryListV2Response struct {
	StID             int                    `json:"st_id"`
	StAccountID      int                    `json:"st_account_id"`
	StIsActive       int                    `json:"st_is_active"`
	StSttNo          string                 `json:"st_stt_no"`
	StProduct        string                 `json:"st_product"`
	StOrigin         string                 `json:"st_origin"`
	StDestination    string                 `json:"st_destination"`
	StRegionID       string                 `json:"st_region_id"`
	StDeadlineReturn time.Time              `json:"st_deadline_return"`
	StBookedType     string                 `json:"st_booked_type"`
	StBookedID       int                    `json:"st_booked_id"`
	StBookedName     string                 `json:"st_booked_name"`
	StMeta           model.StiTemporaryMeta `json:"st_meta"`
	StCreatedAt      time.Time              `json:"st_created_at"`
	StUpdatedAt      *time.Time             `json:"st_updated_at"`
}
