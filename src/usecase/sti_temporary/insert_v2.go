package sti_temporary

import (
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/sti"
	"time"
)

type (
	// StiTemporaryCreateParams ...
	StiTemporaryCreateParams struct {
		STIData          sti.CreateStiRequest   `json:"sti_data"`
		STTModel         model.Stt              `json:"stt_model"`
		DeadLineReturn   time.Time              `json:"dead_line_return"`
		StiTemporaryMeta model.StiTemporaryMeta `json:"sti_temporary_meta"`
	}
)
