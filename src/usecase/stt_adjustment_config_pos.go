package usecase

import (
	"context"

	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *sttCtx) configSttPOS(ctx context.Context, data checkSttPrefix) *stt.SttAdjustmentConfigResponse {
	if !c.isSttValidType(data) {
		return &stt.SttAdjustmentConfigResponse{}
	}

	currentStatus := data.dataStt.SttLastStatusID
	isAfterPup := c.isAfterStatusPup(data.sttHistories, currentStatus)
	rules := c.getSttFieldRulesPos(data)

	res := &stt.SttAdjustmentConfigResponse{
		Config: c.buildSttConfigPos(rules, isAfterPup),
	}

	c.checkInterpackDocumentInternational(ctx, data, res)
	c.populateRespInterpackAndFtzPos(res, data.cityOrigin.FreeTradeZone)
	return res
}

func (c *sttCtx) isAfterStatusPup(histories []model.SttPieceHistoryCustom, currentStatusID string) bool {
	historyStatus := ""
	switch currentStatusID {
	case model.BKD:
		return false
	case model.STTADJUSTED:
		for _, h := range histories {
			if h.HistoryStatus != model.STTADJUSTED {
				historyStatus = h.HistoryStatus
				break
			}
		}
		if historyStatus == model.BKD {
			return false
		}
		return true
	default:
		return true
	}
}

func newFieldRule(required bool, rule string) stt.FieldRule {
	return stt.FieldRule{
		IsRequired: required,
		Rule:       rule,
	}
}

func (c *sttCtx) adjustEditableRule(isAfterPup bool, defaultRule string) string {
	if isAfterPup {
		return stt.NonEditableConfig
	}
	return defaultRule
}

func (c *sttCtx) adjustShowRule(isAfterPup bool, defaultRule string) string {
	if isAfterPup {
		return stt.NoShowConfig
	}
	return defaultRule
}

func (c *sttCtx) getSttFieldRulesPos(data checkSttPrefix) sttFieldRules {
	rulesCod := stt.EditableConfig
	if data.dataStt.SttIsCOD || data.dataStt.SttIsDFOD {
		rulesCod = stt.NonEditableConfig
	}

	defaultRule := sttFieldRules{
		ClientRule:   stt.NoShowConfig,
		TariffRule:   stt.ShowConfig,
		EditableRule: stt.EditableConfig,
		WeightRule:   stt.EditableConfig,
		CodDfodRule:  rulesCod,
	}

	switch {
	case data.isSttRetail:
		return defaultRule
	case data.isSttClient:
		return sttFieldRules{ClientRule: stt.NonEditableConfig, TariffRule: stt.NoShowConfig, EditableRule: stt.EditableConfig, WeightRule: stt.EditableConfig, CodDfodRule: rulesCod}
	case data.isSttCA:
		return sttFieldRules{ClientRule: stt.NonEditableConfig, TariffRule: stt.ShowConfig, EditableRule: stt.EditableConfig, WeightRule: stt.EditableConfig, CodDfodRule: rulesCod}
	case data.isSttTokopedia:
		return sttFieldRules{ClientRule: stt.NonEditableConfig, TariffRule: stt.NoShowConfig, EditableRule: stt.NonEditableConfig, WeightRule: stt.EditableConfig, CodDfodRule: stt.NonEditableConfig}
	case data.isSttBukalapak:
		return sttFieldRules{ClientRule: stt.NonEditableConfig, TariffRule: stt.NoShowConfig, EditableRule: stt.NonEditableConfig, WeightRule: stt.NonEditableConfig, CodDfodRule: stt.NonEditableConfig}
	default:
		return defaultRule
	}
}

func (c *sttCtx) buildSttConfigPos(r sttFieldRules, isAfterPup bool) stt.Config {
	return stt.Config{
		Client:              newFieldRule(true, r.ClientRule),
		PaymentMethod:       newFieldRule(true, stt.NonEditableConfig),
		ShipmentID:          newFieldRule(true, stt.NonEditableConfig),
		ManualSttNo:         newFieldRule(false, stt.NonEditableConfig),
		SttNoRefExternal:    newFieldRule(false, stt.NonEditableConfig),
		SenderName:          newFieldRule(true, c.adjustEditableRule(isAfterPup, r.EditableRule)),
		SenderPhone:         newFieldRule(true, c.adjustEditableRule(isAfterPup, r.EditableRule)),
		SenderAddress:       newFieldRule(true, c.adjustEditableRule(isAfterPup, r.EditableRule)),
		SenderDistrict:      newFieldRule(true, stt.NonEditableConfig),
		SenderPostalCode:    newFieldRule(false, stt.NonEditableConfig),
		SenderSave:          newFieldRule(false, stt.NonEditableConfig),
		RecipientName:       newFieldRule(true, c.adjustEditableRule(isAfterPup, r.EditableRule)),
		RecipientPhone:      newFieldRule(true, c.adjustEditableRule(isAfterPup, r.EditableRule)),
		RecipientAddress:    newFieldRule(true, c.adjustEditableRule(isAfterPup, r.EditableRule)),
		RecipientDistrict:   newFieldRule(true, c.adjustEditableRule(isAfterPup, r.CodDfodRule)),
		RecipientPostalCode: newFieldRule(false, c.adjustEditableRule(isAfterPup, r.CodDfodRule)),
		RecipientSave:       newFieldRule(false, stt.NonEditableConfig),
		Commodity:           newFieldRule(true, stt.NonEditableConfig),
		ProductType:         newFieldRule(true, stt.NonEditableConfig),
		GoodsPrice:          newFieldRule(true, stt.NonEditableConfig),
		Insurance:           newFieldRule(false, stt.NonEditableConfig),
		CODAmount:           newFieldRule(false, stt.NonEditableConfig),
		GrossWeight:         newFieldRule(true, c.adjustEditableRule(isAfterPup, r.WeightRule)),
		Dimension:           newFieldRule(true, c.adjustEditableRule(isAfterPup, r.WeightRule)),
		ChargeableWeight:    newFieldRule(false, stt.NonEditableConfig),
		AdjustPiece:         newFieldRule(false, stt.NoShowConfig),
		ServiceType:         newFieldRule(true, stt.NonEditableConfig),
		Tariff:              newFieldRule(true, c.adjustShowRule(isAfterPup, r.TariffRule)),
	}
}
