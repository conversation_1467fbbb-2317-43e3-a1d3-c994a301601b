package usecase

import (
	"context"
	"time"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/repository"
	"github.com/Lionparcel/hydra/src/usecase/cbp_pickup"
)

type PickupManifestCbp interface {
	ViewCbpPickup(ctx context.Context, params *cbp_pickup.PickupManifestCBPParams) (*shared.Pagination, error)
	CbpPickupManifest(ctx context.Context, params *cbp_pickup.PickupManifestCBPParams) ([]model.CBPPickupManifestResponse, *model.CBPSummary, error)
}

type pickupManifestCbpCtx struct {
	cfg                   *config.Config
	pickupManifestCbpRepo repository.PickupManifestCbpRepository
	clientRepo            repository.ClientRepository
	partnerRepo           repository.PartnerRepository
}

func NewPickupManifestCbpUc(
	cfg *config.Config,
	pickupManifestCbpRepo repository.PickupManifestCbpRepository,
	clientRepo repository.ClientRepository,
	partnerRepo repository.PartnerRepository,
) PickupManifestCbp {
	return &pickupManifestCbpCtx{
		cfg:                   cfg,
		pickupManifestCbpRepo: pickupManifestCbpRepo,
		clientRepo:            clientRepo,
		partnerRepo:           partnerRepo,
	}
}

func (c *pickupManifestCbpCtx) ViewCbpPickup(ctx context.Context, params *cbp_pickup.PickupManifestCBPParams) (*shared.Pagination, error) {
	var (
		opName  = "pickupManifestCbpCtx-ViewCbpPickup"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		res     = new(shared.Pagination)
		resData = []cbp_pickup.ResponsePickupManifestCBP{}
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": resData, "error": err})
	}()

	if err = params.ValidateV2(); err != nil {
		return nil, err
	}

	params.Field = "*"
	params.IsLimit = true
	cbpPickup, errDb := c.pickupManifestCbpRepo.Select(selfCtx, params)
	if errDb != nil {
		err = errDb
		return nil, shared.ERR_UNEXPECTED_DB
	}

	resData = cbp_pickup.MappingDataResponsePickupManifestCBP(cbpPickup)
	getTotalRecords := false
	totalData := 0
	if params.IsTotalData {
		totalData = len(resData)
		getTotalRecords = totalData > 0 && totalData == params.Limit
	}
	if getTotalRecords {
		params.Field = "stt.id"
		params.DbConnection = model.SLAVE_REPORT
		params.IsLimit = false
		allData, errDb := c.pickupManifestCbpRepo.Select(selfCtx, params)
		totalData = len(allData)
		if errDb != nil {
			err = errDb
			totalData = 100
		}

	}

	res = &shared.Pagination{
		Data: resData,
		Meta: shared.Meta{
			Page:         params.Page,
			Limit:        params.Limit,
			TotalRecords: totalData,
		},
	}

	return res, nil
}

func (c *pickupManifestCbpCtx) CbpPickupManifest(ctx context.Context, params *cbp_pickup.PickupManifestCBPParams) ([]model.CBPPickupManifestResponse, *model.CBPSummary, error) {
	opName := "cbpPickupCtx-CbpPickupManifest"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	resData := []model.CBPPickupManifestResponse{}
	Summary := &model.CBPSummary{}
	listUpdateToPrinted := []int{}
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": resData, "error": err})
	}()

	if err = params.Validate(); err != nil {
		return nil, nil, err
	}

	params.Field = "*"
	params.IsLimit = false
	cbpPickup, err := c.pickupManifestCbpRepo.Select(selfCtx, params)
	if err != nil {
		return nil, nil, err
	}

	totalGrossWeight := 0.0
	totalPieces := 0
	totalSTT := 0

	if params.PmcClientId != 0 {
		client, err := c.clientRepo.GetByID(selfCtx, params.PmcClientId, params.Token)
		if err != nil {
			return nil, nil, err
		}

		if client == nil {
			err = shared.NewMultiStringBadRequestError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Client not found",
				"id": "Client tidak ditemukan",
			})
			return nil, nil, err
		}

		Summary.ClientCode = client.Data.ClientCode
		Summary.ClientId = client.Data.ClientID
		Summary.ClientName = client.Data.ClientCompanyName
		Summary.ClientOrigin = client.Data.ClientCityCode + " - " + client.Data.ClientCityName
	}

	pos, err := c.partnerRepo.GetByID(selfCtx, int(params.PartnerID), params.Token)
	if err != nil {
		return nil, nil, err
	}

	if pos == nil {
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Partner not found",
			"id": "Partner tidak ditemukan",
		})
		return nil, nil, err
	}
	Summary.PosName = pos.Data.Name
	Summary.PosOrigin = pos.Data.PartnerLocation.CityCode + " - " + pos.Data.PartnerLocation.City.Name

	for i := 0; i < len(cbpPickup); i++ {
		resData = append(resData, model.CBPPickupManifestResponse{
			SttID:                      int(cbpPickup[i].Stt.SttID),
			SttNo:                      cbpPickup[i].Stt.SttNo,
			SttShipmentID:              cbpPickup[i].Stt.SttShipmentID,
			SttRecipientAddress:        cbpPickup[i].Stt.SttRecipientAddress,
			SttCommodityCode:           cbpPickup[i].Stt.SttCommodityCode,
			SttCommodityName:           cbpPickup[i].Stt.SttCommodityName,
			SttClientID:                cbpPickup[i].Stt.SttClientID,
			SttPosID:                   cbpPickup[i].Stt.SttPosID,
			SttProductType:             cbpPickup[i].Stt.SttProductType,
			SttChargeableWeight:        cbpPickup[i].Stt.SttChargeableWeight,
			SttTotalPiece:              cbpPickup[i].Stt.SttTotalPiece,
			SttLastStatusID:            cbpPickup[i].Stt.SttLastStatusID,
			SttCounter:                 cbpPickup[i].Stt.SttCounter,
			SttInsuranceType:           cbpPickup[i].Stt.SttInsuranceType,
			SttOriginCity:              cbpPickup[i].Stt.SttOriginCityID,
			SttDestinationCity:         cbpPickup[i].Stt.SttDestinationCityID,
			SttTaxNumber:               cbpPickup[i].Stt.SttTaxNumber,
			SttBookedName:              cbpPickup[i].Stt.SttBookedName,
			SttBilledTo:                cbpPickup[i].Stt.SttBookedForCode + " - " + cbpPickup[i].Stt.SttBookedForName,
			SttCreatedName:             cbpPickup[i].Stt.SttCreatedName,
			SttCreatedAt:               cbpPickup[i].Stt.SttCreatedAt,
			SttUpdatedAt:               cbpPickup[i].Stt.SttUpdatedAt,
			SttPaymentStatus:           cbpPickup[i].Stt.SttPaymentStatus,
			SttPaymentDateAt:           &cbpPickup[i].Stt.SttPaymentDateAt.Time,
			SttUpdatedByName:           cbpPickup[i].Stt.SttUpdatedName,
			SttDestinationCityName:     cbpPickup[i].Stt.SttDestinationCityName,
			SttOriginCityName:          cbpPickup[i].Stt.SttOriginCityName,
			SttDestinationDistrictName: cbpPickup[i].Stt.SttDestinationDistrictName,
			SttOriginDistrictName:      cbpPickup[i].Stt.SttOriginDistrictName,
			SttGrossWeight:             cbpPickup[i].Stt.SttGrossWeight,
		})

		totalGrossWeight = totalGrossWeight + cbpPickup[i].Stt.SttGrossWeight
		totalPieces = totalPieces + cbpPickup[i].Stt.SttTotalPiece
		totalSTT = totalSTT + 1

		listUpdateToPrinted = append(listUpdateToPrinted, cbpPickup[i].PickupManifestCBP.PmcId)
	}

	err = c.pickupManifestCbpRepo.UpdateStatus(selfCtx, listUpdateToPrinted, model.PMCSTATUSPRINTED)
	if err != nil {
		return nil, nil, err
	}

	Summary.PickupDate = time.Now()
	Summary.TotalSTT = totalSTT
	Summary.TotalPieces = totalPieces
	Summary.TotalGrossWeight = totalGrossWeight

	return resData, Summary, nil
}
