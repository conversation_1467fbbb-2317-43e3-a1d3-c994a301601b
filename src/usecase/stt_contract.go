package usecase

import (
	"context"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/config/pubsub"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
	ucRiskClassification "github.com/Lionparcel/hydra/src/modules/cod_dfod_fraud_detection/risk_classification/usecase"
	"github.com/Lionparcel/hydra/src/repository"
	"github.com/Lionparcel/hydra/src/usecase/stt"

	"github.com/labstack/echo"
)

// Stt ...
type Stt interface {
	CreateSTT(ctx context.Context, params *stt.CreateSttRequest) (*stt.CreateSttResponse, error)
	CreateSTTV2(ctx context.Context, params *stt.CreateSttRequest) (*stt.CreateSttResponse, error)
	ViewStt(ctx context.Context, req stt.ViewSttRequest) (*shared.Pagination, error)
	DetailStt(ctx context.Context, params *stt.SttDetailRequest) (*stt.SttDetailResponse, error)
	DetailSttTracking(ctx context.Context, params *stt.SttDetailTrackingRequest) (*stt.SttDetailTrackingResponse, error)
	DetailSttTrackingV2(ctx context.Context, params *stt.SttDetailTrackingRequest) (res *stt.SttDetailTrackingResponse, err error)
	DetailResi(ctx context.Context, req stt.DetailResiRequest) (*stt.DetailResiResponse, error)
	ResiSendEmail(ctx context.Context, req *stt.ResiSendEmailRequest) error
	MockeryShipmentGenerate(ctx context.Context, prefix string) (string, error)
	DetailShipmentAlgoByID(ctx context.Context, params *stt.ViewDetailShipmentAlgoRequest) (*shared.Pagination, error)
	ViewSttManifest(ctx context.Context, req stt.ViewSttManifestRequest) (*shared.Pagination, error)
	ExportSttManifest(ctx context.Context, params *stt.ViewSttManifestRequest, e echo.Context) error
	CancelStt(ctx context.Context, params *stt.RequestCancelStt) (*stt.ResponseCancelStt, error)
	UpdateSTT(ctx context.Context, params *stt.UpdateSttRequest) (*stt.UpdateSttResponse, error)
	CreateSTTForClient(ctx context.Context, params *stt.CreateSttManualForClient) (*stt.CreateSttResponse, error)
	CreateSTTManualForTokopedia(ctx context.Context, params *stt.PublishSttBookingTokopedia) (*stt.CreateSttResponse, error)
	ValidatePhoneNumber(ctx context.Context, params *stt.ValidatePhoneNumberRequest) (*stt.ValidatePhoneNumberResponse, error)
	ViewBulkStt(ctx context.Context, req stt.ViewBulkSttRequest) ([]stt.ViewBulkSttResponse, error)
	ViewSttShipmentPrefix(ctx context.Context) []stt.ViewSttShipmentPrefixResponse
	ViewSttPlain(ctx context.Context, req *stt.ViewSttPlainRequest) (*shared.Pagination, error)
	ViewSttForManifest(ctx context.Context, params *stt.ViewSttForManifestRequest) (*shared.Pagination, error)
	GenerateRequestViewStt(ctx context.Context, params *stt.ViewSttRequest) (*model.SttViewParams, error)
	CreateSTTReverseJourney(ctx context.Context, params *stt.CreateSttManualReverseJourney) (*stt.CreateSttReverseJourneyResponse, error)
	ViewSttManifestBag(ctx context.Context, req stt.ViewSttManifestBagRequest) (*shared.Pagination, error)

	ViewSttSenderTracking(ctx context.Context, params *stt.ViewSttTrackingRequest) (*shared.Pagination, error)

	ViewSttTrackingList(ctx context.Context, params *stt.ViewSttTrackingListRequest) (*shared.Pagination, error)

	// MigrateAdditionalData
	MigrateAdditionalData(ctx context.Context) error

	// DTPOL
	ViewDTPOLStartEndPoints(ctx context.Context, params *stt.ViewDTPOLStartEndPointsRequest) (*stt.ViewDTPOLStartEndPointsResponse, error)
	ViewDTPOLStartEndPointsV2(ctx context.Context, params *stt.ViewDTPOLStartEndPointsRequest) (*stt.ViewDTPOLStartEndPointsResponse, error)
	ViewCargoPlaneStatusARR(ctx context.Context, params *stt.ViewCargoPlaneStatusARRRequest) (*stt.ViewCargoPlaneStatusARRResponse, error)
	ViewDTPOLStartEndPointsV3(ctx context.Context, params *stt.ViewDTPOLStartEndPointsRequest) (*stt.ViewDTPOLStartEndPointsResponse, error)

	// Async update retail tariff
	PubsubTariffRetailSTT(ctx context.Context, params *stt.RequestPubSubUpdateCalculateTariff) error
	ManualUpdateTariffRetailSTT(ctx context.Context, params *stt.RequestManualUpdateCalculateTariff) error
	UpdateTariffRetailSTT(ctx context.Context, params *stt.RequestUpdateCalculateTariff) error

	//STT Claim
	PubsubSTTClaim(ctx context.Context, params *stt.RequestPubSubUpdateSttClaim) error

	// ValidationShipmentLilo
	ValidateStatusIncomingLILO(ctx context.Context, params *stt.ValidateShipmentLiloRequest) (*stt.ValidateShipmentLiloResponse, error)

	CreateSTTManualForTokopediaFailed(ctx context.Context, params *stt.PublishSttBookingTokopediaFailed) error

	CreateSTTBookingNonCrossDocking(ctx context.Context, params *stt.PublishSttBookingNonCrossDocking) (*stt.CreateSttResponse, error)
	RenameNoRef(ctx context.Context, params *stt.RenameNoRefRequest) (*stt.RenameNoRefResponse, error)
	GetOldestSTT(ctx context.Context, params *stt.GetOldestSttRequest) (*model.Stt, error)
	ReverseDestination(ctx context.Context, params *stt.ReverseDestinationParams) error
	GetDfodPasti(ctx context.Context, params *stt.SttDfodPastiRequest) (*stt.SttDfodPastiResponse, error)
	Validate(ctx context.Context, params *stt.ValidateRequest) (*stt.ValidateResponse, error)
	CorporateCrossDockingRetry(ctx context.Context, params *stt.CorporateCrossDockingRetryRequest) (*stt.CorporateCrossDockingRetryResponse, error)
	AssessmentRelabel(ctx context.Context, params *stt.AssessmentRelabelRequest) (*stt.AssessmentRelabelResponse, error)
	GetSttRelabel(ctx context.Context, params *stt.GetSttRelabelRequest) (*stt.GetSttRelabelResponse, error)
	GenerateSignedURLs(ctx context.Context, sttDetail *model.Stt, attachment string) ([]string, error)
	GetDetailSttShipment(ctx context.Context, param *stt.GetDetailSttShipmentRequest) (*stt.GetDetailSttShipmentResponse, error)
	SttAdjustmentConfig(ctx context.Context, params *stt.SttAdjustmentConfigRequest) (*stt.SttAdjustmentConfigResponse, error)
	DetailSttTrackingTokopedia(ctx context.Context, params *stt.DetailSttTrackingTokopediaRequest) (res *stt.SttDetailTrackingResponse, err error)
}

// sttCtx ..
type sttCtx struct {
	cfg                         *config.Config
	sttRepo                     repository.SttRepository
	sttManualRepo               repository.SttManualRepository
	clientRepo                  repository.ClientRepository
	partnerRepo                 repository.PartnerRepository
	commodityRepo               repository.CommodityRepository
	sttOptionalRateRepo         repository.SttOptionalRateRepository
	districtRepo                repository.DistrictRepository
	checkTariffRepo             repository.CheckTariffRepository
	accountRepo                 repository.AccountRepository
	productRepo                 repository.ProductTypeRepository
	sttPiecesRepo               repository.SttPiecesRepository
	cityRepo                    repository.CityRepository
	estimateSlaRepo             repository.EstimateSlaRepository
	shipmentPacketRepo          repository.ShipmentPackageRepository
	shipmentRepo                repository.ShipmentRepository
	sttPieceHistoryRepo         repository.SttPieceHistoryRepository
	routeRepo                   repository.RouteRepository
	configurablePriceRepo       repository.ConfigurablePriceRepository
	reasonRepo                  repository.ReasonRepository
	gatewaySttUc                GatewayStt
	gatewaySttStatus            GatewaySttStatus
	walletRepo                  repository.WalletRepository
	balanceLimitRepo            repository.BalanceLimitRepository
	messageGatewayUc            MessageGateway
	sttElexysRepo               repository.SttElexysRepository
	repoAccount                 repository.AccountRepository
	cargoDetailRepo             repository.CargoDetailRepository
	stiDestRepo                 repository.StiDestRepository
	logFailedDTPOLRepo          repository.LogFailedDTPOLRepository
	sttActivityUc               SttActivity
	CargoFlightRepo             repository.CargoFlightRepository
	transactionRepo             repository.TransactionRepository
	algoRepo                    repository.AlgoRepository
	deliveryRepo                repository.DeliveryRepository
	sttTransactionRepo          repository.SttTransactionRepository
	timeRepo                    repository.TimeRepository
	cargoV2Uc                   CargoV2
	ngenRepo                    repository.NgenRepository
	cargoRepo                   repository.CargoRepository
	handoverRepo                repository.HandoverRepository
	handoverDetailRepo          repository.HandoverDetailRepository
	countryRepo                 repository.CountryRepository
	predefinedHolidayRepo       repository.PredefinedHoliday
	rebuttalDexRepo             repository.RebuttalDexRepository
	partnerLogRepo              repository.PartnerLogRepository
	retryPubsubRepo             repository.RetryPubsubRepository
	checkoutRepo                repository.CheckoutRepository
	sttCorporateRepo            repository.SttCorporateRepository
	customProcessRepo           repository.CustomProcessRepository
	sttReverseJourneyRepository repository.SttReverseJourneyRepository
	exchangeRateRepo            repository.ExchangeRateRepository
	Pubsub                      pubsub.GooglePubsub
	partnerLog                  repository.PartnerLogRepository
	crossDockingFailedRepo      repository.CrossDockingFailedRepository
	slackRepo                   repository.SlackRepository
	bagVendorRepo               repository.BagVendorRepository
	dictionaryError             shared.DictionaryError
	codConfigRepo               repository.CodConfigRepository
	embargoConfigRepo           repository.EmbargoConfigRepository
	cityBalanceRepo             repository.CityBalanceRepository
	bagRepo                     repository.BagRepository
	bagUc                       BagOrchestra
	flagManagementRepo          repository.FlagManagementRepository
	pickupManifestCbpRepo       repository.PickupManifestCbpRepository
	rtcUc                       ReadyToCargo
	requestPriorityDelivery     RequestPriorityDelivery
	dexAssessmentRepo           repository.DexAssessmentRepository
	dtpolRepo                   repository.DtpolRepository
	sttDueRepo                  repository.SttDueRepository
	partnerLocationRepo         repository.PartnerLocationRepository
	internationalDocumentRepo   repository.InternationalDocumentRepository
	sttVendorRepo               repository.SttVendorRepository
	campaignSttQuoteRepo        repository.CampaignSttQuoteRepository
	cacheRepo                   repository.CacheRepository
	shortlinkRepo               repository.ShortlinkRepository
	sttAssessmentRepo           repository.SttAssessmentRepository
	sttCustomFlagRepo           repository.STTCustomFlagRepository
	configDfodPastiRepo         repository.ConfigDfodPastiRepository
	riskClassificationUc        ucRiskClassification.RiskClassification
	customerRepo                repository.CustomerRepository
	middlewareRepo              repository.MiddlewareCLient
	configurableRuleRepo        repository.ConfigurableRuleRepository
}

// NewSttUc ...
func NewSttUc(
	cfg *config.Config,
	sttRepo repository.SttRepository,
	sttManualRepo repository.SttManualRepository,
	commodityRepo repository.CommodityRepository,
	partnerRepo repository.PartnerRepository,
	clientRepo repository.ClientRepository,
	sttOptionalRateRepo repository.SttOptionalRateRepository,
	districtRepo repository.DistrictRepository,
	accountRepo repository.AccountRepository,
	checkTariffRepo repository.CheckTariffRepository,
	productRepo repository.ProductTypeRepository,
	sttPiecesRepo repository.SttPiecesRepository,
	cityRepo repository.CityRepository,
	estimateSlaRepo repository.EstimateSlaRepository,
	shipmentRepo repository.ShipmentRepository,
	shipmentPacketRepo repository.ShipmentPackageRepository,
	sttPieceHistoryRepo repository.SttPieceHistoryRepository,
	routeRepo repository.RouteRepository,
	configurablePriceRepo repository.ConfigurablePriceRepository,
	reasonRepo repository.ReasonRepository,
	gatewaySttUc GatewayStt,
	gatewaySttStatus GatewaySttStatus,
	walletRepo repository.WalletRepository,
	balanceLimitRepo repository.BalanceLimitRepository,
	messageGatewayUc MessageGateway,
	sttElexysRepo repository.SttElexysRepository,
	repoAccount repository.AccountRepository,
	cargoDetailRepo repository.CargoDetailRepository,
	stiDestRepo repository.StiDestRepository,
	logFailedDTPOLRepo repository.LogFailedDTPOLRepository,
	sttActivityUc SttActivity,
	CargoFlightRepo repository.CargoFlightRepository,
	transactionRepo repository.TransactionRepository,
	algoRepo repository.AlgoRepository,
	deliveryRepo repository.DeliveryRepository,
	sttTransactionRepo repository.SttTransactionRepository,
	timeRepo repository.TimeRepository,
	cargoV2Uc CargoV2,
	ngenRepo repository.NgenRepository,
	cargoRepo repository.CargoRepository,
	handoverRepo repository.HandoverRepository,
	handoverDetailRepo repository.HandoverDetailRepository,
	countryRepo repository.CountryRepository,
	predefinedHolidayRepo repository.PredefinedHoliday,
	rebuttalDexRepo repository.RebuttalDexRepository,
	partnerLogRepo repository.PartnerLogRepository,
	retryPubsubRepo repository.RetryPubsubRepository,
	checkoutRepo repository.CheckoutRepository,
	sttCorporateRepo repository.SttCorporateRepository,
	customProcessRepo repository.CustomProcessRepository,
	sttReverseJourneyRepository repository.SttReverseJourneyRepository,
	exchangeRateRepo repository.ExchangeRateRepository,
	Pubsub pubsub.GooglePubsub,
	partnerLog repository.PartnerLogRepository,
	crossDockingFailedRepo repository.CrossDockingFailedRepository,
	slackRepo repository.SlackRepository,
	bagVendorRepo repository.BagVendorRepository,
	dictionaryError shared.DictionaryError,
	codConfigRepo repository.CodConfigRepository,
	embargoConfigRepo repository.EmbargoConfigRepository,
	cityBalanceRepo repository.CityBalanceRepository,
	bagRepo repository.BagRepository,
	bagUc BagOrchestra,
	flagManagementRepo repository.FlagManagementRepository,
	pickupManifestCbpRepo repository.PickupManifestCbpRepository,
	rtcUc ReadyToCargo,
	requestPriorityDelivery RequestPriorityDelivery,
	internationalDocumentRepo repository.InternationalDocumentRepository,
	dexAssessmentRepo repository.DexAssessmentRepository,
	dtpolRepo repository.DtpolRepository,
	sttDueRepo repository.SttDueRepository,
	partnerLocationRepo repository.PartnerLocationRepository,
	sttVendorRepo repository.SttVendorRepository,
	campaignSttQuoteRepo repository.CampaignSttQuoteRepository,
	cacheRepo repository.CacheRepository,
	shortlinkRepo repository.ShortlinkRepository,
	sttAssessmentRepo repository.SttAssessmentRepository,
	sttCustomFlagRepo repository.STTCustomFlagRepository,
	configDfodPastiRepo repository.ConfigDfodPastiRepository,
	riskClassificationUc ucRiskClassification.RiskClassification,
	customerRepo repository.CustomerRepository,
	middlewareRepo repository.MiddlewareCLient,
	configurableRuleRepo repository.ConfigurableRuleRepository,
) Stt {
	return &sttCtx{
		cfg:                         cfg,
		sttRepo:                     sttRepo,
		clientRepo:                  clientRepo,
		partnerRepo:                 partnerRepo,
		sttManualRepo:               sttManualRepo,
		checkTariffRepo:             checkTariffRepo,
		commodityRepo:               commodityRepo,
		accountRepo:                 accountRepo,
		sttOptionalRateRepo:         sttOptionalRateRepo,
		districtRepo:                districtRepo,
		productRepo:                 productRepo,
		sttPiecesRepo:               sttPiecesRepo,
		cityRepo:                    cityRepo,
		estimateSlaRepo:             estimateSlaRepo,
		shipmentPacketRepo:          shipmentPacketRepo,
		shipmentRepo:                shipmentRepo,
		sttPieceHistoryRepo:         sttPieceHistoryRepo,
		routeRepo:                   routeRepo,
		configurablePriceRepo:       configurablePriceRepo,
		reasonRepo:                  reasonRepo,
		gatewaySttUc:                gatewaySttUc,
		gatewaySttStatus:            gatewaySttStatus,
		walletRepo:                  walletRepo,
		balanceLimitRepo:            balanceLimitRepo,
		messageGatewayUc:            messageGatewayUc,
		sttElexysRepo:               sttElexysRepo,
		repoAccount:                 repoAccount,
		cargoDetailRepo:             cargoDetailRepo,
		stiDestRepo:                 stiDestRepo,
		logFailedDTPOLRepo:          logFailedDTPOLRepo,
		sttActivityUc:               sttActivityUc,
		CargoFlightRepo:             CargoFlightRepo,
		transactionRepo:             transactionRepo,
		algoRepo:                    algoRepo,
		deliveryRepo:                deliveryRepo,
		sttTransactionRepo:          sttTransactionRepo,
		timeRepo:                    timeRepo,
		cargoV2Uc:                   cargoV2Uc,
		ngenRepo:                    ngenRepo,
		cargoRepo:                   cargoRepo,
		handoverRepo:                handoverRepo,
		handoverDetailRepo:          handoverDetailRepo,
		countryRepo:                 countryRepo,
		predefinedHolidayRepo:       predefinedHolidayRepo,
		rebuttalDexRepo:             rebuttalDexRepo,
		partnerLogRepo:              partnerLogRepo,
		retryPubsubRepo:             retryPubsubRepo,
		checkoutRepo:                checkoutRepo,
		sttCorporateRepo:            sttCorporateRepo,
		customProcessRepo:           customProcessRepo,
		sttReverseJourneyRepository: sttReverseJourneyRepository,
		exchangeRateRepo:            exchangeRateRepo,
		Pubsub:                      Pubsub,
		partnerLog:                  partnerLog,
		crossDockingFailedRepo:      crossDockingFailedRepo,
		slackRepo:                   slackRepo,
		bagVendorRepo:               bagVendorRepo,
		dictionaryError:             dictionaryError,
		codConfigRepo:               codConfigRepo,
		embargoConfigRepo:           embargoConfigRepo,
		cityBalanceRepo:             cityBalanceRepo,
		bagRepo:                     bagRepo,
		bagUc:                       bagUc,
		flagManagementRepo:          flagManagementRepo,
		pickupManifestCbpRepo:       pickupManifestCbpRepo,
		rtcUc:                       rtcUc,
		requestPriorityDelivery:     requestPriorityDelivery,
		dexAssessmentRepo:           dexAssessmentRepo,
		dtpolRepo:                   dtpolRepo,
		sttDueRepo:                  sttDueRepo,
		partnerLocationRepo:         partnerLocationRepo,
		internationalDocumentRepo:   internationalDocumentRepo,
		sttVendorRepo:               sttVendorRepo,
		campaignSttQuoteRepo:        campaignSttQuoteRepo,
		cacheRepo:                   cacheRepo,
		shortlinkRepo:               shortlinkRepo,
		sttAssessmentRepo:           sttAssessmentRepo,
		sttCustomFlagRepo:           sttCustomFlagRepo,
		configDfodPastiRepo:         configDfodPastiRepo,
		riskClassificationUc:        riskClassificationUc,
		customerRepo:                customerRepo,
		middlewareRepo:              middlewareRepo,
		configurableRuleRepo:        configurableRuleRepo,
	}
}
