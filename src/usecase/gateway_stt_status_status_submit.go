package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/Lionparcel/go-lptool/v2/lputils"

	json2 "github.com/Lionparcel/hydra/shared/json"

	"github.com/Lionparcel/hydra/config/pubsub"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/slack"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	gatewayStt "github.com/Lionparcel/hydra/src/usecase/gateway_stt"
)

func (c *gatewaySttStatusCtx) StatusSubmit(ctx context.Context, params *model.UpdateSttStatusWithExtendForMiddleware) error {
	opName := "UsecaseGateway_stt_status-StatusSubmit"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": "", "error": err})
	}()

	// set delay get from slave in millisecond
	time.Sleep(time.Duration(c.cfg.DelayGetFromSlave()) * time.Millisecond)

	sttPieceHistory, err := c.sttPieceHistoryRepo.SelectBySttNo(selfCtx, &model.SttPieceHistoryViewParam{
		SttNoIn:               []string{params.UpdateSttStatus.SttNo},
		SttPieceHistoryStatus: params.StatusCode,
		SortBy:                `sph.history_created_at`,
		OrderDesc:             true,
	})
	if err != nil {
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Error Get Stt Piece History",
			"id": "Gagal Mendapatkan Stt Piece History",
		})
		return err
	}

	if len(sttPieceHistory) < 1 {
		sttPieceHistory, err = c.sttPieceHistoryRepo.SelectBySttNo(selfCtx, &model.SttPieceHistoryViewParam{
			SttNoIn:               []string{params.UpdateSttStatus.SttNo},
			SttPieceHistoryStatus: params.StatusCode,
			SortBy:                `sph.history_created_at`,
			OrderDesc:             true,
			IsFromMaster:          true,
		})
		if err != nil {
			err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Error Get Stt Piece History",
				"id": "Gagal Mendapatkan Stt Piece History",
			})
			return err
		}
	}

	c.generateDataFromSttPieceHistory(selfCtx, params, sttPieceHistory)

	sttData, err := c.sttRepo.Get(selfCtx, &model.SttViewDetailParams{
		Stt: model.Stt{SttNo: params.SttNo},
	})
	if err != nil {
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Error Get Stt Detail",
			"id": "Gagal Mendapatkan Detail Stt",
		})
		return err
	}

	params.UpdateSttStatus.IsCod = sttData.SttIsCOD
	params.UpdateSttStatus.Sender = model.UpdateSttStatusSender{
		Name:    sttData.SttSenderName,
		Address: sttData.SttSenderAddress,
		Phone:   sttData.SttSenderPhone,
	}

	detailReverseJourney := sttData.GetSttMetaDetailReverseJourney()
	params.UpdateSttStatus.ShipmentID = sttData.SttShipmentID

	if detailReverseJourney.RootReverseShipmentID != `` {
		params.UpdateSttStatus.ShipmentID = detailReverseJourney.RootReverseShipmentID
	}

	if detailReverseJourney.ReverseSttNo != `` {
		params.UpdateSttStatus.RefSttNo = detailReverseJourney.ReverseSttNo
	}

	if detailReverseJourney.RootReverseSttNo != `` {
		params.SttJourneyType, _ = c.middlewareRepo.DetailSttTrackingJourney(selfCtx, model.SttDetailTrackingJourneyParam{
			SttNo:           params.SttNo,
			SttTrackingMeta: &detailReverseJourney,
		})
	}
	c.setParamsForPubsub(ctx, params, sttData)
	updateStt, err := json2.EncodeWithoutEscapeHTMLWithErr(params.UpdateSttStatus)
	if err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed parse to JSON",
			"id": "Gagal memparsing ke JSON",
		})
	}

	result, err := c.PublishSttStatusUpdate(selfCtx, updateStt, params.SttNo)
	if err != nil {
		return err
	}

	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.partnerLog.Insert(goCtx, &model.PartnerLog{
			Action:   model.PLSttStatusSubmit,
			RefID:    params.SttNo,
			Request:  params.UpdateSttStatus,
			Response: result,
		})
	})

	/*
	 * Add code to send to middleware after update stt status
	 */
	lputils.TrackGoroutine(func(ctx context.Context) {
		if strings.EqualFold(params.BookedForType, model.CLIENT) {
			/*
			 * Checking if
			 * is_with_proof_file is true and
			 * update_from is algo-system and
			 * status_code is POD then call endpoint algo check image url
			 */
			if params.IsWithProofFile && params.UpdatedBy == model.ALGO_ACCOUNT_NAME &&
				params.StatusCode == model.POD {

				/* looping 3 times to call endpoint algo */
				isUrlPictureSuccessToBeGetFromAlgo := false
				var res *model.AlgoGetPhoto
				for i := 0; i < 3; i++ {

					// sleep 5 second every iteration to make sure algo done process
					time.Sleep(5 * time.Second)

					//
					res, err := c.algoRepo.GetPhotos(ctx, params.SttNo)
					if err == nil && res != nil {
						params.UrlPicture = strings.Join(res.PhotoUrl, ",")
						isUrlPictureSuccessToBeGetFromAlgo = true
						break // break 3 time loops
					}
				}

				if !isUrlPictureSuccessToBeGetFromAlgo {
					// Send log to activity log
					resByte, _ := json.Marshal(res)
					tmpLog := &model.PartnerLog{
						Action:   model.PLMiddlewareRequest,
						RefID:    "error-log-algo",
						Request:  params.SttNo,
						Response: string(resByte),
					}
					go c.partnerLog.Insert(context.Background(), tmpLog)
				}
			}

			if c.shouldSubmitToMiddleware(params.UpdateSttStatus.StatusCode) {
				err := c.middlewareRepo.SubmitDataToMiddleware(ctx, params)
				if err != nil {
					logger.Ef(`Submit-DataToMiddlewares Error %s`, err.Error())
					jsonParam, _ := json.Marshal(params)
					tmpLog := &model.PartnerLog{
						Action:   model.PLMiddlewareRequest,
						RefID:    "error-submit-middleware",
						Request:  params,
						Response: err,
					}
					go c.partnerLog.Insert(ctx, tmpLog)

					go slack.SendNotification(slack.Notification{
						Title:     "Error Submit Data To Middleware(Hydra)",
						Body:      string(jsonParam),
						Ctx:       "GatewaySttStatus-StatusSubmit",
						Error:     err,
						Channel:   slack.GenesisNotifStatusSubmit,
						Indicator: slack.WarningIndicator,
					})
				}
			}
		}
	}, int((30 * time.Minute).Seconds()))
	/** end phase of Add code to send to middleware after submit stt **/

	return nil
}

func (c *gatewaySttStatusCtx) PublishSttStatusUpdate(ctx context.Context, updateStt string, sttNo string) (string, error) {
	topicID := shared.GeneratePubsubTopicID(c.cfg.UpdateStatusSttTopicID(), c.cfg.EnvAlgoPubsub(), shared.PubSubAlgo)

	result, err := c.Pubsub.PublishMessage(ctx, topicID, &pubsub.PubSubMessage{
		Message:               updateStt,
		OrderingKey:           sttNo,
		EnableMessageOrdering: true,
		Attributes:            model.AttributesPubsubGenesis,
	})

	if err != nil {
		return "", shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to publish message update status stt",
			"id": "Gagal menerbitkan pesan update status stt",
		})
	}

	return result, nil
}

func (c *gatewaySttStatusCtx) generateDataFromSttPieceHistory(ctx context.Context, params *model.UpdateSttStatusWithExtendForMiddleware, sttPieceHistory []model.SttPieceHistoryCustom) {
	opName := "UsecaseGateway_stt_status-StatusSubmit-generateDataFromSttPieceHistory"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var (
		err          error
		paramsBefore = params
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		trace.Finish(map[string]interface{}{"param_before": paramsBefore, "param_after": params, "sttPieceHistory": sttPieceHistory, "result": nil, "error": err})
	}()

	if len(sttPieceHistory) <= 0 {
		return
	}

	if params.Partner.ID == 0 && sttPieceHistory[0].HistoryActorID != 0 {
		var accountToken *model.AccountTokenResponse
		accountToken, err = c.accountRepo.GetTokenGenesis(selfCtx)
		if accountToken != nil {
			var partner *model.Partner
			partner, err = c.partnerRepo.GetByID(selfCtx, sttPieceHistory[0].HistoryActorID, accountToken.Data.Token)
			if partner != nil {
				params.Partner = model.GenerateUpdateSttStatusPartnerData(*partner)
				params.LastStatusPartnerCode = params.Partner.PartnerCode
			}
		}
	}

	if params.Partner.ID != 0 {
		params.LastStatusPartnerCode = params.Partner.PartnerCode
	}

	injectRemarksToUpdateParams(params, sttPieceHistory[0])

	params.LastStatusPartnerLoc = sttPieceHistory[0].HistoryLocation
	params.LastStatusPartnerType = sttPieceHistory[0].HistoryActorRole
	params.LastStatusPartnerName = sttPieceHistory[0].HistoryActorName

	c.generateDataFromSttPieceHistoryAgentName(selfCtx, params, sttPieceHistory)

	c.generateDataFromSttPieceHistoryReason(selfCtx, params, sttPieceHistory)

	// Generate Reason for DEX / CODREJ
	c.generateDataFromSttPieceHistoryReasonDEXCODREJ(ctx, params, sttPieceHistory)

	// stt shipment tokopedia
	c.getReasonCode(selfCtx, sttPieceHistory, params)
}

func (c *gatewaySttStatusCtx) generateDataFromSttPieceHistoryAgentName(ctx context.Context, params *model.UpdateSttStatusWithExtendForMiddleware, sttPieceHistory []model.SttPieceHistoryCustom) {
	// pos / subconsole / console / system / internal
	params.UpdateSttStatus.AgentName = sttPieceHistory[0].HistoryActorName

	remarkPieceHistory := sttPieceHistory[0].RemarkPieceHistoryToStruct()
	hasHub := sttPieceHistory[0].HistoryActorRole == model.CONSOLE && remarkPieceHistory != nil && remarkPieceHistory.HubName != ``
	if hasHub {
		params.UpdateSttStatus.AgentName = fmt.Sprintf(`%s - %s`, params.UpdateSttStatus.AgentName, remarkPieceHistory.HubName)
		params.UpdateSttStatus.HubID = remarkPieceHistory.HubID
		params.UpdateSttStatus.HubName = remarkPieceHistory.HubName
	}

	// System
	if strings.EqualFold(params.UpdateSttStatus.AgentName, model.AccountSystem.ActorName) {
		params.UpdateSttStatus.AgentName = gatewayStt.AccountSystem
	}

	// Internal To Admin
	if strings.EqualFold(params.UpdateSttStatus.AgentName, model.AccountInternal.ActorName) {
		params.UpdateSttStatus.AgentName = gatewayStt.AccountAdmin
	}
}

func (c *gatewaySttStatusCtx) generateDataFromSttPieceHistoryReason(ctx context.Context, params *model.UpdateSttStatusWithExtendForMiddleware, sttPieceHistory []model.SttPieceHistoryCustom) {
	if params.UpdateSttStatus.StatusCode != model.REJECTED {
		return
	}

	remarkPieceHistory := sttPieceHistory[0].RemarkPieceHistoryToStruct()

	params.UpdateSttStatus.ReasonCode = sttPieceHistory[0].HistoryReason
	params.UpdateSttStatus.ReasonTitle = remarkPieceHistory.CustomProcessRemarks
	if params.ReasonCode == `` || params.ReasonTitle == `` {
		reason, err := c.reasonRepo.GetDetail(ctx, &model.ReasonViewParams{
			StatusCode:  params.UpdateSttStatus.StatusCode,
			ReasonCode:  params.ReasonCode,
			ReasonTitle: params.ReasonTitle,
		})
		if reason == nil || err != nil {
			return
		}
		params.UpdateSttStatus.ReasonCode = reason.ReasonCode
		params.UpdateSttStatus.ReasonTitle = reason.ReasonTitle
	}
}

// shouldSubmitToMiddleware checks if the status code requires middleware submission
func (c *gatewaySttStatusCtx) shouldSubmitToMiddleware(statusCode string) bool {
	excludedStatuses := []string{
		model.HALCD,
		model.SCRAPCD,
		model.CNXCD,
		model.STLDISPATCH,
	}

	for _, excludedStatus := range excludedStatuses {
		if statusCode == excludedStatus {
			return false
		}
	}
	return true
}

func injectRemarksToUpdateParams(params *model.UpdateSttStatusWithExtendForMiddleware, sttPieceHistory model.SttPieceHistoryCustom) {
	remarksHistory := sttPieceHistory.RemarkPieceHistoryToStruct()
	if remarksHistory == nil {
		return
	}

	params.CourierName = remarksHistory.DriverName
	params.CourierPhone = remarksHistory.DriverPhone
	params.UpdateSttStatus.Attachments = remarksHistory.Attactments
	historyStatus := sttPieceHistory.HistoryStatus
	isGenerateReverseJourney := historyStatus == model.REROUTE ||
		historyStatus == model.RTS ||
		historyStatus == model.RTSHQ

	if isGenerateReverseJourney {
		params.UpdateSttStatus.NextSttNo = remarksHistory.CustomProcessRemarks
	}

	params.UpdateSttStatus.ClaimNo = remarksHistory.ClaimNo
}

// setParamsForPubsub sets the parameters for pubsub submission
func (c *gatewaySttStatusCtx) setParamsForPubsub(ctx context.Context, params *model.UpdateSttStatusWithExtendForMiddleware, sttData *model.Stt) {
	params.UpdateSttStatus.ListSystemStatus = params.ListSystemStatus
	hasInsurance, insuranceRate := c.getInsuranceData(ctx, sttData)

	piecesData := c.getPiecesData(ctx, sttData)

	if len(params.UpdateSttStatus.ListSystemStatus) > 0 {
		for i := range params.UpdateSttStatus.ListSystemStatus {
			c.setSystemStatus(&params.UpdateSttStatus.ListSystemStatus[i], sttData, hasInsurance, insuranceRate, piecesData)
		}
	}
}

// getInsuranceData retrieves insurance information for the STT
func (c *gatewaySttStatusCtx) getInsuranceData(ctx context.Context, sttData *model.Stt) (bool, float64) {
	optionalRates, _ := c.sttOptionalRateRepo.Select(ctx, &model.SttOptionalRate{
		SttOptionalRateSttID:  sttData.SttID,
		SttOptionalRateParams: model.INSURANCE,
	})

	for _, v := range optionalRates {
		if v.SttOptionalRateParams == model.INSURANCE && v.SttOptionalRateName != model.INSURANCEFREENAME {
			return true, v.SttOptionalRateRate
		}
	}
	return false, 0
}

// getPiecesData retrieves pieces information for the STT
func (c *gatewaySttStatusCtx) getPiecesData(ctx context.Context, sttData *model.Stt) []model.SttPieceForClient {
	pieces, _ := c.sttPieceRepo.Select(ctx, &model.SttPiecesViewParam{
		SttID: int(sttData.SttID),
	})

	if len(pieces) == 0 {
		return nil
	}

	piecesDetails := make([]model.SttPieceForClient, 0, len(pieces))
	for _, piece := range pieces {
		piecesDetails = append(piecesDetails, model.SttPieceForClient{
			PieceID:           piece.SttPieceID,
			PieceLength:       piece.SttPieceLength,
			PieceWidth:        piece.SttPieceWidth,
			PieceHeight:       piece.SttPieceHeight,
			PieceGrossWeight:  piece.SttPieceGrossWeight,
			PieceVolumeWeight: piece.SttPieceVolumeWeight,
		})
	}
	return piecesDetails
}

// setMainParam sets the main parameters for the middleware submission
func (c *gatewaySttStatusCtx) setMainParam(param *model.UpdateSttStatusWithExtendForMiddleware, sttData *model.Stt, hasInsurance bool, insuranceRate float64, piecesData []model.SttPieceForClient) {
	param.Partner = model.UpdateSttStatusPartnerData{}
	param.Sender = model.UpdateSttStatusSender{}
	param.ShipmentID = sttData.SttShipmentID
	param.TotalTariff = sttData.SttTotalAmount
	param.IsInsurance = hasInsurance
	param.InsuranceType = sttData.SttInsuranceType
	param.InsuranceRate = insuranceRate
	param.PiecesDetails = piecesData

	param.LastStatusPartnerType = ""
	param.LastStatusPartnerLoc = ""
	param.LastStatusPartnerName = ""
	param.LastStatusPartnerCode = ""
	param.AgentName = ""

	param.HubID = 0
	param.HubName = ""
}

// setSystemStatus sets the system status with the provided data
func (c *gatewaySttStatusCtx) setSystemStatus(param *model.SystemStatus, sttData *model.Stt, hasInsurance bool, insuranceRate float64, piecesData []model.SttPieceForClient) {
	param.Partner = model.UpdateSttStatusPartnerData{}
	param.Sender = model.UpdateSttStatusSender{}
	param.ShipmentID = sttData.SttShipmentID
	param.TotalTariff = sttData.SttTotalAmount
	param.IsInsurance = hasInsurance
	param.InsuranceType = sttData.SttInsuranceType
	param.InsuranceRate = insuranceRate
	param.PiecesDetails = piecesData

	param.LastStatusPartnerType = ""
	param.LastStatusPartnerLoc = ""
	param.LastStatusPartnerName = ""
	param.LastStatusPartnerCode = ""
	param.AgentName = ""

	param.HubID = 0
	param.HubName = ""
}
