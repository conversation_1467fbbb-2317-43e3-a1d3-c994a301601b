package usecase

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strings"
	"time"

	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
)

func (c *ptPosCtx) CreateOrderPtPos(ctx context.Context, params *model.CreateOrderPtPosParams) error {
	var (
		opName  = "ptPosCtx-CreateOrderPtPos"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		errLog  string
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "error": err})

		if errLog == "" && err != nil {
			return
		}
		c.backgroundProcessPartnerLog(&model.PartnerLog{
			Action:   model.PLSttPTPOSCreateOrder,
			RefID:    params.Stt.SttNo,
			Request:  params,
			Response: errLog,
		})
	}()

	co := createOrderPtPos{
		service: c,
		params:  params,
		now:     time.Now(),
	}

	funcs := []func(ctx context.Context) error{
		co.getDestinationDistrict,
		co.validate,
		co.getPiece,
		co.generateOriginPostalCode,
		co.generateDestinationPostalCode,
		co.getPtPosFee,
		co.createOrder,
	}

	for i := range funcs {
		err = funcs[i](selfCtx)
		if err != nil {
			if co.errLog != "" {
				return err
			}

			/*
				no need handle error
				- district is not for pt pos
				- stt vendor already created
			*/
			return nil
		}
	}

	return nil
}

type createOrderPtPos struct {
	service *ptPosCtx
	params  *model.CreateOrderPtPosParams
	errLog  string
	now     time.Time

	destinationDistrict *model.District
	sttDetailResult     *model.SttDetailResult

	originPostalCode      string
	destinationPostalCode string

	fee *model.PtPosGetFeeResponseData
}

func (c *createOrderPtPos) getDestinationDistrict(ctx context.Context) error {
	var (
		opName  = "createOrderPtPos-getDestinationDistrict"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": err})
	}()

	destinationDistrict, err := c.service.districtRepo.GetByCode(selfCtx, &model.CredentialRestAPI{
		Token: c.params.Token,
	}, c.params.Stt.SttDestinationDistrictID)

	if err != nil {
		c.errLog = fmt.Sprintf("Failed getting destination district. %v", err)
		return err
	}

	if destinationDistrict == nil {
		c.errLog = "Failed getting destination district. not found"
		return errors.New(c.errLog)
	}

	c.destinationDistrict = destinationDistrict

	return nil
}

// no need partner log
func (c *createOrderPtPos) validate(ctx context.Context) error {
	err := errors.New("validate stt")

	if !model.IsDistrictTypeVendor[c.destinationDistrict.Data.Type] || !model.IsValidPTPOSVendorType[c.destinationDistrict.Data.VendorCode] {
		return err
	}

	if c.params.Stt.SttPaymentStatus == model.UNPAID {
		return err
	}

	if c.params.Stt.SttIsCOD {
		return err
	}

	sttVendor, err := c.service.sttVendorRepo.Get(ctx, &model.SttVendorViewParams{SttID: c.params.Stt.SttID})
	if err != nil {
		return err
	}

	// stt already booking to ptpos
	if sttVendor != nil {
		return errors.New("validate stt already booked")
	}

	return nil
}

func (c *createOrderPtPos) getPiece(ctx context.Context) error {
	var (
		opName  = "createOrderPtPos-getPiece"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": err})
	}()

	pieces, err := c.service.sttPiecesRepo.SelectDetail(selfCtx, &model.SttPiecesViewParam{
		BasedFilter: model.BasedFilter{
			Limit: 1,
		},
		SttID:      int(c.params.Stt.SttID),
		SttPieceNo: 1,
	})

	if err != nil {
		c.errLog = fmt.Sprintf("Failed getting pieces. %v", err)
		return err
	}

	if len(pieces) == 0 {
		c.errLog = "Failed getting pieces, not found"
		return errors.New(c.errLog)
	}

	c.sttDetailResult = &pieces[0]

	return nil
}

func (c *createOrderPtPos) generateOriginPostalCode(ctx context.Context) error {
	if c.params.Partner.Data.PartnerLocation.District.OriginZipCode != model.NULL {
		c.originPostalCode = c.params.Partner.Data.PartnerLocation.District.OriginZipCode
		postcodeList := strings.Split(c.originPostalCode, ",")
		if len(postcodeList) > 0 {
			c.originPostalCode = postcodeList[0]
		}
	}
	c.originPostalCode = strings.TrimSpace(c.originPostalCode)

	return nil
}

func (c *createOrderPtPos) generateDestinationPostalCode(ctx context.Context) error {
	if c.destinationDistrict.Data.DestinationZipCode != model.NULL {
		c.destinationPostalCode = c.destinationDistrict.Data.DestinationZipCode
		postcodeList := strings.Split(c.destinationPostalCode, ",")
		if len(postcodeList) > 0 {
			c.destinationPostalCode = postcodeList[0]
		}
	}
	c.destinationPostalCode = strings.TrimSpace(c.destinationPostalCode)

	return nil
}

func (c *createOrderPtPos) calculateDimension(f float64) float64 {
	if f < 1 {
		return 1
	}
	return math.Trunc(f)
}

func (c *createOrderPtPos) getPtPosFee(ctx context.Context) error {
	var (
		opName  = "createOrderPtPos-getPtPosFee"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": err})
	}()

	fees, err := c.service.ptPosRepo.GetFee(selfCtx, &model.PtPosGetFeeParams{
		SttNo:           c.params.Stt.SttNo,
		DestTypeID:      "1",
		ItemTypeID:      "1",
		ShipperZipCode:  c.originPostalCode,
		ReceiverZipCode: c.destinationPostalCode,
		Weight:          math.Trunc(c.params.Stt.SttChargeableWeight) * 1000,
		Length:          c.calculateDimension(c.sttDetailResult.SttPieceLength),
		Width:           c.calculateDimension(c.sttDetailResult.SttPieceWidth),
		Height:          c.calculateDimension(c.sttDetailResult.SttPieceHeight),
		Diameter:        0,
		ValueGoods:      0,
	})
	if err != nil {
		c.errLog = fmt.Sprintf("Failed getting fee from PT POS. %v", err)
		return err
	}

	if fees == nil {
		c.errLog = "Failed getting fee from PT POS. not found"
		return errors.New(c.errLog)
	}

	serviceCode := c.service.cfg.PtPosServiceCode()
	for i := range fees.Response.Data {
		if fees.Response.Data[i].ServiceCode != serviceCode {
			continue
		}

		c.fee = &fees.Response.Data[i]
		break
	}

	if c.fee == nil {
		c.errLog = fmt.Sprintf("Failed getting fee from PT POS. ServiceCode %s not found", serviceCode)
		return errors.New(c.errLog)
	}

	return nil
}

func (c *createOrderPtPos) createOrder(ctx context.Context) error {
	var (
		opName  = "createOrderPtPos-createOrder"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": err})
	}()

	createOrder := &model.PtPosCreateOrderParams{
		UserID:      c.service.cfg.PtPosUserID(),
		MemberID:    c.service.cfg.PtPosMemberID(),
		OrderID:     c.params.Stt.SttNo,
		ItemDetails: model.PtPosDefaultItemDetail,
		Services:    model.PtPosDefaultService,
	}

	c.assignOrderSenderReceiver(createOrder)
	c.assignOrderItemProperties(createOrder)
	c.assignOrderFee(createOrder)

	orderResp, err := c.service.ptPosRepo.CreateOrder(selfCtx, createOrder)
	if err != nil {
		c.errLog = fmt.Sprintf("Failed create Order PT POS. %v", err)
		return err
	}

	if orderResp.RespCode != "000" {
		c.errLog = fmt.Sprintf("Failed create Order PT POS. %v", orderResp.ResMsg)
		return err
	}

	if err := c.service.sttVendorRepo.Create(selfCtx, &model.SttVendor{
		SttVendorVendorCode:  model.TypeVendorPTPOS,
		SttVendorReferenceNo: orderResp.TransRef,
		SttVendorSttNo:       c.params.Stt.SttNo,
		SttVendorSttID:       int(c.params.Stt.SttID),
	}); err != nil {
		c.errLog = fmt.Sprintf("Failed create stt vendor PT POS with order. %v", err)
		return err
	}

	if err = c.createPtposRequestData(selfCtx, createOrder); err != nil {
		c.errLog = fmt.Sprintf("Failed create ptpos request data. %v", err)
		return err
	}

	return nil
}

func (c *createOrderPtPos) assignOrderSenderReceiver(createOrder *model.PtPosCreateOrderParams) {
	sender := model.PtPosSenderLocation
	sender.Name = fmt.Sprintf("Lion Parcel %s", c.params.Partner.Data.PartnerLocation.City.Name)
	sender.Phone = c.params.Partner.Data.PhoneNumber
	sender.Email = "<EMAIL>"
	sender.Address = c.params.Partner.Data.Address
	sender.Subdistrict = c.params.Partner.Data.PartnerLocation.District.Name
	sender.City = c.params.Partner.Data.PartnerLocation.City.Name
	sender.ZipCode = c.originPostalCode
	createOrder.Addresses = append(createOrder.Addresses, sender)

	receiver := model.PtPosReceiverLocation
	receiver.Name = c.params.Stt.SttRecipientName
	receiver.Phone = c.params.Stt.SttRecipientPhone
	receiver.Address = c.params.Stt.SttRecipientAddress
	receiver.Subdistrict = c.destinationDistrict.Data.Name
	receiver.City = c.params.Stt.SttDestinationCityName
	receiver.ZipCode = c.destinationPostalCode
	createOrder.Addresses = append(createOrder.Addresses, receiver)

	pickup := model.PtPosPickupLocation
	if c.service.cfg.ServiceTypePTPosPickup() == 1 {
		pickup.Name = fmt.Sprintf("Lion Parcel %s", c.params.Partner.Data.PartnerLocation.City.Name)
		pickup.Phone = c.params.Partner.Data.PhoneNumber
		pickup.Email = "<EMAIL>"
		pickup.Address = c.params.Partner.Data.Address
		pickup.Subdistrict = c.params.Partner.Data.PartnerLocation.District.Name
		pickup.City = c.params.Partner.Data.PartnerLocation.City.Name
		pickup.ZipCode = c.originPostalCode
		pickup.CustomerType = c.service.cfg.CustomerTypePTPosPickup()
		pickup.Geolang = c.params.Partner.Data.Long
		pickup.Geolat = c.params.Partner.Data.Lat
	}
	createOrder.Addresses = append(createOrder.Addresses, pickup)
	createOrder.Addresses = append(createOrder.Addresses, model.PtPosDeliveryLocation)
}

func (c *createOrderPtPos) assignOrderItemProperties(createOrder *model.PtPosCreateOrderParams) {
	createOrder.ItemProperties = model.PtPosCreateOrderItemProperties{
		ItemTypeID:       1,
		ProductID:        c.service.cfg.PtPosServiceCode(),
		ValueGoods:       c.params.Stt.SttGoodsEstimatePrice,
		Weight:           math.Trunc(c.params.Stt.SttChargeableWeight) * 1000,
		Length:           c.calculateDimension(c.sttDetailResult.SttPieceLength),
		Width:            c.calculateDimension(c.sttDetailResult.SttPieceWidth),
		Height:           c.calculateDimension(c.sttDetailResult.SttPieceHeight),
		PercentageCodFee: "0%",
		ItemDesc:         c.params.Stt.SttCommodityName,
	}
}

func (c *createOrderPtPos) assignOrderFee(createOrder *model.PtPosCreateOrderParams) {
	// default insurance
	insuranceDefault := model.PtPosCreateOrderNameValue{
		Name:  "insurance",
		Value: 0,
	}

	createOrder.Services[5] = model.PtPosCreateOrderNameValue{
		Name:  "pickup",
		Value: float64(c.service.cfg.ServiceTypePTPosPickup()),
	}

	createOrder.PaymentValues = append(createOrder.PaymentValues, model.PtPosCreateOrderNameValue{
		Name:  "fee",
		Value: c.fee.Fee,
	})
	createOrder.PaymentValues = append(createOrder.PaymentValues, insuranceDefault)

	createOrder.Taxes = append(createOrder.Taxes, model.PtPosCreateOrderNameValue{
		Name:  "fee",
		Value: c.fee.FeeTax,
	})
	createOrder.Taxes = append(createOrder.Taxes, insuranceDefault)
}

func (c *createOrderPtPos) createPtposRequestData(ctx context.Context, createOrder *model.PtPosCreateOrderParams) error {
	var (
		mdl = &model.PtposRequest{
			PrSttNo:               c.params.Stt.SttNo,
			PrLastStatusUpdatedAt: c.now,
			PrCreatedAt:           c.now,
			PrUpdatedAt:           c.now,
		}
	)
	ptposReqData, err := c.service.ptposRequestRepo.GetDetail(ctx, c.params.Stt.SttNo)
	if err != nil {
		return err
	}

	if ptposReqData != nil {
		return nil
	}

	return c.service.ptposRequestRepo.Create(ctx, mdl)
}
