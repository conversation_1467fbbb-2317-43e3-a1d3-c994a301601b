package usecase

import (
	"context"
	"errors"
	"fmt"

	"github.com/Lionparcel/go-lptool/lputils"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/jne"
)

// JneWebhook ...
func (c *jneCtx) JneWebhook(ctx context.Context, params *jne.JneWebhookRequest) (err error) {
	opName := "UsecaseJNE-JneWebhook"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	lastStatusJNE := ``
	var resp error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": err, "error_log": resp})

		go lputils.TrackGoroutine(func(goCtx context.Context) {
			c.partnerLog.Insert(goCtx, &model.PartnerLog{
				Action:   model.PLSttJNEUpdateStatus,
				RefID:    params.OrderID,
				Request:  params,
				Response: map[string]any{"error_log": err, "result": shared.CheckErrorOrSuccess(resp)},
			})
		})
	}()

	// validate request
	if resp = params.Validate(); resp != nil {
		err = resp
		return
	}

	if len(params.History) == 0 {
		resp = errors.New("History cannot be empty")
		return
	}

	sendStatusNotificationToIntenal := func(err error, status string, orderID string) {
		go lputils.TrackGoroutine(func(goCtx context.Context) {
			c.notificationRepo.SendNotification(goCtx, &model.WebhookMessage{
				Username: model.ServiceName,
				Embeds: []model.Embed{
					{
						Title:       fmt.Sprintf("%s - Error JNE Update Status %s", orderID, status),
						Color:       model.NotifyColorError,
						Description: params.String(),
						Fields: []model.Field{
							{
								Name:  "Error",
								Value: err.Error(),
							},
						},
					},
				},
			})
		})
	}

	// get detail stt by order id
	sttDetail, err := c.sttRepo.SelectDetailV2(selfCtx, &model.SttSelectDetailParams{SttNo: params.OrderID, IsNeedDecrypt: true})
	if err != nil {
		resp = errors.New("An error occurred while querying db")
		return
	}

	if len(sttDetail) == 0 {
		resp = errors.New("STT Number Not Found")
		sendStatusNotificationToIntenal(resp, "", params.OrderID)
		return
	}

	lastStatusJNE, resp = shared.MappingJNEStatus(params.History[len(params.History)-1].StatusCode)
	if resp != nil {
		resp = errors.New("JNE Status Not Allowed")
		sendStatusNotificationToIntenal(resp, "", params.OrderID)
		return
	}

	sttRow := sttDetail[0]

	// validate last status stt
	if lastStatusJNE != model.DEX || lastStatusJNE != model.DEL {
		if sttRow.SttLastStatusID == lastStatusJNE {
			resp = errors.New("JNE Status cannot duplicate")
			sendStatusNotificationToIntenal(resp, lastStatusJNE, params.OrderID)
			return
		}
	}

	isCIStatusAndAvailable := sttRow.SttLastStatusID == model.CI && model.IsAllowUpdateStatusVendorWhenStatusCI[lastStatusJNE]
	if isCIStatusAndAvailable || model.IsJNENotAvailableForceUpdateStatus[lastStatusJNE] {
		resp = fmt.Errorf("Cannot update status JNE when last status = %s and request status = %s", sttRow.SttLastStatusID, lastStatusJNE)
		return nil
	}

	var errSetStatus error
	switch lastStatusJNE {
	case model.DEL:
		// Update status to Del
		errSetStatus = c.setToDel(selfCtx, params, sttDetail)
	case model.HAL:
		// Update status to Hal
		params.SttStatus = lastStatusJNE
		errSetStatus = c.setToHal(selfCtx, params, sttDetail)
	case model.DEX:
		// Update status to DEX
		errSetStatus = c.setToDex(selfCtx, params, sttDetail)
	case model.RTS:
		// Update status to RTS
		errSetStatus = c.setToRTS(selfCtx, params, sttDetail)
	case model.STIDESTSC:
		// Update status to StiDestSc
		errSetStatus = c.setToStiDestSc(selfCtx, params, sttDetail)
	case model.POD:
		// Update status to POD
		errSetStatus = c.setToPod(selfCtx, params, sttDetail)
	}

	if errSetStatus != nil {
		resp = errSetStatus
		sendStatusNotificationToIntenal(errSetStatus, lastStatusJNE, params.OrderID)
		return
	}

	return nil
}

func (c *jneCtx) getSttAtSlaveAndMaster(ctx context.Context, sttNo string) (*model.Stt, error) {
	stt, err := c.sttRepo.Get(ctx, &model.SttViewDetailParams{
		Stt: model.Stt{
			SttNo: sttNo,
		},
	})
	if err != nil {
		return nil, shared.ERR_UNEXPECTED_DB
	}

	if stt != nil {
		return stt, nil
	}

	stt, err = c.sttRepo.GetMaster(ctx, &model.SttViewDetailParams{
		Stt: model.Stt{
			SttNo: sttNo,
		},
	})
	if err != nil {
		return nil, shared.ERR_UNEXPECTED_DB
	}

	return stt, nil
}
