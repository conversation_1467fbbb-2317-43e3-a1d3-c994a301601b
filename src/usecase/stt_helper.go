package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	"github.com/abiewardani/dbr/v2"
	"github.com/jinzhu/copier"

	"github.com/Lionparcel/hydra/config/pubsub"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/repository"
	"github.com/Lionparcel/hydra/src/usecase/elexys"
	"github.com/Lionparcel/hydra/src/usecase/gateway_stt"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *sttCtx) MockeryShipmentGenerate(ctx context.Context, prefix string) (shipmentID string, err error) {
	if prefix == `` {
		return ``, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Please fill prefix Shipment",
			"id": "Silahkan Isi Prefix Shipment",
		})
	}

	if !model.IsPrefixShipmentValid[prefix] {
		return ``, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Prefix Shipment Invalid",
			"id": "Prefix Shipment Invalid",
		})
	}

	randomID := shared.RandomStt()
	shipmentID = strings.ToUpper(fmt.Sprintf("%s%s", prefix, randomID))

	return shipmentID, nil
}

func (c *sttCtx) createShipment(ctx context.Context, params *stt.ViewDetailShipmentAlgoRequest, data *model.ShipmentAlgo) (*model.Shipment, error) {
	var (
		opName        = "Usecases-createShipment"
		trace         = tracer.StartTrace(ctx, opName)
		selfCtx       = trace.Context()
		existShipment = new(model.Shipment)
		err           error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{
			"param": map[string]interface{}{
				"params": params,
				"data":   data,
			},
			"result": existShipment,
			"error":  err})
	}()

	shipmentPackets := make([]model.ShipmentPacket, 0)

	/** build payload for insert shipment (START)*/
	for _, v := range data.Packets {
		shipmentPacket, err := c.buildShipmentPacket(selfCtx, params.ShipmentID, &v)
		if err != nil {
			return nil, err
		}
		shipmentPackets = append(shipmentPackets, *shipmentPacket)
	}

	/** build payload for insert shipment (END)*/
	shipment := &model.Shipment{
		ShipmentAlgoID:          params.ShipmentID,
		ShipmentAlgoNumberOfStt: data.NumberOfStt,
		ShipmentAlgoAgentCode:   data.AgentCode,
		ShipmentShipmentID:      data.ShipmentID,
		ShipmentBookingID:       data.BookingID,
		ShipmentPackets:         shipmentPackets,
		ShipmentCreatedAt:       params.Now,
		ShipmentUpdatedAt:       params.Now,
		ShipmentGroupBookingID:  data.GroupID,
	}

	shipmentMeta := model.ShipmentMeta{
		TotalShipmentGroup: data.TotalShipmentGroup,
	}

	// Jika Prefix C1, Save data cod_handling form algo service
	shimpentID, algoCodHandling := getShipmentIDAndCodHandling(params, data)

	prefix := ""
	if shimpentID != "" {
		prefix = shared.GetPrefixShipmentID(shimpentID)
	}

	if prefix == model.C1 || prefix == model.C2 {
		// validate data cod_handling from algo
		codHandling := ""
		if model.IsShipmentC1CodHandling[algoCodHandling] {
			codHandling = algoCodHandling
		}

		// Set cod_handling in ShipmentMeta
		shipmentMeta.CodHandling = codHandling
	}

	if model.IsShipmentFavorite[prefix] {
		shipmentMeta.DiscountFavoritePercentage = data.DiscountFavoritePercentage
	}

	shipmentMetaStr := shipmentMeta.ToString()
	shipment.ShipmentMeta = &shipmentMetaStr

	if prefix == model.TSLP {
		shipment.ShipmentAlgoID = data.ShipmentID
		params.ShipmentID = data.ShipmentID
	}

	c.handlePrioritySubscription(prefix, data.Packets, shipment)

	if err = c.shipmentRepo.Create(selfCtx, shipment); err != nil {
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while saving shipment",
			"id": "Terjadi kesalahan pada saat query saving shipment",
		})
		return nil, err
	}

	maxLoop := c.cfg.GetMaxLoopRetrySelect()
	delayInMs := c.cfg.RetrySelectDelayInMs()
	l := 0
	for {
		existShipment, err = c.shipmentRepo.Get(selfCtx, &model.ShipmentViewParams{
			ShipmentAlgoID: params.ShipmentID,
			BasedFilter: model.BasedFilter{
				Limit: params.Limit,
				Page:  params.Page,
			},
		})
		if err != nil {
			err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting shipment",
				"id": "Terjadi kesalahan pada saat query getting shipment",
			})
			return nil, err
		}

		l++
		if existShipment != nil || l >= maxLoop {
			break
		}

		// delay before retry select
		delay := l * delayInMs
		time.Sleep(time.Duration(delay) * time.Millisecond)
	}

	if existShipment == nil {
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting shipment",
			"id": "Terjadi kesalahan pada saat query getting shipment",
		})
		return nil, err
	}

	return existShipment, nil
}

func getShipmentIDAndCodHandling(params *stt.ViewDetailShipmentAlgoRequest, data *model.ShipmentAlgo) (string, string) {
	// Determine shipmentID
	shipmentID := params.ShipmentID
	if shipmentID == "" {
		shipmentID = data.ShipmentID
	}

	// Determine algoCodHandling
	algoCodHandling := ""
	for _, packet := range data.Packets {
		if packet.CodHandling != "" {
			algoCodHandling = strings.TrimSpace(strings.ToLower(packet.CodHandling))
			break
		}
	}

	return shipmentID, algoCodHandling
}

func (c *sttCtx) createShipmentByGroupBookingID(ctx context.Context, params *stt.ViewDetailShipmentAlgoRequest, data *model.ShipmentAlgo) ([]model.Shipment, error) {
	var (
		opName               = "Usecases-createShipmentByGroupBookingID"
		trace                = tracer.StartTrace(ctx, opName)
		selfCtx              = trace.Context()
		shipmentGroupBooking = make([]model.Shipment, 0)
		err                  error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{
			"param": map[string]interface{}{
				"params": params,
				"data":   data,
			},
			"result": shipmentGroupBooking,
			"error":  err,
		})
	}()

	/** build payload for insert shipment (START)*/
	for _, v := range data.Packets {
		// get shipment
		existShipment, err := c.shipmentRepo.GetShipment(selfCtx, &model.ShipmentViewParams{ShipmentAlgoID: v.ShipmentID})
		if err != nil {
			err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting shipment",
				"id": "Terjadi kesalahan pada saat query getting shipment",
			})
			return nil, err
		}

		// skip if shipment already exist
		if existShipment != nil {
			// update shipment group booking id if value empty
			if existShipment.ShipmentGroupBookingID == `` {
				existShipment.ShipmentGroupBookingID = params.ShipmentID // value shipment group booking id from query params
				existShipment.ShipmentUpdatedAt = params.Now

				// Update shipment group booking id
				if err := c.shipmentRepo.UpdateShipmentGroupBookingID(selfCtx, existShipment); err != nil {
					err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Failed updated group booking id",
						"id": "Gagal merubah group booking id",
					})
					return nil, err
				}
			}
			continue
		}

		shipmentPackets := make([]model.ShipmentPacket, 0)

		// build shipment
		shipmentPacket, err := c.buildShipmentPacket(selfCtx, v.ShipmentID, &v)
		if err != nil {
			return nil, err
		}

		shipmentPackets = append(shipmentPackets, *shipmentPacket)

		/** build payload for insert shipment (END)*/
		shipment := populateShipmentDataCreateShipmentByGroupBookingID(paramPopulateCreateShipmentByGroupID{
			DataShipment:    v,
			Data:            data,
			ShipmentPackets: shipmentPackets,
			Req:             params,
		})

		c.handlePrioritySubscription(shared.GetPrefixShipmentID(v.ShipmentID), data.Packets, shipment)
		shipmentGroupBooking = append(shipmentGroupBooking, *shipment)
	}

	if len(shipmentGroupBooking) > 0 {
		if err = c.shipmentRepo.CreateBulk(selfCtx, shipmentGroupBooking); err != nil {
			err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while saving shipment",
				"id": "Terjadi kesalahan pada saat query saving shipment",
			})
			return nil, err
		}
	}

	maxLoop := c.cfg.GetMaxLoopRetrySelect()
	delayInMs := c.cfg.RetrySelectDelayInMs()
	l := 0
	for {
		shipmentGroupBooking, err = c.shipmentRepo.Select(selfCtx, &model.ShipmentViewParams{
			ShipmentGroupBookingID: params.ShipmentID,
			BasedFilter: model.BasedFilter{
				Limit: params.Limit,
				Page:  params.Page,
			},
		})
		if err != nil {
			err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting shipment",
				"id": "Terjadi kesalahan pada saat query getting shipment",
			})
			return nil, err
		}

		l++
		if len(shipmentGroupBooking) > 0 || l >= maxLoop {
			break
		}

		// delay before retry select
		delay := l * delayInMs
		time.Sleep(time.Duration(delay) * time.Millisecond)
	}

	return shipmentGroupBooking, nil
}

func (c *sttCtx) buildShipmentPacket(ctx context.Context, shipmentID string, data *model.Packet) (*model.ShipmentPacket, error) {
	var (
		opName         = "Usecases-buildShipmentPacket"
		trace          = tracer.StartTrace(ctx, opName)
		selfCtx        = trace.Context()
		shipmentPacket = new(model.ShipmentPacket)
		now            = c.timeRepo.Now(time.Now())
		err            error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{
			"param": map[string]interface{}{
				"shipmentID": shipmentID,
				"data":       data,
			},
			"result": shipmentPacket,
			"error":  err})
	}()

	byteSender, err := json.Marshal(data.Sender)
	if err != nil {
		logger.E(err)
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while Marshaling algo data",
			"id": "Terjadi kesalahan pada saat Marshaling algo data",
		})
		return nil, err
	}

	data.Recipient.Label = data.GetShipmentRecipientAddressType()
	byteRecipient, err := json.Marshal(data.Recipient)
	if err != nil {
		logger.E(err)
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while Marshaling algo data",
			"id": "Terjadi kesalahan pada saat Marshaling algo data",
		})
		return nil, err
	}

	// get prefix shipment
	prefix := shared.GetPrefixShipmentID(shipmentID)

	// modulus
	newItem := make([]model.Items, 0)
	if len(data.Items) > 0 {
		item := data.Items[0]
		totalWeight := item.Weight / 1000 // should in kg
		weightPerPiece := totalWeight / float64(data.NoOfPieces)

		if weightPerPiece > model.MAX_GROSS_WEIGHT_PER_PIECE {
			remainder := 0.0
			for i := 0; i < model.MAX_PIECE_NUMBER; i++ {
				remainder = totalWeight - model.MAX_GROSS_WEIGHT_PER_PIECE
				if remainder > 0 {
					newItem = append(newItem, model.Items{
						Weight: model.MAX_GROSS_WEIGHT_PER_PIECE,
					})
					totalWeight = remainder
				} else {
					newItem = append(newItem, model.Items{
						Weight: totalWeight,
					})
					break
				}
			}

			// check if remainder more than > 0, add the rest to last index
			if remainder > 0 && len(newItem) > 0 {
				newItem[len(newItem)-1].Weight = newItem[len(newItem)-1].Weight + remainder
			}

		} else {
			for i := 0; i < int(data.NoOfPieces); i++ {
				newItem = append(newItem, model.Items{
					Weight: weightPerPiece,
				})
			}
		}

		// if shipment tokopedia / bukalapak / C1, items always as packet sent. Should not modulo
		if model.MappingShipmentPrefixCustomerName[prefix] == model.BUKALAPAK || model.MappingShipmentPrefixCustomerName[prefix] == model.TOKOPEDIA ||
			(prefix == model.C1 || prefix == model.C2) {
			newItem = make([]model.Items, 0)
			for _, i := range data.Items {
				weight := i.Weight / 1000 // should in kg
				item := model.Items{
					Name:   i.Name,
					Weight: weight,
					Height: shared.DefaultDimension(i.Height),
					Length: shared.DefaultDimension(i.Length),
					Width:  shared.DefaultDimension(i.Width),
				}

				if prefix == model.B1 || prefix == model.B2 || prefix == model.T1 {
					item.Name = i.Name
				}

				newItem = append(newItem, item)
			}

		} else {
			newItem = c.updateDimensionItems(item, newItem)
		}
	}

	byteItems, err := json.Marshal(newItem)
	if err != nil {
		logger.E(err)
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while Marshaling algo data",
			"id": "Terjadi kesalahan pada saat Marshaling algo data",
		})
		return nil, err
	}

	data.NoOfPieces = int64(len(newItem))

	//Check if ACA/ACB isCOD must true and COD Amount cannot empty
	if prefix == model.ACA || prefix == model.ACB {
		if !data.IsCod {
			err = shared.NewMultiStringValidationError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Status COD cannot false",
				"id": "Status COD tidak boleh false",
			})
			return nil, err
		}
		if data.CodValue < 1 {
			err = shared.NewMultiStringValidationError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "COD Amount cannot empty",
				"id": "COD Amount tidak boleh kosong",
			})
			return nil, err
		}
		if data.CodFee < 1 {
			err = shared.NewMultiStringValidationError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "COD Fee cannot empty",
				"id": "COD Fee tidak boleh kosong",
			})
			return nil, err
		}
	}

	shipmentPacket = &model.ShipmentPacket{
		ShipmentPacketSender:             string(byteSender),
		ShipmentPacketRecipient:          string(byteRecipient),
		ShipmentPacketProduct:            data.Product,
		ShipmentPacketServiceType:        data.ServiceType,
		ShipmentPacketCommodity:          data.Commodity,
		ShipmentPacketIsCod:              data.IsCod,
		ShipmentPacketIsDfod:             data.IsDfod,
		ShipmentPacketIsWoodpacking:      data.IsWoodPacking,
		ShipmentPacketIsInsurance:        data.IsInsurance,
		ShipmentPacketGoodsValue:         data.GoodsValue,
		ShipmentPacketCodValue:           data.CodValue,
		ShipmentPacketCodFee:             data.CodFee,
		ShipmentPacketNoOfPieces:         data.NoOfPieces,
		ShipmentPacketCustomerCode:       data.CustomerCode,
		ShipmentPacketItems:              string(byteItems),
		ShipmentPacketCustomerBranchCode: data.CustomerBranch,
		ShipmentPacketCreatedAt:          now,
		ShipmentPacketUpdatedAt:          now,
		ShipmentPacketTotalTariff:        data.TotalTariff,
	}

	return shipmentPacket, nil
}

func (c *sttCtx) updateDimensionItems(item model.Items, newItems []model.Items) []model.Items {
	if len(newItems) == 1 {
		newItems[0].Weight = shared.RoundFloat(newItems[0].Weight, 3)
		newItems[0].Height = item.Height
		newItems[0].Length = item.Length
		newItems[0].Width = item.Width
		return newItems
	}

	// ((Height*Length*Width)/param no of pieces) ^ (1/3)
	dimension := math.Cbrt((item.Height * item.Length * item.Width) / float64(len(newItems)))
	dimension, _ = strconv.ParseFloat(fmt.Sprintf("%.3f", dimension), 64)

	for i := range newItems {
		newItems[i].Weight = shared.RoundFloat(newItems[i].Weight, 3)
		newItems[i].Height = dimension
		newItems[i].Length = dimension
		newItems[i].Width = dimension
	}

	return newItems
}

func (c *sttCtx) generateSttManual(ctx context.Context, params *stt.RequestGenerateStt) error {

	var (
		opName  = "Usecases-generateSttManual"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		errLog  error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": nil, "error": errLog})
	}()

	tempShipmentID := params.SttCreate.Stt.SttShipmentID

	params.SttCreate.Stt.SttSource = model.MANUAL
	params.SttCreate.Stt.SttShipmentID = ""
	if len(tempShipmentID) >= 2 && shared.GetPrefixShipmentID(tempShipmentID) == model.CP {
		params.SttCreate.Stt.SttShipmentID = tempShipmentID
	}

	params.SttCreate.Stt.SttBilledTo = params.ReferenceName
	params.SttCreate.Stt.SttBookedForID = params.BookedForActor.ID
	params.SttCreate.Stt.SttBookedForName = params.BookedForActor.Name
	params.SttCreate.Stt.SttBookedForCode = params.BookedForActor.Code

	var sttType string
	if params.SttRequest.AccountType == model.PARTNER {
		params.SttCreate.Stt.SttPosID = params.SttRequest.AccountRefID
		params.SttData.IsMixpack = false
		params.SttCreate.Stt.SttIsDO = false
		params.SttCreate.Stt.SttBookedForType = model.POS
		sttType = model.SttAutoTypePartner
	}

	if params.SttRequest.AccountType == model.CLIENT {
		params.SttCreate.Stt.SttClientID = params.SttRequest.AccountRefID
		params.SttCreate.Stt.SttBookedForType = model.CLIENT
		sttType = model.SttAutoTypeClient
	}

	if err := c.checkIsAllowCOD(selfCtx, &stt.CheckIsAllowCOD{
		DestinationDistrict: params.DestinationDistrict,
		SttCreate:           params.SttCreate,
		BookedForActor:      params.BookedForActor,
	}); err != nil {
		errLog = err
		return err
	}

	if params.SttData.SttNo != `` {

		/**
		 * check by account reference id and by account type
		 */

		checkSTT, errLog := c.sttManualRepo.Get(selfCtx, &model.SttManualViewParams{
			SttManualID:  params.SttCreate.Stt.SttNo,
			AccountType:  params.SttRequest.AccountType,
			AccountRefID: params.SttRequest.AccountRefID,
			IsMixpack:    params.SttData.IsMixpack,
		})

		if errLog != nil {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while querying db",
				"id": "Terjadi kesalahan pada saat query db",
			})
		}

		if checkSTT == nil {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "STT is not found",
				"id": "STT tidak ditemukan",
			})
		}

		if checkSTT.SttManualStatus == model.SttManualUsed {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "STT is already used",
				"id": "STT sudah terpakai",
			})
		}

		/**
		 * flag when we need to update STT manual to used
		 */

		params.SttCreate.IsSttManual = true

		/**
		 * DO false when client use STT manual
		 */

		params.SttCreate.Stt.SttIsDO = false

		params.SttCreate.SttManual = checkSTT
	}

	if params.SttData.SttNo == `` {
		newTime := c.timeRepo.Now(time.Now()).Add(time.Millisecond * time.Duration(params.Index))
		/**
		 * generate stt by account type
		 */

		sttNo := shared.GenerateSttID(sttType, params.SttData.IsMixpack, false, params.SttCreate.Stt.SttIsDO, &newTime)
		sttNo, errLog = c.checkSttNoDuplication(selfCtx, stt.CheckSttNoDuplicationRequest{
			SttNo: sttNo,
			GenerateSttNoFunc: func() string {
				now := c.timeRepo.Now(time.Time{}).Add(time.Millisecond * time.Duration(params.Index))
				sttNoFunc := shared.GenerateSttID(sttType, params.SttData.IsMixpack, false, params.SttCreate.Stt.SttIsDO, &now)
				return sttNoFunc
			},
		})
		if errLog != nil {
			return errLog
		}
		params.SttCreate.Stt.SttNo = sttNo
	}

	return nil
}

func (c *sttCtx) generateSttShipmentID(ctx context.Context, params *stt.RequestGenerateStt) error {
	var (
		opName  = "Usecases-generateSttShipmentID"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		client  = new(model.Client)
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": nil, "error": err})
	}()

	if params.SttRequest.AccountType != model.PARTNER {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid Account Type format",
			"id": "Format Account Type tidak valid",
		})
	}

	if params.SttCreate.Stt.SttShipmentID == `` || len(params.SttCreate.Stt.SttShipmentID) < 2 {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "ShipmentID is required to be filled correctly",
			"id": "ShipmentID harus di isi dengan benar",
		})
	}

	shipment, err := c.shipmentRepo.GetShipment(selfCtx, &model.ShipmentViewParams{
		ShipmentAlgoID: params.SttCreate.Stt.SttShipmentID,
	})
	if err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while querying db",
			"id": "Terjadi kesalahan pada saat query db",
		})
	}

	if shipment == nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Shipment is not found",
			"id": "Shipment tidak ditemukan",
		})
	}

	// check is cancel true
	if shipment.IsCancelShipment {
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Shipment was canceled",
			"id": "Shipment telah dibatalkan",
		})
		return err
	}

	// check shipment belongs to pos if pickup fav (AP) dan dropoff fav (AS)
	if err := c.checkAgentCode(selfCtx, &stt.CheckAgentCodeRequest{
		ShipmentID:  params.SttCreate.Stt.SttShipmentID,
		PartnerID:   params.SttRequest.AccountRefID,
		PartnerType: params.SttRequest.AccountRefType,
		AccountType: params.SttRequest.AccountType,
		Token:       params.SttRequest.Token,
		AgentCode:   shipment.ShipmentAlgoAgentCode,
	}); err != nil {
		return err
	}

	prefix := shared.GetPrefixShipmentID(params.SttCreate.Stt.SttShipmentID)
	maxLimitStt := model.MappingShipmentPrefixMaxStt[prefix]

	if prefix == model.DO && params.SttCreate.Stt.SttNoRefExternal == `` {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Number Ref External is required",
			"id": "Nomor Ref External harus diisi",
		})
	}

	// prefix ACA dan ACB
	if prefix == model.ACA || prefix == model.ACB {
		// IsCod should be true
		if !params.SttCreate.Stt.SttIsCOD {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Shipment ACA or ACB required COD amount and IsCOD should be true",
				"id": "Shipment ACA atau ACB membutuhkan COD amount dan IsCod True",
			})
		}

		// Stt cod amount and cod fee should be more than 0
		if params.SttData.SttCODAmount < 1 || params.SttData.SttCODFee < 1 {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "COD Amount and COD Fee cannot be empty",
				"id": "COD amount dan COD fee tidak boleh kosong",
			})
		}

		// assign value cod amount and cod fee
		params.SttCreate.Stt.SttCODAmount = params.SttData.SttCODAmount
		params.SttCreate.Stt.SttCODFee = params.SttData.SttCODFee
		params.SttCreate.Stt.SttTotalAmount += params.SttData.SttCODFee
	}

	if params.SttRequest.ShipmentPackageID < 0 || (params.SttRequest.ShipmentPackageID < 1 && maxLimitStt < 100) {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Shipment Package ID is required to be filled correctly",
			"id": "Shipment Package ID harus di isi dengan benar",
		})
	}

	errShipmentUsed := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "ShipmentID is already used",
		"id": "ShipmentID sudah terpakai",
	})

	errQuery := func(table string) error {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while querying db",
			"id": "Terjadi kesalahan pada saat query db",
		})
	}

	checkStt := &model.SttViewParams{
		ShipmentID: params.SttCreate.Stt.SttShipmentID,
		NoLimit:    true,
	}
	shipmentStts, err := c.sttRepo.Select(selfCtx, checkStt)
	if err != nil {
		return errQuery("STT")
	}

	if maxLimitStt > 0 && len(shipmentStts) == maxLimitStt {
		return errShipmentUsed
	}

	if maxLimitStt == 0 {
		shipmentPacket, err := c.shipmentPacketRepo.Select(selfCtx, &model.ShipmentPacketViewParams{
			ShipmentPacketShipmentID: shipment.ShipmentID,
		})

		if err != nil {
			return errQuery("Shipment Packet")
		}

		if len(shipmentStts) == len(shipmentPacket) {
			return errShipmentUsed
		}

	}

	if params.SttRequest.ShipmentPackageID > 0 {
		shipmentPacket, err := c.shipmentPacketRepo.Get(selfCtx, &model.ShipmentPacketViewParams{
			ShipmentPacketShipmentID: shipment.ShipmentID,
			ShipmentPacketID:         params.SttRequest.ShipmentPackageID,
			SttEmpty:                 true,
		})

		if err != nil {
			return errQuery("Shipment Packet")
		}

		if shipmentPacket == nil {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Shipment Package is not found",
				"id": "Shipment Package tidak ditemukan",
			})
		}

		if len(shipmentPacket.ShipmentPacketItems) > 0 {
			var items model.SttMeta
			err := json.Unmarshal([]byte(shipmentPacket.ShipmentPacketItems), &items)
			if err == nil && len(items.GoodsNames) > 0 {
				metaStt := params.SttCreate.Stt.SttMetaToStruct()

				if metaStt == nil {
					metaStt = &model.SttMeta{}
				}

				metaStt.GoodsNames = items.GoodsNames
				params.SttCreate.Stt.SttMeta = metaStt.ToString()
			}
		}
	}

	params.SttCreate.Stt.SttBilledTo = model.MappingShipmentPrefixBilledTo[prefix]
	if params.SttCreate.Stt.SttBilledTo == `` {
		params.SttCreate.Stt.SttBilledTo = model.CUSTOMERAPPS
	}

	params.SttCreate.Stt.SttSource = model.CA
	switch params.SttCreate.Stt.SttBilledTo {
	case model.CUSTOMERAPPS:
		// used model.CA `customer-apps`
	case model.CUSTOMERAPPS_DORETURN_CLIENT_CODE:
		// used model.CA `customer-apps`
	case model.CUSTOMERAPPS_CODRETURN_CLIENT_CODE:
		// used model.CA `customer-apps`
	case model.CLIENT_CODRETURN_CLIENT_CODE:
		// used model.CA `customer-apps`
	case model.COD_CA_RETAIL_CODE:
		// used model.CA `customer-apps`
	case model.COD_CA_RETAIL_DROPOFF_CODE:
		// used model.CA `customer-apps`
	default:
		params.SttCreate.Stt.SttSource = model.MP
	}

	// Get Client TOKPED, BL, or CA
	if shared.GetPrefixShipmentID(params.SttData.SttShipmentID) == model.C1 || shared.GetPrefixShipmentID(params.SttData.SttShipmentID) == model.C2 {
		shipment, err := c.shipmentRepo.Get(selfCtx, &model.ShipmentViewParams{
			ShipmentAlgoID: params.SttData.SttShipmentID,
		})
		if err != nil || shipment == nil {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Shipment Not Found",
				"id": "Shipment Tidak Ditemukan",
			})
		}

		if len(shipment.ShipmentPackets) > 0 {
			spcBranchCode := shipment.ShipmentPackets[0].ShipmentPacketCustomerBranchCode
			if spcBranchCode == "" {
				return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Shipment Packet Customer Branch Code cannot be empty",
					"id": "Shipment Packet Customer Branch Code tidak boleh kosong",
				})
			}
			client, err = c.clientRepo.GetByCode(selfCtx, spcBranchCode, params.SttRequest.Token)
			if err != nil || client == nil {
				return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Client Not Found",
					"id": "Client Tidak Ditemukan",
				})
			}

			// validation branch only
			if client.Data.ClientParentID < 1 {
				return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Client is not branch",
					"id": "Client bukan branch",
				})
			}

			// if shipment C1 set source to CA and billed to company name
			params.SttCreate.Stt.SttSource = model.CA
			params.SttCreate.Stt.SttBilledTo = client.Data.ClientCompanyName
		} else {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Shipment Packet Not Found",
				"id": "Shipment Packet Tidak Ditemukan",
			})
		}
	} else {
		clientCode := model.MappingShipmentPrefixClientCode[shared.GetPrefixShipmentID(params.SttCreate.Stt.SttShipmentID)]
		client, err = c.clientRepo.GetByCode(selfCtx, clientCode, params.SttRequest.Token)
		if err != nil || client == nil {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Client Not Found",
				"id": "Client Tidak Ditemukan",
			})
		}
	}

	if err := c.checkIsAllowCOD(selfCtx, &stt.CheckIsAllowCOD{
		DestinationDistrict: params.DestinationDistrict,
		SttCreate:           params.SttCreate,
		BookedForActor: &model.Actor{
			ID:    client.Data.ClientID,
			IsCod: client.Data.ClientIsCOD,
			Name:  client.Data.ClientCompanyName,
			Code:  client.Data.ClientCode,
			Type:  model.CLIENT,
		},
	}); err != nil {
		return err
	}

	params.SttCreate.Stt.SttClientID = client.Data.ClientID
	params.SttCreate.Stt.SttPosID = 0
	params.SttCreate.ShipmentPackageID = params.SttRequest.ShipmentPackageID
	params.SttData.IsMixpack = false
	params.SttCreate.Stt.SttIsDO = false
	params.SttCreate.Stt.SttBookedForID = client.Data.ClientID
	params.SttCreate.Stt.SttBookedForName = client.Data.ClientCompanyName
	params.SttCreate.Stt.SttBookedForCode = client.Data.ClientCode
	params.SttCreate.Stt.SttBookedForType = model.CLIENT

	sttType := model.SttAutoTypeCA
	switch prefix {
	case model.DO:
		sttType = model.SttAutoTypeCADO
		params.SttCreate.Stt.SttIsDO = true
	case model.ACR:
		sttType = model.SttAutoTypeCAACR
	case model.CCR:
		sttType = model.SttAutoTypeClientCCR
	}

	newTime := c.timeRepo.Now(time.Now()).Add(time.Millisecond * time.Duration(params.Index))
	/**
	 * generate stt by account type
	 */
	sttNo := shared.GenerateSttID(sttType, false, false, false, &newTime)
	sttNo, err = c.checkSttNoDuplication(selfCtx, stt.CheckSttNoDuplicationRequest{
		SttNo: sttNo,
		GenerateSttNoFunc: func() string {
			now := c.timeRepo.Now(time.Time{}).Add(time.Millisecond * time.Duration(params.Index))
			sttNoFunc := shared.GenerateSttID(sttType, false, false, false, &now)
			return sttNoFunc
		},
	})

	if err != nil {
		return err
	}
	params.SttCreate.Stt.SttNo = sttNo

	return nil
}

func (c *sttCtx) ValidatePhoneNumber(ctx context.Context, params *stt.ValidatePhoneNumberRequest) (*stt.ValidatePhoneNumberResponse, error) {

	res := new(stt.ValidatePhoneNumberResponse)
	if err := params.Validate(); err != nil {
		return res, err
	}

	prefixShipment := ``
	if len(params.SttShipmentID) > 1 {
		prefixShipment = shared.GetPrefixShipmentID(params.SttShipmentID)
	}
	if prefixShipment != model.AO && strings.EqualFold(params.SttRecipientPhone, params.SttSenderPhone) {
		return res, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Sender and Recepient number cannot be the same",
			"id": "Nomor pengirim dan penerima tidak boleh sama",
		})
	}

	res.IsAllowToUse = true
	return res, nil
}

// checkSaldo ...
func (c *sttCtx) checkSaldo(ctx context.Context, params stt.CheckSaldoParams) error {
	opName := "sttCtx-checkSaldo"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "error": err})
	}()

	// check current saldo only for POS
	if params.AccountRefType != model.POS {
		return nil
	}

	// check current saldo for manual booking and shipment booking prefix AP and AS
	if params.Source == model.ALGO && !model.IsShipmentPrefixFavorite[shared.GetPrefixShipmentID(params.Stt.SttShipmentID)] {
		return nil
	}

	balanceLimit, err := c.balanceLimitRepo.Select(selfCtx, model.BalanceLimitViewParams{
		LimitRuleAppliedTo: params.AccountRefType,
		Limit:              10,
		Page:               1,
		Token:              params.Token,
	})
	if err != nil || balanceLimit == nil || len(balanceLimit.Data) == 0 {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Balance Limit is Not Found",
			"id": "Balance Limit Tidak Ditemukan",
		})
	}

	var limitRuleMinBalance float64
	for _, value := range balanceLimit.Data {
		if value.LimitRuleMetaData.IsCodDelivery && params.IsCODDelivery {
			limitRuleMinBalance = value.LimitRuleMinBalance
			break
		} else if !value.LimitRuleMetaData.IsCodDelivery && !params.IsCODDelivery {
			limitRuleMinBalance = value.LimitRuleMinBalance
			break
		}
	}

	partnerWallet, err := c.walletRepo.Get(selfCtx, model.WalletViewParams{
		Token: params.Token,
	})
	if err != nil || partnerWallet == nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Wallet is Not Found",
			"id": "Wallet Tidak Ditemukan",
		})
	}

	// deduct currect saldo with total tariff
	deductedSaldo := partnerWallet.Data.WalletBalance - params.CheckTariff.TotalTariff

	if deductedSaldo < limitRuleMinBalance {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"error_type": "ERROR_POS_UNDER_LIMIT_BALANCE",
			"balance":    strconv.FormatFloat(partnerWallet.Data.WalletBalance, 'f', -1, 64),
			"tariff":     strconv.FormatFloat(params.CheckTariff.TotalTariffAfterDiscount, 'f', -1, 64),
			"en":         "Unable to book. Saldo is below specified minimum balance",
			"id":         "Tidak bisa booking. Saldo dibawah minimum balance saldo yang ditentukan",
		})
	}

	return nil
}

// ViewSttShipmentPrefix ...
func (c *sttCtx) ViewSttShipmentPrefix(ctx context.Context) []stt.ViewSttShipmentPrefixResponse {

	var res []stt.ViewSttShipmentPrefixResponse
	for key, val := range model.MappingShipmentPrefixList {
		res = append(res, stt.ViewSttShipmentPrefixResponse{
			Prefix: key,
			Name:   val,
		})
	}

	return res
}

func (c *sttCtx) checkAgentCode(ctx context.Context, params *stt.CheckAgentCodeRequest) error {
	opName := "sttCtx-checkAgentCode"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var errLog error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "error": errLog})
	}()

	shipmentPrefix := shared.GetPrefixShipmentID(params.ShipmentID)
	if shipmentPrefix == model.AP || shipmentPrefix == model.AS {
		if params.AccountType != model.PARTNER && params.PartnerType != model.POS {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "This shipment only for POS",
				"id": "Shipment id ini hanya untuk POS",
			})
		}

		// get partner
		partner, err := c.partnerRepo.GetByID(selfCtx, params.PartnerID, params.Token)
		if err != nil || partner == nil {
			errLog = err
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Failed Get POS",
				"id": "Gagal Mendapatkan data POS",
			})
		}

		if partner.Data.PartnerExternalCode == `` {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Partner External Code Unmapping",
				"id": "Partner External Code belum di mapping untuk POS ini",
			})
		}

		if partner.Data.PartnerExternalCode != params.AgentCode {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Dont have permission to access this shipment",
				"id": "Tidak diijinkan untuk memproses shipment ini",
			})
		}
	}

	return nil
}

func (c *sttCtx) MigrateAdditionalData(ctx context.Context) error {
	accountToken, err := c.repoAccount.GetTokenGenesis(ctx)
	if err != nil || accountToken == nil {
		logger.Ef(`MigrateAdditionalData Error %s`, err.Error())
		return err
	}

	for i := 1; i <= 2000; i++ {
		offset := (i - 1) * 500
		sttList, err := c.sttRepo.Select(ctx, &model.SttViewParams{
			BasedFilter: model.BasedFilter{
				Limit:  500,
				Offset: offset,
			},
			NoLimit:   false,
			BookedEnd: "2021-09-30 24:00:00",
		})

		if err != nil {
			fmt.Println(i)
			continue
		}

		// sttList ...
		for _, val := range sttList {
			// get origin district
			originDistrictRepo, err := c.districtRepo.GetByCode(ctx, &model.CredentialRestAPI{
				Token:    accountToken.Data.Token,
				ClientID: 0,
			}, val.SttOriginDistrictID)

			if err == nil && originDistrictRepo != nil {
				val.SttOriginDistrictName = originDistrictRepo.Data.Name
				val.SttOriginDistrictUrsaCode = originDistrictRepo.Data.UrsaCode
				if originDistrictRepo.Data.City != nil {
					val.SttOriginCityName = originDistrictRepo.Data.City.Name
				}
			} else {
				val.SttOriginDistrictName = val.SttOriginDistrictID
				val.SttOriginCityName = val.SttOriginCityName
			}

			destinationDistrictRepo, err := c.districtRepo.GetByCode(ctx, &model.CredentialRestAPI{
				Token:    accountToken.Data.Token,
				ClientID: 0,
			}, val.SttDestinationDistrictID)

			if err == nil && destinationDistrictRepo != nil {
				val.SttDestinationDistrictName = destinationDistrictRepo.Data.Name
				val.SttDestinationDistrictUrsaCode = destinationDistrictRepo.Data.UrsaCode
				if destinationDistrictRepo.Data.City != nil {
					val.SttDestinationCityName = destinationDistrictRepo.Data.City.Name
				}
			} else {
				val.SttDestinationDistrictName = val.SttDestinationDistrictID
				val.SttDestinationCityName = val.SttDestinationCityID
			}

			commodity, err := c.commodityRepo.GetCommodityByCode(ctx, val.SttCommodityCode, accountToken.Data.Token)
			if err == nil && destinationDistrictRepo != nil {
				val.SttCommodityName = commodity.Data.CommodityName
				val.SttCommodityHsCode = commodity.Data.HsCode
			} else {
				val.SttCommodityName = val.SttCommodityCode
			}

			if val.SttClientID > 0 {
				client, err := c.clientRepo.GetByID(ctx, val.SttClientID, accountToken.Data.Token)
				if client != nil && err == nil {
					val.SttBookedForCode = client.Data.ClientCode
					val.SttBookedForName = client.Data.ClientCompanyName
					val.SttBookedForType = client.Data.ClientType
					val.SttBookedForID = client.Data.ClientID
				}
			} else {
				partner, err := c.partnerRepo.GetByID(ctx, val.SttBookedBy, accountToken.Data.Token)
				if partner != nil && err == nil {
					val.SttBookedForCode = partner.Data.Code
					val.SttBookedForName = partner.Data.Name
					val.SttBookedForType = partner.Data.Type
					val.SttBookedForID = partner.Data.ID
				}
			}

			if val.SttBookedByType == model.CLIENT {
				client, err := c.clientRepo.GetByID(ctx, val.SttClientID, accountToken.Data.Token)
				if client != nil && err == nil {
					val.SttBookedByCode = client.Data.ClientCode
				}
			} else {
				partner, err := c.partnerRepo.GetByID(ctx, val.SttBookedBy, accountToken.Data.Token)
				if partner != nil && err == nil {
					val.SttBookedByCode = partner.Data.Code
				}
			}

			err = c.sttRepo.UpdateSTTMigration(ctx, &val)
			if err != nil {
				logger.Ef(`MigrateAdditionalData Error %s`, err.Error())
			}
			fmt.Println(val)

			time.Sleep(10 * time.Millisecond)
		}
	}

	return nil
}

func (c *sttCtx) checkIsAllowCOD(ctx context.Context, params *stt.CheckIsAllowCOD) error {

	errCODNotAllowed := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"error_type": "ERROR_COD_NOT_ALLOWED",
		"en":         "Stt cannot be booked. Please check is client or the destination allowed for COD services",
		"id":         "Stt tidak bisa dibooking. Mohon cek apakah client atau destinasi yang dipilih memperbolehkan layanan COD",
	})

	if params.SttCreate.Stt.SttIsCOD && params.DestinationDistrict.Data.IsCod == model.DISTRICT_IS_NOT_COD {
		return errCODNotAllowed
	}

	if !params.BookedForActor.IsCod && params.SttCreate.Stt.SttIsCOD {
		return errCODNotAllowed
	}
	return nil
}

// Editional STT Function Related

// validation for allow Edit and Cancel STT
func (c *sttCtx) IsAllowEditAndCancelStt(ctx context.Context, stt model.SttDetailResult, sttMeta *model.SttMeta) (bool, bool) {
	var isAllowToEdit, isAllowToCancel bool

	if model.IsNotAllowToEditInternal[stt.SttLastStatusID] {
		isAllowToEdit = c.ValidateIsAllowToEditForSttAdjustmentPOD(ctx, stt, sttMeta)
	} else {
		isAllowToEdit = true
	}

	if model.IsAllowToCancelInternal[stt.SttLastStatusID] {
		isAllowToCancel = true
	}

	// handle Cancel STT Intracity at status STI Dest
	if stt.SttLastStatusID == model.STIDEST && stt.SttOriginCityID != stt.SttDestinationCityID {
		isAllowToCancel = false
	}

	return isAllowToEdit, isAllowToCancel
}

// validation for Stt Adjustment POD
func (c *sttCtx) ValidateIsAllowToEditForSttAdjustmentPOD(ctx context.Context, stt model.SttDetailResult, sttMeta *model.SttMeta) bool {
	if model.IsAllowToEditForSttAdjustmentPOD[stt.SttLastStatusID] && stt.SttBookedForType == model.CLIENT {

		if stt.SttIsDFOD {
			return false
		}

		dtpol, err := c.sttTransactionRepo.GetDetail(ctx, &model.SttTransactionViewParams{
			SttNo: stt.SttNo,
		})
		if err != nil {
			logger.Ef(`Failed to get DTPOL data error: %s`, err.Error())
			return false
		}

		if dtpol == nil {
			return false
		}

		if dtpol.StartingDtpolStatus != `` && dtpol.EndingDtpolStatus != `` && sttMeta != nil {
			// Validate Is Allow To Edit For Stt Adjustment Stt Reverse Journey After POD
			if sttMeta.DetailSttReverseJourney != nil {
				if len(sttMeta.DetailSttReverseJourney.ReverseShipmentID) > 0 &&
					model.IsSttStatusReturnToSender[sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt] &&
					!model.MappingShipmentPrefixCustomerApps[shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.ReverseShipmentID)] {
					return true
				}
				if sttMeta.DetailSttReverseJourney.ReverseShipmentID == `` {
					return true
				}
			}
			if sttMeta.DetailSttReverseJourney == nil {
				if len(stt.SttShipmentID) > 0 && !model.MappingShipmentPrefixCustomerApps[shared.GetPrefixShipmentID(stt.SttShipmentID)] {
					return true
				}
				if stt.SttShipmentID == `` {
					return true
				}
			}
		}

	}

	return false
}

func (c *sttCtx) checkSttNoDuplication(ctx context.Context, params stt.CheckSttNoDuplicationRequest) (string, error) {
	opName := "sttCtx-checkSttNoDuplication"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	sttNo := params.SttNo

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": sttNo})
	}()

	if !c.cfg.EnableCheckSttNoDuplicationRetry() {
		return sttNo, nil
	}

	for i := 0; i < c.cfg.CheckSttNoDuplicationRetry(); i++ {
		if ok := c.sttRepo.CreateCacheOnce(selfCtx, fmt.Sprintf("%s:%s", model.CacheSttNoDuplicateCheck, sttNo), model.CacheSttNoDuplicateCheckExpired); ok {

			stt, err := c.sttRepo.Get(selfCtx, &model.SttViewDetailParams{
				Stt: model.Stt{
					SttNo: sttNo,
				},
			})

			if err == nil && stt == nil {
				return sttNo, nil
			}
		}

		sttNo = params.GenerateSttNoFunc()
	}

	return "", shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "Failed to create STT Booking, Please resubmit again",
		"id": "Gagal membuat STT Booking, Tolong dikirim ulang",
	})
}

func (c *sttCtx) CalculateRetailTariff(ctx context.Context, params *stt.CalculateRetailTariffRequest) error {
	opName := "UsecaseStt-CalculateRetailTariff"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var (
		result string
		errLog error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		trace.Finish(map[string]interface{}{"param": nil, "result": result, "error": errLog})
	}()

	bMessage, _ := json.Marshal(params)
	sMessage := string(bMessage)

	/* pulish message to pubsub */
	err := c.gatewaySttUc.PublishCalculateRetailTariff(selfCtx, &gateway_stt.PubsubCalculateRetailTariffRequest{
		Message:  sMessage,
		OrderKey: params.SttNo,
		SttNo:    params.SttNo,
	})
	if err != nil {
		// if any error insert into table retry_pubsub
		err := c.retryPubsubRepo.Create(selfCtx, &model.RetryPubSub{
			RpPayload:       sMessage,
			RpSttNo:         params.SttNo,
			RpType:          stt.CalculateRetailTariff,
			RpPubsubTopicID: c.cfg.HydraCalculateRetailTariffTopicID(),
			RpCreatedAt:     c.timeRepo.Now(time.Time{}),
		})
		if err != nil {
			errLog = err
			return nil
		}
		return nil
	}

	return nil
}

func (c *sttCtx) generateReverseJourneyBookedFor(ctx context.Context, params *stt.CreateStt, additionalParams interface{}) (*stt.BookedActorDetail, error) {
	opName := "UsecaseStt-CreateSTTReverseJourney-generateReverseJourneyBookedFor"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res := stt.BookedActorDetail{}
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": errors})
	}()

	errInvalidAcountType := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "Invalid Account Type format",
		"id": "Format Account Type tidak valid",
	})

	errClientNotFound := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "Client is not found",
		"id": "Client tidak ditemukan",
	})

	switch params.BookedFor.Type {
	case model.CLIENT:
		bookedForClient, err := c.clientRepo.GetByID(selfCtx, params.BookedFor.ID, params.Token)
		if err != nil || bookedForClient == nil {
			errors = err
			return nil, errClientNotFound
		}

		if strings.ToLower(bookedForClient.Data.ClientStatusClient) != model.APPROVED {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Client is not aproved",
				"id": "Client tidak di aprove",
			})
		}

		res.ID = bookedForClient.Data.ClientID
		res.Name = bookedForClient.Data.ClientCompanyName
		res.ExternalCode = bookedForClient.Data.ClientElexysCode
		res.Type = model.CLIENT
		res.Code = bookedForClient.Data.ClientCode

		sharedDetail, ok := additionalParams.(stt.SharedDetailParams)
		if !ok {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to parse stt reverse journey",
				"id": "Gagal memparsing stt reverse journey",
			})
		}

		sttMeta := sharedDetail.Stt.SttMetaToStruct()
		if sttMeta == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to parse stt meta reverse journey",
				"id": "Gagal memparsing stt meta reverse journey",
			})
		}

		shipmentPrefix := ``
		if sharedDetail.Stt.SttLastStatusID == model.CODREJ &&
			((sharedDetail.Stt.SttShipmentID != `` && model.IsShipmentFavorite[shared.GetPrefixShipmentID(sharedDetail.Stt.SttShipmentID)]) ||
				(sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` &&
					sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt == model.REROUTE &&
					model.IsShipmentFavorite[shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID)]) ||
				(sharedDetail.Stt.SttShipmentID != `` && model.MappingShipmentCODValid[shared.GetPrefixShipmentID(sharedDetail.Stt.SttShipmentID)]) ||
				(sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` &&
					sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt == model.REROUTE &&
					model.MappingShipmentCODValid[shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID)])) {
			shipmentPrefix = model.ACR

			if shipmentPrefix == `` {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Client Not Found",
					"id": "Client Tidak Ditemukan",
				})
			}

			clientCode := model.MappingShipmentPrefixClientCode[shipmentPrefix]
			client, err := c.clientRepo.GetByCode(selfCtx, clientCode, params.Token)
			if err != nil || client == nil {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Client Not Found",
					"id": "Client Tidak Ditemukan",
				})
			}

			res.ID = client.Data.ClientID
			res.Name = client.Data.ClientCompanyName
			res.ExternalCode = client.Data.ClientElexysCode
			res.Type = model.CLIENT
			res.Code = client.Data.ClientCode

		}
	case model.POS:
		bookedForPartner, err := c.partnerRepo.GetByID(selfCtx, params.BookedFor.ID, params.Token)
		if err != nil || bookedForPartner == nil {
			errors = err
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner is not found",
				"id": "Partner tidak ditemukan",
			})
		}

		if bookedForPartner.Data.PartnerLocation == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner is not yet mapped with location",
				"id": "Partner belum dipetakan dengan lokasi",
			})
		}

		res.ID = bookedForPartner.Data.ID
		res.Name = bookedForPartner.Data.Name
		res.ExternalCode = bookedForPartner.Data.PartnerExternalCode
		res.Code = bookedForPartner.Data.Code
		res.Type = model.POS
	default:
		return nil, errInvalidAcountType
	}

	return &res, nil
}

func (c *sttCtx) generateReverseJourneyBookedBy(ctx context.Context, params *stt.CreateStt, additionalParams interface{}) (*stt.BookedActorDetail, error) {
	opName := "UsecaseStt-CreateSTTReverseJourney-generateReverseJourneyBookedBy"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res := stt.BookedActorDetail{}
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": errors})
	}()

	errInvalidAcountType := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "Invalid Account Type format",
		"id": "Format Account Type tidak valid",
	})

	switch params.AccountType {
	case model.PARTNER:
		if params.AccountRefType != model.POS {
			return nil, errInvalidAcountType
		}

		partnerBooking, err := c.partnerRepo.GetByID(selfCtx, params.AccountRefID, params.Token)
		if err != nil || partnerBooking == nil {
			errors = err
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner is not found",
				"id": "Partner tidak ditemukan",
			})
		}

		if partnerBooking.Data.PartnerLocation == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner is not yet mapped with location",
				"id": "Partner belum dipetakan dengan lokasi",
			})
		}

		res.DistrictCode = partnerBooking.Data.PartnerLocation.DistrictCode
		res.CityCode = partnerBooking.Data.PartnerLocation.CityCode
		res.ExternalCode = partnerBooking.Data.PartnerExternalCode
		res.ExternalCode = partnerBooking.Data.PartnerExternalCode
		res.IsCODDelivery = partnerBooking.Data.PartnerIsCODDelivery

		res.ID = params.AccountRefID
		res.Name = params.AccountRefName
		res.Type = params.AccountRefType
		res.Code = partnerBooking.Data.Code
	default:
		return nil, errInvalidAcountType
	}

	return &res, nil
}

func (c *sttCtx) generateCommodity(ctx context.Context, params *stt.CreateStt, additionalParams interface{}) (*model.Commodity, error) {

	opName := "UsecaseStt-CreateSTT-generateCommodity"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	commodity := new(model.Commodity)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": commodity, "error": errors})
	}()

	commodity, errors = c.commodityRepo.GetCommodityByCode(selfCtx, params.Stt.SttCommodityCode, params.Token)
	if errors != nil || commodity == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Commodity is not found",
			"id": "Commodity tidak ditemukan",
		})
	}

	if commodity.Data.CommodityStatus != model.ACTIVE {
		errors = shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Commodity is inactive",
			"id": "Commodity tidak aktif",
		})
		return nil, errors
	}

	return commodity, nil
}

func (c *sttCtx) generateProductType(ctx context.Context, params *stt.CreateStt, additionalParams interface{}) (*model.ProductType, error) {

	opName := "UsecaseStt-CreateSTT-generateProductType"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	productType := new(model.ProductType)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": productType, "error": errors})
	}()

	productType, errors = c.productRepo.GetProductTypes(selfCtx, &model.ListProductTypeRequest{
		Token:  params.Token,
		Code:   params.Stt.SttProductType,
		Status: model.ACTIVE,
		Limit:  model.DefaultLimit,
		Page:   model.DefaultPage,
	})
	if errors != nil || productType == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Product Type is not found",
			"id": "Product Type tidak ditemukan",
		})
	}

	if len(productType.Data) < 1 {
		return nil, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Product Type is inactive",
			"id": "Product Type tidak aktif",
		})
	}

	return productType, nil
}

func (c *sttCtx) generateOrigin(ctx context.Context, params *stt.CreateStt, additionalParams interface{}) (*stt.LocationDetail, error) {
	opName := "UsecaseStt-CreateSTT-generateOrigin"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res := new(stt.LocationDetail)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": errors})
	}()

	districtOrigin, err := c.districtRepo.GetByCode(selfCtx, &model.CredentialRestAPI{
		Token: params.Token,
	}, params.Stt.SttOriginDistrictID)

	if err != nil || districtOrigin == nil || districtOrigin.Data.City == nil {
		errors = err
		return nil, stt.ErrDistrictCity("Origin")
	}

	if strings.ToLower(districtOrigin.Data.Status) != model.ACTIVE {
		return nil, stt.ErrDistrictCityInactive("Origin")
	}

	cityOrigin, err := c.cityRepo.Get(selfCtx, districtOrigin.Data.City.Code, params.Token)
	if err != nil || cityOrigin == nil {
		errors = err
		return nil, stt.ErrCity("Origin")
	}

	if strings.ToLower(cityOrigin.IsActive) != model.ACTIVE {
		return nil, stt.ErrCityInactive("Origin")
	}

	res.District = districtOrigin
	res.City = cityOrigin

	return res, nil
}

func (c *sttCtx) generateDestination(ctx context.Context, params *stt.CreateStt, additionalParams interface{}) (*stt.LocationDetail, error) {
	opName := "UsecaseStt-CreateSTT-generateDestination"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res := new(stt.LocationDetail)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": errors})
	}()

	districtDestination, err := c.districtRepo.GetByCode(selfCtx, &model.CredentialRestAPI{
		Token: params.Token,
	}, params.Stt.SttDestinationDistrictID)

	if err != nil || districtDestination == nil || districtDestination.Data.City == nil {
		errors = err
		return nil, stt.ErrDistrictCity("Destination")
	}

	if strings.ToLower(districtDestination.Data.Status) != model.ACTIVE {
		return nil, stt.ErrDistrictCityInactive("Destination")
	}

	sttVendor := (districtDestination.Data.Type == model.TYPE_VENDOR || districtDestination.Data.Type == model.TYPE_VENDOR_LANJUTAN)
	sttVendorNinjaOrPI := districtDestination.Data.VendorCode == model.TypeVendorNINJA || districtDestination.Data.VendorCode == model.TypeVendorPTPOS
	sttVendorNotAllowPiecesMoreThan1 := sttVendor && sttVendorNinjaOrPI && len(params.Stt.SttPieces) > 1
	if sttVendorNotAllowPiecesMoreThan1 {
		return nil, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "District destination type doesn't support delivery item that have more than 1 koli wihtin 1 STT",
			"id": "Tipe district destinasi tidak mendukung pengiriman barang yang memiliki lebih dari 1 koli dalam satu STT",
		})
	}

	cityDestination, err := c.cityRepo.Get(selfCtx, districtDestination.Data.City.Code, params.Token)
	if err != nil || cityDestination == nil {
		errors = err
		return nil, stt.ErrCity("Destination")
	}

	if strings.ToLower(cityDestination.IsActive) != model.ACTIVE {
		return nil, stt.ErrCityInactive("Destination")
	}

	res.District = districtDestination
	res.City = cityDestination

	return res, nil
}

func (c *sttCtx) generateConfigurablePrice(ctx context.Context, params *stt.CreateStt, additionalParams interface{}) (*model.ConfigurablePrice, error) {

	opName := "UsecaseStt-CreateSTT-generateConfigurablePrice"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	configPrices := new(model.ConfigurablePrice)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": configPrices, "error": errors})
	}()

	configPrices, errors = c.configurablePriceRepo.Select(selfCtx, &model.ConfigurablePriceViewParams{
		ConfigurablePriceStatus: model.ACTIVE,
		Token:                   params.Token,
	})

	errInsuranceInactive := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
		"en": "Insurance is inactive",
		"id": "Insurance tidak aktif",
	})

	if errors != nil || configPrices == nil || len(configPrices.Data) == 0 {
		return nil, errInsuranceInactive
	}

	isInsuraceActive := false
	for _, configPrice := range configPrices.Data {
		if configPrice.ConfigurablePriceType == model.INSURANCE {
			isInsuraceActive = true
			break
		}
	}

	if !isInsuraceActive {
		return nil, errInsuranceInactive
	}

	return configPrices, nil
}

func (c *sttCtx) isNewMisbookingCalculation(params *stt.CreateStt, sharedParams stt.SharedDetailParams, sttMeta *model.SttMeta) bool {
	// MISBOOKING - REROUTE check prev stt status is MISBOOKING
	isMisbooking := sharedParams.Stt.SttLastStatusID == model.MISBOOKING
	// MISBOOKING - REROUTE check target new stt status is REROUTE
	isReroute := sharedParams.ReverseJourneyStatus == model.REROUTE
	// MISBOOKING - REROUTE check Product TYPE
	isCorrectProduct := params.Stt.SttProductType == model.REGPACK || params.Stt.SttProductType == model.JAGOPACK
	// MISBOOKING BY CONSOLE
	isMisbookingByConsole := sharedParams.Stt.SttUpdatedActorRole.String == model.CONSOLE &&
		(sttMeta.ReverseDestination != nil && !sttMeta.ReverseDestination.IsMisbookingByInternal)
	// MISBOOKING STT Source is MANUAL only
	isRetailOnly := sharedParams.Stt.SttSource == model.MANUAL

	return isMisbooking && isReroute && isCorrectProduct && isMisbookingByConsole && isRetailOnly
}

type paramGenerateReverseJourneyTariffMisBooking struct {
	paramsCreateStt *stt.CreateStt
	sharedParams    stt.SharedDetailParams
	sttMeta         *model.SttMeta
	reqCheckTariff  *stt.CalculateTariffParams
	checkTariff     *model.CheckTariffResponse
}

func (c *sttCtx) generateReverseJourneyTariffMisBooking(ctx context.Context, params paramGenerateReverseJourneyTariffMisBooking) (*model.CheckTariffResponse, error) {
	var (
		newAmount              float64
		checkTariffExpected    = new(model.CheckTariffResponse)
		errors                 error
		reqCheckTariffExpected stt.CalculateTariffParams
	)

	rootStt := params.sharedParams.Stt
	tariffMisBooking := params.sttMeta.RetailTariff.TotalTariffAfterDiscount - params.sttMeta.RetailTariff.CodFeeAfterDiscount

	err := copier.Copy(&reqCheckTariffExpected, params.reqCheckTariff)
	if err != nil {
		return nil, errors
	}
	reqCheckTariffExpected.RequestCalculateTariff.OriginID = rootStt.SttOriginDistrictID
	reqCheckTariffExpected.RequestCalculateTariff.DestinationID = params.paramsCreateStt.Stt.SttDestinationDistrictID
	checkTariffExpected, errors = c.checkTariffRepo.TariffCalculation(ctx, &reqCheckTariffExpected)
	if errors != nil || checkTariffExpected == nil {
		return nil, errors
	}

	newAmount = checkTariffExpected.Data.TotalTariff - tariffMisBooking
	params.checkTariff.Data.TotalTariff = newAmount
	params.checkTariff.Data.TotalTariffAfterDiscount = newAmount
	params.checkTariff.Data.InsuranceName = checkTariffExpected.Data.InsuranceName
	params.checkTariff.Data.InsuranceAdminFee = checkTariffExpected.Data.InsuranceAdminFee
	params.checkTariff.Data.InsuranceRates = checkTariffExpected.Data.InsuranceRates
	params.checkTariff.Data.InsuranceRatesAfterDiscount = checkTariffExpected.Data.InsuranceRatesAfterDiscount
	params.checkTariff.Data.RoundingDiff = checkTariffExpected.Data.RoundingDiff
	params.checkTariff.Data.HeavyWeightSurchargeRemarks = checkTariffExpected.Data.HeavyWeightSurchargeRemarks
	params.checkTariff.Data.EstimateSLA = checkTariffExpected.Data.EstimateSLA
	params.checkTariff.Data.Currency = checkTariffExpected.Data.Currency
	params.checkTariff.Data.ChargeableWeight = checkTariffExpected.Data.ChargeableWeight
	params.checkTariff.Data.GrossWeight = checkTariffExpected.Data.GrossWeight
	params.checkTariff.Data.VolumeWeight = checkTariffExpected.Data.VolumeWeight
	params.checkTariff.Data.VolumeWeightDiscount = checkTariffExpected.Data.VolumeWeightDiscount
	if newAmount < 1 {
		params.checkTariff.Data.TotalTariff = 0
		params.checkTariff.Data.TotalTariffAfterDiscount = 0
	}

	//Add Partner Log
	go c.partnerLog.Insert(context.Background(), &model.PartnerLog{
		Action:  "hydra-check-tariff-expected-misbooking",
		RefID:   params.sharedParams.Stt.SttNo,
		Request: params,
		Response: map[string]interface{}{
			"tariff_misbooking":     tariffMisBooking,
			"check_tariff_expected": checkTariffExpected,
			"new_amount":            newAmount,
		},
	})
	return params.checkTariff, errors
}

func (c *sttCtx) generateReverseJourneyTariffCalculation(ctx context.Context, params paramGenerateReverseJourneyTariffMisBooking) (checkTariff *model.CheckTariffResponse, errors error) {
	bookedForType := ""
	if params.paramsCreateStt != nil {
		bookedForType = params.paramsCreateStt.BookedFor.Type
	}

	if c.isNewMisbookingCalculation(params.paramsCreateStt, params.sharedParams, params.sttMeta) && bookedForType != model.CLIENT {
		checkTariff, errors = c.generateReverseJourneyTariffMisBooking(ctx, params)
	} else {
		checkTariff, errors = c.checkTariffRepo.TariffCalculation(ctx, params.reqCheckTariff)
	}
	return
}

type respGenerateReverseJourneyTariffValidateSharedAndSttMeta struct {
	sharedParams stt.SharedDetailParams
	sttMeta      *model.SttMeta
}

func (c *sttCtx) generateReverseJourneyTariffValidateSharedAndSttMeta(additionalParams interface{}) (*respGenerateReverseJourneyTariffValidateSharedAndSttMeta, error) {
	sharedParams, ok := additionalParams.(stt.SharedDetailParams)
	if !ok {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to parse stt reverse journey",
			"id": "Gagal memparsing stt reverse journey",
		})
	}

	sttMeta := sharedParams.Stt.SttMetaToStruct()
	if sttMeta == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to parse stt meta",
			"id": "Gagal memparsing stt meta",
		})
	}

	return &respGenerateReverseJourneyTariffValidateSharedAndSttMeta{
		sharedParams: sharedParams,
		sttMeta:      sttMeta,
	}, nil
}

func (c *sttCtx) generateReverseJourneyTariff(ctx context.Context, params *stt.CreateStt, additionalParams interface{}) (*model.CheckTariffResponse, *model.SttMeta, error) {

	opName := "UsecaseStt-CreateSTTReverseJourney-generateReverseJourneyTariff"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	checkTariff := new(model.CheckTariffResponse)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": checkTariff, "error": errors})
	}()

	/**
	 * get check tariff
	 */

	isTariffNotRound := false
	if params.Stt.SttIsCOD {
		isTariffNotRound = true
	}

	params.Stt.SttIsWoodpacking = false
	params.Stt.SttIsCOD = false

	respValidate, err := c.generateReverseJourneyTariffValidateSharedAndSttMeta(additionalParams)
	if err != nil {
		return nil, nil, err
	}

	_, shipmentPrefix, er := c.CheckSTTFromCARetailFromRoot(ctx, respValidate.sttMeta)
	if er != nil {
		return nil, nil, er
	}

	reqCheckTariff := &stt.CalculateTariffParams{
		RequestCalculateTariff: &stt.RequestCalculateTariff{
			OriginID:         params.Stt.SttOriginDistrictID,
			DestinationID:    params.Stt.SttDestinationDistrictID,
			ProductType:      params.Stt.SttProductType,
			CommodityID:      params.Commodity.Data.CommodityID,
			InsuranceType:    params.Stt.SttInsuranceType,
			GoodsPrice:       params.Stt.SttGoodsEstimatePrice,
			CodAmount:        params.Stt.SttCODAmount,
			IsCod:            params.Stt.SttIsCOD,
			IsWoodpacking:    params.Stt.SttIsWoodpacking,
			AccountType:      params.BookedFor.Type,
			AccountRefID:     params.BookedFor.ID,
			IsHaveTaxID:      false,
			IsDisablePromo:   true,
			IsTariffNotRound: isTariffNotRound,
			ShipmentPrefix:   shipmentPrefix,
		},
		Token: params.Token,
	}

	if len(shipmentPrefix) < 1 {
		reqCheckTariff.RequestCalculateTariff.ShipmentPrefix = shared.GetPrefixFromShipmentOrNoRefExt(params.Stt.SttShipmentID, params.Stt.SttNoRefExternal)
	}

	if params.Stt.SttTaxNumber != `` {
		reqCheckTariff.RequestCalculateTariff.IsHaveTaxID = true
	}

	sharedParams := respValidate.sharedParams
	sttMeta := respValidate.sttMeta

	reqCheckTariff.RequestCalculateTariff.GeneratePiecesCalculateTariff(params.Stt.SttPieces)
	checkTariff, errors = c.generateReverseJourneyTariffCalculation(selfCtx, paramGenerateReverseJourneyTariffMisBooking{
		paramsCreateStt: params,
		sharedParams:    sharedParams,
		sttMeta:         sttMeta,
		reqCheckTariff:  reqCheckTariff,
		checkTariff:     checkTariff,
	})
	if errors != nil || checkTariff == nil {

		/**
		 * check tariff for stt reverse journey will be done 2 times (client tariff and then retail tariff), ONLY IF
		 * booked for is client and tariff for route is not available and mis booking is not new calculation
		 */
		isShouldReturnError := func() bool {
			return !strings.Contains(errors.Error(), shared.ErrPriceSelectedRouteNotAvailable) ||
				reqCheckTariff.RequestCalculateTariff.AccountType == model.POS
		}
		if isShouldReturnError() {
			return nil, nil, errors
		}

		reqCheckTariff.RequestCalculateTariff.AccountType = params.BookedBy.Type
		reqCheckTariff.RequestCalculateTariff.AccountRefID = params.BookedBy.ID
		checkTariff, errors = c.checkTariffRepo.TariffCalculation(selfCtx, reqCheckTariff)
		if errors != nil || checkTariff == nil {
			return nil, nil, errors
		}
	}

	// Note: The following commented code is not used in the current implementation.
	// (Untuk keperluan epic: https://lionparcel.atlassian.net/browse/CS-27607)
	//checkTariff.Data.ChargeableWeight = sharedParams.Stt.SttChargeableWeight
	//checkTariff.Data.GrossWeight = sharedParams.Stt.SttGrossWeight
	//checkTariff.Data.VolumeWeight = sharedParams.Stt.SttVolumeWeight

	if checkTariff.Data.VolumeWeightDiscount == sttMeta.VolumeWeightDiscount {
		checkTariff.Data.ChargeableWeight = sharedParams.Stt.SttChargeableWeight
		checkTariff.Data.GrossWeight = sharedParams.Stt.SttGrossWeight
		checkTariff.Data.VolumeWeight = sharedParams.Stt.SttVolumeWeight
	}

	// set COD to 0
	checkTariff.Data.CodAmount = 0
	checkTariff.Data.CodFee = 0

	if sharedParams.ReverseJourneyStatus == model.RTSHQ ||
		sharedParams.ReverseJourneyStatus == model.CNX ||
		(sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt == model.RTSHQ) ||
		(sharedParams.ReverseJourneyStatus == model.RTS && sharedParams.Stt.SttIsCOD &&
			((sharedParams.Stt.SttBookedByType == model.POS && sharedParams.Stt.SttBookedForType == model.POS) ||
				(sharedParams.Stt.SttShipmentID != `` && (shared.GetPrefixShipmentID(sharedParams.Stt.SttShipmentID) == model.C1 || shared.GetPrefixShipmentID(sharedParams.Stt.SttShipmentID) == model.C2) && sharedParams.CodHandling == model.SPECIALCOD) ||
				(sharedParams.CodHandling == model.SPECIALCOD && sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` && (shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID) == model.C1 || shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID) == model.C2)) ||
				(sharedParams.Stt.SttShipmentID != `` && model.IsShipmentFavorite[shared.GetPrefixShipmentID(sharedParams.Stt.SttShipmentID)]) ||
				(sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` &&
					model.IsShipmentFavorite[shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID)]) ||
				(sharedParams.Stt.SttShipmentID != `` && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(sharedParams.Stt.SttShipmentID)]) ||
				(sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` &&
					model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID)]))) {
		// set 0 for reverse journey tariff RTSHQ or CNX or RTS with : COD Retail or DKLIK SPECIAL COD C1 or shipment fav or prefix ARB ARA (COD Customer Apps Retail)

		checkTariff.Data.CityRates = 0
		checkTariff.Data.ForwardRates = 0
		checkTariff.Data.ShippingCost = 0
		checkTariff.Data.CommoditySurcharge = 0
		checkTariff.Data.HeavyWeightSurcharge = 0
		checkTariff.Data.DocumentSurcharge = 0
		checkTariff.Data.InsuranceRates = 0
		checkTariff.Data.WoodpackingRates = 0
		checkTariff.Data.TotalTariff = 0
		checkTariff.Data.TaxRates = 0
		checkTariff.Data.BMTaxRate = 0
		checkTariff.Data.PPNTaxRate = 0
		checkTariff.Data.PPHTaxRate = 0
		checkTariff.Data.OriginDistrictRate = 0
		checkTariff.Data.DestinationDistrictRate = 0
		checkTariff.Data.PublishRate = 0
		checkTariff.Data.ShippingSurchargeRate = 0
		checkTariff.Data.HeavyWeightSurchargeRemarks = ``
		checkTariff.Data.TarifAfterDiscount = model.TarifAfterDiscount{}
	}

	return checkTariff, sttMeta, nil

}

func (c *sttCtx) generateSttNo(ctx context.Context, params *stt.CreateStt, additionalParams interface{}) (string, error) {

	opName := "UsecaseStt-CreateSTT-generateSttNo"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var errors error
	sttNo := ``

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": sttNo, "error": errors})
	}()

	newTime := c.timeRepo.Now(time.Now()).Add(time.Millisecond * time.Duration(0))

	sharedDetail, ok := additionalParams.(stt.SharedDetailParams)
	if !ok {
		return "", shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to parse stt reverse journey",
			"id": "Gagal memparsing stt reverse journey",
		})
	}

	sttMeta := sharedDetail.Stt.SttMetaToStruct()
	if sttMeta == nil {
		return "", shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to parse stt meta reverse journey",
			"id": "Gagal memparsing stt meta reverse journey",
		})
	}

	/**
	 * generate stt by last status
	 */
	sttType := model.SttAutoTypeReverseJourney

	if sharedDetail.Stt.SttLastStatusID == model.CODREJ {
		if sharedDetail.Stt.SttShipmentID != `` &&
			(model.MappingShipmentCODValid[shared.GetPrefixShipmentID(sharedDetail.Stt.SttShipmentID)] || model.IsShipmentFavorite[shared.GetPrefixShipmentID(sharedDetail.Stt.SttShipmentID)]) {
			sttType = model.SttAutoTypeCAACR
		}

		if sharedDetail.Stt.SttShipmentID == `` {
			sttType = model.SttAutoTypeClientCCR
		}

		if sharedDetail.Stt.SttIsCOD && sharedDetail.Stt.SttBookedByType == model.POS && sharedDetail.Stt.SttBookedForType == model.POS {
			sttType = model.SttAutoTypeReverseJourney
		}

		if sharedDetail.Stt.SttShipmentID != `` && (shared.GetPrefixShipmentID(sharedDetail.Stt.SttShipmentID) == model.C1 || shared.GetPrefixShipmentID(sharedDetail.Stt.SttShipmentID) == model.C2) {
			sttType = model.SttAutoTypeClientCCR
			if sharedDetail.CodHandling == model.SPECIALCOD {
				sttType = model.SttAutoTypeCAACR
			}
		}
	}

	if sharedDetail.Stt.SttLastStatusID == model.MISBOOKING {
		sttType = model.SttAutoTypeReverseJourneyMisBooking
		if model.IsSttStatusRTSRTSHQ[sharedDetail.ReverseJourneyStatus] {
			sttType = model.SttAutoTypeReverseJourney
		}
	}

	if sharedDetail.IsPrefixReturnReverseJourney && sharedDetail.ReverseJourneyStatus != model.REROUTE {
		sttType = model.SttAutoTypeReturnReverseJourney
	}

	if sharedDetail.ReverseJourneyStatus == model.RTS && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt == model.REROUTE {
		sttType = model.SttAutoTypeReverseJourney
		if sharedDetail.Stt.SttLastStatusID == model.CODREJ && sharedDetail.Stt.SttIsCOD {
			if (sttMeta.DetailSttReverseJourney.RootReverseShipmentID == `` && sharedDetail.Stt.SttBookedForType == model.CLIENT) ||
				sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` && (shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID) == model.C1 || shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID) == model.C2) {
				sttType = model.SttAutoTypeClientCCR
				if sharedDetail.CodHandling == model.SPECIALCOD {
					sttType = model.SttAutoTypeCAACR
				}
			}

			if sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` &&
				(model.MappingShipmentCODValid[shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID)] || model.IsShipmentFavorite[shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID)]) {
				sttType = model.SttAutoTypeCAACR
			}
		}
	}

	// custom-process RTSHQ
	if sharedDetail.ReverseJourneyStatus == model.RTSHQ {

		// Shipment C1 standardcod
		if (sttMeta.DetailSttReverseJourney == nil && (sharedDetail.RootShipmentPrefix == model.C1 || sharedDetail.RootShipmentPrefix == model.C2) && sharedDetail.CodHandling == model.STANDARDCOD) ||
			(sttMeta.DetailSttReverseJourney != nil && (sharedDetail.RootShipmentPrefix == model.C1 || sharedDetail.RootShipmentPrefix == model.C2) && sharedDetail.CodHandling == model.STANDARDCOD && model.IsMisrouteOrMisbooking[sttMeta.DetailSttReverseJourney.RootReverseLastStatusStt]) {
			sttType = model.SttAutoTypeReverseJourney // 77

			if sharedDetail.Stt.SttLastStatusID == model.CODREJ {
				sttType = model.SttAutoTypeClientCCR // 94
			}

		} else if (sharedDetail.RootShipmentPrefix == model.C1 || sharedDetail.RootShipmentPrefix == model.C2) && sharedDetail.CodHandling == "" { // Shipment C1 non cod
			sttType = model.SttAutoTypeReverseJourney // 77
		}

	}

	sttNo = shared.GenerateSttID(sttType, false, false, false, &newTime)
	sttNo, errors = c.checkSttNoDuplication(selfCtx, stt.CheckSttNoDuplicationRequest{
		SttNo: sttNo,
		GenerateSttNoFunc: func() string {
			now := c.timeRepo.Now(time.Time{}).Add(time.Millisecond * time.Duration(0))
			sttNoFunc := shared.GenerateSttID(sttType, false, false, false, &now)
			return sttNoFunc
		},
	})
	if errors != nil {
		return "", errors
	}

	return sttNo, nil
}

func (c *sttCtx) generateReverseJourneySttDetail(ctx context.Context, params *stt.CreateSttDetail, additionalParams interface{}) (*model.SttCreate, error) {
	opName := "UsecaseStt-CreateSTTReverseJourney-generateReverseJourneySttDetail"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var errors error
	sttCreate := new(model.SttCreate)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": sttCreate, "error": errors})
	}()

	sttCreate = params.SttCreate

	/**
	 * Additional stt field booked for client
	 */
	sttCreate.Stt.SttSource = model.MANUAL
	sttCreate.Stt.SttShipmentID = ""

	switch params.CreateStt.BookedFor.Type {
	case model.CLIENT:
		sttCreate.Stt.SttClientID = params.CreateStt.BookedFor.ID
		sttCreate.Stt.SttPosID = 0
	case model.POS:
		sttCreate.Stt.SttClientID = 0
		sttCreate.Stt.SttPosID = params.CreateStt.BookedFor.ID

	}

	sttCreate.Stt.SttBilledTo = params.CreateStt.BookedFor.Name
	sttCreate.Stt.SttIsDO = false
	sttCreate.Stt.SttIsCOD = false

	sharedDetail, ok := additionalParams.(stt.SharedDetailParams)
	if !ok {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to parse stt reverse journey",
			"id": "Gagal memparsing stt reverse journey",
		})
	}

	if sharedDetail.ReverseJourneyStatus == model.REROUTE {
		sttCreate.Stt.SttIsDO = params.CreateStt.Stt.SttIsDO
		sttCreate.Stt.SttIsCOD = params.CreateStt.Stt.SttIsCOD
		sttCreate.Stt.SttIsDFOD = params.CreateStt.Stt.SttIsDFOD
		sttCreate.Stt.SttCODAmount = params.CreateStt.Stt.SttCODAmount
		sttCreate.Stt.SttCODFee = 0
		if params.SttCreate.CheckTariff != nil {
			params.SttMeta.RoundingDiff = params.SttCreate.CheckTariff.RoundingDiff
		}
	}

	sttCreate.Stt.SttMeta = params.SttMeta.ToString()
	sttCreate.Stt.SttUpdatedActorID = dbr.NewNullInt64(params.CreateStt.BookedBy.ID)
	sttCreate.Stt.SttUpdatedActorRole = dbr.NewNullString(params.CreateStt.BookedBy.Type)
	sttCreate.Stt.SttUpdatedActorName = dbr.NewNullString(params.CreateStt.BookedBy.Name)
	sttCreate.Stt.SttCommodityID = params.CreateStt.Commodity.Data.CommodityID
	sttCreate.Stt.SttCreatedBy = model.AccountSystem.ActorID
	sttCreate.Stt.SttCreatedName = model.AccountSystem.ActorName
	sttCreate.Stt.SttUpdatedBy = model.AccountSystem.ActorID
	sttCreate.Stt.SttUpdatedName = model.AccountSystem.ActorName
	sttCreate.SttPieceHistory.HistoryCreatedName = model.AccountSystem.ActorName
	sttCreate.SttPieceHistory.HistoryCreatedBy = model.AccountSystem.ActorID

	return sttCreate, nil
}

func (c *sttCtx) generateRemarksHistory(ctx context.Context, params *stt.CreateStt, additionalParams interface{}) (*model.RemarkPieceHistory, error) {
	opName := "UsecaseStt-CreateSTT-generateRemarksHistory"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res := stt.CreateSttResponse{}
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": errors})
	}()

	return &model.RemarkPieceHistory{
		ActorExternalCode:       params.BookedBy.ExternalCode,
		ActorExternalType:       params.BookedBy.Type,
		HistoryLocationName:     params.OriginLocation.City.Name,
		BookingFee:              params.CheckTariff.Data.TotalTariff,
		BookingFeeAfterDiscount: params.CheckTariff.Data.TotalTariffAfterDiscount,
		ChargeableWeight:        params.CheckTariff.Data.ChargeableWeight,
		HistoryDistrictCode:     params.OriginLocation.District.Data.Code,
		HistoryDistrictName:     params.OriginLocation.District.Data.Name,
	}, nil
}

func (c *sttCtx) generateSttMeta(ctx context.Context, params *stt.CreateStt, additionalParams interface{}) (*model.SttMeta, error) {
	opName := "UsecaseStt-CreateSTT-generateSttMeta"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res := new(model.SttMeta)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": errors})
	}()

	sharedDetail, ok := additionalParams.(stt.SharedDetailParams)
	if !ok {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to parse stt reverse journey",
			"id": "Gagal memparsing stt reverse journey",
		})
	}

	sttMeta := sharedDetail.Stt.SttMetaToStruct()
	res = &model.SttMeta{
		EstimateSLA:             c.generateEstimaeSlaByProductType(params.CheckTariff.Data.EstimateSLA, params.Stt.SttProductType, params.Stt.SttCreatedAt, params.OriginLocation.City.Timezone),
		RoundingDiff:            params.CheckTariff.Data.RoundingDiff,
		OriginCityName:          params.OriginLocation.City.Name,
		OriginDistrictName:      params.OriginLocation.District.Data.Name,
		DestinationCityName:     params.DestinationLocation.City.Name,
		DestinationDistrictName: params.DestinationLocation.District.Data.Name,
		DetailCalculateRetailTariff: []model.DetailCalculateRetailTariff{
			{Status: model.BKD, IsCalculated: false},
		},
		PostalCodeDestination: params.Stt.PostalCodeDestination,
		VolumeWeightDiscount:  params.CheckTariff.Data.VolumeWeightDiscount,
		DetailSttReverseJourney: &model.DetailSttReverseJourney{
			ReverseSttNo:            sharedDetail.Stt.SttNo,
			ReverseShipmentID:       sharedDetail.Stt.SttShipmentID,
			ReverseLastStatusStt:    sharedDetail.Stt.SttLastStatusID,
			ReverseJourneyStatusStt: sharedDetail.ReverseJourneyStatus,
			RootReverseSttNo: func() string {
				if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseSttNo != `` {
					return sttMeta.DetailSttReverseJourney.RootReverseSttNo
				}
				return sharedDetail.Stt.SttNo
			}(),
			RootReverseShipmentID: func() string {
				if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` {
					return sttMeta.DetailSttReverseJourney.RootReverseShipmentID
				}
				return sharedDetail.Stt.SttShipmentID
			}(),
			RootReverseLastStatusStt: func() string {
				if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseLastStatusStt != `` {
					return sttMeta.DetailSttReverseJourney.RootReverseLastStatusStt
				}
				return sharedDetail.Stt.SttLastStatusID
			}(),
			RootReverseCodHandling: func() string {
				if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseCodHandling != `` {
					return sttMeta.DetailSttReverseJourney.RootReverseCodHandling
				}
				return sharedDetail.CodHandling
			}(),
			ReverseChargedPosID:     sharedDetail.ReverseChargedPosID,
			ReverseChargedConsoleID: sharedDetail.ReverseChargedConsoleID,
			RootReverseSttNoRefExternal: func() string {
				if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseSttNoRefExternal != `` {
					return sttMeta.DetailSttReverseJourney.RootReverseSttNoRefExternal
				}
				return sharedDetail.Stt.SttNoRefExternal
			}(),
		},
		PostalCodeOrigin: params.Stt.PostalCodeOrigin,
		SttFtzIdentityNumber: params.Stt.SttFtzIdentityNumber,
		SttFtzRecipientEmail: params.Stt.SttFtzRecipientEmail,
		SttFtzTaxImage:       params.Stt.SttFtzTaxImage,
		SttFtzAttachFiles:    params.Stt.SttFtzAttachFiles,
		SttFtzCIPL:           params.Stt.SttFtzCIPL,
		SttFtzKtpImage:       params.Stt.SttFtzKtpImage,
	}

	return res, nil
}

func (c *sttCtx) isShipmentSpecialCOD(ctx context.Context, shipmentID string) (bool, string, error) {
	shipment, err := c.shipmentRepo.Get(ctx, &model.ShipmentViewParams{
		ShipmentAlgoID: shipmentID,
	})
	if err != nil {
		return false, "", err
	}

	if shipment == nil || len(shipment.ShipmentPackets) == 0 {
		return false, "", shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting shipment data",
			"id": "Terjadi kesalahan pada saat query getting shipment data",
		})
	}
	// perlu handle
	var isC1SpecialCOD bool
	var codHandling string
	if shipment.ShipmentMeta != nil {
		shipmentMetaStruct := shipment.ShipmentMetaToStruct()
		if shipmentMetaStruct != nil {
			codHandling = shipmentMetaStruct.CodHandling
			isC1SpecialCOD = shipmentMetaStruct.CodHandling == model.SPECIALCOD
		}
	}
	return isC1SpecialCOD, codHandling, nil
}

func (c *sttCtx) checkSaldoSttReverseJourney(ctx context.Context, params stt.CheckSaldoParams, additionalParams interface{}) error {

	// check current saldo only for POS
	if params.AccountRefType != model.POS {
		return nil
	}

	// check current saldo for manual booking and shipment booking prefix AP and AS
	if params.Source == model.ALGO && !model.IsShipmentPrefixFavorite[shared.GetPrefixShipmentID(params.Stt.SttShipmentID)] {
		return nil
	}

	sharedParams, ok := additionalParams.(stt.SharedDetailParams)
	if !ok {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to parse stt reverse journey",
			"id": "Gagal memparsing stt reverse journey",
		})
	}

	if sharedParams.ReverseJourneyStatus == model.RTSHQ {
		return nil
	}

	accountToken, err := c.accountRepo.GetTokenGenesis(ctx)
	if err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to retrieve token genesis",
			"id": "Gagal mendapatkan token genesis",
		})
	}

	balanceLimit, err := c.balanceLimitRepo.Select(ctx, model.BalanceLimitViewParams{
		LimitRuleAppliedTo: params.AccountRefType,
		Limit:              10,
		Page:               1,
		Token:              accountToken.Data.Token,
	})
	isBalanceDataEmpty := balanceLimit == nil || len(balanceLimit.Data) == 0
	if err != nil || isBalanceDataEmpty {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Balance Limit is Not Found",
			"id": "Balance Limit Tidak Ditemukan",
		})
	}

	var limitRuleMinBalance float64
	for _, value := range balanceLimit.Data {
		if value.LimitRuleMetaData.IsCodDelivery && params.IsCODDelivery {
			limitRuleMinBalance = value.LimitRuleMinBalance
			break
		} else if !value.LimitRuleMetaData.IsCodDelivery && !params.IsCODDelivery {
			limitRuleMinBalance = value.LimitRuleMinBalance
			break
		}
	}

	partnerWallets, err := c.walletRepo.SelectLastBalance(ctx, model.WalletViewParams{
		Token:     accountToken.Data.Token,
		Limit:     1,
		Page:      1,
		ActorType: params.AccountRefType,
		ActorID:   params.AccountRefID,
	})

	isPartnerWalletsEmpty := partnerWallets == nil || len(partnerWallets.Data) == 0
	if err != nil || isPartnerWalletsEmpty {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Wallet is Not Found",
			"id": "Wallet Tidak Ditemukan",
		})
	}

	sttNo, sttShipmentID, sttLastStatusID := c.isSttReverseIfReverseGetStt(sharedParams.Stt, sharedParams.IsPrefixReturnReverseJourney)

	if c.excludeCheckSaldoSttReverseJourney(sttNo, sttShipmentID, sharedParams.ReverseJourneyStatus, sttLastStatusID) {
		return nil
	}

	// deduct currect saldo with total tariff
	deductedSaldo := partnerWallets.Data[0].WalletBalance - params.CheckTariff.TotalTariff

	if deductedSaldo < limitRuleMinBalance {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Unable to book. Saldo is below specified minimum balance",
			"id": "Tidak bisa booking. Saldo dibawah minimum balance saldo yang ditentukan",
		})
	}

	return nil
}

func (c *sttCtx) isSttReverseIfReverseGetStt(stt model.SttDetailResult, isPrefixReturnReverseJourney bool) (string, string, string) {
	sttNo := stt.SttNo
	sttShipmentID := stt.SttShipmentID
	sttLastStatusID := stt.SttLastStatusID
	if isPrefixReturnReverseJourney {
		detailReverseJourney := stt.GetSttMetaDetailReverseJourney()
		if detailReverseJourney.RootReverseSttNo != `` {
			sttNo = detailReverseJourney.RootReverseSttNo
		}
		if detailReverseJourney.RootReverseShipmentID != `` {
			sttShipmentID = detailReverseJourney.RootReverseShipmentID
		}
	}

	return sttNo, sttShipmentID, sttLastStatusID
}

func (c *sttCtx) createSTT(ctx context.Context, params *stt.CreateStt, flowBooking stt.FlowBooking, additionalParams interface{}) (*model.SttCreate, error) {
	opName := "UsecaseStt-createSTT"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res := new(model.SttCreate)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": errors})
	}()

	if err := params.Stt.Validate(0, params.Source); err != nil {
		errors = err
		return nil, err
	}

	if err := params.Stt.ValidateInterpack(0, params.Source); err != nil {
		errors = err
		return nil, err
	}

	bookedFor, err := flowBooking.GenerateActorBookedFor(selfCtx, params, additionalParams)
	if err != nil {
		errors = err
		return nil, err
	}

	bookedBy, err := flowBooking.GenerateActorBookedBy(selfCtx, params, additionalParams)
	if err != nil {
		errors = err
		return nil, err
	}

	commodity, err := flowBooking.GenerateCommodity(selfCtx, params, additionalParams)
	if err != nil {
		errors = err
		return nil, err
	}

	_, err = flowBooking.GenerateProductType(selfCtx, params, additionalParams)
	if err != nil {
		errors = err
		return nil, err
	}

	originLocation, err := flowBooking.GenerateOrigin(selfCtx, params, additionalParams)
	if err != nil {
		errors = err
		return nil, err
	}

	destinationLocation, err := flowBooking.GenerateDestination(selfCtx, params, additionalParams)
	if err != nil {
		errors = err
		return nil, err
	}

	params.Stt.SttOriginCityID = originLocation.District.Data.City.Code
	params.Stt.SttDestinationCityID = destinationLocation.District.Data.City.Code

	_, err = flowBooking.GenerateConfigurablePrice(selfCtx, params, additionalParams)
	if err != nil {
		errors = err
		return nil, err
	}

	if params.Stt.SttNo == `` {
		sttNo, err := flowBooking.GenerateSttNo(selfCtx, params, additionalParams)
		if err != nil {
			errors = err
			return nil, err
		}

		params.Stt.SttNo = sttNo
	}

	checkTariff, _, err := flowBooking.GenerateTariff(selfCtx, &stt.CreateStt{
		Stt:            params.Stt,
		AccountType:    params.AccountType,
		AccountID:      params.AccountID,
		AccountName:    params.AccountName,
		Token:          params.Token,
		AccountRefID:   params.AccountRefID,
		AccountRefName: params.AccountRefName,
		AccountRefType: params.AccountRefType,
		Source:         params.Source,
		Commodity:      commodity,
		BookedFor:      *bookedFor,
		BookedBy:       *bookedBy,
	}, additionalParams)
	if err != nil {
		errors = err
		return nil, err
	}

	// validate total stt volume weight and total stt gross weight
	if err := stt.ValidateTotalWeight(checkTariff.Data.VolumeWeight, checkTariff.Data.GrossWeight, params.Stt.SttShipmentID, params.Stt.SttNoRefExternal); err != nil {
		errors = err
		return nil, err
	}

	if err := flowBooking.CheckSaldo(selfCtx, stt.CheckSaldoParams{
		Source:         params.Source,
		AccountRefType: bookedBy.Type,
		CheckTariff:    &checkTariff.Data,
		AccountRefID:   bookedBy.ID,
		Token:          params.Token,
		IsCODDelivery:  params.BookedBy.IsCODDelivery,
	}, additionalParams); err != nil {
		errors = err
		return nil, err
	}

	tempRemarksPieceHistory, err := flowBooking.GenerateRemarksHistoryStt(selfCtx, &stt.CreateStt{
		Stt:                 params.Stt,
		AccountType:         params.AccountType,
		AccountID:           params.AccountID,
		AccountName:         params.AccountName,
		Token:               params.Token,
		AccountRefID:        params.AccountRefID,
		AccountRefName:      params.AccountRefName,
		AccountRefType:      params.AccountRefType,
		Source:              params.Source,
		Commodity:           commodity,
		BookedFor:           *bookedFor,
		BookedBy:            *bookedBy,
		CheckTariff:         checkTariff,
		DestinationLocation: *destinationLocation,
		OriginLocation:      *originLocation,
	}, additionalParams)
	if err != nil {
		errors = err
		return nil, err
	}

	// set STT meta
	sttMeta, err := flowBooking.GenerateSttMeta(selfCtx, &stt.CreateStt{
		Stt:                 params.Stt,
		AccountType:         params.AccountType,
		AccountID:           params.AccountID,
		AccountName:         params.AccountName,
		Token:               params.Token,
		AccountRefID:        params.AccountRefID,
		AccountRefName:      params.AccountRefName,
		AccountRefType:      params.AccountRefType,
		Source:              params.Source,
		Commodity:           commodity,
		BookedFor:           *bookedFor,
		BookedBy:            *bookedBy,
		CheckTariff:         checkTariff,
		DestinationLocation: *destinationLocation,
		OriginLocation:      *originLocation,
	}, additionalParams)
	if err != nil {
		errors = err
		return nil, err
	}

	tempRemarksPieceHistory.VolumeWeightDiscount = sttMeta.VolumeWeightDiscount

	/**
	 * General stt field for booking
	 */
	sttCreate := stt.GenerateSttCreate(&stt.SttCreateParams{
		Stt:                   &params.Stt,
		CheckTariff:           &checkTariff.Data,
		Now:                   &params.Now,
		BookedBy:              bookedBy.ID,
		BookedName:            bookedBy.Name,
		BookedRole:            bookedBy.Type,
		BookedCode:            bookedBy.Code,
		IsSttManual:           false,
		AccountID:             int(params.AccountID),
		AccountName:           params.AccountName,
		Remarks:               tempRemarksPieceHistory.ToString(),
		DestinationDistrict:   destinationLocation.District,
		OriginDistrict:        originLocation.District,
		Commodity:             commodity,
		BookedForExternalCode: bookedFor.ExternalCode,
		BookedByExternalCode:  bookedBy.ExternalCode,
		BookedForActor: &model.Actor{
			ID:   bookedFor.ID,
			Name: bookedFor.Name,
			Code: bookedFor.Code,
			Type: bookedFor.Type,
		},

		// set empty
		ElexysTariff: &elexys.CheckTariffEstimationElexys{},
		SttManual:    &model.SttManual{},
	})

	/**
	 * Additional stt field booked for client
	 */
	sttCreate, errors = flowBooking.GenerateSttDetail(selfCtx, &stt.CreateSttDetail{
		CreateStt: &stt.CreateStt{
			Stt:            params.Stt,
			AccountType:    params.AccountType,
			AccountID:      params.AccountID,
			AccountName:    params.AccountName,
			Token:          params.Token,
			AccountRefID:   params.AccountRefID,
			AccountRefName: params.AccountRefName,
			AccountRefType: params.AccountRefType,
			Source:         params.Source,
			Commodity:      commodity,
			BookedFor:      *bookedFor,
			BookedBy:       *bookedBy,
		},
		SttCreate: sttCreate,
		SttMeta:   *sttMeta,
	}, additionalParams)
	if errors != nil {
		return nil, errors
	}

	return sttCreate, nil
}

func (c *sttCtx) validateDistrictTypeSttDO(ctx context.Context, clientIsDO bool, district *model.District) error {
	if clientIsDO && !model.IsDistrictTypeForClientDO[district.Data.Type] {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "District destination type doesn't support STT DO",
			"id": "Tipe district destinasi tidak mendukung STT DO",
		})
	}
	return nil
}

func (c *sttCtx) checkCalculateTariffPromoElligible(bookedByRole, bookedForRole, source string) bool {
	if bookedByRole == model.POS && bookedForRole == model.POS && source != model.ALGO {
		return true
	}
	return false
}

func (c *sttCtx) generateSttHistoryDataAdjustment(req *stt.UpdateSttRequest, data *model.SttDetailResult, historyData *model.HistoryDataAdjustment, pieceDetail model.SttPieceAdjustment, isAfterPOD bool) *model.HistoryDataAdjustment {
	sttMeta := data.SttMetaToStruct()
	shipment := shared.GetPrefixShipmentID(data.SttNo)

	isSttReverseJourney := false
	reverseJourneyStatus := ""
	if model.IsPrefixValidReverseJourney[data.SttNo[:2]] && sttMeta != nil && sttMeta.DetailSttReverseJourney != nil {
		isSttReverseJourney = true
		reverseJourneyStatus = sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt
	}

	res := &model.HistoryDataAdjustment{}
	switch {
	case model.IsPrefixSttRetail[data.SttNo[:2]]:
		switch req.AccountType {
		case model.PARTNER:
			fallthrough
		case model.INTERNAL:
			res = &model.HistoryDataAdjustment{
				SenderNameBeforeAdjustment:            historyData.SenderNameBeforeAdjustment,
				SenderPhoneBeforeAdjustment:           historyData.SenderPhoneBeforeAdjustment,
				SenderAddressBeforeAdjustment:         historyData.SenderAddressBeforeAdjustment,
				RecipientNameBeforeAdjustment:         historyData.RecipientNameBeforeAdjustment,
				RecipientPhoneBeforeAdjustment:        historyData.RecipientPhoneBeforeAdjustment,
				RecipientAddressBeforeAdjustment:      historyData.RecipientAddressBeforeAdjustment,
				RecipientAddressTypeBeforeAdjustment:  historyData.RecipientAddressTypeBeforeAdjustment,
				DestinationCityIDBeforeAdjustment:     historyData.DestinationCityIDBeforeAdjustment,
				DestinationDistrictIDBeforeAdjustment: historyData.DestinationDistrictIDBeforeAdjustment,
				CommodityCodeBeforeAdjustment:         historyData.CommodityCodeBeforeAdjustment,
				GoodsStatusBeforeAdjustment:           historyData.GoodsStatusBeforeAdjustment,
				TaxNumberBeforeAdjustment:             historyData.TaxNumberBeforeAdjustment,
				PieceGrossWeightBeforeAdjustment:      pieceDetail.SttPieceGrossWeightBeforeAdjustment,
				PieceHeightBeforeAdjustment:           pieceDetail.SttPieceHeightBeforeAdjustment,
				PieceWidthBeforeAdjustment:            pieceDetail.SttPieceWidthBeforeAdjustment,
				PieceLengthBeforeAdjustment:           pieceDetail.SttPieceLengthBeforeAdjustment,

				SenderNameAfterAdjustment:            historyData.SenderNameAfterAdjustment,
				SenderPhoneAfterAdjustment:           historyData.SenderPhoneAfterAdjustment,
				SenderAddressAfterAdjustment:         historyData.SenderAddressAfterAdjustment,
				RecipientNameAfterAdjustment:         historyData.RecipientNameAfterAdjustment,
				RecipientPhoneAfterAdjustment:        historyData.RecipientPhoneAfterAdjustment,
				RecipientAddressAfterAdjustment:      historyData.RecipientAddressAfterAdjustment,
				RecipientAddressTypeAfterAdjustment:  historyData.RecipientAddressTypeAfterAdjustment,
				DestinationCityIDAfterAdjustment:     historyData.DestinationCityIDAfterAdjustment,
				DestinationDistrictIDAfterAdjustment: historyData.DestinationDistrictIDAfterAdjustment,
				CommodityCodeAfterAdjustment:         historyData.CommodityCodeAfterAdjustment,
				GoodsStatusAfterAdjustment:           historyData.GoodsStatusAfterAdjustment,
				TaxNumberAfterAdjustment:             historyData.TaxNumberAfterAdjustment,
				PieceGrossWeightAfterAdjustment:      pieceDetail.SttPieceGrossWeightAfterAdjustment,
				PieceHeightAfterAdjustment:           pieceDetail.SttPieceHeightAfterAdjustment,
				PieceWidthAfterAdjustment:            pieceDetail.SttPieceWidthAfterAdjustment,
				PieceLengthAfterAdjustment:           pieceDetail.SttPieceLengthAfterAdjustment,
			}
		}

	case isSttReverseJourney && req.AccountType == model.INTERNAL:
		res = &model.HistoryDataAdjustment{
			PieceGrossWeightBeforeAdjustment: pieceDetail.SttPieceGrossWeightBeforeAdjustment,
			PieceHeightBeforeAdjustment:      pieceDetail.SttPieceHeightBeforeAdjustment,
			PieceWidthBeforeAdjustment:       pieceDetail.SttPieceWidthBeforeAdjustment,
			PieceLengthBeforeAdjustment:      pieceDetail.SttPieceLengthBeforeAdjustment,

			PieceGrossWeightAfterAdjustment: pieceDetail.SttPieceGrossWeightAfterAdjustment,
			PieceHeightAfterAdjustment:      pieceDetail.SttPieceHeightAfterAdjustment,
			PieceWidthAfterAdjustment:       pieceDetail.SttPieceWidthAfterAdjustment,
			PieceLengthAfterAdjustment:      pieceDetail.SttPieceLengthAfterAdjustment,
		}
		fallthrough
	case data.SttNo[:2] == model.PrefixAutoReturnReverseJourneyMissbokingReroute:
		fallthrough
	case data.SttNo[:2] == model.PrefixAutoReverseJourney && (reverseJourneyStatus == model.RTS || reverseJourneyStatus == model.REROUTE):
		res.RecipientNameBeforeAdjustment = historyData.RecipientNameBeforeAdjustment
		res.RecipientPhoneBeforeAdjustment = historyData.RecipientPhoneBeforeAdjustment
		res.RecipientAddressBeforeAdjustment = historyData.RecipientAddressBeforeAdjustment
		res.RecipientAddressTypeBeforeAdjustment = historyData.RecipientAddressTypeBeforeAdjustment
		res.DestinationCityIDBeforeAdjustment = historyData.DestinationCityIDBeforeAdjustment
		res.DestinationDistrictIDBeforeAdjustment = historyData.DestinationDistrictIDBeforeAdjustment
		res.CommodityCodeBeforeAdjustment = historyData.CommodityCodeBeforeAdjustment
		res.PiecePerPackBeforeAdjustment = historyData.PiecePerPackBeforeAdjustment

		res.RecipientNameAfterAdjustment = historyData.RecipientNameAfterAdjustment
		res.RecipientPhoneAfterAdjustment = historyData.RecipientPhoneAfterAdjustment
		res.RecipientAddressAfterAdjustment = historyData.RecipientAddressAfterAdjustment
		res.RecipientAddressTypeAfterAdjustment = historyData.RecipientAddressTypeAfterAdjustment
		res.DestinationCityIDAfterAdjustment = historyData.DestinationCityIDAfterAdjustment
		res.DestinationDistrictIDAfterAdjustment = historyData.DestinationDistrictIDAfterAdjustment
		res.CommodityCodeAfterAdjustment = historyData.CommodityCodeAfterAdjustment
		res.PiecePerPackAfterAdjustment = historyData.PiecePerPackAfterAdjustment

	case data.SttBookedForType == model.CLIENT:
		res = &model.HistoryDataAdjustment{
			SenderNameBeforeAdjustment:            historyData.SenderNameBeforeAdjustment,
			SenderPhoneBeforeAdjustment:           historyData.SenderPhoneBeforeAdjustment,
			SenderAddressBeforeAdjustment:         historyData.SenderAddressBeforeAdjustment,
			RecipientNameBeforeAdjustment:         historyData.RecipientNameBeforeAdjustment,
			RecipientPhoneBeforeAdjustment:        historyData.RecipientPhoneBeforeAdjustment,
			RecipientAddressBeforeAdjustment:      historyData.RecipientAddressBeforeAdjustment,
			RecipientAddressTypeBeforeAdjustment:  historyData.RecipientAddressTypeBeforeAdjustment,
			DestinationCityIDBeforeAdjustment:     historyData.DestinationCityIDBeforeAdjustment,
			DestinationDistrictIDBeforeAdjustment: historyData.DestinationDistrictIDBeforeAdjustment,
			CommodityCodeBeforeAdjustment:         historyData.CommodityCodeBeforeAdjustment,
			GoodsStatusBeforeAdjustment:           historyData.GoodsStatusBeforeAdjustment,
			TaxNumberBeforeAdjustment:             historyData.TaxNumberBeforeAdjustment,
			PieceGrossWeightBeforeAdjustment:      pieceDetail.SttPieceGrossWeightBeforeAdjustment,
			PieceHeightBeforeAdjustment:           pieceDetail.SttPieceHeightBeforeAdjustment,
			PieceWidthBeforeAdjustment:            pieceDetail.SttPieceWidthBeforeAdjustment,
			PieceLengthBeforeAdjustment:           pieceDetail.SttPieceLengthBeforeAdjustment,

			SenderNameAfterAdjustment:            historyData.SenderNameAfterAdjustment,
			SenderPhoneAfterAdjustment:           historyData.SenderPhoneAfterAdjustment,
			SenderAddressAfterAdjustment:         historyData.SenderAddressAfterAdjustment,
			RecipientNameAfterAdjustment:         historyData.RecipientNameAfterAdjustment,
			RecipientPhoneAfterAdjustment:        historyData.RecipientPhoneAfterAdjustment,
			RecipientAddressAfterAdjustment:      historyData.RecipientAddressAfterAdjustment,
			RecipientAddressTypeAfterAdjustment:  historyData.RecipientAddressTypeAfterAdjustment,
			DestinationCityIDAfterAdjustment:     historyData.DestinationCityIDAfterAdjustment,
			DestinationDistrictIDAfterAdjustment: historyData.DestinationDistrictIDAfterAdjustment,
			CommodityCodeAfterAdjustment:         historyData.CommodityCodeAfterAdjustment,
			GoodsStatusAfterAdjustment:           historyData.GoodsStatusAfterAdjustment,
			TaxNumberAfterAdjustment:             historyData.TaxNumberAfterAdjustment,
			PieceGrossWeightAfterAdjustment:      pieceDetail.SttPieceGrossWeightAfterAdjustment,
			PieceHeightAfterAdjustment:           pieceDetail.SttPieceHeightAfterAdjustment,
			PieceWidthAfterAdjustment:            pieceDetail.SttPieceWidthAfterAdjustment,
			PieceLengthAfterAdjustment:           pieceDetail.SttPieceLengthAfterAdjustment,
		}
		if isAfterPOD && req.AccountType == model.INTERNAL && model.IsAllowToEditInternalPOD[data.SttLastStatusID] {
			res.ProductNameBeforeAdjustment = historyData.ProductNameBeforeAdjustment

			res.ProductNameAfterAdjustment = historyData.ProductNameAfterAdjustment
		}

	case (shared.IsShipmentIDFormat(shipment) && !isAfterPOD) || (isAfterPOD && data.SttBookedForType == model.CLIENT && model.IsShipmentMarketplace[shipment]):
		res = &model.HistoryDataAdjustment{
			PieceGrossWeightBeforeAdjustment: pieceDetail.SttPieceGrossWeightBeforeAdjustment,
			PieceHeightBeforeAdjustment:      pieceDetail.SttPieceHeightBeforeAdjustment,
			PieceWidthBeforeAdjustment:       pieceDetail.SttPieceWidthBeforeAdjustment,
			PieceLengthBeforeAdjustment:      pieceDetail.SttPieceLengthBeforeAdjustment,

			PieceGrossWeightAfterAdjustment: pieceDetail.SttPieceGrossWeightAfterAdjustment,
			PieceHeightAfterAdjustment:      pieceDetail.SttPieceHeightAfterAdjustment,
			PieceWidthAfterAdjustment:       pieceDetail.SttPieceWidthAfterAdjustment,
			PieceLengthAfterAdjustment:      pieceDetail.SttPieceLengthAfterAdjustment,
		}

	}

	res.IsPromo = historyData.IsPromo
	res.TotalTariff = historyData.TotalTariff
	res.TotalTariffAfterDiscount = historyData.TotalTariffAfterDiscount

	return res
}

func (c *sttCtx) ValidateStatusIncomingLILO(ctx context.Context, params *stt.ValidateShipmentLiloRequest) (*stt.ValidateShipmentLiloResponse, error) {
	opName := "UsecaseStt-ValidateStatusIncomingLILO"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var errors error
	res := new(stt.ValidateShipmentLiloResponse)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": shared.CheckErrorNil(errors)})
	}()

	sttMeta := params.SttMetaToStruct()

	if params.SttLastStatusID != model.BAGGING ||
		!shared.IsLiloPrefix(params.SttNoRefExternal) ||
		!strings.HasPrefix(params.SttNo, model.PrefixClient) ||
		sttMeta == nil || !sttMeta.IsSttCrossdocking {
		res.IsValid = true
		return res, nil
	}

	sttHistory, err := c.sttPieceHistoryRepo.Select(selfCtx, &model.SttPieceHistoryViewParam{
		SttPieceHistorySttPieceID: params.SttPieceID,
		Order:                     true,
	})
	if err != nil {
		errors = err
		return nil, shared.ERR_UNEXPECTED_DB
	}

	if len(sttHistory) < 1 {
		return nil, shared.NewMultiStringValidationError(shared.HTTPErrorNotFound, map[string]string{
			"en": "Stt history Not Found",
			"id": "Stt history tidak ditemukan",
		})
	}

	isBaggingAfterBkd := false
	lastStatus := ""
	for _, data := range sttHistory {
		isBaggingAfterBkd = false
		if !model.IsStatusbeforeSTI[data.HistoryStatus] {
			break
		}
		if data.HistoryStatus == model.BAGGING && lastStatus == model.BKD {
			isBaggingAfterBkd = true
		}
		lastStatus = data.HistoryStatus
	}
	res.IsBaggingAfterBkd = isBaggingAfterBkd

	if isBaggingAfterBkd && model.IsAllowUpdateShipmentStatusLilo[params.UpdateStatus] {
		res.IsValid = true
	}

	return res, nil
}

func (c *sttCtx) OrchestraCreateBaggingPublish(ctx context.Context, params *stt.CreateSttManualForClient) error {

	var (
		opName  = "sttCtx-OrchestraCreateBaggingPublish"
		trace   = tracer.StartTrace(ctx, opName)
		result  string
		selfCtx = trace.Context()
		message []byte
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{
			"param":  params,
			"result": result,
		})
	}()

	if !shared.IsBagNoReff(params.Stt.SttNoRefExternal) {
		return nil
	}

	defer func() {
		go c.partnerLog.Insert(context.Background(), &model.PartnerLog{
			Action:   model.PLOrchestraCreateBagging,
			RefID:    params.BagLilo.BagVendorNo,
			Request:  params,
			Response: result,
		})

	}()

	message, err = json.Marshal(params.BagLilo)
	if err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed parse to JSON",
			"id": "Gagal memparsing ke JSON",
		})
	}

	topicID := shared.GeneratePubsubTopicID(c.cfg.OrchestraCrossDockingTopicID(), c.cfg.Environment(), shared.PubSubGenesis)
	result, err = c.Pubsub.PublishMessage(context.Background(), topicID, &pubsub.PubSubMessage{
		Message:               string(message),
		OrderingKey:           params.BagLilo.BagVendorNo,
		EnableMessageOrdering: true,
		Attributes:            model.AttributesPubSubOrchestraCreatebagging,
	})
	if err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to publish message refund cancel",
			"id": "Gagal menerbitkan pesan refund cancel",
		})
	}

	return nil
}

func (c *sttCtx) generateEstimaeSlaByProductType(estimateSLA, productType string, now time.Time, timezone string) string {

	// Split Estimate SLA
	estimateSlaMin := ""
	estimateSlaMax := ""
	estimateSLA = strings.Replace(estimateSLA, "working", "", -1)
	trimEstimateSlString := strings.Replace(estimateSLA, " ", "", -1)
	trimEstimateSlString = strings.TrimSpace(trimEstimateSlString)
	splitEstimateSlaTrim := strings.Split(trimEstimateSlString, "-")
	if len(splitEstimateSlaTrim) > 1 {
		estimateSlaMin = splitEstimateSlaTrim[0]
		estimateSlaMax = strings.Replace(splitEstimateSlaTrim[1], "Hari", "", -1)
	}

	if estimateSlaMin == "" {
		return estimateSLA
	}

	// Check Estimate SLA for ONEPACK
	if model.IsAllowGenerateEstimateSLAProductType[productType] {
		timezoneLocation, ok := model.TimeZoneToLocation[timezone]
		if !ok {
			timezoneLocation = model.TimeAsiaJakarta
		}
		localLocation, _ := time.LoadLocation(timezoneLocation)
		syyyy, smm, sdd := now.Date()
		customStartTimeFormated := time.Date(syyyy, smm, sdd, 17, 0, 0, 0, localLocation)
		if now.After(customStartTimeFormated) {
			estimateSlaMaxInt, _ := strconv.Atoi(estimateSlaMax)
			estimateSlaMax = fmt.Sprint(estimateSlaMaxInt + 1)
		}
	}

	return fmt.Sprintf("%s - %s Hari", estimateSlaMin, estimateSlaMax)
}

func (c *sttCtx) isAllowedToRelabel(sttNo string) bool {
	configShortlink := c.cfg.GetAssessmentRelabel()

	prefixStt := shared.GetPrefixSttNo(sttNo)
	for _, prefix := range configShortlink.SttPrefixAllowed {
		if strings.HasPrefix(fmt.Sprintf("%sLP", prefixStt), prefix) {
			return true
		}
	}
	return false
}

func (c *sttCtx) checkPrioritySubscription(shipmentPrefix string, packet model.Packet) bool {
	sspConfig := c.cfg.GetShipmentSubscriptionPriorities()
	sspdpConfig := c.cfg.GetShipmentSubscriptionPriorityDeliveryPrefixes()
	if !sspdpConfig[shipmentPrefix] {
		return false
	}
	if !sspConfig["PRIORITY_DELIVERY"] {
		return false
	}
	if packet.Subscription == nil {
		return false
	}
	for _, benefitFlag := range packet.Subscription.BenefitFlag {
		if sspConfig[benefitFlag] {
			return true
		}
	}
	return false
}

type paramPopulateCreateShipmentByGroupID struct {
	DataShipment    model.Packet
	Data            *model.ShipmentAlgo
	ShipmentPackets []model.ShipmentPacket
	Req             *stt.ViewDetailShipmentAlgoRequest
}

func populateShipmentDataCreateShipmentByGroupBookingID(params paramPopulateCreateShipmentByGroupID) *model.Shipment {
	shipmentMeta := model.ShipmentMeta{
		TotalShipmentGroup: len(params.Data.Packets),
	}
	shipmentMetaStr := shipmentMeta.ToString()
	shipment := &model.Shipment{
		ShipmentAlgoID:          params.DataShipment.ShipmentID,
		ShipmentShipmentID:      params.DataShipment.ShipmentID,
		ShipmentAlgoNumberOfStt: 1, // only one stt for one shipment group booking id
		ShipmentAlgoAgentCode:   params.Data.AgentCode,
		ShipmentPackets:         params.ShipmentPackets,
		ShipmentGroupBookingID:  params.Req.ShipmentID, // value shipment group booking id from query params
		ShipmentCreatedAt:       params.Req.Now,
		ShipmentUpdatedAt:       params.Req.Now,
		ShipmentMeta:            &shipmentMetaStr,
	}
	return shipment
}

func IsAllowUpdateToDEL(ctx context.Context, r repository.SttRepository, rule map[string]bool, stt model.Stt) bool {
	var (
		opName             = "IsAllowUpdateToDEL"
		trace              = tracer.StartTrace(ctx, opName)
		selfCtx            = trace.Context()
		isAllowUpdateToDEL bool
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{
			"param":  stt,
			"result": isAllowUpdateToDEL,
		})
	}()

	isAllowUpdateToDEL = rule[stt.SttLastStatusID]
	if isAllowUpdateToDEL {
		return isAllowUpdateToDEL
	}

	mustBeCheckedEverBeenInStiDest := map[string]bool{
		model.INHUB:  true,
		model.OUTHUB: true,
	}

	if _, ok := mustBeCheckedEverBeenInStiDest[stt.SttLastStatusID]; ok {
		isAllowUpdateToDEL = IsSttEverBeenInStiDest(selfCtx, r, stt.SttNo)
		return isAllowUpdateToDEL
	}

	return isAllowUpdateToDEL
}

func IsSttEverBeenInStiDest(ctx context.Context, r repository.SttRepository, sttNo string) bool {
	var (
		opName                 = "IsSttEverBeenInStiDest"
		trace                  = tracer.StartTrace(ctx, opName)
		selfCtx                = trace.Context()
		isSttEverBeenInStiDest bool
		err                    error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{
			"param":  sttNo,
			"result": isSttEverBeenInStiDest,
			"error":  err,
		})
	}()

	isSttEverBeenInStiDest, err = r.IsSttEverBeenInStiDest(selfCtx, sttNo)

	return isSttEverBeenInStiDest
}

func (c *sttCtx) handlePrioritySubscription(prefix string, packets []model.Packet, shipment *model.Shipment) {
	if len(packets) == 0 {
		return
	}
	if c.checkPrioritySubscription(prefix, packets[0]) {
		shipmentMeta := shipment.ShipmentMetaToStruct()
		if shipmentMeta == nil {
			shipmentMeta = &model.ShipmentMeta{}
		}
		shipmentMeta.PrioritySubscription = true
		shipmentMetaStr := shipmentMeta.ToString()
		shipment.ShipmentMeta = &shipmentMetaStr
	}
}

func (c *sttCtx) CheckSTTFromCARetailFromRoot(ctx context.Context, sttMeta *model.SttMeta) (bool, string, error) {
	opName := "UsecaseStt-CheckSTTFromCARetailFromMeta"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	var (
		shipmentPrefix, sttReverse, sttNo, shipmentId string
		isValid                                       bool
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"sttMeta": sttMeta, "shipmentPrefix": shipmentPrefix, "sttReverse": sttReverse, "sttNo": sttNo, "shipmentId": shipmentId})
	}()

	if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil {
		// get STT root

		sttNo = sttMeta.DetailSttReverseJourney.RootReverseSttNo
		shipmentId = sttMeta.DetailSttReverseJourney.RootReverseShipmentID
		billedTo := ""

		if len(shipmentId) < 1 && len(sttNo) > 0 {
			sttData, er := c.sttRepo.Get(ctx, &model.SttViewDetailParams{
				Stt: model.Stt{
					SttNo: sttMeta.DetailSttReverseJourney.RootReverseSttNo,
				},
			})
			if er != nil {
				return false, "", shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Failed to get data root STT",
					"id": "Gagal mendapatkan data awal STT",
				})
			}

			sttNo = sttData.SttNo
			shipmentId = sttData.SttShipmentID
			billedTo = sttData.SttBilledTo
		}

		if shipmentId != `` {
			shipmentPrefix = shared.GetPrefixShipmentID(shipmentId)
		}

		isFromCA := model.MappingShipmentPrefixCustomerName[shipmentPrefix] == model.CUSTOMERAPPS || billedTo == "CUSTOMER APPS"
		isFromRetail := model.IsPrefixSttRetail[sttNo[:2]]

		isValid = isFromCA || isFromRetail
	}

	return isValid, shipmentPrefix, nil
}
