package sti_sc

import (
	"fmt"
	"time"

	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/Lionparcel/hydra/shared/excel"
	"github.com/Lionparcel/hydra/src/model"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/usecase/general"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

// DetailStiScRequest ...
type DetailStiScRequest struct {
	StiScID     int    `json:"sti_sc_id"`
	PartnerID   int    `json:"partner_id"`
	PartnerType string `json:"partner_type"`
	Token       string `json:"token"`
}

// DetailStiScResponse ...
type DetailStiScResponse struct {
	StiPartnerID     int                    `json:"sti_partner_id"`
	StiPartnerName   string                 `json:"sti_partner_name"`
	StiCityName      string                 `json:"sti_city_name"`
	StiCityCode      string                 `json:"sti_city_code"`
	StiTotalPieces   int                    `json:"sti_total_pieces"`
	StiTotalStt      int                    `json:"sti_total_stt"`
	StiGrossWeight   float64                `json:"sti_total_gross_weight"`
	StiVolumeWeight  float64                `json:"sti_total_volume_weight"`
	StiCreatedAt     time.Time              `json:"sti_created_at"`
	NeedToStiScTotal int                    `json:"need_to_sti_sc_total"`
	StiDetail        []*general.SttResponse `json:"stt"`
}

type StiDetailResponse struct {
	No             int     `json:"no"`
	STTNumber      string  `json:"stt_number"`
	STTPieceID     int     `json:"stt_piece_id"`
	STTPieceNumber string  `json:"stt_piece_number"`
	Product        string  `json:"product"`
	TotalPieces    string  `json:"total_pieces"`
	GrossWeight    float64 `json:"gross_weight"`
	VolumeWeight   float64 `json:"volume_weight"`
	LastStatus     string  `json:"last_status"`
	Note           string  `json:"note"`
}

// Validate ...
func (c *DetailStiScRequest) Validate() error {
	if err := validation.Validate(c.StiScID, validation.Min(1), validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "STI-SC ID must be field",
			"id": "STI-SC ID tidak boleh kosong",
		})
	}
	return nil
}

type StiScDetailSttDueParams struct {
	ManifestID  int    `json:"sti_scid"`
	BookedType  string `json:"booked_type" query:"booked_type"`
	BookedID    int    `json:"booked_id" query:"booked_id"`
	PartnerID   int    `json:"partner_id"`
	PartnerType string `json:"partner_type"`
	AccountID   int    `json:"account_id"`
	AccountType string `json:"account_type"`
	Token       string `json:"token"`
}

func (c *StiScDetailSttDueParams) Validate() error {
	if err := validation.Validate(c.ManifestID, validation.Min(1), validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "STI-SC ID must be field",
			"id": "STI-SC ID tidak boleh kosong",
		})
	}

	if err := validation.Validate(c.PartnerType, validation.In(model.SUBCONSOLE)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Partner Type must be Sub-Console",
			"id": "Partner Type harus Sub-Console",
		})
	}
	return nil
}

type StiScDetailSttDueFile struct {
	BaggingNumber string  `json:"bagging_number" xlsx:"-"`
	STTNo         string  `json:"stt_no" xlsx:"Nomor STT"`
	RefNo         string  `json:"ref_no" xlsx:"Nomor Referensi"`
	ProductName   string  `json:"product_name" xlsx:"Nama Produk"`
	KoliTotal     int     `json:"koli_total" xlsx:"Total Koli"`
	Bruto         float64 `json:"bruto" xlsx:"Total Berat Kotor"`
	City          string  `json:"city" xlsx:"Kota Tujuan"`
	POSName       string  `json:"pos_name" xlsx:"Nama POS/Klien"`
	BookingDate   string  `json:"booking_date" xlsx:"Tanggal Booking"`
	ManifestDate  string  `json:"manifest_date" xlsx:"Tanggal Manifest"`
}

type StiScDetailSttDueResponse struct {
	Data     []StiScDetailSttDueFile
	File     *excelize.File `json:"file"`
	FileName string         `json:"file_name"`
}

func (r *StiScDetailSttDueResponse) ParseData(data ...model.STISCData) *StiScDetailSttDueResponse {
	stiScDetailSttDueFile := make([]StiScDetailSttDueFile, 0)
	for _, v := range data {
		stiScDetailSttDueFile = append(stiScDetailSttDueFile, StiScDetailSttDueFile{
			BaggingNumber: v.SdBagNo,
			STTNo:         v.SdSttNo,
			RefNo:         v.SdRefNo,
			ProductName:   v.SttProductType,
			KoliTotal:     v.SttTotalPiece,
			Bruto:         v.SttGrossWeight,
			City:          v.DestinationCity,
			POSName:       v.SdBookedName,
			BookingDate:   v.SdSttBookedAt.Format("02-01-2006 15:04:05") + " WIB",
			ManifestDate:  v.StiCreatedAt.Format("02-01-2006 15:04:05") + " WIB",
		})
	}
	r.Data = stiScDetailSttDueFile
	return r
}

func (r *StiScDetailSttDueResponse) ToFile() (*StiScDetailSttDueResponse, error) {
	if len(r.Data) == 0 {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "No data to export to excel file",
			"id": "Tidak ada data untuk diekspor ke file excel",
		})
	}

	r.FileName = fmt.Sprintf("STI-SC-%s.xlsx", time.Now().Format("20060102150405"))
	file, err := excel.GenerateFileXLS(r.Data, excel.GenerateFileExcelOption{
		SheetName:         "STT belum di STI SC",
		FontStyle:         excel.FontStyleBlackBold,
		WidthColumn:       22,
		IsFontStyleHeader: true,
		Filename:          r.FileName,
		DeleteSheetName:   excel.DefaultSheetName,
	})

	r.File = file
	return r, err
}

type StiSCTemporaryListParams struct {
	SttNo       string `json:"stt_no" query:"stt_no"`
	PartnerID   int    `json:"partner_id" query:"partner_id"`
	PartnerType string `json:"partner_type" query:"partner_type"`
	AccountID   int64  `json:"account_id" query:"account_id"`
	AccountType string `json:"account_type" query:"account_type"`
	BookedType  string `json:"booked_type" query:"booked_type"`
	BookedID    int    `json:"booked_id" query:"booked_id"`
	Token       string `json:"token"`
}

func (r *StiSCTemporaryListParams) Validate() error {
	if err := validation.Validate(r.PartnerType, validation.In(model.SUBCONSOLE)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Partner Type must be Sub-Console",
			"id": "Partner Type harus Sub-Console",
		})
	}
	return nil
}

type StiSCTemporaryList struct {
	ID          int     `json:"sst_id"`
	AccountID   int     `json:"sst_account_id"`
	IsActive    int     `json:"sst_is_active"`
	SttNo       string  `json:"sst_stt_no"`
	RefNo       string  `json:"sst_ref_no"`
	Product     string  `json:"sst_product"`
	Origin      string  `json:"sst_origin"`
	Destination string  `json:"sst_destination"`
	PodDate     string  `json:"sst_pod_date"`
	GrossWeight float64 `json:"sst_gross_weight"`
	TotalPiece  int     `json:"sst_total_piece"`
	IsPaid      int     `json:"sst_is_paid"`
	BookedType  string  `json:"sst_booked_type"`
	BookedID    int     `json:"sst_booked_id"`
	BookedName  string  `json:"sst_booked_name"`
	CreatedAt   string  `json:"sst_created_at"`
	UpdatedAt   string  `json:"sst_updated_at"`

	SstMeta model.StiScTemporaryMeta `json:"sst_meta"`
}

type StiSCTemporaryListResponse struct {
	Data []StiSCTemporaryList `json:"data"`
}

func (r *StiSCTemporaryListResponse) ParseData(data ...model.STISCTemporary) *StiSCTemporaryListResponse {
	stiSCTemporaryList := make([]StiSCTemporaryList, 0)
	for _, v := range data {
		sstList := StiSCTemporaryList{
			ID:          v.ID,
			AccountID:   v.AccountID,
			IsActive:    v.IsActive,
			SttNo:       v.SttNo,
			RefNo:       v.RefNo,
			Product:     v.Product,
			Origin:      v.Origin,
			Destination: v.Destination,
			PodDate:     v.PodDate,
			GrossWeight: v.GrossWeight,
			TotalPiece:  v.TotalPiece,
			IsPaid:      v.IsPaid,
			BookedType:  v.BookedType,
			BookedID:    v.BookedID,
			BookedName:  v.BookedName,
			CreatedAt:   v.CreatedAt,
			UpdatedAt:   v.UpdatedAt,
		}

		sstMeta := v.MetaToStruct()
		if sstMeta != nil {
			sstList.SstMeta = *sstMeta
		}

		if sstList.SstMeta.StatusReturn == "" {
			sstList.SstMeta.StatusReturn = "-"
		}

		stiSCTemporaryList = append(stiSCTemporaryList, sstList)
	}
	r.Data = stiSCTemporaryList
	return r
}
