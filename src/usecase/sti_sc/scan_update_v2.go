package sti_sc

import (
	"strings"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
)

type (
	ScanUpdateV2Request struct {
		SttNo string `json:"stt_no" form:"stt_no"`

		Token       string
		AccountID   int64
		AccountName string
		PartnerID   int
		PartnerType string
		PartnerName string
	}

	ScanUpdateV2Response struct {
		SttNo               string    `json:"stt_no"`
		RefNo               string    `json:"ref_no"`
		ProductType         string    `json:"product_type"`
		BookedID            int       `json:"booked_id"`
		BookedType          string    `json:"booked_type"`
		BookedName          string    `json:"booked_name"`
		PodDate             time.Time `json:"pod_date"`
		TotalGrossWeight    float64   `json:"total_gross_weight"`
		TotalVolumeWeight   float64   `json:"total_volume_weight"`
		TotalPieces         int       `json:"total_pieces"`
		DestinationCityCode string    `json:"destination_city_code"`
		Status              string    `json:"status"`
		IsPaid              bool      `json:"is_paid"`
		OldStatus           string    `json:"old_status"`
		StatusReturn        string    `json:"status_return"`
	}

	CreateStiScDetailV2Params struct {
		SttID          int64
		SttPieceIDs    []int64
		History        []model.SttPieceHistory
		StiScTemporary *model.StiScTemporary
	}
)

func (c *ScanUpdateV2Request) Validate() error {

	c.SttNo = strings.ReplaceAll(c.SttNo, " ", "")

	if len(c.SttNo) == 0 {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "stt_no should be not empty",
			"id": "stt_no tidak boleh kosong",
		})
	}

	if strings.ToLower(c.PartnerType) != model.SUBCONSOLE {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid Account Type format",
			"id": "Format Account Type tidak valid",
		})
	}
	return nil
}
