package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/Lionparcel/go-lptool/v2/lputils"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	customProcess "github.com/Lionparcel/hydra/src/usecase/custom_process"
	"github.com/Lionparcel/hydra/src/usecase/gateway_stt"
	"github.com/Lionparcel/hydra/src/usecase/gateway_stt_status"
	"github.com/Lionparcel/hydra/src/usecase/general"
	"github.com/Lionparcel/hydra/src/usecase/release"
	"github.com/Lionparcel/hydra/src/usecase/stt"
	"github.com/Lionparcel/hydra/src/usecase/stt_activity"
	"github.com/Lionparcel/hydra/src/usecase/stt_piece_history_remark_helper"
	"github.com/abiewardani/dbr/v2"
)

func (c *customProcessCtx) CreateCustomProcess(ctx context.Context, form *customProcess.CreateCustomProcessRequest) (*customProcess.CreateCustomProcessResponse, error) {
	var (
		opName  = "customProcessCtx-CreateCustomProcess"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		sttNoElexys         = []string{}
		remarksPieceHistory model.RemarkPieceHistory
		response            = new(customProcess.CreateCustomProcessResponse)
		reasonTitle         = ``
		reasonCode          string
		err                 error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": response, "error": err})
	}()
	if err := form.Validate(); err != nil {
		return nil, err
	}

	if err = ValidateSttFtz(&ValidateSttFtzParams{
		SttFtzCIPL:           form.SttFtzCIPL,
		SttFtzIdentityNumber: form.SttFtzIdentityNumber,
		SttFtzRecipientEmail: form.SttFtzRecipientEmail,
	}); err != nil {
		return nil, err
	}

	if err = ValidateSttFtz(&ValidateSttFtzParams{
		SttFtzCIPL:           form.ReverseDestination.SttFtzCIPL,
		SttFtzIdentityNumber: form.ReverseDestination.SttFtzIdentityNumber,
		SttFtzRecipientEmail: form.ReverseDestination.SttFtzRecipientEmail,
	}); err != nil {
		return nil, err
	}

	if form.PartnerType == model.CONSOLE || form.PartnerType == model.SUBCONSOLE {
		form.AccountRoleName = form.PartnerType
	}

	err = c.validateStatusCustomProcessCreate(selfCtx, form)
	if err != nil {
		return nil, err
	}

	sttNoIsSuccess := map[string]model.Stt{}
	for _, sttNo := range form.SttNo {
		if _, ok := sttNoIsSuccess[sttNo]; !ok {
			sttNoIsSuccess[sttNo] = model.Stt{}
		}
	}

	if err = c.checkIsAllowedEditCustomProcess(selfCtx, form); err != nil {
		return nil, err
	}

	if !model.IsEligibleSttUnpaidUpdateCustomProcess[form.CustomProcessStatus] {
		err = c.sttPaymentUc.SttPaymentValidation(selfCtx, form.SttNo)
		if err != nil {
			return nil, err
		}
	}

	sttDetails, err := c.sttPieceRepo.SelectDetail(selfCtx, &model.SttPiecesViewParam{
		ListSttNo: form.SttNo, IsNeedDecrypt: true,
	})

	if len(sttDetails) < 1 || err != nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt is not found",
			"id": "Stt tidak ditemukan",
		})
	}

	reasonDescription, err := c.getReasonDescriptionForHALCD(selfCtx, form)
	if err != nil {
		return nil, err
	}

	for i := 0; i < len(sttDetails); i++ {
		// Cross docking validation
		sttMeta := sttDetails[i].Stt.SttMetaToStruct()
		isSttCrossdocking := sttMeta != nil && sttMeta.IsSttCrossdocking
		isLastStatusBAGGING := sttDetails[i].Stt.SttLastStatusID == model.BAGGING
		isNotSCRAPCDorHALCD := (form.CustomProcessStatus != model.SCRAPCD && form.CustomProcessStatus != model.HALCD)
		canProcess := isSttCrossdocking && isLastStatusBAGGING && isNotSCRAPCDorHALCD
		if canProcess {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt No Cannot Be Proccess",
				"id": "Nomor STT atau Nomor Bagging tidak dapat diproses",
			})
		}

		if sttDetails[i].Stt.SttLastStatusID == model.CNXCD {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt Cannot Be Processed Cause CNXCD status is the final status",
				"id": "Nomor STT tidak dapat diproses Karena status CNXCD adalah status akhir",
			})
		}

		if form.CustomProcessStatus == model.CNXCD {
			if sttDetails[i].Stt.SttNoRefExternal == `` || !(sttDetails[i].Stt.SttNoRefExternal != `` && shared.IsLiloPrefix(sttDetails[i].Stt.SttNoRefExternal)) {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Stt No Cannot Be Proccess Cause STT is not TKP01",
					"id": "Nomor STT tidak dapat diproses karena STT bukan TKP01",
				})
			}

			if sttDetails[i].Stt.SttLastStatusID != model.HALCD {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Stt No Cannot Be Proccess Cause last status not HALCD",
					"id": "Nomor STT tidak dapat diproses karena status akhir bukan HALCD",
				})
			}
		}

		if form.CustomProcessStatus == model.HALCD {
			prefixStt := sttDetails[i].Stt.SttNo[:2]
			if !model.IsSttAllowUpdateHALCD[prefixStt] || (sttDetails[i].Stt.SttNoRefExternal == `` || !(sttDetails[i].Stt.SttNoRefExternal != `` && shared.IsLiloPrefix(sttDetails[i].Stt.SttNoRefExternal))) {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Stt Cannot Be Proccess",
					"id": "Nomor STT tidak dapat diproses",
				})
			}

			if !(sttDetails[i].Stt.SttLastStatusID == model.BKD || sttDetails[i].Stt.SttLastStatusID == model.BAGGING) {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Stt No Cannot Be Proccess Cause last status not BKD or Bagging",
					"id": "Nomor STT tidak dapat diproses karena status akhir bukan BKD atau Bagging",
				})
			}
		}

	}

	if err != nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting STT",
			"id": "Terjadi kesalahan pada saat getting STT",
		})
	}

	customProcessPartner := new(model.Partner)
	if form.PartnerID > 0 {
		customProcessPartner, err = c.partnerRepo.GetByID(selfCtx, form.PartnerID, form.Token)
		if err != nil || customProcessPartner == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner is not found",
				"id": "Partner tidak ditemukan",
			})
		}

		if customProcessPartner.Data.PartnerLocation == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner is not yet mapped with location",
				"id": "Partner belum dipetakan dengan lokasi",
			})
		}
		form.PartnerName = customProcessPartner.Data.Name
		form.PartnerType = customProcessPartner.Data.Type
	}

	partnerBookedBy := new(model.Partner)
	// TODO: this validation just for partner on indonesia zone
	err = c.processPartnerBookedBy(selfCtx, form, customProcessPartner, partnerBookedBy)
	if err != nil {
		return nil, err
	}

	// IsSttReturnForRtsRtshq = Rts, Rtshq, dan reroute
	isReverseJourney := c.cfg.ConfigReverseRtsJourney() && model.IsSttReturnForRtsRtshq[form.CustomProcessStatus]
	isNotBannedPartner := customProcessPartner != nil && !customProcessPartner.IsBannedForReverseJourney()
	if isReverseJourney && !isNotBannedPartner {
		form.Remarks = ""
	}

	mapUniqueStt := make(map[string]bool)
	sttData := map[string]customProcess.SttData{}
	now, _ := shared.ParseUTC7(shared.FormatDateTime, time.Now().Format(shared.FormatDateTime))
	sttHistories := []customProcess.SttHistory{}
	for _, sttDetail := range sttDetails {

		sttMeta := sttDetail.SttMetaToStruct()
		if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil {
			isUpdateSttToRtsRtshq := form.CustomProcessStatus == sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt || (form.CustomProcessStatus == model.RTS && sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt == model.RTSHQ)
			if isUpdateSttToRtsRtshq {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Cannot update stt",
					"id": "Gagal update stt",
				})
			}

		}

		codRetailSttOrigin := false
		isReverseJourney := sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseSttNo != ""
		if isReverseJourney {
			sttDetailOrigins, err := c.sttPieceRepo.SelectDetail(selfCtx, &model.SttPiecesViewParam{
				SttNo: sttMeta.DetailSttReverseJourney.RootReverseSttNo,
			})

			if err != nil || len(sttDetailOrigins) == 0 {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "An error occurred while getting STT Origin",
					"id": "Terjadi kesalahan pada saat getting STT Origin",
				})
			}
			sttOrigin := sttDetailOrigins[0]
			codRetailSttOrigin = (sttOrigin.Stt.SttIsCOD && sttOrigin.Stt.SttBookedForType == model.POS && sttOrigin.Stt.SttBookedByType == model.POS)
		}
		/* As a user Consol, I want not to be able to update STT COD Retail to RTSHQ
		As a user Consol, I want not to be able to update STT RTS COD Retail to RTSHQ
		As a user Consol, I want not to be able to update STT Reroute COD Retail to RTSHQ */
		checkPartner := form.PartnerType == model.CONSOLE && form.CustomProcessStatus == model.RTSHQ && ((sttDetail.Stt.SttIsCOD && sttDetail.Stt.SttBookedForType == model.POS && sttDetail.Stt.SttBookedByType == model.POS) || codRetailSttOrigin)
		if checkPartner {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Cannot update stt",
				"id": "Gagal update stt",
			})
		}

		// get shipment id for stt origin
		sttShipmentID := ""
		if sttDetail.SttShipmentID != "" {
			sttShipmentID = sttDetail.SttShipmentID
		} else if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` {
			sttShipmentID = sttMeta.DetailSttReverseJourney.RootReverseShipmentID
		}

		// Shipment C1 specialcod not able to updated RTSHQ
		if form.CustomProcessStatus == model.RTSHQ && sttShipmentID != `` && (shared.GetPrefixShipmentID(sttShipmentID) == model.C1 || shared.GetPrefixShipmentID(sttShipmentID) == model.C2) {
			codHandling := ""
			if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseCodHandling != `` {
				codHandling = sttMeta.DetailSttReverseJourney.RootReverseCodHandling
			}

			if codHandling == "" {
				shipmentData, err := c.shipmentRepo.GetShipment(selfCtx, &model.ShipmentViewParams{
					ShipmentAlgoID: sttShipmentID,
				})
				if err != nil || shipmentData == nil {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "An error occurred while getting detail shipment",
						"id": "Terjadi kesalahan pada saat mendapatkan detail shipment",
					})
				}

				shipmentMeta := shipmentData.ShipmentMetaToStruct()
				if shipmentData.ShipmentMeta != nil && shipmentMeta != nil {
					codHandling = shipmentMeta.CodHandling
				}
			}

			if codHandling == model.SPECIALCOD {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Cannot update stt",
					"id": "Gagal update stt",
				})
			}
		}

		if form.CustomProcessStatus == model.REROUTE && !model.IsAllowUpdateReroute[sttDetail.SttLastStatusID] {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Cannot update stt",
				"id": "Gagal update stt",
			})
		}

		if form.CustomProcessStatus == model.NOTRECEIVED && model.IsNotAllowNotReceived[sttDetail.SttLastStatusID] {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Cannot update stt",
				"id": "Gagal update stt",
			})
		}

		if form.CustomProcessStatus == model.RCCIMP && !release.ReleaseUpdateBulkLastStatusAllowed[sttDetail.SttLastStatusID] {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Cannot update stt",
				"id": "Gagal update stt",
			})
		}

		if sttDetail.SttLastStatusID == model.MISBOOKING {
			if sttDetail.SttUpdatedActorID.Int64 != int64(customProcessPartner.Data.ID) || sttDetail.SttUpdatedActorRole.String != form.PartnerType {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Cannot update stt",
					"id": "Gagal update stt",
				})
			}
		}

		// handle hanging DEL
		if ok := mapUniqueStt[sttDetail.SttNo]; !ok {
			mapUniqueStt[sttDetail.SttNo] = true
			if sttDetail.SttLastStatusID == model.DEL {
				dels, err := c.deliveryRepo.SelectDetail(selfCtx, &model.DeliveryViewParam{
					SttNo:                   sttDetail.SttNo,
					OrderBy:                 "delivery.id",
					SortBy:                  model.SortByDesc,
					FinishedStatusWhereNull: true,
				})
				if err != nil {
					return nil, err
				}
				if len(dels) <= 0 {
					err = shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
						"en": "STT is not found",
						"id": "STT tidak ditemukan",
					})
					return nil, err
				}

				if err = c.deliveryRepo.UpdateHangingDels(selfCtx, dels); err != nil {
					err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Delivery Hanging can't be disable",
						"id": "Delivery Hanging tidak bisa di disable",
					})
					return nil, err
				}
			}
		}

		sttNoElexys = append(sttNoElexys, sttDetail.SttElexysNo.Value())
		statusIsTheSame := sttDetail.Stt.SttLastStatusID == form.CustomProcessStatus && !model.IsAllowToTheSameStatus[form.CustomProcessStatus]
		if (model.IsNotAllowUpdateCustomProcess[sttDetail.Stt.SttLastStatusID] && !model.IsSttReturnForRtsRtshq[form.CustomProcessStatus]) || statusIsTheSame || sttDetail.Stt.SttLastStatusID == model.REROUTE {
			sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
			stt := customProcess.SttData{
				SttNo:       sttDetail.Stt.SttNo,
				SttElexysNo: sttDetail.SttElexysNo.Value(),
			}
			sttData[sttDetail.Stt.SttNo] = stt
			continue
		}

		if form.CustomProcessStatus == model.SCRAPCD {
			if !shared.IsBagNoReff(sttDetail.Stt.SttNoRefExternal) || !model.IsAllowUpdateCustomStatusScrapCD[sttDetail.Stt.SttLastStatusID] {
				sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
				stt := customProcess.SttData{
					SttNo:       sttDetail.Stt.SttNo,
					SttElexysNo: sttDetail.SttElexysNo.Value(),
				}
				sttData[sttDetail.Stt.SttNo] = stt
				continue
			}
		}

		// validation stt product type jumbopack h2h
		if sttDetail.Stt.SttProductType == model.JUMBOPACKH2H {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "The STT failed to scan because the package will be given POD status after it is received by the recipient",
				"id": "STT gagal discan karena paket akan diberi status POD setelah diambil oleh penerima.",
			})
		}

		// for interpack
		if model.IsForInterpack[form.CustomProcessStatus] {
			if !strings.EqualFold(sttDetail.Stt.SttProductType, model.INTERPACK) {
				sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
				stt := customProcess.SttData{
					SttNo:       sttDetail.Stt.SttNo,
					SttElexysNo: sttDetail.SttElexysNo.Value(),
					SttError:    "Hanya untuk jenis pengiriman INTERPACK. Cek & atur ulang",
				}
				sttData[sttDetail.Stt.SttNo] = stt
				continue
			} else {
				histories, err := c.sttPieceHistoryRepo.Select(selfCtx, &model.SttPieceHistoryViewParam{
					SttPieceHistorySttPieceID: sttDetail.SttPieceID,
					Order:                     true,
					OrderDesc:                 true,
				})
				isHistoriesNotFound := histories == nil || len(histories) == 0 || err != nil
				if isHistoriesNotFound {
					return nil, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
						"en": "STT Piece History is not found",
						"id": "STT Piece History tidak ditemukan",
					})
				}

				isHistoryContainInthnd := false
				for _, history := range histories {
					if history.HistoryStatus == model.INTHND {
						isHistoryContainInthnd = true
						break
					}
				}

				if isHistoryContainInthnd {
					sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
					stt := customProcess.SttData{
						SttNo:       sttDetail.Stt.SttNo,
						SttElexysNo: sttDetail.SttElexysNo.Value(),
						SttError:    `Status "INT - HND" ke "OCC - EXP/IMP/HAL" hanya bisa diperbarui oleh Vendor. Tunggu atau hubungi Vendor`,
					}
					sttData[sttDetail.Stt.SttNo] = stt
					continue
				}

				partnerCityCode := ""
				if customProcessPartner.Data.PartnerLocation != nil {
					partnerCityCode = customProcessPartner.Data.PartnerLocation.CityCode
				}

				parterCity, err := c.cityRepo.Get(selfCtx, partnerCityCode, form.Token)
				if parterCity == nil || err != nil {
					return nil, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
						"en": "Partner city not found",
						"id": "Partner city tidak ditemukan",
					})
				}

				sttOriginCity, err := c.cityRepo.Get(selfCtx, sttDetail.Stt.SttOriginCityID, form.Token)
				if sttOriginCity == nil || err != nil {
					return nil, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
						"en": "STT origin city not found",
						"id": "STT origin city tidak ditemukan",
					})
				}

				switch form.CustomProcessStatus {
				case model.OCCEXP:
					if parterCity.CountryID != sttOriginCity.CountryID || sttDetail.Stt.SttLastStatusID == model.OCCEXP || sttDetail.Stt.SttLastStatusID == model.OCCIMP {
						sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
						stt := customProcess.SttData{
							SttNo:       sttDetail.Stt.SttNo,
							SttElexysNo: sttDetail.SttElexysNo.Value(),
							SttError:    "Status tidak valid. Silahkan pilih status “OCC - IMP”",
						}
						sttData[sttDetail.Stt.SttNo] = stt
						continue
					}
				case model.OCCIMP:
					if parterCity.CountryID == sttOriginCity.CountryID || sttDetail.Stt.SttLastStatusID == model.OCCIMP {
						sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
						stt := customProcess.SttData{
							SttNo:       sttDetail.Stt.SttNo,
							SttElexysNo: sttDetail.SttElexysNo.Value(),
							SttError:    "Status tidak valid. Silahkan pilih status “OCC - EXP”",
						}
						sttData[sttDetail.Stt.SttNo] = stt
						continue
					}
				case model.OCCHAL:
					if sttDetail.Stt.SttLastStatusID == model.OCCHAL {
						sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
						stt := customProcess.SttData{
							SttNo:       sttDetail.Stt.SttNo,
							SttElexysNo: sttDetail.SttElexysNo.Value(),
						}
						sttData[sttDetail.Stt.SttNo] = stt
						continue
					}
				}
			}
		}

		/**  Validate last status damage */
		if sttDetail.Stt.SttLastStatusID == model.DAMAGE && model.IsNotAllowedLastStatusDamageUpdateCustomProcess[form.CustomProcessStatus] && !shared.IsBagNoReff(sttDetail.Stt.SttNoRefExternal) {
			sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
			stt := customProcess.SttData{
				SttNo:       sttDetail.Stt.SttNo,
				SttElexysNo: sttDetail.SttElexysNo.Value(),
			}
			sttData[sttDetail.Stt.SttNo] = stt
			continue
		}

		/**  Validate last status MISROUTE */
		if sttDetail.Stt.SttLastStatusID == model.MISROUTE && !model.IsAllowedLastStatusMisrouteUpdateCustomProcess[form.CustomProcessStatus] && !shared.IsBagNoReff(sttDetail.Stt.SttNoRefExternal) {
			sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
			stt := customProcess.SttData{
				SttNo:       sttDetail.Stt.SttNo,
				SttElexysNo: sttDetail.SttElexysNo.Value(),
			}
			sttData[sttDetail.Stt.SttNo] = stt
			continue
		}

		/**  Validate last status not received */
		if sttDetail.Stt.SttLastStatusID == model.NOTRECEIVED && model.IsNotAllowedLastStatusNotReceiveUpdateCustomProcess[form.CustomProcessStatus] && !shared.IsBagNoReff(sttDetail.Stt.SttNoRefExternal) {
			sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
			stt := customProcess.SttData{
				SttNo:       sttDetail.Stt.SttNo,
				SttElexysNo: sttDetail.SttElexysNo.Value(),
			}
			sttData[sttDetail.Stt.SttNo] = stt
			continue
		}

		/**  Validate last status CI */
		if sttDetail.Stt.SttLastStatusID == model.CI && !model.IsAllowedLastStatusCIUpdateCustomProcess[form.CustomProcessStatus] {
			sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
			stt := customProcess.SttData{
				SttNo:       sttDetail.Stt.SttNo,
				SttElexysNo: sttDetail.SttElexysNo.Value(),
			}
			sttData[sttDetail.Stt.SttNo] = stt
			continue
		}

		/**  Validate last status MISBOOKING */
		if sttDetail.Stt.SttLastStatusID == model.MISBOOKING && !model.IsAllowUpdateAfterMisbookingLastStatus[form.CustomProcessStatus] && !shared.IsBagNoReff(sttDetail.Stt.SttNoRefExternal) {
			sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
			stt := customProcess.SttData{
				SttNo:       sttDetail.Stt.SttNo,
				SttElexysNo: sttDetail.SttElexysNo.Value(),
			}
			sttData[sttDetail.Stt.SttNo] = stt
			continue
		}

		/*
			Validate stt to Status
		*/
		notEligibleToStatus, err := c.validateToStatus(selfCtx, form, sttDetail)
		if notEligibleToStatus {
			c.generateDataMapForSttFailed(&sttNoIsSuccess, &sttData, sttDetail)
			continue
		}
		if err != nil {
			return nil, err
		}

		if model.IsAllowedStatusCustomProcessNew[form.CustomProcessStatus] && model.IsNotAllowUpdateCustomProcessNew[sttDetail.Stt.SttLastStatusID] {
			sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
			stt := customProcess.SttData{
				SttNo:       sttDetail.Stt.SttNo,
				SttElexysNo: sttDetail.SttElexysNo.Value(),
			}
			sttData[sttDetail.Stt.SttNo] = stt
			continue
		}

		// validate if last status CODREJ
		if sttDetail.SttLastStatusID == model.CODREJ && !model.IsAllowedLastStatusCodRejUpdateCustomProcess[form.CustomProcessStatus] && !shared.IsBagNoReff(sttDetail.Stt.SttNoRefExternal) {
			sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
			stt := customProcess.SttData{
				SttNo:       sttDetail.Stt.SttNo,
				SttElexysNo: sttDetail.SttElexysNo.Value(),
			}
			sttData[sttDetail.Stt.SttNo] = stt
			continue
		}

		if sttDetail.Stt.SttLastStatusID == model.SCRAPCD && model.IsNotAllowUpdateAfterScrapCDLastStatus[form.CustomProcessStatus] {
			sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
			stt := customProcess.SttData{
				SttNo:       sttDetail.Stt.SttNo,
				SttElexysNo: sttDetail.SttElexysNo.Value(),
			}
			sttData[sttDetail.Stt.SttNo] = stt
			continue
		}

		/**
		 * Check last status stt eligible or not to update to REJECTED
		 */
		if form.CustomProcessStatus == model.REJECTED {
			if sttDetail.SttLastStatusID == model.STTREMOVE {
				history, err := c.sttPieceHistoryRepo.Select(selfCtx, &model.SttPieceHistoryViewParam{
					SttPieceHistorySttPieceID: sttDetail.SttPieceID,
					Order:                     true,
					OrderDesc:                 true,
				})

				if history == nil || len(history) < 2 || err != nil {
					return nil, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
						"en": "STT Piece History is not found",
						"id": "STT Piece History tidak ditemukan",
					})
				}

				if !(history[1].HistoryStatus == model.CARGOPLANE) {
					stt := customProcess.SttData{
						SttNo:       sttDetail.Stt.SttNo,
						SttElexysNo: sttDetail.SttElexysNo.Value(),
					}
					sttData[sttDetail.Stt.SttNo] = stt
					continue
				}

			} else if !model.IsAllowUpdateRejected[sttDetail.SttLastStatusID] {
				stt := customProcess.SttData{
					SttNo:       sttDetail.Stt.SttNo,
					SttElexysNo: sttDetail.SttElexysNo.Value(),
				}
				sttData[sttDetail.Stt.SttNo] = stt
				continue
			}
		}

		if sttDetail.Stt.SttLastStatusID != model.CI {
			if model.IsSttReturnForRtsRtshq[form.CustomProcessStatus] && !model.IsAllowedStatusCustomProcessRTSHQ[sttDetail.SttLastStatusID] {
				stt := customProcess.SttData{
					SttNo:       sttDetail.Stt.SttNo,
					SttElexysNo: sttDetail.SttElexysNo.Value(),
				}
				sttData[sttDetail.Stt.SttNo] = stt
				continue
			}
		}

		/**
		 * Trucking status cannot updated to SCRAP
		 */
		if model.TruckingStatusMapping[sttDetail.SttLastStatusID] && model.IsNotAllowedCustomProcessStatusForLastStatusTrucking[form.CustomProcessStatus] {
			sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
			stt := customProcess.SttData{
				SttNo:       sttDetail.Stt.SttNo,
				SttElexysNo: sttDetail.SttElexysNo.Value(),
			}
			sttData[sttDetail.Stt.SttNo] = stt
			continue
		}

		if form.CustomProcessStatus == model.CLAIM {
			historyClaim, err := c.sttPieceHistoryRepo.Get(selfCtx, &model.SttPieceHistoryViewParam{
				SttPieceHistorySttPieceID: sttDetail.SttPieceID,
				SttPieceHistoryStatus:     model.CLAIM,
			})
			if err != nil {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Failed get data history status",
					"id": "Gagal ambil data histori status",
				})
			}
			if historyClaim != nil {
				sttNoIsSuccess[sttDetail.Stt.SttNo] = model.Stt{}
				stt := customProcess.SttData{
					SttNo:       sttDetail.Stt.SttNo,
					SttElexysNo: sttDetail.SttElexysNo.Value(),
				}
				sttData[sttDetail.Stt.SttNo] = stt
				continue
			}
		}

		// Set district name value if exist on customProcessPartner
		districtName := ""
		if form.PartnerID > 0 && customProcessPartner != nil {
			if customProcessPartner.Data.PartnerLocation.District != nil {
				districtName = customProcessPartner.Data.PartnerLocation.District.Name
			}

			// assign location name
			remarksPieceHistory.HistoryLocationName = customProcessPartner.Data.PartnerLocation.City.Name
			remarksPieceHistory.HistoryDistrictName = districtName
		}

		// assign remarks piece history
		remarksPieceHistory.ClaimNo = form.ClaimNo
		remarksPieceHistory.CustomProcessRemarks = form.Remarks

		if form.CustomProcessStatus == model.CLAIM {
			remarksPieceHistory.ClaimNo = form.Remarks
		}

		if customProcess.IsHistoryNeedHubData[form.CustomProcessStatus] {
			SetHub(c.cityRepo, c.districtRepo, &stt_piece_history_remark_helper.SetHubParams{
				Ctx:                 selfCtx,
				Token:               form.Token,
				HubID:               form.HubID,
				HubName:             form.HubName,
				HubOriginCity:       form.HubOriginCity,
				HubDistrictCode:     form.HubDistrictCode,
				HubDestinationID:    form.HubDestinationID,
				HubDestinationType:  form.HubDestinationType,
				HubDestinationName:  form.HubDestinationName,
				HubDestinationCity:  form.HubDestinationCity,
				RemarksPieceHistory: &remarksPieceHistory,
			})
		}

		if form.CustomProcessStatus == model.CNXCD &&
			sttDetail.Stt.SttNoRefExternal != `` && shared.IsLiloPrefix(sttDetail.Stt.SttNoRefExternal) &&
			sttDetail.Stt.SttLastStatusID == model.HALCD {
			remarksPieceHistory.CustomProcessRemarks = model.RemarksCNXCD
		}

		if form.CustomProcessStatus == model.HALCD &&
			sttDetail.Stt.SttNoRefExternal != `` && shared.IsLiloPrefix(sttDetail.Stt.SttNoRefExternal) &&
			(sttDetail.Stt.SttLastStatusID == model.BKD || sttDetail.Stt.SttLastStatusID == model.BAGGING) {
			remarksPieceHistory.CustomProcessRemarks = reasonDescription
		}

		if form.CustomProcessStatus == model.REJECTED {
			reason, err := c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
				StatusCode:  model.REJECTED,
				ReasonTitle: form.Remarks,
			})
			if reason == nil || err != nil {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Remarks is not valid",
					"id": "Remarks tidak valid",
				})
			}
			reasonCode = reason.ReasonCode
			reasonTitle = reason.ReasonTitle
		}

		// remarksPieceHistory.VehicleNumber = form.Remarks
		sttHistories = append(sttHistories, customProcess.SttHistory{
			SttHistory: model.SttPieceHistory{
				SttPieceID:       sttDetail.SttPiece.SttPieceID,
				HistoryStatus:    form.CustomProcessStatus,
				HistoryLocation:  customProcessPartner.Data.PartnerLocation.CityCode,
				HistoryActorID:   form.PartnerID,
				HistoryActorName: form.PartnerName,
				HistoryActorRole: form.PartnerType,
				HistoryCreatedAt: now,
				HistoryCreatedBy: form.AccountID,
				HistoryRemark:    remarksPieceHistory.ToString(),

				HistoryReason:      reasonCode,
				HistoryCreatedName: form.AccountName,
			},
			SttID: sttDetail.Stt.SttID,
		})

		sttNoIsSuccess[sttDetail.Stt.SttNo] = sttDetail.Stt

		// CNXCD
		if form.CustomProcessStatus == model.CNXCD && sttDetail.Stt.SttLastStatusID == model.HALCD &&
			sttDetail.Stt.SttNoRefExternal != `` && shared.IsLiloPrefix(sttDetail.Stt.SttNoRefExternal) {
			sttData[sttDetail.Stt.SttNo] = customProcess.SttData{
				SttNo:       sttDetail.Stt.SttNo,
				SttElexysNo: sttDetail.SttElexysNo.Value(),
			}

			/** Publish Message to pubsub for the purposes of the refund calculation in GOBER service */
			sttDetailData := sttDetail.Stt
			sttPieceID := sttDetail.SttPieceID
			go lputils.TrackGoroutine(func(goCtx context.Context) {
				var (
					isAdjustmentBeforePup, isAdjustmentAfterPup bool
					sttStatusBeforeRejected                     string
					clientCodDiscountAmount                     *float64
				)

				if sttDetailData.SttIsCOD && sttDetailData.SttBookedForType == model.CLIENT {
					pieceHistories, err := c.sttPieceHistoryRepo.Select(goCtx, &model.SttPieceHistoryViewParam{
						SttPieceHistorySttPieceID: sttPieceID,
						Order:                     true,
						OrderDesc:                 true,
					})
					if err != nil {
						logger.E(err)
					}

					for _, pieceHistory := range pieceHistories {
						if (pieceHistory.HistoryStatus == model.STTADJUSTED || pieceHistory.HistoryStatus == model.BKD) && clientCodDiscountAmount == nil {
							remarks := pieceHistory.RemarkPieceHistoryToStruct()
							if remarks != nil {
								clientCodDiscountAmount = &remarks.ClientCodBookingDiscount
							}
						}
					}
				}

				optionalRate, err := c.sttOptionalRateRepo.Select(goCtx, &model.SttOptionalRate{
					SttOptionalRateSttID: sttDetailData.SttID,
				})
				if err != nil {
					logger.E(err)
				}
				var woodpackingRate, insuranceRate float64
				for _, v := range optionalRate {
					if v.SttOptionalRateParams == model.WOODPACKING {
						woodpackingRate = v.SttOptionalRateRate
					}
					if v.SttOptionalRateParams == model.INSURANCE {
						insuranceRate = v.SttOptionalRateRate
					}
				}

				var (
					referenceName, referenceType string
					referenceID                  int
				)

				switch form.AccountType {
				case model.PARTNER:
					referenceName = form.PartnerName
					referenceID = form.PartnerID
					referenceType = form.PartnerType
				case model.INTERNAL, model.CUSTOMERSERVICE:
					referenceName = model.INTERNAL
					referenceType = model.INTERNAL
				}

				message := stt.MessagePubsubCancelSttRequest{
					Stt:                      sttDetailData,
					SttStatusBeforeCnx:       sttDetailData.SttLastStatusID,
					SttIsAdjustmentBeforePup: isAdjustmentBeforePup,
					SttIsAdjustmentAfterPup:  isAdjustmentAfterPup,
					AccountName:              form.AccountName,
					AccountID:                int(form.AccountID),
					ActorID:                  referenceID,
					ActorName:                referenceName,
					ActorType:                referenceType,
					WoodpackingRate:          woodpackingRate,
					InsuranceRate:            insuranceRate,
					SttStatusBeforeRejected:  sttStatusBeforeRejected,
					ClientCodDiscountAmount:  clientCodDiscountAmount,
					IsCNXCD:                  true,
				}

				if err := c.gatewaySttStatusUc.RefundCancelBooking(goCtx, &message); err != nil {
					logger.E(err)
				}
			}, 60*3) // set timeout 3 minutes
		}
	}

	mapSttPiecesHistory := c.mappingToSttPieceHistory(sttHistories)

	// Define variable for update status time
	listSttUpdateTime := []stt_activity.SttActivityRequestDetail{}

	totalSttSuccess := 0
	totalGrossWeight := 0.0
	totalVolumeWeight := 0.0
	totalChargeableWeight := 0.0
	sttNoFailed := []general.STTFailedGeneralResponse{}
	res := general.STTFailedGeneralResponse{}
	for sttNo, stt := range sttNoIsSuccess {
		_, found := shared.FindStringOnStringSlice(sttNoElexys, sttNo)
		if found {
			continue
		}
		if stt.SttID == 0 {
			sttNoGenesis := ``
			sttNoElexys := ``
			sttNoError := ``
			for sttNumber, value := range sttData {
				if sttNo == sttNumber {
					sttNoGenesis = value.SttNo
					sttNoElexys = value.SttElexysNo
					sttNoError = value.SttError
				}
			}
			res.SttNo = sttNoGenesis
			res.SttElexysNo = sttNoElexys
			res.Error = sttNoError
			bagNo, bagVendorNo, err := c.getDataBagFromSTT(selfCtx, res.SttNo)
			if err != nil {
				return nil, err
			}
			res.BagNo = bagNo
			res.BagVendorNo = bagVendorNo
			sttNoFailed = append(sttNoFailed, res)
			continue
		}

		totalSttSuccess++
		totalGrossWeight += stt.SttGrossWeight
		totalVolumeWeight += stt.SttVolumeWeight
		totalChargeableWeight += stt.SttChargeableWeight

		// Adding time CUSTOM PROCESS status HAL, REJECTED, ODA, RTS, SCRAP, CLAIM
		listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
			SttNo:         stt.SttNo,
			SttStatus:     form.CustomProcessStatus,
			SttStatusTime: now,
		})

	}

	response = &customProcess.CreateCustomProcessResponse{
		TotalSttSuccess: totalSttSuccess,
		TotalSttFailed:  len(sttNoFailed),
		SttFailed:       sttNoFailed,
	}

	if totalSttSuccess != 0 {
		var (
			customProcessPartnerID   int    = form.PartnerID
			customProcessPartnerCode string = customProcessPartner.Data.Code
			customProcessPartnerName string = form.PartnerName
			customProcessAccountType string = form.AccountType
		)
		customProcess := &customProcess.InsertCustomProcessData{
			CustomProcess: &model.CustomProcess{
				CustomProcessTotalSTT:              totalSttSuccess,
				CustomProcessTotalPiece:            len(sttHistories),
				CustomProcessTotalGrossWeight:      totalGrossWeight,
				CustomProcessTotalVolumeWeight:     totalVolumeWeight,
				CustomProcessTotalChargeableWeight: totalChargeableWeight,
				CustomProcessLatestStatus:          form.CustomProcessStatus,
				CustomProcessCreatedAt:             now,
				CustomProcessCreatedBy:             form.AccountID,
				CustomProcessCreatedName:           form.AccountName,
				CustomProcessUpdatedAt:             now,
				CustomProcessUpdatedName:           form.AccountName,
				CustomProcessPartnerID:             customProcessPartnerID,
				CustomProcessPartnerCode:           customProcessPartnerCode,
				CustomProcessPartnerName:           customProcessPartnerName,
				CustomProcessRemarks:               form.Remarks,
				CustomProcessAccountType:           customProcessAccountType,
			},
			SttHistories: sttHistories,
		}

		if isReverseJourney && isNotBannedPartner {
			data := sttDetails[0]
			sttPieces := []*stt.SttPieces{}
			for _, val := range sttDetails {
				sttPieces = append(sttPieces, &stt.SttPieces{
					SttPieceLength:      val.SttPieceLength,
					SttPieceWidth:       val.SttPieceWidth,
					SttPieceHeight:      val.SttPieceHeight,
					SttPieceGrossWeight: val.SttPieceGrossWeight,
				})
			}
			// booked actor POS
			bookedActorDetail := &stt.BookedActorDetail{
				ID:           partnerBookedBy.Data.ID,
				Name:         partnerBookedBy.Data.Name,
				Type:         partnerBookedBy.Data.Type,
				Code:         partnerBookedBy.Data.Code,
				ExternalCode: partnerBookedBy.Data.PartnerExternalCode,
			}
			// get detail client
			if data.SttBookedForType == model.CLIENT {
				client, err := c.clientRepo.GetByID(selfCtx, data.SttBookedForID, form.Token)
				if err != nil {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Client Not Found",
						"id": "Client Tidak Ditemukan",
					})
				}
				bookedActorDetail = &stt.BookedActorDetail{
					ID:           client.Data.ClientID,
					Name:         client.Data.ClientCompanyName,
					Type:         model.CLIENT,
					Code:         client.Data.ClientCode,
					ExternalCode: client.Data.ClientCode,
				}
			}
			var (
				SttGoodsStatus   = data.SttGoodsStatus
				SttTaxNumber     = data.SttTaxNumber
				SttCommodityCode = data.SttCommodityCode
				SttProductType   = data.SttProductType
			)

			if model.IsSttAllowEditCustomProcess[form.CustomProcessStatus] {
				SttGoodsStatus = form.SttGoodsStatus
				SttTaxNumber = form.SttTaxNumber
				SttCommodityCode = form.SttCommodityCode
				SttProductType = form.SttProductTypeCode
			}

			if form.PartnerType == model.CONSOLE && model.IsSttAllowEditProductTypeCustomProcess[form.CustomProcessStatus] {
				SttProductType = form.SttProductTypeCode
			}

			sttSenderName := customProcessPartner.Data.Name
			sttSenderPhone := customProcessPartner.Data.PhoneNumber
			sttSenderAddress := customProcessPartner.Data.Address

			if form.CustomProcessStatus == model.REROUTE {
				sttSenderName = data.SttSenderName
				sttSenderPhone = data.SttSenderPhone
				sttSenderAddress = data.SttSenderAddress
			}

			payloadSttRequest := &stt.CreateSttManualReverseJourney{
				CreateStt: stt.CreateStt{
					Stt: stt.Stt{
						SttGoodsEstimatePrice:    data.SttGoodsEstimatePrice,
						SttGoodsStatus:           SttGoodsStatus,
						SttNoRefExternal:         data.SttNo,
						SttDestinationCityID:     form.CityCodeDestination,
						SttOriginCityID:          customProcessPartner.Data.PartnerLocation.CityCode,
						SttOriginDistrictID:      customProcessPartner.Data.PartnerLocation.DistrictCode,
						SttDestinationDistrictID: form.DisctrictCodeDestination,
						SttSenderName:            sttSenderName,
						SttSenderPhone:           sttSenderPhone,
						SttSenderAddress:         sttSenderAddress,
						SttRecipientName:         form.SttReceiptName,
						SttRecipientAddress:      form.SttReceiptAddress,
						SttRecipientPhone:        form.SttReceiptPhone,
						SttRecipientAddressType:  form.SttReceiptAddressType,
						SttProductType:           SttProductType,
						SttCommodityCode:         SttCommodityCode,
						SttInsuranceType:         data.SttInsuranceType,
						SttNextCommodity:         form.SttNextCommodity,
						SttPiecePerPack:          form.SttPiecePerPack,
						SttPieces:                sttPieces,
						SttTaxNumber:             SttTaxNumber,
						PostalCodeDestination:    form.SttDestinationZipCode,
						IsNewForm:                form.IsNewForm,
					},
					AccountType:    model.PARTNER,
					AccountID:      int64(model.AccountSystem.ActorID),
					AccountName:    model.AccountDefaultActor.ActorName,
					Token:          form.Token,
					AccountRefID:   form.SttBookedBy,
					AccountRefName: partnerBookedBy.Data.Name,
					AccountRefType: partnerBookedBy.Data.Type,
					Source:         model.MANUAL,
					BookedFor:      *bookedActorDetail,
				},
				ReverseJourneyActor: stt.BookedActorDetail{
					ID:           customProcessPartner.Data.ID,
					Name:         customProcessPartner.Data.Name,
					Type:         customProcessPartner.Data.Type,
					Code:         customProcessPartner.Data.Code,
					ExternalCode: customProcessPartner.Data.PartnerExternalCode,
					DistrictCode: customProcessPartner.Data.PartnerLocation.DistrictCode,
					DistrictName: customProcessPartner.Data.PartnerLocation.DistrictCode,
					CityCode:     customProcessPartner.Data.PartnerLocation.CityCode,
					CityName:     customProcessPartner.Data.PartnerLocation.CityCode,
				},
				ReverseJourneyStatus: form.CustomProcessStatus,
				CustomProcess:        customProcess,
				ReverseDestination:   form.ReverseDestination,
				PartnerModel:         customProcessPartner,
			}

			if form.CustomProcessStatus == model.REROUTE {
				payloadSttRequest.Stt.SttIsDO = data.SttIsDO
				payloadSttRequest.Stt.SttIsCOD = data.SttIsCOD
				payloadSttRequest.Stt.SttIsDFOD = data.SttIsDFOD
				payloadSttRequest.Stt.SttCODFee = 0
				payloadSttRequest.Stt.SttCODAmount = data.SttCODAmount
			}

			payloadSttRequest.Stt.SttFtzCIPL = form.SttFtzCIPL
			payloadSttRequest.Stt.SttFtzRecipientEmail = form.SttFtzRecipientEmail
			payloadSttRequest.Stt.SttFtzIdentityNumber = form.SttFtzIdentityNumber
			payloadSttRequest.Stt.SttFtzAttachFiles = form.SttFtzAttachFiles
			payloadSttRequest.Stt.SttFtzKtpImage = form.SttFtzKtpImage
			payloadSttRequest.Stt.SttFtzTaxImage = form.SttFtzTaxImage

			payloadSttRequest.ReverseDestination.SttFtzCIPL = form.ReverseDestination.SttFtzCIPL
			payloadSttRequest.ReverseDestination.SttFtzRecipientEmail = form.ReverseDestination.SttFtzRecipientEmail
			payloadSttRequest.ReverseDestination.SttFtzIdentityNumber = form.ReverseDestination.SttFtzIdentityNumber
			payloadSttRequest.ReverseDestination.SttFtzAttachFiles = form.ReverseDestination.SttFtzAttachFiles
			payloadSttRequest.ReverseDestination.SttFtzKtpImage = form.ReverseDestination.SttFtzKtpImage
			payloadSttRequest.ReverseDestination.SttFtzTaxImage = form.ReverseDestination.SttFtzTaxImage

			sttReturn, err := c.sttUc.CreateSTTReverseJourney(selfCtx, payloadSttRequest)
			defer func() {
				go lputils.TrackGoroutine(func(goCtx context.Context) {
					c.partnerLog.Insert(goCtx, &model.PartnerLog{
						Action:  model.PLCustomProcessSttReturnRtsRtshq,
						RefID:   data.SttNo,
						Request: payloadSttRequest,
						Response: func() interface{} {
							if err != nil {
								return err
							}
							return sttReturn
						}(),
					})
				}) // set timeout 1 minutes
			}()
			if err != nil {
				errResp := shared.MultiLangError{}
				dataerr := []byte(err.Error())
				_ = json.Unmarshal(dataerr, &errResp)

				response.TotalSttFailed++
				response.TotalSttSuccess--
				res.SttNo = data.SttNo
				res.Error = errResp.Msg["id"]
				bagNo, bagVendorNo, err := c.getDataBagFromSTT(selfCtx, res.SttNo)
				if err != nil {
					return nil, err
				}
				res.BagNo = bagNo
				res.BagVendorNo = bagVendorNo
				response.SttFailed = append(response.SttFailed, res)
				return response, nil
			}

			if sttReturn != nil {
				sttReturnSuccess := fmt.Sprintf("%s,%s,%d", data.SttNo, sttReturn.Stt.SttNo, sttReturn.Stt.SttID)
				response.SttSuccessRefNo = append(response.SttSuccessRefNo, sttReturnSuccess)
				response.CustomProcessID = int(sttReturn.CustomProcessID)
			}

			// publish message to PubSub
			go lputils.TrackGoroutine(func(goCtx context.Context) {
				cityDestinationCache := make(map[string]model.City)

				for _, stt := range sttNoIsSuccess {
					if stt.SttID > 0 {
						if model.IsPublishStatusCustomProcess[form.CustomProcessStatus] {
							// check and set reconcile for latest status DEX
							_ = handleAutoReconcile(goCtx, paramHandleAutoReconcile{c.deliveryRepo, stt.SttNo, 0, now, form.CustomProcessStatus, stt.SttLastStatusID})
							params := &model.UpdateSttStatusWithExtendForMiddleware{
								UpdateSttStatus: &model.UpdateSttStatus{
									SttNo: func() string {
										if stt.SttElexysNo.Valid {
											return stt.SttElexysNo.Value()
										}
										return stt.SttNo
									}(),
									Datetime:   now.UTC(),
									StatusCode: form.CustomProcessStatus,
									Location:   customProcessPartner.Data.PartnerLocation.CityCode,
									Remarks:    fmt.Sprintf(`Paket diupdate oleh %s`, customProcessPartner.Data.Name),
									City:       customProcessPartner.Data.PartnerLocation.City.Name,
									UpdatedBy:  customProcessPartner.Data.Name,
									UpdatedOn:  now.UTC(),
									Reason:     reasonTitle,
									ReasonCode: reasonCode,
								},
								ServiceType: model.PACKAGESERVICE, Product: stt.SttProductType, Pieces: stt.SttTotalPiece,
								GrossWeight: stt.SttGrossWeight, VolumeWeight: stt.SttVolumeWeight,
								ChargeableWeight: stt.SttChargeableWeight, BookedForType: stt.SttBookedForType,
								Reason:     reasonTitle,
								ReasonCode: reasonCode,
							}

							AppendLastAndSystemStatus(AppendLastAndSystemStatusParams{
								StatusSubmitParams: params,
								SttPieceHistories:  mapSttPiecesHistory[stt.SttID],
								PartnerName:        customProcessPartner.Data.Name,
							})
							c.gatewaySttStatusUc.StatusSubmit(goCtx, params)
						}

						if !model.IsPublishStatusCustomProcess[form.CustomProcessStatus] && strings.EqualFold(stt.SttBookedForType, model.CLIENT) {
							params := &model.UpdateSttStatusWithExtendForMiddleware{
								UpdateSttStatus: &model.UpdateSttStatus{
									SttID: stt.SttID,
									SttNo: func() string {
										if stt.SttElexysNo.Valid {
											return stt.SttElexysNo.Value()
										}
										return stt.SttNo
									}(),
									Datetime:   now.UTC(),
									StatusCode: form.CustomProcessStatus,
									Location:   customProcessPartner.Data.PartnerLocation.CityCode,
									Remarks:    fmt.Sprintf(`Paket diupdate oleh %s`, customProcessPartner.Data.Name),
									City:       customProcessPartner.Data.PartnerLocation.City.Name,
									UpdatedBy:  customProcessPartner.Data.Name,
									UpdatedOn:  now.UTC(),
									Reason:     reasonTitle,
									ReasonCode: reasonCode,
								},
								ServiceType: model.PACKAGESERVICE, Product: stt.SttProductType, Pieces: stt.SttTotalPiece,
								GrossWeight: stt.SttGrossWeight, VolumeWeight: stt.SttVolumeWeight, ChargeableWeight: stt.SttChargeableWeight,
								Reason:     reasonTitle,
								ReasonCode: reasonCode,
							}
							AppendLastAndSystemStatus(AppendLastAndSystemStatusParams{
								StatusSubmitParams: params,
								SttPieceHistories:  mapSttPiecesHistory[stt.SttID],
								PartnerName:        customProcessPartner.Data.Name,
							})
							err := c.middlewareRepo.SubmitDataToMiddleware(goCtx, params)
							if err != nil {
								logger.Ef(`Submit-DataToMiddlewares Error %s`, err.Error())
							}
						}

						if form.CustomProcessStatus == model.RTS || form.CustomProcessStatus == model.RTSHQ {
							if _, ok := cityDestinationCache[stt.SttDestinationCityID]; !ok {
								cityDestination, err := c.cityRepo.Get(goCtx, stt.SttDestinationCityID, form.Token)
								if err == nil && cityDestination != nil {
									cityDestinationCache[stt.SttDestinationCityID] = *cityDestination
								}
							}

							if city, ok := cityDestinationCache[stt.SttDestinationCityID]; ok {
								localTime, err := shared.ParseTimeWithLocation(now.Format(time.RFC3339), time.RFC3339, shared.MappingTimzoneToTimeLocation[city.Timezone])
								if err == nil {
									c.gatewaySttStatusUc.GoberDTPOLCommission(goCtx, &model.GoberDTPOLCommission{
										SttNo:              stt.SttNo,
										UpdatedAtLocalTime: localTime,
										UpdatedAtWIB:       now,
										SttLastStatus:      form.CustomProcessStatus,
									})
								}
							}
						}
					}
				}
			}, 60*3) // set timeout 3 minutes

		} else {
			createCustomProcess, err := c.customProcessRepo.Create(selfCtx, customProcess)

			if err != nil || createCustomProcess == nil {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "An error occurred while create Custom Process",
					"id": "Terjadi kesalahan pada saat create Custom Process",
				})
			}

			/*
			 * Updating list time stt status
			 */
			go lputils.TrackGoroutine(func(goCtx context.Context) {
				c.sttActivityUc.UpdateSttTime(goCtx, &stt_activity.SttActivityRequest{
					ListSttData: listSttUpdateTime,
				})
			}, 60*2) // set timeout 2 minutes

			// publish message to PubSub
			go lputils.TrackGoroutine(func(goCtx context.Context) {
				cityDestinationCache := make(map[string]model.City)

				for _, stt := range sttNoIsSuccess {
					if stt.SttID > 0 {
						if model.IsPublishStatusCustomProcess[form.CustomProcessStatus] {
							// check and set reconcile for latest status HAL,RTS,DEX,RTSHQ
							_ = handleAutoReconcile(goCtx, paramHandleAutoReconcile{c.deliveryRepo, stt.SttNo, 0, now, form.CustomProcessStatus, stt.SttLastStatusID})

							params := &model.UpdateSttStatusWithExtendForMiddleware{
								UpdateSttStatus: &model.UpdateSttStatus{
									SttNo: func() string {
										if stt.SttElexysNo.Valid {
											return stt.SttElexysNo.Value()
										}
										return stt.SttNo
									}(),
									Datetime:   now.UTC(),
									StatusCode: form.CustomProcessStatus,
									Location:   customProcessPartner.Data.PartnerLocation.CityCode,
									Remarks:    fmt.Sprintf(`Paket diupdate oleh %s`, customProcessPartner.Data.Name),
									City:       customProcessPartner.Data.PartnerLocation.City.Name,
									UpdatedBy:  customProcessPartner.Data.Name,
									UpdatedOn:  now.UTC(),
									Reason:     reasonTitle,
									ReasonCode: reasonCode,
								},
								ServiceType: model.PACKAGESERVICE, Product: stt.SttProductType, Pieces: stt.SttTotalPiece,
								GrossWeight: stt.SttGrossWeight, VolumeWeight: stt.SttVolumeWeight,
								ChargeableWeight: stt.SttChargeableWeight, BookedForType: stt.SttBookedForType,
								Reason:     reasonTitle,
								ReasonCode: reasonCode,
							}

							AppendLastAndSystemStatus(AppendLastAndSystemStatusParams{
								StatusSubmitParams: params,
								SttPieceHistories:  mapSttPiecesHistory[stt.SttID],
								PartnerName:        customProcessPartner.Data.Name,
							})

							c.gatewaySttStatusUc.StatusSubmit(goCtx, params)
						}

						if !model.IsPublishStatusCustomProcess[form.CustomProcessStatus] && strings.EqualFold(stt.SttBookedForType, model.CLIENT) {
							params := &model.UpdateSttStatusWithExtendForMiddleware{
								UpdateSttStatus: &model.UpdateSttStatus{
									SttNo: func() string {
										if stt.SttElexysNo.Valid {
											return stt.SttElexysNo.Value()
										}
										return stt.SttNo
									}(),
									Datetime:   now.UTC(),
									StatusCode: form.CustomProcessStatus,
									Location:   customProcessPartner.Data.PartnerLocation.CityCode,
									Remarks:    fmt.Sprintf(`Paket diupdate oleh %s`, customProcessPartner.Data.Name),
									City:       customProcessPartner.Data.PartnerLocation.City.Name,
									UpdatedBy:  customProcessPartner.Data.Name,
									UpdatedOn:  now.UTC(),
									Reason:     reasonTitle,
									ReasonCode: reasonCode,
								},
								ServiceType: model.PACKAGESERVICE, Product: stt.SttProductType, Pieces: stt.SttTotalPiece,
								GrossWeight: stt.SttGrossWeight, VolumeWeight: stt.SttVolumeWeight, ChargeableWeight: stt.SttChargeableWeight,
								Reason:     reasonTitle,
								ReasonCode: reasonCode,
							}

							AppendLastAndSystemStatus(AppendLastAndSystemStatusParams{
								StatusSubmitParams: params,
								SttPieceHistories:  mapSttPiecesHistory[stt.SttID],
								PartnerName:        customProcessPartner.Data.Name,
							})
							err := c.middlewareRepo.SubmitDataToMiddleware(goCtx, params)
							if err != nil {
								logger.Ef(`Submit-DataToMiddlewares Error %s`, err.Error())
							}
						}

						if form.CustomProcessStatus == model.RTS || form.CustomProcessStatus == model.RTSHQ {
							if _, ok := cityDestinationCache[stt.SttDestinationCityID]; !ok {
								cityDestination, err := c.cityRepo.Get(goCtx, stt.SttDestinationCityID, form.Token)
								if err == nil && cityDestination != nil {
									cityDestinationCache[stt.SttDestinationCityID] = *cityDestination
								}
							}

							if city, ok := cityDestinationCache[stt.SttDestinationCityID]; ok {
								localTime, err := shared.ParseTimeWithLocation(now.Format(time.RFC3339), time.RFC3339, shared.MappingTimzoneToTimeLocation[city.Timezone])
								if err == nil {
									c.gatewaySttStatusUc.GoberDTPOLCommission(goCtx, &model.GoberDTPOLCommission{
										SttNo:              stt.SttNo,
										UpdatedAtLocalTime: localTime,
										UpdatedAtWIB:       now,
										SttLastStatus:      form.CustomProcessStatus,
									})
								}
							}
						}
					}
				}
			}, 60*3) // set timeout 3 minutes

			go lputils.TrackGoroutine(func(goCtx context.Context) {
				for _, stt := range sttNoIsSuccess {
					if stt.SttID > 0 {
						if form.CustomProcessStatus == model.HAL || form.CustomProcessStatus == model.CI {
							stt.SttLastStatusID = form.CustomProcessStatus
							c.messageGatewayUc.SendMessage(goCtx, &model.SendMessageRequest{
								RecieverNumber: model.RecieverNumber{
									PackageSender:   stt.SttSenderPhone,
									PackageReceiver: stt.SttRecipientPhone,
								},
								ConsoleCity: customProcessPartner.Data.PartnerLocation.City.Name,
								PackageType: shared.CheckPackageType(stt),
								EventStatus: form.CustomProcessStatus,
								Token:       form.Token,
							}, &stt)
						}
					}
				}
			}, 60*3) // set timeout 3 minutes

			response.CustomProcessID = createCustomProcess.CustomProcess.CustomProcessID
		}
	}

	/*
	 * trigger release pos parent commission
	 */
	if model.IsCustomProcessStatusValidReleaseCommission[form.CustomProcessStatus] {
		go lputils.TrackGoroutine(func(goCtx context.Context) {
			c.gatewaySttStatusUc.TriggerReleasePosParentCommission(goCtx, &gateway_stt_status.TriggerReleasePosParentCommissionRequest{
				SttSuccess: sttNoIsSuccess,
				Status:     form.CustomProcessStatus,
				Token:      form.Token,
			})
		}, 60*2) // set timeout 2 minutes
	}

	// trigger release pos booking commission when custom process in (RTS,RTSHQ,CI,CLAIM)
	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.releaseBookingCommission(goCtx, form.CustomProcessStatus, sttNoIsSuccess)
	}, 60*2) // set timeout 2 minutes

	/*
	 * publish message stt cod retail claim to gober
	 */
	if form.CustomProcessStatus == model.CLAIM {
		updatedBy, updatedByType := form.AccountID, form.AccountType
		if form.PartnerID > 0 {
			updatedBy, updatedByType = form.PartnerID, form.PartnerType
		}

		for k, v := range sttDetails {
			if v.SttLastStatusID == model.RTS {
				sttMeta := v.SttMetaToStruct()
				if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.ReverseSttNo != "" {
					sttReverse, err := c.sttRepo.Get(selfCtx, &model.SttViewDetailParams{
						Stt: model.Stt{
							SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
						},
					})

					if err == nil && sttReverse != nil && sttReverse.SttIsDFOD {
						continue
					}
				}
			}

			sttMeta := sttDetails[k].Stt.SttMetaToStruct()
			isSttReverseJourneyReroute := sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt == model.REROUTE

			isSttCodRetail := v.SttIsCOD && v.SttBookedByType == model.POS && v.SttBookedForType == model.POS

			isPrefixShipmentFavoriteCod := v.SttIsCOD && v.SttShipmentID != `` && model.IsShipmentFavorite[shared.GetPrefixShipmentID(v.SttShipmentID)]
			isRerouteShipmentFavoriteCod := v.SttIsCOD && isSttReverseJourneyReroute && sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` && model.IsShipmentFavorite[shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID)]

			sttClaimCodRetail := new(gateway_stt.GoberRevertBookingCodPublishMessageRequest)
			if isSttCodRetail || isPrefixShipmentFavoriteCod || isRerouteShipmentFavoriteCod || v.SttIsDFOD {
				// stt
				sttClaimCodRetail = &gateway_stt.GoberRevertBookingCodPublishMessageRequest{
					SttNo:            v.SttNo,
					SttBookedByType:  v.SttBookedByType,
					SttBookedForType: v.SttBookedForType,
					SttIsCOD:         v.SttIsCOD,
					SttIsDFOD:        v.SttIsDFOD,
					SttShipmentID:    v.SttShipmentID,
					UpdateByID:       updatedBy,
					UpdateByType:     updatedByType,
				}

				if isSttReverseJourneyReroute {
					sttClaimCodRetail.SttNo = sttMeta.DetailSttReverseJourney.RootReverseSttNo
					sttClaimCodRetail.SttShipmentID = sttMeta.DetailSttReverseJourney.RootReverseShipmentID
				}
			}

			// send to gober
			if sttClaimCodRetail.SttNo != `` {
				go lputils.TrackGoroutine(func(goCtx context.Context) {
					stt := sttClaimCodRetail
					c.gatewaySttUc.GoberRevertBookingCod(goCtx, stt)
				}) // set timeout 1 minutes
			}
		}
	}

	if customProcess.StatusInactiveRtc[form.CustomProcessStatus] {
		go lputils.TrackGoroutine(func(goCtx context.Context) {
			stts := sttDetails
			for i := range stts {
				c.rtcUc.UpdateInactiveRTCBySttId(goCtx, int(stts[i].SttID))
			}
		}, 60*2) // set timeout 2 minutes
	}

	if customProcess.StatusIsShowZero[form.CustomProcessStatus] {
		go lputils.TrackGoroutine(func(goCtx context.Context) {
			stts := sttDetails
			for i := range stts {
				c.requestPriorityDelivery.UpdateIsShowToZero(goCtx, stts[i].SttNo)
			}
		}, 60*5) // set timeout 5 minutes
	}

	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.updateSttMetaCustomProcess(goCtx, form, sttNoIsSuccess, now)
	}, 60*2) // set timeout 2 minutes

	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.updateSttDueIsShowFalse(goCtx, sttNoIsSuccess, form.CustomProcessStatus)
	}, 60*2) // set timeout 2 minutes

	return response, nil
}

func (c *customProcessCtx) updateSttMetaCustomProcess(ctx context.Context, form *customProcess.CreateCustomProcessRequest, sttNoIsSuccess map[string]model.Stt, now time.Time) {
	if form.CustomProcessStatus != model.MISBOOKING && form.CustomProcessStatus != model.REROUTE {
		return
	}

	for _, stt := range sttNoIsSuccess {
		sttProcess := stt
		if form.CustomProcessStatus == model.REROUTE {
			sttReverse, err := c.sttRepo.Get(ctx, &model.SttViewDetailParams{
				Stt: model.Stt{
					SttNoRefExternal: stt.SttNo,
				},
			})
			if err != nil || sttReverse == nil {
				continue
			}
			sttProcess = *sttReverse
		}

		metaStt := sttProcess.SttMetaToStruct()
		if metaStt == nil {
			continue
		}

		metaStt.ReverseDestination = &model.ReverseDestination{
			Remarks:                  form.ReverseDestination.Remarks,
			SttProductType:           form.ReverseDestination.SttProductType,
			ReturnCityCode:           form.ReverseDestination.ReturnCityCode,
			ReturnCityName:           form.ReverseDestination.ReturnCityName,
			ReturnDistrictCode:       form.ReverseDestination.ReturnDistrictCode,
			ReturnDistrictName:       form.ReverseDestination.ReturnDistrictName,
			ReturnReceiptAddress:     form.ReverseDestination.ReturnReceiptAddress,
			ReturnReceiptAddressType: form.ReverseDestination.ReturnReceiptAddressType,
			ReturnReceiptName:        form.ReverseDestination.ReturnReceiptName,
			ReturnReceiptPhone:       form.ReverseDestination.ReturnReceiptPhone,
			SttDestinationZipCode:    form.ReverseDestination.SttDestinationZipCode,
			SttCommodityCode:         form.ReverseDestination.SttCommodityCode,
			SttGoodsStatus:           form.ReverseDestination.SttGoodsStatus,
			SttTaxNumber:             form.ReverseDestination.SttTaxNumber,
			SttPiecePerPack:          form.ReverseDestination.SttPiecePerPack,
			SttNextCommodity:         form.ReverseDestination.SttNextCommodity,
			SttFtzCIPL:               form.ReverseDestination.SttFtzCIPL,
			SttFtzRecipientEmail:     form.ReverseDestination.SttFtzRecipientEmail,
			SttFtzIdentityNumber:     form.ReverseDestination.SttFtzIdentityNumber,
			SttFtzAttachFiles:        form.ReverseDestination.SttFtzAttachFiles,
			SttFtzKtpImage:           form.ReverseDestination.SttFtzKtpImage,
			SttFtzTaxImage:           form.ReverseDestination.SttFtzTaxImage,
		}

		if form.CustomProcessStatus == model.MISBOOKING && form.AccountType == model.INTERNAL {
			metaStt.ReverseDestination.IsMisbookingByInternal = true
		}

		sttProcess.SttMeta = metaStt.ToString()
		sttProcess.SttUpdatedAt = now
		c.sttRepo.UpdateMetaStt(ctx, &sttProcess)
	}
}

func (c *customProcessCtx) generateDataMapForSttFailed(sttNoIsSuccess *map[string]model.Stt, sttData *map[string]customProcess.SttData, sttDetail model.SttDetailResult) {
	(*sttNoIsSuccess)[sttDetail.Stt.SttNo] = model.Stt{}
	stt := customProcess.SttData{
		SttNo:       sttDetail.Stt.SttNo,
		SttElexysNo: sttDetail.SttElexysNo.Value(),
	}
	(*sttData)[sttDetail.Stt.SttNo] = stt
}

func (c *customProcessCtx) validateToStatus(ctx context.Context, form *customProcess.CreateCustomProcessRequest, sttDetail model.SttDetailResult) (bool, error) {
	funcValidationToStatus := map[string]func(ctx context.Context, form *customProcess.CreateCustomProcessRequest, sttDetail model.SttDetailResult) (bool, error){
		model.RTSHQ:      c.validateToRTSHQ,
		model.RTS:        c.validateToRTS,
		model.HAL:        c.validateToHAL,
		model.OCC:        c.validateToOCC,
		model.CI:         c.validateToCI,
		model.MISBOOKING: c.validateToMISBOOKING,
		model.REROUTE:    c.validateToREROUTE,
		model.SCRAPCD:    c.validateToSCRAPCD,
		model.INHUB:      c.validateToHUB,
		model.OUTHUB:     c.validateToHUB,
	}

	validateToStatus, ok := funcValidationToStatus[form.CustomProcessStatus]
	if !ok {
		return false, nil
	}

	statusHub := []string{model.INHUB, model.OUTHUB}
	needStatusBeforeAdjustment := map[string][]string{
		model.RTS:     statusHub,
		model.RTSHQ:   statusHub,
		model.REROUTE: statusHub,
		model.INHUB:   []string{}, // no filter
		model.OUTHUB:  []string{}, // no filter
	}

	if filter, ok := needStatusBeforeAdjustment[form.CustomProcessStatus]; ok {
		sttHistory, err := GetSttStatusHistoryBeforeAdjusment(ctx, c.sttPieceHistoryRepo, sttDetail.SttNo, filter)
		if err != nil || sttHistory == nil {
			return false, err
		}

		sttDetail.Stt.SttLastStatusID = sttHistory.HistoryStatus
		form.SttHistory = sttHistory
	}

	return validateToStatus(ctx, form, sttDetail)
}

func (c *customProcessCtx) validateToRTS(ctx context.Context, form *customProcess.CreateCustomProcessRequest, sttDetail model.SttDetailResult) (bool, error) {
	if model.IsNotAllowUpdateToRTS[sttDetail.Stt.SttLastStatusID] {
		return true, nil
	}

	priorityDel, err := c.priorityDeliveryRepo.GetBySttNo(ctx, sttDetail.SttNo)
	if err != nil {
		return false, shared.ERR_UNEXPECTED_DB
	}

	isWaitSalesforceConfirmation := priorityDel != nil && priorityDel.PdIsShow == 0 && priorityDel.MetaToStruct() != nil && priorityDel.MetaToStruct().TicketSfID != ""
	if isWaitSalesforceConfirmation {
		return false, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT cannot be updated because it has to wait for customer/client confirmation.",
			"id": "STT tidak bisa di update karena harus menunggu konfirmasi customer/klien",
		})
	}

	return false, nil
}

func (c *customProcessCtx) validateToRTSHQ(ctx context.Context, form *customProcess.CreateCustomProcessRequest, sttDetail model.SttDetailResult) (bool, error) {
	validateLastStatus := !model.IsAllowUpdateRTSHQ[sttDetail.SttLastStatusID]
	validateNotClientAndCodDfod := sttDetail.SttBookedForType != model.CLIENT && (sttDetail.SttIsCOD || sttDetail.SttIsDFOD)
	validateCodCaRetail := sttDetail.SttShipmentID != `` && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(sttDetail.SttShipmentID)]
	validateApAs := sttDetail.SttShipmentID != `` && model.MappingShipmentPrefixApAs[shared.GetPrefixShipmentID(sttDetail.SttShipmentID)] && (sttDetail.SttIsCOD || sttDetail.SttIsDFOD)

	notEligibleRTSHQ := validateLastStatus || validateNotClientAndCodDfod || validateCodCaRetail || validateApAs
	if notEligibleRTSHQ {
		return notEligibleRTSHQ, nil
	}

	return c.validateToRTSHQReverse(ctx, form, sttDetail.SttMetaToStruct())
}

func (c *customProcessCtx) validateToRTSHQReverse(ctx context.Context, form *customProcess.CreateCustomProcessRequest, sttMeta *model.SttMeta) (bool, error) {
	sttNoReverse := c.getSttReverseNoFromSttMeta(sttMeta)
	if sttNoReverse == "" {
		return false, nil
	}

	sttReverseDetails, err := c.sttPieceRepo.SelectDetail(ctx, &model.SttPiecesViewParam{
		SttNo: sttNoReverse,
	})
	if err != nil {
		return false, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting STT",
			"id": "Terjadi kesalahan pada saat getting STT",
		})
	}

	bookNotForClient := sttReverseDetails[0].SttBookedForType != model.CLIENT
	codDfod := sttReverseDetails[0].SttIsCOD || sttReverseDetails[0].SttIsDFOD
	validateLastStatusSttReverse := model.IsNotAllowSttReverseJourneyUpdateToRTSHQ[sttReverseDetails[0].SttLastStatusID]

	validateReverseNotClientAndCodDfod := bookNotForClient && codDfod && validateLastStatusSttReverse
	validateReverseCodCaRetail := sttReverseDetails[0].SttShipmentID != `` && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(sttReverseDetails[0].SttShipmentID)]

	return validateReverseNotClientAndCodDfod || validateReverseCodCaRetail, nil
}

func (c *customProcessCtx) getSttReverseNoFromSttMeta(sttMeta *model.SttMeta) string {
	hasSttNoReverse := sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.ReverseSttNo != ""
	if hasSttNoReverse {
		return sttMeta.DetailSttReverseJourney.ReverseSttNo
	}
	return ""
}

func (c *customProcessCtx) validateToHAL(ctx context.Context, form *customProcess.CreateCustomProcessRequest, sttDetail model.SttDetailResult) (bool, error) {
	return model.IsNotAllowUpdateToHAL[sttDetail.Stt.SttLastStatusID], nil
}

func (c *customProcessCtx) validateToOCC(ctx context.Context, form *customProcess.CreateCustomProcessRequest, sttDetail model.SttDetailResult) (bool, error) {
	return model.IsNotAllowedStatusCustomProcessOCC[sttDetail.Stt.SttLastStatusID], nil
}

func (c *customProcessCtx) validateToCI(ctx context.Context, form *customProcess.CreateCustomProcessRequest, sttDetail model.SttDetailResult) (bool, error) {
	return sttDetail.SttLastStatusID != model.MISSING, nil
}

func (c *customProcessCtx) validateToMISBOOKING(ctx context.Context, form *customProcess.CreateCustomProcessRequest, sttDetail model.SttDetailResult) (bool, error) {
	return !model.IsAllowUpdateMisbooking[sttDetail.SttLastStatusID], nil
}

func (c *customProcessCtx) validateToREROUTE(ctx context.Context, form *customProcess.CreateCustomProcessRequest, sttDetail model.SttDetailResult) (bool, error) {
	return !model.IsAllowUpdateReroute[sttDetail.SttLastStatusID], nil
}

func (c *customProcessCtx) validateToSCRAPCD(ctx context.Context, form *customProcess.CreateCustomProcessRequest, sttDetail model.SttDetailResult) (bool, error) {
	return !shared.IsBagNoReff(sttDetail.Stt.SttNoRefExternal) || !model.IsAllowUpdateCustomStatusScrapCD[sttDetail.Stt.SttLastStatusID], nil
}

func (c *customProcessCtx) validateToHUB(ctx context.Context, form *customProcess.CreateCustomProcessRequest, sttDetail model.SttDetailResult) (bool, error) {
	// return notEligibles
	sttLastStatus := form.SttHistory.HistoryStatus

	isAllowWithTheSameStatus := c.ValidateToTheSameStatus(ctx, &ValidateToTheSameStatusParams{
		Status:             form.CustomProcessStatus,
		HubID:              form.HubID,
		HubDestinationID:   form.HubDestinationID,
		HubDestinationCity: form.HubDestinationCity,
		SttHistory:         form.SttHistory,
		PartnerId:          form.PartnerID,
	})
	if !isAllowWithTheSameStatus {
		return true, nil
	}

	rules, ok := model.IsAllowUpdateHub[form.CustomProcessStatus]
	if !ok {
		return true, nil
	}

	if !rules.Allowed[sttLastStatus] {
		return true, nil
	}

	if rules.NotAllowed[sttLastStatus] {
		return true, nil
	}

	return false, nil
}

func (c *customProcessCtx) updateSttDueIsShowFalse(ctx context.Context, sttNoIsSuccess map[string]model.Stt, customProcessStatus string) {
	statusTarget := make([]string, 0)
	if customProcess.StatusForHideSttDueSTI[customProcessStatus] {
		statusTarget = append(statusTarget, model.STI)
	}

	if customProcess.StatusForHiddeSttDueSTIDEST[customProcessStatus] {
		statusTarget = append(statusTarget, model.STIDEST)
	}

	if customProcess.StatusForHiddeSttDueDEL[customProcessStatus] {
		statusTarget = append(statusTarget, model.DEL)
	}

	if len(statusTarget) == 0 {
		return
	}

	sttNos := []string{}
	for _, stt := range sttNoIsSuccess {
		sttNos = append(sttNos, stt.SttNo)
	}

	_ = c.sttDueRepo.UpdateBulk(ctx, &model.STTDueUpdateBulkParams{
		FieldInsertSttDue: &model.STTDueModel{
			SdIsShow:    false,
			SdUpdatedAt: time.Now(),
		},
		SttTargetStatus: statusTarget,
		SttNo:           sttNos,
		UpdateFields: []string{
			`sd_is_show`,
			`sd_updated_at`,
		},
		IsSttNoRequired: true,
	})
}

func (c *customProcessCtx) validateStatusCustomProcessCreate(selfCtx context.Context, form *customProcess.CreateCustomProcessRequest) error {
	isStatusValid := map[string]bool{}
	rcp, err := c.customProcessRoleRepo.Select(selfCtx, &model.CustomProcessRoleViewParams{
		AccountRoleType: form.AccountRoleName,
	})
	if err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while querying db",
			"id": "Terjadi kesalahan pada saat query db",
		})
	}

	for _, val := range rcp {
		isStatusValid[val.RoleCustomProcessStatus] = true
	}
	if form.CustomProcessStatus != "" {
		if !isStatusValid[form.CustomProcessStatus] {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "custom_process_status is not valid",
				"id": "custom_process_status tidak valid",
			})
		}
	}

	return nil
}

// bagNo, bagVendorNo, error
func (c *customProcessCtx) getDataBagFromSTT(ctx context.Context, sttNo string) (string, string, error) {
	bagDatas, err := c.bagRepo.SelectBagWithSTT(ctx, &model.BagViewParams{
		SttNo:               sttNo,
		IsDeletedFalse:      true,
		IsNotAvailableFalse: true,
	})
	if err != nil && err != dbr.ErrNotFound {
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to retrieved bag",
			"id": "Gagal mendapatkan bag",
		})
		return "", "", err
	}

	if len(bagDatas) > 0 && bagDatas[0].BagCode != nil {
		if !shared.IsBagNoReff(*bagDatas[0].BagCode) {
			return *bagDatas[0].BagCode, "", nil
		}

		bagVendor, err := c.bagVendorRepo.Get(ctx, model.BagVendor{
			BvBagNo: *bagDatas[0].BagCode,
		})
		if err != nil || bagVendor == nil {
			return "", "", shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Bag vendor not found",
				"id": "Bag vendor tidak ditemukan",
			})
		}

		return *bagDatas[0].BagCode, bagVendor.BvBagVendorNo, nil
	}

	return "", "", nil
}

/* processPartnerBookedBy for validate partner while RTS */
func (c *customProcessCtx) processPartnerBookedBy(ctx context.Context, form *customProcess.CreateCustomProcessRequest, customProcessPartner, partnerBookedBy *model.Partner) error {
	if model.IsSttReturnForRtsRtshq[form.CustomProcessStatus] {
		err := c.validatePartnerBookedBy(ctx, form, customProcessPartner, partnerBookedBy)
		if err != nil {
			return err
		}

		if form.CustomProcessStatus == model.RTSHQ {
			form.SttReceiptName = c.flagManagementRepo.CloudBeesRTSHQDefaultName(ctx)
			form.SttReceiptAddress = c.flagManagementRepo.CloudBeesRTSHQDefaultAddress(ctx)
			form.SttReceiptPhone = c.flagManagementRepo.CloudBeesRTSHQDefaultPhoneNumber(ctx)
			form.SttReceiptAddressType = c.flagManagementRepo.CloudBeesRTSHQDefaultAddressType(ctx)
			form.SttDestinationDistrictID = c.flagManagementRepo.CloudBeesRTSHQDefaultDistrictCode(ctx)

			districtConsoleDefault, err := c.districtRepo.GetByCode(ctx, &model.CredentialRestAPI{Token: form.Token}, form.SttDestinationDistrictID)
			if err != nil {
				return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "An error occurred while getting data",
					"id": "Terjadi kesalahan pada saat getting data",
				})
			}
			form.CityCodeDestination = districtConsoleDefault.Data.City.Code
			form.DisctrictCodeDestination = districtConsoleDefault.Data.Code
		} else {
			isDetailAddressEmpty := form.SttReceiptAddress == `` || form.SttReceiptName == `` || form.SttReceiptPhone == `` || form.SttReceiptAddressType == `` || form.SttDestinationDistrictID == ``
			if isDetailAddressEmpty {
				return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "detail address is mandatory",
					"id": "alamat wajib diisi",
				})
			}
			districtDestination, err := c.districtRepo.GetByCode(ctx, &model.CredentialRestAPI{Token: form.Token}, form.SttDestinationDistrictID)
			if err != nil {
				return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "An error occurred while getting data",
					"id": "Terjadi kesalahan pada saat getting data",
				})
			}
			form.CityCodeDestination = districtDestination.Data.City.Code
			form.DisctrictCodeDestination = districtDestination.Data.Code
		}
	}

	return nil
}

func (c *customProcessCtx) validatePartnerBookedBy(ctx context.Context, form *customProcess.CreateCustomProcessRequest, customProcessPartner, partnerBookedBy *model.Partner) error {
	var err error

	// As existing code flow, Just partner can update IsSttReturnForRtsRtshq
	if customProcessPartner == nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner is not found",
			"id": "Partner tidak ditemukan",
		})
	}

	// just indonesian console can update IsSttReturnForRtsRtshq (confirmed)
	if customProcessPartner.IsBannedForReverseJourney() {
		return nil
	}

	if form.SttBookedBy <= 0 {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner booked is not found",
			"id": "Partner booked tidak ditemukan",
		})
	}

	partnerBookedByData, err := c.partnerRepo.GetByID(ctx, form.SttBookedBy, form.Token)
	if err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner is not found",
			"id": "Partner tidak ditemukan",
		})
	}

	if partnerBookedByData.Data.PartnerLocation == nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner is not yet mapped with location",
			"id": "Partner belum dipetakan dengan lokasi",
		})
	}

	*partnerBookedBy = *partnerBookedByData
	if customProcessPartner.Data.PartnerIDSttReturn > 0 {
		if customProcessPartner.Data.PartnerIDSttReturn != form.SttBookedBy {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner is not match",
				"id": "Partner tidak cocok",
			})
		}
	} else {
		if partnerBookedBy.Data.ParentID != customProcessPartner.Data.ID {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner is not match",
				"id": "Partner tidak cocok",
			})
		}
	}

	return nil
}
