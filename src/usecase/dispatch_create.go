package usecase

import (
	"context"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/dispatch"
)

func (c *dispatchCtx) CreateNewDispatch(ctx context.Context, req dispatch.CreateNewDispatchRequest) (dispatch.CreateNewDispatchResponse, error) {
	var (
		opName  = "Usecase-stiDestCtx-ManifestStiDest"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		result dispatch.CreateNewDispatchResponse
		err    error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": result, "error": err})
	}()

	if err = req.Validate(); err != nil {
		return result, err
	}

	err = c.GetPartnerData(selfCtx, &req)
	if err != nil {
		return result, err
	}

	// get data dispatch temporary by account id and hub id. also check only data with is_active = true
	dispatchTemps, err := c.GetDispatchTemporary(selfCtx, model.GetDispatchTemporaryParams{
		AccountID: req.AccountId,
		HubID:     req.HubId,
		IsActive:  shared.BoolPtr(true),
	})

	if err != nil {
		return result, err
	}

	// create new dispatch
	dispatchData, err := c.CreateRepoDispatch(selfCtx, req, dispatchTemps)
	if err != nil {
		return result, err
	}

	result.DispatchId = dispatchData.DispatchID
	result.DispatchCode = dispatchData.DispatchNo
	return result, nil
}

func (c *dispatchCtx) GetPartnerData(ctx context.Context, req *dispatch.CreateNewDispatchRequest) error {
	partner, err := c.partnerRepo.GetByID(ctx, req.PartnerId, req.Token)
	if err != nil {
		return err
	}

	if partner == nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner data not found",
			"id": "Partner data tidak ditemukan",
		})
	}

	if partner.Data.PartnerLocation == nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner is not yet mapped with location",
			"id": "Partner belum di Mapping dengan lokasi",
		})
	}

	if partner.Data.PartnerLocation.City == nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner is not yet mapped with city",
			"id": "Partner belum di Mapping dengan kota",
		})
	}

	req.PartnerData = partner
	return nil
}

func (c *dispatchCtx) CreateRepoDispatch(ctx context.Context, req dispatch.CreateNewDispatchRequest, dispatchTemps []model.DispatchTemporary) (model.Dispatch, error) {
	var (
		dispatch        model.Dispatch
		dispatchDetails []model.DispatchDetail
		dispatchTempIds []int64
		err             error
	)

	for _, dispatchTemp := range dispatchTemps {
		dispatchDetails = append(dispatchDetails, *dispatchTemp.ToDispatchDetail())
		dispatchTempIds = append(dispatchTempIds, dispatchTemp.ID)
	}

	dispatch.SetCode()
	dispatch.SetPartner(int64(req.PartnerId), req.PartnerCode, req.PartnerName)
	dispatch.SetCreatedById(req.AccountId)
	dispatch.SetCreatedByName(req.AccountName)
	dispatch.SetCreatedByRole(req.AccountType)
	dispatch.SetPartnerDestinationCity(req.PartnerData.Data.PartnerLocation.CityCode, req.PartnerData.Data.PartnerLocation.City.Name)
	dispatch.ResumeDispatch(dispatchDetails)

	dispatchAndDetails, err := c.dispatchRepo.CreateNew(ctx, model.CreateNewDispatchRequest{
		AccountId: req.AccountId,
		DispatchWithDetails: model.DispatchWithDetails{
			Dispatch:        dispatch,
			DispatchDetails: dispatchDetails,
		},
		IsActive:        false, // for update dispatch temporary status is_active
		DispatchTempIds: dispatchTempIds,
	})
	if err != nil {
		return dispatch, err
	}

	return dispatchAndDetails.Dispatch, nil
}

func (c *dispatchCtx) GetDispatchTemporary(ctx context.Context, params model.GetDispatchTemporaryParams) ([]model.DispatchTemporary, error) {
	// get data dispatch temporary by account id and hub id. also check only data with is_active = true
	dispatchTemp, err := c.dispatchRepo.GetDispatchTemporary(ctx, params)
	if err != nil {
		return nil, err
	}

	if len(dispatchTemp) < 1 {
		return dispatchTemp, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Dispatch has been processed",
			"id": "Dispatch sudah terproses",
		})
	}

	return dispatchTemp, nil
}
