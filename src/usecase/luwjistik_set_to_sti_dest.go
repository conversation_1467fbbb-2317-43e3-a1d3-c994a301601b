package usecase

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/Lionparcel/go-lptool/v2/lputils"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/sti_dest"
	"github.com/Lionparcel/hydra/src/usecase/stt_activity"
	"github.com/abiewardani/dbr/v2"
)

// setToStiDestSc
func (c *luwjistikCtx) setToStiDest(ctx context.Context, form *model.ListenerLuwjistikStatus, sttDetail []model.SttDetailResult) (err error) {
	opName := "luwjistikCtx-setToStiDest"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": err})

		go c.bgPartnerInsert(&model.PartnerLog{
			Action:  model.PLSttLuwjistikStiDest,
			RefID:   form.EventPayload.OrderCode,
			Request: form,
			Response: func() interface{} {
				if err != nil {
					return err.Error()
				}
				return model.LuwjistikWebhookResponse{Success: true}
			}(),
		})
	}()

	// Stt Detail
	sttRow := sttDetail[0]

	request := sti_dest.CreateStiDestParams{}
	now := c.timeRepo.Now(time.Now())

	accountToken, err := c.repoAccount.GetTokenGenesis(selfCtx)
	if err != nil || accountToken == nil {
		err = shared.ErrUnexpected
		return
	}

	token := accountToken.Data.Token
	sttNoSuccess := map[string]model.Stt{}

	// get stt piece
	var totalGrossWeight, totalVolumeWeight float64

	// Define variable for update status time
	listSttUpdateTime := []stt_activity.SttActivityRequestDetail{}

	totalGrossWeight += sttRow.SttGrossWeight
	totalVolumeWeight += sttRow.SttVolumeWeight
	// validate last status stt piece
	if !model.IsAllowUpdateToSTIDEST[sttRow.SttLastStatusID] {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while get route leg",
			"id": "Terjadi kesalahan pada saat ambil route leg",
		})
	}

	// check if already update to STIDEST before
	if sttRow.SttLastStatusID == model.CLAIM {
		history, err := c.sttHistoryRepo.Get(ctx, &model.SttPieceHistoryViewParam{
			SttPieceHistorySttPieceID: sttRow.SttPieceID,
			SttPieceHistoryStatus:     model.STIDEST,
		})

		if err != nil || history != nil {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while get route leg",
				"id": "Terjadi kesalahan pada saat ambil route leg",
			})
		}
	}

	// Get route leg type city-to-city
	routeleg, err := c.routeRepo.SelectRouteLeg(selfCtx, sttRow.SttOriginCityID, sttRow.SttDestinationCityID, model.CityToCity, token)
	if err != nil || routeleg == nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while get route leg",
			"id": "Terjadi kesalahan pada saat ambil route leg",
		})
	}

	// Get Information City STT
	cityStt, err := c.repoCity.Get(selfCtx, sttRow.SttDestinationCityID, token)
	if err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while querying DB",
			"id": "Terjadi kesalahan pada saat querying DB",
		})
	}
	if cityStt == nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "City Not Found",
			"id": "Kota tidak ditemukan",
		})
	}

	// insert piece to detail
	for _, val := range sttDetail {
		stiDestDetailReq := model.StiDestDetail{
			StiDestDetailSttNo:                val.SttNo,
			StiDestDetailSttID:                int(sttRow.SttID),
			StiDestDetailBagNo:                "", // ambil dari history ambil last bagging
			StiDestDetailSttTotalPiece:        val.SttTotalPiece,
			StiDestDetailSttPieceID:           int(val.SttPieceID),
			StiDestDetailSttPieceNo:           val.SttPieceNo,
			StiDestDetailSttPieceGrossWeight:  val.SttPieceGrossWeight,
			StiDestDetailSttPieceVolumeWeight: val.SttPieceVolumeWeight,
			StiDestPickupPartnerID:            model.AccountLuwjistik.ActorID,
			StiDestPickupPartnerCode:          model.LuwjistikName,
			StiDestPickupPartnerName:          model.AccountLuwjistik.ActorName,
			StiDestProductType:                val.SttProductType,
			StiDestDetailCreatedAt:            form.EventPayload.Update.UpdateTimestampTime,
			StiDestDetailUpdatedAt:            &now,
		}
		request.StiDestDetail = append(request.StiDestDetail, stiDestDetailReq)

		tempRemarksPieceHistory := model.RemarkPieceHistory{
			CargoNumber: "",
		}

		tempRemarksPieceHistory.HistoryLocationName = cityStt.Name
		tempRemarksPieceHistory.HistoryDistrictName = sttRow.SttDestinationDistrictName
		tempRemarksPieceHistory.HistoryDistrictCode = sttRow.SttDestinationDistrictID
		request.History = append(request.History, model.SttPieceHistory{ // STI
			SttPieceID:         int64(val.SttPieceID),
			HistoryStatus:      model.STIDEST,
			HistoryLocation:    sttRow.SttDestinationCityID,
			HistoryActorID:     model.AccountLuwjistik.ActorID,
			HistoryActorName:   model.AccountLuwjistik.ActorName,
			HistoryActorRole:   model.VENDOR,
			HistoryCreatedAt:   form.EventPayload.Update.UpdateTimestampTime,
			HistoryCreatedBy:   0,
			HistoryCreatedName: model.AccountLuwjistik.ActorName,
			HistoryRemark:      tempRemarksPieceHistory.ToString(),
		})

		// Adding time STI-DEST status STI
		listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
			SttNo:         val.SttNo,
			SttStatus:     model.STIDEST,
			SttStatusTime: now,
		})

		request.Stt = append(request.Stt, model.Stt{
			SttNo:               sttRow.SttNo,
			SttLastStatusID:     model.STIDEST,
			SttUpdatedActorID:   dbr.NewNullInt64(model.AccountLuwjistik.ActorID),    // req.PartnerID
			SttUpdatedActorRole: dbr.NewNullString(model.VENDOR),                     // req.PartnerType
			SttUpdatedActorName: dbr.NewNullString(model.AccountLuwjistik.ActorName), // req.PartnerName
		})

		sttNoSuccess[val.SttNo] = val.Stt

	}

	stiDestRequest := model.StiDest{
		StiDestPartnerID:         model.AccountLuwjistik.ActorID,
		StiDestPartnerCode:       model.LuwjistikName,
		StiDestPartnerName:       model.AccountLuwjistik.ActorName,
		StiDestTotalStt:          1,
		StiDestTotalGrossWeight:  totalGrossWeight,
		StiDestTotalVolumeWeight: totalVolumeWeight,
		StiDestTotalPiece:        len(request.StiDestDetail),
		StiDestStatusType:        model.STIDEST,
		StiDestArrivalCityCode:   model.LuwjistikName,
		StiDestArrivalCityName:   model.AccountLuwjistik.ActorName,
		StiDestCustomCargoType:   "",
		StiDestCustomCargoNo:     "",
		StiDestStatusUpdatedAt:   now,
		StiDestCreatedAt:         now,
		StiDestCreatedBy:         model.AccountLuwjistik.ActorID,
		StiDestCreatedName:       model.AccountLuwjistik.ActorName,
		StiDestUpdatedAt:         &now,
	}
	request.StiDest = stiDestRequest

	_, err = c.stiDestRepo.CreateStiDest(selfCtx, &request)
	if err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while querying db",
			"id": "Terjadi kesalahan pada saat query db",
		})
	}

	/*
	 * Updating list time stt status
	 */

	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.sttActivityUc.UpdateSttTime(goCtx, &stt_activity.SttActivityRequest{
			ListSttData: listSttUpdateTime,
		})
	})

	for sttNo, sttData := range sttNoSuccess {
		if model.IsPublishStatusStiDesc[sttData.SttLastStatusID] {
			c.bgStatusSubmit(&bgGatewaySttStatusSubmitReq{
				StatusSubmitParams: &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo: func() string {
							if sttData.SttElexysNo.Valid {
								return sttData.SttElexysNo.Value()
							}
							return sttNo
						}(),
						Datetime:   now.UTC(),
						StatusCode: sttData.SttLastStatusID,
						Location:   cityStt.Code,
						Remarks:    fmt.Sprintf(`Station transit in consolidator destination %s`, model.AccountLuwjistik.ActorName),
						City:       cityStt.Name,
						UpdatedBy:  model.AccountLuwjistik.ActorName,
						UpdatedOn:  now.UTC(),
					},
					ServiceType:      model.PACKAGESERVICE,
					Product:          sttData.SttProductType,
					Pieces:           sttData.SttTotalPiece,
					GrossWeight:      sttData.SttGrossWeight,
					VolumeWeight:     sttData.SttVolumeWeight,
					ChargeableWeight: sttData.SttChargeableWeight,
					BookedForType:    sttData.SttBookedForType,
				},
				History:     request.History,
				PartnerName: model.AccountLuwjistik.ActorName,
			})
		}

		if !model.IsPublishStatusStiDesc[sttData.SttLastStatusID] && strings.EqualFold(sttData.SttBookedForType, model.CLIENT) {
			go lputils.TrackGoroutine(func(goCtx context.Context) {
				err := c.middlewareRepo.SubmitDataToMiddleware(goCtx, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttID: sttData.SttID,
						SttNo: func() string {
							if sttData.SttElexysNo.Valid {
								return sttData.SttElexysNo.Value()
							}
							return sttNo
						}(),
						Datetime:   now.UTC(),
						Location:   cityStt.Code,
						Remarks:    fmt.Sprintf(`Station transit in consolidator destination %s`, model.AccountLuwjistik.ActorName),
						City:       cityStt.Name,
						UpdatedBy:  model.AccountLuwjistik.ActorName,
						StatusCode: sttData.SttLastStatusID,
						UpdatedOn:  now.UTC(),
					},
					ServiceType:      model.PACKAGESERVICE,
					Product:          sttData.SttProductType,
					Pieces:           sttData.SttTotalPiece,
					GrossWeight:      sttData.SttGrossWeight,
					VolumeWeight:     sttData.SttVolumeWeight,
					ChargeableWeight: sttData.SttChargeableWeight,
				})
				if err != nil {
					logger.Ef(`%s-SubmitDataToMiddleware Error %s`, opName, err.Error())
				}
			})
		}

	}

	go lputils.TrackGoroutine(func(goCtx context.Context) {
		if err == nil {
			sttRow.SttLastStatusID = model.STIDEST
			c.messageGatewayUc.SendMessage(goCtx, &model.SendMessageRequest{
				RecieverNumber: model.RecieverNumber{
					PackageSender:   sttRow.SttSenderPhone,
					PackageReceiver: sttRow.SttRecipientPhone,
				},
				PackageType:       shared.GetPackageType(sttRow.SttIsCOD, sttRow.SttIsDFOD),
				EventStatus:       model.STIDEST,
				DriverName:        sttRow.SttSenderName,
				DriverPhoneNumber: sttRow.SttSenderPhone,
				Token:             accountToken.Data.Token,
			}, &sttRow.Stt)
		}
		c.rtcUc.UpdateInactiveRTCBySttId(goCtx, int(sttRow.SttID))
	}, 5*60)

	return nil
}
