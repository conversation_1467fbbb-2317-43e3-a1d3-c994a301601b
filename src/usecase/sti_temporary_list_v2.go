package usecase

import (
	"context"
	"encoding/json"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/sti_temporary"
)

func (c *stiCtx) ViewStiTemporaryV2(ctx context.Context, params *sti_temporary.StiTemporaryListV2Request) ([]sti_temporary.StiTemporaryListV2Response, error) {
	opName := "UsecaseSti-ViewStiTemporaryV2"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	data := []sti_temporary.StiTemporaryListV2Response{}
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": data, "error": err})
	}()

	if len(params.SttOrBag) >= 2 && shared.IsSttNoFormatLike(params.SttOrBag) {
		params.SttNo = params.SttOrBag
	} else {
		params.BagNo = params.SttOrBag
	}
	isActive := true
	params.IsActive = &isActive
	stiTemporaries, err := c.stiTemporaryRepo.SelectList(selfCtx, params)
	for i := range stiTemporaries {
		stiMeta := stiTemporaries[i].StMetaToStruct()
		if stiMeta == nil {
			continue
		}

		if c.isViewStiTemporaryV2IsFilteredByMeta(params, stiMeta) {
			continue
		}

		mapingMeta := make(map[string]model.StiTemporaryMeta)
		listPdDeadlineReturn := make(map[string]time.Time)

		mapingMeta[stiTemporaries[i].StSttNo] = *stiMeta
		stiMeta.SttNeedToRelabel = c.getValueSttNeedToRelabel(stiTemporaries[i].SttMeta)
		listPdDeadlineReturn[stiTemporaries[i].StSttNo] = stiTemporaries[i].StDeadlineReturn

		data = append(data, sti_temporary.StiTemporaryListV2Response{
			StID:             stiTemporaries[i].StID,
			StAccountID:      stiTemporaries[i].StAccountID,
			StIsActive:       stiTemporaries[i].StIsActive,
			StSttNo:          stiTemporaries[i].StSttNo,
			StProduct:        stiTemporaries[i].StProduct,
			StOrigin:         stiTemporaries[i].StOrigin,
			StDestination:    stiTemporaries[i].StDestination,
			StRegionID:       stiTemporaries[i].StRegionID,
			StDeadlineReturn: stiTemporaries[i].StDeadlineReturn,
			StMeta:           *stiMeta,
			StCreatedAt:      stiTemporaries[i].StCreatedAt,
			StUpdatedAt:      stiTemporaries[i].StUpdatedAt,
			StBookedID:       stiTemporaries[i].StBookedID,
			StBookedName:     stiTemporaries[i].StBookedName.Value(),
			StBookedType:     stiTemporaries[i].StBookedType.Value(),
		})
	}

	return data, nil
}

func (c *stiCtx) isViewStiTemporaryV2IsFilteredByMeta(params *sti_temporary.StiTemporaryListV2Request, stiMeta *model.StiTemporaryMeta) bool {
	isStiDest := stiMeta.IsStiDest == 1

	filteredByIsStiDest := params.IsStiDest != nil && *params.IsStiDest != isStiDest

	filteredByStatusReturn := params.StatusReturn != `` && params.StatusReturn != stiMeta.StatusReturn

	filteredByNoRef := params.NoRef != `` && params.NoRef != stiMeta.RefNo

	return filteredByIsStiDest || filteredByStatusReturn || filteredByNoRef || c.isViewStiTemporaryV2IsFilteredBagByMeta(params.BagNo, stiMeta.BagNo)
}

func (c *stiCtx) isViewStiTemporaryV2IsFilteredBagByMeta(searchBag, bagNo string) bool {
	filteredByBagNo := searchBag != `` && bagNo == ``

	if searchBag != `` && bagNo != `` {
		filteredByBagNo = !shared.PrefixSearch(bagNo, searchBag)
	}

	return filteredByBagNo
}

func (c *stiCtx) getValueSttNeedToRelabel(sttMeta string) bool {
	if sttMeta == `` {
		return false
	}

	sttMetaStruct := new(model.SttMeta)
	err := json.Unmarshal([]byte(sttMeta), &sttMetaStruct)
	if err != nil {
		return false
	}
	if sttMetaStruct != nil && sttMetaStruct.AssessmentRelabel != nil {
		return true
	}
	return false
}
