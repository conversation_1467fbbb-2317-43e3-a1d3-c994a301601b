package usecase

import (
	"context"
	"fmt"
	"strings"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/general"
	"github.com/Lionparcel/hydra/src/usecase/handover"
)

func (c *handoverCtx) ViewDetailStt(ctx context.Context, params *handover.ViewDetailSttRequest) (*handover.ViewDetailSttResponse, error) {
	opName := "UsecaseHandover-ViewDetailStt"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	res := new(handover.ViewDetailSttResponse)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "err": err})
	}()

	if err := params.Validate(); err != nil {
		return nil, err
	}

	isBagScan := false
	sttNos := map[string]bool{}
	if params.BagNo != "" {
		isBagScan = true
	}

	// check is account type valid
	if strings.ToLower(params.PartnerType) != model.CONSOLE && strings.ToLower(params.PartnerType) != model.SUBCONSOLE {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Account Type invalid",
			"id": "Tipe akun tidak valid",
		})
	}

	if isBagScan {
		data, err := c.bagRepo.SelectBagWithSTT(selfCtx, &model.BagViewParams{
			BagCode:             params.BagNo,
			IsDeletedFalse:      true,
			IsNotAvailableFalse: true,
		})
		if err != nil || len(data) == 0 {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Bag Not Found",
				"id": "Bag tidak ditemukan ",
			})
		}
		for _, val := range data {
			sttNos[val.SttNo] = true
		}
	}

	if !isBagScan {
		sttNos[params.SttNo] = true
	}

	districtByCode := make(map[string]*model.District)
	for valSttNo, _ := range sttNos {

		// is stt exist
		stt, err := c.sttRepo.SelectDetail(selfCtx, 0, valSttNo, false)
		if err != nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting STT",
				"id": "Terjadi kesalahan pada saat getting STT",
			})
		}

		if len(stt) == 0 {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt Not Found",
				"id": "Stt tidak ditemukan",
			})
		}

		sttRow := stt[0]
		token := ctx.Value(`tokenStr`).(string)

		if err = validateHub2HubProductType(sttRow.SttProductType); err != nil {
			return nil, err
		}

		// is destination city same with subconsolidator’s city
		partnerParent, err := c.partnerRepo.GetByID(selfCtx, params.PartnerID, token)
		if err != nil || partnerParent == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting Partner Info",
				"id": "Terjadi kesalahan pada saat getting Partner Info",
			})
		}

		// get information district and city
		detailDistrict, ok := districtByCode[sttRow.SttDestinationDistrictID]
		if !ok {
			detailDistrict, err = c.districtRepo.GetByCode(
				selfCtx,
				&model.CredentialRestAPI{Token: token},
				sttRow.SttDestinationDistrictID,
			)
			if err != nil || detailDistrict == nil {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "An error occurred while getting Detail District",
					"id": "Terjadi kesalahan pada saat getting Detail District",
				})
			}
			districtByCode[sttRow.SttDestinationDistrictID] = detailDistrict
		}

		params.PartnerCode = strings.ToUpper(params.PartnerCode)

		if params.PartnerCode != model.LUWJISTIC && partnerParent.Data.PartnerLocation.CityCode != sttRow.SttDestinationCityID {
			res.BagStt = make([]general.SttResponse, 0)
			res.IsAllowUpdateStatus = false
			res.ErrorMessage = `STT cannot be processed`
			if !partnerParent.IsFromIndonesia() {
				res.ErrorMessage = `STT tidak dapat diproses`
			}
			return res, nil
		}

		if !model.IsAllowUpdateToHND[sttRow.SttLastStatusID] {
			res.BagStt = make([]general.SttResponse, 0)
			res.IsAllowUpdateStatus = false
			res.ErrorMessage = `STT cannot be processed`
			if partnerParent.IsFromIndonesia() {
				res.ErrorMessage = `STT tidak dapat diproses`
			}
			return res, nil
		}

		if params.PartnerCode == model.LUWJISTIC && sttRow.SttProductType != model.INTERPACK {
			res.BagStt = make([]general.SttResponse, 0)
			res.IsAllowUpdateStatus = false
			res.ErrorMessage = `Luwjistik must interpack product type`
			if partnerParent.IsFromIndonesia() {
				res.ErrorMessage = `Luwjistik harus interpack product type`
			}
			return res, nil
		}

		if sttRow.SttProductType == model.INTERPACK && !model.IsAllowUpdateSTTInterpackToHND[sttRow.SttLastStatusID] {
			res.BagStt = make([]general.SttResponse, 0)
			res.IsAllowUpdateStatus = false
			res.ErrorMessage = `STT cannot be processed`
			if partnerParent.IsFromIndonesia() {
				res.ErrorMessage = `STT tidak dapat diproses`
			}
			return res, nil
		}

		// check if already update to HND before
		if sttRow.SttLastStatusID == model.CLAIM {
			history, err := c.sttPieceHistoryRepo.Get(selfCtx, &model.SttPieceHistoryViewParam{
				SttPieceHistorySttPieceID: sttRow.SttPieceID,
				SttPieceHistoryStatus:     model.HND,
			})

			if err != nil || history != nil {
				res.BagStt = make([]general.SttResponse, 0)
				res.IsAllowUpdateStatus = false
				res.ErrorMessage = `STT cannot be processed`
				if partnerParent.IsFromIndonesia() {
					res.ErrorMessage = `STT tidak dapat diproses`
				}
				return res, nil
			}
		}

		isPaid := false
		if sttRow.SttPaymentStatus == "" || sttRow.SttPaymentStatus == model.PAID {
			isPaid = true
		}

		isSttFromAutoCA := shared.GetPrefixSttNo(sttRow.Stt.SttNo) == model.PrefixAutoCA
		isSttRowShipment := sttRow.Stt.SttShipmentID != ``
		isFromCustomerAppRetailCOD := (isSttFromAutoCA && isSttRowShipment) && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(sttRow.Stt.SttShipmentID)]
		if isFromCustomerAppRetailCOD {
			isPaid = true
		}

		// detail response
		sttResponse := general.SttResponse{
			SttID:                sttRow.SttID,
			SttNo:                sttRow.SttNo,
			SttProductType:       sttRow.SttProductType,
			SttDestinationCityID: sttRow.SttDestinationCityID,
			SttGrossWeight:       sttRow.SttGrossWeight,
			SttVolumeWeight:      sttRow.SttVolumeWeight,
			SttChargeableWeight:  sttRow.SttChargeableWeight,
			SttLastStatusID:      sttRow.SttLastStatusID,
			SttTotalPiece:        sttRow.SttTotalPiece,
			SttElexysNo:          sttRow.SttElexysNo.Value(),
		}

		if len(params.PartnerCode) > 0 {

			isDifferentVendor := len(detailDistrict.Data.VendorCode) > 0 && detailDistrict.Data.VendorCode != params.PartnerCode

			if isDifferentVendor {
				if isBagScan {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Some districts in the STT do not match the vendor area. Please check again",
						"id": "Sebagian kecamatan pada STT tidak sesuai area vendor. Silakan cek kembali",
					})
				} else {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": fmt.Sprintf("STT must be handover by vendor <b>%s</b>", detailDistrict.Data.VendorCode),
						"id": fmt.Sprintf("STT ini harus dihandover ke vendor <b>%s</b>", detailDistrict.Data.VendorCode),
					})

				}

			}

			isNotValidVendor := len(detailDistrict.Data.VendorCode) < 1 && c.cfg.IsHandoverScanPartnerValidVendor()[params.PartnerCode]
			// Jika partner code terisi tetapi data vendor kosong
			if isNotValidVendor {
				if isBagScan {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Some districts in the STT do not match the vendor area. Please check again",
						"id": "Sebagian kecamatan pada STT tidak sesuai area vendor. Silakan cek kembali",
					})
				}
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "District in the STT not vendor area, Please check your STT number",
					"id": "Kecamatan STT bukan area vendor. Silakan cek nomor STT",
				})
			}
		}

		sttResponse.SttDestinationCityID = detailDistrict.Data.City.Code
		sttResponse.SttDestinationCityName = detailDistrict.Data.City.Name

		// get information woodpacking
		optionalRate, err := c.optionalRate.Select(selfCtx, &model.SttOptionalRate{
			SttOptionalRateSttID:  sttRow.SttID,
			SttOptionalRateParams: model.WOODPACKING,
		})
		if err != nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting Optional Rate",
				"id": "Terjadi kesalahan pada saat getting Optional Rate",
			})
		}

		if len(optionalRate) > 0 {
			sttResponse.SttWoodPacking = `Yes`
		} else {
			sttResponse.SttWoodPacking = `No`
		}

		// get infomation commodity
		commodity, err := c.commodityRepo.GetCommodityByCode(selfCtx, sttRow.SttCommodityCode, token)
		if err != nil || commodity == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting Commodity",
				"id": "Terjadi kesalahan pada saat getting Commodity",
			})
		}

		sttResponse.SttCommodityCode = commodity.Data.CommodityCode
		sttResponse.SttCommodityName = commodity.Data.CommodityName
		sttResponse.SttCommodityNameEN = commodity.Data.CommodityNameEn
		if sttRow.SttCODAmount > 0 {
			sttResponse.SttCODAmount = sttRow.SttCODAmount
		}

		for _, val := range stt {
			sttResponse.Piece = append(sttResponse.Piece, general.SttPieceResponse{
				SttPieceID:           val.SttPieceID,
				SttPieceLastStatusID: val.SttPieceLastStatusID,
				SttPieceNo:           val.SttPieceNo,
				SttPieceGrossWeight:  val.SttPieceGrossWeight,
				SttPieceVolumeWeight: val.SttPieceVolumeWeight,
			})
		}

		if isBagScan {
			res.BagStt = append(res.BagStt, sttResponse)
		}

		if !isBagScan {
			res.Stt = &sttResponse
		}

		res.IsAllowUpdateStatus = true
		res.IsPaid = isPaid
	}

	return res, nil
}

func validateHub2HubProductType(productType string) error {
	if model.IsNotAllowStatusHubToHub[productType] {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "The STT failed to scan because the package will be given POD status after it is received by the recipient",
			"id": "STT gagal discan karena paket akan diberi status POD setelah diambil oleh penerima.",
		})
	}
	return nil
}
