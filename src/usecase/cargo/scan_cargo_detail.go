package cargo

import (
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
)

type IsAllowUpdateStatusCargoRequest struct {
	BagOrStt    ScanBagOrSttRequest `json:"bag_or_stt" query:"bag_or_stt" form:"bag_or_stt"`
	PartnerID   int                 `json:"partner_id" query:"partner_id" form:"partner_id"`
	PartnerType string
	AccountType string
	Token       string
}

type ScanBagOrSttRequest struct {
	BagNo string `json:"bag_no"`
	SttNo string `json:"stt_no"`
}

type IsAllowUpdateStatusCargoResponse struct {
	IsAllowUpdateStatus bool                `json:"is_allow_update_status"`
	ErrorMessage        string              `json:"error_message"`
	BagOrStt            []ScanCargoResponse `json:"bag_or_stt"`
}

type SttWithBagNoResponse struct {
	Stt                  model.Stt
	SttNoElexys          string
	BagNo                string
	BagCustomGrossWeight float64
	BagGrossWeight       float64

	BagCustomVolumeWeight float64
	BagCustomLength       float64
	BagCustomWidth        float64
	BagCustomHeight       float64
}

type SttPiece struct {
	SttPieceLength       float64 `json:"stt_piece_length"`
	SttPieceWidth        float64 `json:"stt_piece_width"`
	SttPieceHeight       float64 `json:"stt_piece_height"`
	SttPieceGrossWeight  float64 `json:"stt_piece_gross_weight"`
	SttPieceVolumeWeight float64 `json:"stt_piece_volume_weight"`
}

type ScanCargoResponse struct {
	BagNo                   string               `json:"bag_no"`
	BagVendorNo             string               `json:"bag_vendor_no"`
	SttID                   int                  `json:"stt_id"`
	SttNo                   string               `json:"stt_no"`
	SttNoElexys             string               `json:"stt_no_elexys"`
	SttTotalPiece           int                  `json:"stt_total_piece"`
	GrossWeight             float64              `json:"gross_weight"`
	BagData                 ScanCargoResponseBag `json:"bag_data"`
	VolumeWeight            float64              `json:"volume_weight"`
	ProductType             string               `json:"product_type"`
	CommodityID             int                  `json:"commodity_id"`
	CommodityName           string               `json:"commodity_name"`
	CommodityCode           string               `json:"commodity_code"`
	CommodityGroupCode      string               `json:"commodity_group_code"`
	CommodityGroupName      string               `json:"commodity_group_name"`
	IsPaid                  bool                 `json:"is_paid"`
	RegionID                string               `json:"region_id"`
	RegionName              string               `json:"region_name"`
	DestinationCityCode     string               `json:"destination_city_code"`
	DestinationCityName     string               `json:"destination_city_name"`
	DestinationDistrictCode string               `json:"destination_district_code"`
	DestinationDistrictName string               `json:"destination_district_name"`
	ShcCode                 string               `json:"shc_code"`
	ShcDescription          string               `json:"shc_description"`
	SttAssessmentStatus     string               `json:"stt_assessment_status"`
	SttPieces               []SttPiece           `json:"stt_pieces"`
}

type ScanCargoResponseBag struct {
	BagCustomGrossWeight float64 `json:"bag_custom_gross_weight,omitempty"`
	BagGrossWeight       float64 `json:"bag_gross_weight,omitempty"`
	BagCustomLength      float64 `json:"bag_custom_length,omitempty"`
	BagCustomWidth       float64 `json:"bag_custom_width,omitempty"`
	BagCustomHeight      float64 `json:"bag_custom_height,omitempty"`
}

func (c *IsAllowUpdateStatusCargoRequest) Validate() error {
	if c.AccountType == model.INTERNAL {
		if c.PartnerID < 1 {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner ID cannot be empty",
				"id": "Partner ID tidak boleh kosong",
			})
		}
	}

	return nil
}
