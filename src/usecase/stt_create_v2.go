package usecase

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/abiewardani/dbr/v2"
	"github.com/google/uuid"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/gateway_stt"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *sttCtx) CreateSTTV2(ctx context.Context, params *stt.CreateSttRequest) (*stt.CreateSttResponse, error) {
	opName := "UsecaseStt-CreateSTTV2"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res := stt.CreateSttResponse{}

	paramsCreateStt := make([]model.SttCreate, 0)
	var (
		referenceRole  string
		referenceCode  string
		BookedForActor *model.Actor
		errLog         error
	)

	defer func() {
		if params.Stt[0].SttNoRefExternal != "" {
			c.sttRepo.DeleteCacheOnce(selfCtx, fmt.Sprintf("%v:%v", model.CacheRefNoExternal, params.Stt[0].SttNoRefExternal))
		}
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": errLog})
	}()

	sttData := params.Stt[0]

	errInvalid := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "Invalid Account Type format",
		"id": "Format Account Type tidak valid",
	})

	now, _ := shared.ParseUTC7(shared.FormatDateTime, c.timeRepo.Now(time.Now()).Format(shared.FormatDateTime))

	err := sttData.Validate(0, params.Source)
	if err != nil {
		return nil, err
	}

	if err := c.validateSenderPhone(selfCtx, sttData, ``); err != nil {
		return nil, err
	}

	err = c.validatePartnerDestination(selfCtx, params)
	if err != nil {
		return nil, err
	}

	err = sttData.ValidateInterpack(0, params.Source)
	if err != nil {
		return nil, err
	}

	validate, err := c.accountRepo.ValidateAccount(selfCtx, params.Token, false)
	if err != nil {
		return nil, err
	}
	if !validate.IsAllow {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Account is not eligible to create STT",
			"id": "Akun tidak diperbolehkan untuk membuat STT",
		})
	}

	sttData.IsMixpack = sttData.SttProductType == model.MIXPACK

	if params.Source == model.ALGO && len(sttData.SttShipmentID) < 4 {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "ShipmentID is required to be filled correctly",
			"id": "ShipmentID harus di isi dengan benar",
		})
	}

	// source algo & account partner validation
	err = sttData.ValidateJumbopack(stt.ValidateJumbopackParams{
		Source:       params.Source,
		AccountType:  params.AccountType,
		IsPartnerPcu: true,
		SenderName:   sttData.SttSenderName,
		SenderPhone:  sttData.SttSenderPhone,
	})
	if err != nil {
		errLog = err
		return nil, err
	}

	/**
	 * get client or partner
	 */
	actorExternalCode := ``     // this value is external code from partner or client
	bookedForExternalCode := `` // this value is external code from partner or client that got booked
	switch params.AccountType {
	case model.PARTNER:

		if params.AccountRefType != model.PARTNERPOS {
			return nil, errInvalid
		}

		actorExternalCode = params.Partner.PartnerExternalCode

		if params.Partner.PartnerLocation == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner is not yet mapped with location",
				"id": "Partner belum dipetakan dengan lokasi",
			})
		}

		actorExternalCode = params.Partner.PartnerExternalCode

		sttData.SttOriginDistrictID = params.Partner.PartnerLocation.DistrictCode

		referenceRole = params.AccountRefType
		referenceCode = params.Partner.Code

		partnerBooking, err := c.getPartrnerCreateSTTV2(selfCtx, params, sttData)
		if err != nil {
			return nil, err
		}
		// isPartnerPCU validation
		err = sttData.ValidateJumbopack(stt.ValidateJumbopackParams{
			Source:       params.Source,
			AccountType:  params.AccountType,
			IsPartnerPcu: partnerBooking.Data.PartnerIsPCU,
			SenderName:   sttData.SttSenderName,
			SenderPhone:  sttData.SttSenderPhone,
		})
		if err != nil {
			errLog = err
			return nil, err
		}

		if partnerBooking.Data.PartnerPosParentID > 0 && params.Source == model.ALGO && !model.IsAllowPosCabangShipment[shared.GetPrefixShipmentID(sttData.SttShipmentID)] {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner POS is branch and only allow shipment id with prefix [AD/ACB/B2/T1/TKLP/ARB]",
				"id": "Partner POS adalah cabang dan shiment id hanya boleh dengan prefix [AD/ACB/B2/T1/TKLP/ARB]",
			})
		}

		BookedForActor = &model.Actor{
			ID:    partnerBooking.Data.ID,
			Name:  partnerBooking.Data.Name,
			Code:  partnerBooking.Data.Code,
			IsCod: partnerBooking.Data.PartnerIsCodBooking,
			Type:  model.POS,
			ActorPosDetail: model.ActorPos{
				PosBranchCommission: partnerBooking.Data.PartnerPosBranchCommission,
				PosParentID:         partnerBooking.Data.PartnerPosParentID,
			},
		}

	case model.CLIENT:
		if err = c.validateSttClientCreateSTTV2(params, sttData, errInvalid); err != nil {
			return nil, err
		}

		clientCode := model.MappingClientIDWithClientCode[params.AccountRefID]
		clientBooking, ok := model.MappingClientActor[clientCode]
		if !ok {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Client Not Found",
				"id": "Client Tidak Ditemukan",
			})
		}
		actorExternalCode = clientBooking.ClientElexysCode
		sttData.SttOriginDistrictID = clientBooking.ClientDistrictCode
		referenceRole = strings.ToLower(model.CLIENT)
		referenceCode = clientBooking.ClientCode

	default:
		return nil, errInvalid
	}

	bookedForExternalCode = actorExternalCode // default is the same as the one who created booking
	if sttData.SttNoRefExternal != `` {
		extNumberUsed := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "The external reference number is already in use, please check and change it.",
			"id": "Nomor referensi eksternal sudah terpakai, cek & ubah lagi.",
		})

		flag := c.sttRepo.GetCacheRefExternalFlag(selfCtx, sttData.SttNoRefExternal)

		if flag {
			return nil, extNumberUsed
		} else {
			sttCheck, err := c.sttRepo.Get(selfCtx, &model.SttViewDetailParams{
				Stt: model.Stt{SttNoRefExternal: sttData.SttNoRefExternal},
			})

			if err != nil {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Failed to retrieved stt",
					"id": "Gagal mendapatkan stt",
				})
			}

			if (sttCheck != nil && sttCheck.SttID > 0) || !c.sttRepo.CreateCacheRefExternalFlag(selfCtx, sttData.SttNoRefExternal) {
				return nil, extNumberUsed
			}
		}
	}

	sttData.SttDestinationCityID = params.DistrictDestination.City.Code
	sttData.SttOriginCityID = params.DistrictOrigin.City.Code

	// get origin city timezone since there is no origin city timezone from params
	originCity, err := c.cityRepo.Get(selfCtx, sttData.SttOriginCityID, params.Token)
	if err != nil || originCity == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid Origin City",
			"id": "Origin City tidak valid",
		})
	}
	params.DistrictOrigin.City.Timezone = originCity.Timezone

	// validate insurance type
	if params.Source == model.MANUAL {
		if sttData.SttGoodsEstimatePrice >= model.MIN_INSURANCE_BASIC {
			sttData.SttInsuranceType = model.INSURANCEBASIC
		}
		// takeout woodpacking
		sttData.SttIsWoodpacking = false
	}

	isDisablePromo := !c.checkCalculateTariffPromoElligible(referenceRole, model.CLIENT, params.Source)
	promoAppliedTo := ``

	// if tarif get from algo and prefix  AG / AI / AD / ACA / AO / ACB / AP / AS, should be disable promo set false
	if params.Source == model.ALGO && sttData.SttShipmentID != "" && model.PrefixShipmentIDIsAllowdPromoDiscont[shared.GetPrefixShipmentID(sttData.SttShipmentID)] {
		isDisablePromo = false
		promoAppliedTo = model.PromoAppliedToShipment
	}

	/**
	 * get check tariff
	 */

	reqCheckTariff := &stt.CalculateTariffParams{
		RequestCalculateTariff: &stt.RequestCalculateTariff{
			OriginID:       sttData.SttOriginDistrictID,
			DestinationID:  sttData.SttDestinationDistrictID,
			ProductType:    sttData.SttProductType,
			CommodityID:    params.Commodity.CommodityID,
			InsuranceType:  sttData.SttInsuranceType,
			GoodsPrice:     sttData.SttGoodsEstimatePrice,
			IsWoodpacking:  sttData.SttIsWoodpacking,
			AccountType:    referenceRole,
			AccountRefID:   params.AccountRefID,
			IsHaveTaxID:    false,
			Commodity:      model.Commodity{Data: params.Commodity},
			IsDisablePromo: isDisablePromo,
			PromoAppliedTo: promoAppliedTo,
		},
		Token: params.Token,
	}

	reqCheckTariff.RequestCalculateTariff.GeneratePiecesCalculateTariff(sttData.SttPieces)

	if sttData.SttShipmentID != `` {
		reqCheckTariff.RequestCalculateTariff.ShipmentPrefix = shared.GetPrefixShipmentID(sttData.SttShipmentID)
	}

	var (
		// COD Retail
		percentageCodFee          = 0.0
		minimumCodFee             = 0.0
		originCommissionRate      = 0.0
		destinationCommissionRate = 0.0
		codIsInsurance            bool
		codAmountConfigType       string
		totalTariffReturn         = 0.0
		codHandling               string

		// COD Client
		clientPaymentMethod       = ""
		clientCodConfigAmount     = ""
		clientCodShipmentDiscount = 0.0
		rateVatShipment           = 0.0
		rateVatCod                = 0.0
		originMinCommission       = 0
		destinationMinCommission  = 0
	)

	// if tarif get from algo, should be use client tariff BL/TP/CA ,shipment AP & AS prefix using client tarif)
	if shared.GetPrefixShipmentID(sttData.SttShipmentID) == model.C1 || shared.GetPrefixShipmentID(sttData.SttShipmentID) == model.C2 {
		shipment, err := c.shipmentRepo.Get(selfCtx, &model.ShipmentViewParams{
			ShipmentAlgoID: sttData.SttShipmentID,
		})
		if err != nil || shipment == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Shipment Not Found",
				"id": "Shipment Tidak Ditemukan",
			})
		}
		if len(shipment.ShipmentPackets) > 0 {
			spcCode := shipment.ShipmentPackets[0].ShipmentPacketCustomerCode
			if spcCode == "" {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Shipment Packet Customer Code cannot be empty",
					"id": "Shipment Packet Customer Code tidak boleh kosong",
				})
			}

			client, err := c.clientRepo.GetByCode(selfCtx, spcCode, params.Token)
			if err != nil || client == nil {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Client Not Found",
					"id": "Client Tidak Ditemukan",
				})
			}

			// validation parent only
			if client.Data.ClientParentID > 0 {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Client is not parent",
					"id": "Client bukan parent",
				})
			}

			spcBranchCode := shipment.ShipmentPackets[0].ShipmentPacketCustomerBranchCode
			if spcBranchCode == "" {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Shipment Packet Customer Branch Code cannot be empty",
					"id": "Shipment Packet Customer Branch Code tidak boleh kosong",
				})
			}

			clientBranch, err := c.clientRepo.GetByCode(selfCtx, spcBranchCode, params.Token)
			if err != nil || client == nil {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Client Branch Not Found",
					"id": "Client Branch Tidak Ditemukan",
				})
			}

			// validation parent ID
			if clientBranch.Data.ClientParentID != client.Data.ClientID {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Client parent incorrect",
					"id": "Client induk salah",
				})
			}

			// set check tariff should be get from client
			reqCheckTariff.RequestCalculateTariff.AccountType = model.CLIENT
			reqCheckTariff.RequestCalculateTariff.AccountRefID = clientBranch.Data.ClientID

			bookedForExternalCode = clientBranch.Data.ClientElexysCode

			if sttData.SttIsCOD {
				shipmentMeta := shipment.ShipmentMetaToStruct()
				if shipmentMeta != nil {
					codHandling = shipmentMeta.CodHandling
				}

				reqCheckTariff.RequestCalculateTariff.CodHandling = codHandling

				if codHandling == model.SPECIALCOD {
					// COD Retail

					codConfig, err := c.codConfigRepo.ViewCodConfig(selfCtx, params.Token)
					if err != nil || codConfig == nil || len(codConfig.Data) == 0 {
						errLog = err
						return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
							"en": "COD configuration not found",
							"id": "Konfigurasi COD tidak ditemukan",
						})
					}

					config := codConfig.Data[0]
					for _, data := range codConfig.Data {
						if data.CcoType == model.CcoTypeRetail {
							config = data
							break
						}
					}

					ccoProductTypeList := strings.Split(config.CcoProductType, ",")
					for i, ccoProductType := range ccoProductTypeList {
						if ccoProductType == sttData.SttProductType {
							break
						}
						if i == len(ccoProductTypeList)-1 {
							return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
								"en": "Product type is not supported for COD services",
								"id": "Tipe produk tidak didukung untuk layanan COD",
							})
						}
					}

					errValidate := c.validateSTTPriceIsCommon(sttData, config, "COD")
					if errValidate != nil {
						return nil, errValidate
					}

					if config.CcoIsInsurance && sttData.SttInsuranceType != model.INSURANCEFREE {
						sttData.SttInsuranceType = model.INSURANCEFREE
						reqCheckTariff.RequestCalculateTariff.InsuranceType = model.INSURANCEFREE
					}

					percentageCodFee = config.CcoPercentageCod
					minimumCodFee = config.CcoMinCodFee
					originCommissionRate = config.CcoOriginCommissionRate
					destinationCommissionRate = config.CcoDestinationCommissionRate
					codIsInsurance = config.CcoIsInsurance
					codAmountConfigType = config.CcoCodType

					// check tariff return
					reqCalcTariff := *reqCheckTariff.RequestCalculateTariff
					reqCalcTariff.IsCod = false
					reqCalcTariff.IsDisablePromo = true
					reqCalcTariff.OriginID = reqCheckTariff.RequestCalculateTariff.DestinationID
					reqCalcTariff.DestinationID = reqCheckTariff.RequestCalculateTariff.OriginID
					reqCheckTariffReturn := &stt.CalculateTariffParams{
						RequestCalculateTariff: &reqCalcTariff,
						Token:                  params.Token,
					}
					checkTariffReturn, err := c.checkTariffRepo.TariffCalculationV2(selfCtx, reqCheckTariffReturn)
					if err != nil || checkTariffReturn == nil {
						errLog = err
						return nil, err
					}

					totalTariffReturn = checkTariffReturn.Data.TotalTariff

					reqCheckTariff.RequestCalculateTariff.ShipmentPrefix = shared.GetPrefixShipmentID(sttData.SttShipmentID)
					reqCheckTariff.RequestCalculateTariff.IsCod = true
					reqCheckTariff.RequestCalculateTariff.CodAmount = sttData.SttCODAmount

				} else {
					// COD Client
					reqCheckTariff.RequestCalculateTariff.IsCod = true
					reqCheckTariff.RequestCalculateTariff.CodAmount = sttData.SttCODAmount

					rateVatShipment = c.cfg.RateVatShipment()
					rateVatCod = c.cfg.RateVatCod()

					clientCodConfigAmount = clientBranch.Data.ClientCodConfigAmount
					clientPaymentMethod = "invoice"

					if clientBranch.Data.ClientCodConfigAmount == model.GoodsPriceTotalTarif {
						clientPaymentMethod = client.Data.ClientPaymentMethod
						clientCodShipmentDiscount = clientBranch.Data.ClientCodShipmentDiscount
					}

					if sttData.SttIsDFOD {
						clientPaymentMethod = model.ClientPaymentMethodSplitBill
						clientCodShipmentDiscount = clientBranch.Data.ClientCodShipmentDiscount
					}
				}
			}

		} else {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Shipment Packet Not Found",
				"id": "Shipment Packet Tidak Ditemukan",
			})
		}

		if sttData.SttIsDFOD {
			reqCheckTariff.RequestCalculateTariff.IsDfod = true
		}
	} else {
		clientCode := model.MappingShipmentPrefixClientCode[shared.GetPrefixShipmentID(sttData.SttShipmentID)]
		client := &model.Client{
			Data: model.MappingClientActor[clientCode],
		}
		// set check tariff should be get from client
		reqCheckTariff.RequestCalculateTariff.AccountType = model.CLIENT
		reqCheckTariff.RequestCalculateTariff.AccountRefID = client.Data.ClientID

		bookedForExternalCode = client.Data.ClientElexysCode
	}

	if sttData.SttTaxNumber != `` {
		reqCheckTariff.RequestCalculateTariff.IsHaveTaxID = true
	}

	if params.Source == model.ALGO && (sttData.SttIsCOD || sttData.SttIsDFOD) && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(sttData.SttShipmentID)] {

		var config *model.CodConfigBase
		codConfig, err := c.codConfigRepo.ViewCodConfig(selfCtx, params.Token)
		if err != nil || c.isCodConfigEmpty(codConfig) {
			errLog = err
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "COD configuration not found",
				"id": "Konfigurasi COD tidak ditemukan",
			})
		}

		configType := model.CcoTypeCARetail
		configName := `COD`
		if sttData.SttIsDFOD {
			configType = model.CcoTypeDFODCA
			configName = `DFOD`
		}
		for _, data := range codConfig.Data {
			if data.CcoType == configType {
				config = &data
				break
			}
		}

		if config == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": fmt.Sprintf("Failed to fetch data config %s", configName),
				"id": fmt.Sprintf("Gagal mendapatkan data config %s", configName),
			})
		}

		ccoProductTypeList := strings.Split(config.CcoProductType, ",")
		for i, ccoProductType := range ccoProductTypeList {
			if ccoProductType == sttData.SttProductType {
				break
			}
			if i == len(ccoProductTypeList)-1 {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": fmt.Sprintf("Product type is not supported for %s services", configName),
					"id": fmt.Sprintf("Tipe produk tidak didukung untuk layanan %s", configName),
				})
			}
		}

		errValidate := c.validateSTTPriceIsCommon(sttData, *config, configName)
		if errValidate != nil {
			return nil, errValidate
		}

		if config.CcoIsInsurance && sttData.SttInsuranceType != model.INSURANCEFREE {
			sttData.SttInsuranceType = model.INSURANCEFREE
			reqCheckTariff.RequestCalculateTariff.InsuranceType = model.INSURANCEFREE
		}

		percentageCodFee = config.CcoPercentageCod
		minimumCodFee = config.CcoMinCodFee
		originCommissionRate = config.CcoOriginCommissionRate
		destinationCommissionRate = config.CcoDestinationCommissionRate
		codIsInsurance = config.CcoIsInsurance
		codAmountConfigType = config.CcoCodType

		originMinCommission = config.CcoOriginMinCommission
		destinationMinCommission = config.CcoDestinationMinCommission

		// check tariff return
		reqCalcTariff := *reqCheckTariff.RequestCalculateTariff
		reqCalcTariff.IsCod = false
		reqCalcTariff.IsDfod = false
		reqCalcTariff.IsDisablePromo = true
		reqCalcTariff.OriginID = reqCheckTariff.RequestCalculateTariff.DestinationID
		reqCalcTariff.DestinationID = reqCheckTariff.RequestCalculateTariff.OriginID
		reqCheckTariffReturn := &stt.CalculateTariffParams{
			RequestCalculateTariff: &reqCalcTariff,
			Token:                  params.Token,
		}
		checkTariffReturn, err := c.checkTariffRepo.TariffCalculationV2(selfCtx, reqCheckTariffReturn)
		if err != nil || checkTariffReturn == nil {
			errLog = err
			return nil, err
		}

		totalTariffReturn = checkTariffReturn.Data.TotalTariff

		reqCheckTariff.RequestCalculateTariff.ShipmentPrefix = shared.GetPrefixShipmentID(sttData.SttShipmentID)
		reqCheckTariff.RequestCalculateTariff.IsCod = true
		reqCheckTariff.RequestCalculateTariff.CodAmount = sttData.SttCODAmount

		if sttData.SttIsDFOD {
			reqCheckTariff.RequestCalculateTariff.IsDfod = true
		}
	}

	checkTariff, err := c.checkTariffRepo.TariffCalculationV2(selfCtx, reqCheckTariff)
	if err != nil || checkTariff == nil {
		return nil, err
	}

	// validate total stt volume weight and total stt gross weight
	if err := stt.ValidateTotalWeight(checkTariff.Data.VolumeWeight, checkTariff.Data.GrossWeight, params.Stt[0].SttShipmentID, params.Stt[0].SttNoRefExternal); err != nil {
		return nil, err
	}

	IsPrefixCODCARetail := sttData.SttShipmentID != `` && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(sttData.SttShipmentID)]
	if (sttData.SttIsDFOD || sttData.SttIsCOD) && params.AccountType == model.PARTNER && IsPrefixCODCARetail && checkTariff.Data.CodFee > sttData.SttGoodsEstimatePrice {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt Goods Estimate Price must not be lower than COD Fee",
			"id": "Stt Goods Estimate Price tidak boleh lebih kecil dari COD Fee",
		})
	}

	if err := c.checkSaldo(selfCtx, stt.CheckSaldoParams{
		Source:         params.Source,
		AccountRefType: referenceRole,
		Token:          params.Token,
		Stt:            &sttData,
		CheckTariff:    &checkTariff.Data,
	}); err != nil {
		return nil, err
	}

	// build remarks piece history
	tempRemarksPieceHistory := model.RemarkPieceHistory{
		ActorExternalCode:   actorExternalCode,
		ActorExternalType:   params.AccountType,
		HistoryLocationName: params.DistrictOrigin.City.Name,
		BookingFee: func() float64 {
			if reqCheckTariff.RequestCalculateTariff.IsCod && checkTariff.Data.CodFee > 0 {
				return checkTariff.Data.TotalTariff - checkTariff.Data.CodFee
			}
			return checkTariff.Data.TotalTariff
		}(),
		BookingFeeAfterDiscount: func() float64 {
			if reqCheckTariff.RequestCalculateTariff.IsCod && checkTariff.Data.CodFeeAfterDiscount > 0 {
				return checkTariff.Data.TotalTariffAfterDiscount - checkTariff.Data.CodFeeAfterDiscount
			}
			return checkTariff.Data.TotalTariffAfterDiscount
		}(),
		VolumeWeightDiscount:     checkTariff.Data.VolumeWeightDiscount,
		ClientCodBookingDiscount: checkTariff.Data.CODBookingDiscount,
		ChargeableWeight:         checkTariff.Data.ChargeableWeight,
		HistoryDistrictCode:      params.DistrictOrigin.Code,
		HistoryDistrictName:      params.DistrictOrigin.Name,
		HistoryDataAdjustment: &model.HistoryDataAdjustment{
			IsPromo:                  checkTariff.Data.IsPromo,
			TotalTariff:              checkTariff.Data.TotalTariff,
			TotalTariffAfterDiscount: checkTariff.Data.TotalTariffAfterDiscount,
		},
	}

	// set STT meta
	sttMeta := model.SttMeta{
		EstimateSLA:             c.generateEstimaeSlaByProductType(checkTariff.Data.EstimateSLA, sttData.SttProductType, now, params.DistrictOrigin.City.Timezone),
		RoundingDiff:            checkTariff.Data.RoundingDiff,
		OriginCityName:          params.DistrictOrigin.City.Name,
		OriginDistrictName:      params.DistrictOrigin.Name,
		DestinationCityName:     params.DistrictDestination.City.Name,
		DestinationDistrictName: params.DistrictDestination.Name,
		DetailCalculateRetailTariff: []model.DetailCalculateRetailTariff{
			{
				Status:       model.BKD,
				IsCalculated: false,
			},
		},
		PostalCodeDestination: sttData.PostalCodeDestination,

		VolumeWeightDiscount:      checkTariff.Data.VolumeWeightDiscount,
		ClientPaymentMethod:       clientPaymentMethod,
		ClientCodConfigAmount:     clientCodConfigAmount,
		ClientCodShipmentDiscount: clientCodShipmentDiscount,
		RateVatShipment:           rateVatShipment,
		RateVatCod:                rateVatCod,
		ListDiscount:              checkTariff.Data.ListDiscount,
		PostalCodeOrigin:          sttData.PostalCodeOrigin,
	}

	if params.Source == model.ALGO && params.AccountRefType == model.PARTNERPOS {
		clientTariffBeforeDiscount := setDataClientTariffBeforeDisc(checkTariff.Data)

		cityRateAfterDiscount := checkTariff.Data.PublishRateAfterDiscount + checkTariff.Data.ShippingSurchargeRateAfterDiscount
		forwardRateAfterDiscount := checkTariff.Data.OriginDistrictRateAfterDiscount + checkTariff.Data.DestinationDistrictRateAfterDiscount
		shippingCostAfterDiscount := cityRateAfterDiscount + forwardRateAfterDiscount

		sttMeta.ClientTariff = &model.ClientTariff{
			BeforeDiscount: clientTariffBeforeDiscount,
			AfterDiscount: model.ClientTariffDetailWithDiscount{
				ClientTariffDetail: model.ClientTariffDetail{
					CityRates:               cityRateAfterDiscount,
					ForwardRates:            forwardRateAfterDiscount,
					ShippingCost:            shippingCostAfterDiscount,
					CommoditySurcharge:      checkTariff.Data.CommoditySurchargeAfterDiscount,
					HeavyWeightSurcharge:    checkTariff.Data.HeavyWeightSurchargeAfterDiscount,
					DocumentSurcharge:       checkTariff.Data.DocumentSurchargeAfterDiscount,
					InsuranceRates:          checkTariff.Data.InsuranceRatesAfterDiscount,
					WoodpackingRates:        checkTariff.Data.WoodpackingRatesAfterDiscount,
					TotalTariff:             checkTariff.Data.TotalTariffAfterDiscount,
					TaxRates:                checkTariff.Data.TaxRates,
					BMTaxRate:               checkTariff.Data.BMTaxRate,
					PPNTaxRate:              checkTariff.Data.PPNTaxRate,
					PPHTaxRate:              checkTariff.Data.PPHTaxRate,
					OriginDistrictRate:      checkTariff.Data.OriginDistrictRateAfterDiscount,
					DestinationDistrictRate: checkTariff.Data.DestinationDistrictRateAfterDiscount,
					PublishRate:             checkTariff.Data.PublishRateAfterDiscount,
					ShippingSurchargeRate:   checkTariff.Data.ShippingSurchargeRateAfterDiscount,
					CodAmount:               checkTariff.Data.CodAmount,
					CodFee:                  checkTariff.Data.CodFeeAfterDiscount,
				},
				Discount:                 checkTariff.Data.Discount,
				DiscountType:             checkTariff.Data.DiscountType,
				IsPromo:                  checkTariff.Data.IsPromo,
				IsDiscountExceedMaxPromo: checkTariff.Data.IsDiscountExceedMaxPromo,
				TotalDiscount:            checkTariff.Data.TotalDiscount,
			},
		}
	}

	if params.Source == model.ALGO && (sttData.SttIsCOD || sttData.SttIsDFOD) && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(sttData.SttShipmentID)] {
		if sttData.SttIsDFOD {
			sttMeta.DfodConfiguration = &model.DfodConfiguration{
				CodConfiguration: model.CodConfiguration{
					PercentageCodFee:             percentageCodFee,
					MinCodFee:                    minimumCodFee,
					PosOriginCommissionRate:      originCommissionRate,
					PosDestinationCommissionRate: destinationCommissionRate,
					CodIsInsurance:               codIsInsurance,
					CodAmountConfigType:          codAmountConfigType,
				},
				DfodFee:                  checkTariff.Data.CodFee,
				OriginMinCommission:      originMinCommission,
				DestinationMinCommission: destinationMinCommission,
			}
		} else {
			sttMeta.CodConfiguration = &model.CodConfiguration{
				PercentageCodFee:             percentageCodFee,
				MinCodFee:                    minimumCodFee,
				PosOriginCommissionRate:      originCommissionRate,
				PosDestinationCommissionRate: destinationCommissionRate,
				CodIsInsurance:               codIsInsurance,
				CodAmountConfigType:          codAmountConfigType,
			}
		}
		sttMeta.TotalTariffReturn = totalTariffReturn
	}

	if params.Source == model.ALGO && sttData.SttIsCOD && (codHandling == model.SPECIALCOD && (shared.GetPrefixShipmentID(sttData.SttShipmentID) == model.C1 || shared.GetPrefixShipmentID(sttData.SttShipmentID) == model.C2)) {
		sttMeta.CodConfiguration = &model.CodConfiguration{
			PercentageCodFee:             percentageCodFee,
			MinCodFee:                    minimumCodFee,
			PosOriginCommissionRate:      originCommissionRate,
			PosDestinationCommissionRate: destinationCommissionRate,
			CodIsInsurance:               codIsInsurance,
			CodAmountConfigType:          codAmountConfigType,
		}
		sttMeta.TotalTariffReturn = totalTariffReturn
	}

	// flag paid/unpaid
	paymentStatus := ``
	if c.cfg.EnableFeatureHoldCommission() && sttData.SttShipmentID != `` && model.IsPrefixShipmentPaymentHoldValid[shared.GetPrefixShipmentID(sttData.SttShipmentID)] {
		paymentStatus = model.UNPAID
	}

	sttRecipientAddressType := dbr.NullString{
		sql.NullString{
			String: "",
			Valid:  false,
		},
	}

	if sttData.SttRecipientAddressType != "" {
		sttRecipientAddressType = dbr.NullString{
			sql.NullString{
				String: sttData.SttRecipientAddressType,
				Valid:  true,
			},
		}
	}

	sttCreate := model.SttCreate{
		Stt: c.generateSttForCreateSttV2(sttData, checkTariff, params, referenceRole),
		SttPieceHistory: model.SttPieceHistory{
			HistoryStatus:      model.BKD,
			HistoryLocation:    sttData.SttOriginCityID,
			HistoryActorName:   params.AccountRefName,
			HistoryActorRole:   referenceRole,
			HistoryActorID:     params.AccountRefID,
			HistoryCreatedAt:   now,
			HistoryCreatedBy:   int(params.AccountID),
			HistoryCreatedName: params.AccountName,
			HistoryRemark:      tempRemarksPieceHistory.ToString(),
		},
	}
	sttCreate.Stt.SttRecipientAddressType = sttRecipientAddressType
	sttCreate.Stt.SttBookedAt = now
	sttCreate.Stt.SttCreatedAt = now
	sttCreate.Stt.SttUpdatedAt = now
	sttCreate.Stt.SttMeta = sttMeta.ToString()
	sttCreate.Stt.SttBookedByCode = referenceCode
	sttCreate.Stt.SttPaymentStatus = paymentStatus

	// Source Algo
	if err := c.generateSttShipmentID(ctx, &stt.RequestGenerateStt{
		SttCreate:           &sttCreate,
		SttData:             &sttData,
		SttRequest:          params,
		ReferenceName:       params.AccountRefName,
		Index:               0,
		BookedForActor:      &model.Actor{},
		DestinationDistrict: &model.District{Data: params.DistrictDestination},
	}); err != nil {
		return nil, err
	}

	for _, pieces := range sttData.SttPieces {
		sttCreate.SttPieces = append(sttCreate.SttPieces, model.SttPiece{
			SttPieceLength:       pieces.SttPieceLength,
			SttPieceWidth:        pieces.SttPieceWidth,
			SttPieceHeight:       pieces.SttPieceHeight,
			SttPieceGrossWeight:  pieces.SttPieceGrossWeight,
			SttPieceVolumeWeight: pieces.SttPieceVolumeWeight,
		})
	}

	sttCreate.SttOptionalRate = append(sttCreate.SttOptionalRate, model.SttOptionalRate{
		SttOptionalRateName:   checkTariff.Data.InsuranceName,
		SttOptionalRateRate:   checkTariff.Data.InsuranceRates,
		SttOptionalRateParams: model.INSURANCE,
	})

	if sttData.SttIsWoodpacking {
		sttCreate.SttOptionalRate = append(sttCreate.SttOptionalRate, model.SttOptionalRate{
			SttOptionalRateName:   strings.Title(model.WOODPACKING),
			SttOptionalRateRate:   checkTariff.Data.WoodpackingRates,
			SttOptionalRateParams: model.WOODPACKING,
		})
	}

	sttDue, err := c.GenerateSttDue(selfCtx, &sttCreate.Stt, params.Token)
	if err != nil {
		return nil, err
	}

	sttCustomFlag := c.checkNewDfodRulesAndUpdateMetaSttCustomFlag(selfCtx, CheckDfodRulesParam{
		sttCreate:  &sttCreate.Stt,
		sttMeta:    &sttMeta,
		cityOrigin: originCity,
		token:      params.Token,
	})

	paramsCreateStt = append(paramsCreateStt, model.SttCreate{
		IsSttManual:           sttCreate.IsSttManual,
		ShipmentPackageID:     sttCreate.ShipmentPackageID,
		Stt:                   sttCreate.Stt,
		SttPieces:             sttCreate.SttPieces,
		SttOptionalRate:       sttCreate.SttOptionalRate,
		SttPieceHistory:       sttCreate.SttPieceHistory,
		CheckTariff:           &checkTariff.Data,
		OriginDistrict:        &model.District{Data: params.DistrictOrigin},
		DestinationDistrict:   &model.District{Data: params.DistrictDestination},
		BookedForExternalCode: bookedForExternalCode,
		BookedByExternalCode:  actorExternalCode,
		Commodity:             &model.Commodity{Data: params.Commodity},
		ElexysTariff:          sttCreate.ElexysTariff,
		SttManual:             sttCreate.SttManual,
		SttDue:                sttDue,
		SttCustomFlag:         sttCustomFlag,
	})

	generatedStt, err := c.sttRepo.Create(selfCtx, &model.SttCreateParams{
		SttCreate: paramsCreateStt,
		IsElexys:  c.cfg.IsElexysConfig(),
	})

	if err != nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to create STT Booking, Please resubmit again",
			"id": "Gagal membuat STT Booking, Tolong dikirim ulang",
		})
	}

	var wg sync.WaitGroup
	/* publish message to calculate retail tariff */
	if sttCreate.Stt.SttBookedByType == model.POS && sttCreate.Stt.SttBookedForType == model.CLIENT {
		wg.Add(1)

		payloadCalculateRetailTariff := stt.CalculateRetailTariffRequest{
			OriginID:          reqCheckTariff.RequestCalculateTariff.OriginID,
			DestinationID:     reqCheckTariff.RequestCalculateTariff.DestinationID,
			CommodityID:       reqCheckTariff.RequestCalculateTariff.CommodityID,
			ProductType:       reqCheckTariff.RequestCalculateTariff.ProductType,
			AccountType:       paramsCreateStt[0].Stt.SttBookedByType,
			AccountRefID:      paramsCreateStt[0].Stt.SttBookedBy,
			GoodsPrice:        reqCheckTariff.RequestCalculateTariff.GoodsPrice,
			CodAmount:         reqCheckTariff.RequestCalculateTariff.CodAmount,
			IsCod:             false,
			InsuranceType:     reqCheckTariff.RequestCalculateTariff.InsuranceType,
			IsWoodpacking:     reqCheckTariff.RequestCalculateTariff.IsWoodpacking,
			IsHaveTaxID:       reqCheckTariff.RequestCalculateTariff.IsHaveTaxID,
			Pieces:            reqCheckTariff.RequestCalculateTariff.Pieces,
			IsBookingPurposes: reqCheckTariff.RequestCalculateTariff.IsBookingPurposes,
			HistoryStatus:     []string{model.BKD},
			SttNo:             paramsCreateStt[0].Stt.SttNo,
			IsDisablePromo:    isDisablePromo,
		}

		go func() {
			defer wg.Done()
			c.CalculateRetailTariff(context.Background(), &payloadCalculateRetailTariff)
		}()
	}

	go func() {
		// count goroutine
		uuid := uuid.New().String()
		shared.IncreaseTotalGoRoutineCount(uuid)
		defer shared.DecreaseTotalGoRoutineCount(uuid)
		shipmentID := sttCreate.Stt.SttShipmentID
		isHoldCommision := shipmentID != "" && params.Source == model.ALGO && model.IsShipmentHoldCommission[shared.GetPrefixShipmentID(shipmentID)]
		c.gatewaySttUc.BookingCommission(context.Background(), &gateway_stt.BookingCommissionRequest{
			SttCreate: func(params model.SttCreate) *model.SttCreate {
				createStt := params
				shipmentID := createStt.Stt.SttShipmentID
				if createStt.Stt.SttIsCOD && createStt.Stt.SttCODFee > 0 && (shared.GetPrefixShipmentID(shipmentID) == model.C2 || shared.GetPrefixShipmentID(shipmentID) == model.C1 || model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(shipmentID)]) {
					createStt.CheckTariff.TotalTariff = createStt.CheckTariff.TotalTariff - createStt.Stt.SttCODFee
				}
				createStt.CheckTariff.TotalTariff = createStt.CheckTariff.TotalTariff - checkTariff.Data.CODBookingDiscount
				return &createStt
			}(paramsCreateStt[0]),
			SttCreateRequest: &sttData,
			Token:            params.Token,
			AccountRefType:   referenceRole,
			AccountRefID:     params.AccountRefID,
			PartnerPosParentID: func(id int) int {
				if params.Stt[0].SttShipmentID != `` && (model.MappingShipmentPrefixPosBranchBookingCommission[shared.GetPrefixShipmentID(params.Stt[0].SttShipmentID)] || (codHandling == model.SPECIALCOD && (shared.GetPrefixShipmentID(sttData.SttShipmentID) == model.C1 || shared.GetPrefixShipmentID(sttData.SttShipmentID) == model.C2))) {
					return id
				}
				return 0
			}(BookedForActor.PosParentID()),
			PartnerPosBranchCommission: func(commmission float64) float64 {
				if params.Stt[0].SttShipmentID != `` && (model.MappingShipmentPrefixPosBranchBookingCommission[shared.GetPrefixShipmentID(params.Stt[0].SttShipmentID)] || (codHandling == model.SPECIALCOD && (shared.GetPrefixShipmentID(sttData.SttShipmentID) == model.C1 || shared.GetPrefixShipmentID(sttData.SttShipmentID) == model.C2))) {
					return commmission
				}
				return 0
			}(BookedForActor.PosBranchCommission()),
			ClientPaymentMethod:       clientPaymentMethod,
			ClientCodConfigAmount:     clientCodConfigAmount,
			ClientCodShipmentDiscount: clientCodShipmentDiscount,
			BookingReturn:             totalTariffReturn / 2,
			CodHandling:               codHandling, IsHoldCommision: isHoldCommision, Source: params.Source,
		})
	}()

	go c.gatewaySttUc.SttSubmit(context.Background(), &gateway_stt.SttSubmitRequest{SttCreate: &paramsCreateStt[0], PercentageCodFee: percentageCodFee, MinCodFee: minimumCodFee, CODHandling: codHandling, Token: params.Token})

	// messaging service
	go func(sttID int64) { // sttID arguments here
		stt := &paramsCreateStt[0].Stt
		if stt != nil {
			c.messageGatewayUc.SendMessage(context.Background(), &model.SendMessageRequest{
				RecieverNumber: model.RecieverNumber{PackageSender: stt.SttSenderPhone, PackageReceiver: stt.SttRecipientPhone},
				PackageType:    shared.CheckPackageType(*stt),
				EventStatus:    model.BKD,
				Token:          params.Token,
			}, stt)
		}
	}(generatedStt[0].SttID) // Why do we need to pass this as an arguments?

	wg.Wait()

	res.Stt = generatedStt
	if paramsCreateStt[0].CheckTariff.IsPromo {
		res.Stt[0].IsDiscount = paramsCreateStt[0].CheckTariff.IsPromo
		res.Stt[0].TotalDiscount = paramsCreateStt[0].CheckTariff.TotalDiscount
		res.Stt[0].TotalBeforeDiscount = paramsCreateStt[0].CheckTariff.TotalTariff
		res.Stt[0].TotalAfterDiscount = paramsCreateStt[0].CheckTariff.TotalTariffAfterDiscount
	}
	c.createSttSetResponse(selfCtx, &res, params.Token, sttCreate.Stt)
	return &res, nil
}

func (c *sttCtx) getPartrnerCreateSTTV2(ctx context.Context, params *stt.CreateSttRequest, sttData stt.Stt) (*model.Partner, error) {
	partnerBooking, err := c.partnerRepo.GetByID(ctx, params.AccountRefID, params.Token)
	if err != nil || partnerBooking == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner Not Found",
			"id": "Partner Tidak Ditemukan",
		})
	}
	if sttData.SttProductType == model.VIPPACK && !partnerBooking.Data.PartnerIsAllowBookVIPPACK {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Product type is invalid",
			"id": "Tipe produk tidak valid",
		})
	}
	return partnerBooking, nil
}

func (c *sttCtx) validateSttClientCreateSTTV2(params *stt.CreateSttRequest, sttData stt.Stt, errInvalid error) error {
	if sttData.SttProductType == model.VIPPACK {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Product type is invalid",
			"id": "Tipe produk tidak valid",
		})
	}
	if params.Source == model.ALGO {
		return errInvalid
	}

	if model.IsShipmentTokopedia[shared.GetPrefixShipmentID(sttData.SttShipmentID)] || shared.IsLiloPrefix(sttData.SttNoRefExternal) {
		return nil
	}

	if sttData.SttIsCOD && !sttData.SttIsDFOD {
		if err := c.validateCodAmount(sttData); err != nil {
			return err
		}
	}

	return nil
}

func (c *sttCtx) validateCodAmount(sttData stt.Stt) error {
	if sttData.SttCODAmount < 1 {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt COD Amount cannot be empty if Is COD",
			"id": "Stt COD Amount tidak boleh kosong jika Is COD",
		})
	}

	isNotValidCODAmount := sttData.SttCODFee < 0 || sttData.SttGoodsEstimatePrice != sttData.SttCODAmount
	if isNotValidCODAmount {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt COD Fee cannot be less than 0 or Stt COD Amount is not the same as Stt Goods Price",
			"id": "Stt COD Fee tidak boleh kurang dari 0 atau Stt COD Amount tidak sama dengan Stt Goods Price",
		})
	}
	return nil
}

func (c *sttCtx) validatePartnerDestination(ctx context.Context, params *stt.CreateSttRequest) error {

	districtDestination, err := c.districtRepo.GetByCode(ctx, &model.CredentialRestAPI{
		Token: params.Token,
	}, params.Stt[0].SttDestinationDistrictID)
	if err != nil || districtDestination == nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "District destination type doesn't support delivery item that have more than 1 koli wihtin 1 STT",
			"id": "Tipe district destinasi tidak mendukung pengiriman barang yang memiliki lebih dari 1 koli dalam satu STT",
		})
	}

	sttVendor := (districtDestination.Data.Type == model.TYPE_VENDOR || districtDestination.Data.Type == model.TYPE_VENDOR_LANJUTAN)
	sttVendorNinjaOrPI := districtDestination.Data.VendorCode == model.TypeVendorNINJA || districtDestination.Data.VendorCode == model.TypeVendorPTPOS
	sttVendorNotAllowPiecesMoreThan1 := sttVendor && sttVendorNinjaOrPI && len(params.Stt[0].SttPieces) > 1
	if sttVendorNotAllowPiecesMoreThan1 {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "District destination type doesn't support delivery item that have more than 1 koli wihtin 1 STT",
			"id": "Tipe district destinasi tidak mendukung pengiriman barang yang memiliki lebih dari 1 koli dalam satu STT",
		})
	}

	return nil
}

type CheckDfodRulesParam struct {
	sttCreate  *model.Stt
	sttMeta    *model.SttMeta
	cityOrigin *model.City
	token      string
}

func (c *sttCtx) isCodConfigEmpty(codConfig *model.CodConfigs) bool {
	return codConfig == nil || len(codConfig.Data) == 0
}
