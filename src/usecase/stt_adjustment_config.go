package usecase

import (
	"context"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *sttCtx) SttAdjustmentConfig(ctx context.Context, params *stt.SttAdjustmentConfigRequest) (*stt.SttAdjustmentConfigResponse, error) {
	var (
		opName  = "UsecaseStt-SttAdjustmentConfig"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		res     = new(stt.SttAdjustmentConfigResponse)
		err     error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": err})
	}()
	if err = params.Validate(); err != nil {
		return nil, err
	}

	data, err := c.prepareAdjustmentData(selfCtx, params)
	if err != nil {
		return nil, err
	}

	switch params.AccountRefType {
	case model.POS:
		res = c.configSttPOS(selfCtx, data)
	case model.CONSOLE:
		res = c.configSttConsole(selfCtx, data)
	case model.INTERNAL:
		res = c.configSttInternal(selfCtx, data)
	case model.CLIENT:
		res = c.configSttClient(selfCtx, data)
	}

	return res, nil
}

type checkSttPrefix struct {
	req                 *stt.SttAdjustmentConfigRequest
	isSttRetail         bool
	isSttClient         bool
	isSttCA             bool
	isSttTokopedia      bool
	isSttBukalapak      bool
	isSttReverseJourney bool
	dataStt             *model.Stt
	sttHistories        []model.SttPieceHistoryCustom
	cityOrigin          *model.City
	token               string
}

func (c *sttCtx) prepareAdjustmentData(ctx context.Context, params *stt.SttAdjustmentConfigRequest) (checkSttPrefix, error) {
	data, err := c.getDataSttAndCheckPrefix(ctx, params.SttNo)
	if err != nil {
		return checkSttPrefix{}, err
	}

	histories, err := c.sttPieceHistoryRepo.SelectBySttNo(ctx, &model.SttPieceHistoryViewParam{
		SttNoIn:   []string{params.SttNo},
		SortBy:    `sph.history_created_at`,
		OrderDesc: true,
	})
	if err != nil || len(histories) == 0 {
		return checkSttPrefix{}, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt History is not found",
			"id": "Stt History tidak ditemukan",
		})
	}

	cityOrigin, err := c.cityRepo.Get(ctx, data.dataStt.SttOriginCityID, params.Token)
	if err != nil || cityOrigin == nil {
		return checkSttPrefix{}, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "City origin is not found",
			"id": "Kota asal tidak ditemukan",
		})
	}

	data.req = params
	data.sttHistories = histories
	data.cityOrigin = cityOrigin
	data.token = params.Token

	return data, nil
}

func (c *sttCtx) getDataSttAndCheckPrefix(ctx context.Context, sttNo string) (checkSttPrefix, error) {
	var res checkSttPrefix

	sttData, err := c.sttRepo.Get(ctx, &model.SttViewDetailParams{
		Stt: model.Stt{SttNo: sttNo},
	})
	if err != nil {
		return res, shared.ERR_UNEXPECTED_DB
	}
	if sttData == nil {
		return res, shared.ERR_STT_NOT_FOUND
	}

	prefixStt := shared.GetPrefixSttNo(sttData.SttNo)

	prefixShipment := ""
	if sttData.SttShipmentID != "" {
		prefixShipment = shared.GetPrefixShipmentID(sttData.SttShipmentID)
	}

	res.dataStt = sttData

	c.checkPrefixFlags(&res, prefixStt, prefixShipment)

	return res, nil
}

func (c *sttCtx) checkPrefixFlags(res *checkSttPrefix, prefixStt, prefixShipment string) {
	switch {
	case model.IsPrefixSttRetail[prefixStt]:
		res.isSttRetail = true
	case model.IsPrefixSttClient[prefixStt]:
		res.isSttClient = true
	case model.PrefixAutoCA == prefixStt:
		switch {
		case model.IsShipmentPrefixEnableCOD[prefixShipment]:
			res.isSttCA = true
		case model.T1 == prefixShipment:
			res.isSttTokopedia = true
		case model.IsShipmentBukalapak[prefixShipment]:
			res.isSttBukalapak = true
		}
	case model.IsPrefixValidReverseJourney[prefixStt]:
		res.isSttReverseJourney = true
	}
}

func (c *sttCtx) getFtzRule(isFtz string) string {
	if isFtz == "yes" {
		return stt.NonEditableConfig
	}
	return stt.NoShowConfig
}

func (c *sttCtx) populateRespInterpackAndFtzPos(res *stt.SttAdjustmentConfigResponse, ftzCity string) {
	ruleFtz := c.getFtzRule(ftzCity)
	res.Config.FtzCIPL.IsRequired = true
	res.Config.FtzIdentityNo.IsRequired = true
	res.Config.FtzCIPL.Rule = ruleFtz
	res.Config.FtzIdentityNo.Rule = ruleFtz
	res.Config.FtzTaxNo.Rule = ruleFtz
	res.Config.FtzEmail.Rule = ruleFtz
	res.Config.FtzShipmentStatus.Rule = ruleFtz
	res.Config.FtzImageTaxNo.Rule = ruleFtz
	res.Config.FtzImageIdentityNo.Rule = ruleFtz
	res.Config.FtzImageBeforePacking.Rule = ruleFtz
	res.Config.FtzImageAfterPacking.Rule = ruleFtz
}

type sttFieldRules struct {
	ClientRule          string
	TariffRule          string
	EditableRule        string
	WeightRule          string
	CodDfodRule         string
	AdjustPiece         string
	SenderRule          string
	ReceiverRule        string
	ReceiverAddressRule string
	CommodityRule       string
}

func (c *sttCtx) isSttValidType(data checkSttPrefix) bool {
	return data.isSttRetail || data.isSttClient || data.isSttCA || data.isSttTokopedia || data.isSttBukalapak
}

func (c *sttCtx) isDataInterpackEmpty(sttMeta *model.SttMeta) bool {
	return sttMeta != nil && (len(sttMeta.SttAttachFiles) == 0 || sttMeta.SttRecipientEmail == "" || sttMeta.SttKtpImage == "" || sttMeta.SttTaxImage == "" ||
		sttMeta.SttInterTaxNumber == "" || sttMeta.SttIdentityNumber == "" || len(sttMeta.SttCIPL) == 0)
}

func (c *sttCtx) checkInterpackDocumentInternational(ctx context.Context, data checkSttPrefix, res *stt.SttAdjustmentConfigResponse) {
	sttMeta := data.dataStt.SttMetaToStruct()
	dataIntDocConfig := new(model.InternationalDocumentData)
	isInterpack := false
	if data.dataStt.SttProductType == model.INTERPACK {
		isInterpack = true
		dataIntDocConfig = c.getDetailInternationalDocument(ctx, data, c.isDataInterpackEmpty(sttMeta))
	}
	c.populateResponse(res, sttMeta, dataIntDocConfig, isInterpack)
}

func (c *sttCtx) populateResponse(res *stt.SttAdjustmentConfigResponse, sttMeta *model.SttMeta, dataIntDocConfig *model.InternationalDocumentData, isInterpack bool) {
	res.Config.InterCIPL.Rule = c.checkIsInterpackEditable(isInterpack, sttMeta.SttCIPL, getSafeBool(dataIntDocConfig, func(d *model.InternationalDocumentData) bool { return d.IdcIsOtherCommodity }))
	res.Config.InterIdentityNo.Rule = c.checkIsInterpackEditable(isInterpack, sttMeta.SttIdentityNumber, getSafeBool(dataIntDocConfig, func(d *model.InternationalDocumentData) bool { return d.IdcIsIdentityNumber }))
	res.Config.InterTaxNo.Rule = c.checkIsInterpackEditable(isInterpack, sttMeta.SttInterTaxNumber, getSafeBool(dataIntDocConfig, func(d *model.InternationalDocumentData) bool { return d.IdcIsTaxIdentificationNumber }))
	res.Config.InterEmail.Rule = c.checkIsInterpackEditable(isInterpack, sttMeta.SttRecipientEmail, getSafeBool(dataIntDocConfig, func(d *model.InternationalDocumentData) bool { return d.IdcIsReceiverEmail }))
	res.Config.InterImageTaxNo.Rule = c.checkIsInterpackEditable(isInterpack, sttMeta.SttTaxImage, getSafeBool(dataIntDocConfig, func(d *model.InternationalDocumentData) bool { return d.IdcIsNpwpImage }))
	res.Config.InterImageIdentityNo.Rule = c.checkIsInterpackEditable(isInterpack, sttMeta.SttKtpImage, getSafeBool(dataIntDocConfig, func(d *model.InternationalDocumentData) bool { return d.IdcIsKtpImage }))
	res.Config.InterImageBeforePacking.Rule = c.checkIsInterpackEditable(isInterpack, sttMeta.SttAttachFiles, getSafeBool(dataIntDocConfig, func(d *model.InternationalDocumentData) bool { return d.IdcIsBeforeAndAfterPackingImage }))
	res.Config.InterImageAfterPacking.Rule = c.checkIsInterpackEditable(isInterpack, sttMeta.SttAttachFiles, getSafeBool(dataIntDocConfig, func(d *model.InternationalDocumentData) bool { return d.IdcIsBeforeAndAfterPackingImage }))
}

func (c *sttCtx) getDetailInternationalDocument(ctx context.Context, data checkSttPrefix, isGetDetail bool) *model.InternationalDocumentData {
	if isGetDetail {
		tempCityOrigin, err := c.cityRepo.Get(ctx, data.dataStt.SttOriginCityID, data.token)
		if err != nil {
			logger.Ef(`SttAdjustmentConfigConsole-GetDetailInternationalDocument Error %v`, err)
			return nil
		}
		tempCityDestination, err := c.cityRepo.Get(ctx, data.dataStt.SttDestinationCityID, data.token)
		if err != nil {
			logger.Ef(`SttAdjustmentConfigConsole-GetDetailInternationalDocument Error %v`, err)
			return nil
		}

		res, err := c.internationalDocumentRepo.GetDetailInternationalDocument(ctx, model.GetInternationalDocumentParams{
			Token:             data.token,
			OriginCityID:      tempCityOrigin.ID,
			DestinationCityID: tempCityDestination.ID,
		})
		isResEmpty := res == nil || (res.IdcID == 0 && res.IdcCountryID == 0)
		if err != nil || isResEmpty {
			logger.Ef(`SttAdjustmentConfigConsole-GetDetailInternationalDocument Error %v`, err)
			return nil
		}
		return res
	}
	return nil
}

func (c *sttCtx) checkIsInterpackEditable(isInterpack bool, data any, isRequired bool) string {
	if !isInterpack {
		return stt.NoShowConfig
	}

	// safe check
	isEmpty := func() bool {
		switch v := data.(type) {
		case string:
			return v == ""
		case []string:
			return len(v) == 0
		case []model.SttCIPL:
			return len(v) == 0
		default:
			return true // fallback: treat unknown type as empty
		}
	}()

	isEditable := isEmpty && isRequired
	if isEditable {
		return stt.EditableConfig
	}
	return stt.NonEditableConfig
}

func getSafeBool(data *model.InternationalDocumentData, getter func(*model.InternationalDocumentData) bool) bool {
	if data == nil {
		return false
	}
	return getter(data)
}
