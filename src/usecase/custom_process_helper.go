package usecase

import (
	"context"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
)

func (c *customProcessCtx) releaseBookingCommission(ctx context.Context, customProcessStatus string, sttNoIsSuccess map[string]model.Stt) {
	var err error
	opName := "customProcessCtx-releaseBookingCommission"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	sttNo := []string{}

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": sttNo, "custom_process": customProcessStatus, "error": err})
	}()

	if !model.IsCustomProcessStatusReleaseBookingCommission[customProcessStatus] {
		return
	}

	for _, stt := range sttNoIsSuccess {
		if stt.SttID == 0 {
			continue
		}

		sttReqBookComm := c.getOldestStt(selfCtx, stt)

		if shared.CheckPrefixSttNoForReleaseBookingCommission(sttReqBookComm) {
			err = c.gatewaySttUc.ReleaseBookingCommission(selfCtx, sttReqBookComm)
		}
	}
}

func (c *customProcessCtx) getOldestStt(ctx context.Context, stt model.Stt) model.Stt {
	var err error
	opName := "customProcessCtx-getOldestStt"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": stt.SttNo, "error": err})
	}()

	sttMeta := stt.SttMetaToStruct()
	isSttHasRootReverseSttNumber := sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseSttNo != ``

	if !isSttHasRootReverseSttNumber {
		return stt
	}

	sttData, err := c.sttRepo.Get(selfCtx, &model.SttViewDetailParams{
		Stt: model.Stt{
			SttNo: sttMeta.DetailSttReverseJourney.RootReverseSttNo,
		},
	})

	if sttData != nil && sttData.SttID > 0 {
		return *sttData
	}

	return stt
}

type ValidateToTheSameStatusParams struct {
	Status             string
	HubID              int
	HubDestinationID   int
	HubDestinationCity string
	SttHistory         *model.SttPieceHistoryCustom
	PartnerId          int
}

func (c *customProcessCtx) ValidateToTheSameStatus(ctx context.Context, params *ValidateToTheSameStatusParams) (isAllow bool) {
	sttLastStatus := params.SttHistory.HistoryStatus

	toTheSameStatus := sttLastStatus == params.Status
	if !toTheSameStatus {
		isAllow = true
		return
	}

	funcValidationToStatus := map[string]func(ctx context.Context, params *ValidateToTheSameStatusParams) bool{
		model.INHUB:  c.ValidateToTheSameStatusINHUB,
		model.OUTHUB: c.ValidateToTheSameStatusOUTHUB,
	}

	validate, ok := funcValidationToStatus[params.Status]
	if !ok {
		isAllow = false
		return
	}

	return validate(ctx, params)
}

func (c *customProcessCtx) ValidateToTheSameStatusINHUB(ctx context.Context, params *ValidateToTheSameStatusParams) bool {
	// the same consol it's mean the same 3LC
	isTheSameConsol := params.PartnerId == params.SttHistory.HistoryActorID && params.SttHistory.HistoryActorRole == model.CONSOLE
	sttHistoryRemark := params.SttHistory.RemarkPieceHistoryToStruct()

	return isTheSameConsol && sttHistoryRemark.HubID != params.HubID
}

func (c *customProcessCtx) ValidateToTheSameStatusOUTHUB(ctx context.Context, params *ValidateToTheSameStatusParams) bool {
	sttHistoryRemark := params.SttHistory.RemarkPieceHistoryToStruct()
	isTheSameConsol := sttHistoryRemark.HubDestinationCity == params.HubDestinationCity

	return isTheSameConsol && sttHistoryRemark.HubDestinationID != params.HubDestinationID
}
