package usecase

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/predefined_holiday"
	"github.com/Lionparcel/hydra/src/usecase/sti_sc"
	"github.com/Lionparcel/hydra/src/usecase/stt_activity"
)

func (c *stiSc) ScanUpdateV2(ctx context.Context, params *sti_sc.ScanUpdateV2Request) (*sti_sc.ScanUpdateV2Response, error) {
	var (
		opName  = "stiScV2-ScanUpdateV2"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		res     = new(sti_sc.ScanUpdateV2Response)
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": err})
	}()

	if err = params.Validate(); err != nil {
		return nil, err
	}

	now, _ := shared.ParseUTC7(shared.FormatDateTime, c.timeRepo.Now(time.Now()).Format(shared.FormatDateTime))
	s := scanUpdateStiScV2{
		service: c,
		params:  params,
		now:     now,
	}

	funcs := []func(ctx context.Context) error{
		s.getAndValidateStt,
		s.getStiScPartner,
		s.checkPriorityDelivery,
		s.processSttDetails,
		s.updateStiSc,
	}

	for i := range funcs {
		err = funcs[i](selfCtx)
		if err != nil {
			return nil, err
		}
	}

	res = s.generateResponse()

	s.backgroundProcess()

	return res, nil
}

type scanUpdateStiScV2 struct {
	service *stiSc
	params  *sti_sc.ScanUpdateV2Request
	now     time.Time

	// process
	sttDetails        []model.SttDetailResult
	stiScPartner      *model.Partner
	sttPieceIDs       []int64
	histories         []model.SttPieceHistory
	listSttUpdateTime []stt_activity.SttActivityRequestDetail

	// response
	refNo        string
	isPaid       bool
	podDate      time.Time
	oldStatus    string
	bookedID     int
	bookedType   string
	bookedName   string
	statusReturn string
}

// main process
func (s *scanUpdateStiScV2) getAndValidateStt(ctx context.Context) error {
	var (
		opName  = "scanUpdateStiScV2-getAndValidateStt"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": err})
	}()

	sttDetails, err := s.service.sttRepo.SelectDetail(selfCtx, 0, s.params.SttNo, false)
	if err != nil {
		return shared.ERR_UNEXPECTED_DB
	}

	if len(sttDetails) == 0 {
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT data not found",
			"id": "STT data tidak ditemukan",
		})
		return err
	}

	err = s.validateSTTSTISCStatus(selfCtx, sttDetails)
	if err != nil {
		return err
	}

	s.sttDetails = sttDetails
	s.oldStatus = sttDetails[0].SttLastStatusID
	s.podDate = s.generatePodDate(selfCtx)
	s.refNo = s.generateRefNo()
	s.isPaid = s.isPaymentStatusPaid()

	s.bookedID = s.sttDetails[0].Stt.SttBookedBy
	s.bookedType = s.sttDetails[0].Stt.SttBookedByType
	s.bookedName = s.sttDetails[0].Stt.SttBookedName
	if s.sttDetails[0].SttBookedByType == model.INTERNAL {
		s.bookedID = s.sttDetails[0].Stt.SttBookedForID
		s.bookedType = s.sttDetails[0].Stt.SttBookedForType
		s.bookedName = s.sttDetails[0].Stt.SttBookedForName
	}

	return nil
}

func (s *scanUpdateStiScV2) validateSTTSTISCStatus(ctx context.Context, sttDetails []model.SttDetailResult) error {
	if sttDetails[0].SttLastStatusID == model.STTADJUSTED {
		// get stt piece history
		sttPieceIDs := make([]int64, 0)
		for _, sd := range sttDetails {
			sttPieceIDs = append(sttPieceIDs, sd.SttPiece.SttPieceID)
		}

		sttPieceHistories, er := s.service.sttPieceHistoryRepo.SelectWithSpecificStatusAndPieceID(ctx, &model.SttPieceHistoryViewParam{
			SttPieceHistorySttPieceIDWhereIn: sttPieceIDs,
			OrderDesc:                        true,
		})

		if er != nil {
			return er
		}

		if len(sttPieceHistories) < 1 {
			err := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "STT data not found",
				"id": "STT data tidak ditemukan",
			})
			return err
		}

		// get status last one before on stt piece history
		if !model.IsAllowedStatusBeforeSTISC[sttPieceHistories[1].HistoryStatus] {
			err := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": fmt.Sprintf("STT %s can't process", s.params.SttNo),
				"id": fmt.Sprintf("STT %s tidak bisa di proses", s.params.SttNo),
			})
			return err
		}
	} else {
		if !model.IsAllowedStatusSTISC[sttDetails[0].SttLastStatusID] {
			err := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": fmt.Sprintf("STT %s can't process with last status %s", s.params.SttNo, sttDetails[0].SttLastStatusID),
				"id": fmt.Sprintf("STT %s tidak bisa di proses dengan last status %s", s.params.SttNo, sttDetails[0].SttLastStatusID),
			})
			return err
		}
	}

	return nil
}

// main process
func (s *scanUpdateStiScV2) getStiScPartner(ctx context.Context) error {
	var (
		opName  = "scanUpdateStiScV2-getStiScPartner"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": err})
	}()

	stiScPartner, err := s.service.partnerRepo.GetByID(selfCtx, s.params.PartnerID, s.params.Token)
	if err != nil || stiScPartner == nil {
		return shared.ERR_UNEXPECTED_DB
	}

	s.stiScPartner = stiScPartner

	return nil
}

// main process
func (s *scanUpdateStiScV2) processSttDetails(ctx context.Context) error {
	var (
		opName  = "scanUpdateStiScV2-processSttDetails"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": err})
	}()

	for _, sttDetail := range s.sttDetails {
		s.sttPieceIDs = append(s.sttPieceIDs, sttDetail.SttPiece.SttPieceID)

		err = s.validateSttDetail(selfCtx, &sttDetail)
		if err != nil {
			return err
		}

		err = s.generateSttDetailHistory(selfCtx, &sttDetail)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *scanUpdateStiScV2) validateSttDetail(ctx context.Context, sttDetail *model.SttDetailResult) error {
	var (
		opName  = "scanUpdateStiScV2-validateSttDetails"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": err})
	}()

	funcs := []func(ctx context.Context, sttDetail *model.SttDetailResult) error{
		s.validationBaggingGroupStiSc,
	}

	for i := range funcs {
		err = funcs[i](selfCtx, sttDetail)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *scanUpdateStiScV2) validationBaggingGroupStiSc(ctx context.Context, sttDetail *model.SttDetailResult) error {
	var (
		opName  = "scanUpdateStiScV2-validationBaggingGroupStiSc"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"params": sttDetail, "error": err})
	}()

	if !s.service.cfg.IsUseValidationBaggingGroupStiSc() {
		return nil
	}

	err = s.service.getBaggingGroup(selfCtx, *sttDetail, s.stiScPartner, s.params.Token)
	if err != nil {
		return err
	}

	return nil
}

func (s *scanUpdateStiScV2) validationLastStatusSttAdjusted(ctx context.Context, sttDetail *model.SttDetailResult) error {
	var (
		opName  = "scanUpdateStiScV2-validationLastStatusSttAdjusted"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"params": sttDetail, "error": err})
	}()

	if sttDetail.SttLastStatusID != model.STTADJUSTED {
		return nil
	}

	sttHistory, err := s.service.sttPieceHistoryRepo.Select(ctx, &model.SttPieceHistoryViewParam{
		SttPieceHistorySttPieceID: sttDetail.SttPieceID,
	})
	if err != nil {
		return shared.ERR_UNEXPECTED_DB
	}

	for _, history := range sttHistory {
		if !model.MapStatusSttAdjustmentSTISC[history.HistoryStatus] {
			err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": fmt.Sprintf("STT %s can't process with status after %s", s.params.SttNo, history.HistoryStatus),
				"id": fmt.Sprintf("STT %s tidak bisa di proses dengan status sesudah %s", s.params.SttNo, history.HistoryStatus),
			})
			return err
		}
	}

	return nil
}

func (s *scanUpdateStiScV2) generateSttDetailHistory(ctx context.Context, sttDetail *model.SttDetailResult) error {
	var (
		opName  = "scanUpdateStiScV2-generateSttDetailHistory"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"params": sttDetail, "error": err})
	}()

	tempRemarksPieceHistory := model.RemarkPieceHistory{
		HistoryLocationName: s.stiScPartner.Data.PartnerLocation.City.Name,
	}
	if s.stiScPartner.Data.PartnerLocation.District != nil {
		tempRemarksPieceHistory.HistoryDistrictName = s.stiScPartner.Data.PartnerLocation.District.Name
	}

	funcs := []func(ctx context.Context, sttDetail *model.SttDetailResult, tempRemarksPieceHistory *model.RemarkPieceHistory) error{
		s.generateSttDetailHistoryLastStatusBKD,
		s.generateSttDetailHistoryLastStatusPUPC,
		s.generateSttDetailHistoryLastStatusDefault,
	}

	for i := range funcs {
		err = funcs[i](selfCtx, sttDetail, &tempRemarksPieceHistory)
		if err != nil {
			return err
		}
	}

	return nil
}

func (s *scanUpdateStiScV2) generateSttDetailHistoryLastStatusBKD(ctx context.Context, sttDetail *model.SttDetailResult, tempRemarksPieceHistory *model.RemarkPieceHistory) error {
	var (
		opName  = "scanUpdateStiScV2-generateSttDetailHistoryLastStatusBKD"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": err})
	}()

	if sttDetail.SttPieceLastStatusID != model.BKD {
		return nil
	}

	// assign value remarks piece history
	tempRemarksPieceHistory.DriverName = model.AccountSystem.ActorName

	if sttDetail.Stt.SttBookedByType == model.POS {
		partnerData, err := s.service.partnerRepo.GetByID(ctx, sttDetail.Stt.SttBookedBy, s.params.Token)

		isPartnerPosBranch := err == nil && partnerData != nil && partnerData.Data.PartnerPosType == model.Branch
		if isPartnerPosBranch {
			// PUPC
			s.histories = append(s.histories, model.SttPieceHistory{
				SttPieceID:         int64(sttDetail.SttPieceID),
				HistoryStatus:      model.PUPC,
				HistoryLocation:    s.stiScPartner.Data.PartnerLocation.CityCode,
				HistoryActorID:     model.AccountSystem.ActorID,
				HistoryActorName:   model.AccountSystem.ActorName,
				HistoryActorRole:   model.POS,
				HistoryCreatedAt:   s.now,
				HistoryCreatedName: model.AccountSystem.ActorName,
				HistoryRemark:      tempRemarksPieceHistory.ToString(),
			})
		}

		// Adding time PUPC status
		s.listSttUpdateTime = append(s.listSttUpdateTime, stt_activity.SttActivityRequestDetail{
			SttNo:         sttDetail.SttNo,
			SttStatus:     model.PUPC,
			SttStatusTime: s.now,
		})
	}

	// PUP
	s.histories = append(s.histories, model.SttPieceHistory{
		SttPieceID:         int64(sttDetail.SttPieceID),
		HistoryStatus:      model.PUP,
		HistoryLocation:    s.stiScPartner.Data.PartnerLocation.CityCode,
		HistoryActorID:     model.AccountSystem.ActorID,
		HistoryActorName:   model.AccountSystem.ActorName,
		HistoryActorRole:   model.POS,
		HistoryCreatedAt:   s.now,
		HistoryCreatedName: model.AccountSystem.ActorName,
		HistoryRemark:      tempRemarksPieceHistory.ToString(),
	})

	tempRemarksPieceHistory.DriverName = ``

	s.listSttUpdateTime = append(s.listSttUpdateTime, stt_activity.SttActivityRequestDetail{
		SttNo:         sttDetail.SttNo,
		SttStatus:     model.PUP,
		SttStatusTime: s.now,
	})

	return nil
}

func (s *scanUpdateStiScV2) generateSttDetailHistoryLastStatusPUPC(ctx context.Context, sttDetail *model.SttDetailResult, tempRemarksPieceHistory *model.RemarkPieceHistory) error {

	if sttDetail.SttPieceLastStatusID != model.PUPC {
		return nil
	}

	// PUP
	s.histories = append(s.histories, model.SttPieceHistory{
		SttPieceID:         int64(sttDetail.SttPieceID),
		HistoryStatus:      model.PUP,
		HistoryLocation:    s.stiScPartner.Data.PartnerLocation.CityCode,
		HistoryActorID:     model.AccountSystem.ActorID,
		HistoryActorName:   model.AccountSystem.ActorName,
		HistoryActorRole:   model.POS,
		HistoryCreatedAt:   s.now,
		HistoryCreatedName: model.AccountSystem.ActorName,
		HistoryRemark:      tempRemarksPieceHistory.ToString(),
	})

	// Adding time PUP status
	s.listSttUpdateTime = append(s.listSttUpdateTime, stt_activity.SttActivityRequestDetail{
		SttNo:         sttDetail.SttNo,
		SttStatus:     model.PUP,
		SttStatusTime: s.now,
	})

	return nil
}

func (s *scanUpdateStiScV2) generateSttDetailHistoryLastStatusDefault(ctx context.Context, sttDetail *model.SttDetailResult, tempRemarksPieceHistory *model.RemarkPieceHistory) error {
	s.histories = append(s.histories, model.SttPieceHistory{
		SttPieceID:         int64(sttDetail.SttPieceID),
		HistoryStatus:      model.STISC,
		HistoryLocation:    s.stiScPartner.Data.PartnerLocation.CityCode,
		HistoryActorID:     s.params.PartnerID,
		HistoryActorName:   s.params.PartnerName,
		HistoryActorRole:   s.params.PartnerType,
		HistoryCreatedAt:   s.now,
		HistoryCreatedBy:   int(s.params.AccountID),
		HistoryCreatedName: s.params.AccountName,
		HistoryRemark:      tempRemarksPieceHistory.ToString(),
	})

	s.listSttUpdateTime = append(s.listSttUpdateTime, stt_activity.SttActivityRequestDetail{
		SttNo:         sttDetail.SttNo,
		SttStatus:     model.STISC,
		SttStatusTime: s.now,
	})

	return nil
}

// main process
func (s *scanUpdateStiScV2) updateStiSc(ctx context.Context) error {
	var (
		opName    = "scanUpdateStiScV2-updateStiSc"
		trace     = tracer.StartTrace(ctx, opName)
		selfCtx   = trace.Context()
		stiScTemp = new(model.StiScTemporary)
		err       error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": err})
	}()

	stiScTemp, err = s.generateStiScTemporary(selfCtx)
	if err != nil {
		return err
	}

	err = s.service.stiDetailRepo.CreateWithTransactionV2(selfCtx, &sti_sc.CreateStiScDetailV2Params{
		SttID:          s.sttDetails[0].SttID,
		History:        s.histories,
		SttPieceIDs:    s.sttPieceIDs,
		StiScTemporary: stiScTemp,
	})
	if err != nil {
		return shared.ERR_UNEXPECTED_DB
	}

	return nil
}

func (s *scanUpdateStiScV2) generateStiScTemporary(ctx context.Context) (*model.StiScTemporary, error) {
	var (
		opName    = "scanUpdateStiScV2-generateStiScTemporary"
		trace     = tracer.StartTrace(ctx, opName)
		selfCtx   = trace.Context()
		stiScTemp = new(model.StiScTemporary)
		err       error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"result": stiScTemp, "error": err})
	}()

	sstMeta := &model.StiScTemporaryMeta{
		StatusReturn: s.statusReturn,
	}

	stiScTemp = &model.StiScTemporary{
		SstAccountID:   s.params.AccountID,
		SstIsActive:    true,
		SstRefNo:       s.refNo,
		SstSttNo:       s.sttDetails[0].Stt.SttNo,
		SstProduct:     s.sttDetails[0].Stt.SttProductType,
		SstOrigin:      s.sttDetails[0].Stt.SttOriginCityID,
		SstDestination: s.sttDetails[0].Stt.SttDestinationCityID,
		SstPodDate:     s.podDate,
		SstGrossWeight: s.sttDetails[0].Stt.SttGrossWeight,
		SstTotalPiece:  s.sttDetails[0].Stt.SttTotalPiece,
		SstIsPaid:      s.isPaid,
		SstBookedType:  s.sttDetails[0].Stt.SttBookedByType,
		SstBookedID:    int64(s.sttDetails[0].Stt.SttBookedBy),
		SstBookedName:  s.sttDetails[0].Stt.SttBookedName,
		SstMeta:        sstMeta.ToString(),
	}

	if strings.EqualFold(s.sttDetails[0].Stt.SttBookedByType, model.INTERNAL) {
		stiScTemp.SstBookedType = s.sttDetails[0].Stt.SttBookedForType
		stiScTemp.SstBookedID = int64(s.sttDetails[0].Stt.SttBookedForID)
		stiScTemp.SstBookedName = s.sttDetails[0].Stt.SttBookedForName
	}

	return stiScTemp, nil
}

func (s *scanUpdateStiScV2) generateRefNo() string {
	refNo := ``
	if s.sttDetails[0].Stt.SttShipmentID != `` {
		refNo = s.sttDetails[0].Stt.SttShipmentID
	} else {
		refNo = s.sttDetails[0].Stt.SttNoRefExternal
	}
	if model.IsNoRefExternal[shared.GetPrefixShipmentID(s.params.SttNo)] {
		refNo = s.params.SttNo
	}
	return refNo
}

func (s *scanUpdateStiScV2) isPaymentStatusPaid() bool {
	stt := s.sttDetails[0].Stt
	if stt.SttPaymentStatus == "" || stt.SttPaymentStatus == model.PAID {
		return true
	}

	isPrefixSttAutoCa := shared.GetPrefixSttNo(stt.SttNo) == model.PrefixAutoCA
	isShipmentCodCaRetail := stt.SttShipmentID != `` && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(stt.SttShipmentID)]

	if isPrefixSttAutoCa && isShipmentCodCaRetail {
		return true
	}

	return false
}

func (s *scanUpdateStiScV2) generatePodDate(ctx context.Context) time.Time {
	var (
		opName  = "scanUpdateStiScV2-generateStiScTemporary"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		podDate = time.Time{}
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"result": podDate, "error": err})
	}()

	sttMeta := s.sttDetails[0].Stt.SttMetaToStruct()
	if sttMeta == nil {
		return podDate
	}

	// Logic SLA
	estimasiSLA := strings.TrimSpace(strings.ToLower(sttMeta.EstimateSLA))
	estimasiSLA = strings.ReplaceAll(estimasiSLA, "hari", "")
	splitEstimasiSLA := strings.Split(estimasiSLA, "-")

	estimateSlaMin := ""
	estimateSlaMax := ""
	if len(splitEstimasiSLA) > 1 {
		estimateSlaMin = strings.TrimSpace(splitEstimasiSLA[0])
		estimateSlaMax = strings.TrimSpace(splitEstimasiSLA[1])
	}

	estimateSlaResp, err := s.service.predefinedHolidayRepo.CheckDate(selfCtx, &model.CredentialRestAPI{
		Token: s.params.Token,
	}, predefined_holiday.RequestSundayAndHolidayCheck{
		StartDate:   s.sttDetails[0].Stt.SttBookedAt.Format(shared.FormatDateTime),
		Min:         estimateSlaMin,
		Max:         estimateSlaMax,
		ProductType: s.sttDetails[0].Stt.SttProductType,
	})
	if err == nil {
		max, _ := time.Parse(shared.NgenFormatDateTimeZone, estimateSlaResp.Max)
		podDate, _ = shared.ParseUTC7(shared.FormatDate, max.Format(shared.FormatDate))
	}

	return podDate
}

// main process
func (s *scanUpdateStiScV2) checkPriorityDelivery(ctx context.Context) error {
	var (
		opName  = "scanUpdateStiScV2-checkPriorityDelivery"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": err})
	}()

	s.statusReturn = "-"

	pd, err := s.service.priorityDeliveryRepo.GetBySttNo(selfCtx, s.params.SttNo)
	if pd == nil {
		return nil
	}

	isPodDateNotLate := !shared.IsLessThanToday(s.podDate) && shared.IsToday(s.podDate)
	statusReturnPODWithoutFlag := pd.PdStatusReturn == `POD` && pd.PdFlag == ``

	withoutStatusReturn := isPodDateNotLate && statusReturnPODWithoutFlag

	if !withoutStatusReturn && pd.PdIsShow == 1 {
		s.statusReturn = "Segera " + pd.PdStatusReturn

		if s.sttDetails[0].SttProductType == model.JUMBOPACKH2H {
			s.statusReturn = pd.PdStatusReturn
		}
	}

	return nil
}

// main process
func (s *scanUpdateStiScV2) generateResponse() *sti_sc.ScanUpdateV2Response {
	return &sti_sc.ScanUpdateV2Response{
		SttNo:               s.sttDetails[0].Stt.SttNo,
		RefNo:               s.refNo,
		ProductType:         s.sttDetails[0].Stt.SttProductType,
		BookedID:            s.bookedID,
		BookedType:          s.bookedType,
		BookedName:          s.bookedName,
		PodDate:             s.podDate,
		TotalGrossWeight:    s.sttDetails[0].Stt.SttGrossWeight,
		TotalVolumeWeight:   s.sttDetails[0].Stt.SttVolumeWeight,
		TotalPieces:         s.sttDetails[0].Stt.SttTotalPiece,
		DestinationCityCode: s.sttDetails[0].Stt.SttDestinationCityID,
		Status:              model.STISC,
		IsPaid:              s.isPaid,
		OldStatus:           s.oldStatus,
		StatusReturn:        s.statusReturn,
	}
}

// main process
func (s *scanUpdateStiScV2) backgroundProcess() {
	go s.updateSttTime()
	go s.statusSubmit()
	go s.processSttDue()
	go s.inactiveRtcByID()
	go s.setStiCounting()
}

func (s *scanUpdateStiScV2) statusSubmit() {
	statusSubmitParams := &model.UpdateSttStatusWithExtendForMiddleware{
		UpdateSttStatus: &model.UpdateSttStatus{
			SttNo:      s.sttDetails[0].GetSttElexysNoOrSttNo(),
			Datetime:   s.now.UTC(),
			StatusCode: model.STISC,
			Location:   s.stiScPartner.Data.PartnerLocation.CityCode,
			Remarks:    fmt.Sprintf(`Paket berada di %s`, s.params.PartnerName),
			City:       s.stiScPartner.Data.PartnerLocation.City.Name,
			UpdatedBy:  s.params.PartnerName,
			UpdatedOn:  s.now.UTC(),
		},
		ServiceType:      model.PACKAGESERVICE,
		Product:          s.sttDetails[0].SttProductType,
		Pieces:           s.sttDetails[0].SttTotalPiece,
		GrossWeight:      s.sttDetails[0].SttGrossWeight,
		VolumeWeight:     s.sttDetails[0].SttVolumeWeight,
		ChargeableWeight: s.sttDetails[0].SttChargeableWeight,
		BookedForType:    s.sttDetails[0].SttBookedForType,
	}

	AppendLastAndSystemStatus(AppendLastAndSystemStatusParams{
		StatusSubmitParams: statusSubmitParams,
		SttPieceHistories:  s.histories,
		PartnerName:        s.params.PartnerName,
	})

	s.service.gatewaySttStatusUc.StatusSubmit(context.Background(), statusSubmitParams)
}

func (s *scanUpdateStiScV2) processSttDue() {
	sttDue, _ := s.service.GenerateSttDueFromSubconsole(context.Background(), &s.sttDetails[0].Stt, s.params.Token)
	sttDue.SdBookedType = model.SUBCONSOLE
	sttDue.SdBookedID = uint64(s.params.PartnerID)
	sttDue.SdBookedName = s.params.PartnerName

	s.service.sttDueRepo.UpdateIsShowBulk(context.Background(), &model.STTDueUpdateIsShow{
		IsShow:       0,
		TargetStatus: model.STI,
		STTNos:       []string{s.sttDetails[0].SttNo},
	})

	s.service.sttDueRepo.InsertBulk(context.Background(), []*model.STTDueModel{sttDue})
}

func (s *scanUpdateStiScV2) inactiveRtcByID() {
	if s.sttDetails[0].SttOriginCityID != s.sttDetails[0].SttDestinationCityID {
		return
	}
	s.service.rtcUc.UpdateInactiveRTCBySttId(context.Background(), int(s.sttDetails[0].SttID))
}

func (s *scanUpdateStiScV2) setStiCounting() {
	countStt := 1 // because scan 1 stt

	var count int
	// {account_id}:{hub_id}:{booked_type}:{booked_id}
	keyCache := model.SetCacheHydraScanSti(s.params.AccountID, 0, s.bookedType, strconv.Itoa(s.bookedID))
	s.service.cacheRepo.GetCache(context.Background(), keyCache, &count)

	if count <= 0 {
		s.service.cacheRepo.CreateCache(context.Background(), keyCache, countStt, model.CacheHydraScanStiExpired)
	}
}

func (s *scanUpdateStiScV2) updateSttTime() {
	s.service.sttActivityUc.UpdateSttTime(context.Background(), &stt_activity.SttActivityRequest{
		ListSttData: s.listSttUpdateTime,
	})
}
