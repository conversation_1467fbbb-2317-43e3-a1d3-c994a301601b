package usecase

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
)

func (s *sttCtx) GenerateSignedURLs(ctx context.Context, sttDetail *model.Stt, attachment string) ([]string, error) {
	if !s.isValidRequest(attachment) {
		return nil, nil
	}

	expiredDurationProof := s.getExpiredDurationProof()

	if s.shouldSkipGeneration(sttDetail) {
		return nil, nil
	}

	return s.generateSignedUrlsByProvider(ctx, attachment, expiredDurationProof)
}

func (s *sttCtx) isValidRequest(attachment string) bool {
	return attachment != "" && s.cfg.IsProofSignedUrl()
}

func (s *sttCtx) getExpiredDurationProof() time.Duration {
	return time.Second * time.Duration(s.cfg.ExpiredDurationProofUrl())
}

func (s *sttCtx) shouldSkipGeneration(stt *model.Stt) bool {
	sttRef := stt.SttNoRefExternal
	var journeyID string

	sttMeta := stt.SttMetaToStruct()

	if isPrefixReverseJourney(stt.SttNo) && sttMeta != nil {
		reverseJourneyDetail := sttMeta.DetailSttReverseJourney

		if len(reverseJourneyDetail.RootReverseShipmentID) > 0 {
			journeyID = reverseJourneyDetail.RootReverseShipmentID
		} else {
			journeyID = reverseJourneyDetail.RootReverseSttNoRefExternal
		}

	} else if len(stt.SttShipmentID) > 0 {
		journeyID = stt.SttShipmentID
	} else {
		journeyID = stt.SttNoRefExternal
	}

	if sttRef == "" {
		return !isPrefixJourneyAllowGenerated(journeyID)
	}

	return !isPrefixJourneyAllowGenerated(journeyID)
}

func (s *sttCtx) generateSignedUrlsByProvider(ctx context.Context, attachment string, expiredDurationProof time.Duration) ([]string, error) {
	splitedAttch := strings.Split(attachment, ",")
	if len(splitedAttch) == 0 {
		return nil, fmt.Errorf("no attachments found")
	}

	var result []string

	for _, attch := range splitedAttch {
		attchSplit := strings.Split(attch, "/")
		if len(attchSplit) < 4 {
			return nil, fmt.Errorf("invalid attachment format: %s", attch)
		}

		split := strings.Join(attchSplit[3:], "/")
		result = append(result, split)
	}

	if len(result) > 0 {
		switch {
		case strings.Contains(splitedAttch[0], "amazonaws"):
			return generateSignedURLs(ctx, result, expiredDurationProof, true)
		case strings.Contains(splitedAttch[0], "storage.googleapis"):
			return generateSignedURLs(ctx, result, expiredDurationProof, false)
		default:
			return nil, fmt.Errorf("unsupported provider in attachment: %s", splitedAttch[0])
		}
	}

	return nil, fmt.Errorf("no valid signed URLs generated")

}

func generateSignedURLs(ctx context.Context, attachments []string, duration time.Duration, isFromAws bool) ([]string, error) {
	var generatedUrls []string

	for _, attachment := range attachments {
		var (
			url string
			err error
		)

		if isFromAws {
			url, err = shared.SignedURLFromAWS(ctx, attachment, duration)
		} else {
			url, err = shared.SignedURL(attachment, duration)
		}

		if err != nil {
			return nil, err
		}

		generatedUrls = append(generatedUrls, url)
	}

	return generatedUrls, nil
}

func isPrefixReverseJourney(stt string) bool {
	prefixesSttJourney := []string{"77", "78", "66", "89", "94"}
	for _, prefix := range prefixesSttJourney {
		if strings.HasPrefix(stt, prefix) {
			return true
		}
	}
	return false
}

func isPrefixJourneyAllowGenerated(stt string) bool {
	prefixesSttJourney := []string{"T1", "TKLP", "TSLP", "TKP01", "TKP"}
	for _, prefix := range prefixesSttJourney {
		if strings.HasPrefix(stt, prefix) {
			return true
		}
	}
	return false
}
