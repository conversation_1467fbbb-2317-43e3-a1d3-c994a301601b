package sti

import (
	"strconv"
	"strings"
	"time"

	"github.com/Lionparcel/hydra/src/usecase/general"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

type (
	// IsAllowUpdateStatusResponse ...
	IsAllowUpdateStatusResponse struct {
		IsAllowUpdateStatus bool   `json:"is_allow_update_status"`
		IsPaid              bool   `json:"is_paid"`
		ErrorMessage        string `json:"error_message"`
	}

	// DetailPieceResponse ...
	DetailPieceResponse struct {
		IsAllowUpdateStatusResponse
		Piece *Piece `json:"piece"`
	}
	// Piece ...
	Piece struct {
		STTNumber       string    `json:"stt_number"`
		STTPieceNumber  string    `json:"stt_piece_number"`
		PieceNumber     int       `json:"piece_number"`
		CityDestination string    `json:"city_destination"`
		Product         string    `json:"product"`
		Woodpacking     string    `json:"woodpacking"`
		Commodity       string    `json:"commodity"`
		CODAmount       string    `json:"cod_amount"`
		GrossWeight     float64   `json:"gross_weight"`
		VolumeWeight    float64   `json:"volume_weight"`
		BookingDate     time.Time `json:"booking_date"`
		LastStatus      string    `json:"last_status"`
		POSName         string    `json:"pos_name"`
		SubConsoleName  string    `json:"sub_console_name"`
	}

	// CheckSttPieceNumberRequest ...
	CheckSttPieceNumberRequest struct {
		SttPieceNumber string `json:"stt_piece" query:"stt_piece" form:"stt_piece"`
		PartnerID      int
		PartnerType    string
		SttNo          string
		PieceNo        int
	}
)

// Validate CheckSttPieceNumberRequest
func (c *CheckSttPieceNumberRequest) Validate() error {
	c.SttPieceNumber = strings.ReplaceAll(strings.TrimSpace(c.SttPieceNumber), " ", "")
	if err := validation.Validate(c.SttPieceNumber, validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "STT Pieces Number is reqiured to be filled",
			"id": "Nomor STT Pieces harus di isi",
		})
	}

	if err := validation.Validate(c.SttPieceNumber, validation.Match(model.STTPieceFormat)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Invalid STT Pieces Number format",
			"id": "Format Nomor STT Pieces tidak benar",
		})
	}

	sttPiece := strings.Split(c.SttPieceNumber, `-`)
	c.SttNo = sttPiece[0]
	pieceNo, _ := strconv.Atoi(sttPiece[1])
	c.PieceNo = pieceNo

	return nil
}

type ViewDetailSttResponse struct {
	IsAllowUpdateStatusResponse
	model.BaggingMappingLocationBase
	Stt general.SttResponse `json:"stt"`
}

type ViewDetailSttStiResponse struct {
	TotalSttSuccess int                `json:"total_stt_success"`
	TotalSttFailed  int                `json:"total_stt_failed"`
	TotalStt        int                `json:"total_stt"`
	TotalKoli       int                `json:"total_koli"`
	StiResult       string             `json:"sti_result"`
	Stts            []ViewDetailSttSti `json:"stts"`
}

type ViewDetailSttSti struct {
	IsAllowUpdateStatusResponse
	Stt *general.SttResponse `json:"stt"`
}

// ViewDetailSttRequest ...
type ViewDetailSttRequest struct {
	SttNo            string `json:"stt_no" query:"stt_no" form:"stt_no"`
	SttOrPickupCode  string `json:"stt_or_pickup_code" query:"stt_or_pickup_code" form:"stt_or_pickup_code"`
	PartnerID        int
	IsPickupManifest bool
	PickupManifestID int
	PartnerType      string
	Token            string
}

// Validate CheckSttPieceNumberRequest
func (c *ViewDetailSttRequest) Validate() error {
	c.SttOrPickupCode = strings.ReplaceAll(strings.TrimSpace(c.SttOrPickupCode), " ", "")
	if err := validation.Validate(c.SttOrPickupCode, validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "STT  Number or Pickup manifest id is required to be filled",
			"id": "Nomor STT atau Pickup manifest id harus di isi",
		})
	}

	if strings.Contains(c.SttOrPickupCode, `PUM`) && c.SttOrPickupCode[:3] == `PUM` {
		c.IsPickupManifest = true
		pickupManifestID, err := strconv.Atoi(c.SttOrPickupCode[3:])
		if err != nil || pickupManifestID < 1 {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid Pickup manifest id",
				"id": "Pickup manifest id tidak valid",
			})
		}

		c.PickupManifestID = pickupManifestID
	} else if strings.Contains(c.SttOrPickupCode, `PUPC`) && c.SttOrPickupCode[:4] == `PUPC` {
		c.IsPickupManifest = true
		pickupManifestID, err := strconv.Atoi(c.SttOrPickupCode[4:])
		if err != nil || pickupManifestID < 1 {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid Pickup manifest id",
				"id": "Pickup manifest id tidak valid",
			})
		}

		c.PickupManifestID = pickupManifestID
	} else {
		c.SttNo = c.SttOrPickupCode
	}

	return nil
}

type ViewDetailBagSttRequest struct {
	BagOrSttNo  string `json:"bag_or_stt_no" query:"bag_or_stt_no" form:"bag_or_stt_no"`
	PartnerID   int    `json:"partner_id" query:"partner_id" form:"partner_id"`
	AccountType string
	PartnerType string
}

func (c *ViewDetailBagSttRequest) Validate() error {
	if c.AccountType == model.INTERNAL {
		if c.PartnerID < 1 {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "PartnerID cannot be empty",
				"id": "PartnerID tidak boleh kosong",
			})
		}
	}
	return nil
}

func (c *ViewDetailBagSttRequest) TypeNo() (string, error) {
	if c.BagOrSttNo == `` {
		return "", shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT or BAG NO is empty",
			"id": "STT or BAG NO kosong",
		})
	}

	var status string = "stt"
	errBagNo := validation.Validate(c.BagOrSttNo, validation.Match(model.BagCodeFormat))

	if errBagNo == nil {
		status = "bag"
	}

	return status, nil
}
