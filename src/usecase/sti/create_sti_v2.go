package sti

import "time"

type Stt struct {
	IsPaid                         bool      `json:"is_paid"`
	Flag                           string    `json:"flag"`
	StatusReturn                   string    `json:"status_return"`
	DeadlineReturn                 time.Time `json:"deadline_return"`
	BagNo                          string    `json:"bag_no"`
	IsStiDest                      int       `json:"is_sti_dest"`
	RefNo                          string    `json:"ref_no"`
	SttID                          int       `json:"stt_id"`
	SttNo                          string    `json:"stt_no"`
	SttProductType                 string    `json:"stt_product_type"`
	SttTotalPiece                  int       `json:"stt_total_piece"`
	SttDestinationCityID           string    `json:"stt_destination_city_id"`
	SttDestinationCityName         string    `json:"stt_destination_city_name"`
	SttOriginCityID                string    `json:"stt_origin_city_id"`
	SttOriginCityName              string    `json:"stt_origin_city_name"`
	SttWoodPacking                 string    `json:"stt_wood_packing"`
	SttCommodityCode               string    `json:"stt_commodity_code"`
	SttCommodityName               string    `json:"stt_commodity_name"`
	SttGrossWeight                 float64   `json:"stt_gross_weight"`
	SttVolumeWeight                float64   `json:"stt_volume_weight"`
	SttChargeableWeight            float64   `json:"stt_chargeable_weight"`
	SttLastStatusID                string    `json:"stt_last_status_id"`
	SttPaymentStatus               string    `json:"stt_payment_status"`
	BookingID                      string    `json:"booking_id"`
	SttShipmentID                  string    `json:"stt_shipment_id"`
	SttNoRefExternal               string    `json:"stt_no_ref_external"`
	Piece                          []PieceV2 `json:"piece"`
	SttDestinationDistrictUrsacode string    `json:"stt_destination_district_ursacode"`
	PackageNumber                  *string   `json:"package_number"`
	SttRegionID                    string    `json:"stt_region_id"`
	SttRegionName                  string    `json:"stt_region_name"`
	SttDestinationRegionName       string    `json:"stt_destination_region_name"`
	SttOriginRegionName            string    `json:"stt_origin_region_name"`
	StiDestOneBagScan              bool      `json:"sti_dest_one_bag_scan"`
	BookedType                     string    `json:"booked_type"`
	BookedID                       int       `json:"booked_id"`
	BookedName                     string    `json:"booked_name"`
	IsDangerousGoods               bool      `json:"is_dangerous_goods"`
	SttAssessmentStatus            string    `json:"stt_assessment_status"`
	SttNeedToRelabel               bool      `json:"stt_need_to_relabel"`
}

type Bag struct {
	IsPaid                         bool      `json:"is_paid"`
	Flag                           string    `json:"flag"`
	StatusReturn                   string    `json:"status_return"`
	DeadlineReturn                 time.Time `json:"deadline_return"`
	BagNo                          string    `json:"bag_no"`
	IsStiDest                      int       `json:"is_sti_dest"`
	RefNo                          string    `json:"ref_no"`
	SttID                          int       `json:"stt_id"`
	SttNo                          string    `json:"stt_no"`
	SttProductType                 string    `json:"stt_product_type"`
	SttTotalPiece                  int       `json:"stt_total_piece"`
	SttDestinationCityID           string    `json:"stt_destination_city_id"`
	SttDestinationCityName         string    `json:"stt_destination_city_name"`
	SttOriginCityID                string    `json:"stt_origin_city_id"`
	SttOriginCityName              string    `json:"stt_origin_city_name"`
	SttWoodPacking                 string    `json:"stt_wood_packing"`
	SttCommodityCode               string    `json:"stt_commodity_code"`
	SttCommodityName               string    `json:"stt_commodity_name"`
	SttGrossWeight                 float64   `json:"stt_gross_weight"`
	SttVolumeWeight                float64   `json:"stt_volume_weight"`
	SttChargeableWeight            float64   `json:"stt_chargeable_weight"`
	SttLastStatusID                string    `json:"stt_last_status_id"`
	SttPaymentStatus               string    `json:"stt_payment_status"`
	BookingID                      string    `json:"booking_id"`
	SttShipmentID                  string    `json:"stt_shipment_id"`
	SttNoRefExternal               string    `json:"stt_no_ref_external"`
	Piece                          []PieceV2 `json:"piece"`
	SttDestinationDistrictUrsacode string    `json:"stt_destination_district_ursacode"`
	PackageNumber                  *string   `json:"package_number"`
	SttRegionID                    string    `json:"stt_region_id"`
	SttRegionName                  string    `json:"stt_region_name"`
	SttDestinationRegionName       string    `json:"stt_destination_region_name"`
	SttOriginRegionName            string    `json:"stt_origin_region_name"`
	StiDestOneBagScan              bool      `json:"sti_dest_one_bag_scan"`
	BookedType                     string    `json:"booked_type"`
	BookedID                       int       `json:"booked_id"`
	BookedName                     string    `json:"booked_name"`
	IsDangerousGoods               bool      `json:"is_dangerous_goods"`
	SttAssessmentStatus            string    `json:"stt_assessment_status"`
}

type PieceV2 struct {
	SttPieceID           int     `json:"stt_piece_id"`
	SttPieceGrossWeight  float64 `json:"stt_piece_gross_weight"`
	SttPieceVolumeWeight float64 `json:"stt_piece_volume_weight"`
	SttPieceNo           int     `json:"stt_piece_no"`
	SttPieceLastStatusID string  `json:"stt_piece_last_status_id"`
}

type ResponseCreateV2 struct {
	StiID int   `json:"sti_id"`
	Stt   *Stt  `json:"stt"`
	Bag   []Bag `json:"bag"`
}
