package sti

import (
	"strings"
	"time"

	validation "github.com/go-ozzo/ozzo-validation/v4"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/general"
	"github.com/Lionparcel/hydra/src/usecase/stt_activity"
)

type CreateStiRequest struct {
	PartnerID       int      `json:"-"`
	PartnerCode     string   `json:"-"`
	PartnerName     string   `json:"-"`
	PartnerType     string   `json:"-"`
	AccountID       int64    `json:"-"`
	AccountName     string   `json:"-"`
	StiSttNo        []string `json:"stt_no"`
	BagOrSttNo      string   `json:"bag_or_stt_no"`
	HubID           int      `json:"hub_id"`
	HubName         string   `json:"hub_name"`
	HubOriginCity   string   `json:"hub_origin_city"`
	HubDistrictCode string   `json:"hub_district_code"`
	Token           string
	BookedType      string `json:"booked_type"`
	BookedID        int    `json:"booked_id"`
}

type CreateSti struct {
	PartnerID         int                     `json:"partner_id"`
	PartnerCode       string                  `json:"partner_code"`
	PartnerName       string                  `json:"partner_name"`
	OriginCityCode    string                  `json:"origin_city_code"`
	OriginCityName    string                  `json:"origin_city_name"`
	TotalSTT          int                     `json:"total_stt"`
	TotalGrossWeight  float64                 `json:"total_gross_weight"`
	TotalVolumeWeight float64                 `json:"total_volume_weight"`
	TotalPieces       int                     `json:"total_pieces"`
	STICreatedAt      time.Time               `json:"sti_created_at"`
	STICreatedBy      int                     `json:"sti_created_by"`
	STICreatedByName  string                  `json:"sti_created_by_name"`
	STIUpdatedAt      time.Time               `json:"sti_updated_at"`
	StiDetail         []model.StiDetail       `json:"sti_detail"`
	SttPieceHistory   []model.SttPieceHistory `json:"stt_piece_stt_history"`
	Stt               []model.Stt             `json:"stt"`
}

type StiSttPiece struct {
	STIDetailSttNo          string  `json:"sti_detail_stt_no"`
	STIDetailSttID          int     `json:"sti_detail_stt_id"`
	STISttTotalPiece        int     `json:"sti_stt_total_piece"`
	STISttPieceID           int     `json:"sti_stt_piece_id"`
	STISttPieceNo           string  `json:"sti_stt_piece_no"`
	STISttPieceGrossWeight  float64 `json:"sti_stt_piece_gross_weight"`
	STISttPieceVolumeWeight float64 `json:"sti_stt_piece_volume_weight"`
	STISttPieceProductType  string  `json:"sti_stt_piece_product_type"`
}

// Validate CreateStiRequest
func (c *CreateStiRequest) Validate() error {
	if err := validation.Validate(c.PartnerID, validation.Min(1), validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Partner id cannot be empty",
			"id": "Id partner tidak boleh kosong",
		})
	}

	if len(c.StiSttNo) == 0 {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "STT number id cannot be empty",
			"id": "Nomor STT tidak boleh kosong",
		})
	}

	/**
	 * CONS only
	 */
	if strings.ToLower(c.PartnerType) != model.CONSOLE {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid Account Type format",
			"id": "Format Account Type tidak valid",
		})
	}

	return nil
}

type CreateStiRequestParams struct {
	CreateSti
	Partner           model.Partner
	ListSttSuccess    map[string]model.Stt
	ListSttUpdateTime []stt_activity.SttActivityRequestDetail
	STTFailed         []general.STTFailedGeneralResponse
	StiCreatedAt      time.Time
	Token             string
}

type GenerateResponseV2Params struct {
	Data                 *CreateStiRequest
	IsStiDest            map[string]int
	ListPdDeadlineReturn map[string]time.Time
	Token                string
	MappingMeta          map[string]model.StiTemporaryMeta
	BookingResult        map[string]BookingSTTResult
	SttIsNoPaid          map[string]bool
}

type BookingSTTResult struct {
	BookedTypeResult string `json:"-"`
	BookedIDResult   int    `json:"-"`
	BookedNameResult string `json:"-"`
}
