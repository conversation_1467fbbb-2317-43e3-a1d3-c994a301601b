package usecase

import (
	"context"
	"strings"
	"time"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *sttCtx) checkIsInsideAssigned3LC(ctx context.Context, params *stt.CreateSttManualForClient, origin3LC string) error {
	// Get Assigned 3LC rule config
	rules, err := c.cfg.GetLimitedAssigned3LCRule()
	if err != nil {
		return err
	}
	if len(rules) == 0 {
		return nil
	}

	// Check if account type & role inside config
	if !isAccountInsideAssigned3LCRule(params.AccountType, params.AccountRoleName, rules) {
		return nil
	}

	// Get account
	account, err := c.accountRepo.GetAccountDetail(ctx, int(params.AccountID), params.Token)
	if err != nil {
		return err
	}

	// Get assigned 3LC
	assigned3LC := getAssigned3LCFromAccountMeta(account.Data.Meta)
	if len(assigned3LC) == 0 {
		return nil
	}

	// Check assigned 3LC
	if assigned3LC[0] == "ALL" {
		return nil
	}
	for _, value := range assigned3LC {
		if value == origin3LC {
			return nil
		}
	}
	return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "User is not permitted to perform this operation.",
		"id": "User tidak diizinkan melakukan operasi ini.",
	})
}

func getAssigned3LCFromAccountMeta(accountMeta interface{}) []string {
	// Convert Meta
	obj, ok := accountMeta.(map[string]interface{})
	if !ok {
		return []string{}
	}

	assigned3LC, ok := obj["assigned_3lc"].(string)
	if !ok {
		return []string{}
	}
	return strings.Split(assigned3LC, ",")
}

func isAccountInsideAssigned3LCRule(accountType string, accountRole string, rules []config.LimitedAssigned3LCRule) bool {
	for _, rule := range rules {
		if rule.Role != accountRole {
			continue
		}

		if rule.Type != accountType {
			continue
		}
		return true
	}

	return false
}

func (c *sttCtx) generateBookedSTTCreateSTTForClient(ctx context.Context, params *stt.CreateSttManualForClient, bookedClient *model.Client) (bookedBy int, bookedName, bookedRole, bookedCode, actorExternalCode string, bookedByActorDetail model.Actor, err error) {
	// actorExternalCode this value is external code from partner or client
	generateBooked := map[string]func(ctx context.Context, params *stt.CreateSttManualForClient, bookedClient *model.Client) (bookedBy int, bookedName, bookedRole, bookedCode, actorExternalCode string, bookedByActorDetail model.Actor, err error){
		model.INTERNAL: c.generateBookedSTTCreateSTTForClientByInternal,
		model.PARTNER:  c.generateBookedSTTCreateSTTForClientByPartnerOrClient,
		model.CLIENT:   c.generateBookedSTTCreateSTTForClientByPartnerOrClient,
	}

	generateBookedFunc, ok := generateBooked[params.AccountType]
	if !ok {
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid Account Type format",
			"id": "Format Account Type tidak valid",
		})
		return
	}

	return generateBookedFunc(ctx, params, bookedClient)
}

func (c *sttCtx) generateBookedSTTCreateSTTForClientByInternal(ctx context.Context, params *stt.CreateSttManualForClient, bookedClient *model.Client) (bookedBy int, bookedName, bookedRole, bookedCode, actorExternalCode string, bookedByActorDetail model.Actor, err error) {
	bookedBy = model.AccountInternal.ActorID
	bookedName = model.AccountInternal.ActorName
	bookedRole = model.AccountInternal.ActorName
	actorExternalCode = model.AccountInternal.ActorName
	bookedCode = model.AccountInternal.ActorName
	return
}

func (c *sttCtx) generateBookedSTTCreateSTTForClientByPartnerOrClient(ctx context.Context, params *stt.CreateSttManualForClient, bookedClient *model.Client) (bookedBy int, bookedName, bookedRole, bookedCode, actorExternalCode string, bookedByActorDetail model.Actor, err error) {
	isKurirRekomendasi := shared.IsLiloPrefix(params.Stt.SttNoRefExternal)

	if params.AccountType == model.CLIENT {
		params.AccountRefType = model.POS
		params.AccountRefID = bookedClient.Data.ClientPartnerID
		params.AccountRefName = bookedClient.Data.ClientPartnerName
	}

	if params.AccountRefType != model.POS {
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid Account Type format",
			"id": "Format Account Type tidak valid",
		})
		return
	}

	partnerDataNotEligible := params.AccountType == model.PARTNER && bookedClient.Data.ClientPartnerID != params.AccountRefID && !isKurirRekomendasi
	if partnerDataNotEligible {
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Client is not found",
			"id": "Client tidak ditemukan",
		})
		return
	}

	partnerBooking, err := c.getValidatePartnerSTTCreateSTTForClientByPartnerOrClient(ctx, params)
	if err != nil {
		return
	}

	params.Stt.SttOriginDistrictID = partnerBooking.Data.PartnerLocation.DistrictCode
	params.Stt.SttOriginCityID = partnerBooking.Data.PartnerLocation.CityCode
	actorExternalCode = partnerBooking.Data.PartnerExternalCode

	bookedBy = params.AccountRefID
	bookedName = params.AccountRefName
	bookedRole = params.AccountRefType
	bookedCode = partnerBooking.Data.Code
	bookedByActorDetail = model.Actor{
		ActorPosDetail: model.ActorPos{
			PosBranchCommission: partnerBooking.Data.PartnerPosBranchCommission,
			PosParentID:         partnerBooking.Data.PartnerPosParentID,
		},
	}

	return
}

func (c *sttCtx) generateSttMetaProductTypeInterpack(params *stt.CreateSttManualForClient, sttMeta model.SttMeta) model.SttMeta {
	if params.Stt.SttProductType == model.INTERPACK {
		sttMeta.SttAttachFiles = params.Stt.SttAttachFiles
		sttMeta.SttCommodityDetail = params.Stt.SttCommodityDetail
		sttMeta.SttRecipientEmail = params.Stt.SttRecipientEmail
		sttMeta.SttKtpImage = params.Stt.SttKtpImage
		sttMeta.SttTaxImage = params.Stt.SttTaxImage
		sttMeta.SttInterTaxNumber = params.Stt.SttInterTaxNumber
		sttMeta.SttIdentityNumber = params.Stt.SttIdentityNumber
		sttMeta.SttCIPL = params.Stt.SttCIPL
	}

	return sttMeta
}

func (c *sttCtx) getSttManual(ctx context.Context, params *stt.CreateSttManualForClient, isMixpack bool) (*model.SttManual, error) {
	checkSTT, err := c.sttManualRepo.Get(ctx, &model.SttManualViewParams{
		SttManualID:  params.Stt.SttNo,
		AccountType:  model.CLIENT,
		AccountRefID: params.SttClient,
		IsMixpack:    isMixpack,
	})
	if err != nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while querying db",
			"id": "Terjadi kesalahan pada saat query db",
		})
	}

	if checkSTT == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT is not found",
			"id": "STT tidak ditemukan",
		})
	}

	if checkSTT.SttManualStatus == model.SttManualUsed {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT is already used",
			"id": "STT sudah terpakai",
		})
	}
	return checkSTT, nil
}

func (c *sttCtx) checkProductTypes(ctx context.Context, params *stt.CreateSttManualForClient) error {
	productType, err := c.productRepo.GetProductTypes(ctx, &model.ListProductTypeRequest{
		Token:  params.Token,
		Code:   params.Stt.SttProductType,
		Status: model.ACTIVE,
		Limit:  model.DefaultLimit,
		Page:   model.DefaultPage,
	})
	if err != nil || productType == nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Product Type is not found",
			"id": "Product Type tidak ditemukan",
		})
	}

	if len(productType.Data) < 1 {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Product Type is inactive",
			"id": "Product Type tidak aktif",
		})
	}
	return nil
}

func (c *sttCtx) createpickupManifest(ctx context.Context, params *stt.CreateSttManualForClient, generatedStt []stt.CreateSttResult, sttCreate *model.SttCreate) error {
	now, _ := shared.ParseUTC7(shared.FormatDateTime, c.timeRepo.Now(time.Now()).Format(shared.FormatDateTime))
	if params.AccountType == model.CLIENT {
		err := c.pickupManifestCbpRepo.Create(ctx, &model.PickupManifestCbp{
			PmcSttNo:     generatedStt[0].SttNo,
			PmcStatus:    model.PmcUnprinted,
			PmcPosID:     sttCreate.Stt.SttBookedBy,
			PmcClientID:  sttCreate.Stt.SttClientID,
			PmcCreatedAt: now,
			PmcUpdatedAt: now,
		})
		if err != nil {
			return err
		}
	}
	return nil
}