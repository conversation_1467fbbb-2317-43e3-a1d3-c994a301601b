package usecase

import (
	"context"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/src/repository"
	"github.com/Lionparcel/hydra/src/usecase/cargo_v2"
)

// CargoV2 interface  ...
type CargoV2 interface {
	CreateCargoV2(ctx context.Context, params *cargo_v2.CreateCargoV2Request) (*cargo_v2.CreateCargoV2Response, error)
	CreateCargoV2Sabre(ctx context.Context, params *cargo_v2.CreateCargoV2Request) (*cargo_v2.CreateCargoV2Response, error)
	SchedulerUpdateEstimationTimeCargoNgenV2(ctx context.Context)

	UpdateCargoPlaneStatus(ctx context.Context, params *cargo_v2.UpdateCargoPlaneStatusParams) error

	ForceUpdateDepArrCargoFlight(ctx context.Context, params *cargo_v2.ForceUpdateDepArrCargoRequest) error
}

type cargoV2Ctx struct {
	cargoRepo               repository.CargoRepository
	cargoDetailRepo         repository.CargoDetailRepository
	districtRepo            repository.DistrictRepository
	cityRepo                repository.CityRepository
	sttPieceHistoryRepo     repository.SttPieceHistoryRepository
	sttRepo                 repository.SttRepository
	sttPieceRepo            repository.SttPiecesRepository
	commodityRepo           repository.CommodityRepository
	productTypeRepo         repository.ProductTypeRepository
	cargoProductTypeRepo    repository.CargoProductTypeRepository
	commodityGroupRepo      repository.CommodityGroupRepository
	partnerRepo             repository.PartnerRepository
	bagRepo                 repository.BagRepository
	ngenRepo                repository.NgenRepository
	airportRepo             repository.AirportRepository
	gatewaySttStatusUc      GatewaySttStatus
	accountRepo             repository.AccountRepository
	partnerLocationRepo     repository.PartnerLocationRepository
	partnerLog              repository.PartnerLogRepository
	cfg                     *config.Config
	cargoFlightRepo         repository.CargoFlightRepository
	sttActivityRepo         repository.SttActivityRepository
	sttActivityUc           SttActivity
	timeRepo                repository.TimeRepository
	luwjistik               Luwjistik
	retryCargoRepo          repository.RetryCargoRepository
	readyToCargo            ReadyToCargo
	cargoReserveRepo        repository.CargoReserveRepository
	cargoReserveUc          CargoReserve
	bagCargo                repository.BagCargoRepository
	SttReadyToCargoRepo     repository.SttReadyToCargoRepository
	bagReadyToCargoRepo     repository.BaggingReadyToCargoHistoryRepository
	readyToCargoRepo        repository.ReadyToCargoRepository
	cacheRepo               repository.CacheRepository
	sttDueRepo              repository.SttDueRepository
	hubRepo                 repository.HubRepository
	sttDueUC                STTDue
	retryCargoUc            RetryCargo
	cargoSearchFlightUc     CargoSearchFlightUC
	readyToCargoHistoryRepo repository.ReadyToCargoHistoryRepository
}

// NewCargoV2Uc ...
func NewCargoV2Uc(
	cargoRepo repository.CargoRepository,
	cargoDetailRepo repository.CargoDetailRepository,
	cityRepo repository.CityRepository,
	districtRepo repository.DistrictRepository,
	sttPieceHistoryRepo repository.SttPieceHistoryRepository,
	sttRepo repository.SttRepository,
	sttPieceRepo repository.SttPiecesRepository,
	commodityRepo repository.CommodityRepository,
	productTypeRepo repository.ProductTypeRepository,
	cargoProductTypeRepo repository.CargoProductTypeRepository,
	commodityGroupRepo repository.CommodityGroupRepository,
	partnerRepo repository.PartnerRepository,
	bagRepo repository.BagRepository,
	ngenRepo repository.NgenRepository,
	airportRepo repository.AirportRepository,
	gatewaySttStatusUc GatewaySttStatus,
	accountRepo repository.AccountRepository,
	partnerLocationRepo repository.PartnerLocationRepository,
	partnerLog repository.PartnerLogRepository,
	cargoFlightRepo repository.CargoFlightRepository,
	sttActivityRepo repository.SttActivityRepository,
	sttActivityUc SttActivity,
	timeRepo repository.TimeRepository,
	luwjistik Luwjistik,
	retryCargoRepo repository.RetryCargoRepository,
	readyToCargo ReadyToCargo,
	cargoReserveRepo repository.CargoReserveRepository,
	cargoReserveUc CargoReserve,
	bagCargo repository.BagCargoRepository,
	SttReadyToCargoRepo repository.SttReadyToCargoRepository,
	bagReadyToCargoRepo repository.BaggingReadyToCargoHistoryRepository,
	readyToCargoRepo repository.ReadyToCargoRepository,
	cacheRepo repository.CacheRepository,
	sttDueRepo repository.SttDueRepository,
	hubRepo repository.HubRepository,
	sttDueUC STTDue,
	retryCargoUc RetryCargo,
	cargoSearchFlightUc CargoSearchFlightUC,
	RtcRepo repository.ReadyToCargoHistoryRepository,
) CargoV2 {
	return &cargoV2Ctx{
		cargoRepo:               cargoRepo,
		cargoDetailRepo:         cargoDetailRepo,
		districtRepo:            districtRepo,
		cityRepo:                cityRepo,
		sttPieceHistoryRepo:     sttPieceHistoryRepo,
		sttRepo:                 sttRepo,
		sttPieceRepo:            sttPieceRepo,
		commodityRepo:           commodityRepo,
		productTypeRepo:         productTypeRepo,
		cargoProductTypeRepo:    cargoProductTypeRepo,
		commodityGroupRepo:      commodityGroupRepo,
		partnerRepo:             partnerRepo,
		bagRepo:                 bagRepo,
		ngenRepo:                ngenRepo,
		airportRepo:             airportRepo,
		gatewaySttStatusUc:      gatewaySttStatusUc,
		accountRepo:             accountRepo,
		partnerLocationRepo:     partnerLocationRepo,
		partnerLog:              partnerLog,
		cargoFlightRepo:         cargoFlightRepo,
		sttActivityRepo:         sttActivityRepo,
		sttActivityUc:           sttActivityUc,
		timeRepo:                timeRepo,
		luwjistik:               luwjistik,
		retryCargoRepo:          retryCargoRepo,
		readyToCargo:            readyToCargo,
		cargoReserveRepo:        cargoReserveRepo,
		cargoReserveUc:          cargoReserveUc,
		bagCargo:                bagCargo,
		SttReadyToCargoRepo:     SttReadyToCargoRepo,
		bagReadyToCargoRepo:     bagReadyToCargoRepo,
		readyToCargoRepo:        readyToCargoRepo,
		cacheRepo:               cacheRepo,
		sttDueRepo:              sttDueRepo,
		hubRepo:                 hubRepo,
		sttDueUC:                sttDueUC,
		retryCargoUc:            retryCargoUc,
		cargoSearchFlightUc:     cargoSearchFlightUc,
		readyToCargoHistoryRepo: RtcRepo,
	}
}
