package delivery

import (
	"strings"

	"github.com/Lionparcel/hydra/shared"
	validation "github.com/go-ozzo/ozzo-validation/v4"
)

// ViewDetailSttRequest ...
type ViewDetailSttRequest struct {
	SttNo       string `json:"stt_no" query:"stt_no" form:"stt_no"`
	PartnerID   int
	PartnerType string
}

// Validate CheckSttPieceNumberRequest
func (c *ViewDetailSttRequest) Validate() error {
	c.SttNo = strings.ReplaceAll(strings.TrimSpace(c.SttNo), " ", "")
	if err := validation.Validate(c.SttNo, validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "STT Number is required to be filled",
			"id": "Nomor STT  harus di isi",
		})
	}

	return nil
}
