package usecase

import (
	"context"
	"database/sql"
	"fmt"
	"time"

	"github.com/Lionparcel/go-lptool/v2/lputils"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/dispatch"
	"github.com/Lionparcel/hydra/src/usecase/stt_activity"
	"github.com/Lionparcel/hydra/src/usecase/stt_piece_history_remark_helper"
)

func (c *dispatchCtx) CreateTemporary(ctx context.Context, params *dispatch.CreateDispatchTemporaryRequest) (*dispatch.CreateDispatchTemporaryResponse, error) {
	var (
		opName  = "dispatchCtx-CreateTemporary"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		res     = &dispatch.CreateDispatchTemporaryResponse{}
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "res": res, "error": err})
	}()

	// validate account role, determine the status
	err = params.Validate()
	if err != nil {
		return nil, err
	}

	cdt := createDispatchTemp{
		service:    c,
		params:     params,
		now:        time.Now(),
		scanStatus: map[string]string{},
	}

	// get scan by stt or dem
	fs := []func(ctx context.Context) error{
		cdt.GetConsole,
		cdt.GetListStt,
		cdt.GetExistingDispatchTemporary,
		cdt.ProcessValidateStt,
		cdt.UpdateDemStatus,
		cdt.ExecuteDispatchTemporary,
		cdt.GenerateResponse,
	}

	for i := range fs {
		err = fs[i](selfCtx)
		if err != nil {
			return nil, err
		}
	}

	res = cdt.res

	return res, nil
}

type createDispatchTemp struct {
	service *dispatchCtx
	params  *dispatch.CreateDispatchTemporaryRequest
	now     time.Time

	partner          *model.Partner
	allowedLocations map[string]bool
	sttNos           []string                // for get dispatch_temporary
	stts             []model.SttDetailResult // stt might be duplicate, depends on stt piece
	sttNoRef         map[string]string
	sttDemNo         string

	existingSttNoDts map[string]model.DispatchTemporary
	createDt         []model.DispatchTemporary
	updateDt         []model.DispatchTemporary

	failedCount int

	demStatus  string
	scanStatus map[string]string
	res        *dispatch.CreateDispatchTemporaryResponse
}

func (c *createDispatchTemp) GetConsole(ctx context.Context) error {
	var (
		opName     = "dispatchCtx-createDispatchTemp-GetConsole"
		trace      = tracer.StartTrace(ctx, opName)
		selfCtx    = trace.Context()
		errTracing error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": errTracing})
	}()

	partner, err := c.service.partnerRepo.GetByID(selfCtx, c.params.PartnerID, c.params.Token)
	if err != nil || partner == nil {
		errTracing = err
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting making HTTP request",
			"id": "Terjadi kesalahan pada saat HTTP request",
		})
	}
	if partner.Data.PartnerLocation == nil {
		errTracing = err
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner is not yet mapped with location",
			"id": "Partner belum di Mapping dengan lokasi",
		})
	}

	groupLocations, err := c.service.hubRepo.GetBaggingGroupByPartnerID(selfCtx, &model.HubViewBaggingGroupParams{
		PartnerID: c.params.PartnerID,
	}, c.params.Token)
	if err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting making HTTP request",
			"id": "Terjadi kesalahan pada saat HTTP request",
		})
	}

	c.allowedLocations = map[string]bool{}
	c.allowedLocations[partner.Data.PartnerLocation.CityCode] = true
	for i := range groupLocations {
		c.allowedLocations[groupLocations[i].CityCode] = true
	}

	c.partner = partner

	return nil
}

func (c *createDispatchTemp) GetListStt(ctx context.Context) error {
	var (
		opName     = "dispatchCtx-createDispatchTemp-GetListStt"
		trace      = tracer.StartTrace(ctx, opName)
		selfCtx    = trace.Context()
		errTracing error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": errTracing})
	}()

	getNoRef := func(stt *model.Stt) string {
		refNo := stt.SttNoRefExternal
		if stt.SttShipmentID != "" {
			refNo = stt.SttShipmentID
		}

		return refNo
	}

	c.sttNoRef = map[string]string{}

	// Get dem
	if c.params.IsScanByDem {
		dem, err := c.service.deliveryManifestRepo.SelectDetail(selfCtx, &model.DeliveryManifestViewParams{
			DeliveryManifestNo: c.params.ScanNo,
			// WithCurrentSttList: true,
		})
		if err != nil || len(dem) <= 0 {
			errTracing = err
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed obtain STT/delivery manifest data",
				"id": "Gagal memperoleh data STT/delivery manifest",
			})
		}

		sttUnique := map[string]bool{}
		for i := range dem {
			c.stts = append(c.stts, model.SttDetailResult{
				SttPiece: dem[i].SttPiece,
				Stt:      dem[i].Stt,
			})

			if sttUnique[dem[i].Stt.SttNo] {
				continue
			}
			sttUnique[dem[i].Stt.SttNo] = true

			c.sttNos = append(c.sttNos, dem[i].Stt.SttNo)
			c.sttNoRef[dem[i].Stt.SttNo] = getNoRef(&dem[i].Stt)
		}
		c.sttDemNo = c.params.ScanNo
		return nil
	}

	// Get by stt
	sttDetail, err := c.service.sttRepo.SelectDetailStt(selfCtx, &model.SttSelectDetailParams{
		Search:          c.params.ScanNo,
		SearchByPattern: true,
	})
	if err != nil || len(sttDetail) == 0 {
		errTracing = err
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed obtain STT/delivery manifest data",
			"id": "Gagal memperoleh data STT/delivery manifest",
		})
	}
	c.stts = append(c.stts, sttDetail...)
	c.sttNos = append(c.sttNos, sttDetail[0].SttNo)
	c.sttNoRef[sttDetail[0].SttNo] = getNoRef(&sttDetail[0].Stt)

	// Get dem for response
	dem, err := c.service.deliveryManifestRepo.Select(selfCtx, &model.DeliveryManifestViewParams{
		Search:  c.params.ScanNo,
		IsSttNo: true,
	})
	if err != nil {
		errTracing = err
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed obtain delivery manifest data",
			"id": "Gagal memperoleh data delivery manifest",
		})
	}
	if len(dem) > 0 {
		c.sttDemNo = dem[0].DeliveryManifestNo
	}

	return nil
}

func (c *createDispatchTemp) GetExistingDispatchTemporary(ctx context.Context) error {
	var (
		opName     = "dispatchCtx-createDispatchTemp-GetListStt"
		trace      = tracer.StartTrace(ctx, opName)
		selfCtx    = trace.Context()
		errTracing error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": errTracing})
	}()

	dts, err := c.service.dispatchRepo.GetActiveDispatchTemporaryBySttNo(selfCtx, &model.GetDispatchTemporaryByAccountSttNo{
		SttNos:    c.sttNos,
		AccountID: c.params.AccountID,
		HubID:     c.params.HubID,
	})
	if err != nil {
		errTracing = err
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed obtain dispatch temporary data",
			"id": "Gagal memperoleh data dispatch temporary",
		})
	}

	c.existingSttNoDts = map[string]model.DispatchTemporary{}
	for i := range dts {
		c.existingSttNoDts[dts[i].STTNo] = dts[i]
	}

	var isAllScannedBeforeIsSuccess bool
	var countSuccessScannned int
	for i := range dts {
		isNeedCount := dts[i].DemStatus == dispatch.DispatchDemStatusSuccess &&
			(dts[i].ScanNo == c.params.ScanNo || !c.params.IsScanByDem)
		if isNeedCount {
			countSuccessScannned++
		}
	}

	isAllScannedBeforeIsSuccess = countSuccessScannned == len(c.sttNos)
	if isAllScannedBeforeIsSuccess {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Dem or Stt no has been scanned before",
			"id": "Nomor Dem atau Stt pernah discan",
		})
	}

	return nil
}

func (c *createDispatchTemp) ProcessValidateStt(ctx context.Context) error {
	var (
		opName     = "dispatchCtx-createDispatchTemp-GetListStt"
		trace      = tracer.StartTrace(ctx, opName)
		selfCtx    = trace.Context()
		errTracing error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": errTracing})
	}()

	updatedFields := c.prepareUpdatedFields()

	isSttNotUnique := map[string]bool{}
	for i := range c.stts {
		if isSttNotUnique[c.stts[i].SttNo] {
			continue
		}
		isSttNotUnique[c.stts[i].SttNo] = true

		if err := c.processSingleStt(selfCtx, &c.stts[i].Stt, updatedFields); err != nil {
			errTracing = err
			return err
		}
	}

	return nil
}

func (c *createDispatchTemp) prepareUpdatedFields() model.DispatchTemporary {
	return model.DispatchTemporary{
		UpdatedAt: sql.NullTime{
			Time:  c.now,
			Valid: true,
		},
		UpdatedBy: sql.NullInt64{
			Int64: c.params.AccountID,
			Valid: true,
		},
	}
}

func (c *createDispatchTemp) processSingleStt(ctx context.Context, stt *model.Stt, updatedFields model.DispatchTemporary) error {
	scanStatus := c.determineSttScanStatus(ctx, stt)

	existingDt, exists := c.existingSttNoDts[stt.SttNo]
	// if status before is success force to success
	if existingDt.ScanStatus == dispatch.DispatchScanPerSttSuccess {
		scanStatus = dispatch.DispatchScanPerSttSuccess
	}

	c.scanStatus[stt.SttNo] = scanStatus

	if scanStatus != dispatch.DispatchScanPerSttSuccess {
		c.failedCount++
	}

	if exists {
		c.updateDt = append(c.updateDt, c.updateDispatchTemp(ctx, stt, existingDt, updatedFields))
		return nil
	}

	c.createDt = append(c.createDt, c.createNewDispatchTemp(stt, updatedFields))
	return nil
}

func (c *createDispatchTemp) determineSttScanStatus(ctx context.Context, stt *model.Stt) string {
	if !c.allowedLocations[stt.SttDestinationCityID] {
		return dispatch.DispatchScanPerSttDestination
	}

	if !c.checkSttIsPaid(ctx, stt) {
		return dispatch.DispatchScanPerSttUnpaid
	}

	if !c.isValidStatus(stt) {
		return dispatch.DispatchScanPerSttStatus + stt.SttLastStatusID
	}

	return dispatch.DispatchScanPerSttSuccess
}

func (c *createDispatchTemp) isValidStatus(stt *model.Stt) bool {
	if c.isStatusTransitionAllowed(stt) {
		return true
	}

	return model.IsAllowKonsolDispatchOrShuttleDispatch[stt.SttLastStatusID] &&
		!model.IsNotAllowKonsolDispatchOrShuttleDispatch[stt.SttLastStatusID]
}

func (c *createDispatchTemp) isStatusTransitionAllowed(stt *model.Stt) bool {
	return (stt.SttLastStatusID == model.KONDISPATCH && c.params.Status == model.STLDISPATCH) ||
		(stt.SttLastStatusID == model.STLDISPATCH && c.params.Status == model.KONDISPATCH)
}

func (c *createDispatchTemp) updateDispatchTemp(ctx context.Context, stt *model.Stt, existingDt model.DispatchTemporary, updatedFields model.DispatchTemporary) model.DispatchTemporary {
	status := c.params.Status
	if c.scanStatus[existingDt.STTNo] != dispatch.DispatchScanPerSttSuccess {
		status = stt.SttLastStatusID
	}

	existingDt.ProductType = stt.SttProductType
	existingDt.DestinationCityID = stt.SttDestinationCityID
	existingDt.DestinationCityName = stt.SttDestinationCityName
	existingDt.DestinationDistrictID = stt.SttDestinationDistrictID
	existingDt.DestinationDistrictName = stt.SttDestinationDistrictName
	existingDt.DestinationURSAcode = stt.SttDestinationDistrictUrsaCode
	existingDt.ScanNo = c.params.ScanNo
	existingDt.RefNo = c.sttNoRef[stt.SttNo]
	existingDt.DemNo = c.sttDemNo
	existingDt.Pieces = stt.SttTotalPiece
	existingDt.GrossWeight = stt.SttGrossWeight
	existingDt.LastStatus = status
	existingDt.RecipientName = stt.SttRecipientName
	existingDt.RecipientPhone = stt.SttRecipientPhone
	existingDt.RecipientAddress = stt.SttRecipientAddress
	existingDt.ScanStatus = c.scanStatus[stt.SttNo]
	existingDt.HubID = c.params.HubID
	existingDt.UpdatedBy = updatedFields.UpdatedBy
	existingDt.UpdatedAt = updatedFields.UpdatedAt
	return existingDt
}

func (c *createDispatchTemp) createNewDispatchTemp(stt *model.Stt, updatedFields model.DispatchTemporary) model.DispatchTemporary {
	status := c.params.Status
	if c.scanStatus[stt.SttNo] != dispatch.DispatchScanPerSttSuccess {
		status = stt.SttLastStatusID
	}
	return model.DispatchTemporary{
		AccountID:               c.params.AccountID,
		IsActive:                true,
		STTNo:                   stt.SttNo,
		ProductType:             stt.SttProductType,
		DestinationCityID:       stt.SttDestinationCityID,
		DestinationCityName:     stt.SttDestinationCityName,
		DestinationDistrictID:   stt.SttDestinationDistrictID,
		DestinationDistrictName: stt.SttDestinationDistrictName,
		DestinationURSAcode:     stt.SttDestinationDistrictUrsaCode,
		ScanNo:                  c.params.ScanNo,
		RefNo:                   c.sttNoRef[stt.SttNo],
		DemNo:                   c.sttDemNo,
		Pieces:                  stt.SttTotalPiece,
		GrossWeight:             stt.SttGrossWeight,
		LastStatus:              status,
		RecipientName:           stt.SttRecipientName,
		RecipientPhone:          stt.SttRecipientPhone,
		RecipientAddress:        stt.SttRecipientAddress,
		ScanStatus:              c.scanStatus[stt.SttNo],
		HubID:                   c.params.HubID,
		CreatedAt:               c.now,
		CreatedBy:               c.params.AccountID,
	}
}

func (c *createDispatchTemp) UpdateDemStatus(ctx context.Context) error {
	var (
		opName     = "dispatchCtx-createDispatchTemp-UpdateDemStatus"
		trace      = tracer.StartTrace(ctx, opName)
		selfCtx    = trace.Context()
		errTracing error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": errTracing})
	}()

	// Update Dem status
	demStatus := dispatch.DispatchDemStatusSuccess
	if c.failedCount > 0 {
		demStatus = dispatch.DispatchDemStatusPartial
		if c.failedCount == len(c.sttNos) {
			demStatus = dispatch.DispatchDemStatusFailed
		}
	}

	for i := range c.createDt {
		c.createDt[i].DemStatus = demStatus
	}

	for i := range c.updateDt {
		c.updateDt[i].DemStatus = demStatus
	}

	c.demStatus = demStatus

	return nil
}

func (c *createDispatchTemp) checkSttIsPaid(ctx context.Context, stt *model.Stt) bool {
	opName := "dispatchCtx-createDispatchTemp-checkSttIsPaid"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": stt})
	}()

	if stt.SttPaymentStatus == "" || stt.SttPaymentStatus == model.PAID {
		return true
	}

	sttPerfixAutoCA := shared.GetPrefixSttNo(stt.SttNo) == model.PrefixAutoCA
	shipmentCODCARetail := stt.SttShipmentID != `` && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(stt.SttShipmentID)]

	return sttPerfixAutoCA && shipmentCODCARetail
}
func (c *createDispatchTemp) ExecuteDispatchTemporary(ctx context.Context) error {
	var (
		opName     = "dispatchCtx-createDispatchTemp-ExecuteDispatchTemporary"
		trace      = tracer.StartTrace(ctx, opName)
		selfCtx    = trace.Context()
		errTracing error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": errTracing})
	}()

	for i := range c.updateDt {
		err := c.service.dispatchRepo.UpdateDispatchTemporary(selfCtx, &c.updateDt[i])
		if err != nil {
			errTracing = err
			return shared.ERR_UNEXPECTED_DB
		}

		existingDt := c.existingSttNoDts[c.updateDt[i].STTNo]
		scanBeforeIsFailed := existingDt.ScanStatus != dispatch.DispatchScanPerSttSuccess
		scanNowIsSuccess := c.scanStatus[c.updateDt[i].STTNo] == dispatch.DispatchScanPerSttSuccess

		if !(scanNowIsSuccess && scanBeforeIsFailed) {
			continue
		}

		err = c.updateStatus(selfCtx, c.updateDt[i].STTNo)
		if err != nil {
			errTracing = err
			return shared.ERR_UNEXPECTED_DB
		}
	}

	for i := range c.createDt {
		err := c.service.dispatchRepo.CreateDispatchTemporary(selfCtx, &c.createDt[i])
		if err != nil {
			errTracing = err
			return shared.ERR_UNEXPECTED_DB
		}

		if c.scanStatus[c.createDt[i].STTNo] != dispatch.DispatchScanPerSttSuccess {
			continue
		}

		err = c.updateStatus(selfCtx, c.createDt[i].STTNo)
		if err != nil {
			errTracing = err
			return shared.ERR_UNEXPECTED_DB
		}
	}

	return nil
}

func (c *createDispatchTemp) updateStatus(ctx context.Context, sttNo string) error {
	var (
		opName     = "dispatchCtx-createDispatchTemp-UpdateStatus"
		trace      = tracer.StartTrace(ctx, opName)
		selfCtx    = trace.Context()
		errTracing error
	)

	var (
		sttPieceHistory []model.SttPieceHistory
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": errTracing})
	}()

	remarksPieceHistory := c.generateHistoryPieceRemark(ctx)

	createSttHistory := func(ctx context.Context, stt *model.SttDetailResult, status string) {
		history := &model.SttPieceHistory{
			SttPieceID:         int64(stt.SttPiece.SttPieceID),
			HistoryStatus:      status,
			HistoryLocation:    c.partner.Data.PartnerLocation.CityCode,
			HistoryActorID:     c.partner.Data.ID,
			HistoryActorName:   c.partner.Data.Name,
			HistoryActorRole:   model.CONSOLE,
			HistoryCreatedAt:   c.now,
			HistoryCreatedBy:   int(c.params.AccountID),
			HistoryCreatedName: c.params.AccountName,
			HistoryRemark:      remarksPieceHistory.ToString(),
		}

		sttPieceHistory = append(sttPieceHistory, *history)
		if err := c.service.sttHistoryRepo.Create(ctx, history, int(stt.SttID)); err != nil {
			logger.Ef("dispatchCtx-createDispatchTemp-UpdateStatus: %v", err)
		}
	}

	stt := model.SttDetailResult{}
	// will be process per stt piece
	// so do not check if stt is unique or not
	for i := range c.stts {
		if c.stts[i].SttNo != sttNo {
			continue
		}

		stt = c.stts[i]

		createSttHistory(selfCtx, &stt, c.params.Status)
	}

	c.backgroundProcess(selfCtx, &stt.Stt, sttPieceHistory, remarksPieceHistory)

	return nil
}

func (c *createDispatchTemp) generateHistoryPieceRemark(ctx context.Context) model.RemarkPieceHistory {
	var (
		opName     = "dispatchCtx-createDispatchTemp-generateHistoryPieceRemark"
		trace      = tracer.StartTrace(ctx, opName)
		selfCtx    = trace.Context()
		errTracing error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": errTracing})
	}()

	// build remarks piece history
	tempRemarksPieceHistory := model.RemarkPieceHistory{
		HistoryLocationName: func() string {
			if c.partner.Data.PartnerLocation.City != nil {
				return c.partner.Data.PartnerLocation.City.Name
			}
			return c.partner.Data.PartnerLocation.CityCode
		}(),
		HistoryDistrictName: func() string {
			if c.partner.Data.PartnerLocation.District != nil {
				return c.partner.Data.PartnerLocation.District.Name
			}
			return c.partner.Data.PartnerLocation.DistrictCode
		}(),
	}

	SetHub(c.service.cityRepo, c.service.districtRepo, &stt_piece_history_remark_helper.SetHubParams{
		Ctx:                 selfCtx,
		Token:               c.params.Token,
		HubID:               int(c.params.HubID),
		HubName:             c.params.HubName,
		HubOriginCity:       c.params.HubOriginCity,
		HubDistrictCode:     c.params.HubDistrictCode,
		RemarksPieceHistory: &tempRemarksPieceHistory,
	})

	return tempRemarksPieceHistory
}

func (c *createDispatchTemp) backgroundProcess(ctx context.Context, stt *model.Stt, sttPieceHistory []model.SttPieceHistory, remarksPieceHistory model.RemarkPieceHistory) error {
	var (
		opName     = "dispatchCtx-createDispatchTemp-backgroundProcess"
		trace      = tracer.StartTrace(ctx, opName)
		selfCtx    = trace.Context()
		errTracing error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": errTracing})
	}()

	go func() {
		goCtx, cancel := lputils.CreateContext(60 * 5)
		defer cancel()

		c.service.rtcUc.UpdateInactiveRTCBySttId(goCtx, int(stt.SttID))
	}()

	go func() {
		goCtx, cancel := lputils.CreateContext(60 * 5)
		defer cancel()
		c.service.sttActivityUc.UpdateSttTime(goCtx, &stt_activity.SttActivityRequest{
			ListSttData: []stt_activity.SttActivityRequestDetail{
				{
					SttNo:         stt.SttNo,
					SttStatusTime: c.now,
					SttStatus:     c.params.Status,
				},
			},
		})
	}()

	go func() {
		goCtx, cancel := lputils.CreateContext(60 * 5)
		defer cancel()

		remark := c.generatorDispatchDescription(remarksPieceHistory, c.params.Status)

		statusSubmitParams := &model.UpdateSttStatusWithExtendForMiddleware{
			UpdateSttStatus: &model.UpdateSttStatus{
				SttNo:      stt.GetValidSttElexysNo(),
				Datetime:   c.now.UTC(),
				StatusCode: c.params.Status,
				Location:   c.partner.Data.PartnerLocation.CityCode,
				Remarks:    MappingWebhookRemark(c.params.Status, remark, "", ""), // need adjust ?
				City:       c.partner.Data.PartnerLocation.City.Name,
				UpdatedBy:  c.partner.Data.Name,
				UpdatedOn:  c.now.UTC(),
			},
			ServiceType:      model.PACKAGESERVICE,
			Product:          stt.SttProductType,
			Pieces:           stt.SttTotalPiece,
			GrossWeight:      stt.SttGrossWeight,
			VolumeWeight:     stt.SttVolumeWeight,
			ChargeableWeight: stt.SttChargeableWeight,
			BookedForType:    stt.SttBookedForType,
		}
		AppendLastAndSystemStatus(AppendLastAndSystemStatusParams{
			StatusSubmitParams: statusSubmitParams,
			SttPieceHistories:  sttPieceHistory,
			PartnerName:        c.partner.Data.Name,
		})
		c.service.gatewaySttStatusUc.StatusSubmit(goCtx, statusSubmitParams)
	}()

	return nil
}

func (c *createDispatchTemp) GenerateResponse(ctx context.Context) error {
	var (
		opName     = "dispatchCtx-createDispatchTemp-GenerateResponse"
		trace      = tracer.StartTrace(ctx, opName)
		selfCtx    = trace.Context()
		errTracing error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"error": errTracing})
	}()

	c.initializeBaseResponse()

	if c.params.IsScanByDem {
		c.generateDemResponse()
	} else {
		if err := c.generateSingleSttResponse(); err != nil {
			return err
		}
	}

	return nil
}

func (c *createDispatchTemp) initializeBaseResponse() {
	c.res = &dispatch.CreateDispatchTemporaryResponse{
		DemStatus: c.demStatus,
	}
}

func (c *createDispatchTemp) generateSingleSttResponse() error {
	stt := c.stts[0] // Using first STT as per original logic
	status := c.determineStatus(stt.SttNo, stt.SttLastStatusID)
	createdAt, updatedAt := c.getTimestamps(stt.SttNo)

	c.res.Stt = c.createSttResponse(stt.Stt, status, createdAt, updatedAt)
	return nil
}

func (c *createDispatchTemp) generateDemResponse() {
	isSttNotUnique := make(map[string]bool)

	for _, stt := range c.stts {
		if isSttNotUnique[stt.SttNo] {
			continue
		}
		isSttNotUnique[stt.SttNo] = true

		status := c.determineStatus(stt.SttNo, stt.SttLastStatusID)
		createdAt, updatedAt := c.getTimestamps(stt.SttNo)

		c.res.Dem = append(c.res.Dem, c.createDemResponse(stt.Stt, status, createdAt, updatedAt))
	}
}

func (c *createDispatchTemp) determineStatus(sttNo string, lastStatus string) string {
	if c.scanStatus[sttNo] != dispatch.DispatchScanPerSttSuccess {
		return lastStatus
	}
	return c.params.Status
}

func (c *createDispatchTemp) getTimestamps(sttNo string) (time.Time, time.Time) {
	createdAt := c.now
	var updatedAt time.Time

	if edt, ok := c.existingSttNoDts[sttNo]; ok {
		createdAt = edt.CreatedAt
		updatedAt = c.now
	}

	return createdAt, updatedAt
}

func (c *createDispatchTemp) createSttResponse(stt model.Stt, status string, createdAt, updatedAt time.Time) *dispatch.CreateDispatchTemporarySttResponse {
	return &dispatch.CreateDispatchTemporarySttResponse{
		SttID:                          stt.SttID,
		SttNo:                          stt.SttNo,
		SttProductType:                 stt.SttProductType,
		SttTotalPiece:                  stt.SttTotalPiece,
		SttDestinationCityID:           stt.SttDestinationCityID,
		SttDestinationCityName:         stt.SttDestinationCityName,
		SttDestinationDistrictID:       stt.SttDestinationDistrictID,
		SttDestinationDistrictName:     stt.SttDestinationDistrictName,
		SttDestinationDistrictUrsaCode: stt.SttDestinationDistrictUrsaCode,
		SttCommodityCode:               stt.SttCommodityCode,
		SttCommodityName:               stt.SttCommodityName,
		SttGrossWeight:                 stt.SttGrossWeight,
		SttRecipientAddress:            stt.SttRecipientAddress,
		SttLastStatusID:                status,
		SttRecipientName:               stt.SttRecipientName,
		SttRecipientPhone:              stt.SttRecipientPhone,
		SttCodAmount:                   stt.SttCODAmount,
		RefNo:                          c.sttNoRef[stt.SttNo],
		DemNo:                          c.sttDemNo,
		DtScanNo:                       c.params.ScanNo,
		DtScanStatus:                   c.scanStatus[stt.SttNo],
		DtCreatedAt:                    createdAt,
		DtUpdatedAt:                    updatedAt,
	}
}

func (c *createDispatchTemp) createDemResponse(stt model.Stt, status string, createdAt, updatedAt time.Time) dispatch.CreateDispatchTemporaryDemResponse {
	return dispatch.CreateDispatchTemporaryDemResponse{
		SttID:                          stt.SttID,
		SttNo:                          stt.SttNo,
		SttProductType:                 stt.SttProductType,
		SttTotalPiece:                  stt.SttTotalPiece,
		SttDestinationCityID:           stt.SttDestinationCityID,
		SttDestinationCityName:         stt.SttDestinationCityName,
		SttDestinationDistrictID:       stt.SttDestinationDistrictID,
		SttDestinationDistrictName:     stt.SttDestinationDistrictName,
		SttDestinationDistrictUrsaCode: stt.SttDestinationDistrictUrsaCode,
		SttCommodityCode:               stt.SttCommodityCode,
		SttCommodityName:               stt.SttCommodityName,
		SttRecipientAddress:            stt.SttRecipientAddress,
		SttGrossWeight:                 stt.SttGrossWeight,
		SttLastStatusID:                status,
		SttRecipientName:               stt.SttRecipientName,
		SttRecipientPhone:              stt.SttRecipientPhone,
		SttCodAmount:                   stt.SttCODAmount,
		DemNo:                          c.sttDemNo,
		RefNo:                          c.sttNoRef[stt.SttNo],
		DtScanNo:                       c.params.ScanNo,
		DtScanStatus:                   c.scanStatus[stt.SttNo],
		DtCreatedAt:                    createdAt,
		DtUpdatedAt:                    updatedAt,
	}
}

func (c *createDispatchTemp) generatorDispatchDescription(remarksPieceHistory model.RemarkPieceHistory, status string) string {
	var (
		consoleType  = `Konsol`
		city3LC      = c.partner.Data.PartnerLocation.CityCode
		cityName     = remarksPieceHistory.HistoryLocationName
		districtName = remarksPieceHistory.HistoryDistrictName
	)

	isActorHub := remarksPieceHistory.HubID != 0
	if isActorHub {
		consoleType = `HUB`
		city3LC = remarksPieceHistory.HubOriginCity
		cityName = remarksPieceHistory.HubCityName
		districtName = remarksPieceHistory.HubDistrictName
	}

	// KONDISPATCH
	desc := fmt.Sprintf(`Paket telah keluar dari %s %s %s,%s dan ditugaskan kepada Shuttle atau Kurir`, consoleType, city3LC, cityName, districtName)
	if status == model.STLDISPATCH {
		// expected STLDISPATCH
		desc = fmt.Sprintf(`Paket telah sampai di lokasi shuttle %s %s %s,%s dan sedang diserahkan ke kurir untuk pengantaran.`, consoleType, city3LC, cityName, districtName)
	}

	return desc
}
