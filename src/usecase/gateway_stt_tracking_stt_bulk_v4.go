package usecase

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	gatewaySttTracking "github.com/Lionparcel/hydra/src/usecase/gateway_stt_tracking"
	"github.com/Lionparcel/hydra/src/usecase/predefined_holiday"
)

func (c *gatewaySttTrackingCtx) SttTrackingBulkV4(ctx context.Context, params *gatewaySttTracking.SttTrackingBulkRequestV4) (*gatewaySttTracking.SttTrackingBulkResponseV4, error) {
	var err error
	respSttTracking := new(gatewaySttTracking.SttTrackingBulkResponseV4)
	opName := "STTV4Usecase-SttTrackingBulkV4"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": respSttTracking, "error": err})
	}()

	if err = params.Validate(); err != nil {
		return nil, err
	}

	errQueryDB := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "An error occurred while querying db",
		"id": "Terjadi kesalahan pada saat query db",
	})

	// Create response stt tracking
	respSttTracking.Stts = make([]gatewaySttTracking.SttTrackingBulkV4, 0)
	cityMapping := make(map[string]*model.City)
	for _, sttNo := range params.SttNo {
		sttNo = strings.TrimSpace(sttNo)
		if sttNo == `` {
			continue
		}
		dataStt, err := c.sttPiecesRepo.GetDetail(selfCtx, &model.SttViewParams{
			Search:            sttNo,
			IsSearchByPattern: true,
		})
		if err != nil {
			return nil, errQueryDB
		}

		if dataStt == nil {
			//check again data with bookingID or shipmentID
			if !shared.IsShipmentIDFormat(sttNo) {
				continue
			}
			switch shared.GetPrefixShipmentID(sttNo) {
			case model.TKLP, model.TSLP:
				shipment, err := c.shipmentRepo.GetShipment(selfCtx, &model.ShipmentViewParams{
					ShipmentBookingID: sttNo,
				})
				if err != nil {
					return nil, errQueryDB
				}
				if shipment != nil {
					dataStt, err = c.sttPiecesRepo.GetDetail(selfCtx, &model.SttViewParams{
						Search:            shipment.ShipmentShipmentID,
						IsSearchByPattern: true,
					})
					if err != nil {
						return nil, errQueryDB
					}
				}
			case model.T1:
				shipment, err := c.shipmentRepo.GetShipment(selfCtx, &model.ShipmentViewParams{
					ShipmentShipmentID: sttNo,
				})
				if err != nil {
					return nil, errQueryDB
				}
				if shipment != nil {
					dataStt, err = c.sttPiecesRepo.GetDetail(selfCtx, &model.SttViewParams{
						Search:            shipment.ShipmentBookingID,
						IsSearchByPattern: true,
					})
					if err != nil {
						return nil, errQueryDB
					}
				}
			}
			if dataStt == nil {
				continue
			}
		}

		productType := dataStt.SttProductType

		cityCountry, err := c.cityRepo.Get(selfCtx, dataStt.SttDestinationCityID, params.Token)
		if err != nil || cityCountry == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to get location city data ",
				"id": "Gagal mendapatkan data kota lokasi",
			})
		}

		countryName := cityCountry.Country.Name

		sttHistory, err := c.sttPieceHistoryRepo.Select(selfCtx, &model.SttPieceHistoryViewParam{
			SttPieceHistorySttPieceID: dataStt.SttPieceID,
		})
		if err != nil {
			return nil, errQueryDB
		}

		if !params.IsTokopedia {
			sttHistory = c.maskingResonCodeForExternal(selfCtx, sttHistory)
		}

		// Mapping data sttHistory
		listSttPieceHistoryByHistoryID := map[int][]model.SttPieceHistory{}
		for _, historyData := range sttHistory {
			listSttPieceHistoryByHistoryID[historyData.HistoryID] = append(listSttPieceHistoryByHistoryID[historyData.HistoryID], historyData)
		}

		bkdDate := ""
		internalCodeCache := new(sync.Map)
		histories := make([]gatewaySttTracking.SttHistoryV4, 0)
		for i, val := range sttHistory {
			// parsing history remark
			historyRemark := val.RemarkPieceHistoryToStruct()

			countryNameHistory := ""
			countryCode := ""
			timeZoneCity := ""
			if val.HistoryLocation != "" {
				city, err := c.cityRepo.Get(ctx, val.HistoryLocation, params.Token)
				if err != nil || city == nil {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Failed to get location city data ",
						"id": "Gagal mendapatkan data kota lokasi",
					})
				}

				countryCode = city.Country.Code
				timeZoneCity = city.Timezone
				countryNameHistory = city.Country.Name
				if historyRemark == nil {
					historyRemark = &model.RemarkPieceHistory{
						HistoryLocationName: city.Name,
					}
				} else {
					if historyRemark.HistoryLocationName == "" {
						historyRemark.HistoryLocationName = city.Name
					}
				}
				if historyRemark.ActorExternalCode == model.LuwjistikName {
					if model.IsGetCityFromHistoryLocation[val.HistoryStatus] {
						historyRemark.HistoryLocationName = city.Name
					}
				}
			}

			if val.HistoryStatus == model.DEL && val.HistoryActorID > 0 {
				if historyRemark.ActorExternalCode == `` {
					partner, err := c.partnerRepo.GetByID(selfCtx, val.HistoryActorID, params.Token)
					if err != nil || partner == nil {
						return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
							"en": "Failed to get partner data ",
							"id": "Gagal mendapatkan data partner",
						})
					}
					historyRemark.ActorExternalCode = partner.Data.PartnerExternalCode
				}
			}

			// Checking status allow to be show
			if model.SttStatusForSttTrackingClient[val.HistoryStatus] == "" {
				continue
			}

			// get bkd date
			if val.HistoryStatus == model.BKD {
				bkdDate = val.HistoryCreatedAt.Format(shared.FormatDateTime)
			}

			internalCodeCache.Store("status", val.HistoryStatus)
			historyData := gatewaySttTracking.SttHistoryV4{
				Row:         i + 1,
				StatusCode:  strings.ToUpper(val.HistoryStatus),
				Location:    strings.ToUpper(val.HistoryLocation),
				City:        strings.ToUpper(historyRemark.HistoryLocationName),
				Remarks:     c.generateSttTrackingDescriptionForClient(val.HistoryStatus, val.HistoryID, listSttPieceHistoryByHistoryID, params.Token, productType, countryName, params.ReferenceType, countryNameHistory),
				ShortStatus: model.SttStatusForSttTrackingClient[val.HistoryStatus],
				Datetime:    val.HistoryCreatedAt.UTC(),
				UpdatedBy:   strings.ToUpper(val.HistoryCreatedName),
				UpdatedOn:   val.HistoryCreatedAt.UTC(),
				CountryCode: countryCode,
				GMT:         timeZoneCity,
				Attachment: func() []string {
					if historyRemark != nil {
						return historyRemark.GetAllAttachments()
					}
					return []string{}
				}(),
				ActorRole:         val.HistoryActorRole,
				ProblemReasonCode: c.getReasonCode(selfCtx, &val, sttNo),
			}

			historyData.Remarks = c.maskingDescriptionDex(historyData.ProblemReasonCode, historyData.Remarks)

			histories = append(histories, historyData)

		}

		sttMeta := dataStt.SttMetaToStruct()
		if sttMeta == nil {
			var originCity *model.City
			if cityMapping[dataStt.SttOriginCityID] == nil {
				originCity, err := c.cityRepo.Get(selfCtx, dataStt.SttOriginCityID, params.Token)
				if err != nil || originCity == nil {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Failed to get origin city data ",
						"id": "Gagal mendapatkan data kota asal",
					})
				}
				cityMapping[dataStt.SttOriginCityID] = originCity
			}
			originCity = cityMapping[dataStt.SttOriginCityID]

			var destinationCity *model.City
			if cityMapping[dataStt.SttDestinationCityID] == nil {
				destinationCity, err := c.cityRepo.Get(selfCtx, dataStt.SttDestinationCityID, params.Token)
				if err != nil || destinationCity == nil {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Failed to get destination city data ",
						"id": "Gagal mendapatkan data kota tujuan",
					})
				}
				cityMapping[dataStt.SttDestinationCityID] = destinationCity
			}
			destinationCity = cityMapping[dataStt.SttDestinationCityID]

			// parsing stt remark
			sttMeta = &model.SttMeta{
				OriginCityName:      originCity.Name,
				DestinationCityName: destinationCity.Name,
			}
		}

		// Get estimate sla
		estimateSla := `-`
		if sttMeta != nil {
			estimateSla = sttMeta.EstimateSLA
		}
		// Trimming estimateSla string format
		estimateSlaMin := ""
		estimateSlaMax := ""
		trimEstimateSlString := strings.Replace(estimateSla, " ", "", -1)
		trimEstimateSlString = strings.TrimSpace(trimEstimateSlString)
		splitEstimateSlaTrim := strings.Split(trimEstimateSlString, "-")
		if len(splitEstimateSlaTrim) > 1 {
			estimateSlaMin = splitEstimateSlaTrim[0]
			estimateSlaMax = strings.Replace(splitEstimateSlaTrim[1], "Hari", "", -1)
		}

		/*
		 * Checking rebuttal dex status
		 */
		rebuttalDex := ""
		if dataStt.SttLastStatusID == model.DEX {
			dataRebuttalDex, err := c.rebuttalDexRepo.GetBySttNo(ctx, dataStt.SttNo)
			if err == nil && dataRebuttalDex != nil && dataRebuttalDex.Status != "" {
				rebuttalDex = model.Rebuttal
			}
		}

		var sttNoRef string
		if model.IsSttReturnForRtsRtshq[dataStt.SttLastStatusID] {
			dataSttRef, err := c.sttPiecesRepo.GetDetail(ctx, &model.SttViewParams{
				SttNoRefExternal: dataStt.SttNo,
			})
			if err != nil {
				return nil, errQueryDB
			}
			if dataSttRef != nil {
				sttNoRef = dataSttRef.SttNo
			}
		}
		prefix := shared.GetPrefixSttNo(dataStt.SttNo)
		if model.IsPrefixReverseJourney[prefix] {
			sttNoRef = dataStt.SttNoRefExternal
		}

		sttTracking := &gatewaySttTracking.SttTrackingBulkV4{
			SttNo: strings.ToUpper(func() string {
				if dataStt.SttElexysNo.Valid {
					return dataStt.SttElexysNo.Value()
				}
				return dataStt.SttNo
			}()),
			Sender: gatewaySttTracking.PersonObjectV4{
				Name:    strings.ToUpper(dataStt.SttSenderName),
				Address: dataStt.SttSenderAddress,
				Phone:   dataStt.SttSenderPhone,
			},
			Recipient: gatewaySttTracking.PersonObjectV4{
				Name:    strings.ToUpper(dataStt.SttRecipientName),
				Address: dataStt.SttRecipientAddress,
				Phone:   dataStt.SttRecipientPhone,
			},
			Origin:           strings.ToUpper(fmt.Sprintf(`%s (%s)`, sttMeta.OriginCityName, dataStt.SttOriginCityID)),
			Destination:      strings.ToUpper(fmt.Sprintf(`%s (%s)`, sttMeta.DestinationCityName, dataStt.SttDestinationCityID)),
			CurrentStatus:    strings.ToUpper(dataStt.SttLastStatusID),
			ChargeableWeight: dataStt.SttChargeableWeight,
			ServiceType:      model.PACKAGESERVICE,
			ProductType:      dataStt.SttProductType,
			Pieces:           dataStt.SttTotalPiece,
			GrossWeight:      dataStt.SttGrossWeight,
			VolumeWeight:     dataStt.SttVolumeWeight,
			ShipmentID:       dataStt.SttShipmentID,
			TotalAmount:      dataStt.SttTotalAmount,
			CodValue: func() float64 {
				if dataStt.SttIsCOD {
					return dataStt.SttCODAmount
				}
				return 0
			}(),
			ExternalID: func() string {
				if dataStt.SttIsDO {
					return dataStt.SttNoRefExternal
				}
				if dataStt.SttMetaToStruct() != nil {
					if dataStt.SttMetaToStruct().TicketCode != `` {
						return dataStt.SttMetaToStruct().TicketCode
					}
				}
				return ""
			}(),
			History:        histories,
			RebuttalDex:    rebuttalDex,
			SttReturnRefNo: sttNoRef,
		}

		/*
		 * Generate exact date from `estimateSla` string
		 */
		estimateSlaResp, err := c.predefinedHolidayRepo.CheckDate(selfCtx, &model.CredentialRestAPI{
			Token: params.Token,
		}, predefined_holiday.RequestSundayAndHolidayCheck{
			StartDate:   bkdDate,
			Min:         estimateSlaMin,
			Max:         estimateSlaMax,
			ProductType: dataStt.SttProductType,
		})
		if err == nil {
			min, _ := time.Parse(shared.NgenFormatDateTimeZone, estimateSlaResp.Min)
			max, _ := time.Parse(shared.NgenFormatDateTimeZone, estimateSlaResp.Max)
			sttTracking.EstimationDate = fmt.Sprintf("%s - %s", min.Format(shared.FormatDate), max.Format(shared.FormatDate))
		}

		if val, ok := internalCodeCache.Load("status"); ok && val != nil {
			sttTracking.CurrentStatus = val.(string)
		}

		respSttTracking.Stts = append(respSttTracking.Stts, *sttTracking)
	}

	return respSttTracking, nil
}
