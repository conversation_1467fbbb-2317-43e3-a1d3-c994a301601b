package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/abiewardani/dbr/v2"
	"github.com/google/uuid"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/gateway_stt"
	"github.com/Lionparcel/hydra/src/usecase/general"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

// UpdateSTT ..
func (c *sttCtx) UpdateSTT(ctx context.Context, req *stt.UpdateSttRequest) (response *stt.UpdateSttResponse, err error) {
	opName := "UsecaseStt-UpdateSTT"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	response = new(stt.UpdateSttResponse)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": response, "error": err})
	}()

	sttDetail, err := c.updateSttGetSttDetail(selfCtx, req)
	if err != nil {
		return nil, err
	}

	if err = c.updateSttValidateRequest(ctx, req, sttDetail); err != nil {
		return nil, err
	}

	now, _ := shared.ParseUTC7(shared.FormatDateTime, c.timeRepo.Now(time.Time{}).Format(shared.FormatDateTime))
	params, err := c.buildUpdateSttParams(selfCtx, req, sttDetail, now)
	if err != nil {
		return nil, err
	}

	// Check is account is in Limited Assigned 3LC rule
	if err = c.checkIsInsideAssigned3LC(selfCtx, &stt.CreateSttManualForClient{
		AccountType: req.AccountType, AccountRoleName: req.AccountRoleName, AccountID: req.AccountID, Token: req.Token}, params.CityOrigin.Code); err != nil {
		return nil, err
	}

	sttRow := sttDetail[0].Stt
	params.SttBookedBy, params.SttBookedByType = sttRow.SttBookedBy, sttRow.SttBookedByType
	paramsUpdateStt, sttRequestUpdate := c.updateSttBuildRequestSttUpdate(req, sttRow, params, now)
	fn := c.updateSttGetParamsCreditDebit(req, sttRow, sttRequestUpdate)
	params, err = fn(selfCtx, params)
	if err != nil {
		return nil, err
	}
	params.SttUpdatedAt = now
	sttRow.SttBookedBy, sttRow.SttBookedByType = params.SttBookedBy, params.SttBookedByType

	//check stt for assessment
	sttNeedAssessment := c.checkSttNeedAssessment(selfCtx, sttDetail, paramsUpdateStt, req.Token)

	// Set DFOD Inactive period custom flag
	paramsUpdateStt = c.addCustomFlagDfodPastiInactivePeriod(ctx, CheckDFODPastiInactivePeriodParam{&sttRow, &params.CityOrigin, req.Token}, paramsUpdateStt)

	paramsUpdateStt = c.setHistoryRemarks(selfCtx, req, paramsUpdateStt)

	// Update STT
	if err = c.executeUpdateStt(selfCtx, paramsUpdateStt); err != nil {
		return nil, err
	}

	if params.SttStatusAfterAdjusted == model.STTADJUSTEDPOD {
		go c.rtcUc.UpdateInactiveRTCBySttId(context.Background(), int(req.SttID))
	}

	var wg sync.WaitGroup
	wg.Add(1)
	go func() {
		defer wg.Done()
		c.updateSttRecalculateTariffRetail(sttRow, params)
	}()

	// publish message to PubSub
	go func() {
		// count goroutine
		uuid := uuid.New().String()
		shared.IncreaseTotalGoRoutineCount(uuid)
		defer shared.DecreaseTotalGoRoutineCount(uuid)

		c.updateSttPublishMessageSttAdjustment(UpdateSttPublishMessageParams{selfCtx, sttRow, sttRequestUpdate, params, req})
	}()

	// publish message to PubSub Gober
	go func() {
		// count goroutine
		uuid := uuid.New().String()
		shared.IncreaseTotalGoRoutineCount(uuid)
		defer shared.DecreaseTotalGoRoutineCount(uuid)

		c.sttAdjustmentPublishAdjustmentPenalty(req, sttRow, sttRequestUpdate, params)
	}()

	// publish message to PubSub Assessment
	go func() {
		// count goroutine
		uuid := uuid.New().String()
		shared.IncreaseTotalGoRoutineCount(uuid)
		defer shared.DecreaseTotalGoRoutineCount(uuid)

		c.publishSttAssessment(sttRow, paramsUpdateStt, stt.ClientAssessment{
			ClientID:               sttDetail[0].Stt.SttClientID,
			ClientIsNeedAssessment: sttNeedAssessment.IsClientNeedAssessment,
		}, sttNeedAssessment.IsNeedAssessment)
	}()

	//add promo status ant total discount
	response = &stt.UpdateSttResponse{SttID: int(req.SttID)}
	response = setCheckTariffResponse(response, params.CheckTariffResponse)
	// delete cache resi
	c.sttRepo.DeleteCacheResi(selfCtx, int(sttDetail[0].SttID))
	wg.Wait()
	return response, nil
}

func (c *sttCtx) buildUpdateResponse(req *stt.UpdateSttRequest, params stt.UpdateSttParams) *stt.UpdateSttResponse {
	return &stt.UpdateSttResponse{
		SttID:               int(req.SttID),
		IsDiscount:          params.CheckTariffResponse.Data.IsPromo,
		TotalDiscount:       params.CheckTariffResponse.Data.TotalDiscount,
		TotalAfterDiscount:  params.CheckTariffResponse.Data.TotalTariffAfterDiscount,
		TotalBeforeDiscount: params.CheckTariffResponse.Data.TotalTariffBeforeDiscount,
	}
}

func shipmentPrefixAllowEditResult(sttDetail model.SttDetailResult) bool {

	if shared.IsLiloPrefix(sttDetail.SttNoRefExternal) && !shared.IsBagNoReff(sttDetail.SttNoRefExternal) {
		return true
	}

	if len(sttDetail.SttShipmentID) > 4 {
		return model.MappingAllowedEditMapping[shared.GetPrefixShipmentID(sttDetail.SttShipmentID)]
	}

	if model.MappingAllowedEditMapping[shared.GetPrefixSttNo(sttDetail.SttNo)] {
		return true
	}

	return false
}

func (c *sttCtx) updateSttGetSttDetail(ctx context.Context, req *stt.UpdateSttRequest) (sttDetail []model.SttDetailResult, err error) {
	// get detail stt
	sttDetail, err = c.sttRepo.SelectDetail(ctx, int(req.SttID), "", false)
	if err != nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while Querying DB",
			"id": "Terjadi kesalahan pada saat Kueri DB",
		})
	}

	if len(sttDetail) == 0 {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT is not found",
			"id": "STT tidak ditemukan",
		})
	}

	req.SttShipmentID = sttDetail[0].SttShipmentID
	if req.SttShipmentID != `` {
		checkShipmentCAGrossWeight := c.flagManagementRepo.ShipmentCAGrossWeight(ctx, model.ShipmentAccessWeigth{
			ShipmentID:          shared.GetPrefixShipmentID(req.SttShipmentID),
			DestinationCityCode: req.SttDestinationCityID,
			OriginCityCode:      sttDetail[0].SttOriginCityID,
		})

		allowCheckShipmentGrossWeight := req.AccountType != model.INTERNAL && checkShipmentCAGrossWeight
		if allowCheckShipmentGrossWeight {
			reqGrossWeight := req.GetGrossWeightUpdate()
			if reqGrossWeight != sttDetail[0].SttGrossWeight && reqGrossWeight < 10 {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"id": "Berat barang kurang dari 10 kg untuk shipment Customer Apps rute JABODETABEKCIK, tidak boleh diubah.",
					"en": "Gross weight less than 10 kg for shipment Customer Apps route JABODETABEKCIK, are not able to be changed.",
				})
			}
		}
	}

	req.SttPiecesIDNeedToBeArchieved = c.getSttPiecesNeedToBeArchieved(req.SttPieces, sttDetail)

	return sttDetail, nil
}

// delete SttPiece
func (c *sttCtx) getSttPiecesNeedToBeArchieved(newSttPieces []model.SttPiece, sttDetail []model.SttDetailResult) []int64 {
	sttPiecesIDNeedToBeArchieved := []int64{}
	for i := range sttDetail {
		needTobeArchieved := true
		for j := range newSttPieces {
			if sttDetail[i].SttPieceID == newSttPieces[j].SttPieceID {
				needTobeArchieved = false
				break
			}
		}

		if needTobeArchieved {
			sttPiecesIDNeedToBeArchieved = append(sttPiecesIDNeedToBeArchieved, sttDetail[i].SttPieceID)
		}
	}

	return sttPiecesIDNeedToBeArchieved
}

func (c *sttCtx) updateSttCheckAccountRefClient(ctx context.Context, req *stt.UpdateSttRequest, sttRow model.Stt) (err error) {
	opName := "UsecaseStt-updateSttCheckAccountRefClient"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": nil, "error": err})
	}()

	if req.AccountRefType == model.CLIENT {
		clientData, err := c.clientRepo.GetByID(selfCtx, req.AccountRefID, req.Token)
		if err != nil {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Client Not Found",
				"id": "Client Tidak Ditemukan",
			})
		}

		if clientData.Data.ClientParentID == 0 {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Only Client branch can use",
				"id": "Hanya Client branch yang bisa menggunakan",
			})
		}
	}

	if model.IsPrefixReverseJourney[shared.GetPrefixSttNo(sttRow.SttNo)] && req.AccountType == model.CLIENT {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Client cant update STT Booking",
			"id": "Client tidak dapat mengubah STT Booking",
		})
	}
	return nil
}

func (c *sttCtx) updateSttValidateProductType(req *stt.UpdateSttRequest, mapSttPieceID map[int64]bool) (err error) {

	switch req.SttProductType {
	case model.INTERPACK:
		// check if product type interpack

		// Check if there is added or deleted stt piece
		sttPieces := req.SttPieces
		for k, piece := range req.SttPieces {
			switch {
			case mapSttPieceID[piece.SttPieceID]:
				delete(mapSttPieceID, piece.SttPieceID)
				sttPieces = append(sttPieces[0:k], sttPieces[k+1:]...)
			}
		}

		if len(mapSttPieceID) > 0 || len(sttPieces) > 0 {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Cannot add or delete Piece/Koli for Product Interpack",
				"id": "Tidak dapat menambah atau menghapus Piece/Koli untuk Produk Interpack",
			})
		}
	case model.MIXPACK:
		if req.SttIsDO {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Cannot use mixpack product type",
				"id": "Tidak bisa menggunakan product tipe mixpack",
			})
		}
	}

	return nil
}

func (c *sttCtx) updateSttValidateSttLastStatus(ctx context.Context, sttRow model.Stt) (err error) {

	switch sttRow.SttLastStatusID {
	case model.BAGGING:
		sttMeta := sttRow.SttMetaToStruct()
		if sttMeta != nil && sttMeta.IsSttCrossdocking {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "STT bagging LILO cannot to adjust",
				"id": "STT bagging LILO tidak bisa diubah",
			})
		}
	case model.CNXCD:
		// last status CNXCD not allow update
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT CNXCD cannot to adjust",
			"id": "STT CNXCD tidak bisa diubah",
		})

	case model.POD:
		// handle can not adjust shipment ARA, ARB
		return c.updateSttPodValidateShipment(ctx, sttRow)
	case model.REROUTE:
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT reroute cannot to adjust",
			"id": "STT reroute tidak bisa diubah",
		})
	case model.DEL:
		// handle hanging DEL
		return c.updateSttDelValidateHangingDel(ctx, sttRow)
	}
	return nil
}

func (c *sttCtx) updateSttPodValidateShipment(ctx context.Context, sttRow model.Stt) (err error) {
	opName := "UsecaseStt-updateSttPodValidateShipment"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": sttRow, "result": nil, "error": err})
	}()

	sttShipmentID := sttRow.SttShipmentID
	// validate stt origin if stt no is stt reverse
	if len(sttRow.SttNo) >= 2 && model.IsPrefixValidReverseJourney[shared.GetPrefixSttNo(sttRow.SttNo)] {
		sttOrigin, err := c.sttRepo.Get(selfCtx, &model.SttViewDetailParams{
			Stt: model.Stt{SttNo: sttRow.SttNoRefExternal},
		})
		if err != nil {
			return shared.ERR_UNEXPECTED_DB
		}
		if sttOrigin != nil {
			sttShipmentID = sttOrigin.SttShipmentID
		}
	}

	if len(sttShipmentID) >= 4 && model.IsShipmentPrefixEnableCOD[shared.GetPrefixShipmentID(sttShipmentID)] {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT pod with shipment ara/arb cannot to adjust",
			"id": "STT pod dengan shipment ara/arb tidak bisa diubah",
		})
	}
	return nil
}

func (c *sttCtx) updateSttDelValidateHangingDel(ctx context.Context, sttRow model.Stt) (err error) {
	opName := "UsecaseStt-updateSttDelValidateHangingDel"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": sttRow, "result": nil, "error": err})
	}()

	dels, err := c.deliveryRepo.SelectDetail(selfCtx, &model.DeliveryViewParam{
		SttNo:                   sttRow.SttNo,
		OrderBy:                 "delivery.id",
		SortBy:                  model.SortByDesc,
		FinishedStatusWhereNull: true,
	})
	if err != nil {
		return err
	}
	if len(dels) <= 0 {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "STT is not found",
			"id": "STT tidak ditemukan",
		})
	}

	if err = c.deliveryRepo.UpdateHangingDels(selfCtx, dels); err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Delivery Hanging can't be disable",
			"id": "Delivery Hanging tidak bisa di disable",
		})
	}
	return nil
}

func (c *sttCtx) updateSttValidateShipmentPrefix(req *stt.UpdateSttRequest, sttRow model.Stt) (err error) {
	prefixShipment := ``
	if len(sttRow.SttShipmentID) >= 4 {
		prefixShipment = shared.GetPrefixShipmentID(sttRow.SttShipmentID)
	}

	if prefixShipment != model.AO {
		if strings.EqualFold(req.SttSenderAddress, req.SttRecipientAddress) {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Sender and Recepient address cannot be the same",
				"id": "Alamat pengirim dan penerima tidak boleh sama",
			})
		}
	}

	// validation shipment prefix uneditable
	// B1, B2, T1, TKLP
	if model.MappingShipmentDisableEditStt[prefixShipment] {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT is not editable",
			"id": "STT tidak bisa di edit",
		})
	}
	return nil
}

func (c *sttCtx) buildUpdateSttParams(ctx context.Context, req *stt.UpdateSttRequest, sttDetail []model.SttDetailResult, now time.Time) (params stt.UpdateSttParams, err error) {
	opName := "UsecaseStt-buildUpdateSttParams"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": params, "error": err})
	}()

	listFn := []stt.BuildParamsUpdateStt{
		c.updateSttGetSttReverse(sttDetail[0].Stt),
		c.updateSttGetSttPieceHistory(sttDetail[0].Stt),
		c.updateSttValidateAccountType(req, sttDetail[0]),
		c.updateSttGetBookedByActor(req, sttDetail[0].Stt),
		c.updateSttGetCommodityAndProductType(req),
		c.updateSttGetOriginCityDistrict(req, sttDetail[0].Stt),
		c.updateSttGetDestinationCityDistrict(req),
		c.updateSttValidateBookedTypeClient(req, sttDetail[0].Stt),
		c.updateSttGetDataPieceHistoryAdjustment(req, sttDetail, now),
		c.updateSttGetValidProductType(req, sttDetail[0].Stt),
		c.updateSttGetInsuranceType(req, sttDetail[0].Stt),
		c.updateSttBuildReqCheckTariff(req, sttDetail[0].Stt),
		c.updateSttCheckIsElligiblePromo(req, sttDetail[0].Stt),
		c.updateSttGetClientCodConfig(sttDetail[0].Stt),
		c.updateSttGetCodConfigRetail(req, sttDetail[0].Stt),
		c.updateSttGetDfodConfig(req, sttDetail[0].Stt),
		c.updateSttRequestCheckTariff(sttDetail[0].Stt),
		c.sttUpdateCheckIsZeroTariff(sttDetail[0].Stt),
		c.sttUpdateCalculateCodDfodfee(sttDetail[0].Stt),
		c.updateSttBuildSttPieceHistory(req, sttDetail[0]),
		c.updateSttBuildListHistoryStatus(),
		c.updateSttCheckPaymentInvoice(sttDetail[0].Stt),
	}

	// variable for model.STTADJUSTED
	params = stt.UpdateSttParams{
		SttStatusAfterAdjusted: model.STTADJUSTED,
	}

	for _, fn := range listFn {
		params, err = fn(ctx, params)
		if err != nil {
			return params, err
		}
	}
	params.SttAttachFiles = req.SttWeightAttachFiles
	return params, nil
}

func (c *sttCtx) updateSttGetSttReverse(sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		opName := "UsecaseStt-updateSttGetSttReverse"
		trace := tracer.StartTrace(ctx, opName)
		selfCtx := trace.Context()
		var err error
		sttReverse := &model.Stt{}
		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": sttRow, "result": sttReverse, "error": err})
		}()

		sttMeta := sttRow.SttMetaToStruct()
		isSttReverseJourney := sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.ReverseSttNo != ``
		if isSttReverseJourney {
			sttReverse, err = c.sttRepo.Get(selfCtx, &model.SttViewDetailParams{
				Stt: model.Stt{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				},
			})

			if err != nil {
				return params, shared.ERR_UNEXPECTED_DB
			}

			if sttReverse == nil {
				return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "STT reverse is not found",
					"id": "STT reverse tidak ditemukan",
				})
			}

			sttMetaReverse := sttReverse.SttMetaToStruct()
			if sttMetaReverse == nil {
				return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "STT meta reverse is not found",
					"id": "STT meta reverse tidak ditemukan",
				})
			}

			params.SttReverse = *sttReverse
			params.SttMetaReverse = *sttMetaReverse
		}

		return params, nil
	}
}

func (c *sttCtx) updateSttGetSttPieceHistory(sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		opName := "UsecaseStt-updateSttGetSttPieceHistory"
		trace := tracer.StartTrace(ctx, opName)
		selfCtx := trace.Context()
		latestSttHistories := make([]model.SttPieceHistory, 0)
		var err error
		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": sttRow, "result": latestSttHistories, "error": err})
		}()

		sttPiece, err := c.sttPiecesRepo.Get(selfCtx, &model.SttPiecesViewParam{
			SttID: int(sttRow.SttID),
		})
		if err != nil || sttPiece == nil {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt Piece is not found",
				"id": "Stt Piece tidak ditemukan",
			})
		}

		latestSttHistories, err = c.sttPieceHistoryRepo.Select(selfCtx, &model.SttPieceHistoryViewParam{
			Order:                     true,
			OrderDesc:                 true,
			SttPieceHistorySttPieceID: sttPiece.SttPieceID,
		})
		if err != nil || len(latestSttHistories) == 0 {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt History is not found",
				"id": "Stt History tidak ditemukan",
			})
		}
		params.SttPieceHistory = latestSttHistories
		return params, nil
	}
}

func (c *sttCtx) updateSttValidateAccountType(req *stt.UpdateSttRequest, sttDetail model.SttDetailResult) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		opName := "UsecaseStt-updateSttValidateAccountType"
		trace := tracer.StartTrace(ctx, opName)
		selfCtx := trace.Context()
		var err error
		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": sttDetail, "result": params, "error": err})
		}()

		switch req.AccountType {
		case model.CLIENT:
			return c.updateSttValidateAccountClient(selfCtx, req, sttDetail.Stt, params)

		case model.PARTNER:
			return c.updateSttValidateAccountPartner(selfCtx, req, sttDetail, params)

		case model.INTERNAL:
			if model.IsNotAllowToEditInternal[sttDetail.SttLastStatusID] {
				sttMeta := sttDetail.SttMetaToStruct()
				// validation for Stt Adjustment POD
				if c.ValidateIsAllowToEditForSttAdjustmentPOD(ctx, sttDetail, sttMeta) {
					params.SttStatusAfterAdjusted = model.STTADJUSTEDPOD
				} else {
					return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "STT is not found",
						"id": "STT tidak ditemukan",
					})
				}
			}
			return params, nil
		}

		return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Account Type invalid",
			"id": "Tipe akun tidak valid",
		})
	}
}

func (c *sttCtx) updateSttValidateAccountClient(ctx context.Context, req *stt.UpdateSttRequest, sttRow model.Stt, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {

	client, err := c.clientRepo.GetByID(ctx, req.AccountRefID, req.Token)
	if err != nil || client == nil {
		return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Client is not found",
			"id": "Client tidak ditemukan",
		})
	}

	if client.Data.ClientParentID == 0 {
		return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT is not found",
			"id": "STT tidak ditemukan",
		})
	}

	if !model.IsStatusBeforePUP[sttRow.SttLastStatusID] {
		return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT is not found",
			"id": "STT tidak ditemukan",
		})
	}

	switch {
	case sttRow.SttLastStatusID == model.STTADJUSTED:
		for _, history := range params.SttPieceHistory {
			if !model.IsStatusBeforePUP[history.HistoryStatus] {
				return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "STT is not found",
					"id": "STT tidak ditemukan",
				})
			}
		}
	}
	return params, nil
}

func (c *sttCtx) updateSttValidateAccountPartner(ctx context.Context, req *stt.UpdateSttRequest, sttDetail model.SttDetailResult, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {

	isNotAllowEditShipmentMarketplace := sttDetail.SttShipmentID != `` && model.IsShipmentMarketplace[shared.GetPrefixShipmentID(sttDetail.SttShipmentID)] && !shipmentPrefixAllowEditResult(sttDetail)
	if err := c.updateSttValidateAccountPartnerValidatePosConsole(ctx, req, sttDetail); err != nil {
		return params, err
	}

	switch req.AccountRefType {
	case model.POS:
		// POS
		if err := c.updateSttValidateAccountPartnerPOS(req, sttDetail); err != nil {
			return params, err
		}
		return c.updateSttValidateAccountPos(sttDetail, params, isNotAllowEditShipmentMarketplace)

	case model.CONSOLE:
		// CONSOLE
		if sttDetail.SttLastStatusID != model.STI {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "STT is not found",
				"id": "STT tidak ditemukan",
			})
		}

		fn := c.updateSttValidateAccountConsole(req, sttDetail.Stt, isNotAllowEditShipmentMarketplace)
		return fn(ctx, params)
	}

	// IF IS SUBCONSOLE
	return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "STT is not found",
		"id": "STT tidak ditemukan",
	})
}

func (c *sttCtx) updateSttValidateAccountPos(sttDetail model.SttDetailResult, params stt.UpdateSttParams, isNotAllowEditShipmentMarketplace bool) (stt.UpdateSttParams, error) {
	if isNotAllowEditShipmentMarketplace && model.IsNotAllowEditPosForMP[sttDetail.SttLastStatusID] {
		return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "STT is not editable",
			"id": "STT tidak bisa di edit",
		})
	}

	switch {
	case sttDetail.SttLastStatusID == model.STTADJUSTED:
		for _, history := range params.SttPieceHistory {
			switch {
			case !model.IsStatusBeforePUP[history.HistoryStatus]:
				return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "STT is not found",
					"id": "STT tidak ditemukan",
				})

			case model.IsStatusAfterPUP[history.HistoryStatus] && !shipmentPrefixAllowEditResult(sttDetail):
				return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "STT is not editable",
					"id": "STT tidak bisa di edit",
				})
			}
		}
	}
	return params, nil
}

func (c *sttCtx) updateSttValidateAccountConsole(req *stt.UpdateSttRequest, sttRow model.Stt, isNotAllowEditShipmentMarketplace bool) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		if isNotAllowEditShipmentMarketplace {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "STT is not editable",
				"id": "STT tidak bisa di edit",
			})
		}

		if params.SttPieceHistory[0].HistoryActorID != req.AccountRefID && params.SttPieceHistory[0].HistoryStatus == model.STI {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "STT is not found",
				"id": "STT tidak ditemukan",
			})
		}
		return params, nil
	}
}

func (c *sttCtx) updateSttGetBookedByActor(req *stt.UpdateSttRequest, sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		opName := "UsecaseStt-updateSttGetBookedByActor"
		trace := tracer.StartTrace(ctx, opName)
		selfCtx := trace.Context()
		var err error
		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": sttRow, "result": params, "error": err})
		}()

		if sttRow.SttBookedByType == model.POS {
			partnerBooked, err := c.partnerRepo.GetByID(selfCtx, sttRow.SttBookedBy, req.Token)
			if err != nil || partnerBooked == nil {
				return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Failed to retrieve partner booked by",
					"id": "Gagal mendapatkan partner booked by",
				})
			}

			params.BookedByActor = model.Actor{
				ID:   partnerBooked.Data.ID,
				Name: partnerBooked.Data.Name,
				Code: partnerBooked.Data.Code,
				Type: model.POS,
				ActorPosDetail: model.ActorPos{
					PosBranchCommission: partnerBooked.Data.PartnerPosBranchCommission,
					PosParentID:         partnerBooked.Data.PartnerPosParentID,
				},
			}
			params.PartnerPosIsPickup = partnerBooked.Data.PartnerPosIsPickup
		}
		return c.updateSttCheckSttReversePartnerBookedBy(selfCtx, req, params, sttRow)
	}
}

func (c *sttCtx) updateSttCheckSttReversePartnerBookedBy(ctx context.Context, req *stt.UpdateSttRequest, params stt.UpdateSttParams, sttRow model.Stt) (stt.UpdateSttParams, error) {
	opName := "UsecaseStt-updateSttCheckSttReversePartnerBookedBy"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": params, "error": err})
	}()

	sttReverseIsRetail := params.SttReverse.SttBookedByType == model.POS && params.SttReverse.SttBookedForType == model.POS && params.SttReverse.SttShipmentID == ""

	if shared.GetPrefixSttNo(sttRow.SttNo) == model.PrefixAutoReverseJourneyMisBooking && sttReverseIsRetail {
		partnerSttReverseBooked, err := c.partnerRepo.GetByID(selfCtx, params.SttReverse.SttBookedBy, req.Token)
		if err != nil || partnerSttReverseBooked == nil {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to retrieve stt reverse partner booked by",
				"id": "Gagal mendapatkan stt reverse partner booked by",
			})
		}
		params.PartnerPosIsPickup = partnerSttReverseBooked.Data.PartnerPosIsPickup
	}

	return params, nil
}

func (c *sttCtx) updateSttGetCommodityAndProductType(req *stt.UpdateSttRequest) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		opName := "UsecaseStt-updateSttGetBookedByActor"
		trace := tracer.StartTrace(ctx, opName)
		selfCtx := trace.Context()
		var err error
		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": req, "result": params, "error": err})
		}()

		// validate commodity
		commodity, err := c.commodityRepo.GetCommodityByCode(selfCtx, req.SttCommodityCode, req.Token)
		if err != nil {
			return params, shared.ERR_UNEXPECTED_DB
		}
		if commodity == nil {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Commodity Not Found",
				"id": "Komoditas Tidak Ditemukan",
			})
		}

		if commodity.Data.CommodityStatus != model.ACTIVE {
			return params, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Commodity is inactive",
				"id": "Komoditas tidak aktif",
			})
		}

		// validate product type
		productType, err := c.productRepo.GetProductTypes(selfCtx, &model.ListProductTypeRequest{
			Token:  req.Token,
			Code:   req.SttProductType,
			Status: model.ACTIVE,
			Limit:  model.DefaultLimit,
			Page:   model.DefaultPage,
		})
		if err != nil {
			return params, shared.ERR_UNEXPECTED_DB
		}

		if productType == nil || len(productType.Data) < 1 {
			return params, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Product Type is inactive",
				"id": "Product Type tidak aktif",
			})
		}

		params.Commodity = *commodity
		return params, nil
	}
}

func (c *sttCtx) updateSttGetOriginCityDistrict(req *stt.UpdateSttRequest, sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		opName := "UsecaseStt-updateSttGetOriginCityDistrict"
		trace := tracer.StartTrace(ctx, opName)
		selfCtx := trace.Context()
		var err error
		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": req, "result": params, "error": err})
		}()

		// Get Information origin city stt
		cityStt, err := c.cityRepo.Get(selfCtx, sttRow.SttOriginCityID, req.Token)
		if err != nil {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while querying DB",
				"id": "Terjadi kesalahan pada saat querying DB",
			})
		}
		if cityStt == nil {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "City Not Found",
				"id": "Kota tidak ditemukan",
			})
		}

		// get detail origin district
		districtOrigin, err := c.districtRepo.GetByCode(selfCtx, &model.CredentialRestAPI{
			Token: req.Token,
		}, sttRow.SttOriginDistrictID)

		if err != nil || districtOrigin == nil {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Origin District Not Found",
				"id": "Ditrict Asal Tidak Ditemukan",
			})
		}

		params.CityOrigin = *cityStt
		params.DistrictOrigin = *districtOrigin
		return params, nil
	}
}

func (c *sttCtx) updateSttGetDestinationCityDistrict(req *stt.UpdateSttRequest) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		opName := "UsecaseStt-updateSttGetDestinationCityDistrict"
		trace := tracer.StartTrace(ctx, opName)
		selfCtx := trace.Context()
		var err error
		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": req, "result": params, "error": err})
		}()

		// get detail destination city
		destinationCity, err := c.cityRepo.Get(selfCtx, req.SttDestinationCityID, req.Token)
		if err != nil || destinationCity == nil {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Destination City Not Found",
				"id": "Kota Tujuan Tidak Ditemukan",
			})
		}

		// get detail district request
		districtDestination, err := c.districtRepo.GetByCode(selfCtx, &model.CredentialRestAPI{
			Token: req.Token,
		}, req.SttDestinationDistrictID)
		if err != nil || districtDestination == nil {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Destination District Not Found",
				"id": "Ditrict Tujuan Tidak Ditemukan",
			})
		}

		err = c.updateSttValidateDestinationCityDistrict(req, destinationCity, districtDestination)
		if err != nil {
			return params, err
		}

		params.CityDestination = *destinationCity
		params.DistrictDestination = *districtDestination
		return params, nil
	}
}

func (c *sttCtx) updateSttValidateDestinationCityDistrict(req *stt.UpdateSttRequest, destinationCity *model.City, districtDestination *model.District) error {

	if districtDestination.Data.City.Code != req.SttDestinationCityID {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Destination City Not Found",
			"id": "Kota Tujuan Tidak Ditemukan",
		})
	}

	// validate city and district status
	if strings.ToLower(districtDestination.Data.Status) != model.ACTIVE || strings.ToLower(destinationCity.IsActive) != model.ACTIVE {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Destination City Or District Inactive",
			"id": "Kota atau Ditrict Tujuan Tidak Aktif",
		})
	}

	if len(req.SttPieces) > 1 && districtDestination.IsValidDistrictVendor() {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "District destination type doesn't support delivery item that have more than 1 koli wihtin 1 STT",
			"id": "Tipe district destinasi tidak mendukung pengiriman barang yang memiliki lebih dari 1 koli dalam satu STT",
		})
	}
	return nil
}

func (c *sttCtx) updateSttValidateBookedTypeClient(req *stt.UpdateSttRequest, sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		opName := "UsecaseStt-updateSttValidateBookedTypeClient"
		trace := tracer.StartTrace(ctx, opName)
		selfCtx := trace.Context()
		var err error
		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": req, "result": params, "error": err})
		}()

		// if booked for Client
		if sttRow.SttBookedForType != model.CLIENT {
			return params, nil
		}
		client, err := c.clientRepo.GetByID(selfCtx, sttRow.SttBookedForID, req.Token)
		if err != nil {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Client Not Found",
				"id": "Client Tidak Ditemukan",
			})
		}

		// validation COD and booked for Client
		if sttRow.SttIsCOD {
			// client isCOD == false
			if !client.Data.ClientIsCOD {
				return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Stt cannot be booked. Please check is the client allowed for COD services",
					"id": "Stt tidak bisa dibooking. Mohon cek apakah client yang dipilih memperbolehkan layanan COD",
				})
			}

			// district isCOD == no
			if params.DistrictDestination.Data.IsCod == model.DISTRICT_IS_NOT_COD {
				return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Stt cannot be booked. Please check is the destination allowed for COD services",
					"id": "Stt tidak bisa dibooking. Mohon cek apakah destinasi yang dipilih memperbolehkan layanan COD",
				})
			}
		}
		return params, nil
	}
}

func (c *sttCtx) updateSttGetDataPieceHistoryAdjustment(req *stt.UpdateSttRequest, sttDetail []model.SttDetailResult, now time.Time) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		opName := "UsecaseStt-updateSttGetDataPieceHistoryAdjustment"
		trace := tracer.StartTrace(ctx, opName)
		selfCtx := trace.Context()
		var err error
		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": req, "result": params, "error": err})
		}()

		// set calculate tarif piece param
		for _, valPiece := range req.SttPieces {
			sttPieceAdjustment := valPiece.BuildSttPieceAdjustment(valPiece)
			if valPiece.SttPieceID != 0 {
				// get is detailPiece exist
				detailPiece, err := c.sttPiecesRepo.Get(selfCtx, &model.SttPiecesViewParam{
					SttID:      int(req.SttID),
					SttPieceID: int(valPiece.SttPieceID),
				})
				if err != nil {
					return params, shared.ERR_UNEXPECTED_DB
				}

				if detailPiece == nil {
					return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "STT Piece Not Found",
						"id": "STT Piece Tidak Ditemukan",
					})
				}

				sttPieceAdjustment = valPiece.BuildSttPieceAdjustment(*detailPiece)
			}

			calculateTarifPiece := stt.CalculateTariffPieces{
				PieceLength:      valPiece.SttPieceLength,
				PieceWidth:       valPiece.SttPieceWidth,
				PieceHeight:      valPiece.SttPieceHeight,
				PieceGrossWeight: valPiece.SttPieceGrossWeight,
			}
			params.CalculateTarifPieceArray = append(params.CalculateTarifPieceArray, calculateTarifPiece)

			sttPieceHistoryAdjustment := model.SttPieceHistoryAdjustment{
				SttPieceAdjustment: sttPieceAdjustment,
				SttPieceHistory: model.SttPieceHistory{
					SttPieceID:         valPiece.SttPieceID, // can be 0 for add stt piece, use index instead
					HistoryStatus:      params.SttStatusAfterAdjusted,
					HistoryLocation:    sttDetail[0].SttOriginCityID,
					HistoryActorName:   req.AccountRefName,
					HistoryActorRole:   req.AccountRefType,
					HistoryActorID:     req.AccountRefID,
					HistoryCreatedAt:   now,
					HistoryCreatedBy:   int(req.AccountID),
					HistoryCreatedName: req.AccountName,
				},
			}
			params.SttPieceHistoryAdjustmentArray = append(params.SttPieceHistoryAdjustmentArray, sttPieceHistoryAdjustment)
		}
		return params, nil
	}
}

func (c *sttCtx) updateSttGetValidProductType(req *stt.UpdateSttRequest, sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {

		// if stt booked by client
		params.AccountType = ``
		params.AccountRefID = 0
		if sttRow.SttClientID > 0 {
			params.SttProductType = sttRow.SttProductType
			params.AccountType = model.CLIENT
			params.AccountRefID = sttRow.SttClientID
			if req.AccountType == model.INTERNAL && params.SttStatusAfterAdjusted == model.STTADJUSTEDPOD {
				params.SttProductType = req.SttProductType
			}
		} else {
			params.SttProductType = req.SttProductType
			params.AccountType = model.PARTNERPOS
			params.AccountRefID = req.AccountRefID
		}

		sttMeta := sttRow.SttMetaToStruct()
		isSttReverseJourney := sttMeta != nil && sttMeta.DetailSttReverseJourney != nil
		if model.IsPrefixValidReverseJourney[sttRow.SttNo[:2]] && isSttReverseJourney {
			params.SttProductType = req.SttProductType
		}
		return params, nil
	}
}

func (c *sttCtx) updateSttGetInsuranceType(req *stt.UpdateSttRequest, sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		var err error
		isAllowEdit := req.AccountType == model.INTERNAL && sttRow.SttBookedForType == model.CLIENT && model.IsAllowToEditInternalPOD[sttRow.SttLastStatusID]
		if isAllowEdit {
			params.InsuranceType = req.SttInsuranceType
		} else {
			params, err = c.updateSttGetDataOptionalRate(ctx, sttRow, params)
			if err != nil {
				return params, nil
			}
		}

		switch {
		case !req.SttIsWoodpacking:
			if params.IsWoodpacking {
				return params, shared.NewMultiStringValidationError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Its not permitted to remove the wooden packing",
					"id": "Tidak diizinkan untuk meghilangkan packing kayu",
				})
			}
		default:
			params.IsWoodpacking = true
		}

		err = c.updateSttValidateGoosEstimatePrice(req, sttRow, params.InsuranceType)
		return params, err
	}
}

func (c *sttCtx) updateSttGetDataOptionalRate(ctx context.Context, sttRow model.Stt, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
	opName := "UsecaseStt-updateSttGetDataOptionalRate"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": sttRow, "result": params, "error": err})
	}()

	sttOptionalRateData, err := c.sttOptionalRateRepo.Select(selfCtx, &model.SttOptionalRate{SttOptionalRateSttID: sttRow.SttID})
	if err != nil {
		return params, shared.ERR_UNEXPECTED_DB
	}

	if len(sttOptionalRateData) == 0 {
		return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt optional cannot found",
			"id": "Stt optional tidak ditemukan",
		})
	}

	mapInsuranceType := map[string]string{
		model.INSURANCEFREENAME:    model.INSURANCEFREE,
		model.INSURANCEBASICNAME:   model.INSURANCEBASIC,
		model.INSURANCEPREMIUMNAME: model.INSURANCEPREMIUM,
	}
	for _, val := range sttOptionalRateData {
		switch val.SttOptionalRateParams {
		case model.WOODPACKING:
			params.IsWoodpacking = true
		case model.INSURANCE:
			if _, ok := mapInsuranceType[val.SttOptionalRateName]; ok {
				params.InsuranceType = mapInsuranceType[val.SttOptionalRateName]
			}
		}
	}

	return params, nil

}

func (c *sttCtx) updateSttValidateGoosEstimatePrice(req *stt.UpdateSttRequest, sttRow model.Stt, insuranceType string) error {
	sttMeta := sttRow.SttMetaToStruct()
	isSttReverseJourney := sttMeta != nil && sttMeta.DetailSttReverseJourney != nil
	prefixShipment := ``
	enableMinGoodsPrice := false
	var minGoodsPrice float64

	shipmentIDNotEmpty := sttRow.SttShipmentID != ``
	switch shipmentIDNotEmpty {
	case true:
		enableMinGoodsPrice = insuranceType != model.INSURANCEFREE
		prefixShipment = shared.GetPrefixShipmentID(sttRow.SttShipmentID)
	default:
		switch isSttReverseJourney {
		case true:
			enableMinGoodsPrice = sttMeta.DetailSttReverseJourney.ReverseSttNo != `` && insuranceType != model.INSURANCEFREE
		default:
			enableMinGoodsPrice = true
		}
	}

	isDfodCa := c.checkIsDfodCA(sttRow, prefixShipment)
	if enableMinGoodsPrice && !isDfodCa {
		minGoodsPrice = model.MIN_GOODS_PRICE
	}

	// check jika booking manual, booking algo, dan stt reverse journey tidak memakai insurance free maka goods value mandatory
	if req.SttGoodsEstimatePrice < minGoodsPrice {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Goods Estimate Price must be higher than 0",
			"id": "Harga Perkiraan Barang harus lebih besar dari 0",
		})
	}
	return nil
}

func (c *sttCtx) checkIsDfodCA(sttRow model.Stt, prefixShipment string) (isDfodCA bool) {
	sttMeta := sttRow.SttMetaToStruct()
	isSttReverseJourney := sttMeta != nil && sttMeta.DetailSttReverseJourney != nil
	if sttRow.SttIsDFOD {
		isDfodCA = (isSttReverseJourney && sttMeta.DetailSttReverseJourney.ReverseShipmentID != `` && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.ReverseShipmentID)]) || model.MappingShipmentPrefixCODCustomerAppsRetail[prefixShipment]
	}
	return isDfodCA
}

func (c *sttCtx) updateSttBuildReqCheckTariff(req *stt.UpdateSttRequest, sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		var err error
		var shipmentPrefix string

		_, shipmentPrefix, _ = c.CheckSTTFromCARetailFromRoot(ctx, sttRow.SttMetaToStruct())
		if len(shipmentPrefix) == 0 {
			shipmentPrefix = shared.GetPrefixFromShipmentOrNoRefExt(sttRow.SttShipmentID, sttRow.SttNoRefExternal)
		}
		// get check tarif
		reqCheckTariff := stt.CalculateTariffParams{
			RequestCalculateTariff: &stt.RequestCalculateTariff{
				OriginID:         sttRow.SttOriginDistrictID,
				DestinationID:    req.SttDestinationDistrictID,
				ProductType:      params.SttProductType,
				CommodityID:      params.Commodity.Data.CommodityID,
				InsuranceType:    params.InsuranceType,
				GoodsPrice:       req.SttGoodsEstimatePrice,
				IsWoodpacking:    params.IsWoodpacking,
				AccountType:      params.AccountType,
				AccountRefID:     params.AccountRefID,
				IsHaveTaxID:      false,
				Pieces:           params.CalculateTarifPieceArray,
				IsTariffNotRound: sttRow.SttIsCOD, // total tarif not rounded, cod_amount & cod_fee rounded
				ShipmentPrefix:   shipmentPrefix,
			},
			Token: req.Token,
		}
		params.ReqCheckTariff = reqCheckTariff
		if sttRow.SttShipmentID == `` {
			return params, nil
		}
		prefixShipment := shared.GetPrefixShipmentID(sttRow.SttShipmentID)

		if prefixShipment == model.CP {

			return params, nil
		}
		// if tarif get from algo, should be use client tariff BL/TP/CA (shipment AP & AS prefix using client tarif)
		switch prefixShipment {
		case model.C1, model.C2:
			params.ReqCheckTariff.RequestCalculateTariff.AccountType = model.CLIENT
			params.ReqCheckTariff.RequestCalculateTariff.AccountRefID = sttRow.SttBookedForID
			params.CodHandling, err = c.updateSttGetCodHandling(ctx, sttRow)
			if err != nil {
				return params, err
			}
			params.ReqCheckTariff.RequestCalculateTariff.CodHandling = params.CodHandling
		default:
			fn := c.updateSttCheckShipmentClientCode(req, sttRow, prefixShipment)
			params, err = fn(ctx, params)
			if err != nil {
				return params, err
			}
		}

		sttMeta := sttRow.SttMetaToStruct()
		isSttReverseJourney := sttMeta != nil && sttMeta.DetailSttReverseJourney != nil
		// handle tariff for stt reverse
		if isSttReverseJourney && model.IsSttStatusReturnToSender[sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt] {
			params.ReqCheckTariff.RequestCalculateTariff.AccountType = sttRow.SttBookedForType
			params.ReqCheckTariff.RequestCalculateTariff.AccountRefID = sttRow.SttBookedForID
		}
		params.ReqCheckTariff.RequestCalculateTariff.IsHaveTaxID = req.SttTaxNumber != ``
		params.IsHaveTaxNumber = req.SttTaxNumber != ``

		return params, nil
	}
}

func (c *sttCtx) updateSttGetCodHandling(ctx context.Context, sttRow model.Stt) (codHandling string, err error) {
	opName := "UsecaseStt-updateSttGetCodHandling"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": sttRow, "result": codHandling, "error": err})
	}()

	if !sttRow.SttIsCOD {
		return
	}
	shipment, err := c.shipmentRepo.Get(selfCtx, &model.ShipmentViewParams{
		ShipmentAlgoID: sttRow.SttShipmentID,
	})
	if err != nil || shipment == nil {
		return codHandling, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Shipment Not Found",
			"id": "Shipment Tidak Ditemukan",
		})
	}

	// Set cod_handling
	shipmentMeta := shipment.ShipmentMetaToStruct()
	if shipmentMeta != nil {
		switch shipmentMeta.CodHandling {
		case model.SPECIALCOD:
			codHandling = model.SPECIALCOD
		default:
			codHandling = model.STANDARDCOD
		}
	}
	return codHandling, nil
}

func (c *sttCtx) updateSttCheckShipmentClientCode(req *stt.UpdateSttRequest, sttRow model.Stt, prefixShipment string) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		opName := "UsecaseStt-updateSttCheckShipmentClientCode"
		trace := tracer.StartTrace(ctx, opName)
		selfCtx := trace.Context()
		var err error
		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": sttRow, "result": params, "error": err})
		}()

		clientCode := model.MappingShipmentPrefixClientCode[prefixShipment]
		client, err := c.clientRepo.GetByCode(selfCtx, clientCode, req.Token)
		if err != nil || client == nil {
			return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Client Not Found",
				"id": "Client Tidak Ditemukan",
			})
		}

		// set check tariff should be get from client
		params.ReqCheckTariff.RequestCalculateTariff.AccountType = model.CLIENT
		params.ReqCheckTariff.RequestCalculateTariff.AccountRefID = client.Data.ClientID

		if client.Data.ClientIsDO {
			sttRow.SttIsDO = client.Data.ClientIsDO

			// prefix DO, client data ClientIsDO true and ClientDOPaymentType free so set SttTotalAmount = 0
			if prefixShipment == model.DO && client.Data.ClientDOPaymentType == model.FREE {
				params.ClientIsDOPaymentTypeFree = true
				sttRow.SttTotalAmount = 0
			}
		}
		return params, nil
	}
}

func (c *sttCtx) updateSttCheckIsElligiblePromo(req *stt.UpdateSttRequest, sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {

		isDisablePromo := !c.checkCalculateTariffPromoElligible(sttRow.SttBookedByType, sttRow.SttBookedForType, req.Source)
		promoAppliedTo := ""
		prefixShipment := ``
		if sttRow.SttShipmentID != `` {
			prefixShipment = shared.GetPrefixShipmentID(sttRow.SttShipmentID)
		}

		isShipmentCodCaRetail := false
		if sttRow.SttIsCOD {
			params.IsPrefixCODCARetail = model.MappingShipmentPrefixCODCustomerAppsRetail[prefixShipment]
			params.IsPrefixShipmentFavCOD = model.IsShipmentFavorite[prefixShipment]

			isShipmentCodCaRetail = model.IsShipmentPrefixEnableCOD[prefixShipment]
		}

		if c.cfg.EnablePromoBookingShipment() {
			// if prefix  AG / AI / AD / ACA / AO / ACB / AP / AS, should be disable promo set false (Promo Discount Config)
			// if prefix ARA / ARB applied to shipment, should be disable promo set false
			if isShipmentCodCaRetail || model.PrefixShipmentIDIsAllowdPromoDiscont[prefixShipment] {
				isDisablePromo = false
				promoAppliedTo = model.PromoAppliedToShipment
			}
		}

		sttMeta := sttRow.SttMetaToStruct()
		if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil {
			isDisablePromo = true
		}
		params.IsDisablePromo = isDisablePromo
		params.ReqCheckTariff.RequestCalculateTariff.IsDisablePromo = isDisablePromo
		params.ReqCheckTariff.RequestCalculateTariff.PromoAppliedTo = promoAppliedTo
		params.ReqCheckTariff.RequestCalculateTariff.ShipmentPrefix = prefixShipment

		return params, nil
	}
}

func (c *sttCtx) updateSttGetClientCodConfig(sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {

		checkEmptyString := func(s1, s2 string) string {
			if s1 == "" {
				return s2
			}
			return s1
		}

		checkEmptyFloat := func(f1, f2 float64) float64 {
			if f1 == 0 {
				return f2
			}
			return f1
		}

		sttMeta := sttRow.SttMetaToStruct()
		if sttMeta != nil {
			if sttRow.SttIsCOD && sttRow.SttBookedForType == model.CLIENT {
				params.ClientPaymentMethod = checkEmptyString(sttMeta.ClientPaymentMethod, model.Invoice)
				params.ClientCodConfigAmount = checkEmptyString(sttMeta.ClientCodConfigAmount, model.GoodsPrice)
				params.RateVatShipment = checkEmptyFloat(sttMeta.RateVatShipment, c.cfg.RateVatShipment())
				params.ClientCodShipmentDiscount = sttMeta.ClientCodShipmentDiscount
				params.RateVatCod = checkEmptyFloat(sttMeta.RateVatCod, c.cfg.RateVatCod())
			}
			params.IsDFOD = sttRow.SttIsDFOD && sttMeta.DfodConfiguration != nil
		}

		return params, nil
	}
}

func (c *sttCtx) updateSttGetCodConfigRetail(req *stt.UpdateSttRequest, sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {

		sttMeta := sttRow.SttMetaToStruct()
		if sttMeta == nil {
			return params, nil
		}
		isSttCod := (sttRow.IsSttCODRetail() && params.SttStatusAfterAdjusted != model.STTADJUSTEDPOD) || params.IsPrefixCODCARetail || params.IsPrefixShipmentFavCOD || params.CodHandling == model.SPECIALCOD
		if isSttCod {
			fn := c.updateSttGetTariffReturn(req, sttRow, sttMeta)
			return fn(ctx, params)
		}
		return params, nil
	}
}

func (c *sttCtx) updateSttGetTariffReturn(req *stt.UpdateSttRequest, sttRow model.Stt, sttMeta *model.SttMeta) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		opName := "UsecaseStt-updateSttGetTariffReturn"
		trace := tracer.StartTrace(ctx, opName)
		selfCtx := trace.Context()
		var err error
		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": sttRow, "result": params, "error": err})
		}()

		if sttMeta.DetailSttReverseJourney == nil {
			// check tariff return
			reqCalcTariff := *params.ReqCheckTariff.RequestCalculateTariff
			reqCalcTariff.IsDisablePromo = true
			reqCalcTariff.IsCod = false
			reqCalcTariff.OriginID = params.ReqCheckTariff.RequestCalculateTariff.DestinationID
			reqCalcTariff.DestinationID = params.ReqCheckTariff.RequestCalculateTariff.OriginID
			reqCheckTariffReturn := &stt.CalculateTariffParams{
				RequestCalculateTariff: &reqCalcTariff,
				Token:                  req.Token,
			}
			checkTariffReturn, err := c.checkTariffRepo.TariffCalculation(selfCtx, reqCheckTariffReturn)
			if err != nil || checkTariffReturn == nil {
				return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Check tarif return is not found",
					"id": "Cek tarif return tidak ditemukan",
				})
			}

			params.TotalTariffReturn = checkTariffReturn.Data.TotalTariff
			params.IsCalcTariffReturn = true
		}

		codConfig := sttMeta.CodRetailDetail
		if codConfig == nil {
			codConfig = sttMeta.CodConfiguration
		}
		if codConfig != nil {
			params.CodAmountConfigType = codConfig.CodAmountConfigType
			params.PercentageCodFee = codConfig.PercentageCodFee
			params.MinCodFee = codConfig.MinCodFee
			params.IsTopupCA = true
			if params.CodAmountConfigType == model.GoodsPriceTotalTarif {
				params.ReqCheckTariff.RequestCalculateTariff.IsWithoutCodConfiguration = true
				params.ReqCheckTariff.RequestCalculateTariff.CodPercentageRetail = params.PercentageCodFee
				params.ReqCheckTariff.RequestCalculateTariff.MinCodValueRetail = params.MinCodFee
				params.ReqCheckTariff.RequestCalculateTariff.CodTypeRetail = params.CodAmountConfigType
				params.ReqCheckTariff.RequestCalculateTariff.IsCod = true
				params.ReqCheckTariff.RequestCalculateTariff.CodAmount = req.SttGoodsEstimatePrice
			}
		}
		return params, nil
	}
}

func (c *sttCtx) updateSttGetDfodConfig(req *stt.UpdateSttRequest, sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		isSttRetail := sttRow.SttBookedForType == model.POS && sttRow.SttBookedByType == model.POS
		if !params.ReqCheckTariff.RequestCalculateTariff.IsDisablePromo && isSttRetail {
			params.ReqCheckTariff.RequestCalculateTariff.PromoAppliedTo = model.PromoAppliedToRetail
		}

		// calculate discount shipment favorite COD/DFOD
		if params.IsPrefixShipmentFavCOD && req.DiscountFavoritePercentage > 0 {
			params.ReqCheckTariff.RequestCalculateTariff.DiscountFavoritePercentage = req.DiscountFavoritePercentage
		}

		sttMeta := sttRow.SttMetaToStruct()
		if params.IsDFOD && sttMeta != nil {
			params.PercentageCodFee = sttMeta.DfodConfiguration.PercentageCodFee
			params.MinCodFee = sttMeta.DfodConfiguration.MinCodFee

			params.IsTopupCA = false

			params.ReqCheckTariff.RequestCalculateTariff.IsCod = true
			params.ReqCheckTariff.RequestCalculateTariff.IsDfod = true
			params.ReqCheckTariff.RequestCalculateTariff.IsWithoutCodConfiguration = true
			params.ReqCheckTariff.RequestCalculateTariff.CodPercentageRetail = sttMeta.DfodConfiguration.PercentageCodFee
			params.ReqCheckTariff.RequestCalculateTariff.MinCodValueRetail = sttMeta.DfodConfiguration.MinCodFee
			params.ReqCheckTariff.RequestCalculateTariff.CodTypeRetail = params.CodAmountConfigType
		}
		return params, nil
	}
}

func (c *sttCtx) updateSttRequestCheckTariff(sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		opName := "UsecaseStt-updateSttRequestCheckTariff"
		trace := tracer.StartTrace(ctx, opName)
		selfCtx := trace.Context()
		var err error
		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": sttRow, "result": params, "error": err})
		}()

		isFailed := func(v interface{}, err error) bool {
			return err != nil || shared.IsEmpty(v)
		}
		checkTariff, err := c.checkTariffRepo.TariffCalculation(selfCtx, &params.ReqCheckTariff)
		if isFailed(checkTariff, err) {

			sttMeta := sttRow.SttMetaToStruct()
			/**
			* check tariff for stt reverse journey will be done 2 times (client tariff and then retail tariff), ONLY IF
			* booked for is client and tariff for route is not available
			 */
			if !sttMeta.IsValidSttReverseJourney() {
				return params, err
			}

			if !strings.Contains(err.Error(), shared.ErrPriceSelectedRouteNotAvailable) {
				return params, err
			}

			if params.ReqCheckTariff.RequestCalculateTariff.AccountType == model.POS {
				return params, err
			}

			params.ReqCheckTariff.RequestCalculateTariff.AccountType = model.POS
			params.ReqCheckTariff.RequestCalculateTariff.AccountRefID = sttRow.SttBookedBy
			checkTariff, err = c.checkTariffRepo.TariffCalculation(selfCtx, &params.ReqCheckTariff)
			if isFailed(checkTariff, err) {
				return params, err
			}
		}
		params.CheckTariffResponse = *checkTariff
		return params, nil
	}
}

func (c *sttCtx) sttUpdateCheckIsZeroTariff(sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		var (
			isReverseJourneyRTS           bool
			isSttReverseJourneyZeroTariff bool
		)

		codHandling := ``
		rootSttShipmentID := ``
		sttMeta := sttRow.SttMetaToStruct()
		if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil {
			isSttReverseJourneyZeroTariff = model.IsSttStatusReverseJourneyZeroTariff[sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt]
			isReverseJourneyRTS = sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt == model.RTS
			rootSttShipmentID = sttMeta.DetailSttReverseJourney.RootReverseShipmentID
			codHandling = sttMeta.DetailSttReverseJourney.RootReverseCodHandling
		}

		isReverseFromSttCod := c.checkIsReverseFromCod(params.SttReverse, rootSttShipmentID, codHandling)

		isReverseFromRTSHQ := params.SttMetaReverse.DetailSttReverseJourney != nil && params.SttMetaReverse.DetailSttReverseJourney.ReverseJourneyStatusStt == model.RTSHQ

		isAdjustmentWithZeroTariff := isSttReverseJourneyZeroTariff || isReverseFromRTSHQ || (isReverseJourneyRTS && isReverseFromSttCod)

		if isAdjustmentWithZeroTariff {
			params.CheckTariffResponse.Data.CityRates = 0
			params.CheckTariffResponse.Data.ForwardRates = 0
			params.CheckTariffResponse.Data.ShippingCost = 0
			params.CheckTariffResponse.Data.CommoditySurcharge = 0
			params.CheckTariffResponse.Data.HeavyWeightSurcharge = 0
			params.CheckTariffResponse.Data.DocumentSurcharge = 0
			params.CheckTariffResponse.Data.InsuranceRates = 0
			params.CheckTariffResponse.Data.WoodpackingRates = 0
			params.CheckTariffResponse.Data.TotalTariff = 0
			params.CheckTariffResponse.Data.TaxRates = 0
			params.CheckTariffResponse.Data.BMTaxRate = 0
			params.CheckTariffResponse.Data.PPNTaxRate = 0
			params.CheckTariffResponse.Data.PPHTaxRate = 0
			params.CheckTariffResponse.Data.OriginDistrictRate = 0
			params.CheckTariffResponse.Data.DestinationDistrictRate = 0
			params.CheckTariffResponse.Data.PublishRate = 0
			params.CheckTariffResponse.Data.ShippingSurchargeRate = 0
			params.CheckTariffResponse.Data.CodAmount = 0
			params.CheckTariffResponse.Data.CodFee = 0
			params.CheckTariffResponse.Data.TarifAfterDiscount = model.TarifAfterDiscount{}
		}
		return params, nil
	}
}

func (c *sttCtx) checkIsReverseFromCod(sttReverse model.Stt, sttShipmentID, codHandling string) bool {
	var (
		isCodRetail           bool
		isCodShipmentFavorite bool
	)

	shipmentPrefix := ``
	if sttShipmentID != `` {
		shipmentPrefix = shared.GetPrefixShipmentID(sttShipmentID)
	}

	if sttReverse.SttIsCOD {
		isCodRetail = sttReverse.SttBookedForType == model.POS && sttReverse.SttBookedByType == model.POS
		isCodShipmentFavorite = model.IsShipmentFavorite[shipmentPrefix]
	}
	isC1SpecialCod := (shipmentPrefix == model.C1 || shipmentPrefix == model.C2) && codHandling == model.SPECIALCOD
	isCodCARetail := model.MappingShipmentPrefixCODCustomerAppsRetail[shipmentPrefix]
	isReverseFromSttCod := isCodRetail || isCodCARetail || isC1SpecialCod || isCodShipmentFavorite

	return isReverseFromSttCod
}

func (c *sttCtx) sttUpdateCalculateCodDfodfee(sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		var err error
		// validate total stt volume weight and total stt gross weight
		if err = stt.ValidateTotalWeight(params.CheckTariffResponse.Data.VolumeWeight, params.CheckTariffResponse.Data.GrossWeight, sttRow.SttShipmentID, sttRow.SttNoRefExternal); err != nil {
			return params, err
		}

		params.SttTotalAmountCOD = func() float64 {
			if params.CheckTariffResponse.Data.IsPromo {
				return params.CheckTariffResponse.Data.TotalTariffAfterDiscount
			}
			return params.CheckTariffResponse.Data.TotalTariff
		}()

		// check is COD
		if sttRow.SttIsCOD {
			params.SttCODAmount = sttRow.SttCODAmount
			params.SttCODFee = sttRow.SttCODFee
			params.SttTotalAmountCOD += sttRow.SttCODFee
			params, err = c.sttUpdateCalculateCodFee(ctx, sttRow, params)
			if err != nil {
				return params, err
			}
		}

		params, err = c.sttUpdateCalculateDfodFee(ctx, sttRow, params)
		if err != nil {
			return params, err
		}
		return params, nil
	}
}

func (c *sttCtx) sttUpdateCalculateCodFee(ctx context.Context, sttRow model.Stt, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
	opName := "UsecaseStt-sttUpdateCalculateCodFee"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": sttRow, "result": params, "error": err})
	}()

	isClientConfigTotalTariff := params.ClientCodConfigAmount == model.GoodsPriceTotalTarif && sttRow.SttBookedForType == model.CLIENT

	if !sttRow.SttIsDFOD && isClientConfigTotalTariff {
		params.ReqCheckTariff.RequestCalculateTariff.IsCod = true
		params.ReqCheckTariff.RequestCalculateTariff.CodAmount = sttRow.SttGoodsEstimatePrice + params.CheckTariffResponse.Data.TotalTariff
		checkTariffWithCOD, err := c.checkTariffRepo.TariffCalculation(selfCtx, &params.ReqCheckTariff)
		if err != nil || checkTariffWithCOD == nil {
			return params, err
		}
		params.CodBookingDiscount = params.CheckTariffResponse.Data.TotalTariff * params.ClientCodShipmentDiscount / 100

		params.SttCODAmount = checkTariffWithCOD.Data.CodAmount
		params.SttCODFee = checkTariffWithCOD.Data.CodFee
		params.SttTotalAmountCOD = checkTariffWithCOD.Data.TotalTariffAfterDiscount
	}

	if params.CodAmountConfigType == model.GoodsPriceTotalTarif {
		params.SttCODAmount = params.CheckTariffResponse.Data.CodAmount
		params.SttCODFee = params.CheckTariffResponse.Data.CodFee
		params.SttTotalAmountCOD = params.CheckTariffResponse.Data.TotalTariffAfterDiscount
	}

	return params, nil
}

func (c *sttCtx) sttUpdateCalculateDfodFee(ctx context.Context, sttRow model.Stt, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
	opName := "UsecaseStt-sttUpdateCalculateDfodFee"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": sttRow, "result": params, "error": err})
	}()

	// DFOD Client
	isDfodClient := sttRow.SttIsDFOD && sttRow.SttBookedForType == model.CLIENT && sttRow.SttBookedForID != model.CLIENT_ID_CUSTOMER_APPS
	if isDfodClient {
		params.ReqCheckTariff.RequestCalculateTariff.IsCod = true
		params.ReqCheckTariff.RequestCalculateTariff.IsDfod = true
		params.ReqCheckTariff.RequestCalculateTariff.CodAmount = params.CheckTariffResponse.Data.TotalTariff
		checkTariffWithCOD, err := c.checkTariffRepo.TariffCalculation(selfCtx, &params.ReqCheckTariff)
		if err != nil || checkTariffWithCOD == nil {
			return params, err
		}
		params.CodBookingDiscount = checkTariffWithCOD.Data.CODBookingDiscount

		params.SttCODAmount = checkTariffWithCOD.Data.CodAmount
		params.SttCODFee = checkTariffWithCOD.Data.CodFee
		params.SttTotalAmountCOD = checkTariffWithCOD.Data.TotalTariffAfterDiscount

	}

	if params.IsDFOD {
		params.SttCODFee = params.CheckTariffResponse.Data.CodFee
		params.SttCODAmount = params.CheckTariffResponse.Data.CodAmount
		params.SttTotalAmountCOD = params.CheckTariffResponse.Data.TotalTariffAfterDiscount
	}

	return params, nil
}

func (c *sttCtx) updateSttBuildSttPieceHistory(req *stt.UpdateSttRequest, sttDetail model.SttDetailResult) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		var isAfterPOD bool
		sttRow := sttDetail.Stt
		bookingFee := params.CheckTariffResponse.Data.TotalTariff
		bookingFeeAfterDiscount := params.CheckTariffResponse.Data.TotalTariffAfterDiscount
		if params.ReqCheckTariff.RequestCalculateTariff.IsCod {
			if params.CheckTariffResponse.Data.CodFee > 0 {
				bookingFee = params.CheckTariffResponse.Data.TotalTariff - params.CheckTariffResponse.Data.CodFee
			}
			if params.CheckTariffResponse.Data.CodFeeAfterDiscount > 0 {
				bookingFeeAfterDiscount = params.CheckTariffResponse.Data.TotalTariffAfterDiscount - params.CheckTariffResponse.Data.CodFeeAfterDiscount
			}
		}

		// build remarks piece history
		tempRemarksPieceHistory := model.RemarkPieceHistory{
			HistoryLocationName:         params.CityOrigin.Name,
			LatestStatusBeforeAdjusment: sttRow.SttLastStatusID,
			ChargeableWeight:            params.CheckTariffResponse.Data.ChargeableWeight,
			BookingFee:                  bookingFee,
			BookingFeeAfterDiscount:     bookingFeeAfterDiscount,
			ClientCodBookingDiscount:    params.CodBookingDiscount,
			SttWeightAttachFiles:        req.SttWeightAttachFiles,
			VolumeWeightDiscount:        params.CheckTariffResponse.Data.VolumeWeightDiscount,
		}

		signedSttWeightAttachFile, _ := c.GenerateSignedURLs(ctx, &sttRow, strings.Join(req.SttWeightAttachFiles, ","))
		tempRemarksPieceHistory.SttWeightAttachFileSigneds = signedSttWeightAttachFile

		for _, history := range params.SttPieceHistory {
			if history.HistoryStatus == model.POD {
				isAfterPOD = true
				break
			}
		}

		dataHistoryAdjustment := c.buildDataHistoryAdjustment(sttRow, req, params)

		for _, pieceData := range params.SttPieceHistoryAdjustmentArray {
			historyDataAdjustment := c.generateSttHistoryDataAdjustment(req, &sttDetail, dataHistoryAdjustment, pieceData.SttPieceAdjustment, isAfterPOD)
			tempRemarksPieceHistory.HistoryDataAdjustment = historyDataAdjustment
			pieceData.HistoryRemark = tempRemarksPieceHistory.EncodeToString()
			params.HistorySttArray = append(params.HistorySttArray, pieceData.SttPieceHistory)
		}
		return params, nil
	}
}

func (c *sttCtx) buildDataHistoryAdjustment(sttRow model.Stt, req *stt.UpdateSttRequest, params stt.UpdateSttParams) *model.HistoryDataAdjustment {
	dataHistoryAdjustment := &model.HistoryDataAdjustment{
		// data stt before adjustment
		TaxNumberBeforeAdjustment:             shared.ReturnStringIfDifferent(sttRow.SttTaxNumber, req.SttTaxNumber),
		GoodsStatusBeforeAdjustment:           shared.ReturnStringIfDifferent(sttRow.SttGoodsStatus, req.SttGoodsStatus),
		DestinationCityIDBeforeAdjustment:     shared.ReturnStringIfDifferent(sttRow.SttDestinationCityID, req.SttDestinationCityID),
		DestinationDistrictIDBeforeAdjustment: shared.ReturnStringIfDifferent(sttRow.SttDestinationDistrictID, req.SttDestinationDistrictID),
		SenderNameBeforeAdjustment:            shared.ReturnStringIfDifferent(sttRow.SttSenderName, req.SttSenderName),
		SenderPhoneBeforeAdjustment:           shared.ReturnStringIfDifferent(sttRow.SttSenderPhone, req.SttSenderPhone),
		SenderAddressBeforeAdjustment:         shared.ReturnStringIfDifferent(sttRow.SttSenderAddress, req.SttSenderAddress),
		RecipientNameBeforeAdjustment:         shared.ReturnStringIfDifferent(sttRow.SttRecipientName, req.SttRecipientName),
		RecipientPhoneBeforeAdjustment:        shared.ReturnStringIfDifferent(sttRow.SttRecipientPhone, req.SttRecipientPhone),
		RecipientAddressBeforeAdjustment:      shared.ReturnStringIfDifferent(sttRow.SttRecipientAddress, req.SttRecipientAddress),
		RecipientAddressTypeBeforeAdjustment:  shared.ReturnStringIfDifferent(sttRow.SttRecipientAddressType.Value(), req.SttRecipientAddressType),
		ProductNameBeforeAdjustment:           shared.ReturnStringIfDifferent(sttRow.SttProductType, req.SttProductType),
		CommodityCodeBeforeAdjustment:         shared.ReturnStringIfDifferent(sttRow.SttCommodityCode, req.SttCommodityCode),
		PiecePerPackBeforeAdjustment:          shared.ReturnIntIfDifferent(sttRow.SttPiecePerPack, req.SttPiecePerPack),
		InsuranceTypeBeforeAdjustment:         shared.ReturnStringIfDifferent(sttRow.SttInsuranceType, req.SttInsuranceType),

		// after adjustment
		TaxNumberAfterAdjustment:             shared.ReturnStringIfDifferent(req.SttTaxNumber, sttRow.SttTaxNumber),
		GoodsStatusAfterAdjustment:           shared.ReturnStringIfDifferent(req.SttGoodsStatus, sttRow.SttGoodsStatus),
		DestinationCityIDAfterAdjustment:     shared.ReturnStringIfDifferent(req.SttDestinationCityID, sttRow.SttDestinationCityID),
		DestinationDistrictIDAfterAdjustment: shared.ReturnStringIfDifferent(req.SttDestinationDistrictID, sttRow.SttDestinationDistrictID),
		SenderNameAfterAdjustment:            shared.ReturnStringIfDifferent(req.SttSenderName, sttRow.SttSenderName),
		SenderPhoneAfterAdjustment:           shared.ReturnStringIfDifferent(req.SttSenderPhone, sttRow.SttSenderPhone),
		SenderAddressAfterAdjustment:         shared.ReturnStringIfDifferent(req.SttSenderAddress, sttRow.SttSenderAddress),
		RecipientNameAfterAdjustment:         shared.ReturnStringIfDifferent(req.SttRecipientName, sttRow.SttRecipientName),
		RecipientPhoneAfterAdjustment:        shared.ReturnStringIfDifferent(req.SttRecipientPhone, sttRow.SttRecipientPhone),
		RecipientAddressAfterAdjustment:      shared.ReturnStringIfDifferent(req.SttRecipientAddress, sttRow.SttRecipientAddress),
		RecipientAddressTypeAfterAdjustment:  shared.ReturnStringIfDifferent(req.SttRecipientAddressType, sttRow.SttRecipientAddressType.Value()),
		ProductNameAfterAdjustment:           shared.ReturnStringIfDifferent(req.SttProductType, sttRow.SttProductType),
		CommodityCodeAfterAdjustment:         shared.ReturnStringIfDifferent(req.SttCommodityCode, sttRow.SttCommodityCode),
		PiecePerPackAfterAdjustment:          shared.ReturnIntIfDifferent(req.SttPiecePerPack, sttRow.SttPiecePerPack),
		InsuranceTypeAfterAdjustment:         shared.ReturnStringIfDifferent(req.SttInsuranceType, sttRow.SttInsuranceType),

		IsPromo:                  params.CheckTariffResponse.Data.IsPromo,
		TotalTariff:              params.CheckTariffResponse.Data.TotalTariff,
		TotalTariffAfterDiscount: params.CheckTariffResponse.Data.TotalTariffAfterDiscount,
	}
	return dataHistoryAdjustment
}

func (c *sttCtx) updateSttBuildListHistoryStatus() stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {

		/* build history_status */
		latestHistoriesAfterUpdated := []model.SttPieceHistory{
			{
				HistoryStatus: params.SttStatusAfterAdjusted,
			},
		}
		latestHistoriesAfterUpdated = append(latestHistoriesAfterUpdated, params.SttPieceHistory...)
		for i, j := 0, len(latestHistoriesAfterUpdated)-1; i < len(latestHistoriesAfterUpdated); i++ {
			if model.MapHistoryStatusCalculateRetailTariff[latestHistoriesAfterUpdated[i].HistoryStatus] {
				params.HistoryStatus = append(params.HistoryStatus, latestHistoriesAfterUpdated[i].HistoryStatus)
			}
			if model.MapHistoryStatusCalculateRetailTariff[latestHistoriesAfterUpdated[j].HistoryStatus] {
				params.DetailCalculateRetailTariff = append(params.DetailCalculateRetailTariff, model.DetailCalculateRetailTariff{
					Status:       latestHistoriesAfterUpdated[j].HistoryStatus,
					IsCalculated: false,
				})
			}
			j -= 1
		}
		return params, nil
	}
}

func (c *sttCtx) updateSttCheckPaymentInvoice(sttRow model.Stt) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		shipmentPrefix := ``
		if sttRow.SttShipmentID != `` {
			shipmentPrefix = shared.GetPrefixShipmentID(sttRow.SttShipmentID)
		}
		shipmentPrefixReverse := ``
		if params.SttReverse.SttShipmentID != `` {
			shipmentPrefixReverse = shared.GetPrefixShipmentID(params.SttReverse.SttShipmentID)
		}

		if c.cfg.EnableFeatureHoldCommission() && model.IsPrefixShipmentPaymentHoldValid[shipmentPrefix] {
			params.PaymentStatus = model.UNPAID
		}
		sttMeta := sttRow.SttMetaToStruct()
		isReverseJourney := sttMeta != nil && sttMeta.DetailSttReverseJourney != nil
		if isReverseJourney && c.checkIsSttRequireInvoice(sttRow, sttMeta, shipmentPrefixReverse) {
			params.PaymentStatus = model.UNPAID
			params.IsHoldCommission = true
			params.IsInvoiceCreated = true
		}

		params = c.checkIsHoldCommission(sttRow, shipmentPrefix, params)
		return params, nil
	}
}

func (c *sttCtx) checkIsSttRequireInvoice(sttRow model.Stt, sttMeta *model.SttMeta, shipmentPrefixReverse string) bool {

	isRerouteFromMisbooking := c.cfg.ConfigEnableConfirmCA() && sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt == model.REROUTE && sttMeta.DetailSttReverseJourney.ReverseLastStatusStt == model.MISBOOKING && model.IsPrefixShipmentNonPosFavoriteCAToAlgo[shipmentPrefixReverse]

	isShipmentCaRTS := c.cfg.ConfigRTSEnableConfirmCA() && sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt == model.RTS && model.IsPrefixShipmentCAToAlgoRTS[shipmentPrefixReverse] && shared.GetPrefixSttNo(sttRow.SttNo) == model.PrefixAutoReverseJourney
	return isRerouteFromMisbooking || isShipmentCaRTS
}

func (c *sttCtx) checkIsHoldCommission(sttRow model.Stt, shipmentPrefix string, params stt.UpdateSttParams) stt.UpdateSttParams {
	if shared.GetPrefixSttNo(sttRow.SttNo) == model.PrefixAutoCA && model.IsShipmentHoldCommission[shipmentPrefix] {
		params.IsHoldCommission = true
	}

	isSttCorporate := sttRow.SttBookedByType == model.POS && sttRow.SttBookedForType == model.CLIENT
	if model.IsPrefixSttForCorporate[shared.GetPrefixSttNo(sttRow.SttNo)] && isSttCorporate {
		params.IsHoldCommission = true
	}
	return params
}

func (c *sttCtx) updateSttBuildRequestSttUpdate(req *stt.UpdateSttRequest, sttRow model.Stt, params stt.UpdateSttParams, now time.Time) (paramsUpdateStt model.SttCreateParams, sttRequestUpdate model.SttCreate) {

	// request update stt
	sttRequestUpdate = model.SttCreate{
		Stt:                c.generateUpdateSttRequestSttData(req, sttRow, params, now),
		SttPieces:          req.SttPieces,
		ArrSttPieceHistory: params.HistorySttArray,
		IsInvoiceCreated:   params.IsInvoiceCreated,
	}
	if sttRow.SttIsCOD {
		sttRequestUpdate.Stt.SttCODAmount = params.SttCODAmount
		sttRequestUpdate.Stt.SttCODFee = params.SttCODFee
		sttRequestUpdate.Stt.SttTotalAmount = params.SttTotalAmountCOD
	}

	// optional rate request
	sttRequestUpdate.SttOptionalRate = append(sttRequestUpdate.SttOptionalRate, model.SttOptionalRate{
		SttOptionalRateSttID:  sttRow.SttID,
		SttOptionalRateName:   params.CheckTariffResponse.Data.InsuranceName,
		SttOptionalRateRate:   params.CheckTariffResponse.Data.InsuranceRates,
		SttOptionalRateParams: model.INSURANCE,
	})

	optionalRateUpdate := []model.SttOptionalRate{}
	if params.IsWoodpacking {
		sttRequestUpdate.SttOptionalRate = append(sttRequestUpdate.SttOptionalRate, model.SttOptionalRate{
			SttOptionalRateSttID:  sttRow.SttID,
			SttOptionalRateName:   strings.Title(model.WOODPACKING),
			SttOptionalRateRate:   params.CheckTariffResponse.Data.WoodpackingRates,
			SttOptionalRateParams: model.WOODPACKING,
		})
		optionalRateUpdate = sttRequestUpdate.SttOptionalRate
	}
	// params update STT
	isAllowToEditInternalPOD := req.AccountType == model.INTERNAL && sttRow.SttBookedForType == model.CLIENT && model.IsAllowToEditInternalPOD[sttRow.SttLastStatusID]
	if isAllowToEditInternalPOD {
		optionalRateUpdate = sttRequestUpdate.SttOptionalRate
	}
	paramsUpdateStt.SttCreate = append(paramsUpdateStt.SttCreate, model.SttCreate{
		Stt:                sttRequestUpdate.Stt,
		SttPieces:          sttRequestUpdate.SttPieces,
		SttOptionalRate:    optionalRateUpdate,
		ArrSttPieceHistory: sttRequestUpdate.ArrSttPieceHistory,

		DuplicateHistoriesForNewSttPiece: params.SttPieceHistory,
		OldSttPiecesNeedToBeArchieved:    req.SttPiecesIDNeedToBeArchieved,
	})
	paramsUpdateStt.IsRelabelFromExternalSource = req.IsRelabelFromExternalSource
	return paramsUpdateStt, sttRequestUpdate
}

func (c *sttCtx) generateUpdateSttRequestSttData(req *stt.UpdateSttRequest, sttRow model.Stt, params stt.UpdateSttParams, now time.Time) model.Stt {
	sttMeta := c.updateSttBuildSttMeta(req, sttRow, params, now)
	// add retail tarif when stt booked by pos for pos
	if sttRow.SttBookedByType == model.POS && sttRow.SttBookedForType == model.POS {
		retailTariff := params.CheckTariffResponse.Data
		retailTariff.ListDiscount = make([]model.PromoDiscount, 0)
		sttMeta.RetailTariff = &retailTariff
	}
	return model.Stt{
		SttID:                          req.SttID,
		SttShipmentID:                  sttRow.SttShipmentID,
		SttTaxNumber:                   req.SttTaxNumber,
		SttGoodsEstimatePrice:          req.SttGoodsEstimatePrice,
		SttGoodsStatus:                 req.SttGoodsStatus,
		SttNoRefExternal:               sttRow.SttNoRefExternal,
		SttOriginCityID:                sttRow.SttOriginCityID,
		SttOriginCityName:              params.CityOrigin.Name,
		SttDestinationCityID:           req.SttDestinationCityID,
		SttDestinationCityName:         params.CityDestination.Name,
		SttOriginDistrictID:            sttRow.SttOriginDistrictID,
		SttOriginDistrictName:          params.DistrictOrigin.Data.Name,
		SttDestinationDistrictID:       req.SttDestinationDistrictID,
		SttDestinationDistrictName:     params.DistrictDestination.Data.Name,
		SttDestinationDistrictUrsaCode: params.DistrictDestination.Data.UrsaCode,
		SttSenderName:                  req.SttSenderName,
		SttSenderAddress:               req.SttSenderAddress,
		SttSenderPhone:                 req.SttSenderPhone,
		SttRecipientName:               req.SttRecipientName,
		SttRecipientAddress:            req.SttRecipientAddress,
		SttRecipientAddressType:        dbr.NewNullString(req.SttRecipientAddressType),
		SttRecipientPhone:              req.SttRecipientPhone,
		SttProductType:                 params.SttProductType,
		SttSource:                      sttRow.SttSource,
		SttOriginDistrictRate:          params.CheckTariffResponse.Data.OriginDistrictRate,
		SttDestinationDistrictRate:     params.CheckTariffResponse.Data.DestinationDistrictRate,
		SttPublishRate:                 params.CheckTariffResponse.Data.PublishRate,
		SttShippingSurchargeRate:       params.CheckTariffResponse.Data.ShippingSurchargeRate,
		SttDocumentSurchargeRate:       params.CheckTariffResponse.Data.DocumentSurcharge,
		SttCommoditySurchargeRate:      params.CheckTariffResponse.Data.CommoditySurcharge,
		SttHeavyweightSurchargeRate:    params.CheckTariffResponse.Data.HeavyWeightSurcharge,
		SttBMTaxRate:                   params.CheckTariffResponse.Data.BMTaxRate,
		SttPPNTaxRate:                  params.CheckTariffResponse.Data.PPNTaxRate,
		SttPPHTaxRate:                  params.CheckTariffResponse.Data.PPHTaxRate,
		SttTotalAmount: func() float64 {
			// prefix DO, client data ClientIsDO true and ClientDOPaymentType free so set 0
			if params.ClientIsDOPaymentTypeFree {
				return 0
			}
			if params.CheckTariffResponse.Data.IsPromo {
				return params.CheckTariffResponse.Data.TotalTariffAfterDiscount
			}
			return params.CheckTariffResponse.Data.TotalTariff
		}(),
		SttLastStatusID:               params.SttStatusAfterAdjusted,
		SttGrossWeight:                params.CheckTariffResponse.Data.GrossWeight,
		SttVolumeWeight:               params.CheckTariffResponse.Data.VolumeWeight,
		SttChargeableWeight:           params.CheckTariffResponse.Data.ChargeableWeight,
		SttCommodityCode:              req.SttCommodityCode,
		SttCommodityName:              params.Commodity.Data.CommodityName,
		SttCommodityHsCode:            params.Commodity.Data.HsCode,
		SttIsDFOD:                     sttRow.SttIsDFOD,
		SttIsCOD:                      sttRow.SttIsCOD,
		SttIsDO:                       sttRow.SttIsDO,
		SttMeta:                       sttMeta.ToString(),
		SttUpdatedAt:                  now,
		SttUpdatedBy:                  int(req.AccountID),
		SttUpdatedName:                req.AccountName,
		SttHeavyweightSurchargeRemark: params.CheckTariffResponse.Data.HeavyWeightSurchargeRemarks,
		SttUpdatedActorID:             dbr.NewNullInt64(req.AccountRefID),
		SttUpdatedActorRole:           dbr.NewNullString(req.AccountRefType),
		SttUpdatedActorName:           dbr.NewNullString(req.AccountRefName),
		SttNextCommodity:              req.SttNextCommodity,
		SttPiecePerPack:               req.SttPiecePerPack,
		SttPaymentStatus:              params.PaymentStatus,
		SttInsuranceType:              params.InsuranceType,
		SttBookedForType:              sttRow.SttBookedForType,
		SttCommodityID:                params.Commodity.Data.CommodityID,
		SttTotalPiece:                 len(req.SttPieces),
	}
}

func (c *sttCtx) updateSttBuildSttMeta(req *stt.UpdateSttRequest, sttRow model.Stt, params stt.UpdateSttParams, now time.Time) *model.SttMeta {
	sttMeta := sttRow.SttMetaToStruct()
	switch {
	case sttMeta != nil:
		sttMeta.EstimateSLA = c.generateEstimaeSlaByProductType(params.CheckTariffResponse.Data.EstimateSLA, sttRow.SttProductType, sttRow.SttCreatedAt, params.CityOrigin.Timezone)
		sttMeta.RoundingDiff = params.CheckTariffResponse.Data.RoundingDiff
		sttMeta.DestinationCityName = params.CityDestination.Name
		sttMeta.DestinationDistrictName = params.DistrictDestination.Data.Name
		sttMeta.PostalCodeDestination = req.SttPostalCodeDestination
		switch {
		case len(sttMeta.DetailCalculateRetailTariff) == 0:
			sttMeta.DetailCalculateRetailTariff = params.DetailCalculateRetailTariff
		default:
			sttMeta.DetailCalculateRetailTariff = append(sttMeta.DetailCalculateRetailTariff, model.DetailCalculateRetailTariff{
				Status:       params.SttStatusAfterAdjusted,
				IsCalculated: false,
			})
		}
	default:
		sttMeta = &model.SttMeta{
			EstimateSLA:                 c.generateEstimaeSlaByProductType(params.CheckTariffResponse.Data.EstimateSLA, sttRow.SttProductType, sttRow.SttCreatedAt, params.CityOrigin.Timezone),
			RoundingDiff:                params.CheckTariffResponse.Data.RoundingDiff,
			OriginCityName:              params.CityOrigin.Name,
			OriginDistrictName:          params.DistrictOrigin.Data.Name,
			DestinationCityName:         params.CityDestination.Name,
			DestinationDistrictName:     params.DistrictDestination.Data.Name,
			DetailCalculateRetailTariff: params.DetailCalculateRetailTariff,
			PostalCodeDestination:       req.SttPostalCodeDestination,
		}
	}

	sttMeta.VolumeWeightDiscount = params.CheckTariffResponse.Data.VolumeWeightDiscount
	if params.IsCalcTariffReturn {
		sttMeta.TotalTariffReturn = params.TotalTariffReturn
	}
	sttMeta.ClientPaymentMethod = params.ClientPaymentMethod
	sttMeta.ClientCodConfigAmount = params.ClientCodConfigAmount
	sttMeta.ClientCodShipmentDiscount = params.ClientCodShipmentDiscount
	sttMeta.RateVatShipment = params.RateVatShipment
	sttMeta.RateVatCod = params.RateVatCod
	sttMeta.ListDiscount = params.CheckTariffResponse.Data.ListDiscount
	if params.SttProductType == model.INTERPACK {
		if len(req.SttAttachFiles) > 0 {
			sttMeta.SttAttachFiles = req.SttAttachFiles
		}
		if req.SttCommodityDetail != "" {
			sttMeta.SttCommodityDetail = req.SttCommodityDetail
		}
		sttMeta.SttRecipientEmail = req.SttRecipientEmail
		sttMeta.SttKtpImage = req.SttKtpImage
		sttMeta.SttTaxImage = req.SttTaxImage
		sttMeta.SttInterTaxNumber = req.SttInterTaxNumber
		sttMeta.SttIdentityNumber = req.SttIdentityNumber
	}

	sttMeta.ClientTariff = c.generateSttMetaClientTariff(sttRow, params.CheckTariffResponse)
	if params.IsDFOD {
		sttMeta.DfodConfiguration.DfodFee = params.SttCODFee
	}

	sttMeta.AssessmentRelabel = c.generateAssessmentRelabel(req, sttRow, params, now)

	return sttMeta
}

func (c *sttCtx) generateSttMetaClientTariff(sttRow model.Stt, checkTariff model.CheckTariffResponse) *model.ClientTariff {
	if sttRow.SttBookedByType != model.PARTNERPOS || sttRow.SttBookedForType != model.CLIENT {
		return nil
	}
	clientTariffBeforeDiscount := model.ClientTariffDetail{
		CityRates:               checkTariff.Data.CityRates,
		ForwardRates:            checkTariff.Data.ForwardRates,
		ShippingCost:            checkTariff.Data.ShippingCost,
		CommoditySurcharge:      checkTariff.Data.CommoditySurcharge,
		HeavyWeightSurcharge:    checkTariff.Data.HeavyWeightSurcharge,
		DocumentSurcharge:       checkTariff.Data.DocumentSurcharge,
		InsuranceRates:          checkTariff.Data.InsuranceRates,
		WoodpackingRates:        checkTariff.Data.WoodpackingRates,
		TotalTariff:             checkTariff.Data.TotalTariff,
		TaxRates:                checkTariff.Data.TaxRates,
		BMTaxRate:               checkTariff.Data.BMTaxRate,
		PPNTaxRate:              checkTariff.Data.PPNTaxRate,
		PPHTaxRate:              checkTariff.Data.PPHTaxRate,
		OriginDistrictRate:      checkTariff.Data.OriginDistrictRate,
		DestinationDistrictRate: checkTariff.Data.DestinationDistrictRate,
		PublishRate:             checkTariff.Data.PublishRate,
		ShippingSurchargeRate:   checkTariff.Data.ShippingSurchargeRate,
		CodAmount:               checkTariff.Data.CodAmount,
		CodFee:                  checkTariff.Data.CodFee,
	}
	cityRateAfterDiscount := checkTariff.Data.PublishRateAfterDiscount + checkTariff.Data.ShippingSurchargeRateAfterDiscount
	forwardRateAfterDiscount := checkTariff.Data.OriginDistrictRateAfterDiscount + checkTariff.Data.DestinationDistrictRateAfterDiscount
	shippingCostAfterDiscount := cityRateAfterDiscount + forwardRateAfterDiscount

	return &model.ClientTariff{
		BeforeDiscount: clientTariffBeforeDiscount,
		AfterDiscount: model.ClientTariffDetailWithDiscount{
			ClientTariffDetail: model.ClientTariffDetail{
				CityRates:               cityRateAfterDiscount,
				ForwardRates:            forwardRateAfterDiscount,
				ShippingCost:            shippingCostAfterDiscount,
				CommoditySurcharge:      checkTariff.Data.CommoditySurchargeAfterDiscount,
				HeavyWeightSurcharge:    checkTariff.Data.HeavyWeightSurchargeAfterDiscount,
				DocumentSurcharge:       checkTariff.Data.DocumentSurchargeAfterDiscount,
				InsuranceRates:          checkTariff.Data.InsuranceRatesAfterDiscount,
				WoodpackingRates:        checkTariff.Data.WoodpackingRatesAfterDiscount,
				TotalTariff:             checkTariff.Data.TotalTariffAfterDiscount,
				TaxRates:                checkTariff.Data.TaxRates,
				BMTaxRate:               checkTariff.Data.BMTaxRate,
				PPNTaxRate:              checkTariff.Data.PPNTaxRate,
				PPHTaxRate:              checkTariff.Data.PPHTaxRate,
				OriginDistrictRate:      checkTariff.Data.OriginDistrictRateAfterDiscount,
				DestinationDistrictRate: checkTariff.Data.DestinationDistrictRateAfterDiscount,
				PublishRate:             checkTariff.Data.PublishRateAfterDiscount,
				ShippingSurchargeRate:   checkTariff.Data.ShippingSurchargeRateAfterDiscount,
				CodAmount:               checkTariff.Data.CodAmount,
				CodFee:                  checkTariff.Data.CodFeeAfterDiscount,
			},
			Discount:                 checkTariff.Data.Discount,
			DiscountType:             checkTariff.Data.DiscountType,
			IsPromo:                  checkTariff.Data.IsPromo,
			IsDiscountExceedMaxPromo: checkTariff.Data.IsDiscountExceedMaxPromo,
			TotalDiscount:            checkTariff.Data.TotalDiscount,
		},
	}
}

func (c *sttCtx) updateSttGetParamsCreditDebit(req *stt.UpdateSttRequest, sttRow model.Stt, sttRequestUpdate model.SttCreate) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		opName := "UsecaseStt-updateSttGetParamsCreditDebit"
		trace := tracer.StartTrace(ctx, opName)
		selfCtx := trace.Context()
		var err error
		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": req, "result": params, "error": err})
		}()

		sttMeta := sttRow.SttMetaToStruct()
		if sttMeta == nil {
			return params, nil
		}
		if sttMeta.DetailSttReverseJourney != nil {
			fn := c.updateSttGetParamsCreditDebitReverseJourney(req, sttMeta)
			params, err = fn(ctx, params)
			if err != nil {
				return params, err
			}
		} else {
			err = c.updateSttCheckGoodsEstimatePriceCod(sttRow, params, sttRequestUpdate)
			if err != nil {
				return params, err
			}
		}
		return params, nil
	}
}

func (c *sttCtx) updateSttGetParamsCreditDebitReverseJourney(req *stt.UpdateSttRequest, sttMeta *model.SttMeta) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {
		var err error
		switch sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt {
		case model.REROUTE:
			f := c.updateSttGetParamsCreditDebitReroute(req, sttMeta)
			params, err = f(ctx, params)
			if err != nil {
				return params, err
			}
		case model.RTS:
			params = c.updateSttGetParamsCreditDebitRTS(sttMeta, params)
		}

		sttReverse := params.SttReverse
		IsSttOriginShipmentFavorite := sttReverse.SttShipmentID != `` && model.IsShipmentFavorite[shared.GetPrefixShipmentID(sttReverse.SttShipmentID)] && (sttReverse.SttIsCOD || sttReverse.SttIsDFOD)
		if IsSttOriginShipmentFavorite {
			params.IsZeroCreditDebitTransaction = true
		}
		return params, nil
	}
}

func (c *sttCtx) updateSttGetParamsCreditDebitReroute(req *stt.UpdateSttRequest, sttMeta *model.SttMeta) stt.BuildParamsUpdateStt {
	return func(ctx context.Context, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {

		switch sttMeta.DetailSttReverseJourney.ReverseLastStatusStt {
		case model.MISROUTE:
			params.IsCreditDebitToBookedBy = true
			params.IsZeroCreditCommission = true

			if sttMeta.DetailSttReverseJourney.ReverseChargedPosID > 0 && sttMeta.DetailSttReverseJourney.ReverseChargedConsoleID > 0 {
				params.SttBookedBy = sttMeta.DetailSttReverseJourney.ReverseChargedPosID
				params.SttBookedByType = model.POS
			}

		case model.MISBOOKING:
			params.IsZeroCreditCommission = true

			reverseJourneyBookingActor, err := c.gatewaySttUc.CheckReverseJourneyBookingActor(ctx, &params.SttReverse, req.Token)
			if err != nil {
				return params, err
			}

			if reverseJourneyBookingActor.BookedActor != nil {
				bookedByActor := *reverseJourneyBookingActor.BookedActor
				params.CreditDebitActorID = bookedByActor.ID
				params.CreditDebitActorType = bookedByActor.Type
				params.BookedByActor = bookedByActor
			}
			params.IsDebitToBookedFor = reverseJourneyBookingActor.IsDebitToBookedFor
			params.IsOnlyDebitTransaction = reverseJourneyBookingActor.IsOnlyDebitTransaction

			if params.SttReverse.SttShipmentID != `` {
				prefix := shared.GetPrefixShipmentID(params.SttReverse.SttShipmentID)
				return c.updateSttGetParamsCreditDebitMisbooking(ctx, req, prefix, params)
			}
		}
		return params, nil
	}
}

func (c *sttCtx) updateSttGetParamsCreditDebitMisbooking(ctx context.Context, req *stt.UpdateSttRequest, prefix string, params stt.UpdateSttParams) (stt.UpdateSttParams, error) {

	if !model.IsShipmentMarketplace[prefix] {
		return params, nil
	}
	shipment, err := c.shipmentRepo.Get(ctx, &model.ShipmentViewParams{
		ShipmentAlgoID: params.SttReverse.SttShipmentID,
	})
	if err != nil {
		return params, shared.ERR_UNEXPECTED_DB
	}
	if shipment == nil || len(shipment.ShipmentPackets) == 0 {
		return params, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting shipment data",
			"id": "Terjadi kesalahan pada saat query getting shipment data",
		})
	}

	for _, v := range shipment.ShipmentPackets {
		recipient := model.ShipmentPacketSubject{}
		err = json.Unmarshal([]byte(v.ShipmentPacketRecipient), &recipient)
		if err != nil {
			return params, err
		}

		// get location mapping district client
		destinationDistrictReverse, err := c.districtRepo.GetDistrictClient(ctx, &model.DistrictCityClientViewParams{
			ClientID:     params.SttReverse.SttBookedForID,
			DistrictCode: recipient.Destination,
			Token:        req.Token,
		})
		if err != nil {
			err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting district data",
				"id": "Terjadi kesalahan pada saat query getting district data",
			})
			return params, err
		}

		params.IsZeroCreditDebit = destinationDistrictReverse != nil
		params.IsZeroCreditDebitTransaction = destinationDistrictReverse != nil
	}

	return params, nil
}

func (c *sttCtx) updateSttGetParamsCreditDebitRTS(sttMeta *model.SttMeta, params stt.UpdateSttParams) stt.UpdateSttParams {

	rootReverseShipmentPrefix := ``
	if sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` {
		rootReverseShipmentPrefix = shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID)
	}

	isC1SpecialCod := (rootReverseShipmentPrefix == model.C1 || rootReverseShipmentPrefix == model.C2) && sttMeta.DetailSttReverseJourney.RootReverseCodHandling == model.SPECIALCOD
	isShipmentFavCod := params.SttReverse.SttIsCOD && model.IsShipmentFavorite[rootReverseShipmentPrefix]
	isSttCodValid := params.SttReverse.IsSttCODRetail() || isC1SpecialCod || isShipmentFavCod || model.MappingShipmentPrefixCODCustomerAppsRetail[rootReverseShipmentPrefix]
	if isSttCodValid {
		params.IsZeroCreditCommission = true
	}
	return params
}

func (c *sttCtx) updateSttCheckGoodsEstimatePriceCod(sttRow model.Stt, params stt.UpdateSttParams, sttRequestUpdate model.SttCreate) error {

	isValidSttCod := !params.IsDFOD && ((sttRow.SttIsCOD && sttRow.SttBookedByType == model.POS && sttRow.SttBookedForType == model.POS) || params.IsPrefixCODCARetail || params.IsPrefixShipmentFavCOD)
	if isValidSttCod && sttRequestUpdate.Stt.SttGoodsEstimatePrice < sttRequestUpdate.Stt.SttCODFee {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt Goods Estimate Price is Lower than COD Fee",
			"id": "Harga Barang STT kurang dari Cod Fee",
		})
	}
	return nil
}

func (c *sttCtx) updateSttRecalculateTariffRetail(sttRow model.Stt, params stt.UpdateSttParams) {

	/* publish message to calculate retail tariff */
	if sttRow.SttBookedByType == model.POS && sttRow.SttBookedForType == model.CLIENT {

		payloadCalculateRetailTariff := stt.CalculateRetailTariffRequest{
			OriginID:          params.ReqCheckTariff.RequestCalculateTariff.OriginID,
			DestinationID:     params.ReqCheckTariff.RequestCalculateTariff.DestinationID,
			CommodityID:       params.ReqCheckTariff.RequestCalculateTariff.CommodityID,
			ProductType:       params.ReqCheckTariff.RequestCalculateTariff.ProductType,
			AccountType:       sttRow.SttBookedByType,
			AccountRefID:      sttRow.SttBookedBy,
			GoodsPrice:        params.ReqCheckTariff.RequestCalculateTariff.GoodsPrice,
			CodAmount:         params.ReqCheckTariff.RequestCalculateTariff.CodAmount,
			IsCod:             false,
			InsuranceType:     params.ReqCheckTariff.RequestCalculateTariff.InsuranceType,
			IsWoodpacking:     params.ReqCheckTariff.RequestCalculateTariff.IsWoodpacking,
			IsHaveTaxID:       params.ReqCheckTariff.RequestCalculateTariff.IsHaveTaxID,
			Pieces:            params.ReqCheckTariff.RequestCalculateTariff.Pieces,
			IsBookingPurposes: params.ReqCheckTariff.RequestCalculateTariff.IsBookingPurposes,
			HistoryStatus:     params.HistoryStatus,
			SttNo:             sttRow.SttNo,
			IsDisablePromo:    params.IsDisablePromo,
		}

		sttMeta := sttRow.SttMetaToStruct()
		isReverseJourneyZeroTariff := sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && model.IsSttStatusReverseJourneyZeroTariff[sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt]
		isSttReverseRtsHQ := params.SttMetaReverse.DetailSttReverseJourney != nil && params.SttMetaReverse.DetailSttReverseJourney.ReverseJourneyStatusStt == model.RTSHQ

		if isReverseJourneyZeroTariff && isSttReverseRtsHQ {
			return
		}
		c.CalculateRetailTariff(context.Background(), &payloadCalculateRetailTariff)
	}
}

func (c *sttCtx) updateSttGenerateReqPublishMessage(p UpdateSttPublishMessageParams) *gateway_stt.AdjustmentSttPublishMessageRequest {
	previousSenderPhoneNumber := ""
	if p.SttRow.SttIsCOD && p.SttRow.SttSenderPhone != p.SttRequestUpdate.Stt.SttSenderPhone {
		previousSenderPhoneNumber = p.SttRow.SttSenderPhone
	}
	estimateSLA := c.buildEstimateSLA(p.Ctx, p.SttRow, p.Request.Token)

	pieces := make([]model.SttPieceForClient, 0, len(p.SttRequestUpdate.SttPieces))
	for _, v := range p.SttRequestUpdate.SttPieces {
		pieces = append(pieces, model.SttPieceForClient{
			PieceID:           v.SttPieceID,
			PieceLength:       v.SttPieceLength,
			PieceWidth:        v.SttPieceWidth,
			PieceHeight:       v.SttPieceHeight,
			PieceGrossWeight:  v.SttPieceGrossWeight,
			PieceVolumeWeight: v.SttPieceVolumeWeight,
		})
	}
	return &gateway_stt.AdjustmentSttPublishMessageRequest{
		SttNo: p.SttRow.GetValidSttElexysNo(),
		Sender: gateway_stt.PersonAdjustmentSttPublishMessageRequest{
			Name:    p.SttRequestUpdate.Stt.SttSenderName,
			Address: p.SttRequestUpdate.Stt.SttSenderAddress,
			Phone:   p.SttRequestUpdate.Stt.SttSenderPhone,
		},
		Recipient: gateway_stt.PersonAdjustmentSttPublishMessageRequest{
			Name:    p.SttRequestUpdate.Stt.SttRecipientName,
			Address: p.SttRequestUpdate.Stt.SttRecipientAddress,
			Phone:   p.SttRequestUpdate.Stt.SttRecipientPhone,
		},
		ServiceType:              model.PACKAGESERVICE,
		Product:                  p.SttRequestUpdate.Stt.SttProductType,
		Pieces:                   p.SttRequestUpdate.Stt.SttTotalPiece,
		GrossWeight:              p.SttRequestUpdate.Stt.SttGrossWeight,
		VolumeWeight:             p.SttRequestUpdate.Stt.SttVolumeWeight,
		PublishRate:              p.Params.CheckTariffResponse.Data.PublishRateAfterDiscount,
		ForwardRate:              p.Params.CheckTariffResponse.Data.OriginDistrictRateAfterDiscount + p.Params.CheckTariffResponse.Data.DestinationDistrictRateAfterDiscount,
		ShippingSurchargeRate:    p.Params.CheckTariffResponse.Data.ShippingSurchargeRateAfterDiscount,
		OriginDistrictRate:       p.Params.CheckTariffResponse.Data.OriginDistrictRateAfterDiscount,
		DestinationDistrictRate:  p.Params.CheckTariffResponse.Data.DestinationDistrictRateAfterDiscount,
		CommoditySurchargeRate:   p.Params.CheckTariffResponse.Data.CommoditySurchargeAfterDiscount,
		HeavyWeightSurchargeRate: p.Params.CheckTariffResponse.Data.HeavyWeightSurchargeAfterDiscount,
		IsWoodPacking:            p.Params.IsWoodpacking,
		IsInsurance:              p.SttRequestUpdate.Stt.SttInsuranceType != model.INSURANCEFREE,
		InsuranceRate:            p.Params.CheckTariffResponse.Data.InsuranceRatesAfterDiscount,
		InsuranceType:            p.SttRequestUpdate.Stt.SttInsuranceType,
		GoodsValue:               p.SttRequestUpdate.Stt.SttGoodsEstimatePrice,
		CODAmount:                p.SttRequestUpdate.Stt.SttCODAmount,
		CODValue: func() float64 {
			if p.Params.IsDFOD {
				return p.SttRequestUpdate.Stt.SttCODAmount - p.SttRequestUpdate.Stt.SttCODFee
			}

			isValidCod := p.SttRow.IsSttCODRetail() || p.Params.IsPrefixCODCARetail || p.Params.IsPrefixShipmentFavCOD
			if isValidCod {
				return p.SttRequestUpdate.Stt.SttGoodsEstimatePrice - p.SttRequestUpdate.Stt.SttCODFee
			}
			return p.SttRequestUpdate.Stt.SttCODAmount
		}(),
		CODFee:            p.SttRequestUpdate.Stt.SttCODFee,
		Origin:            fmt.Sprintf("%s (%s)", p.SttRequestUpdate.Stt.SttOriginDistrictName, p.SttRequestUpdate.Stt.SttOriginCityID),
		Destination:       fmt.Sprintf("%s (%s)", p.SttRequestUpdate.Stt.SttDestinationDistrictName, p.SttRequestUpdate.Stt.SttDestinationCityID),
		UpdatedOn:         p.Params.SttUpdatedAt.UTC(),
		ChargeableWeight:  p.SttRequestUpdate.Stt.SttChargeableWeight,
		DocumentSurcharge: p.Params.CheckTariffResponse.Data.DocumentSurchargeAfterDiscount,
		Status:            p.Params.SttStatusAfterAdjusted,
		BookedForType:     p.SttRequestUpdate.Stt.SttBookedForType,
		ReturnDetails: func() *general.ReturnDetails {
			isSttCustomerApps := p.SttRow.SttIsCOD && p.SttRow.SttBookedForType == model.CLIENT && shared.GetPrefixSttNo(p.SttRow.SttNo) != model.PrefixAutoCA
			if isSttCustomerApps {
				clientData, _ := c.clientRepo.GetByID(context.Background(), p.SttRow.SttClientID, p.Request.Token)
				return clientData.GenerateReturnDetailsResponse()
			}
			return nil
		}(),
		TotalTariff:                           p.SttRequestUpdate.Stt.SttTotalAmount,
		IsPromo:                               p.Params.CheckTariffResponse.Data.IsPromo,
		Discount:                              p.Params.CheckTariffResponse.Data.Discount,
		TotalDiscount:                         p.Params.CheckTariffResponse.Data.TotalDiscount,
		PublishRateBeforeDiscount:             p.SttRequestUpdate.Stt.SttPublishRate,
		ShippingSurchargeRateBeforeDiscount:   p.SttRequestUpdate.Stt.SttShippingSurchargeRate,
		OriginDistrictRateBeforeDiscount:      p.SttRequestUpdate.Stt.SttOriginDistrictRate,
		DestinationDistrictRateBeforeDiscount: p.SttRequestUpdate.Stt.SttDestinationDistrictRate,
		ForwardRateBeforeDiscount:             p.SttRequestUpdate.Stt.SttOriginDistrictRate + p.SttRequestUpdate.Stt.SttDestinationDistrictRate,
		DocumentSurchargeBeforeDiscount:       p.SttRequestUpdate.Stt.SttDocumentSurchargeRate,
		CommoditySurchargeBeforeDiscount:      p.SttRequestUpdate.Stt.SttCommoditySurchargeRate,
		HeavyWeightSurchargeBeforeDiscount:    p.SttRequestUpdate.Stt.SttHeavyweightSurchargeRate,
		InsuranceRatesBeforeDiscount:          p.Params.CheckTariffResponse.Data.InsuranceRates,
		TotalTariffBeforeDiscount:             p.Params.CheckTariffResponse.Data.TotalTariffBeforeDiscount,
		IsInvoiceCreated:                      p.SttRequestUpdate.IsInvoiceCreated,
		IsTopupCA:                             p.Params.IsTopupCA,
		PercentageCodFee:                      p.Params.PercentageCodFee,
		MinCodFee:                             p.Params.MinCodFee,
		IsDFOD:                                p.SttRow.SttIsDFOD,
		IsCOD:                                 p.SttRow.SttIsCOD,
		SttAttachFiles:                        p.Params.SttAttachFiles,
		ShippingCost:                          p.Params.CheckTariffResponse.Data.ShippingCost,
		PreviousSenderPhoneNumber:             previousSenderPhoneNumber,
		OriginCity:                            p.SttRow.SttOriginCityID,
		DestinationCity:                       p.SttRow.SttDestinationCityID,
		EstimatedSla:                          estimateSLA,
		Source:                                getSourceGetDetailSttShipment(p.SttRow.SttNo, p.SttRow.SttShipmentID, p.SttRow.SttMetaToStruct()),
		TotalPiece:                            p.SttRow.SttTotalPiece,
		PiecesDetails:                         pieces,
	}
}

func (c *sttCtx) sttAdjustmentPublishAdjustmentPenalty(req *stt.UpdateSttRequest, sttRow model.Stt, sttRequestUpdate model.SttCreate, params stt.UpdateSttParams) {

	sttMeta := sttRow.SttMetaToStruct()
	// check remark history
	bookingFeeBeforePUP, chargeAbleWeightBeforePUP := c.getBookingFeeBeforePup(sttRow, params.SttPieceHistory)
	pieceArray := []gateway_stt.SttPiece{}
	for _, piece := range req.SttPieces {
		pieceData := gateway_stt.SttPiece{
			PieceLength:      piece.SttPieceLength,
			PieceWidth:       piece.SttPieceWidth,
			PieceHeight:      piece.SttPieceHeight,
			PieceGrossWeight: piece.SttPieceGrossWeight,
		}
		pieceArray = append(pieceArray, pieceData)
	}

	isSttReverseJourney := sttMeta != nil && sttMeta.DetailSttReverseJourney != nil
	if isSttReverseJourney && c.updateSttCheckIsZeroCreditDebit(sttMeta, &params.SttMetaReverse) {
		params.IsZeroCreditDebit = true
	}

	woodpackingRate := 0.0
	if params.IsWoodpacking {
		woodpackingRate = params.CheckTariffResponse.Data.WoodpackingRates
	}
	pgBookingReq := c.sttAdjustmentGeneratePgBookingRequest(sttRow, sttRequestUpdate, params)
	pgBookingReq.IsWoodpacking = params.IsWoodpacking
	pgBookingReq.WoodpackingRates = woodpackingRate
	pgBookingReq.Token = req.Token
	pgBookingReq.IsHaveTaxID = req.SttTaxNumber != ``
	pgBookingReq.IsZeroCreditDebit = params.IsZeroCreditDebit
	pgBookingReq.Pieces = pieceArray

	isCodConfigAmountTotalTariff := params.ClientCodConfigAmount == model.GoodsPriceTotalTarif || params.CodAmountConfigType == model.GoodsPriceTotalTarif
	isCodFeeUpdated := isCodConfigAmountTotalTariff || sttRow.SttIsDFOD

	sttAdjRequest := &gateway_stt.AdjustmentSttGoberPublishMessageRequest{
		PgBookingRequest:                 pgBookingReq,
		ChargeAbleWeightBeforePUP:        chargeAbleWeightBeforePUP,
		LastStatus:                       sttRow.SttLastStatusID,
		LatestStatusBeforeAdjustment:     sttRow.SttLastStatusID,
		StatusBecome:                     params.SttStatusAfterAdjusted,
		UpdateByType:                     req.AccountType,
		UpdateByID:                       req.AccountRefID,
		BookingFee:                       c.sttAdjustmentCalculateBookingFee(sttRequestUpdate, params),
		BookingFeeBeforePUP:              bookingFeeBeforePUP,
		ChargeAbleWeightBeforeAdjustment: sttRow.SttChargeableWeight,
		CODFee:                           sttRequestUpdate.Stt.SttCODFee,
		IsCodFeeUpdated:                  isCodFeeUpdated,
		ClientPaymentMethod:              params.ClientPaymentMethod,
		ClientCodConfigAmount:            params.ClientCodConfigAmount,
		ClientCodShipmentDiscount:        params.ClientCodShipmentDiscount,
		IsCod:                            sttRow.SttIsCOD,
		IsDfod:                           sttRow.SttIsDFOD,
	}
	sttAdjRequest = c.sttAdjustmentCheckReverseJourney(sttMeta, &params.SttMetaReverse, sttAdjRequest)
	c.gatewaySttUc.SttAdjustmentCalculatePenalty(context.Background(), sttAdjRequest)
}

func (c *sttCtx) updateSttCheckIsZeroCreditDebit(sttMeta, sttMetaReverse *model.SttMeta) (isZeroCreditDebit bool) {
	var (
		rootSttIsCod         bool
		isSttReverseRtsRtsHQ bool
	)

	isReverseJourneyZeroTariff := model.IsSttStatusReverseJourneyZeroTariff[sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt]

	rootShipmentID := sttMeta.DetailSttReverseJourney.RootReverseShipmentID
	if rootShipmentID != `` {
		rootShipmentPrefix := shared.GetPrefixShipmentID(sttMeta.DetailSttReverseJourney.RootReverseShipmentID)

		isC1SpecialCod := (rootShipmentPrefix == model.C1 || rootShipmentPrefix == model.C2) && sttMeta.DetailSttReverseJourney.RootReverseCodHandling == model.SPECIALCOD

		rootSttIsCod = model.MappingShipmentPrefixCODCustomerAppsRetail[rootShipmentPrefix] || isC1SpecialCod
	}

	if sttMetaReverse.DetailSttReverseJourney != nil {
		isSttReverseRtsHQ := sttMetaReverse.DetailSttReverseJourney.ReverseJourneyStatusStt == model.RTSHQ
		isSttReverseRTSCod := sttMetaReverse.DetailSttReverseJourney.ReverseJourneyStatusStt == model.RTS && rootSttIsCod
		isSttReverseRtsRtsHQ = isSttReverseRtsHQ || isSttReverseRTSCod
	}

	isZeroCreditDebit = isReverseJourneyZeroTariff || isSttReverseRtsRtsHQ
	return isZeroCreditDebit
}

func (c *sttCtx) getBookingFeeBeforePup(sttRow model.Stt, sttHistories []model.SttPieceHistory) (bookingFeeBeforePUP, chargeAbleWeightBeforePUP float64) {
	// check remark history
	bookingFeeBeforePUP = sttRow.SttTotalAmount
	chargeAbleWeightBeforePUP = sttRow.SttChargeableWeight
	checkHistoryRemarks := func(historyRemark *model.RemarkPieceHistory) {
		bookingFeeBeforePUP = historyRemark.BookingFee
		if historyRemark.HistoryDataAdjustment != nil && historyRemark.HistoryDataAdjustment.IsPromo {
			bookingFeeBeforePUP = historyRemark.BookingFeeAfterDiscount
		}
		bookingFeeBeforePUP -= historyRemark.ClientCodBookingDiscount
		chargeAbleWeightBeforePUP = historyRemark.ChargeableWeight
	}

	mapLastStatus := map[string]string{
		model.STTADJUSTED: model.BKD,
	}
	for i := 0; i < len(sttHistories); i++ {
		historyStatus := sttHistories[i].HistoryStatus
		if !model.IsStatusBeforePUP[historyStatus] {
			continue
		}
		historyRemark := sttHistories[i].RemarkPieceHistoryToStruct()
		// if last status stt adjustment get default bookingFeeBeforePUP from bkd
		if historyRemark != nil && historyRemark.LatestStatusBeforeAdjusment == mapLastStatus[historyStatus] {
			checkHistoryRemarks(historyRemark)
			break
		}
	}

	return bookingFeeBeforePUP, chargeAbleWeightBeforePUP
}

func (c *sttCtx) sttAdjustmentCalculateBookingFee(sttRequestUpdate model.SttCreate, params stt.UpdateSttParams) float64 {
	shipmentID := sttRequestUpdate.Stt.SttShipmentID
	isSvalidShipment := shipmentID == `` || model.IsShipmentCod[shared.GetPrefixShipmentID(shipmentID)]

	isTariffIncludeCodFee := sttRequestUpdate.Stt.SttIsCOD && sttRequestUpdate.Stt.SttCODFee > 0 && isSvalidShipment
	if isTariffIncludeCodFee {
		params.CheckTariffResponse.Data.TotalTariff = params.CheckTariffResponse.Data.TotalTariff - params.CheckTariffResponse.Data.CodFee
	}

	// prefix DO, client data ClientIsDO true and ClientDOPaymentType free so set 0
	if params.ClientIsDOPaymentTypeFree {
		return 0
	}

	return params.CheckTariffResponse.Data.TotalTariff - params.CodBookingDiscount
}

func (c *sttCtx) sttAdjustmentCheckReverseJourney(sttMeta *model.SttMeta, sttMetaReverse *model.SttMeta, payload *gateway_stt.AdjustmentSttGoberPublishMessageRequest) *gateway_stt.AdjustmentSttGoberPublishMessageRequest {

	var (
		isReverseJourneyReturn bool
		isReverseFromCodRej    bool
	)

	payload.PgBookingRequest.IsSttReverseJourney = sttMetaReverse != nil && sttMetaReverse.DetailSttReverseJourney != nil && model.IsSttStatusReturnToSender[sttMetaReverse.DetailSttReverseJourney.ReverseJourneyStatusStt]

	rootShipmentPrefix := ``
	if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil {
		if sttMeta.DetailSttReverseJourney.RootReverseShipmentID != `` {
			reverseSttShipmentID := sttMeta.DetailSttReverseJourney.RootReverseShipmentID
			payload.ReverseSttShipmentID = reverseSttShipmentID
			rootShipmentPrefix = shared.GetPrefixShipmentID(reverseSttShipmentID)
			isReverseFromCodRej = sttMeta.DetailSttReverseJourney.ReverseLastStatusStt == model.CODREJ
		}
		isReverseJourneyReturn = model.IsSttStatusReturnToSender[sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt]
	}
	if isReverseJourneyReturn {
		payload.PgBookingRequest.IsSttReverseJourney = true
		payload.PgBookingRequest.IsSttShipmentFavoriteReverseJourney = model.IsShipmentPrefixFavorite[rootShipmentPrefix]
		payload.IsPenaltyDebitBookedFor = isReverseFromCodRej
	}

	return payload
}

func (c *sttCtx) sttAdjustmentGeneratePgBookingRequest(sttRow model.Stt, sttRequestUpdate model.SttCreate, params stt.UpdateSttParams) gateway_stt.PgBookingRequest {
	accountRefID := sttRow.SttClientID
	accountType := model.CLIENT
	if sttRow.SttPosID > 0 {
		accountRefID = sttRow.SttPosID
		accountType = model.POS
	}
	return gateway_stt.PgBookingRequest{
		SttNo:            sttRow.SttNo,
		OriginID:         sttRequestUpdate.Stt.SttOriginDistrictID,
		DestinationID:    sttRequestUpdate.Stt.SttDestinationDistrictID,
		ProductType:      sttRequestUpdate.Stt.SttProductType,
		BookedForID:      accountRefID,
		BookedForType:    accountType,
		CommodityID:      sttRequestUpdate.Stt.SttCommodityID,
		GoodsPrice:       sttRequestUpdate.Stt.SttGoodsEstimatePrice,
		InsuranceType:    params.InsuranceType,
		CityRates:        sttRequestUpdate.Stt.SttPublishRate + sttRequestUpdate.Stt.SttShippingSurchargeRate,
		ForwardRates:     sttRequestUpdate.Stt.SttOriginDistrictRate + sttRequestUpdate.Stt.SttDestinationDistrictRate,
		ChargeAbleWeight: sttRequestUpdate.Stt.SttChargeableWeight,
		// ShippingCosts: ,
		CommoditySurcharge:           sttRequestUpdate.Stt.SttCommoditySurchargeRate,
		HeavyWeightSurcharge:         sttRequestUpdate.Stt.SttHeavyweightSurchargeRate,
		DocumentSurcharge:            sttRequestUpdate.Stt.SttDocumentSurchargeRate,
		InsuranceRates:               params.CheckTariffResponse.Data.InsuranceRates,
		InsuranceAdminFee:            params.CheckTariffResponse.Data.InsuranceAdminFee,
		InsuranceName:                params.CheckTariffResponse.Data.InsuranceName,
		InsuranceLabel:               params.InsuranceType,
		TotalTarifff:                 params.CheckTariffResponse.Data.TotalTariff,
		TaxRates:                     params.CheckTariffResponse.Data.BMTaxRate + params.CheckTariffResponse.Data.PPNTaxRate + params.CheckTariffResponse.Data.PPHTaxRate,
		BMTaxRate:                    sttRequestUpdate.Stt.SttBMTaxRate,
		PPNTaxRate:                   sttRequestUpdate.Stt.SttPPNTaxRate,
		PPHTaxRate:                   sttRequestUpdate.Stt.SttPPHTaxRate,
		OriginDistrictRate:           params.CheckTariffResponse.Data.OriginDistrictRate,
		DestinationDistrictRate:      params.CheckTariffResponse.Data.DestinationDistrictRate,
		PublishRate:                  sttRequestUpdate.Stt.SttPublishRate,
		ShippingSurchargeRate:        sttRequestUpdate.Stt.SttShippingSurchargeRate,
		BookingType:                  sttRow.SttBookedByType,
		BookedBy:                     sttRow.SttBookedBy,
		BookedByType:                 sttRow.SttBookedByType,
		ShipmentID:                   sttRow.SttShipmentID,
		CommodityCode:                sttRequestUpdate.Stt.SttCommodityCode,
		CityOriginID:                 sttRequestUpdate.Stt.SttOriginCityID,
		CityDestinationID:            sttRequestUpdate.Stt.SttDestinationCityID,
		PartnerPosParentID:           params.BookedByActor.PosParentID(),
		PartnerPosBranchCommission:   params.BookedByActor.PosBranchCommission(),
		IsCreditDebitToBookedBy:      params.IsCreditDebitToBookedBy,
		IsZeroCreditCommission:       params.IsZeroCreditCommission,
		IsOnlyDebitTransaction:       params.IsOnlyDebitTransaction,
		IsDebitToBookedFor:           params.IsDebitToBookedFor,
		CreditDebitActorID:           params.CreditDebitActorID,
		CreditDebitActorType:         params.CreditDebitActorType,
		TarifAfterDiscount:           params.CheckTariffResponse.Data.TarifAfterDiscount,
		IsHoldCommision:              params.IsHoldCommission,
		IsZeroCreditDebitTransaction: params.IsZeroCreditDebitTransaction,
		BookingReturn:                params.TotalTariffReturn / 2,
		CodHandling:                  params.CodHandling,
		Currency:                     params.CheckTariffResponse.Data.Currency,
		PartnerPosIsPickup:           params.PartnerPosIsPickup,
	}
}
