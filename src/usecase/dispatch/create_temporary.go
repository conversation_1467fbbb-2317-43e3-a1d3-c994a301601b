package dispatch

import (
	"strings"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
)

const (
	KonsolChecker = `konsol_checker`
	KonsolShuttle = `konsol_shuttle`

	DispatchScanPerSttSuccess     = `success`
	DispatchScanPerSttUnpaid      = `failed_unpaid`
	DispatchScanPerSttDestination = `failed_destination`
	DispatchScanPerSttStatus      = `failed_`

	DispatchDemStatusSuccess = `success`
	DispatchDemStatusPartial = `partial`
	DispatchDemStatusFailed  = `failed`
)

type (
	CreateDispatchTemporaryRequest struct {
		ScanNo          string `json:"scan_no"`
		HubID           int64  `json:"hub_id"`
		HubName         string `json:"hub_name"`
		HubOriginCity   string `json:"hub_origin_city"`
		HubDistrictCode string `json:"hub_district_code"`

		PartnerID       int    `json:"-"`
		AccountID       int64  `json:"-"`
		AccountName     string `json:"-"`
		AccountRoleName string `json:"-"`
		Token           string `json:"-"`
		IsScanByDem     bool   `json:"-"`
		Status          string `json:"-"`
	}

	CreateDispatchTemporaryResponse struct {
		Stt       *CreateDispatchTemporarySttResponse  `json:"stt,omitempty"`
		DemStatus string                               `json:"dem_status"`
		Dem       []CreateDispatchTemporaryDemResponse `json:"dem,omitempty"`
	}

	CreateDispatchTemporarySttResponse struct {
		SttID                          int64     `json:"stt_id"`
		SttNo                          string    `json:"stt_no"`
		SttProductType                 string    `json:"stt_product_type"`
		SttTotalPiece                  int       `json:"stt_total_piece"`
		SttDestinationCityID           string    `json:"stt_destination_city_id"`
		SttDestinationCityName         string    `json:"stt_destination_city_name"`
		SttDestinationDistrictID       string    `json:"stt_destination_district_id"`
		SttDestinationDistrictName     string    `json:"stt_destination_district_name"`
		SttDestinationDistrictUrsaCode string    `json:"stt_destination_district_ursa_code"`
		SttCommodityCode               string    `json:"stt_commodity_code"`
		SttRecipientAddress            string    `json:"stt_recipient_address"`
		SttCommodityName               string    `json:"stt_commodity_name"`
		SttGrossWeight                 float64   `json:"stt_gross_weight"`
		SttLastStatusID                string    `json:"stt_last_status_id"`
		SttRecipientName               string    `json:"stt_recipient_name"`
		SttRecipientPhone              string    `json:"stt_recipient_phone"`
		SttCodAmount                   float64   `json:"stt_cod_amount"`
		RefNo                          string    `json:"ref_no"`
		DemNo                          string    `json:"dem_no"`
		DtScanNo                       string    `json:"dt_scan_no"`
		DtScanStatus                   string    `json:"dt_scan_status"`
		DtCreatedAt                    time.Time `json:"dt_created_at"`
		DtUpdatedAt                    time.Time `json:"dt_updated_at"`
	}

	CreateDispatchTemporaryDemResponse struct {
		SttID                          int64  `json:"stt_id"`
		SttNo                          string `json:"stt_no"`
		SttProductType                 string `json:"stt_product_type"`
		SttTotalPiece                  int    `json:"stt_total_piece"`
		SttDestinationCityID           string `json:"stt_destination_city_id"`
		SttDestinationCityName         string `json:"stt_destination_city_name"`
		SttDestinationDistrictID       string `json:"stt_destination_district_id"`
		SttDestinationDistrictName     string `json:"stt_destination_district_name"`
		SttDestinationDistrictUrsaCode string `json:"stt_destination_district_ursa_code"`
		SttCommodityCode               string `json:"stt_commodity_code"`
		SttCommodityName               string `json:"stt_commodity_name"`
		SttRecipientAddress            string `json:"stt_recipient_address"`

		SttGrossWeight    float64   `json:"stt_gross_weight"`
		SttLastStatusID   string    `json:"stt_last_status_id"`
		SttRecipientName  string    `json:"stt_recipient_name"`
		SttRecipientPhone string    `json:"stt_recipient_phone"`
		SttCodAmount      float64   `json:"stt_cod_amount"`
		DemNo             string    `json:"dem_no"`
		RefNo             string    `json:"ref_no"`
		DtScanNo          string    `json:"dt_scan_no"`
		DtScanStatus      string    `json:"dt_scan_status"`
		DtCreatedAt       time.Time `json:"dt_created_at"`
		DtUpdatedAt       time.Time `json:"dt_updated_at"`
	}
)

func (r *CreateDispatchTemporaryRequest) Validate() error {
	if r.AccountRoleName != KonsolChecker && r.AccountRoleName != KonsolShuttle {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Account role not valid",
			"id": "Account role tidak valid",
		})
	}

	r.Status = model.STLDISPATCH
	if r.AccountRoleName == KonsolChecker {
		r.Status = model.KONDISPATCH
	}

	r.ScanNo = strings.ReplaceAll(r.ScanNo, " ", "")
	if r.ScanNo == "" {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Scan no is empty",
			"id": "Nomor scan kosong",
		})
	}

	if strings.HasPrefix(r.ScanNo, model.PrefixDeliveryManifest) {
		r.IsScanByDem = true
	}

	return nil
}
