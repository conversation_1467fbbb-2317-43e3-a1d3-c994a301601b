package usecase

import (
	"context"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/reason"
)

func (c *reasonCtx) ViewReason(ctx context.Context, params *reason.ViewReasonRequest) ([]reason.ViewReasonResponse, error) {
	res := make([]reason.ViewReasonResponse, 0)

	opName := "reasonCtx-ViewReason"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": err})
	}()

	sttData, err := c.fetchStt(selfCtx, params.SttNo)
	if err != nil {
		return res, err
	}

	data, err := c.reasonRepo.SelectDetail(selfCtx, &model.ReasonViewParams{
		ReasonCode:       params.ReasonCode,
		StatusCode:       params.StatusCode,
		ReasonStatus:     params.ReasonStatus,
		IsNotShownEnable: params.IsNotShownEnable,
		IsCreateTicketSf: params.IsCreateTicketSf,
		ReasonTitle:      params.ReasonTitle,
	})

	if err != nil {
		return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while http request",
			"id": "Terjadi kesalahan pada saat request http",
		})
	}

	mapReasonExternal, err := c.setMapReasonExternalViewReason(ctx, params.IsGetExternalMapping, data)
	if err != nil {
		return res, err
	}

	for _, val := range data {
		if c.shouldExcludeReasonDexFromResponse(params, val, sttData) {
			continue
		}

		var externalMappingData *reason.ExternalMappingData
		dataMapReasonExternal, ok := mapReasonExternal[val.ReasonCode]
		if ok {
			externalMappingData = &reason.ExternalMappingData{
				ReasonID:             dataMapReasonExternal.ReasonID,
				ReasonCode:           dataMapReasonExternal.ReasonCode,
				ReasonExternalCode:   dataMapReasonExternal.ReasonExternalCode,
				ReasonDescription:    dataMapReasonExternal.ReasonDescription,
				ReasonDescriptionEn:  dataMapReasonExternal.ReasonDescriptionEn,
				ReasonStatus:         dataMapReasonExternal.ReasonStatus,
				ReasonCanGenerateStt: dataMapReasonExternal.ReasonCanGenerateStt,
				StatusCode:           dataMapReasonExternal.ReasonMappingStatusCode,
			}
		}

		res = append(res, reason.ViewReasonResponse{
			ReasonID:                val.ReasonID,
			ReasonCode:              val.ReasonCode,
			ReasonExternalCode:      val.ReasonExternalCode,
			ReasonDescription:       val.ReasonDescription,
			ReasonDescriptionEn:     val.ReasonDescriptionEn,
			ReasonStatus:            val.ReasonStatus,
			ReasonCanGenerateStt:    val.ReasonCanGenerateStt,
			ReasonMappingStatusCode: val.ReasonMappingStatusCode,
			ReasonExternalMapping:   externalMappingData,
		})
	}

	return res, nil
}

func (c *reasonCtx) setMapReasonExternalViewReason(ctx context.Context, isGetExternalMapping bool, data []model.ReasonDetailResult) (map[string]model.ReasonExternalDetailResult, error) {
	var (
		reasonCodes       []string
		mapReasonExternal = make(map[string]model.ReasonExternalDetailResult)
	)

	if !isGetExternalMapping {
		return mapReasonExternal, nil
	}

	for _, valReason := range data {
		reasonCodes = append(reasonCodes, valReason.ReasonCode)
	}

	dataReasonExternal, err := c.reasonRepo.SelectReasonExternalDetail(ctx, reasonCodes)
	if err != nil {
		return mapReasonExternal, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while http request",
			"id": "Terjadi kesalahan pada saat request http",
		})
	}

	for _, valReasonExternal := range dataReasonExternal {
		mapReasonExternal[valReasonExternal.ReasonExternalReasonCode] = valReasonExternal
	}

	return mapReasonExternal, nil
}

func (c *reasonCtx) fetchStt(ctx context.Context, sttNo string) (*model.Stt, error) {
	if sttNo == `` {
		return nil, nil
	}
	sttData, err := c.sttRepo.Get(ctx, &model.SttViewDetailParams{
		Stt:           model.Stt{SttNo: sttNo},
		CustomColumns: `stt_delivery_attempt`,
	})
	if err != nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while http request",
			"id": "Terjadi kesalahan pada saat request http",
		})
	}
	if sttData == nil {
		return nil, shared.ERR_STT_NOT_FOUND
	}
	return sttData, nil
}

func (c *reasonCtx) shouldExcludeReasonDexFromResponse(params *reason.ViewReasonRequest, reasonDetail model.ReasonDetailResult, stt *model.Stt) bool {
	if stt == nil || len(c.cfg.ReasonDexExclude()) == 0 {
		return false
	}
	eligibleToExclude := params.StatusCode == model.DEX && stt.SttDeliveryAttempt == 1
	if eligibleToExclude && shared.IsInArrayString(c.cfg.ReasonDexExclude(), reasonDetail.ReasonCode) {
		return true
	}
	return false
}
