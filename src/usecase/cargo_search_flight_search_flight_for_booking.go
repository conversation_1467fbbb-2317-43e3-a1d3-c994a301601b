package usecase

import (
	"context"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"

	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/usecase/cargo"
)

func (c *cargoSearchFlightUsecase) SearchFlightForBooking(ctx context.Context, req *cargo.CargoConfigurationSearchFlightFindingRequest) ([]model.CargoConfigurationSearchFlightFindingData, error) {
	var (
		opName  = "cargoSearchFlightUsecase-SearchFlightForBooking"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		result = make([]model.CargoConfigurationSearchFlightFindingData, 0)
		err    error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"req": req, "result": result, "error": err})
		c.partnerLog.Insert(selfCtx, &model.PartnerLog{
			Action: opName,
			RefID:  req.ToRqID(),
			Request: map[string]interface{}{
				"parameter": req,
			},
			Response: map[string]interface{}{
				"result": result,
				"err":    err,
			},
		})
	}()

	// Validate req
	req.MaxThresholdTime = c.cfg.SabreDefaultValidationThreshold()
	if err = req.Validate(); err != nil {
		return result, err
	}

	req.UserReqTime = req.DepartureDateConfigTime

	// Validate city
	_, err = c.SearchFlightValidateCity(ctx, req.OriginCityCode, req.UserToken)
	if err != nil {
		return result, err
	}

	valid, err := c.GetCargoConfigurationSearchFlightBCT(ctx, req)
	if err != nil {
		return result, err
	}

	// not valid is because the configuration not found
	if !valid {
		return result, shared.NewMultiStringErrorDataNotFound(shared.HTTPErrorDataNotFound, map[string]string{
			"en": "No flight found",
			"id": "Tidak ada penerbangan yang cocok",
		})
	}

	// Search Flight Sabre
	itineraries, err := c.SearchFlightBookingSabre(ctx, req)
	if err != nil {
		return result, err
	}

	result = itineraries.ToSearchFlightFindingData()
	if len(result) < 1 {
		return result, shared.NewMultiStringErrorDataNotFound(shared.HTTPErrorDataNotFound, map[string]string{
			"en": "No flight found with MCT configuration",
			"id": "Tidak ada penerbangan yang cocok dengan configurasi MCT",
		})
	}

	return result, nil
}

func (c *cargoSearchFlightUsecase) SearchFlightBookingSabre(ctx context.Context, req *cargo.CargoConfigurationSearchFlightFindingRequest) (model.ItineraryPartsResult, error) {
	var (
		opName  = "cargoSearchFlightUsecase-SearchFlightBookingSabre"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		data    []model.SabreItineraryPart
		err     error
		results model.ItineraryPartsResult

		departureDate, dateAfter, dateBefore time.Time
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"req": req, "results": results, "error": err})
		c.partnerLog.Insert(selfCtx, &model.PartnerLog{
			Action: opName,
			RefID:  req.ToRqID(),
			Request: map[string]interface{}{
				"param": req,
			},
			Response: map[string]interface{}{
				"itinerary_results": results,
				"dateAfter":         dateAfter,
				"dateBefore":        dateBefore,
				"error":             err,
			},
		})
	}()

	departureDate = req.UserReqTime
	departureDateAndThreshold := departureDate.Add(time.Duration(req.ThresholdTime) * time.Minute)

	// If the threshold is still on the same day
	if departureDateAndThreshold.Day() == departureDate.Day() {
		req.SabreReqDate = append(req.SabreReqDate, cargo.CargoSabreTimeRange{
			From:  departureDate,
			Until: departureDateAndThreshold,
		})
	} else {
		// First range: from the request time until the end of the day
		req.SabreReqDate = append(req.SabreReqDate, cargo.CargoSabreTimeRange{
			From:  departureDate,
			Until: time.Date(departureDate.Year(), departureDate.Month(), departureDate.Day(), 23, 59, 59, 0, departureDate.Location()),
		})
		// Second range: from 00:00 on the next day until the threshold time
		req.SabreReqDate = append(req.SabreReqDate, cargo.CargoSabreTimeRange{
			From:  time.Date(departureDateAndThreshold.Year(), departureDateAndThreshold.Month(), departureDateAndThreshold.Day(), 0, 0, 0, 0, departureDate.Location()),
			Until: departureDateAndThreshold,
		})
	}

	for _, param := range c.SearchFlightSabreParam(ctx, req) {
		result, err := c.sabreRepo.SearchFlightV2(ctx, &param.Params)
		if err != nil {
			return results, err
		}
		itineraries, err := c.SearchFlightSabreProcessItinerary(ctx, &cargo.CargoFlightItineraryParams{
			DateFrom:    param.TimeRange.From,
			DateUntil:   param.TimeRange.Until,
			SabreResult: &result,
			Req:         req,
		})
		if err != nil {
			return results, err
		}
		data = append(data, itineraries...)
	}

	results.ItineraryPart = data
	results.SortByArrivalItinerary()
	return results, nil
}
