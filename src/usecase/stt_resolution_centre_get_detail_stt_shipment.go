package usecase

import (
	"context"
	"strings"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

type (
	dataSttPieceHistoryGetDetailSttShipment struct {
		TotalDelAttempt    *int
		TotalDexAttempt    *int
		TotalCodRejAttempt *int
		LastDeliv          *model.SttPieceHistoryCustom
		LastDelivReason    *model.SttPieceHistoryCustom
		SttPieceHistory    *model.SttPieceHistoryCustom
	}
	respSttPieceHistoryGetDetailSttShipment struct {
		TotalDelAttempt    int
		TotalDexAttempt    int
		TotalCodRejAttempt int
		LastDeliv          *model.SttPieceHistoryCustom
		LastDelivReason    *model.SttPieceHistoryCustom
		SttPieceHistory    model.SttPieceHistoryCustom
	}
	paramPartnerCommodityGetDetailSttShipment struct {
		sttPieceHistory model.SttPieceHistoryCustom
		opName          string
		sttDetail       *model.Stt
		token           string
	}
	respLastDelivGetDetailSttShipment struct {
		LastDelivHistoryStatus    string
		LastDelivHistoryCreatedAt string
		LastDelivPartnerCode      string
		LastDelivPartnerName      string
		LastDelivPartnerType      string
		LastDelivCourierName      string
		LastDelivCourierPhone     string
	}
	paramTransformResponseGetDetailSttShipment struct {
		SttDetail             *model.Stt
		SttMeta               *model.SttMeta
		PartnerTier           string
		EstimatedSlaStartDate string
		EstimatedSlaEndDate   string
		LastDelivReasonCode   string
		LastDelivReasonName   string
		RespSttPieceHistory   *respSttPieceHistoryGetDetailSttShipment
		RespLastDeliv         *respLastDelivGetDetailSttShipment
		PartnerData           *model.Partner
		CommodityData         *model.Commodity
		SttPieces             []model.SttPiece
	}
)

func (s *sttCtx) GetDetailSttShipment(ctx context.Context, params *stt.GetDetailSttShipmentRequest) (*stt.GetDetailSttShipmentResponse, error) {
	opName := "UsecaseStt-GetDetailSttShipment"
	trace := tracer.StartTrace(ctx, opName)
	var (
		err          error
		dataStt      *stt.GetDetailSttShipmentStt
		dataShipment *stt.GetDetailSttShipmentShipment
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(ctx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": nil, "error": err})
	}()

	dataStt, dataShipment, err = s.handlerGetDetailSttShipment(ctx, params, opName)
	if err != nil {
		logger.Ef("[%s] error handlerGetDetailSttShipment : %v", opName, err)
		return nil, err
	}

	return &stt.GetDetailSttShipmentResponse{
		Stt:      dataStt,
		Shipment: dataShipment,
	}, nil
}

func (s *sttCtx) getSttGetDetailSttShipment(ctx context.Context, sttNo, opName string) (*model.Stt, *model.SttMeta, error) {
	sttDetail, err := s.sttRepo.Get(ctx, &model.SttViewDetailParams{
		Stt: model.Stt{SttNo: sttNo},
	})
	if err != nil {
		logger.Ef("[%s] error sttRepo.Get : %v", opName, err)
		return nil, nil, shared.ERR_UNEXPECTED_DB
	}
	if sttDetail == nil {
		logger.If("[%s] not found sttRepo.Get", opName)
		return nil, nil, shared.ERR_STT_NOT_FOUND
	}
	return sttDetail, sttDetail.SttMetaToStruct(), nil
}

func (s *sttCtx) sttPieceHistoryGetDetailSttShipment(ctx context.Context, sttDetail *model.Stt, opName string) (*respSttPieceHistoryGetDetailSttShipment, error) {
	var (
		totalDelAttempt    int
		totalDexAttempt    int
		totalCodRejAttempt int
		lastDeliv          = new(model.SttPieceHistoryCustom)
		lastDelivReason    = new(model.SttPieceHistoryCustom)
	)
	sttPieceHistories, err := s.sttPieceHistoryRepo.SelectBySttNo(ctx, &model.SttPieceHistoryViewParam{
		SttNoIn:   []string{sttDetail.SttNo},
		SortBy:    `sph.history_created_at`,
		OrderDesc: false,
		SttNo:     sttDetail.SttNo,
	})
	if err != nil {
		logger.Ef("[%s] error sttPieceHistoryRepo.SelectBySttNo : %v", opName, err)
		return nil, shared.ERR_UNEXPECTED_DB
	}
	if len(sttPieceHistories) == 0 {
		logger.If("[%s] not found sttPieceHistoryRepo.SelectBySttNo", opName)
		return nil, shared.ERR_STT_NOT_FOUND
	}
	sttPieceHistory := sttPieceHistories[len(sttPieceHistories)-1]

	for i := len(sttPieceHistories) - 1; i >= 0; i-- {
		history := sttPieceHistories[i]
		isHistoryValid := history.HistoryStatus != model.BKD && history.HistoryActorRole != model.INTERNAL && history.HistoryActorID > 0
		if isHistoryValid {
			sttPieceHistory.HistoryActorName = history.HistoryActorName
			sttPieceHistory.HistoryActorRole = history.HistoryActorRole
			sttPieceHistory.HistoryLocation = history.HistoryLocation
			sttPieceHistory.HistoryActorID = history.HistoryActorID
			break
		}
	}

	validMapLastDeliv := map[string]bool{
		model.DEL:    true,
		model.DEX:    true,
		model.CODREJ: true,
	}
	validMapLastDelivReason := map[string]bool{
		model.DEX:    true,
		model.CODREJ: true,
	}
	for _, history := range sttPieceHistories {
		getDataFromHistoryStatus(history, &dataSttPieceHistoryGetDetailSttShipment{
			TotalDelAttempt:    &totalDelAttempt,
			TotalDexAttempt:    &totalDexAttempt,
			TotalCodRejAttempt: &totalCodRejAttempt,
			LastDeliv:          lastDeliv,
			LastDelivReason:    lastDelivReason,
		}, validMapLastDeliv, validMapLastDelivReason)
	}

	return &respSttPieceHistoryGetDetailSttShipment{
		TotalDelAttempt:    totalDelAttempt,
		TotalDexAttempt:    totalDexAttempt,
		TotalCodRejAttempt: totalCodRejAttempt,
		LastDeliv:          lastDeliv,
		LastDelivReason:    lastDelivReason,
		SttPieceHistory:    sttPieceHistory,
	}, err
}

func (s *sttCtx) partnerByHistoryActorGetDetailSttShipment(ctx context.Context, params paramPartnerCommodityGetDetailSttShipment) (*model.Partner, error) {
	if params.sttPieceHistory.HistoryActorID == 0 {
		partnerData := new(model.Partner)
		partnerData.Data.Code = params.sttPieceHistory.HistoryActorName
		return partnerData, nil
	}

	partnerData, err := s.partnerRepo.GetByID(ctx, params.sttPieceHistory.HistoryActorID, params.token)
	if err != nil {
		logger.Ef("[%s] error partnerRepo.GetByID : %v", params.opName, err)
		return nil, shared.ERR_UNEXPECTED_DB
	}
	if partnerData == nil {
		logger.If("[%s] not found partnerRepo.GetByID", params.opName)
		return nil, shared.ERR_STT_NOT_FOUND
	}

	return partnerData, nil
}

func (s *sttCtx) partnerCommodityGetDetailSttShipment(ctx context.Context, params paramPartnerCommodityGetDetailSttShipment) (*model.Partner, *model.Commodity, error) {
	// Partner Data
	partnerData, err := s.partnerByHistoryActorGetDetailSttShipment(ctx, params)
	if err != nil {
		logger.Ef("[%s] error partnerByHistoryActorGetDetailSttShipment : %v", params.opName, err)
		return nil, nil, err
	}

	// Commodity Data
	commodityData, err := s.commodityRepo.GetCommodityByCode(ctx, params.sttDetail.SttCommodityCode, params.token)
	if err != nil {
		logger.Ef("[%s] error commodityRepo.GetCommodityByCode : %v", params.opName, err)
		return nil, nil, shared.ERR_UNEXPECTED_DB
	}
	if commodityData == nil {
		logger.If("[%s] not found commodityRepo.GetCommodityByCode", params.opName)
		return nil, nil, shared.ERR_STT_NOT_FOUND
	}

	return partnerData, commodityData, nil
}

func (s *sttCtx) lastDelivReasonGetDetailSttShipment(ctx context.Context, opName string, lastDelivReason *model.SttPieceHistoryCustom) (lastDelivReasonCode, lastDelivReasonName string, err error) {
	if lastDelivReason == nil {
		return
	}
	lastDelivReasonData, err := s.reasonRepo.GetDetail(ctx, &model.ReasonViewParams{ReasonCode: lastDelivReason.HistoryReason})
	if err != nil {
		logger.Ef("[%s] error reasonRepo.GetDetail : %v", opName, err)
		return
	}
	if lastDelivReasonData != nil {
		lastDelivReasonCode = lastDelivReasonData.ReasonCode
		lastDelivReasonName = lastDelivReasonData.ReasonTitle
	}

	return
}

func (s *sttCtx) getPartnerLastDelivGetDetailSttShipment(ctx context.Context, params *stt.GetDetailSttShipmentRequest, lastDeliv *model.SttPieceHistoryCustom, opName string) (lastDelivPartnerCode, lastDelivPartnerName, lastDelivPartnerType string, err error) {
	if lastDeliv.HistoryActorID == 0 {
		lastDelivPartnerCode = lastDeliv.HistoryActorName
		lastDelivPartnerName = lastDeliv.HistoryActorName
		lastDelivPartnerType = lastDeliv.HistoryActorRole
		return
	}

	lastDelivPartner, err := s.partnerRepo.GetByID(ctx, lastDeliv.HistoryActorID, params.TokenStr)
	if err != nil {
		logger.Ef("[%s] error partnerRepo.GetByID : %v", opName, err)
		return
	}
	if lastDelivPartner != nil {
		lastDelivPartnerCode = lastDelivPartner.Data.Code
		lastDelivPartnerName = lastDelivPartner.Data.Name
		lastDelivPartnerType = lastDelivPartner.Data.Type
	}

	return
}

func (s *sttCtx) lastDelivGetDetailSttShipment(ctx context.Context, params *stt.GetDetailSttShipmentRequest, lastDeliv *model.SttPieceHistoryCustom, opName string) (*respLastDelivGetDetailSttShipment, error) {
	var (
		lastDelivCourierName      string
		lastDelivCourierPhone     string
		lastDelivHistoryStatus    string
		lastDelivHistoryCreatedAt string
	)
	if lastDeliv == nil {
		return &respLastDelivGetDetailSttShipment{}, nil
	}
	lastDelivHistoryStatus = lastDeliv.HistoryStatus
	lastDelivHistoryCreatedAt = lastDeliv.HistoryCreatedAt.Format(time.RFC3339)

	lastDelivPartnerCode, lastDelivPartnerName, lastDelivPartnerType, err := s.getPartnerLastDelivGetDetailSttShipment(ctx, params, lastDeliv, opName)
	if err != nil {
		logger.Ef("[%s] error getPartnerLastDelivGetDetailSttShipment : %v", opName, err)
		return nil, err
	}

	// Last Deliv Courier STT Piece History
	lastDelivCourierData := lastDeliv.RemarkPieceHistoryToStruct()
	if lastDelivCourierData != nil {
		lastDelivCourierName = lastDelivCourierData.DriverName
		lastDelivCourierPhone = lastDelivCourierData.DriverPhone
	}

	return &respLastDelivGetDetailSttShipment{
		LastDelivHistoryStatus:    lastDelivHistoryStatus,
		LastDelivHistoryCreatedAt: lastDelivHistoryCreatedAt,
		LastDelivPartnerCode:      lastDelivPartnerCode,
		LastDelivPartnerName:      lastDelivPartnerName,
		LastDelivPartnerType:      lastDelivPartnerType,
		LastDelivCourierName:      lastDelivCourierName,
		LastDelivCourierPhone:     lastDelivCourierPhone,
	}, nil
}

func (s *sttCtx) estimatedSlaGetDetailSttShipment(ctx context.Context, params *stt.GetDetailSttShipmentRequest, sttDetail *model.Stt, sttMeta *model.SttMeta) (estimatedSlaStartDate, estimatedSlaEndDate string) {
	estimatedSlaDate, _ := s.getDetailSttTrackingEstimationDate(ctx, sttMeta.EstimateSLA, sttDetail.SttBookedAt.Format(shared.FormatDateTime), params.TokenStr, sttDetail.SttProductType)
	estimatedSlaDateSplit := strings.Split(estimatedSlaDate, " - ")
	if len(estimatedSlaDateSplit) > 1 {
		estimatedSlaStartDateTime, _ := time.Parse(shared.FormatDate, estimatedSlaDateSplit[0])
		estimatedSlaStartDate = estimatedSlaStartDateTime.Format(time.RFC3339)
		estimatedSlaEndDateTime, _ := time.Parse(shared.FormatDate, estimatedSlaDateSplit[1])
		estimatedSlaEndDate = estimatedSlaEndDateTime.Format(time.RFC3339)
	}
	return
}

func (s *sttCtx) sttSttPieceHistoryGetDetailSttShipment(ctx context.Context, params *stt.GetDetailSttShipmentRequest, opName string) (*model.Stt, *model.SttMeta, *respSttPieceHistoryGetDetailSttShipment, []model.SttPiece, error) {
	sttDetail, sttMeta, err := s.getSttGetDetailSttShipment(ctx, params.SttShipment, opName)
	if err != nil {
		logger.Ef("[%s] error getSttGetDetailSttShipment : %v", opName, err)
		return nil, nil, nil, nil, err
	}

	// STT Piece
	sttPieces, err := s.sttPiecesRepo.Select(ctx, &model.SttPiecesViewParam{SttID: int(sttDetail.SttID)})
	if err != nil {
		logger.Ef("[%s] error sttPiecesRepo.Select : %v", opName, err)
		return nil, nil, nil, nil, err
	}

	// STT Piece History
	respSttPieceHistory, err := s.sttPieceHistoryGetDetailSttShipment(ctx, sttDetail, opName)
	if err != nil {
		logger.Ef("[%s] error sttPieceHistiryGetDetailSttShipment : %v", opName, err)
		return nil, nil, nil, nil, err
	}

	return sttDetail, sttMeta, respSttPieceHistory, sttPieces, nil
}

func (s *sttCtx) transformResponseGetDetailSttShipment(params paramTransformResponseGetDetailSttShipment) *stt.GetDetailSttShipmentStt {
	var (
		totalDimLength, totalDimHeight, totalDimWidth float64
	)
	for _, piece := range params.SttPieces {
		totalDimHeight += piece.SttPieceHeight
		totalDimWidth += piece.SttPieceWidth
		totalDimLength += piece.SttPieceLength
	}

	return &stt.GetDetailSttShipmentStt{
		SttNo:                 params.SttDetail.SttNo,
		ShipmentID:            params.SttDetail.SttShipmentID,
		RefExternal:           params.SttDetail.SttNoRefExternal,
		BookedAt:              params.SttDetail.SttBookedAt.Format(time.RFC3339),
		BookedBy:              params.SttDetail.SttBookedBy,
		BookedByCode:          params.SttDetail.SttBookedByCode,
		BookedTier:            params.PartnerTier,
		BookedName:            params.SttDetail.SttBookedName,
		BookedByType:          params.SttDetail.SttBookedByType,
		OriginCity:            params.SttDetail.SttOriginCityID,
		DestinationCity:       params.SttDetail.SttDestinationCityID,
		EstimatedSla:          params.SttMeta.EstimateSLA,
		EstimatedSlaStartDate: params.EstimatedSlaStartDate,
		EstimatedSlaEndDate:   params.EstimatedSlaEndDate,
		LastStatus:            params.RespSttPieceHistory.SttPieceHistory.HistoryStatus,
		LastStatusAt:          params.RespSttPieceHistory.SttPieceHistory.HistoryCreatedAt.Format(time.RFC3339),
		LastStatusPartnerLoc:  params.RespSttPieceHistory.SttPieceHistory.HistoryLocation,
		LastStatusPartnerType: params.RespSttPieceHistory.SttPieceHistory.HistoryActorRole,
		LastStatusPartnerName: params.RespSttPieceHistory.SttPieceHistory.HistoryActorName,
		LastStatusPartnerCode: params.PartnerData.Data.Code,
		Source:                getSourceGetDetailSttShipment(params.SttDetail.SttNo, params.SttDetail.SttShipmentID, params.SttMeta),
		ProductType:           params.SttDetail.SttProductType,
		GrossWeight:           params.SttDetail.SttGrossWeight,
		ChargeableWeight:      params.SttDetail.SttChargeableWeight,
		VolumeWeight:          params.SttDetail.SttVolumeWeight,
		TotalDimHeight:        totalDimHeight,
		TotalDimWidth:         totalDimWidth,
		TotalDimLength:        totalDimLength,
		TotalPiece:            int64(params.SttDetail.SttTotalPiece),
		GoodsPrice:            params.SttDetail.SttGoodsEstimatePrice,
		IsInsurance:           model.IsInsuranceValid[params.SttDetail.SttInsuranceType],
		CommodityCode:         params.CommodityData.Data.CommodityCode,
		CommodityName:         params.CommodityData.Data.CommodityName,
		CommodityIsDG:         params.CommodityData.Data.IsDangerousGoods,
		CommodityIsK:          params.CommodityData.Data.CommodityIsQuarantine,
		IsCOD:                 params.SttDetail.SttIsCOD,
		IsDFOD:                params.SttDetail.SttIsDFOD,
		TotalDELAttempt:       int64(params.RespSttPieceHistory.TotalDelAttempt),
		TotalDEXAttempt:       int64(params.RespSttPieceHistory.TotalDexAttempt),
		TotalCODREJAttempt:    int64(params.RespSttPieceHistory.TotalCodRejAttempt),
		LastDelivStatus:       params.RespLastDeliv.LastDelivHistoryStatus,
		LastDelivAt:           params.RespLastDeliv.LastDelivHistoryCreatedAt,
		LastDelivReasonCode:   params.LastDelivReasonCode,
		LastDelivReasonName:   params.LastDelivReasonName,
		LastDelivPartnerCode:  params.RespLastDeliv.LastDelivPartnerCode,
		LastDelivPartnerName:  params.RespLastDeliv.LastDelivPartnerName,
		LastDelivPartnerType:  params.RespLastDeliv.LastDelivPartnerType,
		LastDelivCourierName:  params.RespLastDeliv.LastDelivCourierName,
		LastDelivCourierPhone: params.RespLastDeliv.LastDelivCourierPhone,
		SenderName:            params.SttDetail.SttSenderName,
		SenderPhone:           params.SttDetail.SttSenderPhone,
		RecipientName:         params.SttDetail.SttRecipientName,
		RecipientPhone:        params.SttDetail.SttRecipientPhone,
		RecipientAddress:      params.SttDetail.SttRecipientAddress,
		CreatedAt:             params.SttDetail.SttCreatedAt.Format(time.RFC3339),
		UpdatedAt:             params.SttDetail.SttUpdatedAt.Format(time.RFC3339),
		CodAmount:             params.SttDetail.SttCODAmount,
	}
}

func (s *sttCtx) getDataSTTHandlerGetDetailSttShipment(ctx context.Context, params *stt.GetDetailSttShipmentRequest, opName string) (*stt.GetDetailSttShipmentStt, error) {
	var (
		sttDetail   *model.Stt
		sttMeta     *model.SttMeta
		partnerTier string
	)

	// STT and STT Piece History
	sttDetail, sttMeta, respSttPieceHistory, sttPieces, err := s.sttSttPieceHistoryGetDetailSttShipment(ctx, params, opName)
	if err != nil {
		logger.Ef("[%s] error sttSttPieceHistoryGetDetailSttShipment : %v", opName, err)
		return nil, err
	}

	// Partner Tier
	partnerTier, err = s.getPartnerTier(ctx, params.PartnerID)
	if err != nil {
		logger.Ef("[%s] error getPartnerTier : %v", opName, err)
		return nil, err
	}

	// Partner and Commodity Data
	partnerData, commodityData, err := s.partnerCommodityGetDetailSttShipment(ctx, paramPartnerCommodityGetDetailSttShipment{
		respSttPieceHistory.SttPieceHistory,
		opName,
		sttDetail,
		params.TokenStr,
	})
	if err != nil {
		logger.Ef("[%s] error partnerCommodityGetDetailSttShipment : %v", opName, err)
		return nil, shared.ERR_UNEXPECTED_DB
	}

	// Last Deliv Reason Data
	lastDelivReasonCode, lastDelivReasonName, err := s.lastDelivReasonGetDetailSttShipment(ctx, opName, respSttPieceHistory.LastDelivReason)
	if err != nil {
		return nil, err
	}

	// Last Deliv Data
	respLastDeliv, err := s.lastDelivGetDetailSttShipment(ctx, params, respSttPieceHistory.LastDeliv, opName)
	if err != nil {
		return nil, err
	}

	estimatedSlaStartDate, estimatedSlaEndDate := s.estimatedSlaGetDetailSttShipment(ctx, params, sttDetail, sttMeta)

	return s.transformResponseGetDetailSttShipment(paramTransformResponseGetDetailSttShipment{
		SttDetail:             sttDetail,
		SttMeta:               sttMeta,
		PartnerTier:           partnerTier,
		EstimatedSlaStartDate: estimatedSlaStartDate,
		EstimatedSlaEndDate:   estimatedSlaEndDate,
		LastDelivReasonCode:   lastDelivReasonCode,
		LastDelivReasonName:   lastDelivReasonName,
		RespSttPieceHistory:   respSttPieceHistory,
		RespLastDeliv:         respLastDeliv,
		PartnerData:           partnerData,
		CommodityData:         commodityData,
		SttPieces:             sttPieces,
	}), nil
}

func transformRespShipmentGetDetailSttShipment(detailShipment *model.ShipmentAlgoResolutionCentre, commodity *model.Commodity) *stt.GetDetailSttShipmentShipment {
	return &stt.GetDetailSttShipmentShipment{
		SttNo:            detailShipment.SttNo,
		ShipmentID:       detailShipment.ShipmentID,
		LastStatus:       detailShipment.LastStatus,
		LastStatusAt:     detailShipment.LastStatusAt,
		CreatedAt:        detailShipment.CreatedAt,
		SenderName:       detailShipment.SenderName,
		SenderPhone:      detailShipment.SenderPhone,
		RecipientName:    detailShipment.RecipientName,
		RecipientPhone:   detailShipment.RecipientPhone,
		ProductType:      detailShipment.ProductType,
		DimLength:        detailShipment.DimLength,
		DimHeight:        detailShipment.DimHeight,
		DimWidth:         detailShipment.DimWidth,
		GrossWeight:      detailShipment.GrossWeight,
		ChargeableWeight: detailShipment.ChargeableWeight,
		VolumeWeight:     detailShipment.VolumeWeight,
		TotalPiece:       detailShipment.TotalPiece,
		IsCOD:            detailShipment.IsCOD,
		IsDFOD:           detailShipment.IsDFOD,
		IsInsurance:      detailShipment.IsInsurance,
		CodAmount:        detailShipment.CodAmount,
		GoodsPrice:       detailShipment.GoodsPrice,
		CommodityCode:    detailShipment.CommodityCode,
		CommodityName:    commodity.Data.CommodityName,
		CommodityIsDG:    commodity.Data.IsDangerousGoods,
		CommodityIsK:     commodity.Data.CommodityIsQuarantine,
	}
}

func detectFormatGetDetailSttShipment(s string) string {
	if shared.IsSttGenesisFormat(s) {
		return "STT"
	}
	if shared.IsShipmentIDFormat(s) {
		return "SHIPMENT"
	}
	return "UNKNOWN"
}

func (s *sttCtx) handlerGetDetailSttShipment(ctx context.Context, params *stt.GetDetailSttShipmentRequest, opName string) (*stt.GetDetailSttShipmentStt, *stt.GetDetailSttShipmentShipment, error) {
	var (
		err          error
		dataStt      *stt.GetDetailSttShipmentStt
		dataShipment *stt.GetDetailSttShipmentShipment
	)
	switch detectFormatGetDetailSttShipment(params.SttShipment) {
	case "STT":
		dataStt, err = s.getDataSTTHandlerGetDetailSttShipment(ctx, params, opName)
		if err != nil {
			logger.Ef("[%s] error getDataSTTHandlerGetDetailSttShipment : %v", opName, err)
			return nil, nil, err
		}
	case "SHIPMENT":
		detailShipment, err := s.shipmentRepo.GetShipmentAlgoResolutionCentreByID(ctx, params.SttShipment)
		if err != nil {
			logger.Ef("[%s] error GetShipmentAlgoResolutionCentreByID : %v", opName, err)
			return nil, nil, err
		}

		commodityDetail, _ := s.commodityRepo.GetCommodityByCode(ctx, detailShipment.CommodityCode, params.TokenStr)
		if commodityDetail == nil {
			commodityDetail = &model.Commodity{}
		}
		dataShipment = transformRespShipmentGetDetailSttShipment(detailShipment, commodityDetail)
	}

	return dataStt, dataShipment, err
}
