package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/Lionparcel/go-lptool/lputils"
	"strings"

	"github.com/Lionparcel/hydra/src/model"
)

func (c *gatewaySttTrackingCtx) SttTracking() {

}

func (c *gatewaySttTrackingCtx) generateSttTrackingDescriptionForClient(status string, hisID int, statusPieceWithArrayData map[int][]model.SttPieceHistory, token string, productType string, countryName, accountType, countryNameHistory string) string {
	var statusHistory string = ""
	if len(statusPieceWithArrayData[hisID]) < 1 {
		return statusHistory
	}

	switch status {
	case model.BKD:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu telah diproses oleh Agen Lion Parcel %s, %s`, cityName, districtName)
	case model.PUP, model.PUPC:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu akan diantar ke Gudang Lion Parcel %s, %s`, cityName, districtName)
	case model.STISC:
		cityName := ""
		districtName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Transit Lion Parcel %s, %s`, cityName, districtName)
	case model.STI:
		cityName := ""
		districtName := ""

		hubId := 0
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
			hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
			hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
			hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
		}

		if hubId != 0 {
			cityName = hubCityName
			districtName = hubDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Lion Parcel %s, %s`, cityName, districtName)
	case model.CARGOPLANE:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		hubId := 0
		hubCityName := ""
		hubDistrictName := ""

		// if stt piece history with status INTHND exist, overwrite city and district
		for _, sttPieceHistory := range statusPieceWithArrayData {
			if sttPieceHistory[0].HistoryStatus == model.INTHND && sttPieceHistory[0].RemarkPieceHistoryToStruct() != nil {
				cityName = sttPieceHistory[0].RemarkPieceHistoryToStruct().HistoryLocationName
				districtName = sttPieceHistory[0].RemarkPieceHistoryToStruct().HistoryDistrictName

				hubId = sttPieceHistory[0].RemarkPieceHistoryToStruct().HubID
				hubCityName = sttPieceHistory[0].RemarkPieceHistoryToStruct().HubCityName
				hubDistrictName = sttPieceHistory[0].RemarkPieceHistoryToStruct().HubDistrictName
				break
			}
		}

		if hubId != 0 {
			districtName = hubDistrictName
			cityName = hubCityName
		}

		statusHistory = fmt.Sprintf(`Paketmu akan diterbangkan dengan pesawat dari Kota %s, %s`, cityName, districtName)
	case model.CARGOTRUCK:
		historyLocationName := ""
		districtName := ""

		hubId := 0
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
			hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
			hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
			hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
		}

		if hubId != 0 {
			districtName = hubDistrictName
			historyLocationName = hubCityName
		}

		if productType == model.INTERPACK {
			statusHistory = fmt.Sprintf(`Paketmu akan diberangkatkan ke Negara %s dari Kota %s, %s`, countryName, historyLocationName, districtName)
		} else {
			statusHistory = fmt.Sprintf(`Paketmu akan diberangkatkan dengan truk dari Kota %s, %s`, historyLocationName, districtName)
		}

	case model.CARGOTRAIN:
		cityName := ""
		districtName := ""

		hubId := 0
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName

			hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
			hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
			hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
		}

		if hubId != 0 {
			districtName = hubDistrictName
			cityName = hubCityName
		}

		statusHistory = fmt.Sprintf(`Paketmu akan diberangkatkan dengan kereta dari Kota %s, %s `, cityName, districtName)
	case model.CARGOSHIP:
		cityName := ""
		districtName := ""

		hubId := 0
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName

			hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
			hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
			hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
		}

		if hubId != 0 {
			districtName = hubDistrictName
			cityName = hubCityName
		}

		statusHistory = fmt.Sprintf(`Paketmu akan diberangkatkan dengan kapal dari Kota %s, %s `, cityName, districtName)
	case model.TRANSIT:
		statusHistory = `Paketmu sedang transit`
	case model.STIDEST:
		cityName := ""
		districtName := ""

		hubId := 0
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName

			hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
			hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
			hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
		}

		if hubId != 0 {
			districtName = hubDistrictName
			cityName = hubCityName
		}

		if productType == model.INTERPACK {
			statusHistory = fmt.Sprintf("Paket telah sampai di Negara %s", countryName)
		} else {
			statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Lion Parcel %s, %s`, cityName, districtName)
		}

	case model.STIDESTSC:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Transit Lion Parcel %s, %s`, cityName, districtName)
	case model.DEL:
		driverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			driverName = historyRemark.DriverName
		}

		if productType == model.INTERPACK {
			statusHistory = "Paketmu diantar ke alamat penerima oleh Kurir. Pastikan nomor penerima dapat dihubungi oleh kurir"
		} else {
			switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
			case model.VENDOR:
				statusHistory = `Paketmu diantar oleh kurir ke alamat penerima. Pastikan kamu dapat dihubungi oleh kurir`
			default:
				statusHistory = fmt.Sprintf(`Paketmu diantar ke alamat penerima oleh kurir %s. Pastikan nomor penerima dapat dihubungi oleh kurir`, driverName)
			}
		}

	case model.POD:
		receiverName := ""
		for _, val := range statusPieceWithArrayData[hisID] {
			historyRemark := &model.RemarkPieceHistory{}
			if val.HistoryRemark != `` {
				json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
			}
			receiverName = historyRemark.ReceiverName
		}

		if productType == model.INTERPACK {
			statusHistory = "Paketmu telah sampai di tujuan & diterima"
		} else {
			statusHistory = fmt.Sprintf(`Paketmu telah sampai di tujuan & diterima oleh  %s`, receiverName)
		}
	case model.DEX:
		res := &model.ReasonDetailResult{}
		reasonDescription := ""
		if statusPieceWithArrayData[hisID][0].HistoryReason != `` {
			if statusPieceWithArrayData[hisID][0].HistoryReason != model.AnotherReason {
				res, _ = c.reasonRepo.GetDetail(context.Background(), &model.ReasonViewParams{
					ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
				})
				reasonDescription = res.ReasonDescription
			} else {
				for _, val := range statusPieceWithArrayData[hisID] {
					historyRemark := &model.RemarkPieceHistory{}
					if val.HistoryRemark != `` {
						json.Unmarshal([]byte(val.HistoryRemark), historyRemark)
					}
					reasonDescription = historyRemark.OtherReason
				}
			}
		}

		statusHistory = fmt.Sprintf(`Paketmu gagal diantarkan karena %s. Percobaan pengiriman ulang akan dilakukan secara berkala. Pastikan alamat dan kontak Penerima sudah sesuai.`, reasonDescription)

	case model.CODREJ:
		res := &model.ReasonDetailResult{}
		if statusPieceWithArrayData[hisID][0].HistoryReason != `` {
			res, _ = c.reasonRepo.GetDetail(context.Background(), &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})
		}
		if res != nil {
			statusHistory = fmt.Sprintf(`Paket COD mu dikembalikan ke alamat pengirim karena %s.`, res.ReasonDescription)
		}
	case model.HAL:
		cityName := ""
		districtName := ""

		historyReason := ""
		hubId := 0
		hubCityName := ""
		hubDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName

			hubId = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubID
			hubCityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubCityName
			hubDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HubDistrictName
		}

		if hubId != 0 {
			districtName = hubDistrictName
			cityName = hubCityName
		}

		switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
		case model.VENDOR:
			statusHistory = fmt.Sprintf(`Paketmu berada di Gudang Lion Parcel %s, %s.`, cityName, districtName)
		default:
			statusHistory = fmt.Sprintf(`Paketmu berada di Gudang Lion Parcel %s, %s karena %s.`, cityName, districtName, historyReason)
		}

	case model.RTS:
		if productType == model.INTERPACK {
			statusHistory = "Paketmu dikembalikan ke alamat pengirim karena alasan tertentu."
		} else {
			switch statusPieceWithArrayData[hisID][0].HistoryActorRole {
			case model.VENDOR:
				statusHistory = `Paketmu dikembalikan ke alamat pengirim karena alasan tertentu.`
			default:
				newSttNumber := ""
				if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
					newSttNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
				}
				statusHistory = fmt.Sprintf(`Paketmu akan dikembalikan ke alamat pengirim dengan nomor resi baru %s.`, newSttNumber)
				if accountType != model.CLIENT {
					statusHistory = fmt.Sprintf(`Paket akan dikembalikan ke pengirim dengan nomor STT baru %s`, newSttNumber)
				}
			}
		}
	case model.CLAIM:
		claimNumber := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			claimNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().ClaimNo
		}

		statusHistory = fmt.Sprintf(`Paketmu dalam proses klaim. Silahkan cek nomor klaim kamu %s untuk detail klaim`, claimNumber)
	case model.CNX:
		res := &model.ReasonDetailResult{}
		sttNoReference := ""
		if statusPieceWithArrayData[hisID][0].HistoryReason != `` {
			res, _ = c.reasonRepo.GetDetail(context.Background(), &model.ReasonViewParams{
				ReasonCode: statusPieceWithArrayData[hisID][0].HistoryReason,
			})

			if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
				sttNoReference = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().SttNoReference
			}
		}
		if res != nil {
			statusHistory = fmt.Sprintf(`Pengiriman paketmu telah dibatalkan karena alasan %s.`, res.ReasonDescription)

			if sttNoReference != `` {
				statusHistory += fmt.Sprintf(` Cek nomor resi terbaru %s.`, sttNoReference)
			}
		}
	case model.STTADJUSTED:
		statusHistory = "Terjadi penyesuaian harga terkait berat & volume paketmu"
	case model.STTADJUSTEDPOD:
		statusHistory = "Terjadi penyesuaian harga terkait berat & volume paketmu"
	case model.PICKUPTRUCKING:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu telah diberangkatkan dengan truk dari Kota %s, %s`, cityName, districtName)
	case model.DROPOFFTRUCKING:
		cityName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			cityName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		districtName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			districtName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu telah sampai dengan truk di Kota %s, %s`, cityName, districtName)
	case model.REJECTED:
		historyReason := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

		if len(historyReason) > 0 {
			ctx, cancel := lputils.CreateContext(30)
			defer cancel()

			reason, _ := c.reasonRepo.GetDetail(ctx, &model.ReasonViewParams{
				ReasonTitle: historyReason,
			})
			if reason != nil {
				historyReason = reason.ReasonDescription
			}
		}

		if productType == model.INTERPACK {
			statusHistory = fmt.Sprintf("Paketmu akan dialihkan menggunakan armada lain karena %s.", historyReason)
		} else {
			statusHistory = fmt.Sprintf("Paketmu akan dialihkan menggunakan armada lain karena %s.", historyReason)
		}
	case model.NOTRECEIVED:
		statusHistory = "Pengiriman paketmu masih dalam proses."
	case model.DAMAGE:
		if productType == model.INTERPACK {
			statusHistory = "Pengiriman paketmu mengalami kendala karena alasan tertentu."
		} else {
			statusHistory = "Pengiriman paketmu mengalami kendala."
		}
	case model.MISSING:
		historyReason := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		if productType == model.INTERPACK {
			statusHistory = "Pengiriman paketmu mengalami kendala karena alasan tertentu."
		} else {
			statusHistory = fmt.Sprintf("Pengiriman paketmu mengalami kendala karena %s.", historyReason)
		}
	case model.RTSHQ:
		newSttNumber := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			newSttNumber = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

		statusHistory = fmt.Sprintf(`Paket dikembalikan ke Lion Parcel Pusat dengan nomor resi baru %s.`, newSttNumber)
		if accountType != model.CLIENT {
			statusHistory = fmt.Sprintf(`Paket dikembalikan ke Lion Parcel Pusat dengan nomor STT baru %s`, newSttNumber)
		}
	case model.OCC:
		countryDestination := ""
		data, err := c.cityRepo.Get(context.Background(), statusPieceWithArrayData[hisID][0].HistoryLocation, token)
		if err == nil && data != nil {
			dataCountry, err := c.countryRepo.Get(context.Background(), data.CountryID, token)
			if err == nil && dataCountry != nil {
				countryDestination = dataCountry.Name
			}
		}

		if productType == model.INTERPACK {
			statusHistory = fmt.Sprintf(`Paketmu dalam proses pengecekan Bea Cukai untuk dikirim ke Negara %s.`, countryName)
		} else {
			statusHistory = fmt.Sprintf(`Paketmu dalam proses pengecekan Bea Cukai untuk dikirim ke tujuan %s.`, countryDestination)
		}
	case model.REROUTE:
		newSttNumber := ""
		history := statusPieceWithArrayData[hisID][0]
		remarkHistory := history.RemarkPieceHistoryToStruct()
		if remarkHistory != nil {
			newSttNumber = remarkHistory.CustomProcessRemarks
		}
		statusHistory = fmt.Sprintf(`Paketmu akan dikirim kembali ke alamat tujuan yang sesuai dengan nomor resi terbaru %s`, newSttNumber)
	case model.MISBOOKING:
		statusHistory = `Terjadi kesalahan pada paketmu saat diproses Agen.`
	case model.SCRAPCD:
		statusHistory = `STT Dimusnahkan karena bagging telah diterima`
	case model.HALCD:
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			statusHistory = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

	case model.CNXCD:
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			statusHistory = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}

	case model.INTSTI:
		cityINTHND := ``
		districtINTHND := ``

		for _, sttPieceHistory := range statusPieceWithArrayData {
			if sttPieceHistory[0].HistoryStatus == model.INTHND && sttPieceHistory[0].RemarkPieceHistoryToStruct() != nil {
				cityINTHND = sttPieceHistory[0].RemarkPieceHistoryToStruct().HistoryLocationName
				districtINTHND = sttPieceHistory[0].RemarkPieceHistoryToStruct().HistoryDistrictName
				break
			}
		}

		statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Transit Lion Parcel %s, %s`, cityINTHND, districtINTHND)
	case model.OCCEXP:
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		statusHistory = fmt.Sprintf(`Paketmu sedang diperiksa Bea Cukai untuk dikirim ke negara %s`, historyLocationName)
		if strings.ToUpper(statusPieceWithArrayData[hisID][0].HistoryActorName) != model.LUWJISTIC && statusPieceWithArrayData[hisID][0].HistoryActorRole != model.VENDOR {
			statusHistory = fmt.Sprintf(`Paketmu sedang diperiksa Bea Cukai untuk dikirim ke negara %s`, countryName)
		}
	case model.OCCIMP:
		historyLocationName := ""
		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
		}
		statusHistory = fmt.Sprintf(`Paketmu telah selesai diperiksa Bea Cukai dan telah diterima di negara %s`, historyLocationName)
		if strings.ToUpper(statusPieceWithArrayData[hisID][0].HistoryActorName) != model.LUWJISTIC && statusPieceWithArrayData[hisID][0].HistoryActorRole != model.VENDOR {
			statusHistory = fmt.Sprintf(`Paketmu telah selesai diperiksa Bea Cukai dan telah diterima di negara %s`, countryName)
		}
	case model.OCCHAL:
		historyLocationName := ""
		historyReason := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			historyReason = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
		}
		statusHistory = fmt.Sprintf(`Paketmu masih berada di Gudang Lion Parcel %s`, historyLocationName)
		if strings.ToUpper(statusPieceWithArrayData[hisID][0].HistoryActorName) != model.LUWJISTIC && statusPieceWithArrayData[hisID][0].HistoryActorRole != model.VENDOR {
			statusHistory = fmt.Sprintf(`Paketmu berada di Gudang Lion Parcel %s %s karena %s`, countryNameHistory, historyLocationName, historyReason)
		}
	case model.INHUB:
		historyLocationName := ""
		historyDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			historyDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Lion Parcel %s, %s`, historyLocationName, historyDistrictName)

		if len(statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks) != 0 {
			vehicleNumber := statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
			statusHistory = fmt.Sprintf(`%s dengan nomor kendaraan %s`, statusHistory, vehicleNumber)
		}

	case model.OUTHUB:
		historyLocationName := ""
		historyDistrictName := ""

		if statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct() != nil {
			historyLocationName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryLocationName
			historyDistrictName = statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().HistoryDistrictName
		}

		statusHistory = fmt.Sprintf(`Paketmu telah diberangkatkan dari Gudang Lion Parcel %s, %s`, historyLocationName, historyDistrictName)

		if len(statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks) != 0 {
			vehicleNumber := statusPieceWithArrayData[hisID][0].RemarkPieceHistoryToStruct().CustomProcessRemarks
			statusHistory = fmt.Sprintf(`%s dengan nomor kendaraan %s`, statusHistory, vehicleNumber)
		}
	case model.KONDISPATCH:
		statusHistory = `Paketmu telah ditugaskan ke kurir dan siap diantar.`
	}

	return c.buildNewStatusHistory(status, statusHistory)
}

func (c *gatewaySttTrackingCtx) buildNewStatusHistory(status string, statusHistory string) string {
	switch status {
	case model.BAGGING:
		statusHistory = `Paket disortir`
	case model.SCRAP:
		statusHistory = `STT Dimusnahkan`
	}
	return statusHistory
}
