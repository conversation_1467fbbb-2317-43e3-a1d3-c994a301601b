package usecase

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/Lionparcel/go-lptool/v2/lputils"
	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/shared"
	shmock "github.com/Lionparcel/hydra/shared/mocks"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/repository"
	"github.com/Lionparcel/hydra/src/repository/mocks"
	"github.com/Lionparcel/hydra/src/usecase/custom_process"
	customProcess "github.com/Lionparcel/hydra/src/usecase/custom_process"
	"github.com/Lionparcel/hydra/src/usecase/gateway_stt"
	"github.com/Lionparcel/hydra/src/usecase/gateway_stt_status"
	"github.com/Lionparcel/hydra/src/usecase/general"
	ucmock "github.com/Lionparcel/hydra/src/usecase/mocks"
	"github.com/Lionparcel/hydra/src/usecase/stt_activity"
	"github.com/abiewardani/dbr/v2"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
)

type CustomProcessUsecaseTestSuite struct {
	suite.Suite
	ctx                     context.Context
	customProcessRepo       *mocks.CustomProcessRepository
	partnerRepo             *mocks.PartnerRepository
	sttPieceRepo            *mocks.SttPiecesRepository
	commodityRepo           *mocks.CommodityRepository
	districtRepo            *mocks.DistrictRepository
	cityRepo                *mocks.CityRepository
	gatewaySttStatusUc      *ucmock.GatewaySttStatus
	middlewareRepo          *mocks.MiddlewareCLient
	sttActivityUc           *ucmock.SttActivity
	sttPieceHistoryRepo     *mocks.SttPieceHistoryRepository
	deliveryRepo            *mocks.DeliveryRepository
	customProcessRoleRepo   *mocks.CustomProcessRoleRepository
	messageGatewayUc        *ucmock.MessageGateway
	sttPaymentUc            *ucmock.SttPayment
	accountRepo             *mocks.AccountRepository
	sttUc                   *ucmock.Stt
	clientRepo              *mocks.ClientRepository
	partnerLog              *mocks.PartnerLogRepository
	reasonRepo              *mocks.ReasonRepository
	sttRepo                 *mocks.SttRepository
	productRepo             *mocks.ProductTypeRepository
	repoCommodity           *mocks.CommodityRepository
	bagRepo                 *mocks.BagRepository
	bagVendorRepo           *mocks.BagVendorRepository
	shipmentRepo            *mocks.ShipmentRepository
	sttOptionalRateRepo     *mocks.SttOptionalRateRepository
	DictionaryError         *shmock.DictionaryError
	gatewaySttUc            *ucmock.GatewayStt
	usecase                 CustomProcess
	flagManagementRepo      *mocks.FlagManagementRepository
	timeRepo                *mocks.TimeRepository
	rtcUc                   ReadyToCargo
	requestPriorityDelivery RequestPriorityDelivery
	priorityDeliveryRepo    *mocks.PriorityDeliveryRepository
	sttDueRepo              *mocks.SttDueRepository
	deliveryManifestRepo    *mocks.DeliveryManifestRepository
}

func (s *CustomProcessUsecaseTestSuite) SetupTest() {
	s.customProcessRepo = &mocks.CustomProcessRepository{}
	s.partnerRepo = &mocks.PartnerRepository{}
	s.sttPieceRepo = &mocks.SttPiecesRepository{}
	s.commodityRepo = &mocks.CommodityRepository{}
	s.districtRepo = &mocks.DistrictRepository{}
	s.cityRepo = &mocks.CityRepository{}
	s.gatewaySttStatusUc = &ucmock.GatewaySttStatus{}
	s.middlewareRepo = &mocks.MiddlewareCLient{}
	s.sttActivityUc = &ucmock.SttActivity{}
	s.sttPieceHistoryRepo = &mocks.SttPieceHistoryRepository{}
	s.deliveryRepo = &mocks.DeliveryRepository{}
	s.customProcessRoleRepo = &mocks.CustomProcessRoleRepository{}
	s.messageGatewayUc = &ucmock.MessageGateway{}
	s.sttPaymentUc = &ucmock.SttPayment{}
	s.accountRepo = &mocks.AccountRepository{}
	s.sttUc = &ucmock.Stt{}
	s.clientRepo = &mocks.ClientRepository{}
	s.partnerLog = &mocks.PartnerLogRepository{}
	s.reasonRepo = &mocks.ReasonRepository{}
	s.sttRepo = &mocks.SttRepository{}
	s.productRepo = &mocks.ProductTypeRepository{}
	s.repoCommodity = &mocks.CommodityRepository{}
	s.bagRepo = &mocks.BagRepository{}
	s.bagVendorRepo = &mocks.BagVendorRepository{}
	s.shipmentRepo = &mocks.ShipmentRepository{}
	s.gatewaySttUc = &ucmock.GatewayStt{}
	s.sttOptionalRateRepo = &mocks.SttOptionalRateRepository{}
	s.flagManagementRepo = &mocks.FlagManagementRepository{}
	s.timeRepo = &mocks.TimeRepository{}
	s.DictionaryError = &shmock.DictionaryError{}
	s.rtcUc = &ucmock.ReadyToCargo{}
	s.requestPriorityDelivery = &ucmock.RequestPriorityDelivery{}
	s.priorityDeliveryRepo = &mocks.PriorityDeliveryRepository{}
	s.sttDueRepo = &mocks.SttDueRepository{}
	s.deliveryManifestRepo = &mocks.DeliveryManifestRepository{}

	s.usecase = NewCustomProcess(s.customProcessRepo,
		s.partnerRepo, s.sttPieceRepo,
		s.commodityRepo, s.districtRepo, s.cityRepo,
		s.gatewaySttStatusUc, s.middlewareRepo, s.sttActivityUc,
		s.sttPieceHistoryRepo, s.deliveryRepo, s.customProcessRoleRepo,
		s.messageGatewayUc, s.sttPaymentUc, s.accountRepo, s.sttUc, &config.Config{},
		s.clientRepo, s.partnerLog, s.reasonRepo,
		s.sttRepo, s.productRepo, s.repoCommodity,
		s.bagRepo, s.bagVendorRepo, s.shipmentRepo, s.sttOptionalRateRepo,
		s.DictionaryError, s.gatewaySttUc, s.flagManagementRepo, s.timeRepo,
		s.rtcUc, s.requestPriorityDelivery, s.priorityDeliveryRepo, s.sttDueRepo, s.deliveryManifestRepo)
	ctxBg, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()
	s.ctx = ctxBg
}

func TestCustomProcessUsecase(t *testing.T) {
	suite.Run(t, new(CustomProcessUsecaseTestSuite))
}

func Test_customProcessCtx_CreateCustomProcess(t *testing.T) {
	t.SkipNow()
	type args struct {
		ctx    context.Context
		params *customProcess.CreateCustomProcessRequest
	}

	ctxBg, cancel := lputils.CreateContext(60) // set timeout 60 seconds
	defer cancel()

	tests := []struct {
		name       string
		args       args
		want       *customProcess.CreateCustomProcessResponse
		wantErr    bool
		errResp    error
		beforeFunc func() *customProcessCtx
	}{
		{
			name: "Test_customProcessCtx_CreateCustomProcess-ErrorSttPieceNotFound",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.PARTNER,
					PartnerID:           1,
					PartnerName:         "Tes",
					PartnerType:         model.CONSOLE,
					SttBookedBy:         2,
					CustomProcessStatus: model.HALCD,
					SttNo:               []string{"98LP2514720183684"},
					Remarks:             "hehe",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			wantErr: true,
			errResp: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt is not found",
				"id": "Stt tidak ditemukan",
			}),
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockGatewaySttStatusUc := new(ucmock.GatewaySttStatus)
				mockSttPieceHistory := new(mocks.SttPieceHistoryRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					districtRepo:          mockDistrictRepo,
					gatewaySttStatusUc:    mockGatewaySttStatusUc,
					sttPieceHistoryRepo:   mockSttPieceHistory,
					sttDueRepo:            mockSttDueRepo,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.HALCD,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, mock.Anything).Return(nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"98LP2514720183684"},
					IsNeedDecrypt: true,
				}).Return([]model.SttDetailResult{}, fmt.Errorf("error")).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-ErrorCNXCD",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.PARTNER,
					PartnerID:           1,
					PartnerName:         "Tes",
					PartnerType:         model.CONSOLE,
					SttBookedBy:         2,
					CustomProcessStatus: model.CNXCD,
					SttNo:               []string{"98LP2514720183684"},
					Remarks:             "hehe",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed: []general.STTFailedGeneralResponse{
					{
						SttNo: "98LP2514720183684",
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockSttOptionalRateRepo := new(mocks.SttOptionalRateRepository)
				mockGatewaySttStatusUc := new(ucmock.GatewaySttStatus)
				mockSttPieceHistory := new(mocks.SttPieceHistoryRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					districtRepo:          mockDistrictRepo,
					gatewaySttStatusUc:    mockGatewaySttStatusUc,
					sttOptionalRateRepo:   mockSttOptionalRateRepo,
					sttPieceHistoryRepo:   mockSttPieceHistory,
					sttDueRepo:            mockSttDueRepo,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.CNXCD,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, mock.Anything).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:            "98LP2514720183684",
							SttNoRefExternal: "TKP01-1698006902",
							SttLastStatusID:  model.HALCD,
							SttBookedForType: model.CLIENT,
							SttIsCOD:         true,
							SttMeta:          `{"estimate_sla":"3 - 4 Hari","origin_city_name":"JAKARTA","origin_district_name":"KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT","destination_city_name":"JOGJAKARTA","destination_district_name":"DEPOK, SLEMAN","ticket_code":"TKP01-1698006902","other_shipper_ticket_code":[{"ticket_code":"TKP01-1698006902","shipper":"TOKOPEDIA","estimate_sla":"","origin_city_name":"","origin_district_name":"","destination_city_name":"","destination_district_name":"","booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":""},{"ticket_code":"TGAA-1698006902-1","shipper":"ANTERAJA","estimate_sla":"","origin_city_name":"","origin_district_name":"","destination_city_name":"","destination_district_name":"","booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":""},{"ticket_code":"TKP01-1698006902","shipper":"LION","estimate_sla":"","origin_city_name":"","origin_district_name":"","destination_city_name":"","destination_district_name":"","booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":""}],"booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":"","retail_tariff":{"city_rates":108500,"forward_rates":32000,"chargeable_weight":22,"gross_weight":21.9,"volume_weight":11,"shipping_cost":140500,"commodity_surcharge":0,"heavy_weight_surcharge":21700,"document_surcharge":0,"insurance_rates":0,"insurance_name":"Insurance Free","woodpacking_rates":0,"total_tariff":162200,"tax_rates":0,"bm_tax_rate":0,"ppn_tax_rate":0,"pph_tax_rate":0,"origin_district_rate":10000,"destination_district_rate":22000,"publish_rate":78000,"shipping_surcharge_rate":30500,"heavy_weight_surcharge_remarks":"[{\"heavy_weight_surcharge\":21700,\"gross_weight\":21.9}]","estimate_sla":"3 - 4 Hari","cod_amount":0,"cod_fee":0,"total_tariff_before_discount":162200,"cod_booking_discount":0,"is_discount_exceed_max_promo":false,"is_promo":false,"discount_type":"","discount":0,"parameter_calculation":"","total_discount":0,"publish_rate_after_discount":78000,"shipping_surcharge_rate_after_discount":30500,"origin_district_rate_after_discount":10000,"destination_district_rate_after_discount":22000,"document_surcharge_after_discount":0,"commodity_surcharge_after_discount":0,"heavy_weight_surcharge_after_discount":21700,"woodpacking_rates_after_discount":0,"insurance_rates_after_discount":0,"cod_fee_after_discount":0,"total_tariff_after_discount":162200,"total_surcharge_after_discount":0},"detail_calculate_retail_tariff":[{"status":"BKD","is_calculated":true,"calculated_at":"2023-10-23T03:35:05.451307439+07:00"}],"is_stt_crossdocking":false,"client_payment_method":"","client_cod_config_amount":"","client_cod_shipment_discount":0,"rate_vat_shipment":0,"rate_vat_cod":0,"total_tariff_return":0}`,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"98LP2514720183684"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						Name:               "test",
						Type:               model.CONSOLE,
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				mockSttPieceHistory.On("Select", mock.Anything, mock.Anything).Return([]model.SttPieceHistory{
					{
						HistoryStatus: model.BKD,
						HistoryRemark: `{
							"client_cod_booking_discount":1000
						  }`,
					},
					{
						HistoryStatus: model.HALCD,
					},
				}, nil).Once()

				mockSttOptionalRateRepo.On("Select", mock.Anything, &model.SttOptionalRate{SttOptionalRateSttID: sttDetail[0].SttID}).Return([]model.SttOptionalRate{
					{
						SttOptionalRateParams: model.WOODPACKING,
						SttOptionalRateRate:   10,
					},
					{
						SttOptionalRateParams: model.INSURANCE,
						SttOptionalRateRate:   10,
					},
				}, nil).Once()

				mockGatewaySttStatusUc.On("RefundCancelBooking", mock.Anything, mock.Anything).Return(nil)

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-ErrorInternalCNXCD",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerName:         "Tes",
					PartnerType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.CNXCD,
					SttNo:               []string{"98LP2514720183684"},
					Remarks:             "hehe",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed: []general.STTFailedGeneralResponse{
					{
						SttNo: "98LP2514720183684",
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockSttOptionalRateRepo := new(mocks.SttOptionalRateRepository)
				mockGatewaySttStatusUc := new(ucmock.GatewaySttStatus)
				mockSttPieceHistory := new(mocks.SttPieceHistoryRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					districtRepo:          mockDistrictRepo,
					gatewaySttStatusUc:    mockGatewaySttStatusUc,
					sttOptionalRateRepo:   mockSttOptionalRateRepo,
					sttPieceHistoryRepo:   mockSttPieceHistory,
					sttDueRepo:            mockSttDueRepo,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.CNXCD,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, mock.Anything).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:            "98LP2514720183684",
							SttNoRefExternal: "TKP01-1698006902",
							SttLastStatusID:  model.HALCD,
							SttBookedForType: model.CLIENT,
							SttIsCOD:         true,
							SttMeta:          `{"estimate_sla":"3 - 4 Hari","origin_city_name":"JAKARTA","origin_district_name":"KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT","destination_city_name":"JOGJAKARTA","destination_district_name":"DEPOK, SLEMAN","ticket_code":"TKP01-1698006902","other_shipper_ticket_code":[{"ticket_code":"TKP01-1698006902","shipper":"TOKOPEDIA","estimate_sla":"","origin_city_name":"","origin_district_name":"","destination_city_name":"","destination_district_name":"","booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":""},{"ticket_code":"TGAA-1698006902-1","shipper":"ANTERAJA","estimate_sla":"","origin_city_name":"","origin_district_name":"","destination_city_name":"","destination_district_name":"","booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":""},{"ticket_code":"TKP01-1698006902","shipper":"LION","estimate_sla":"","origin_city_name":"","origin_district_name":"","destination_city_name":"","destination_district_name":"","booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":""}],"booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":"","retail_tariff":{"city_rates":108500,"forward_rates":32000,"chargeable_weight":22,"gross_weight":21.9,"volume_weight":11,"shipping_cost":140500,"commodity_surcharge":0,"heavy_weight_surcharge":21700,"document_surcharge":0,"insurance_rates":0,"insurance_name":"Insurance Free","woodpacking_rates":0,"total_tariff":162200,"tax_rates":0,"bm_tax_rate":0,"ppn_tax_rate":0,"pph_tax_rate":0,"origin_district_rate":10000,"destination_district_rate":22000,"publish_rate":78000,"shipping_surcharge_rate":30500,"heavy_weight_surcharge_remarks":"[{\"heavy_weight_surcharge\":21700,\"gross_weight\":21.9}]","estimate_sla":"3 - 4 Hari","cod_amount":0,"cod_fee":0,"total_tariff_before_discount":162200,"cod_booking_discount":0,"is_discount_exceed_max_promo":false,"is_promo":false,"discount_type":"","discount":0,"parameter_calculation":"","total_discount":0,"publish_rate_after_discount":78000,"shipping_surcharge_rate_after_discount":30500,"origin_district_rate_after_discount":10000,"destination_district_rate_after_discount":22000,"document_surcharge_after_discount":0,"commodity_surcharge_after_discount":0,"heavy_weight_surcharge_after_discount":21700,"woodpacking_rates_after_discount":0,"insurance_rates_after_discount":0,"cod_fee_after_discount":0,"total_tariff_after_discount":162200,"total_surcharge_after_discount":0},"detail_calculate_retail_tariff":[{"status":"BKD","is_calculated":true,"calculated_at":"2023-10-23T03:35:05.451307439+07:00"}],"is_stt_crossdocking":false,"client_payment_method":"","client_cod_config_amount":"","client_cod_shipment_discount":0,"rate_vat_shipment":0,"rate_vat_cod":0,"total_tariff_return":0}`,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"98LP2514720183684"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						Name:               "test",
						Type:               model.CONSOLE,
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				mockSttPieceHistory.On("Select", mock.Anything, mock.Anything).Return([]model.SttPieceHistory{
					{
						HistoryStatus: model.BKD,
						HistoryRemark: `{
							"client_cod_booking_discount":1000
						  }`,
					},
					{
						HistoryStatus: model.HALCD,
					},
				}, nil).Once()

				mockSttOptionalRateRepo.On("Select", mock.Anything, &model.SttOptionalRate{SttOptionalRateSttID: sttDetail[0].SttID}).Return([]model.SttOptionalRate{
					{
						SttOptionalRateParams: model.WOODPACKING,
						SttOptionalRateRate:   10,
					},
					{
						SttOptionalRateParams: model.INSURANCE,
						SttOptionalRateRate:   10,
					},
				}, nil).Once()

				mockGatewaySttStatusUc.On("RefundCancelBooking", mock.Anything, mock.Anything).Return(nil)

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-FailedStt_SttID0_CARGOPLANE-REJECT",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					CustomProcessStatus: model.REJECTED,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed: []general.STTFailedGeneralResponse{
					{},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.REJECTED,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "11LP16***********",
							SttLastStatusID: model.CARGOPLANE,
						},
					},
				}, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},

		{
			name: "Test_customProcessCtx_CreateCustomProcess-CNXCD-sttNotTKP01",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					CustomProcessStatus: model.CNXCD,
					SttNo:               []string{"98LP2514720218976"},
					Remarks:             "RESXX",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want:    nil,
			wantErr: true,
			errResp: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt No Cannot Be Proccess Cause STT is not TKP01",
				"id": "Nomor STT tidak dapat diproses karena STT bukan TKP01",
			}),
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.CNXCD,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"98LP2514720218976"}).Return(nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"98LP2514720218976"},
					IsNeedDecrypt: true,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:            "98LP2514720218976",
							SttLastStatusID:  model.BKD,
							SttNoRefExternal: "",
						},
					},
				}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-CNXCD-LastStatusNotHALCD",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					CustomProcessStatus: model.CNXCD,
					SttNo:               []string{"98LP2514720218976"},
					Remarks:             "RESXX",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want:    nil,
			wantErr: true,
			errResp: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt No Cannot Be Proccess Cause last status not HALCD",
				"id": "Nomor STT tidak dapat diproses karena status akhir bukan HALCD",
			}),
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.CNXCD,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"98LP2514720218976"}).Return(nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"98LP2514720218976"},
					IsNeedDecrypt: true,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:            "98LP2514720218976",
							SttLastStatusID:  model.CNX,
							SttNoRefExternal: "TKP01-MD00005",
						},
					},
				}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-LastStatusCNXCD",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					CustomProcessStatus: model.RTSHQ,
					SttNo:               []string{"98LP2514720218976"},
					Remarks:             "RESXX",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want:    nil,
			wantErr: true,
			errResp: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt Cannot Be Processed Cause CNXCD status is the final status",
				"id": "Nomor STT tidak dapat diproses Karena status CNXCD adalah status akhir",
			}),
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.RTSHQ,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"98LP2514720218976"}).Return(nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"98LP2514720218976"},
					IsNeedDecrypt: true,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:            "98LP2514720218976",
							SttLastStatusID:  model.CNXCD,
							SttNoRefExternal: "TKP01-MD00005",
						},
					},
				}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},

		{
			name: "Test_customProcessCtx_CreateCustomProcess-HALCD-reasonNotFound",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					CustomProcessStatus: model.HALCD,
					SttNo:               []string{"98LP2514720218976"},
					Remarks:             "hehe",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want:    nil,
			wantErr: true,
			errResp: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Remarks is not valid",
				"id": "Remarks tidak valid",
			}),
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockReasonRepo := new(mocks.ReasonRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					reasonRepo:            mockReasonRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.HALCD,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"98LP2514720218976"}).Return(nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"98LP2514720218976"},
					IsNeedDecrypt: true,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:            "98LP2514720218976",
							SttLastStatusID:  model.BKD,
							SttNoRefExternal: "TKP01-MD00005",
						},
					},
				}, nil).Once()

				mockReasonRepo.On("GetDetail", mock.Anything, mock.Anything).Return(nil, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-HALCD-lasStatuIsBagging",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					CustomProcessStatus: model.HAL,
					SttNo:               []string{"98LP2514720218976"},
					Remarks:             "RESXX",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want:    nil,
			wantErr: true,
			errResp: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt No Cannot Be Proccess",
				"id": "Nomor STT atau Nomor Bagging tidak dapat diproses",
			}),
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockReasonRepo := new(mocks.ReasonRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					reasonRepo:            mockReasonRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.HAL,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"98LP2514720218976"}).Return(nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"98LP2514720218976"},
					IsNeedDecrypt: true,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:            "98LP2514720218976",
							SttLastStatusID:  model.BAGGING,
							SttNoRefExternal: "",
							SttMeta:          `{"estimate_sla":"2-3 Hari","origin_city_name":"JAKARTA","origin_district_name":"PALMERAH, PALMERAH, JAKARTA BARAT","destination_city_name":"JOGJAKARTA","destination_district_name":"DEPOK, SLEMAN","is_stt_crossdocking":true}`,
						},
					},
				}, nil).Once()

				mockReasonRepo.On("GetDetail", mock.Anything, mock.Anything).Return(&model.ReasonDetailResult{
					Reason: model.Reason{
						ReasonDescription: "Aktual barang belum tiba karena FM belum handover",
					},
				}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-HALCD-sttNotTKP01",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					CustomProcessStatus: model.HALCD,
					SttNo:               []string{"98LP2514720218976"},
					Remarks:             "RESXX",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want:    nil,
			wantErr: true,
			errResp: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt Cannot Be Proccess",
				"id": "Nomor STT tidak dapat diproses",
			}),
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockReasonRepo := new(mocks.ReasonRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					reasonRepo:            mockReasonRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.HALCD,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"98LP2514720218976"}).Return(nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"98LP2514720218976"},
					IsNeedDecrypt: true,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:            "98LP2514720218976",
							SttLastStatusID:  model.BKD,
							SttNoRefExternal: "",
						},
					},
				}, nil).Once()

				mockReasonRepo.On("GetDetail", mock.Anything, mock.Anything).Return(&model.ReasonDetailResult{
					Reason: model.Reason{
						ReasonDescription: "Aktual barang belum tiba karena FM belum handover",
					},
				}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-HALCD-Bagging",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					CustomProcessStatus: model.HALCD,
					SttNo:               []string{"98LP2514720218976"},
					Remarks:             "RESXX",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want:    nil,
			wantErr: true,
			errResp: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt No Cannot Be Proccess Cause last status not BKD or Bagging",
				"id": "Nomor STT tidak dapat diproses karena status akhir bukan BKD atau Bagging",
			}),
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockReasonRepo := new(mocks.ReasonRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					reasonRepo:            mockReasonRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.HALCD,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"98LP2514720218976"}).Return(nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"98LP2514720218976"},
					IsNeedDecrypt: true,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:            "98LP2514720218976",
							SttLastStatusID:  model.CNX,
							SttNoRefExternal: "TKP01-MD00005",
						},
					},
				}, nil).Once()

				mockReasonRepo.On("GetDetail", mock.Anything, mock.Anything).Return(&model.ReasonDetailResult{
					Reason: model.Reason{
						ReasonDescription: "Aktual barang belum tiba karena FM belum handover",
					},
				}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-FailedStt_HALDC",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					CustomProcessStatus: model.HALCD,
					SttNo:               []string{"98LP2514720218976"},
					Remarks:             "RESXX",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed: []general.STTFailedGeneralResponse{
					{},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockReasonRepo := new(mocks.ReasonRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					reasonRepo:            mockReasonRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.HALCD,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"98LP2514720218976"}).Return(nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo: []string{"98LP2514720218976"},
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:            "98LP2514720218976",
							SttLastStatusID:  model.BKD,
							SttNoRefExternal: "TKP01-MD00005",
						},
					},
				}, nil).Once()

				mockReasonRepo.On("GetDetail", mock.Anything, mock.Anything).Return(&model.ReasonDetailResult{
					Reason: model.Reason{
						ReasonDescription: "Aktual barang belum tiba karena FM belum handover",
					},
				}, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-FailedUpdateRTSHQ_ShipmentARA",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.RTSHQ,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed: []general.STTFailedGeneralResponse{
					{
						SttNo: "11LP16***********",
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockGatewaySttStatusUc := new(ucmock.GatewaySttStatus)
				mockFlagManagementRepo := new(mocks.FlagManagementRepository)
				mockRtsUc := new(ucmock.ReadyToCargo)
				mockRequestPriorityDelivery := new(ucmock.RequestPriorityDelivery)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo:   mockCustomProcessRoleRepo,
					sttPaymentUc:            mockSttPaymentUc,
					sttPieceRepo:            mockSttPieceRepo,
					partnerRepo:             mockPartnerRepo,
					districtRepo:            mockDistrictRepo,
					gatewaySttStatusUc:      mockGatewaySttStatusUc,
					flagManagementRepo:      mockFlagManagementRepo,
					rtcUc:                   mockRtsUc,
					requestPriorityDelivery: mockRequestPriorityDelivery,
					sttDueRepo:              mockSttDueRepo,
				}

				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultName", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultAddress", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultPhoneNumber", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultAddressType", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultDistrictCode", mock.Anything).Return("DKI00102").Once()

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.RTSHQ,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "11LP16***********",
							SttShipmentID:   "ARA111",
							SttLastStatusID: model.STIDEST,
						},
					},
				}, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 2, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				os.Setenv("DEFAULT_DISTRICT_RTS_RTSHQ", "DKI00102")

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: ""}, "DKI00102").Return(&model.District{
					Data: model.DistrictData{
						Code: "DKI00102",
						City: &model.City{
							Code: "CGK",
						},
					},
				}, nil).Once()

				mockGatewaySttStatusUc.On("TriggerReleasePosParentCommission", mock.Anything, &gateway_stt_status.TriggerReleasePosParentCommissionRequest{
					SttSuccess: map[string]model.Stt{
						"11LP16***********": model.Stt{},
					},
					Status: "RTSHQ",
					Token:  "",
				})

				mockRtsUc.On("UpdateInactiveRTCBySttId", mock.Anything, 0).Return(nil).Once()
				mockRequestPriorityDelivery.On("UpdateIsShowToZero", mock.Anything, "11LP16***********").Return(nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-FailedUpdateSttReverseRTSHQ_ShipmentARA_ErrorGetSttDetail",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.RTSHQ,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want:    nil,
			wantErr: true,
			errResp: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting STT",
				"id": "Terjadi kesalahan pada saat getting STT",
			}),
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockGatewaySttStatusUc := new(ucmock.GatewaySttStatus)
				mockFlagManagementRepo := new(mocks.FlagManagementRepository)
				mockRtsUc := new(ucmock.ReadyToCargo)
				mockRequestPriorityDelivery := new(ucmock.RequestPriorityDelivery)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo:   mockCustomProcessRoleRepo,
					sttPaymentUc:            mockSttPaymentUc,
					sttPieceRepo:            mockSttPieceRepo,
					partnerRepo:             mockPartnerRepo,
					districtRepo:            mockDistrictRepo,
					gatewaySttStatusUc:      mockGatewaySttStatusUc,
					flagManagementRepo:      mockFlagManagementRepo,
					rtcUc:                   mockRtsUc,
					requestPriorityDelivery: mockRequestPriorityDelivery,
					sttDueRepo:              mockSttDueRepo,
				}

				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultName", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultAddress", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultPhoneNumber", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultAddressType", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultDistrictCode", mock.Anything).Return("DKI00102").Once()

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.RTSHQ,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "11LP16***********",
							SttLastStatusID: model.STIDEST,
							SttMeta:         `{ "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" } }`,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 2, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				os.Setenv("DEFAULT_DISTRICT_RTS_RTSHQ", "DKI00102")

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: ""}, "DKI00102").Return(&model.District{
					Data: model.DistrictData{
						Code: "DKI00102",
						City: &model.City{
							Code: "CGK",
						},
					},
				}, nil).Once()

				sttMeta := sttDetail[0].SttMetaToStruct()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "11LP1691461672142",
							SttShipmentID:   "ARA111",
							SttLastStatusID: model.STIDEST,
						},
					},
				}, nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return(nil, errors.New("error")).Once()

				mockGatewaySttStatusUc.On("TriggerReleasePosParentCommission", mock.Anything, &gateway_stt_status.TriggerReleasePosParentCommissionRequest{
					SttSuccess: map[string]model.Stt{
						"11LP16***********": model.Stt{},
					},
					Status: "RTSHQ",
					Token:  "",
				})

				mockRtsUc.On("UpdateInactiveRTCBySttId", mock.Anything, 0).Return(nil).Once()

				mockRequestPriorityDelivery.On("UpdateIsShowToZero", mock.Anything, "11LP16***********").Return(nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-FailedUpdateSttReverseRTSHQ_ShipmentARA",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.RTSHQ,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed: []general.STTFailedGeneralResponse{
					{
						SttNo: "11LP16***********",
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockGatewaySttStatusUc := new(ucmock.GatewaySttStatus)
				mockFlagManagementRepo := new(mocks.FlagManagementRepository)
				mockRtcUc := new(ucmock.ReadyToCargo)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					districtRepo:          mockDistrictRepo,
					gatewaySttStatusUc:    mockGatewaySttStatusUc,
					flagManagementRepo:    mockFlagManagementRepo,
					rtcUc:                 mockRtcUc,
					sttDueRepo:            mockSttDueRepo,
				}

				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultName", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultAddress", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultPhoneNumber", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultAddressType", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultDistrictCode", mock.Anything).Return("DKI00102").Once()

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.RTSHQ,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "11LP16***********",
							SttLastStatusID: model.STIDEST,
							SttMeta:         `{ "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" } }`,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 2, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				os.Setenv("DEFAULT_DISTRICT_RTS_RTSHQ", "DKI00102")

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: ""}, "DKI00102").Return(&model.District{
					Data: model.DistrictData{
						Code: "DKI00102",
						City: &model.City{
							Code: "CGK",
						},
					},
				}, nil).Once()

				sttMeta := sttDetail[0].SttMetaToStruct()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "11LP1691461672142",
							SttShipmentID:   "ARA111",
							SttLastStatusID: model.STIDEST,
						},
					},
				}, nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "11LP1691461672142",
							SttShipmentID:   "ARA111",
							SttLastStatusID: model.STIDEST,
						},
					},
				}, nil).Once()

				mockGatewaySttStatusUc.On("TriggerReleasePosParentCommission", mock.Anything, &gateway_stt_status.TriggerReleasePosParentCommissionRequest{
					SttSuccess: map[string]model.Stt{
						"11LP16***********": model.Stt{},
					},
					Status: "RTSHQ",
					Token:  "",
				})

				mockRtcUc.On("UpdateInactiveRTCBySttId", mock.Anything, 0).Return(nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-FailedUpdateRtshqIsCODorIsDfodTrue",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.RTSHQ,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed: []general.STTFailedGeneralResponse{
					{
						SttNo: "11LP16***********",
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockGatewaySttStatusUc := new(ucmock.GatewaySttStatus)
				mockFlagManagementRepo := new(mocks.FlagManagementRepository)
				mockRtcUc := new(ucmock.ReadyToCargo)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					districtRepo:          mockDistrictRepo,
					gatewaySttStatusUc:    mockGatewaySttStatusUc,
					flagManagementRepo:    mockFlagManagementRepo,
					rtcUc:                 mockRtcUc,
					sttDueRepo:            mockSttDueRepo,
				}

				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultName", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultAddress", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultPhoneNumber", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultAddressType", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultDistrictCode", mock.Anything).Return("DKI00102").Once()

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.RTSHQ,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "11LP16***********",
							SttLastStatusID: model.STIDEST,
							SttMeta:         `{ "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" } }`,
							SttIsDFOD:       true,
							SttIsCOD:        true,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 2, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				os.Setenv("DEFAULT_DISTRICT_RTS_RTSHQ", "DKI00102")

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: ""}, "DKI00102").Return(&model.District{
					Data: model.DistrictData{
						Code: "DKI00102",
						City: &model.City{
							Code: "CGK",
						},
					},
				}, nil).Once()

				sttMeta := sttDetail[0].SttMetaToStruct()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "11LP1691461672142",
							SttLastStatusID: model.STIDEST,
						},
					},
				}, nil).Once()

				mockGatewaySttStatusUc.On("TriggerReleasePosParentCommission", mock.Anything, &gateway_stt_status.TriggerReleasePosParentCommissionRequest{
					SttSuccess: map[string]model.Stt{
						"11LP16***********": model.Stt{},
					},
					Status: "RTSHQ",
					Token:  "",
				})

				mockRtcUc.On("UpdateInactiveRTCBySttId", mock.Anything, 0).Return(nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "Test_customProcessCtx_CreateCustomProcess-FailedUpdateRtshq_ReverseSttIsCODorIsDfodTrue",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.RTSHQ,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed: []general.STTFailedGeneralResponse{
					{
						SttNo: "11LP16***********",
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockDistrictRepo := new(mocks.DistrictRepository)
				mockGatewaySttStatusUc := new(ucmock.GatewaySttStatus)
				mockFlagManagementRepo := new(mocks.FlagManagementRepository)
				mockRtcUc := new(ucmock.ReadyToCargo)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					districtRepo:          mockDistrictRepo,
					gatewaySttStatusUc:    mockGatewaySttStatusUc,
					flagManagementRepo:    mockFlagManagementRepo,
					rtcUc:                 mockRtcUc,
					sttDueRepo:            mockSttDueRepo,
				}

				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultName", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultAddress", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultPhoneNumber", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultAddressType", mock.Anything).Return("").Once()
				mockFlagManagementRepo.On("CloudBeesRTSHQDefaultDistrictCode", mock.Anything).Return("DKI00102").Once()

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.RTSHQ,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "11LP16***********",
							SttLastStatusID: model.STIDEST,
							SttMeta:         `{ "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" } }`,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 2, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				os.Setenv("DEFAULT_DISTRICT_RTS_RTSHQ", "DKI00102")

				mockDistrictRepo.On("GetByCode", mock.Anything, &model.CredentialRestAPI{Token: ""}, "DKI00102").Return(&model.District{
					Data: model.DistrictData{
						Code: "DKI00102",
						City: &model.City{
							Code: "CGK",
						},
					},
				}, nil).Once()

				sttMeta := sttDetail[0].SttMetaToStruct()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "11LP1691461672142",
							SttLastStatusID: model.RTS,
							SttIsDFOD:       true,
							SttIsCOD:        true,
						},
					},
				}, nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "11LP1691461672142",
							SttLastStatusID: model.RTS,
							SttIsDFOD:       true,
							SttIsCOD:        true,
						},
					},
				}, nil).Once()

				mockGatewaySttStatusUc.On("TriggerReleasePosParentCommission", mock.Anything, &gateway_stt_status.TriggerReleasePosParentCommissionRequest{
					SttSuccess: map[string]model.Stt{
						"11LP16***********": model.Stt{},
					},
					Status: "RTSHQ",
					Token:  "",
				})

				mockRtcUc.On("UpdateInactiveRTCBySttId", mock.Anything, 0).Return(nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "success_claim",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.CLAIM,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed:      []general.STTFailedGeneralResponse{{}},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {
				/*
				  TODO: uncomment
				  use comment because UT create cargo V2
				  will be error ???
				*/
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockGatewaySttStatusUc := new(ucmock.GatewaySttStatus)
				mockGatewaySttUc := new(ucmock.GatewayStt)
				// mockCustomProcessRepo := new(mocks.CustomProcessRepository)
				// mockSttActivityUc := new(ucmock.SttActivity)
				mockRtcUc := new(ucmock.ReadyToCargo)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					gatewaySttStatusUc:    mockGatewaySttStatusUc,
					gatewaySttUc:          mockGatewaySttUc,
					// customProcessRepo:     mockCustomProcessRepo,
					// sttActivityUc:         mockSttActivityUc,
					rtcUc:      mockRtcUc,
					sttDueRepo: mockSttDueRepo,
				}

				stt := model.Stt{
					// SttID:            1,
					SttNo:            "11LP16***********",
					SttLastStatusID:  model.STIDEST,
					SttMeta:          `{ "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" } }`,
					SttIsDFOD:        true,
					SttBookedByType:  model.POS,
					SttBookedForType: model.POS,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.CLAIM,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: stt,
						SttPiece: model.SttPiece{
							SttPieceID: 1,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 2, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				sttMeta := sttDetail[0].SttMetaToStruct()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: stt,
					},
				}, nil).Once()

				mockSttPieceHistoryRepo.On("Get", mock.Anything, &model.SttPieceHistoryViewParam{
					SttPieceHistorySttPieceID: 1,
					SttPieceHistoryStatus:     model.CLAIM,
				}).Return(nil, nil).Once()

				// now, _ := shared.ParseUTC7(shared.FormatDateTime, time.Now().Format(shared.FormatDateTime))

				// customProcessdata := customProcess.InsertCustomProcessData{
				// 	CustomProcess: &model.CustomProcess{
				// 		CustomProcessTotalSTT:     1,
				// 		CustomProcessTotalPiece:   1,
				// 		CustomProcessLatestStatus: model.CLAIM,
				// 		CustomProcessCreatedAt:    now,
				// 		CustomProcessUpdatedAt:    now,
				// 		CustomProcessPartnerID:    1,
				// 		CustomProcessRemarks:      "hehe",
				// 		CustomProcessAccountType:  model.INTERNAL,
				// 	},
				// 	SttHistories: []customProcess.SttHistory{
				// 		{
				// 			SttID: 1,
				// 			SttHistory: model.SttPieceHistory{
				// 				SttPieceID:       1,
				// 				HistoryStatus:    model.CLAIM,
				// 				HistoryLocation:  "BDO",
				// 				HistoryActorID:   1,
				// 				HistoryCreatedAt: now,
				// 				HistoryRemark: func() string {
				// 					rm := model.RemarkPieceHistory{
				// 						HistoryLocationName:  "Bandung",
				// 						ClaimNo:              "hehe",
				// 						CustomProcessRemarks: "hehe",
				// 					}
				// 					return rm.ToString()
				// 				}(),
				// 			},
				// 		},
				// 	},
				// }
				// mockCustomProcessRepo.On("Create", mock.Anything, &customProcessdata).Return(&customProcess.InsertCustomProcessResponse{
				// 	CustomProcess: &model.CustomProcess{
				// 		CustomProcessID: 1,
				// 	},
				// }, nil).Once()

				// mockSttActivityUc.On("UpdateSttTime", mock.Anything, &stt_activity.SttActivityRequest{
				// 	ListSttData: []stt_activity.SttActivityRequestDetail{
				// 		{
				// 			SttNo:         "11LP16***********",
				// 			SttStatus:     model.CLAIM,
				// 			SttStatusTime: now,
				// 		},
				// 	},
				// }).Return(nil).Once()

				// mockSttActivityUc.On("StatusSubmit", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
				// 	UpdateSttStatus: &model.UpdateSttStatus{
				// 		SttNo:      "11LP16***********",
				// 		Datetime:   now,
				// 		StatusCode: model.CLAIM,
				// 		Location:   "BDO",
				// 		City:       "Bandung",
				// 		Remarks:    "Paket diupdate oleh ",
				// 		UpdatedOn:  now,
				// 	},
				// 	ServiceType:   model.PACKAGESERVICE,
				// 	BookedForType: model.POS,
				// }).Return(nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				sttNoSuccess := map[string]model.Stt{}
				sttNoSuccess["11LP16***********"] = stt
				mockGatewaySttStatusUc.On("TriggerReleasePosParentCommission", mock.Anything, &gateway_stt_status.TriggerReleasePosParentCommissionRequest{
					SttSuccess: sttNoSuccess,
					Status:     model.CLAIM,
					Token:      "",
				}).Return(nil).Once()

				mockGatewaySttUc.On("GoberRevertBookingCod", mock.Anything, &gateway_stt.GoberRevertBookingCodPublishMessageRequest{
					SttNo:            "11LP16***********",
					SttBookedByType:  model.POS,
					SttBookedForType: model.POS,
					SttIsDFOD:        true,
					SttIsCOD:         false,
					UpdateByID:       1,
				}).Return(nil).Once()

				mockRtcUc.On("UpdateInactiveRTCBySttId", mock.Anything, 0).Return(nil).Once()

				return c
			},
		},
		{
			name: "failed_OCCHAL_product_type_is_not_interpack",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.OCCHAL,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "REGPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed: []general.STTFailedGeneralResponse{
					{
						SttNo: "11LP16***********",
						Error: "Hanya untuk jenis pengiriman INTERPACK. Cek & atur ulang",
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {

				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				stt := model.Stt{
					SttID:           1,
					SttNo:           "11LP16***********",
					SttLastStatusID: model.INTSTI,
					SttMeta:         `{ "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" } }`,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.OCCHAL,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: stt,
						SttPiece: model.SttPiece{
							SttPieceID: 1,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "BDO",
							City: &model.City{
								Name: "Bandung",
							},
						},
					},
				}, nil).Once()

				sttMeta := sttDetail[0].SttMetaToStruct()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: stt,
					},
				}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "failed_get_history_for_interpack",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.OCCEXP,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "INTERPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want:    nil,
			wantErr: true,
			errResp: shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "STT Piece History is not found",
				"id": "STT Piece History tidak ditemukan",
			}),
			beforeFunc: func() *customProcessCtx {

				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				stt := model.Stt{
					SttID:           1,
					SttNo:           "11LP16***********",
					SttLastStatusID: model.INTSTI,
					SttProductType:  "INTERPACK",
					SttOriginCityID: "CGK",
					SttMeta:         `{ "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" } }`,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.OCCEXP,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: stt,
						SttPiece: model.SttPiece{
							SttPieceID: 1,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "KUL",
							City: &model.City{
								Name: "Kuala Lumpur",
							},
						},
					},
				}, nil).Once()

				sttMeta := sttDetail[0].SttMetaToStruct()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: stt,
					},
				}, nil).Once()

				mockSttPieceHistoryRepo.On("Select", mock.Anything, &model.SttPieceHistoryViewParam{
					SttPieceHistorySttPieceID: 1,
					Order:                     true,
					OrderDesc:                 true,
				}).Return([]model.SttPieceHistory{}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "failed_OCCEXP_has_INTHND",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.OCCEXP,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "INTERPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed: []general.STTFailedGeneralResponse{
					{
						SttNo: "11LP16***********",
						Error: `Status "INT - HND" ke "OCC - EXP/IMP/HAL" hanya bisa diperbarui oleh Vendor. Tunggu atau hubungi Vendor`,
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {

				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				stt := model.Stt{
					SttID:           1,
					SttNo:           "11LP16***********",
					SttLastStatusID: model.INTSTI,
					SttProductType:  "INTERPACK",
					SttOriginCityID: "CGK",
					SttMeta:         `{ "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" } }`,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.OCCEXP,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: stt,
						SttPiece: model.SttPiece{
							SttPieceID: 1,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "KUL",
							City: &model.City{
								Name: "Kuala Lumpur",
							},
						},
					},
				}, nil).Once()

				sttMeta := sttDetail[0].SttMetaToStruct()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: stt,
					},
				}, nil).Once()

				mockSttPieceHistoryRepo.On("Select", mock.Anything, &model.SttPieceHistoryViewParam{
					SttPieceHistorySttPieceID: 1,
					Order:                     true,
					OrderDesc:                 true,
				}).Return([]model.SttPieceHistory{
					{
						SttPieceID:    1,
						HistoryStatus: model.BKD,
					},
					{
						SttPieceID:    1,
						HistoryStatus: model.INTHND,
					},
				}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "failed_get_partner_city_for_interpack",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.OCCEXP,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "INTERPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want:    nil,
			wantErr: true,
			errResp: shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Partner city not found",
				"id": "Partner city tidak ditemukan",
			}),
			beforeFunc: func() *customProcessCtx {

				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					cityRepo:              mockCityRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				stt := model.Stt{
					SttID:           1,
					SttNo:           "11LP16***********",
					SttLastStatusID: model.INTSTI,
					SttProductType:  "INTERPACK",
					SttOriginCityID: "CGK",
					SttMeta:         `{ "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" } }`,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.OCCEXP,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: stt,
						SttPiece: model.SttPiece{
							SttPieceID: 1,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "KUL",
							City: &model.City{
								Name: "Kuala Lumpur",
							},
						},
					},
				}, nil).Once()

				sttMeta := sttDetail[0].SttMetaToStruct()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: stt,
					},
				}, nil).Once()

				mockSttPieceHistoryRepo.On("Select", mock.Anything, &model.SttPieceHistoryViewParam{
					SttPieceHistorySttPieceID: 1,
					Order:                     true,
					OrderDesc:                 true,
				}).Return([]model.SttPieceHistory{
					{
						SttPieceID:    1,
						HistoryStatus: model.BKD,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "KUL", "").Return(nil, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "failed_get_stt_origin_city_for_interpack",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.OCCEXP,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "INTERPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want:    nil,
			wantErr: true,
			errResp: shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "STT origin city not found",
				"id": "STT origin city tidak ditemukan",
			}),
			beforeFunc: func() *customProcessCtx {

				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					cityRepo:              mockCityRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				stt := model.Stt{
					SttID:           1,
					SttNo:           "11LP16***********",
					SttLastStatusID: model.INTSTI,
					SttProductType:  "INTERPACK",
					SttOriginCityID: "CGK",
					SttMeta:         `{ "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" } }`,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.OCCEXP,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: stt,
						SttPiece: model.SttPiece{
							SttPieceID: 1,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "KUL",
							City: &model.City{
								Name: "Kuala Lumpur",
							},
						},
					},
				}, nil).Once()

				sttMeta := sttDetail[0].SttMetaToStruct()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: stt,
					},
				}, nil).Once()

				mockSttPieceHistoryRepo.On("Select", mock.Anything, &model.SttPieceHistoryViewParam{
					SttPieceHistorySttPieceID: 1,
					Order:                     true,
					OrderDesc:                 true,
				}).Return([]model.SttPieceHistory{
					{
						SttPieceID:    1,
						HistoryStatus: model.BKD,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "KUL", "").Return(&model.City{
					Code:      "KUL",
					CountryID: 2,
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "CGK", "").Return(nil, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "failed_OCCEXP",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.OCCEXP,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "INTERPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed: []general.STTFailedGeneralResponse{
					{
						SttNo: "11LP16***********",
						Error: "Status tidak valid. Silahkan pilih status “OCC - IMP”",
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {

				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					cityRepo:              mockCityRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				stt := model.Stt{
					SttID:           1,
					SttNo:           "11LP16***********",
					SttLastStatusID: model.INTSTI,
					SttProductType:  "INTERPACK",
					SttOriginCityID: "CGK",
					SttMeta:         `{ "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" } }`,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.OCCEXP,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: stt,
						SttPiece: model.SttPiece{
							SttPieceID: 1,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "KUL",
							City: &model.City{
								Name: "Kuala Lumpur",
							},
						},
					},
				}, nil).Once()

				sttMeta := sttDetail[0].SttMetaToStruct()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: stt,
					},
				}, nil).Once()

				mockSttPieceHistoryRepo.On("Select", mock.Anything, &model.SttPieceHistoryViewParam{
					SttPieceHistorySttPieceID: 1,
					Order:                     true,
					OrderDesc:                 true,
				}).Return([]model.SttPieceHistory{
					{
						SttPieceID:    1,
						HistoryStatus: model.BKD,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "KUL", "").Return(&model.City{
					Code:      "KUL",
					CountryID: 2,
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "CGK", "").Return(&model.City{
					Code:      "CGK",
					CountryID: 1,
				}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "failed_OCCIMP",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.OCCIMP,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "INTERPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed: []general.STTFailedGeneralResponse{
					{
						SttNo: "11LP16***********",
						Error: "Status tidak valid. Silahkan pilih status “OCC - EXP”",
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {

				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					cityRepo:              mockCityRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				stt := model.Stt{
					SttID:           1,
					SttNo:           "11LP16***********",
					SttLastStatusID: model.INTSTI,
					SttProductType:  "INTERPACK",
					SttOriginCityID: "CGK",
					SttMeta:         `{ "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" } }`,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.OCCIMP,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: stt,
						SttPiece: model.SttPiece{
							SttPieceID: 1,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "CGK",
							City: &model.City{
								Name: "JAKARTA",
							},
						},
					},
				}, nil).Once()

				sttMeta := sttDetail[0].SttMetaToStruct()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: stt,
					},
				}, nil).Once()

				mockSttPieceHistoryRepo.On("Select", mock.Anything, &model.SttPieceHistoryViewParam{
					SttPieceHistorySttPieceID: 1,
					Order:                     true,
					OrderDesc:                 true,
				}).Return([]model.SttPieceHistory{
					{
						SttPieceID:    1,
						HistoryStatus: model.BKD,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "CGK", "").Return(&model.City{
					Code:      "CGK",
					CountryID: 1,
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "CGK", "").Return(&model.City{
					Code:      "CGK",
					CountryID: 1,
				}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
		{
			name: "failed_OCCHAL",
			args: args{
				ctx: ctxBg,
				params: &customProcess.CreateCustomProcessRequest{
					AccountType:         model.INTERNAL,
					PartnerID:           1,
					SttBookedBy:         2,
					CustomProcessStatus: model.OCCHAL,
					SttNo:               []string{"11LP16***********"},
					Remarks:             "hehe",
					SttProductTypeCode:  "INTERPACK",
					SttCommodityCode:    "GEN",
					SttGoodsStatus:      "ecommerce",
					HubID:               4,
					HubName:             "Hub Selatan",
				},
			},
			want: &customProcess.CreateCustomProcessResponse{
				TotalSttFailed: 1,
				SttFailed: []general.STTFailedGeneralResponse{
					{
						SttNo: "11LP16***********",
					},
				},
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {

				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPaymentUc := new(ucmock.SttPayment)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockCityRepo := new(mocks.CityRepository)
				mockSttDueRepo := mocks.NewSttDueRepository(t)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPaymentUc:          mockSttPaymentUc,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					cityRepo:              mockCityRepo,
					sttDueRepo:            mockSttDueRepo,
				}

				stt := model.Stt{
					SttID:           1,
					SttNo:           "11LP16***********",
					SttLastStatusID: model.OCCHAL,
					SttProductType:  "INTERPACK",
					SttOriginCityID: "CGK",
					SttMeta:         `{ "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" } }`,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessStatus: model.OCCHAL,
					},
				}, nil).Once()

				mockSttPaymentUc.On("SttPaymentValidation", mock.Anything, []string{"11LP16***********"}).Return(nil).Once()

				sttDetail := []model.SttDetailResult{
					{
						Stt: stt,
						SttPiece: model.SttPiece{
							SttPieceID: 1,
						},
					},
				}
				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					ListSttNo:     []string{"11LP16***********"},
					IsNeedDecrypt: true,
				}).Return(sttDetail, nil).Once()

				mockPartnerRepo.On("GetByID", mock.Anything, 1, "").Return(&model.Partner{
					Data: model.PartnerBase{
						PartnerIDSttReturn: 2,
						PartnerLocation: &model.PartnerLocationBase{
							CityCode: "CGK",
							City: &model.City{
								Name: "JAKARTA",
							},
						},
					},
				}, nil).Once()

				sttMeta := sttDetail[0].SttMetaToStruct()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: sttMeta.DetailSttReverseJourney.ReverseSttNo,
				}).Return([]model.SttDetailResult{
					{
						Stt: stt,
					},
				}, nil).Once()

				mockSttPieceHistoryRepo.On("Select", mock.Anything, &model.SttPieceHistoryViewParam{
					SttPieceHistorySttPieceID: 1,
					Order:                     true,
					OrderDesc:                 true,
				}).Return([]model.SttPieceHistory{
					{
						SttPieceID:    1,
						HistoryStatus: model.BKD,
					},
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "CGK", "").Return(&model.City{
					Code:      "CGK",
					CountryID: 1,
				}, nil).Once()

				mockCityRepo.On("Get", mock.Anything, "CGK", "").Return(&model.City{
					Code:      "CGK",
					CountryID: 1,
				}, nil).Once()

				mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
				mockSttDueRepo.AssertExpectations(t)

				return c
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.beforeFunc()

			got, err := c.CreateCustomProcess(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr || !assert.Equal(t, err, tt.errResp) {
				t.Errorf("Test_customProcessCtx_CreateCustomProcess(); err = %v, wantErr = %v\n", err, tt.errResp)
				return
			}
			if !assert.Equal(t, tt.want, got) {
				t.Errorf("Test_customProcessCtx_CreateCustomProcess(); got = %v, want = %v\n", got, tt.want)
			}
		})
	}
}

func TestCreateCustomProcess(t *testing.T) {
	t.Skip()
	mockPartnerRepo := new(mocks.PartnerRepository)
	mockSttPieceRepoRepo := new(mocks.SttPiecesRepository)
	mockCustomProcessRepo := new(mocks.CustomProcessRepository)
	mockCommodityRepo := new(mocks.CommodityRepository)
	mockDistrictRepo := new(mocks.DistrictRepository)
	mockCityRepo := new(mocks.CityRepository)
	//mockSttActivityRepo := new(mocks.SttActivityRepository)
	mocksSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
	mockSttActivityUc := new(ucmock.SttActivity)

	mockCustomProcessUc := customProcessCtx{
		customProcessRepo:   mockCustomProcessRepo,
		partnerRepo:         mockPartnerRepo,
		sttPieceRepo:        mockSttPieceRepoRepo,
		commodityRepo:       mockCommodityRepo,
		districtRepo:        mockDistrictRepo,
		cityRepo:            mockCityRepo,
		sttActivityUc:       mockSttActivityUc,
		sttPieceHistoryRepo: mocksSttPieceHistoryRepo,
	}

	mockStt, _ := generateStt(t)
	mockStt.SttLastStatusID = model.BKD

	mockSttDetailResults := []model.SttDetailResult{
		{
			Stt: *mockStt,
			SttPiece: model.SttPiece{
				SttPieceID:           1,
				SttPieceSttID:        mockStt.SttID,
				SttPieceLength:       1,
				SttPieceWidth:        2,
				SttPieceHeight:       3,
				SttPieceGrossWeight:  4,
				SttPieceVolumeWeight: 5,
				SttPieceNo:           1,
				SttPieceLastStatusID: mockStt.SttLastStatusID,
			},
		},
	}

	mockPartner := &model.Partner{
		Data: model.PartnerBase{
			ID:   1,
			Name: "",
			PartnerLocation: &model.PartnerLocationBase{
				ID:       1,
				CityCode: "CGK",
			},
		},
	}

	mockCreateCustomProcessRequest := customProcess.CreateCustomProcessRequest{
		CustomProcessStatus: model.HAL,
		SttNo: []string{
			mockStt.SttNo,
		},
		Token:       "",
		AccountID:   1,
		AccountName: "",
		PartnerID:   mockPartner.Data.ID,
		PartnerType: model.CONSOLE,
		PartnerName: mockPartner.Data.Name,
	}

	mockCustomProcess := &customProcess.InsertCustomProcessResponse{
		CustomProcess: &model.CustomProcess{
			CustomProcessID: 1,
		},
	}

	mockResponse := &customProcess.CreateCustomProcessResponse{
		CustomProcessID: mockCustomProcess.CustomProcess.CustomProcessID,
		TotalSttSuccess: len(mockCreateCustomProcessRequest.SttNo),
		TotalSttFailed:  0,
		SttFailed:       []general.STTFailedGeneralResponse{},
	}

	mockSttPieceRepoRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
		ListSttNo: mockCreateCustomProcessRequest.SttNo,
	}).Return(mockSttDetailResults, nil).Once()

	mockPartnerRepo.On("GetByID", mock.Anything, mockCreateCustomProcessRequest.PartnerID, mockCreateCustomProcessRequest.Token).Return(mockPartner, nil).Once()

	mockCustomProcessRepo.On("Create", mock.Anything, mock.Anything).Return(mockCustomProcess, nil)

	t.Run("TestCreateCustomProcess", func(t *testing.T) {

		// response, err := mockCustomProcessUc.CreateCustomProcess(ctxBg, &mockCreateCustomProcessRequest)
		// assert.NoError(t, err)
		t.Log(mockCustomProcessUc)
		t.Log(mockResponse)

		// if !reflect.DeepEqual(response, mockResponse) {
		// 	t.Errorf("CreateCustomProcess() got = %v, want %v", response, mockResponse)
		// }
	})
}

func TestCreateCustomProcessFromMisbooking(t *testing.T) {
	mockPartnerRepo := new(mocks.PartnerRepository)
	mockCustomProcessRepo := new(mocks.CustomProcessRepository)
	mockCommodityRepo := new(mocks.CommodityRepository)
	mockDistrictRepo := new(mocks.DistrictRepository)
	mockCityRepo := new(mocks.CityRepository)
	mocksSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
	mockSttActivityUc := new(ucmock.SttActivity)
	mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
	mockSttPieceRepo := new(mocks.SttPiecesRepository)
	mockSttPaymentStatusUc := new(ucmock.SttPayment)
	mockSttDueRepo := mocks.NewSttDueRepository(t)
	mockBagRepo := mocks.NewBagRepository(t)
	mockCustomProcessUc := customProcessCtx{
		customProcessRepo:     mockCustomProcessRepo,
		partnerRepo:           mockPartnerRepo,
		sttPieceRepo:          mockSttPieceRepo,
		commodityRepo:         mockCommodityRepo,
		districtRepo:          mockDistrictRepo,
		cityRepo:              mockCityRepo,
		sttActivityUc:         mockSttActivityUc,
		sttPieceHistoryRepo:   mocksSttPieceHistoryRepo,
		customProcessRoleRepo: mockCustomProcessRoleRepo,
		sttPaymentUc:          mockSttPaymentStatusUc,
		sttDueRepo:            mockSttDueRepo,
		bagRepo:               mockBagRepo,
	}

	t.Run("UpdateToNotReceived", func(t *testing.T) {

		mockPartner := &model.Partner{
			Data: model.PartnerBase{
				ID:   1,
				Name: "",
				PartnerLocation: &model.PartnerLocationBase{
					ID:       1,
					CityCode: "CGK",
				},
			},
		}

		mockStt, _ := generateStt(t)
		mockStt.SttLastStatusID = model.BKD

		request := customProcess.CreateCustomProcessRequest{
			CustomProcessStatus: model.NOTRECEIVED,
			SttNo: []string{
				mockStt.SttNo,
			},
			Token:       "",
			AccountID:   1,
			AccountName: "",
			PartnerID:   mockPartner.Data.ID,
			PartnerType: model.CONSOLE,
			PartnerName: mockPartner.Data.Name,
			Remarks:     mockStt.SttNo,
		}

		stt := model.Stt{
			SttNo:                mockStt.SttNo,
			SttLastStatusID:      model.MISBOOKING,
			SttOriginCityID:      "CGK1000",
			SttDestinationCityID: "BDO1000",
			SttUpdatedActorID:    dbr.NewNullInt64(1),
			SttCreatedAt:         time.Now(),
		}
		mockSttPieceDetail := []model.SttDetailResult{
			{Stt: stt},
		}

		mockBag := model.BagWithSTT{}
		bagID := 1
		bagCode := "BAG123"

		mockBag.BagID = &bagID
		mockBag.BagCode = &bagCode

		mockCustomProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{{RoleCustomProcessStatus: model.NOTRECEIVED}}, nil)
		mockSttPaymentStatusUc.On("SttPaymentValidation", mock.Anything, mock.Anything).Return(nil).Once()
		mockSttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return(mockSttPieceDetail, nil).Once()
		mockPartnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(mockPartner, nil).Once()
		mockBagRepo.On("SelectBagWithSTT", mock.Anything, mock.Anything).Return([]model.BagWithSTT{mockBag}, nil)

		mockSttDueRepo.On("UpdateBulk", mock.Anything, mock.Anything).Return(nil).Maybe()
		mockSttDueRepo.AssertExpectations(t)

		ctxBg, cancel := lputils.CreateContext(60) // 60 seconds timeout
		defer cancel()
		response, err := mockCustomProcessUc.CreateCustomProcess(ctxBg, &request)
		assert.NotNil(t, response)
		assert.Equal(t, response.TotalSttFailed, 1)
		assert.Equal(t, response.SttFailed[0].SttNo, mockStt.SttNo)
		assert.Nil(t, err)
	})

}

func TestViewCustomProcess(t *testing.T) {
	type args struct {
		ctx  context.Context
		args *customProcess.ViewCustomProcessRequest
	}
	type customArgs struct {
		ctx       context.Context
		startDate string
		endDate   string
		status    string
	}
	type ctxKeyVal struct {
		key string
		val interface{}
	}
	setCtxValue := func(ctx context.Context, keyVal ...ctxKeyVal) context.Context {
		for i := 0; i < len(keyVal); i++ {
			ctx = context.WithValue(ctx, keyVal[i].key, keyVal[i].val)
		}
		return ctx
	}

	mockCustomProcessRepo := new(mocks.CustomProcessRepository)
	mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
	mockAccountRepo := new(mocks.AccountRepository)
	uc := customProcessCtx{
		customProcessRepo:     mockCustomProcessRepo,
		customProcessRoleRepo: mockCustomProcessRoleRepo,
		accountRepo:           mockAccountRepo,
	}

	ctxBg, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()
	ctx := setCtxValue(
		ctxBg,
		ctxKeyVal{"partnerType", model.CONSOLE},
		ctxKeyVal{"partnerID", 1},
		ctxKeyVal{"accountRoleName", "admin_console"},
		ctxKeyVal{"type", "partner"},
	)
	startDate := time.Now().AddDate(0, 0, -3).Format("2006-01-02")
	endDate := time.Now().Format("2006-01-02")

	testTable := []struct {
		name       string
		req        args
		resp       *shared.Pagination
		errResp    bool
		errDetails error
		before     func()
	}{
		{
			name: "TestViewCustomProcessSuccess",
			req: args{
				ctx: ctx,
				args: &customProcess.ViewCustomProcessRequest{
					Limit:     1,
					Page:      1,
					StartDate: startDate,
					EndDate:   endDate,
					Status:    model.HAL,
				},
			},
			resp: &shared.Pagination{
				Data: []model.CustomProcess{
					{CustomProcessID: 1},
				},
				Meta: shared.Meta{
					Page:  1,
					Limit: 1,
				},
			},
			errResp:    false,
			errDetails: nil,
			before: func() {
				mockCustomProcess := []model.CustomProcess{
					{CustomProcessID: 1},
				}

				paramsCustomProcessRole := &model.CustomProcessRoleViewParams{
					AccountRoleType: "console",
				}

				paramsCustomProcess := &model.CustomProcessViewParams{
					BasedFilter:      model.BasedFilter{Limit: 1, Page: 1},
					PartnerIDWhereIn: []int{1},
					StartDate:        startDate,
					EndDate:          endDate,
					Status:           []string{model.HAL},
					AccountType:      model.PARTNER,
				}

				mockCustomProcessRoleRepo.On("Select", mock.Anything, paramsCustomProcessRole).Return([]model.RoleCustomProcess{model.RoleCustomProcess{RoleCustomProcessStatus: "HAL"}}, nil)
				mockCustomProcessRepo.On("Select", mock.Anything, paramsCustomProcess).Return(mockCustomProcess, nil).Once()
			},
		},
		{
			name: "TestViewCustomProcessInvalidDate",
			req: args{
				ctx: ctx,
				args: &customProcess.ViewCustomProcessRequest{
					Limit:     1,
					Page:      1,
					StartDate: time.Now().Format(time.RFC822),
					EndDate:   time.Now().Format(time.RFC822),
					Status:    model.HAL,
				},
			},
			resp:    nil,
			errResp: true,
			errDetails: shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid From StartDate Format",
				"id": "Invalid Format tanggal mulai",
			}),
			before: func() {},
		},
		{
			name: "TestViewCustomProcessInvalidStatus",
			req: args{
				ctx: ctx,
				args: &customProcess.ViewCustomProcessRequest{
					Limit:     1,
					Page:      1,
					StartDate: startDate,
					EndDate:   endDate,
					Status:    model.STTREMOVE,
				},
			},
			resp:    nil,
			errResp: true,
			errDetails: shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid Status value",
				"id": "Invalid nilai status",
			}),
			before: func() {},
		},
		{
			name: "TestViewCustomProcessPartnerTypeNotAllowed",
			req: args{
				ctx: setCtxValue(
					ctxBg,
					ctxKeyVal{"partnerType", model.POS},
					ctxKeyVal{"partnerID", 1},
					ctxKeyVal{"type", "partner"},
					ctxKeyVal{"accountRoleName", "admin_console"},
					ctxKeyVal{"tokenStr", mock.Anything},
				),
				args: &customProcess.ViewCustomProcessRequest{},
			},
			resp:    nil,
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner type only allowed for console/sub-console",
				"id": "Tipe partner hanya boleh console/sub-console",
			}),
			before: func() {},
		},
	}

	for _, tt := range testTable {
		t.Run(tt.name, func(t *testing.T) {

			tt.before()
			got, err := uc.ViewCustomProcess(tt.req.ctx, tt.req.args)
			if (err != nil) != tt.errResp {
				t.Errorf("customprocessCtx.ViewCustomProcess() error = %v, wantErr %v", err, tt.errDetails)
				return
			}
			if !reflect.DeepEqual(got, tt.resp) {
				t.Errorf("customProcessCtx.ViewCustomProcess() = %v, want %v", got, tt.resp)
			}
		})
	}
}

func (s *CustomProcessUsecaseTestSuite) TestViewDetailStt() {
	stt := model.Stt{
		SttNo:                "11LP1111111111",
		SttLastStatusID:      model.BKD,
		SttOriginCityID:      "CGK1000",
		SttDestinationCityID: "BDO1000",
		SttCreatedAt:         time.Now(),
		SttMeta:              `{ "estimate_sla": "1 - 1 Hari", "origin_city_name": "JAKARTA", "origin_district_name": "KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT", "destination_city_name": "JAKARTA", "destination_district_name": "PALMERAH, JAKARTA BARAT", "ticket_code": "", "other_shipper_ticket_code": null, "booked_by_external_type": "", "booked_by_external_code": "", "booked_for_external_type": "", "booked_for_external_code": "", "detail_calculate_retail_tariff": [ { "status": "BKD", "is_calculated": false, "calculated_at": null } ], "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" }, "is_stt_crossdocking": false, "client_payment_method": "", "client_cod_config_amount": "", "client_cod_shipment_discount": 0, "rate_vat_shipment": 0, "rate_vat_cod": 0, "total_tariff_return": 0 }`,
	}
	stt2 := model.Stt{
		SttNo:                "77LP1691468726883",
		SttLastStatusID:      model.MISROUTE,
		SttOriginCityID:      "CGK",
		SttDestinationCityID: "BDO",
		SttCreatedAt:         time.Now(),
		SttMeta:              `{"estimate_sla":"1 - 1 Hari","origin_city_name":"JAKARTA","origin_district_name":"KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT","destination_city_name":"JAKARTA","destination_district_name":"PALMERAH, JAKARTA BARAT","ticket_code":"","other_shipper_ticket_code":null,"booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":"","retail_tariff":{"city_rates":4500,"forward_rates":0,"chargeable_weight":1,"gross_weight":0.5,"volume_weight":0.16666666666666666,"shipping_cost":4500,"commodity_surcharge":0,"heavy_weight_surcharge":0,"document_surcharge":0,"insurance_rates":500,"insurance_name":"Insurance Basic","woodpacking_rates":0,"total_tariff":5000,"tax_rates":0,"bm_tax_rate":0,"ppn_tax_rate":0,"pph_tax_rate":0,"origin_district_rate":0,"destination_district_rate":0,"publish_rate":4500,"shipping_surcharge_rate":0,"heavy_weight_surcharge_remarks":"[{\"heavy_weight_surcharge\":0,\"gross_weight\":0.5}]","estimate_sla":"1 - 1 Hari","cod_amount":0,"cod_fee":0,"total_tariff_before_discount":5000,"is_discount_exceed_max_promo":false,"is_promo":false,"discount_type":"","discount":0,"parameter_calculation":"","total_discount":0,"publish_rate_after_discount":4500,"shipping_surcharge_rate_after_discount":0,"origin_district_rate_after_discount":0,"destination_district_rate_after_discount":0,"document_surcharge_after_discount":0,"commodity_surcharge_after_discount":0,"heavy_weight_surcharge_after_discount":0,"woodpacking_rates_after_discount":0,"insurance_rates_after_discount":500,"cod_fee_after_discount":0,"total_tariff_after_discount":5000,"total_surcharge_after_discount":0},"detail_calculate_retail_tariff":[{"status":"BKD","is_calculated":true,"calculated_at":"2023-08-08T11:00:12.*********+07:00"}],"detail_stt_reverse_journey":{"reverse_stt_no":"19LP1691461937437","reverse_stt_shipment_id":"","reverse_stt_last_status_id":"MISBOOKING","reverse_status_id":"REROUTE","root_reverse_stt_no":"19LP1691461937437","root_reverse_stt_shipment_id":"","root_reverse_stt_last_status_id":"MISBOOKING","reverse_charged_pos_id":0,"reverse_charged_console_id":0},"is_stt_crossdocking":false,"client_payment_method":"","client_cod_config_amount":"","client_cod_shipment_discount":0,"rate_vat_shipment":0,"rate_vat_cod":0,"total_tariff_return":0}`,
	}

	sttOri := &model.Stt{
		SttOriginCityID:         "CGK",
		SttOriginCityName:       "JAKARTA",
		SttOriginDistrictID:     "DKI30",
		SttOriginDistrictName:   "PALMERAH, JAKARTA BARAT",
		SttSenderName:           "Tusfendi",
		SttSenderPhone:          "***********",
		SttSenderAddress:        "tes",
		SttRecipientAddressType: dbr.NewNullString("home"),
	}

	mockSttPieceDetail := []model.SttDetailResult{
		{
			Stt: stt2,
		},
	}

	params := &customProcess.DetailCustomProcessRequest{
		PartnerID:           10,
		CustomProcessStatus: model.RTS,
		AccountRoleType:     model.CONSOLE,
		AccountType:         model.INTERNAL,
		SttNo:               stt.SttNo,
	}

	accountReferenceModelConcole := map[string]interface{}{
		"id": 1.0,
		"partner_location": map[string]interface{}{
			"country": map[string]interface{}{
				"code": model.CountryID,
			},
		},
	}
	accountDetailMock := &model.AccountDetail{
		Data: model.AccountDetailBase{
			AccountID:         1,
			AccountTypeDetail: accountReferenceModelConcole,
		},
	}

	res := customProcess.DetailSttCustomProcessResponse{}
	res.IsPaid = true
	res.IsAllowUpdateStatus = true
	detailStts := map[string]*general.SttResponseCustomProcess{}
	for _, v := range mockSttPieceDetail {
		if _, ok := detailStts[v.SttNo]; !ok {
			var (
				SttReturnCityCode           string
				SttReturnCityName           string
				SttReturnDistrictCode       string
				SttReturnDistrictName       string
				SttReturnReceiptName        string
				SttReturnReceiptPhone       string
				SttReturnReceiptAddress     string
				SttReturnReceiptAddressType string
				isReturnToOriginAddress     bool
			)

			if params.CustomProcessStatus == model.RTS {
				sttMeta := v.SttMetaToStruct()
				if sttMeta.DetailSttReverseJourney != nil {
					sttDataRef := sttOri
					SttReturnCityCode = sttDataRef.SttOriginCityID
					SttReturnCityName = sttDataRef.SttOriginCityName
					SttReturnDistrictCode = sttDataRef.SttOriginDistrictID
					SttReturnDistrictName = sttDataRef.SttOriginDistrictName
					SttReturnReceiptName = sttDataRef.SttSenderName
					SttReturnReceiptPhone = sttDataRef.SttSenderPhone
					SttReturnReceiptAddress = sttDataRef.SttSenderAddress
					SttReturnReceiptAddressType = sttDataRef.SttRecipientAddressType.String
					isReturnToOriginAddress = true
				}
			}
			// SttResponseCustomProcess
			detailStts[v.SttNo] = &general.SttResponseCustomProcess{
				SttResponse: general.SttResponse{
					SttID:                  v.SttID,
					SttNo:                  v.SttNo,
					SttProductType:         v.SttProductType,
					SttGrossWeight:         v.SttGrossWeight,
					SttVolumeWeight:        v.SttVolumeWeight,
					SttLastStatusID:        v.SttLastStatusID,
					SttCODAmount:           v.SttCODAmount,
					SttDestinationCityID:   v.SttDestinationCityID,
					SttDestinationCityName: v.SttDestinationCityName,
					SttOriginCityID:        v.SttOriginCityID,
					SttOriginCityName:      v.SttOriginCityName,
					SttChargeableWeight:    v.SttChargeableWeight,
					SttTotalPiece:          v.SttTotalPiece,
					BookedAt:               &v.SttCreatedAt,
					POSName:                v.SttBookedName,
					SttElexysNo:            v.SttElexysNo.Value(),
					BookedForID:            v.SttBookedForID,
					BookedForName:          v.SttBookedForName,
					BookedForType:          v.SttBookedByType,
				},
				SttReturnLastStatus:         model.REROUTE,
				SttReturnCityCode:           SttReturnCityCode,
				SttReturnCityName:           SttReturnCityName,
				SttReturnDistrictCode:       SttReturnDistrictCode,
				SttReturnDistrictName:       SttReturnDistrictName,
				SttReturnReceiptName:        SttReturnReceiptName,
				SttReturnReceiptPhone:       SttReturnReceiptPhone,
				SttReturnReceiptAddress:     SttReturnReceiptAddress,
				SttReturnReceiptAddressType: SttReturnReceiptAddressType,
				IsReturnToOriginAddress:     isReturnToOriginAddress,
			}

			res.Stt = append(
				res.Stt,
				detailStts[v.SttNo],
			)
		}

		// build detail piece(s) each of stt
		detailStts[v.SttNo].Piece = append(
			detailStts[v.SttNo].Piece,
			general.SttPieceResponse{
				SttPieceID:           v.SttPieceID,
				SttPieceNo:           v.SttPieceNo,
				SttPieceGrossWeight:  v.SttPieceGrossWeight,
				SttPieceVolumeWeight: v.SttPieceVolumeWeight,
				SttPieceLastStatusID: v.SttPieceLastStatusID,
			},
		)
	}

	now := time.Now()
	resC1 := customProcess.DetailSttCustomProcessResponse{}
	resc1String := `{"is_paid":true,"is_allow_update_status":true,"error_message":"","stt":[{"stt_id":0,"stt_no":"88LP1695020714440","stt_product_type":"","stt_total_piece":0,"stt_destination_city_id":"JOG","stt_destination_city_name":"","stt_origin_city_id":"CGK","stt_origin_city_name":"","stt_gross_weight":0,"stt_volume_weight":0,"stt_chargeable_weight":0,"stt_last_status_id":"STI-DEST","booked_at":"2023-09-20T06:44:46.036346+07:00","stt_payment_status":"","booked_for_type":"pos","stt_no_ref_external":"","stt_destination_district_ursacode":"","package_number":null,"stt_region_id":"","stt_region_name":"","stt_destination_region_name":"","stt_origin_region_name":"","sti_dest_one_bag_scan":false,"stt_return_last_status":"","stt_return_city_code":"CGK","stt_return_city_name":"","stt_return_district_code":"","stt_return_district_name":"","stt_return_receipt_name":"","stt_return_receipt_phone":"","stt_return_receipt_address":"jalan mawar","stt_return_receipt_address_type":"","is_return_to_origin_address":true,"piece":[{"stt_piece_id":0,"stt_piece_gross_weight":0,"stt_piece_volume_weight":0,"stt_piece_no":0}]}]}`
	json.Unmarshal([]byte(resc1String), &resC1)
	resC1.Stt[0].BookedAt = &now

	resReverseC1 := customProcess.DetailSttCustomProcessResponse{}
	resReverseC1String := `{"is_paid":true,"is_allow_update_status":true,"error_message":"","stt":[{"stt_id":0,"stt_no":"77LP1691468726883","stt_product_type":"","stt_total_piece":0,"stt_destination_city_id":"JOG","stt_destination_city_name":"","stt_origin_city_id":"CGK","stt_origin_city_name":"","stt_gross_weight":0,"stt_volume_weight":0,"stt_chargeable_weight":0,"stt_last_status_id":"STI-DEST","booked_at":"2019-10-12T07:20:50+07:00","stt_payment_status":"","stt_no_ref_external":"","stt_destination_district_ursacode":"","stt_region_id":"","stt_region_name":"","stt_destination_region_name":"","stt_origin_region_name":"","sti_dest_one_bag_scan":false,"stt_return_last_status":"REROUTE","stt_return_city_code":"CGK","stt_return_city_name":"JAKARTA","stt_return_district_code":"","stt_return_district_name":"","stt_return_receipt_name":"","stt_return_receipt_phone":"","stt_return_receipt_address":"jalan mawar","stt_return_receipt_address_type":"","is_return_to_origin_address":true,"piece":[{"stt_piece_id":0,"stt_piece_gross_weight":0,"stt_piece_volume_weight":0,"stt_piece_no":0}]}]}`
	json.Unmarshal([]byte(resReverseC1String), &resReverseC1)
	resReverseC1.Stt[0].BookedAt = &now

	testTable := []struct {
		name       string
		resp       *customProcess.DetailSttCustomProcessResponse
		params     *customProcess.DetailCustomProcessRequest
		errResp    bool
		errDetails error
		before     func()
	}{
		{
			name: "error get shipment stt c1",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           71,
				CustomProcessStatus: model.RTS,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.PARTNER,
				PartnerType:         model.CONSOLE,
				SttNo:               "88LP1695020714440",
			},
			errResp:    true,
			errDetails: fmt.Errorf("error db"),
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "88LP1695020714440",
							SttLastStatusID:      model.STIDEST,
							SttShipmentID:        "C1PIBT9E1",
							SttOriginCityID:      "CGK",
							SttDestinationCityID: "JOG",
							SttCreatedAt:         time.Now(),
							SttIsCOD:             true,
							SttMeta:              `{"estimate_sla":"3 - 4 Hari","origin_city_name":"JAKARTA","origin_district_name":"KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT","destination_city_name":"JOGJAKARTA","destination_district_name":"DEPOK, SLEMAN","ticket_code":"","other_shipper_ticket_code":null,"booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":"","retail_tariff":{"city_rates":7000,"forward_rates":2500,"chargeable_weight":1,"gross_weight":1,"volume_weight":0.16666666666666666,"shipping_cost":9500,"commodity_surcharge":0,"heavy_weight_surcharge":0,"document_surcharge":0,"insurance_rates":0,"insurance_name":"Insurance Free","woodpacking_rates":0,"total_tariff":9500,"tax_rates":0,"bm_tax_rate":0,"ppn_tax_rate":0,"pph_tax_rate":0,"origin_district_rate":1500,"destination_district_rate":1000,"publish_rate":5000,"shipping_surcharge_rate":2000,"heavy_weight_surcharge_remarks":"[{\"heavy_weight_surcharge\":0,\"gross_weight\":1}]","estimate_sla":"3 - 4 Hari","cod_amount":0,"cod_fee":0,"total_tariff_before_discount":9500,"is_discount_exceed_max_promo":false,"is_promo":false,"discount_type":"","discount":0,"parameter_calculation":"","total_discount":0,"publish_rate_after_discount":5000,"shipping_surcharge_rate_after_discount":2000,"origin_district_rate_after_discount":1500,"destination_district_rate_after_discount":1000,"document_surcharge_after_discount":0,"commodity_surcharge_after_discount":0,"heavy_weight_surcharge_after_discount":0,"woodpacking_rates_after_discount":0,"insurance_rates_after_discount":0,"cod_fee_after_discount":0,"total_tariff_after_discount":9500,"total_surcharge_after_discount":0},"detail_calculate_retail_tariff":[{"status":"BKD","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":true,"calculated_at":"2023-09-19T01:21:52.578484624+07:00"}],"is_stt_crossdocking":false,"client_payment_method":"invoice","client_cod_config_amount":"goods_price","client_cod_shipment_discount":0,"rate_vat_shipment":1.1,"rate_vat_cod":11,"total_tariff_return":12500}`,
						},
					},
				}, nil).Once()
				s.shipmentRepo.On("Get", mock.Anything, mock.Anything).Return(nil, fmt.Errorf("error db")).Once()
				s.DictionaryError.On("ErrSTTCannotBeProccessed", model.CountryID).Return(`STT tidak dapat diproses`)
			},
		},
		{
			name: "error get reverse shipment stt c1",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           41,
				CustomProcessStatus: model.RTS,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.PARTNER,
				PartnerType:         model.CONSOLE,
				SttNo:               "78LP1695168213214123",
			},
			errResp:    true,
			errDetails: fmt.Errorf("error db"),
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "78LP1695168213214",
							SttLastStatusID:      model.STIDEST,
							SttOriginCityID:      "CGK",
							SttDestinationCityID: "JOG",
							SttCreatedAt:         time.Now(),
							SttIsCOD:             true,
							SttMeta:              `{"estimate_sla":"3 - 4 Hari","origin_city_name":"JAKARTA","origin_district_name":"PALMERAH, PALMERAH, JAKARTA BARAT","destination_city_name":"JOGJAKARTA","destination_district_name":"DEPOK, SLEMAN","ticket_code":"","other_shipper_ticket_code":null,"booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":"","retail_tariff":{"city_rates":661500,"forward_rates":101000,"chargeable_weight":101,"gross_weight":101,"volume_weight":0.16666666666666666,"shipping_cost":762500,"commodity_surcharge":0,"heavy_weight_surcharge":198450,"document_surcharge":0,"insurance_rates":0,"insurance_name":"Insurance Free","woodpacking_rates":0,"total_tariff":960950,"tax_rates":0,"bm_tax_rate":0,"ppn_tax_rate":0,"pph_tax_rate":0,"origin_district_rate":0,"destination_district_rate":101000,"publish_rate":473000,"shipping_surcharge_rate":188500,"heavy_weight_surcharge_remarks":"[{\"heavy_weight_surcharge\":198450,\"gross_weight\":101}]","estimate_sla":"3 - 4 Hari","cod_amount":0,"cod_fee":0,"total_tariff_before_discount":960950,"is_discount_exceed_max_promo":false,"is_promo":false,"discount_type":"","discount":0,"parameter_calculation":"","total_discount":0,"publish_rate_after_discount":473000,"shipping_surcharge_rate_after_discount":188500,"origin_district_rate_after_discount":0,"destination_district_rate_after_discount":101000,"document_surcharge_after_discount":0,"commodity_surcharge_after_discount":0,"heavy_weight_surcharge_after_discount":198450,"woodpacking_rates_after_discount":0,"insurance_rates_after_discount":0,"cod_fee_after_discount":0,"total_tariff_after_discount":960950,"total_surcharge_after_discount":0},"detail_calculate_retail_tariff":[{"status":"BKD","is_calculated":true,"calculated_at":"2023-09-20T07:03:35.227541905+07:00"}],"detail_stt_reverse_journey":{"reverse_stt_no":"88LP1695019810209","reverse_stt_shipment_id":"C1PIBT9D2","reverse_stt_last_status_id":"MISBOOKING","reverse_status_id":"REROUTE","root_reverse_stt_no":"88LP1695019810209","root_reverse_stt_shipment_id":"C1PIBT9D2","root_reverse_stt_last_status_id":"MISBOOKING","reverse_charged_pos_id":0,"reverse_charged_console_id":0},"is_stt_crossdocking":false,"client_payment_method":"","client_cod_config_amount":"","client_cod_shipment_discount":0,"rate_vat_shipment":0,"rate_vat_cod":0,"total_tariff_return":0}`,
						},
					},
				}, nil).Once()
				s.sttRepo.On("Get", mock.Anything, mock.Anything).Return(&model.Stt{
					SttNo:         "88LP1695019810209",
					SttShipmentID: "C1PIBT9D",
				}, nil).Once()
				s.shipmentRepo.On("Get", mock.Anything, mock.Anything).Return(nil, fmt.Errorf("error db")).Once()
			},
		},
		{
			name: "error get reverse stt origin c1",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           41,
				CustomProcessStatus: model.RTS,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.PARTNER,
				PartnerType:         model.CONSOLE,
				SttNo:               "78LP1695168213214",
			},
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to get Stt Reverse data",
				"id": "Gagal mendapatkan data Stt Reverse",
			}),
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "78LP16951682132141",
							SttLastStatusID:      model.STIDEST,
							SttOriginCityID:      "CGK",
							SttDestinationCityID: "JOG",
							SttCreatedAt:         time.Now(),
							SttIsCOD:             true,
							SttMeta:              `{"estimate_sla":"3 - 4 Hari","origin_city_name":"JAKARTA","origin_district_name":"PALMERAH, PALMERAH, JAKARTA BARAT","destination_city_name":"JOGJAKARTA","destination_district_name":"DEPOK, SLEMAN","ticket_code":"","other_shipper_ticket_code":null,"booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":"","retail_tariff":{"city_rates":661500,"forward_rates":101000,"chargeable_weight":101,"gross_weight":101,"volume_weight":0.16666666666666666,"shipping_cost":762500,"commodity_surcharge":0,"heavy_weight_surcharge":198450,"document_surcharge":0,"insurance_rates":0,"insurance_name":"Insurance Free","woodpacking_rates":0,"total_tariff":960950,"tax_rates":0,"bm_tax_rate":0,"ppn_tax_rate":0,"pph_tax_rate":0,"origin_district_rate":0,"destination_district_rate":101000,"publish_rate":473000,"shipping_surcharge_rate":188500,"heavy_weight_surcharge_remarks":"[{\"heavy_weight_surcharge\":198450,\"gross_weight\":101}]","estimate_sla":"3 - 4 Hari","cod_amount":0,"cod_fee":0,"total_tariff_before_discount":960950,"is_discount_exceed_max_promo":false,"is_promo":false,"discount_type":"","discount":0,"parameter_calculation":"","total_discount":0,"publish_rate_after_discount":473000,"shipping_surcharge_rate_after_discount":188500,"origin_district_rate_after_discount":0,"destination_district_rate_after_discount":101000,"document_surcharge_after_discount":0,"commodity_surcharge_after_discount":0,"heavy_weight_surcharge_after_discount":198450,"woodpacking_rates_after_discount":0,"insurance_rates_after_discount":0,"cod_fee_after_discount":0,"total_tariff_after_discount":960950,"total_surcharge_after_discount":0},"detail_calculate_retail_tariff":[{"status":"BKD","is_calculated":true,"calculated_at":"2023-09-20T07:03:35.227541905+07:00"}],"detail_stt_reverse_journey":{"reverse_stt_no":"88LP1695019810209","reverse_stt_shipment_id":"C1PIBT9D3","reverse_stt_last_status_id":"MISBOOKING","reverse_status_id":"REROUTE","root_reverse_stt_no":"88LP1695019810209","root_reverse_stt_shipment_id":"C1PIBT9D3","root_reverse_stt_last_status_id":"MISBOOKING","reverse_charged_pos_id":0,"reverse_charged_console_id":0},"is_stt_crossdocking":false,"client_payment_method":"","client_cod_config_amount":"","client_cod_shipment_discount":0,"rate_vat_shipment":0,"rate_vat_cod":0,"total_tariff_return":0}`,
						},
					},
				}, nil).Once()
				s.sttRepo.On("Get", mock.Anything, mock.Anything).Return(nil, fmt.Errorf("error")).Once()
			},
		},
		{
			name: "error get partner in reverse stt origin c1",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           41,
				CustomProcessStatus: model.RTS,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.PARTNER,
				PartnerType:         model.CONSOLE,
				SttNo:               "78LP1695168213214",
			},
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to get partner data",
				"id": "Gagal mendapatkan data partner",
			}),
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "78LP1695168213214",
							SttLastStatusID:      model.STIDEST,
							SttOriginCityID:      "CGK",
							SttDestinationCityID: "JOG",
							SttCreatedAt:         time.Now(),
							SttIsCOD:             true,
							SttMeta:              `{"estimate_sla":"3 - 4 Hari","origin_city_name":"JAKARTA","origin_district_name":"PALMERAH, PALMERAH, JAKARTA BARAT","destination_city_name":"JOGJAKARTA","destination_district_name":"DEPOK, SLEMAN","ticket_code":"","other_shipper_ticket_code":null,"booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":"","retail_tariff":{"city_rates":661500,"forward_rates":101000,"chargeable_weight":101,"gross_weight":101,"volume_weight":0.16666666666666666,"shipping_cost":762500,"commodity_surcharge":0,"heavy_weight_surcharge":198450,"document_surcharge":0,"insurance_rates":0,"insurance_name":"Insurance Free","woodpacking_rates":0,"total_tariff":960950,"tax_rates":0,"bm_tax_rate":0,"ppn_tax_rate":0,"pph_tax_rate":0,"origin_district_rate":0,"destination_district_rate":101000,"publish_rate":473000,"shipping_surcharge_rate":188500,"heavy_weight_surcharge_remarks":"[{\"heavy_weight_surcharge\":198450,\"gross_weight\":101}]","estimate_sla":"3 - 4 Hari","cod_amount":0,"cod_fee":0,"total_tariff_before_discount":960950,"is_discount_exceed_max_promo":false,"is_promo":false,"discount_type":"","discount":0,"parameter_calculation":"","total_discount":0,"publish_rate_after_discount":473000,"shipping_surcharge_rate_after_discount":188500,"origin_district_rate_after_discount":0,"destination_district_rate_after_discount":101000,"document_surcharge_after_discount":0,"commodity_surcharge_after_discount":0,"heavy_weight_surcharge_after_discount":198450,"woodpacking_rates_after_discount":0,"insurance_rates_after_discount":0,"cod_fee_after_discount":0,"total_tariff_after_discount":960950,"total_surcharge_after_discount":0},"detail_calculate_retail_tariff":[{"status":"BKD","is_calculated":true,"calculated_at":"2023-09-20T07:03:35.227541905+07:00"}],"detail_stt_reverse_journey":{"reverse_stt_no":"88LP1695019810209","reverse_stt_shipment_id":"C1PIBT9D","reverse_stt_last_status_id":"MISBOOKING","reverse_status_id":"REROUTE","root_reverse_stt_no":"88LP1695019810209","root_reverse_stt_shipment_id":"C1PIBT9D","root_reverse_stt_last_status_id":"MISBOOKING","reverse_charged_pos_id":0,"reverse_charged_console_id":0},"is_stt_crossdocking":false,"client_payment_method":"","client_cod_config_amount":"","client_cod_shipment_discount":0,"rate_vat_shipment":0,"rate_vat_cod":0,"total_tariff_return":0}`,
						},
					},
				}, nil).Once()
				shipmentMeta := `{"cod_handling": "specialcod"}`
				s.shipmentRepo.On("Get", mock.Anything, mock.Anything).Return(&model.Shipment{
					ShipmentID: 1,
					ShipmentPackets: []model.ShipmentPacket{
						{
							ShipmentPacketID: 3,
						},
					},
					ShipmentMeta: &shipmentMeta,
				}, nil).Once()
				s.sttRepo.On("Get", mock.Anything, mock.Anything).Return(&model.Stt{
					SttNo:                  "88LP1695020714440",
					SttLastStatusID:        model.STIDEST,
					SttShipmentID:          "C1PIBT9E",
					SttOriginCityID:        "CGK",
					SttOriginCityName:      "JAKARTA",
					SttDestinationCityID:   "JOG",
					SttDestinationCityName: "JOGJAKARTA",
					SttCreatedAt:           time.Now(),
					SttIsCOD:               true,
					SttBookedByType:        model.POS,
					SttBookedForType:       model.POS,
					SttMeta:                `{"estimate_sla":"3 - 4 Hari","origin_city_name":"JAKARTA","origin_district_name":"KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT","destination_city_name":"JOGJAKARTA","destination_district_name":"DEPOK, SLEMAN","ticket_code":"","other_shipper_ticket_code":null,"booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":"","retail_tariff":{"city_rates":7000,"forward_rates":2500,"chargeable_weight":1,"gross_weight":1,"volume_weight":0.16666666666666666,"shipping_cost":9500,"commodity_surcharge":0,"heavy_weight_surcharge":0,"document_surcharge":0,"insurance_rates":0,"insurance_name":"Insurance Free","woodpacking_rates":0,"total_tariff":9500,"tax_rates":0,"bm_tax_rate":0,"ppn_tax_rate":0,"pph_tax_rate":0,"origin_district_rate":1500,"destination_district_rate":1000,"publish_rate":5000,"shipping_surcharge_rate":2000,"heavy_weight_surcharge_remarks":"[{\"heavy_weight_surcharge\":0,\"gross_weight\":1}]","estimate_sla":"3 - 4 Hari","cod_amount":0,"cod_fee":0,"total_tariff_before_discount":9500,"is_discount_exceed_max_promo":false,"is_promo":false,"discount_type":"","discount":0,"parameter_calculation":"","total_discount":0,"publish_rate_after_discount":5000,"shipping_surcharge_rate_after_discount":2000,"origin_district_rate_after_discount":1500,"destination_district_rate_after_discount":1000,"document_surcharge_after_discount":0,"commodity_surcharge_after_discount":0,"heavy_weight_surcharge_after_discount":0,"woodpacking_rates_after_discount":0,"insurance_rates_after_discount":0,"cod_fee_after_discount":0,"total_tariff_after_discount":9500,"total_surcharge_after_discount":0},"detail_calculate_retail_tariff":[{"status":"BKD","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":true,"calculated_at":"2023-09-19T01:21:52.578484624+07:00"}],"is_stt_crossdocking":false,"client_payment_method":"invoice","client_cod_config_amount":"goods_price","client_cod_shipment_discount":0,"rate_vat_shipment":1.1,"rate_vat_cod":11,"total_tariff_return":12500}`,
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(nil, fmt.Errorf("error")).Once()
			},
		},
		{
			name: "success reverse stt origin c1",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           41,
				CustomProcessStatus: model.RTS,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.PARTNER,
				PartnerType:         model.CONSOLE,
				SttNo:               "77LP1691468726883",
			},
			resp: &resReverseC1,
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()

				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "77LP1691468726883",
							SttNoRefExternal:     "88LP1695020714440",
							SttLastStatusID:      model.STIDEST,
							SttOriginCityID:      "CGK",
							SttDestinationCityID: "JOG",
							SttCreatedAt:         now,
							SttIsCOD:             true,
							SttMeta:              `{"estimate_sla":"3 - 4 Hari","origin_city_name":"JAKARTA","origin_district_name":"PALMERAH, PALMERAH, JAKARTA BARAT","destination_city_name":"JOGJAKARTA","destination_district_name":"DEPOK, SLEMAN","ticket_code":"","other_shipper_ticket_code":null,"booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":"","retail_tariff":{"city_rates":661500,"forward_rates":101000,"chargeable_weight":101,"gross_weight":101,"volume_weight":0.16666666666666666,"shipping_cost":762500,"commodity_surcharge":0,"heavy_weight_surcharge":198450,"document_surcharge":0,"insurance_rates":0,"insurance_name":"Insurance Free","woodpacking_rates":0,"total_tariff":960950,"tax_rates":0,"bm_tax_rate":0,"ppn_tax_rate":0,"pph_tax_rate":0,"origin_district_rate":0,"destination_district_rate":101000,"publish_rate":473000,"shipping_surcharge_rate":188500,"heavy_weight_surcharge_remarks":"[{\"heavy_weight_surcharge\":198450,\"gross_weight\":101}]","estimate_sla":"3 - 4 Hari","cod_amount":0,"cod_fee":0,"total_tariff_before_discount":960950,"is_discount_exceed_max_promo":false,"is_promo":false,"discount_type":"","discount":0,"parameter_calculation":"","total_discount":0,"publish_rate_after_discount":473000,"shipping_surcharge_rate_after_discount":188500,"origin_district_rate_after_discount":0,"destination_district_rate_after_discount":101000,"document_surcharge_after_discount":0,"commodity_surcharge_after_discount":0,"heavy_weight_surcharge_after_discount":198450,"woodpacking_rates_after_discount":0,"insurance_rates_after_discount":0,"cod_fee_after_discount":0,"total_tariff_after_discount":960950,"total_surcharge_after_discount":0},"detail_calculate_retail_tariff":[{"status":"BKD","is_calculated":true,"calculated_at":"2023-09-20T07:03:35.227541905+07:00"}],"detail_stt_reverse_journey":{"reverse_stt_no":"88LP1695019810209","reverse_stt_shipment_id":"C1PIBT9D","reverse_stt_last_status_id":"MISBOOKING","reverse_status_id":"REROUTE","root_reverse_stt_no":"88LP1695019810209","root_reverse_stt_shipment_id":"C1PIBT9D","root_reverse_stt_last_status_id":"MISBOOKING","reverse_charged_pos_id":0,"reverse_charged_console_id":0},"is_stt_crossdocking":false,"client_payment_method":"","client_cod_config_amount":"","client_cod_shipment_discount":0,"rate_vat_shipment":0,"rate_vat_cod":0,"total_tariff_return":0}`,
						},
					},
				}, nil).Once()
				shipmentMeta := `{"cod_handling": "specialcod"}`
				s.shipmentRepo.On("Get", mock.Anything, mock.Anything).Return(&model.Shipment{
					ShipmentID: 1,
					ShipmentPackets: []model.ShipmentPacket{
						{
							ShipmentPacketID: 2,
						},
					},
					ShipmentMeta: &shipmentMeta,
				}, nil).Once()
				s.sttRepo.On("Get", mock.Anything, mock.Anything).Return(&model.Stt{
					SttNo:                  "88LP1695020714440",
					SttLastStatusID:        model.STIDEST,
					SttShipmentID:          "C1PIBT9E",
					SttOriginCityID:        "CGK",
					SttOriginCityName:      "JAKARTA",
					SttDestinationCityID:   "JOG",
					SttDestinationCityName: "JOGJAKARTA",
					SttCreatedAt:           time.Now(),
					SttIsCOD:               true,
					SttBookedByType:        model.POS,
					SttBookedForType:       model.POS,
					SttMeta:                `{"estimate_sla":"3 - 4 Hari","origin_city_name":"JAKARTA","origin_district_name":"KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT","destination_city_name":"JOGJAKARTA","destination_district_name":"DEPOK, SLEMAN","ticket_code":"","other_shipper_ticket_code":null,"booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":"","retail_tariff":{"city_rates":7000,"forward_rates":2500,"chargeable_weight":1,"gross_weight":1,"volume_weight":0.16666666666666666,"shipping_cost":9500,"commodity_surcharge":0,"heavy_weight_surcharge":0,"document_surcharge":0,"insurance_rates":0,"insurance_name":"Insurance Free","woodpacking_rates":0,"total_tariff":9500,"tax_rates":0,"bm_tax_rate":0,"ppn_tax_rate":0,"pph_tax_rate":0,"origin_district_rate":1500,"destination_district_rate":1000,"publish_rate":5000,"shipping_surcharge_rate":2000,"heavy_weight_surcharge_remarks":"[{\"heavy_weight_surcharge\":0,\"gross_weight\":1}]","estimate_sla":"3 - 4 Hari","cod_amount":0,"cod_fee":0,"total_tariff_before_discount":9500,"is_discount_exceed_max_promo":false,"is_promo":false,"discount_type":"","discount":0,"parameter_calculation":"","total_discount":0,"publish_rate_after_discount":5000,"shipping_surcharge_rate_after_discount":2000,"origin_district_rate_after_discount":1500,"destination_district_rate_after_discount":1000,"document_surcharge_after_discount":0,"commodity_surcharge_after_discount":0,"heavy_weight_surcharge_after_discount":0,"woodpacking_rates_after_discount":0,"insurance_rates_after_discount":0,"cod_fee_after_discount":0,"total_tariff_after_discount":9500,"total_surcharge_after_discount":0},"detail_calculate_retail_tariff":[{"status":"BKD","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":true,"calculated_at":"2023-09-19T01:21:52.578484624+07:00"}],"is_stt_crossdocking":false,"client_payment_method":"invoice","client_cod_config_amount":"goods_price","client_cod_shipment_discount":0,"rate_vat_shipment":1.1,"rate_vat_cod":11,"total_tariff_return":12500}`,
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						Address: "jalan mawar",
					},
				}, nil).Once()
			},
		},
		{
			name: "error get shipment empty shipment packet stt c1",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           71,
				CustomProcessStatus: model.RTS,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.PARTNER,
				PartnerType:         model.CONSOLE,
				SttNo:               "88LP1695020714440",
			},
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting shipment data",
				"id": "Terjadi kesalahan pada saat query getting shipment data",
			}),
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "88LP1695020714440",
							SttLastStatusID:      model.STIDEST,
							SttShipmentID:        "C1PIBT9E",
							SttOriginCityID:      "CGK",
							SttDestinationCityID: "JOG",
							SttCreatedAt:         time.Now(),
							SttIsCOD:             true,
							SttMeta:              `{"estimate_sla":"3 - 4 Hari","origin_city_name":"JAKARTA","origin_district_name":"KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT","destination_city_name":"JOGJAKARTA","destination_district_name":"DEPOK, SLEMAN","ticket_code":"","other_shipper_ticket_code":null,"booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":"","retail_tariff":{"city_rates":7000,"forward_rates":2500,"chargeable_weight":1,"gross_weight":1,"volume_weight":0.16666666666666666,"shipping_cost":9500,"commodity_surcharge":0,"heavy_weight_surcharge":0,"document_surcharge":0,"insurance_rates":0,"insurance_name":"Insurance Free","woodpacking_rates":0,"total_tariff":9500,"tax_rates":0,"bm_tax_rate":0,"ppn_tax_rate":0,"pph_tax_rate":0,"origin_district_rate":1500,"destination_district_rate":1000,"publish_rate":5000,"shipping_surcharge_rate":2000,"heavy_weight_surcharge_remarks":"[{\"heavy_weight_surcharge\":0,\"gross_weight\":1}]","estimate_sla":"3 - 4 Hari","cod_amount":0,"cod_fee":0,"total_tariff_before_discount":9500,"is_discount_exceed_max_promo":false,"is_promo":false,"discount_type":"","discount":0,"parameter_calculation":"","total_discount":0,"publish_rate_after_discount":5000,"shipping_surcharge_rate_after_discount":2000,"origin_district_rate_after_discount":1500,"destination_district_rate_after_discount":1000,"document_surcharge_after_discount":0,"commodity_surcharge_after_discount":0,"heavy_weight_surcharge_after_discount":0,"woodpacking_rates_after_discount":0,"insurance_rates_after_discount":0,"cod_fee_after_discount":0,"total_tariff_after_discount":9500,"total_surcharge_after_discount":0},"detail_calculate_retail_tariff":[{"status":"BKD","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":true,"calculated_at":"2023-09-19T01:21:52.578484624+07:00"}],"is_stt_crossdocking":false,"client_payment_method":"invoice","client_cod_config_amount":"goods_price","client_cod_shipment_discount":0,"rate_vat_shipment":1.1,"rate_vat_cod":11,"total_tariff_return":12500}`,
						},
					},
				}, nil).Once()
				s.shipmentRepo.On("Get", mock.Anything, mock.Anything).Return(&model.Shipment{
					ShipmentID: 2,
				}, nil).Once()
			},
		},
		{
			name: "error get partner stt c1",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           71,
				CustomProcessStatus: model.RTS,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.PARTNER,
				PartnerType:         model.CONSOLE,
				SttNo:               "88LP1695020714440",
			},
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to get partner data",
				"id": "Gagal mendapatkan data partner",
			}),
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "88LP1695020714440",
							SttLastStatusID:      model.STIDEST,
							SttShipmentID:        "C1PIBT9E",
							SttOriginCityID:      "CGK",
							SttDestinationCityID: "JOG",
							SttCreatedAt:         time.Now(),
							SttIsCOD:             true,
							SttBookedByType:      model.POS,
							SttBookedForType:     model.POS,
							SttMeta:              `{"estimate_sla":"3 - 4 Hari","origin_city_name":"JAKARTA","origin_district_name":"KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT","destination_city_name":"JOGJAKARTA","destination_district_name":"DEPOK, SLEMAN","ticket_code":"","other_shipper_ticket_code":null,"booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":"","retail_tariff":{"city_rates":7000,"forward_rates":2500,"chargeable_weight":1,"gross_weight":1,"volume_weight":0.16666666666666666,"shipping_cost":9500,"commodity_surcharge":0,"heavy_weight_surcharge":0,"document_surcharge":0,"insurance_rates":0,"insurance_name":"Insurance Free","woodpacking_rates":0,"total_tariff":9500,"tax_rates":0,"bm_tax_rate":0,"ppn_tax_rate":0,"pph_tax_rate":0,"origin_district_rate":1500,"destination_district_rate":1000,"publish_rate":5000,"shipping_surcharge_rate":2000,"heavy_weight_surcharge_remarks":"[{\"heavy_weight_surcharge\":0,\"gross_weight\":1}]","estimate_sla":"3 - 4 Hari","cod_amount":0,"cod_fee":0,"total_tariff_before_discount":9500,"is_discount_exceed_max_promo":false,"is_promo":false,"discount_type":"","discount":0,"parameter_calculation":"","total_discount":0,"publish_rate_after_discount":5000,"shipping_surcharge_rate_after_discount":2000,"origin_district_rate_after_discount":1500,"destination_district_rate_after_discount":1000,"document_surcharge_after_discount":0,"commodity_surcharge_after_discount":0,"heavy_weight_surcharge_after_discount":0,"woodpacking_rates_after_discount":0,"insurance_rates_after_discount":0,"cod_fee_after_discount":0,"total_tariff_after_discount":9500,"total_surcharge_after_discount":0},"detail_calculate_retail_tariff":[{"status":"BKD","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":true,"calculated_at":"2023-09-19T01:21:52.578484624+07:00"}],"is_stt_crossdocking":false,"client_payment_method":"invoice","client_cod_config_amount":"goods_price","client_cod_shipment_discount":0,"rate_vat_shipment":1.1,"rate_vat_cod":11,"total_tariff_return":12500}`,
						},
					},
				}, nil).Once()
				shipmentMeta := `{"cod_handling": "specialcod"}`
				s.shipmentRepo.On("Get", mock.Anything, mock.Anything).Return(&model.Shipment{
					ShipmentID: 1,
					ShipmentPackets: []model.ShipmentPacket{
						{
							ShipmentPacketID: 1,
						},
					},
					ShipmentMeta: &shipmentMeta,
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(nil, fmt.Errorf("error")).Once()
			},
		},
		{
			name: "success stt c1",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           71,
				CustomProcessStatus: model.RTS,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.PARTNER,
				PartnerType:         model.CONSOLE,
				SttNo:               "88LP1695020714440",
			},
			resp: &resC1,
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:                "88LP1695020714440",
							SttLastStatusID:      model.STIDEST,
							SttShipmentID:        "C1PIBT9E",
							SttOriginCityID:      "CGK",
							SttDestinationCityID: "JOG",
							SttCreatedAt:         now,
							SttIsCOD:             true,
							SttBookedByType:      model.POS,
							SttBookedForType:     model.POS,
							SttMeta:              `{"estimate_sla":"3 - 4 Hari","origin_city_name":"JAKARTA","origin_district_name":"KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT","destination_city_name":"JOGJAKARTA","destination_district_name":"DEPOK, SLEMAN","ticket_code":"","other_shipper_ticket_code":null,"booked_by_external_type":"","booked_by_external_code":"","booked_for_external_type":"","booked_for_external_code":"","retail_tariff":{"city_rates":7000,"forward_rates":2500,"chargeable_weight":1,"gross_weight":1,"volume_weight":0.16666666666666666,"shipping_cost":9500,"commodity_surcharge":0,"heavy_weight_surcharge":0,"document_surcharge":0,"insurance_rates":0,"insurance_name":"Insurance Free","woodpacking_rates":0,"total_tariff":9500,"tax_rates":0,"bm_tax_rate":0,"ppn_tax_rate":0,"pph_tax_rate":0,"origin_district_rate":1500,"destination_district_rate":1000,"publish_rate":5000,"shipping_surcharge_rate":2000,"heavy_weight_surcharge_remarks":"[{\"heavy_weight_surcharge\":0,\"gross_weight\":1}]","estimate_sla":"3 - 4 Hari","cod_amount":0,"cod_fee":0,"total_tariff_before_discount":9500,"is_discount_exceed_max_promo":false,"is_promo":false,"discount_type":"","discount":0,"parameter_calculation":"","total_discount":0,"publish_rate_after_discount":5000,"shipping_surcharge_rate_after_discount":2000,"origin_district_rate_after_discount":1500,"destination_district_rate_after_discount":1000,"document_surcharge_after_discount":0,"commodity_surcharge_after_discount":0,"heavy_weight_surcharge_after_discount":0,"woodpacking_rates_after_discount":0,"insurance_rates_after_discount":0,"cod_fee_after_discount":0,"total_tariff_after_discount":9500,"total_surcharge_after_discount":0},"detail_calculate_retail_tariff":[{"status":"BKD","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":false,"calculated_at":null},{"status":"STT ADJUSTED","is_calculated":true,"calculated_at":"2023-09-19T01:21:52.578484624+07:00"}],"is_stt_crossdocking":false,"client_payment_method":"invoice","client_cod_config_amount":"goods_price","client_cod_shipment_discount":0,"rate_vat_shipment":1.1,"rate_vat_cod":11,"total_tariff_return":12500}`,
						},
					},
				}, nil).Once()
				shipmentMeta := `{"cod_handling": "specialcod"}`
				s.shipmentRepo.On("Get", mock.Anything, mock.Anything).Return(&model.Shipment{
					ShipmentID: 1,
					ShipmentPackets: []model.ShipmentPacket{
						{
							ShipmentPacketID: 1,
						},
					},
					ShipmentMeta: &shipmentMeta,
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						Address: "jalan mawar",
					},
				}, nil).Once()
			},
		},
		{
			name:       "error get customProcessRoleRepo",
			params:     &customProcess.DetailCustomProcessRequest{},
			errResp:    true,
			errDetails: shared.ERR_UNEXPECTED_DB,
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{}, fmt.Errorf("error")).Once()
			},
		},
		{
			name:    "empty get customProcessRoleRepo",
			params:  &customProcess.DetailCustomProcessRequest{},
			resp:    nil,
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Account Type Invalid",
				"id": "Tipe Akun tidak valid",
			}),
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{}, nil).Once()
			},
		},
		{
			name:    "role not valid get customProcessRoleRepo",
			params:  &customProcess.DetailCustomProcessRequest{},
			resp:    nil,
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Status is not valid",
				"id": "Status tidak valid",
			}),
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.HAL,
					},
				}, nil).Once()
			},
		},
		{
			name: "error partner id",
			params: &customProcess.DetailCustomProcessRequest{
				CustomProcessStatus: model.RTS,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               stt.SttNo,
			},
			resp:    nil,
			errResp: true,
			errDetails: shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Partner isn't allow to empty",
				"id": "Partner ID tidak boleh kosong",
			}),
			before: func() {
			},
		},
		{
			name:       "error get partner",
			params:     params,
			resp:       nil,
			errResp:    true,
			errDetails: shared.ERR_UNEXPECTED_DB,
			before: func() {
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(nil, fmt.Errorf("error")).Once()
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
			},
		},
		{
			name:    "error get partner empty",
			params:  params,
			resp:    nil,
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner not found",
				"id": "Partner tidak ditemukan",
			}),
			before: func() {
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(nil, nil).Once()
			},
		},
		{
			name:    "error get partner empty",
			params:  params,
			resp:    nil,
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Partner Type Invalid",
				"id": "Tipe Partner tidak valid",
			}),
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.INTERNAL,
					},
				}, nil).Once()
			},
		},
		{
			name:    "error get SelectDetail",
			params:  params,
			resp:    nil,
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting STT and Piece",
				"id": "Terjadi kesalahan pada saat getting STT and Piece",
			}),
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{}, fmt.Errorf("error")).Once()
			},
		},
		{
			name:   "error get SelectDetail empty",
			params: params,
			resp: &customProcess.DetailSttCustomProcessResponse{
				IsAllowUpdateStatus: false,
				ErrorMessage:        `STT Tidak dapat ditemukan`,
			},
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{}, nil).Once()
				s.DictionaryError.On("ErrDataNotFoundString", model.CountryID, "STT").Return("STT Tidak dapat ditemukan").Once()
			},
		},
		{
			name:    "error Stt No Cannot Be Proccess",
			params:  params,
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt No or Bagging No Cannot Be Processed",
				"id": "Nomor STT atau Nomor Bagging tidak dapat diproses",
			}),
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{Stt: model.Stt{
						SttNo:                "11LP1111111111",
						SttLastStatusID:      model.BAGGING,
						SttOriginCityID:      "CGK1000",
						SttDestinationCityID: "BDO1000",
						SttCreatedAt:         time.Now(),
						SttMeta:              `{ "estimate_sla": "1 - 1 Hari", "origin_city_name": "JAKARTA", "origin_district_name": "KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT", "destination_city_name": "JAKARTA", "destination_district_name": "PALMERAH, JAKARTA BARAT", "ticket_code": "", "other_shipper_ticket_code": null, "booked_by_external_type": "", "booked_by_external_code": "", "booked_for_external_type": "", "booked_for_external_code": "", "detail_calculate_retail_tariff": [ { "status": "BKD", "is_calculated": false, "calculated_at": null } ], "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" }, "is_stt_crossdocking": true, "client_payment_method": "", "client_cod_config_amount": "", "client_cod_shipment_discount": 0, "rate_vat_shipment": 0, "rate_vat_cod": 0, "total_tariff_return": 0 }`,
					}}}, nil).Once()
			},
		},
		{
			name:   "error Stt Cannot Be Proccess cause RTS status",
			params: params,
			resp: &customProcess.DetailSttCustomProcessResponse{
				IsAllowUpdateStatus: false,
				ErrorMessage:        `STT tidak dapat diproses`,
			},
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{Stt: model.Stt{
						SttNo:                "11LP1111111111",
						SttLastStatusID:      model.BKD,
						SttOriginCityID:      "CGK1000",
						SttDestinationCityID: "BDO1000",
						SttCreatedAt:         time.Now(),
						SttMeta:              `{ "estimate_sla": "1 - 1 Hari", "origin_city_name": "JAKARTA", "origin_district_name": "KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT", "destination_city_name": "JAKARTA", "destination_district_name": "PALMERAH, JAKARTA BARAT", "ticket_code": "", "other_shipper_ticket_code": null, "booked_by_external_type": "", "booked_by_external_code": "", "booked_for_external_type": "", "booked_for_external_code": "", "detail_calculate_retail_tariff": [ { "status": "BKD", "is_calculated": false, "calculated_at": null } ], "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" }, "is_stt_crossdocking": true, "client_payment_method": "", "client_cod_config_amount": "", "client_cod_shipment_discount": 0, "rate_vat_shipment": 0, "rate_vat_cod": 0, "total_tariff_return": 0 }`,
					}}}, nil).Once()
			},
		},
		{
			name: "error Stt Cannot Be Proccess cause REROUTE status",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           10,
				CustomProcessStatus: model.REROUTE,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               `11LP1111111111`,
			},
			resp: &customProcess.DetailSttCustomProcessResponse{
				IsAllowUpdateStatus: false,
				ErrorMessage:        `STT tidak dapat diproses`,
			},
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.REROUTE,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{Stt: model.Stt{
						SttNo:                "11LP1111111111",
						SttLastStatusID:      model.BKD,
						SttOriginCityID:      "CGK1000",
						SttDestinationCityID: "BDO1000",
						SttCreatedAt:         time.Now(),
						SttMeta:              `{ "estimate_sla": "1 - 1 Hari", "origin_city_name": "JAKARTA", "origin_district_name": "KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT", "destination_city_name": "JAKARTA", "destination_district_name": "PALMERAH, JAKARTA BARAT", "ticket_code": "", "other_shipper_ticket_code": null, "booked_by_external_type": "", "booked_by_external_code": "", "booked_for_external_type": "", "booked_for_external_code": "", "detail_calculate_retail_tariff": [ { "status": "BKD", "is_calculated": false, "calculated_at": null } ], "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "RTS", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" }, "is_stt_crossdocking": true, "client_payment_method": "", "client_cod_config_amount": "", "client_cod_shipment_discount": 0, "rate_vat_shipment": 0, "rate_vat_cod": 0, "total_tariff_return": 0 }`,
					}}}, nil).Once()
			},
		},
		{
			name: "error Stt Cannot Be Proccess cause Last status stt origin is REROUTE and want to update with RTSHQ",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           10,
				CustomProcessStatus: model.RTS,
				AccountRoleType:     model.SUBCONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               stt.SttNo,
			},
			resp: &customProcess.DetailSttCustomProcessResponse{
				IsAllowUpdateStatus: false,
				ErrorMessage:        `STT tidak dapat diproses`,
			},
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{Stt: model.Stt{
						SttNo:                "11LP1111111111",
						SttLastStatusID:      model.BKD,
						SttOriginCityID:      "CGK1000",
						SttDestinationCityID: "BDO1000",
						SttCreatedAt:         time.Now(),
						SttMeta:              `{ "estimate_sla": "1 - 1 Hari", "origin_city_name": "JAKARTA", "origin_district_name": "KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT", "destination_city_name": "JAKARTA", "destination_district_name": "PALMERAH, JAKARTA BARAT", "ticket_code": "", "other_shipper_ticket_code": null, "booked_by_external_type": "", "booked_by_external_code": "", "booked_for_external_type": "", "booked_for_external_code": "", "detail_calculate_retail_tariff": [ { "status": "BKD", "is_calculated": false, "calculated_at": null } ], "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "REROUTE", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" }, "is_stt_crossdocking": true, "client_payment_method": "", "client_cod_config_amount": "", "client_cod_shipment_discount": 0, "rate_vat_shipment": 0, "rate_vat_cod": 0, "total_tariff_return": 0 }`,
					}}}, nil).Once()
			},
		},
		{
			name: "error Stt Cannot Be Proccess cause Last status MISBOOKING",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           10,
				CustomProcessStatus: model.RTS,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               stt.SttNo,
			},
			resp: &customProcess.DetailSttCustomProcessResponse{
				IsAllowUpdateStatus: false,
				ErrorMessage:        `STT tidak dapat diproses`,
			},
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{Stt: model.Stt{
						SttUpdatedActorRole:  dbr.NewNullString("tes"),
						SttNo:                "11LP1111111111",
						SttLastStatusID:      model.MISBOOKING,
						SttOriginCityID:      "CGK1000",
						SttDestinationCityID: "BDO1000",
						SttCreatedAt:         time.Now(),
						SttMeta:              `{ "estimate_sla": "1 - 1 Hari", "origin_city_name": "JAKARTA", "origin_district_name": "KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT", "destination_city_name": "JAKARTA", "destination_district_name": "PALMERAH, JAKARTA BARAT", "ticket_code": "", "other_shipper_ticket_code": null, "booked_by_external_type": "", "booked_by_external_code": "", "booked_for_external_type": "", "booked_for_external_code": "", "detail_calculate_retail_tariff": [ { "status": "BKD", "is_calculated": false, "calculated_at": null } ], "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "REROUTE", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" }, "is_stt_crossdocking": true, "client_payment_method": "", "client_cod_config_amount": "", "client_cod_shipment_discount": 0, "rate_vat_shipment": 0, "rate_vat_cod": 0, "total_tariff_return": 0 }`,
					}}}, nil).Once()
			},
		},
		{
			name: "error Stt Cannot Be Proccess cause Last status MISBOOKING from POD",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           10,
				CustomProcessStatus: model.MISBOOKING,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               stt.SttNo,
			},
			resp: &customProcess.DetailSttCustomProcessResponse{
				IsAllowUpdateStatus: false,
				ErrorMessage:        `STT tidak dapat diproses`,
			},
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.MISBOOKING,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{Stt: model.Stt{
						SttUpdatedActorRole:  dbr.NewNullString("tes"),
						SttNo:                "11LP1111111111",
						SttLastStatusID:      model.POD,
						SttOriginCityID:      "CGK1000",
						SttDestinationCityID: "BDO1000",
						SttCreatedAt:         time.Now(),
						SttMeta:              `{ "estimate_sla": "1 - 1 Hari", "origin_city_name": "JAKARTA", "origin_district_name": "KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT", "destination_city_name": "JAKARTA", "destination_district_name": "PALMERAH, JAKARTA BARAT", "ticket_code": "", "other_shipper_ticket_code": null, "booked_by_external_type": "", "booked_by_external_code": "", "booked_for_external_type": "", "booked_for_external_code": "", "detail_calculate_retail_tariff": [ { "status": "BKD", "is_calculated": false, "calculated_at": null } ], "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "REROUTE", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" }, "is_stt_crossdocking": true, "client_payment_method": "", "client_cod_config_amount": "", "client_cod_shipment_discount": 0, "rate_vat_shipment": 0, "rate_vat_cod": 0, "total_tariff_return": 0 }`,
					}}}, nil).Once()
			},
		},
		{
			name: "error Stt Cannot Be Proccess cause Last status MISSING to SCRAPCD",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           10,
				CustomProcessStatus: model.SCRAPCD,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               stt.SttNo,
			},
			resp: &customProcess.DetailSttCustomProcessResponse{
				IsAllowUpdateStatus: false,
				ErrorMessage:        `STT tidak dapat diproses`,
			},
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.SCRAPCD,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{Stt: model.Stt{
						SttUpdatedActorRole:  dbr.NewNullString("tes"),
						SttNo:                "11LP1111111111",
						SttLastStatusID:      model.MISSING,
						SttOriginCityID:      "CGK1000",
						SttDestinationCityID: "BDO1000",
						SttCreatedAt:         time.Now(),
						SttMeta:              `{ "estimate_sla": "1 - 1 Hari", "origin_city_name": "JAKARTA", "origin_district_name": "KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT", "destination_city_name": "JAKARTA", "destination_district_name": "PALMERAH, JAKARTA BARAT", "ticket_code": "", "other_shipper_ticket_code": null, "booked_by_external_type": "", "booked_by_external_code": "", "booked_for_external_type": "", "booked_for_external_code": "", "detail_calculate_retail_tariff": [ { "status": "BKD", "is_calculated": false, "calculated_at": null } ], "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "REROUTE", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" }, "is_stt_crossdocking": true, "client_payment_method": "", "client_cod_config_amount": "", "client_cod_shipment_discount": 0, "rate_vat_shipment": 0, "rate_vat_cod": 0, "total_tariff_return": 0 }`,
					}}}, nil).Once()
			},
		},
		{
			name: "error Stt Cannot Be Proccess cause Last status BKD to HALCD",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           10,
				CustomProcessStatus: model.HALCD,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               stt.SttNo,
			},
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt Cannot Be Proccess",
				"id": "Nomor STT tidak dapat diproses",
			}),
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.HALCD,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{Stt: model.Stt{
						SttUpdatedActorRole:  dbr.NewNullString("tes"),
						SttNo:                "11LP1111111111",
						SttLastStatusID:      model.MISSING,
						SttOriginCityID:      "CGK1000",
						SttDestinationCityID: "BDO1000",
						SttCreatedAt:         time.Now(),
						SttMeta:              `{ "estimate_sla": "1 - 1 Hari", "origin_city_name": "JAKARTA", "origin_district_name": "KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT", "destination_city_name": "JAKARTA", "destination_district_name": "PALMERAH, JAKARTA BARAT", "ticket_code": "", "other_shipper_ticket_code": null, "booked_by_external_type": "", "booked_by_external_code": "", "booked_for_external_type": "", "booked_for_external_code": "", "detail_calculate_retail_tariff": [ { "status": "BKD", "is_calculated": false, "calculated_at": null } ], "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "REROUTE", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" }, "is_stt_crossdocking": true, "client_payment_method": "", "client_cod_config_amount": "", "client_cod_shipment_discount": 0, "rate_vat_shipment": 0, "rate_vat_cod": 0, "total_tariff_return": 0 }`,
					}}}, nil).Once()
			},
		},
		{
			name: "error Stt Cannot Be Proccess cause Last status CODREJ to MISBOOKING",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           10,
				CustomProcessStatus: model.MISBOOKING,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               stt.SttNo,
			},
			resp: &customProcess.DetailSttCustomProcessResponse{
				IsAllowUpdateStatus: false,
				ErrorMessage:        `STT tidak dapat diproses`,
			},
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.MISBOOKING,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{Stt: model.Stt{
						SttUpdatedActorRole:  dbr.NewNullString("tes"),
						SttNo:                "11LP1111111111",
						SttLastStatusID:      model.CODREJ,
						SttOriginCityID:      "CGK1000",
						SttDestinationCityID: "BDO1000",
						SttCreatedAt:         time.Now(),
						SttMeta:              `{ "estimate_sla": "1 - 1 Hari", "origin_city_name": "JAKARTA", "origin_district_name": "KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT", "destination_city_name": "JAKARTA", "destination_district_name": "PALMERAH, JAKARTA BARAT", "ticket_code": "", "other_shipper_ticket_code": null, "booked_by_external_type": "", "booked_by_external_code": "", "booked_for_external_type": "", "booked_for_external_code": "", "detail_calculate_retail_tariff": [ { "status": "BKD", "is_calculated": false, "calculated_at": null } ], "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "REROUTE", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" }, "is_stt_crossdocking": true, "client_payment_method": "", "client_cod_config_amount": "", "client_cod_shipment_discount": 0, "rate_vat_shipment": 0, "rate_vat_cod": 0, "total_tariff_return": 0 }`,
					}}}, nil).Once()
			},
		},
		{
			name: "error Stt Cannot Be Proccess cause Last status DAMAGE to CODREJ",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           10,
				CustomProcessStatus: model.CODREJ,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               stt.SttNo,
			},
			resp: &customProcess.DetailSttCustomProcessResponse{
				IsAllowUpdateStatus: false,
				ErrorMessage:        `STT tidak dapat diproses`,
			},
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.CODREJ,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{Stt: model.Stt{
						SttUpdatedActorRole:  dbr.NewNullString("tes"),
						SttNo:                "11LP1111111111",
						SttLastStatusID:      model.DAMAGE,
						SttOriginCityID:      "CGK1000",
						SttDestinationCityID: "BDO1000",
						SttCreatedAt:         time.Now(),
						SttMeta:              `{ "estimate_sla": "1 - 1 Hari", "origin_city_name": "JAKARTA", "origin_district_name": "KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT", "destination_city_name": "JAKARTA", "destination_district_name": "PALMERAH, JAKARTA BARAT", "ticket_code": "", "other_shipper_ticket_code": null, "booked_by_external_type": "", "booked_by_external_code": "", "booked_for_external_type": "", "booked_for_external_code": "", "detail_calculate_retail_tariff": [ { "status": "BKD", "is_calculated": false, "calculated_at": null } ], "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "REROUTE", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" }, "is_stt_crossdocking": true, "client_payment_method": "", "client_cod_config_amount": "", "client_cod_shipment_discount": 0, "rate_vat_shipment": 0, "rate_vat_cod": 0, "total_tariff_return": 0 }`,
					}}}, nil).Once()
			},
		},
		{
			name: "error Stt Cannot Be Proccess cause Last status NOTRECEIVED to POD",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           10,
				CustomProcessStatus: model.POD,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               stt.SttNo,
			},
			resp: &customProcess.DetailSttCustomProcessResponse{
				IsAllowUpdateStatus: false,
				ErrorMessage:        `STT tidak dapat diproses`,
			},
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.POD,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{Stt: model.Stt{
						SttUpdatedActorRole:  dbr.NewNullString("tes"),
						SttNo:                "11LP1111111111",
						SttLastStatusID:      model.NOTRECEIVED,
						SttOriginCityID:      "CGK1000",
						SttDestinationCityID: "BDO1000",
						SttCreatedAt:         time.Now(),
						SttMeta:              `{ "estimate_sla": "1 - 1 Hari", "origin_city_name": "JAKARTA", "origin_district_name": "KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT", "destination_city_name": "JAKARTA", "destination_district_name": "PALMERAH, JAKARTA BARAT", "ticket_code": "", "other_shipper_ticket_code": null, "booked_by_external_type": "", "booked_by_external_code": "", "booked_for_external_type": "", "booked_for_external_code": "", "detail_calculate_retail_tariff": [ { "status": "BKD", "is_calculated": false, "calculated_at": null } ], "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "REROUTE", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" }, "is_stt_crossdocking": true, "client_payment_method": "", "client_cod_config_amount": "", "client_cod_shipment_discount": 0, "rate_vat_shipment": 0, "rate_vat_cod": 0, "total_tariff_return": 0 }`,
					}}}, nil).Once()
			},
		},
		{
			name: "error Stt Cannot Be Proccess cause Last status MISROUTE to POD",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           10,
				CustomProcessStatus: model.POD,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               stt.SttNo,
			},
			resp: &customProcess.DetailSttCustomProcessResponse{
				IsAllowUpdateStatus: false,
				ErrorMessage:        `STT tidak dapat diproses`,
			},
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.POD,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return([]model.SttDetailResult{
					{Stt: model.Stt{
						SttUpdatedActorRole:  dbr.NewNullString("tes"),
						SttNo:                "11LP1111111111",
						SttLastStatusID:      model.MISROUTE,
						SttOriginCityID:      "CGK1000",
						SttDestinationCityID: "BDO1000",
						SttCreatedAt:         time.Now(),
						SttMeta:              `{ "estimate_sla": "1 - 1 Hari", "origin_city_name": "JAKARTA", "origin_district_name": "KEDOYA SELATAN, KEBON JERUK, JAKARTA BARAT", "destination_city_name": "JAKARTA", "destination_district_name": "PALMERAH, JAKARTA BARAT", "ticket_code": "", "other_shipper_ticket_code": null, "booked_by_external_type": "", "booked_by_external_code": "", "booked_for_external_type": "", "booked_for_external_code": "", "detail_calculate_retail_tariff": [ { "status": "BKD", "is_calculated": false, "calculated_at": null } ], "detail_stt_reverse_journey": { "reverse_stt_no": "11LP1691461672142", "reverse_stt_shipment_id": "", "reverse_stt_last_status_id": "TRANSIT", "reverse_status_id": "REROUTE", "root_reverse_stt_no": "11LP1691461672142", "root_reverse_stt_shipment_id": "", "root_reverse_stt_last_status_id": "TRANSIT", "reverse_charged_pos_id": 0, "reverse_charged_console_id": 0, "root_origin_city_id": "CGK", "root_origin_district_id": "DKI00102", "root_sender_name": "tusfendi", "root_sender_phone": "*************", "root_sender_address": "kedoyaaa" }, "is_stt_crossdocking": true, "client_payment_method": "", "client_cod_config_amount": "", "client_cod_shipment_discount": 0, "rate_vat_shipment": 0, "rate_vat_cod": 0, "total_tariff_return": 0 }`,
					}}}, nil).Once()
			},
		},
		{
			name: "error get stt origin",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           10,
				CustomProcessStatus: model.RTS,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               stt2.SttNo,
			},
			resp:    nil,
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to get Stt Reverse data",
				"id": "Gagal mendapatkan data Stt Reverse",
			}),
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return(mockSttPieceDetail, nil).Once()
				s.sttRepo.On("Get", mock.Anything, mock.Anything).Return(sttOri, fmt.Errorf("error")).Once()

				s.sttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: "19LP1691461937437",
				}).Return([]model.SttDetailResult{}, nil).Once()
			},
		},
		{
			name: "success",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           10,
				CustomProcessStatus: model.RTS,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               stt2.SttNo,
			},
			resp: &res,
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, mock.Anything).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.RTS,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, mock.Anything, mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, mock.Anything).Return(mockSttPieceDetail, nil).Once()
				s.sttRepo.On("Get", mock.Anything, mock.Anything).Return(sttOri, nil).Once()

				s.sttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: "19LP1691461937437",
				}).Return([]model.SttDetailResult{}, nil).Once()
			},
		},
		{
			name: "TestViewDetailStt_ClaimCodDfodFalse",
			params: &customProcess.DetailCustomProcessRequest{
				PartnerID:           10,
				CustomProcessStatus: model.CLAIM,
				AccountRoleType:     model.CONSOLE,
				AccountType:         model.INTERNAL,
				SttNo:               stt2.SttNo,
				Token:               "token",
			},
			resp: &customProcess.DetailSttCustomProcessResponse{
				IsPaid:              false,
				IsAllowUpdateStatus: false,
				ErrorMessage:        "STT tidak dapat diproses",
			},
			before: func() {
				s.accountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()
				s.customProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{
					AccountRoleType: model.CONSOLE,
					AccountType:     model.INTERNAL,
				}).Return([]model.RoleCustomProcess{
					{
						RoleCustomProcessAccountType: model.CONSOLE,
						RoleCustomProcessType:        model.PARTNER,
						RoleCustomProcessStatus:      model.CLAIM,
					},
				}, nil).Once()
				s.partnerRepo.On("GetByID", mock.Anything, 10, "token").Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Type: model.CONSOLE,
					},
				}, nil).Once()
				s.sttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: stt2.SttNo,
				}).Return(mockSttPieceDetail, nil).Once()
				mockSttPieceDetail[0].SttPieceID = 1
				mockSttPieceDetail[0].SttIsCOD = false
				mockSttPieceDetail[0].SttIsDFOD = false

				s.sttPieceHistoryRepo.On("Get", mock.Anything, &model.SttPieceHistoryViewParam{
					SttPieceHistorySttPieceID: 1,
					SttPieceHistoryStatus:     model.CLAIM,
				}).Return(&model.SttPieceHistory{
					HistoryID: 1,
				}, nil).Once()
			},
		},
	}

	for _, tt := range testTable {
		s.T().Run(tt.name, func(t *testing.T) {

			tt.before()
			// got, err := s.usecase.ViewDetailStt(s.ctx, tt.params)
			// if (err != nil) != tt.errResp || !assert.Equal(t, err, tt.errDetails) {
			// 	t.Errorf("customprocessCtx.ViewDetailStt() error = %v, wantErr %v", err, tt.errDetails)
			// 	return
			// }
			// if !reflect.DeepEqual(got, tt.resp) {
			// 	t.Errorf("customProcessCtx.ViewDetailStt() = %v, want %v", got, tt.resp)
			// }
		})
	}
}

func TestViewDetailCustomProcess(t *testing.T) {
	type args struct {
		ctx    context.Context
		params *customProcess.ViewDetailCustomProcessRequest
	}

	mockCustomProcessRepo := new(mocks.CustomProcessRepository)
	mockCommodityRepo := new(mocks.CommodityRepository)
	mockCityRepo := new(mocks.CityRepository)
	mockRoleCustomProcess := new(mocks.CustomProcessRoleRepository)

	uc := &customProcessCtx{
		customProcessRepo:     mockCustomProcessRepo,
		commodityRepo:         mockCommodityRepo,
		cityRepo:              mockCityRepo,
		customProcessRoleRepo: mockRoleCustomProcess,
	}

	stt := model.Stt{
		SttNo:                "11LP1111111111",
		SttCommodityCode:     "DOC",
		SttDestinationCityID: "BDO1000",
		SttOriginCityID:      "CGK1000",
	}

	paramsCustomProcess := &model.CustomProcessViewDetailParams{
		CustomProcessID:        1,
		CustomProcessPartnerID: 1,
		Status:                 []string{model.HAL},
	}
	paramsCustomProcessRole := &model.CustomProcessRoleViewParams{
		AccountType:     "partner",
		AccountRoleType: "console",
	}

	mockResultCustomProcessDet := []model.CustomProcessDetailResult{
		{
			Stt:           stt,
			CustomProcess: model.CustomProcess{CustomProcessID: 0, CustomProcessPartnerID: 1},
		},
		{
			Stt: stt,
		},
	}
	mockResultCommodity := &model.Commodity{
		Data: model.CommodityBase{
			CommodityName:   "dokumen",
			CommodityNameEn: "dokumen",
		},
	}
	mockResultCityDest := &model.City{Name: "bandung"}
	mockResultCityOrigin := &model.City{Name: "jakarta"}

	ctxBg, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()
	testTable := []struct {
		name       string
		req        args
		resp       *customProcess.ViewDetailCustomProcessResponse
		errResp    bool
		errDetails error
		before     func()
	}{
		{
			name: "TestViewDetailCustomProcessSuccess",
			req: args{
				ctx: ctxBg,
				params: &customProcess.ViewDetailCustomProcessRequest{
					CustomProcessID: 1,
					PartnerID:       1,
					AccountType:     "partner",
					AccountRoleType: "console",
				},
			},
			resp: &customProcess.ViewDetailCustomProcessResponse{
				Stt: []*general.SttResponse{
					{
						SttNo:                stt.SttNo,
						SttCommodityCode:     stt.SttCommodityCode,
						SttDestinationCityID: stt.SttDestinationCityID,
						SttOriginCityID:      stt.SttOriginCityID,
						SttCommodityName:     "dokumen",
						Piece:                []general.SttPieceResponse{{}, {}},
					},
				},
			},
			errResp:    false,
			errDetails: nil,
			before: func() {
				// rcp
				mockRoleCustomProcess.On("Select", mock.Anything, paramsCustomProcessRole).Return([]model.RoleCustomProcess{model.RoleCustomProcess{RoleCustomProcessStatus: model.HAL}}, nil)

				// Custom process
				mockCustomProcessRepo.On("SelectDetail", mock.Anything, paramsCustomProcess).Return(mockResultCustomProcessDet, nil).Once()

				// Commodity
				mockCommodityRepo.On("GetCommodityByCode", mock.Anything, stt.SttCommodityCode, mock.Anything).Return(mockResultCommodity, nil).Once()

				// STT Destination
				mockCityRepo.On("Get", mock.Anything, stt.SttDestinationCityID, mock.Anything).Return(mockResultCityDest, nil).Once()

				// STT Origin
				mockCityRepo.On("Get", mock.Anything, stt.SttOriginCityID, mock.Anything).Return(mockResultCityOrigin, nil).Once()
			},
		},
		{
			name: "TestViewDetailCustomProcessDetailNotFound",
			req: args{
				ctx: ctxBg,
				params: &customProcess.ViewDetailCustomProcessRequest{
					CustomProcessID: 1,
					PartnerID:       1,
					AccountType:     "partner",
					AccountRoleType: "console",
				},
			},
			resp:    nil,
			errResp: true,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Customer Process Detail Not Found",
				"id": "Customer Process Detail Tidak Ditemukan",
			}),
			before: func() {
				// Custom process
				mockCustomProcessRepo.On("SelectDetail", mock.Anything, paramsCustomProcess).Return([]model.CustomProcessDetailResult{}, nil).Once()
			},
		},
		{
			name: "TestViewDetailCustomProcessAccountNotAllowed",
			req: args{
				ctx: ctxBg,
				params: &customProcess.ViewDetailCustomProcessRequest{
					CustomProcessID: 1,
					PartnerID:       2,
				},
			},
			resp:    nil,
			errResp: false,
			errDetails: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Account not allowed view custom process",
				"id": "Akun tidak diizinkan melihat data custom process ini",
			}),
			before: func() {
				// Custom process
				mockCustomProcessRepo.On("SelectDetail", mock.Anything, paramsCustomProcess).Return([]model.CustomProcessDetailResult{}, nil).Once()
			},
		},
	}

	for _, tt := range testTable {
		t.Run(tt.name, func(t *testing.T) {

			tt.before()
			got, err := uc.ViewDetailCustomProcess(tt.req.ctx, tt.req.params)
			if (err != nil) != tt.errResp {
				t.Errorf("customprocessCtx.ViewCustomProcess() error = %v, wantErr %v", err, tt.errDetails)
				return
			}
			if !reflect.DeepEqual(got, tt.resp) {
				t.Errorf("customProcessCtx.ViewCustomProcess() = %v, want %v", got, tt.resp)
			}
		})
	}
}

func Test_customProcessCtx_ViewDetailStt(t *testing.T) {
	t.SkipNow()
	type args struct {
		ctx    context.Context
		params *customProcess.DetailCustomProcessRequest
	}

	ctx, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()
	errorDictionary := shared.NewDictionaryError()
	accountReferenceModelConcole := map[string]interface{}{
		"id": 1.0,
		"partner_location": map[string]interface{}{
			"country": map[string]interface{}{
				"code": model.CountryID,
			},
		},
	}
	accountDetailMock := &model.AccountDetail{
		Data: model.AccountDetailBase{
			AccountID:         1,
			AccountTypeDetail: accountReferenceModelConcole,
		},
	}

	tests := []struct {
		name       string
		args       args
		want       *customProcess.DetailSttCustomProcessResponse
		wantErr    bool
		errResp    error
		beforeFunc func() *customProcessCtx
	}{
		{
			name: "error_get_account_profile",
			args: args{
				ctx: ctx,
				params: &customProcess.DetailCustomProcessRequest{
					PartnerType:         model.CONSOLE,
					SttNo:               "88LP123",
					CustomProcessStatus: model.RTSHQ,
				},
			},
			wantErr: true,
			errResp: errorDictionary.ErrFailedToRetrieveData("Account profile"),
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockDictionaryError := new(shmock.DictionaryError)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					accountRepo:           mockAccountRepo,
					DictionaryError:       mockDictionaryError,
				}

				mockAccountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(&model.AccountDetail{
					Data: model.AccountDetailBase{
						AccountID: 1,
					},
				}, errors.New("error")).Once()
				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{RoleCustomProcessStatus: model.RTSHQ},
				}, nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: "88LP123",
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "88LP123",
							SttLastStatusID: model.STIDEST,
							SttShipmentID:   "ARA1234",
						},
					},
				}, nil).Once()

				mockDictionaryError.On("ErrFailedToRetrieveData", "Account profile").Return(errorDictionary.ErrFailedToRetrieveData("Account profile")).Once()

				return c
			},
		},
		{
			name: "not_allow_to_RTSHQ_shipment_ARA",
			args: args{
				ctx: ctx,
				params: &customProcess.DetailCustomProcessRequest{
					PartnerType:         model.CONSOLE,
					SttNo:               "88LP123",
					CustomProcessStatus: model.RTSHQ,
				},
			},
			want: &customProcess.DetailSttCustomProcessResponse{
				ErrorMessage: errorDictionary.ErrSTTCannotBeProccessed(model.CountryMY),
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockDictionaryError := new(shmock.DictionaryError)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					accountRepo:           mockAccountRepo,
					DictionaryError:       mockDictionaryError,
				}

				mockAccountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(&model.AccountDetail{
					Data: model.AccountDetailBase{
						AccountType: model.PARTNER,
						AccountID:   1,
						AccountTypeDetail: map[string]interface{}{
							"id": 1.0,
							"partner_location": map[string]interface{}{
								"country": map[string]interface{}{
									"code": model.CountryMY,
								},
							},
						},
					},
				}, nil).Once()
				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{RoleCustomProcessStatus: model.RTSHQ},
				}, nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: "88LP123",
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "88LP123",
							SttLastStatusID: model.STIDEST,
							SttShipmentID:   "ARA1234",
						},
					},
				}, nil).Once()

				mockDictionaryError.On("ErrSTTCannotBeProccessed", model.CountryMY).Return(errorDictionary.ErrSTTCannotBeProccessed(model.CountryMY))

				return c
			},
		},
		{
			name: "error_get_stt_reverse_journey",
			args: args{
				ctx: ctx,
				params: &customProcess.DetailCustomProcessRequest{
					PartnerType:         model.CONSOLE,
					SttNo:               "88LP123",
					CustomProcessStatus: model.RTSHQ,
				},
			},
			want:    nil,
			wantErr: true,
			errResp: shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "An error occurred while getting STT reverse journey",
				"id": "Terjadi kesalahan pada saat mengambil data STT reverse journey",
			}),
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockDictionaryError := new(shmock.DictionaryError)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					accountRepo:           mockAccountRepo,
					DictionaryError:       mockDictionaryError,
				}

				mockAccountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{RoleCustomProcessStatus: model.RTSHQ},
				}, nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: "88LP123",
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "88LP123",
							SttLastStatusID: model.STIDEST,
							SttShipmentID:   "ACA1234",
							SttMeta:         `{"detail_stt_reverse_journey":{"reverse_stt_no":"78LP123"}}`,
						},
					},
				}, nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: "78LP123",
				}).Return([]model.SttDetailResult{}, errors.New("error")).Once()

				mockDictionaryError.On("ErrSTTCannotBeProccessed", model.CountryID).Return(errorDictionary.ErrSTTCannotBeProccessed(model.CountryID))

				return c
			},
		},
		{
			name: "not_allow_to_RTSHQ_reverse_shipment_ARB",
			args: args{
				ctx: ctx,
				params: &customProcess.DetailCustomProcessRequest{
					PartnerType:         model.CONSOLE,
					SttNo:               "88LP123",
					CustomProcessStatus: model.RTSHQ,
				},
			},
			want: &customProcess.DetailSttCustomProcessResponse{
				ErrorMessage: "STT tidak dapat diproses",
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockDictionaryError := new(shmock.DictionaryError)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					accountRepo:           mockAccountRepo,
					DictionaryError:       mockDictionaryError,
				}

				mockAccountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{RoleCustomProcessStatus: model.RTSHQ},
				}, nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: "88LP123",
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "88LP123",
							SttLastStatusID: model.STIDEST,
							SttShipmentID:   "ACA1234",
							SttMeta:         `{"detail_stt_reverse_journey":{"reverse_stt_no":"78LP123"}}`,
						},
					},
				}, nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: "78LP123",
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "78LP123",
							SttLastStatusID: model.STIDEST,
							SttShipmentID:   "ARB321",
						},
					},
				}, nil).Once()
				mockDictionaryError.On("ErrSTTCannotBeProccessed", model.CountryID).Return(errorDictionary.ErrSTTCannotBeProccessed(model.CountryID))

				return c
			},
		},
		{
			name: "not_allow_to_RTSHQ_stt_cod_dfod",
			args: args{
				ctx: ctx,
				params: &customProcess.DetailCustomProcessRequest{
					PartnerType:         model.CONSOLE,
					SttNo:               "88LP123",
					CustomProcessStatus: model.RTSHQ,
				},
			},
			want: &customProcess.DetailSttCustomProcessResponse{
				ErrorMessage: "STT tidak dapat diproses",
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockDictionaryError := new(shmock.DictionaryError)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					accountRepo:           mockAccountRepo,
					DictionaryError:       mockDictionaryError,
				}

				mockAccountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{RoleCustomProcessStatus: model.RTSHQ},
				}, nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: "88LP123",
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "88LP123",
							SttLastStatusID: model.STIDEST,
							SttShipmentID:   "",
							SttMeta:         `{"detail_stt_reverse_journey":{"reverse_stt_no":""}}`,
							SttIsCOD:        true,
							SttIsDFOD:       true,
						},
					},
				}, nil).Once()
				mockDictionaryError.On("ErrSTTCannotBeProccessed", model.CountryID).Return(errorDictionary.ErrSTTCannotBeProccessed(model.CountryID))

				return c
			},
		},
		{
			name: "not_allow_to_RTSHQ_reverse_cod_dfod_last_status_RTS_REROUTE_MISBOOKING_MISROUTE",
			args: args{
				ctx: ctx,
				params: &customProcess.DetailCustomProcessRequest{
					PartnerType:         model.CONSOLE,
					SttNo:               "88LP123",
					CustomProcessStatus: model.RTSHQ,
				},
			},
			want: &customProcess.DetailSttCustomProcessResponse{
				ErrorMessage: "STT tidak dapat diproses",
			},
			wantErr: false,
			errResp: nil,
			beforeFunc: func() *customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockAccountRepo := new(mocks.AccountRepository)
				mockDictionaryError := new(shmock.DictionaryError)

				c := &customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					accountRepo:           mockAccountRepo,
					DictionaryError:       mockDictionaryError,
				}

				mockAccountRepo.On("GetProfile", mock.Anything, mock.Anything).Return(accountDetailMock, nil).Once()

				mockCustomProcessRoleRepo.On("Select", mock.Anything, &model.CustomProcessRoleViewParams{}).Return([]model.RoleCustomProcess{
					{RoleCustomProcessStatus: model.RTSHQ},
				}, nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: "88LP123",
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "88LP123",
							SttLastStatusID: model.STIDEST,
							SttShipmentID:   "",
							SttMeta:         `{"detail_stt_reverse_journey":{"reverse_stt_no":"78LP123"}}`,
						},
					},
				}, nil).Once()

				mockSttPieceRepo.On("SelectDetail", mock.Anything, &model.SttPiecesViewParam{
					SttNo: "78LP123",
				}).Return([]model.SttDetailResult{
					{
						Stt: model.Stt{
							SttNo:           "78LP123",
							SttLastStatusID: model.RTS,
							SttShipmentID:   "",
							SttIsCOD:        true,
							SttIsDFOD:       true,
						},
					},
				}, nil).Once()
				mockDictionaryError.On("ErrSTTCannotBeProccessed", model.CountryID).Return(errorDictionary.ErrSTTCannotBeProccessed(model.CountryID))

				return c
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.beforeFunc()

			got, err := c.ViewDetailStt(tt.args.ctx, tt.args.params)
			if (err != nil) != tt.wantErr || !assert.Equal(t, tt.errResp, err) {
				t.Errorf("Test_customProcessCtx_ViewDetailStt(); err = %v, wantErr = %v\n", err, tt.errResp)
				return
			}
			if !assert.Equal(t, tt.want, got) {
				t.Errorf("Test_customProcessCtx_ViewDetailStt(); got = %v, want = %v\n", got, tt.want)
			}
		})
	}
}

func Test_customProcessCtx_UpdateForceUpdate(t *testing.T) {
	now := time.Now()

	type fields struct {
		customProcessRepo     repository.CustomProcessRepository
		partnerRepo           repository.PartnerRepository
		sttPieceRepo          repository.SttPiecesRepository
		commodityRepo         repository.CommodityRepository
		districtRepo          repository.DistrictRepository
		cityRepo              repository.CityRepository
		gatewaySttStatusUc    GatewaySttStatus
		gatewaySttUc          GatewayStt
		middlewareRepo        repository.MiddlewareCLient
		sttActivityUc         SttActivity
		sttPieceHistoryRepo   repository.SttPieceHistoryRepository
		deliveryRepo          repository.DeliveryRepository
		customProcessRoleRepo repository.CustomProcessRoleRepository
		messageGatewayUc      MessageGateway
		sttPaymentUc          SttPayment
		accountRepo           repository.AccountRepository
		sttUc                 Stt
		cfg                   *config.Config
		clientRepo            repository.ClientRepository
		partnerLog            repository.PartnerLogRepository
		reasonRepo            repository.ReasonRepository
		sttRepo               repository.SttRepository
		productRepo           repository.ProductTypeRepository
		repoCommodity         repository.CommodityRepository
		bagRepo               repository.BagRepository
		bagVendorRepo         repository.BagVendorRepository
		shipmentRepo          repository.ShipmentRepository
		sttOptionalRateRepo   repository.SttOptionalRateRepository
		DictionaryError       shared.DictionaryError
		flagManagementRepo    repository.FlagManagementRepository
	}
	type args struct {
		ctx    context.Context
		params *custom_process.ForceUpdateRequest
	}
	ctxBg, cancel := lputils.CreateContext(60) // 60 seconds timeout
	defer cancel()
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
		before  func() customProcessCtx
	}{
		{
			name: "Test UpdateForceUpdate Success",
			args: args{
				ctx: ctxBg,
				params: &custom_process.ForceUpdateRequest{
					SttNo:       "11LP",
					Status:      model.SCRAPCD,
					PartnerCode: "PER",
					AccountID:   1,
					AccountType: model.CONSOLE,
					AccountName: "budi",
				},
			},
			wantErr: false,
			before: func() customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttRepo := new(mocks.SttRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockSttActivityUc := new(ucmock.SttActivity)
				mockTimeRepo := new(mocks.TimeRepository)
				mockGatewayUc := new(ucmock.GatewaySttStatus)
				mockPartnerLog := new(mocks.PartnerLogRepository)

				c := customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttRepo:               mockSttRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					sttActivityUc:         mockSttActivityUc,
					timeRepo:              mockTimeRepo,
					gatewaySttStatusUc:    mockGatewayUc,
					partnerLog:            mockPartnerLog,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPartnerLog.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				mockPartnerRepo.On("GetByCode", mock.Anything, "PER", mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Name: "PARTNER",
						Type: model.CONSOLE,
						PartnerLocation: &model.PartnerLocationBase{
							City: &model.City{
								Name: "JAKARTA",
							},
							CityCode: "CGK",
						},
					},
				}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{
						SttNo: "11LP",
					},
				}).Return(&model.Stt{
					SttID:               1,
					SttNo:               "11LP",
					SttLastStatusID:     model.MISSING,
					SttNoRefExternal:    "TKP01-BAG-01",
					SttProductType:      model.ONEPACK,
					SttTotalPiece:       1,
					SttGrossWeight:      1,
					SttVolumeWeight:     1,
					SttChargeableWeight: 1,
					SttBookedForType:    "1",
				}, nil).Once()

				mockSttPieceRepo.On("Select", mock.Anything, &model.SttPiecesViewParam{
					SttID: 1,
				}).Return([]model.SttPiece{
					{
						SttPieceID: 1,
					},
				}, nil).Once()

				tempRemarksPieceHistory := model.RemarkPieceHistory{
					IsForceCustomStatus: true,
				}
				mockSttPieceHistoryRepo.On("Create", mock.Anything, &model.SttPieceHistory{
					SttPieceID:         1,
					HistoryStatus:      model.SCRAPCD,
					HistoryLocation:    "CGK",
					HistoryActorID:     1,
					HistoryActorName:   "PARTNER",
					HistoryActorRole:   model.CONSOLE,
					HistoryCreatedBy:   1,
					HistoryCreatedAt:   now,
					HistoryCreatedName: "budi",
					HistoryRemark:      tempRemarksPieceHistory.ToString(),
				}, 1).Return(nil).Once()

				listSttUpdateTime := []stt_activity.SttActivityRequestDetail{}
				listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
					SttNo:         "11LP",
					SttStatus:     model.SCRAPCD,
					SttStatusTime: now,
				})
				mockSttActivityUc.On("UpdateSttTime", mock.Anything, &stt_activity.SttActivityRequest{
					ListSttData: listSttUpdateTime,
				}).Return(nil).Once()

				mockGatewayUc.On("StatusSubmit", mock.Anything, &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo:      "11LP",
						Datetime:   now.UTC(),
						StatusCode: model.SCRAPCD,
						Location:   "CGK",
						Remarks:    fmt.Sprintf(`Paket diupdate oleh %s`, "PARTNER"),
						City:       "JAKARTA",
						UpdatedBy:  "PARTNER",
						UpdatedOn:  now.UTC(),
						DriverSource: model.DriverSource{
							Id:       0,
							Source:   "",
							Name:     "",
							Phone:    "",
							AssignBy: "",
						},
					},
					ServiceType:      model.PACKAGESERVICE,
					Product:          model.ONEPACK,
					Pieces:           1,
					GrossWeight:      1,
					VolumeWeight:     1,
					ChargeableWeight: 1,
					BookedForType:    "1",
				}).Return(nil).Once()

				return c
			},
		},
		{
			name: "Test UpdateForceUpdate Error Invalid Status",
			args: args{
				ctx: ctxBg,
				params: &custom_process.ForceUpdateRequest{
					SttNo:       "11LP",
					Status:      model.BKD,
					PartnerCode: "PER",
					AccountID:   1,
					AccountType: model.CONSOLE,
					AccountName: "budi",
				},
			},
			wantErr: true,
			before: func() customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttRepo := new(mocks.SttRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockSttActivityUc := new(ucmock.SttActivity)
				mockTimeRepo := new(mocks.TimeRepository)
				mockGatewayUc := new(ucmock.GatewaySttStatus)
				mockPartnerLog := new(mocks.PartnerLogRepository)

				c := customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttRepo:               mockSttRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					sttActivityUc:         mockSttActivityUc,
					timeRepo:              mockTimeRepo,
					gatewaySttStatusUc:    mockGatewayUc,
					partnerLog:            mockPartnerLog,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPartnerLog.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				return c
			},
		},
		{
			name: "Test UpdateForceUpdate Error DB GetPartner",
			args: args{
				ctx: ctxBg,
				params: &custom_process.ForceUpdateRequest{
					SttNo:       "11LP",
					Status:      model.SCRAPCD,
					PartnerCode: "PER",
					AccountID:   1,
					AccountType: model.CONSOLE,
					AccountName: "budi",
				},
			},
			wantErr: true,
			before: func() customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttRepo := new(mocks.SttRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockSttActivityUc := new(ucmock.SttActivity)
				mockTimeRepo := new(mocks.TimeRepository)
				mockGatewayUc := new(ucmock.GatewaySttStatus)
				mockDictionaryErr := new(shmock.DictionaryError)
				mockPartnerLog := new(mocks.PartnerLogRepository)

				c := customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttRepo:               mockSttRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					sttActivityUc:         mockSttActivityUc,
					timeRepo:              mockTimeRepo,
					gatewaySttStatusUc:    mockGatewayUc,
					DictionaryError:       mockDictionaryErr,
					partnerLog:            mockPartnerLog,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPartnerLog.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				mockErr := new(shared.MultiStringBadRequestError)
				mockDictionaryErr.On("ErrRequestHttp").Return(mockErr).Once()

				mockPartnerRepo.On("GetByCode", mock.Anything, "PER", mock.Anything).Return(nil, errors.New("")).Once()

				return c
			},
		},
		{
			name: "Test UpdateForceUpdate Error GetPartner Not Found",
			args: args{
				ctx: ctxBg,
				params: &custom_process.ForceUpdateRequest{
					SttNo:       "11LP",
					Status:      model.SCRAPCD,
					PartnerCode: "PER",
					AccountID:   1,
					AccountType: model.CONSOLE,
					AccountName: "budi",
				},
			},
			wantErr: true,
			before: func() customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttRepo := new(mocks.SttRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockSttActivityUc := new(ucmock.SttActivity)
				mockTimeRepo := new(mocks.TimeRepository)
				mockGatewayUc := new(ucmock.GatewaySttStatus)
				mockPartnerLog := new(mocks.PartnerLogRepository)

				c := customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttRepo:               mockSttRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					sttActivityUc:         mockSttActivityUc,
					timeRepo:              mockTimeRepo,
					gatewaySttStatusUc:    mockGatewayUc,
					partnerLog:            mockPartnerLog,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPartnerLog.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				mockPartnerRepo.On("GetByCode", mock.Anything, "PER", mock.Anything).Return(nil, nil).Once()

				return c
			},
		},
		{
			name: "Test UpdateForceUpdate Error Partner Type Invalid",
			args: args{
				ctx: ctxBg,
				params: &custom_process.ForceUpdateRequest{
					SttNo:       "11LP",
					Status:      model.SCRAPCD,
					PartnerCode: "PER",
					AccountID:   1,
					AccountType: model.CONSOLE,
					AccountName: "budi",
				},
			},
			wantErr: true,
			before: func() customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttRepo := new(mocks.SttRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockSttActivityUc := new(ucmock.SttActivity)
				mockTimeRepo := new(mocks.TimeRepository)
				mockGatewayUc := new(ucmock.GatewaySttStatus)
				mockPartnerLog := new(mocks.PartnerLogRepository)

				c := customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttRepo:               mockSttRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					sttActivityUc:         mockSttActivityUc,
					timeRepo:              mockTimeRepo,
					gatewaySttStatusUc:    mockGatewayUc,
					partnerLog:            mockPartnerLog,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPartnerLog.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				mockPartnerRepo.On("GetByCode", mock.Anything, "PER", mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Name: "PARTNER",
						Type: model.POS,
						PartnerLocation: &model.PartnerLocationBase{
							City: &model.City{
								Name: "JAKARTA",
							},
							CityCode: "CGK",
						},
					},
				}, nil).Once()

				return c
			},
		},
		{
			name: "Test UpdateForceUpdate Error DB Get STT",
			args: args{
				ctx: ctxBg,
				params: &custom_process.ForceUpdateRequest{
					SttNo:       "11LP",
					Status:      model.SCRAPCD,
					PartnerCode: "PER",
					AccountID:   1,
					AccountType: model.CONSOLE,
					AccountName: "budi",
				},
			},
			wantErr: true,
			before: func() customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttRepo := new(mocks.SttRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockSttActivityUc := new(ucmock.SttActivity)
				mockTimeRepo := new(mocks.TimeRepository)
				mockGatewayUc := new(ucmock.GatewaySttStatus)
				mockDictionaryErr := new(shmock.DictionaryError)
				mockPartnerLog := new(mocks.PartnerLogRepository)

				c := customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttRepo:               mockSttRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					sttActivityUc:         mockSttActivityUc,
					timeRepo:              mockTimeRepo,
					gatewaySttStatusUc:    mockGatewayUc,
					DictionaryError:       mockDictionaryErr,
					partnerLog:            mockPartnerLog,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPartnerLog.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				mockPartnerRepo.On("GetByCode", mock.Anything, "PER", mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Name: "PARTNER",
						Type: model.CONSOLE,
						PartnerLocation: &model.PartnerLocationBase{
							City: &model.City{
								Name: "JAKARTA",
							},
							CityCode: "CGK",
						},
					},
				}, nil).Once()

				mockErr := new(shared.MultiStringBadRequestError)
				mockDictionaryErr.On("ErrDB").Return(mockErr).Once()
				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{
						SttNo: "11LP",
					},
				}).Return(nil, errors.New("")).Once()

				return c
			},
		},
		{
			name: "Test UpdateForceUpdate Error STT Not Found",
			args: args{
				ctx: ctxBg,
				params: &custom_process.ForceUpdateRequest{
					SttNo:       "11LP",
					Status:      model.SCRAPCD,
					PartnerCode: "PER",
					AccountID:   1,
					AccountType: model.CONSOLE,
					AccountName: "budi",
				},
			},
			wantErr: true,
			before: func() customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttRepo := new(mocks.SttRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockSttActivityUc := new(ucmock.SttActivity)
				mockTimeRepo := new(mocks.TimeRepository)
				mockGatewayUc := new(ucmock.GatewaySttStatus)
				mockPartnerLog := new(mocks.PartnerLogRepository)

				c := customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttRepo:               mockSttRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					sttActivityUc:         mockSttActivityUc,
					timeRepo:              mockTimeRepo,
					gatewaySttStatusUc:    mockGatewayUc,
					partnerLog:            mockPartnerLog,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPartnerLog.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				mockPartnerRepo.On("GetByCode", mock.Anything, "PER", mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Name: "PARTNER",
						Type: model.CONSOLE,
						PartnerLocation: &model.PartnerLocationBase{
							City: &model.City{
								Name: "JAKARTA",
							},
							CityCode: "CGK",
						},
					},
				}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{
						SttNo: "11LP",
					},
				}).Return(nil, nil).Once()

				return c
			},
		},
		{
			name: "Test UpdateForceUpdate Error Stt Not from LILO",
			args: args{
				ctx: ctxBg,
				params: &custom_process.ForceUpdateRequest{
					SttNo:       "11LP",
					Status:      model.SCRAPCD,
					PartnerCode: "PER",
					AccountID:   1,
					AccountType: model.CONSOLE,
					AccountName: "budi",
				},
			},
			wantErr: true,
			before: func() customProcessCtx {
				mockCustomProcessRoleRepo := new(mocks.CustomProcessRoleRepository)
				mockSttPieceRepo := new(mocks.SttPiecesRepository)
				mockPartnerRepo := new(mocks.PartnerRepository)
				mockSttRepo := new(mocks.SttRepository)
				mockSttPieceHistoryRepo := new(mocks.SttPieceHistoryRepository)
				mockSttActivityUc := new(ucmock.SttActivity)
				mockTimeRepo := new(mocks.TimeRepository)
				mockGatewayUc := new(ucmock.GatewaySttStatus)
				mockPartnerLog := new(mocks.PartnerLogRepository)

				c := customProcessCtx{
					customProcessRoleRepo: mockCustomProcessRoleRepo,
					sttPieceRepo:          mockSttPieceRepo,
					partnerRepo:           mockPartnerRepo,
					sttRepo:               mockSttRepo,
					sttPieceHistoryRepo:   mockSttPieceHistoryRepo,
					sttActivityUc:         mockSttActivityUc,
					timeRepo:              mockTimeRepo,
					gatewaySttStatusUc:    mockGatewayUc,
					partnerLog:            mockPartnerLog,
				}

				mockTimeRepo.On("Now", mock.Anything).Return(now).Once()

				mockPartnerLog.On("Insert", mock.Anything, mock.Anything).Return(nil).Once()

				mockPartnerRepo.On("GetByCode", mock.Anything, "PER", mock.Anything).Return(&model.Partner{
					Data: model.PartnerBase{
						ID:   1,
						Name: "PARTNER",
						Type: model.CONSOLE,
						PartnerLocation: &model.PartnerLocationBase{
							City: &model.City{
								Name: "JAKARTA",
							},
							CityCode: "CGK",
						},
					},
				}, nil).Once()

				mockSttRepo.On("Get", mock.Anything, &model.SttViewDetailParams{
					Stt: model.Stt{
						SttNo: "11LP",
					},
				}).Return(&model.Stt{
					SttID:               1,
					SttNo:               "11LP",
					SttLastStatusID:     model.MISSING,
					SttNoRefExternal:    "",
					SttProductType:      model.ONEPACK,
					SttTotalPiece:       1,
					SttGrossWeight:      1,
					SttVolumeWeight:     1,
					SttChargeableWeight: 1,
					SttBookedForType:    "1",
				}, nil).Once()

				return c
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := tt.before()
			if err := c.UpdateForceUpdate(tt.args.ctx, tt.args.params); (err != nil) != tt.wantErr {
				t.Errorf("customProcessCtx.UpdateForceUpdate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
