package usecase

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/abiewardani/dbr/v2"
	"github.com/google/uuid"
	"golang.org/x/sync/errgroup"

	"github.com/Lionparcel/go-lptool/lputils"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/gateway_stt"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *sttCtx) CreateSTT(ctx context.Context, params *stt.CreateSttRequest) (*stt.CreateSttResponse, error) {
	opName := "UsecaseStt-CreateSTT"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res := stt.CreateSttResponse{}
	isDisablePromo := true
	var errLog error

	paramsCreateStt := make([]model.SttCreate, 0)
	var (
		referenceRole  string
		referenceCode  string
		BookedForActor *model.Actor
	)
	params.ReplaceInputCharNonASCII()
	defer func() {
		c.deleteCacheNoRefExternals(selfCtx, params)
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": errLog})
	}()

	errInvalid := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "Invalid Account Type format",
		"id": "Format Account Type tidak valid",
	})

	errHTTPRequest := func(source string) error {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while http request",
			"id": "Terjadi kesalahan pada saat request http",
		})
	}

	if err := c.validateTariffAccountCreateSTT(selfCtx, params); err != nil {
		errLog = err
		return nil, err
	}

	now, _ := shared.ParseUTC7(shared.FormatDateTime, c.timeRepo.Now(time.Now()).Format(shared.FormatDateTime))

	reqCheckTariff := new(stt.CalculateTariffParams)
	sttCreate := model.SttCreate{}

	partnerPosIsPickup := false
	totalTariffReturn := 0.0
	percentageCodFee := 0.0
	minimumCodFee := 0.0
	originCommissionRate := 0.0
	destinationCommissionRate := 0.0
	clientPaymentMethod := ""
	clientCodConfigAmount := ""
	clientCodShipmentDiscount := 0.0
	rateVatShipment := 0.0
	rateVatCod := 0.0
	codIsInsurance := false
	codAmountConfigType := ``
	codHandling := ""
	clientIsDOPaymentTypeFree := false
	originMinCommission := 0
	destinationMinCommission := 0
	currency := ""
	mapShipmentAlgo := make(map[string]*model.Shipment)
	mapData := make(map[string]*stt.Stt)

	for i, data := range params.Stt {
		isAlgo := params.Source == model.ALGO && data.SttShipmentID != ``
		if isAlgo && c.flagManagementRepo.ShipmentCAGrossWeight(selfCtx, model.ShipmentAccessWeigth{
			ShipmentID:          shared.GetPrefixShipmentID(data.SttShipmentID),
			DestinationCityCode: data.SttDestinationCityID,
			OriginCityCode:      data.SttOriginCityID,
		}) {
			shipmentAlgo, err := c.shipmentRepo.Get(selfCtx, &model.ShipmentViewParams{
				ShipmentAlgoID: data.SttShipmentID,
			})

			emptyShipment := err != nil || shipmentAlgo == nil
			emptyShipmentPacket := shipmentAlgo != nil && len(shipmentAlgo.ShipmentPackets) == 0
			if emptyShipment || emptyShipmentPacket {
				errLog = err
				return nil, shared.NewDictionaryError().ErrDBNotFound("Shipment")
			}

			grossWeightReq := data.GetSummaryGrossWeight()
			if grossWeightReq != shipmentAlgo.ShipmentPackets[0].GetGrossWeightTotal() && grossWeightReq < 10 {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"id": "Berat barang kurang dari 10 kg untuk shipment Customer Apps rute JABODETABEKCIK, tidak boleh diubah.",
					"en": "Gross weight less than 10 kg for shipment Customer Apps route JABODETABEKCIK, are not able to be changed.",
				})
			}
		}

		shipmentSubscriptionPriority := false
		if isAlgo {
			shipmentAlgo, err := c.shipmentRepo.Get(selfCtx, &model.ShipmentViewParams{
				ShipmentAlgoID: data.SttShipmentID,
			})

			emptyShipment := err != nil || shipmentAlgo == nil
			emptyShipmentPacket := shipmentAlgo != nil && len(shipmentAlgo.ShipmentPackets) == 0
			if emptyShipment || emptyShipmentPacket {
				errLog = err
				return nil, shared.NewDictionaryError().ErrDBNotFound("Shipment")
			}

			mapShipmentAlgo[data.SttShipmentID] = shipmentAlgo
			mapData[data.SttShipmentID] = &data
			shipmentSubscriptionPriority = c.getShipmentPrioritySubscription(shipmentAlgo)
		}

		if params.Source == model.ALGO && data.SttRecipientAddressType == "" {
			data.SttRecipientAddressType = model.AddressTypeHome
		}

		err := data.Validate(i, params.Source)
		if err != nil {
			errLog = err
			return nil, err
		}

		err = data.ValidateInterpack(i, params.Source)
		if err != nil {
			errLog = err
			return nil, err
		}

		isMixpack := false
		if data.SttProductType == model.MIXPACK {
			isMixpack = true
			data.IsMixpack = true
		}

		if params.Source == model.ALGO && len(data.SttShipmentID) < 4 {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "ShipmentID is required to be filled correctly",
				"id": "ShipmentID harus di isi dengan benar",
			})
		}

		// source algo & account partner validation
		err = data.ValidateJumbopack(stt.ValidateJumbopackParams{
			Source:       params.Source,
			AccountType:  params.AccountType,
			IsPartnerPcu: true,
			SenderName:   data.SttSenderName,
			SenderPhone:  data.SttSenderPhone,
		})
		if err != nil {
			errLog = err
			return nil, err
		}

		/**
		 * get client or partner
		 */
		actorIsCODDelivery := false
		actorExternalCode := ``     // this value is external code from partner or client
		bookedForExternalCode := `` // this value is external code from partner or client that got booked

		isPosMy := false
		switch params.AccountType {
		case model.PARTNER:

			if params.Source == model.MANUAL && !c.cfg.EnableCodRetail() {
				data.SttIsCOD = false
			}

			if params.AccountRefType != model.PARTNERPOS {
				return nil, errInvalid
			}

			partnerBooking, err := c.getValidatePartnerCreateSTT(selfCtx, params, data)
			if err != nil {
				errLog = err
				return nil, err
			}
			actorExternalCode = partnerBooking.Data.PartnerExternalCode
			actorIsCODDelivery = partnerBooking.Data.PartnerIsCODDelivery
			partnerPosIsPickup = partnerBooking.Data.PartnerPosIsPickup

			if partnerBooking.Data.PartnerLocation.Country.Code == model.CountryMY {
				isPosMy = true
			}

			// isPartnerPCU validation
			err = data.ValidateJumbopack(stt.ValidateJumbopackParams{
				Source:       params.Source,
				AccountType:  params.AccountType,
				IsPartnerPcu: partnerBooking.Data.PartnerIsPCU,
				SenderName:   data.SttSenderName,
				SenderPhone:  data.SttSenderPhone,
			})
			if err != nil {
				errLog = err
				return nil, err
			}

			data.SttOriginDistrictID = partnerBooking.Data.PartnerLocation.DistrictCode

			referenceRole = params.AccountRefType
			referenceCode = partnerBooking.Data.Code
			BookedForActor = &model.Actor{
				ID:    partnerBooking.Data.ID,
				Name:  partnerBooking.Data.Name,
				Code:  partnerBooking.Data.Code,
				IsCod: partnerBooking.Data.PartnerIsCodBooking,
				Type:  model.POS,
				ActorPosDetail: model.ActorPos{
					PosBranchCommission: partnerBooking.Data.PartnerPosBranchCommission,
					PosParentID:         partnerBooking.Data.PartnerPosParentID,
				},
			}

			if errValidatePhone := c.validateSenderPhone(selfCtx, params.Stt[0], params.Source); errValidatePhone != nil {
				return nil, errValidatePhone
			}

			if err = validatePartnerData(partnerBooking, data, params); err != nil {
				return nil, err
			}

			if data.SttIsCOD && params.Source == model.MANUAL {
				embargoConfig, err := c.embargoConfigRepo.GetById(selfCtx, &model.CredentialRestAPI{
					Token: params.Token,
				}, 1)

				if err == nil && embargoConfig != nil {
					if embargoConfig.Data.Status == model.ACTIVE && embargoConfig.Data.MaxMinusBalance != 0 {

						// logic validasi origin
						if strings.Contains(embargoConfig.Data.EmbargoCodType, model.Origin) {

							saldoCity, err := c.cityBalanceRepo.Negatif(selfCtx, &model.CredentialRestAPI{
								Token: params.Token,
							}, data.SttOriginCityID)

							if err == nil && embargoConfig != nil {
								if embargoConfig.Data.MaxMinusBalance < saldoCity.Data.SumBalanceMinus*-1 {
									return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
										"error_type": "ERROR_EMBARGO",
										"en":         "Unacceptable due to an embargo",
										"id":         "Tidak dapat di proses karena embargo",
									})
								}
							}
						}

						if strings.Contains(embargoConfig.Data.EmbargoCodType, model.Destination) {
							saldoCity, err := c.cityBalanceRepo.Negatif(selfCtx, &model.CredentialRestAPI{
								Token: params.Token,
							}, data.SttDestinationCityID)
							if err == nil && embargoConfig != nil {
								if embargoConfig.Data.MaxMinusBalance < saldoCity.Data.SumBalanceMinus*-1 {
									return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
										"error_type": "ERROR_EMBARGO",
										"en":         "Unacceptable due to an embargo",
										"id":         "Tidak dapat di proses karena embargo",
									})
								}
							}
						}
					}
				}
			}

			if c.flagManagementRepo.CloudBeesEnableBookingInterpack(selfCtx) && params.Source == model.MANUAL && data.SttProductType == model.INTERPACK {
				if err := c.validateInternationalDocument(selfCtx, data, params.Token); err != nil {
					errLog = err
					return nil, err
				}
			}

		case model.CLIENT:
			if err = c.validateProductTypeAndSourceCreateSTT(data, params, errInvalid); err != nil {
				return nil, err
			}

			clientBooking, err := c.clientRepo.GetByID(selfCtx, params.AccountRefID, params.Token)
			if err != nil || clientBooking == nil {
				errLog = err
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Client Not Found",
					"id": "Client Tidak Ditemukan",
				})
			}
			actorExternalCode = clientBooking.Data.ClientElexysCode

			if clientBooking.Data.ClientDistrictCode == `` {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Client is not yet mapped with location",
					"id": "Client belum dipetakan dengan lokasi",
				})
			}

			if clientBooking.Data.ClientParentID == 0 {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Only Client branch can create booking",
					"id": "Hanya Client branch yang bisa membuat booking",
				})
			}

			if clientBooking.Data.ClientIsDO {
				data.SttIsDO = clientBooking.Data.ClientIsDO

				if data.SttNoRefExternal == `` {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "External Number is required to be filled",
						"id": "External Number harus diisi",
					})
				}

				if data.SttNo != `` {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Client cannot use STT manual",
						"id": "Client tidak bisa menggunakan STT manual",
					})
				}

				if isMixpack {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Client cannot use MIXPACK product type",
						"id": "Client tidak bisa menggunakan produk tipe MIXPACK",
					})
				}
			}

			if data.SttIsCOD {
				if !clientBooking.Data.ClientIsCOD {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Stt cannot be booked. Please check is client or the destination allowed for COD services",
						"id": "Stt tidak bisa dibooking. Mohon cek apakah client atau destinasi yang dipilih memperbolehkan layanan COD",
					})
				}

				if !data.SttIsDFOD && data.SttCODAmount < 1 {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Stt COD Amount cannot be empty if Is COD",
						"id": "Stt COD Amount tidak boleh kosong jika Is COD",
					})
				}

				// for Internal/POS/Client for Client, C1, C2; COD Amount can be different from Goods Price
				if !data.SttIsDFOD && data.IsCodFeeNotValid(clientBooking) {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Stt COD Fee cannot be less than 0 or Stt COD Amount is not the same as Stt Goods Price",
						"id": "Stt COD Fee tidak boleh kurang dari 0 atau Stt COD Amount tidak sama dengan Stt Goods Price",
					})
				}
			}

			if data.SttIsDFOD && !clientBooking.Data.ClientIsDFOD {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Stt cannot be booked. Please check is client or the destination allowed for DFOD services",
					"id": "Stt tidak bisa dibooking. Mohon cek apakah client atau destinasi yang dipilih memperbolehkan layanan DFOD",
				})
			}

			data.SttOriginDistrictID = clientBooking.Data.ClientDistrictCode
			referenceRole = strings.ToLower(model.CLIENT)
			referenceCode = clientBooking.Data.ClientCode

			BookedForActor = &model.Actor{
				ID:    clientBooking.Data.ClientID,
				Name:  clientBooking.Data.ClientCompanyName,
				Code:  clientBooking.Data.ClientCode,
				Type:  model.CLIENT,
				IsCod: clientBooking.Data.ClientIsCOD,
			}
		default:
			return nil, errInvalid
		}

		if !isPosMy {
			errTaxNumber := data.ValidateTaxNumber(data.SttTaxNumber)
			if errTaxNumber != nil {
				return nil, errTaxNumber
			}
		}

		bookedForExternalCode = actorExternalCode // default is the same as the one who created booking
		if data.SttNoRefExternal != `` {
			extNumberUsed := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "The external reference number is already in use, please check and change it.",
				"id": "Nomor referensi eksternal sudah terpakai, cek & ubah lagi.",
			})

			flag := c.sttRepo.GetCacheRefExternalFlag(selfCtx, data.SttNoRefExternal)

			if flag {
				return nil, extNumberUsed
			} else {
				sttCheck, err := c.sttRepo.Get(selfCtx, &model.SttViewDetailParams{
					Stt: model.Stt{SttNoRefExternal: data.SttNoRefExternal},
				})

				if err != nil {
					errLog = err
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Failed to retrieved stt",
						"id": "Gagal mendapatkan stt",
					})
				}

				isSttNoRefExternalExist := (sttCheck != nil && sttCheck.SttID > 0) || !c.sttRepo.CreateCacheRefExternalFlag(selfCtx, data.SttNoRefExternal)
				if isSttNoRefExternalExist {
					return nil, extNumberUsed
				}
			}
		}

		errG, errGCtx := errgroup.WithContext(selfCtx)

		// validation commodity
		commodity := new(model.Commodity)
		errG.Go(func() error {
			tempCommodity, err := c.commodityRepo.GetCommodityByCode(errGCtx, data.SttCommodityCode, params.Token)
			if err != nil || tempCommodity == nil {
				errLog = err
				return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
					"error_type": "ERROR_COMMODITY_NOT_FOUND",
					"en":         "Commodity is not available",
					"id":         "Commodity yang dipilih tidak tersedia",
				})
			}

			if tempCommodity.Data.CommodityStatus != model.ACTIVE {
				return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
					"error_type": "ERROR_COMMODITY_INACTIVE",
					"en":         "Commodity is inactive",
					"id":         "Commodity sedang tidak aktif",
				})
			}
			commodity = tempCommodity
			return nil
		})

		// validation product
		errG.Go(func() error {
			productType, err := c.productRepo.GetProductTypes(errGCtx, &model.ListProductTypeRequest{
				Token:  params.Token,
				Code:   data.SttProductType,
				Status: model.ACTIVE,
				Limit:  model.DefaultLimit,
				Page:   model.DefaultPage,
			})
			if err != nil || productType == nil {
				errLog = err
				return errHTTPRequest("Get Product Type")
			}

			if len(productType.Data) < 1 {
				return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
					"en": "Product Type is inactive",
					"id": "Product Type sedang tidak aktif",
				})
			}
			return nil
		})

		// validation insurance
		errG.Go(func() error {
			configPrices, err := c.configurablePriceRepo.Select(errGCtx, &model.ConfigurablePriceViewParams{
				ConfigurablePriceStatus: model.ACTIVE,
				Token:                   params.Token,
			})

			errInsuranceInactive := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Insurance is inactive",
				"id": "Insurance sedang tidak aktif",
			})

			isConfigPriceNotFound := err != nil || configPrices == nil || len(configPrices.Data) == 0
			if isConfigPriceNotFound {
				errLog = err
				return errInsuranceInactive
			}

			isInsuraceActive := false
			for _, configPrice := range configPrices.Data {
				if configPrice.ConfigurablePriceType == model.INSURANCE {
					isInsuraceActive = true
					break
				}
			}

			if !isInsuraceActive {
				return errInsuranceInactive
			}
			return nil
		})

		errDistrictCityInactive := func(source string) error {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": fmt.Sprintf("%s District is inactive", source),
				"id": fmt.Sprintf("%s District sedang tidak active", source),
			})
		}

		errDistrictCity := func(source string) error {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": fmt.Sprintf("%s District is not available", source),
				"id": fmt.Sprintf("%s District yang dipilih tidak tersedia", source),
			})
		}

		errCityInactive := func(source string) error {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": fmt.Sprintf("%s City is inactive", source),
				"id": fmt.Sprintf("%s City sedang tidak active", source),
			})
		}

		errCity := func(source string) error {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": fmt.Sprintf("%s City is not available", source),
				"id": fmt.Sprintf("%s City yang dipilih tidak tersedia", source),
			})
		}

		// validation origin address
		districtOrigin := new(model.District)
		cityOrigin := new(model.City)
		errG.Go(func() error {
			tempDistrictOrigin, err := c.districtRepo.GetByCode(errGCtx, &model.CredentialRestAPI{
				Token: params.Token,
			}, data.SttOriginDistrictID)

			isTempDistrictOriginNotFound := err != nil || tempDistrictOrigin == nil || tempDistrictOrigin.Data.City == nil
			if isTempDistrictOriginNotFound {
				errLog = err
				return errDistrictCity("Origin")
			}

			if strings.ToLower(tempDistrictOrigin.Data.Status) != model.ACTIVE {
				return errDistrictCityInactive("Origin")
			}

			tempCityOrigin, err := c.cityRepo.Get(errGCtx, tempDistrictOrigin.Data.City.Code, params.Token)
			if err != nil || tempCityOrigin == nil {
				errLog = err
				return errCity("Origin")
			}

			if strings.ToLower(tempCityOrigin.IsActive) != model.ACTIVE {
				return errCityInactive("Origin")
			}

			districtOrigin = tempDistrictOrigin
			cityOrigin = tempCityOrigin
			return nil
		})

		// validation destination address
		districtDestination := new(model.District)
		cityDestination := new(model.City)
		errG.Go(func() error {
			tempDistrictDestination, err := c.districtRepo.GetByCode(errGCtx, &model.CredentialRestAPI{
				Token: params.Token,
			}, data.SttDestinationDistrictID)

			if err != nil || tempDistrictDestination == nil || tempDistrictDestination.Data.City == nil {
				errLog = err
				return errDistrictCity("Destination")
			}

			if strings.ToLower(tempDistrictDestination.Data.Status) != model.ACTIVE {
				return errDistrictCityInactive("Destination")
			}

			sttVendor := (tempDistrictDestination.Data.Type == model.TYPE_VENDOR || tempDistrictDestination.Data.Type == model.TYPE_VENDOR_LANJUTAN)
			sttVendorNinjaOrPI := tempDistrictDestination.Data.VendorCode == model.TypeVendorNINJA || tempDistrictDestination.Data.VendorCode == model.TypeVendorPTPOS
			sttVendorNotAllowPiecesMoreThan1 := sttVendor && sttVendorNinjaOrPI && len(data.SttPieces) > 1
			if sttVendorNotAllowPiecesMoreThan1 {
				return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
					"en": "District destination type doesn't support delivery item that have more than 1 koli wihtin 1 STT",
					"id": "Tipe district destinasi tidak mendukung pengiriman barang yang memiliki lebih dari 1 koli dalam satu STT",
				})
			}

			//validate district for STT DO
			if err := c.validateDistrictTypeSttDO(errGCtx, data.SttIsDO, tempDistrictDestination); err != nil {
				errLog = err
				return err
			}

			tempCityDestination, err := c.cityRepo.Get(errGCtx, tempDistrictDestination.Data.City.Code, params.Token)
			if err != nil || tempCityDestination == nil {
				errLog = err
				return errCity("Destination")
			}

			if strings.ToLower(tempCityDestination.IsActive) != model.ACTIVE {
				return errCityInactive("Destination")
			}

			districtDestination = tempDistrictDestination
			cityDestination = tempCityDestination
			return nil
		})

		err = errG.Wait()
		if err != nil {
			errLog = err
			return nil, err
		}

		data.SttDestinationCityID = districtDestination.Data.City.Code
		data.SttOriginCityID = districtOrigin.Data.City.Code
		currency = cityOrigin.Country.Currency

		// validate insurance type
		if params.Source == model.MANUAL {
			if data.SttGoodsEstimatePrice >= model.MIN_INSURANCE_BASIC {
				data.SttInsuranceType = model.INSURANCEBASIC
			}
		}

		/**
		 * get check tariff
		 */

		reqCheckTariff = &stt.CalculateTariffParams{
			RequestCalculateTariff: &stt.RequestCalculateTariff{
				OriginID:      data.SttOriginDistrictID,
				DestinationID: data.SttDestinationDistrictID,
				ProductType:   data.SttProductType,
				CommodityID:   commodity.Data.CommodityID,
				InsuranceType: data.SttInsuranceType,
				GoodsPrice:    data.SttGoodsEstimatePrice,
				IsWoodpacking: data.SttIsWoodpacking,
				AccountType:   referenceRole,
				AccountRefID:  params.AccountRefID,
				IsHaveTaxID:   false,
			},
			Token: params.Token,
		}

		reqCheckTariff.RequestCalculateTariff.ShipmentPrefix = shared.GetPrefixFromShipmentOrNoRefExt(data.SttShipmentID, data.SttNoRefExternal)

		// if tarif get from algo, should be use client tariff BL/TP/CA , AP & AS prefix using client tarif)
		if params.Source == model.ALGO {
			if shared.GetPrefixShipmentID(data.SttShipmentID) == model.C1 || shared.GetPrefixShipmentID(data.SttShipmentID) == model.C2 {
				shipment, err := c.shipmentRepo.Get(selfCtx, &model.ShipmentViewParams{
					ShipmentAlgoID: data.SttShipmentID,
				})
				if err != nil || shipment == nil {
					errLog = err
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Shipment Not Found",
						"id": "Shipment Tidak Ditemukan",
					})
				}

				// if shipment C1 and stt is_cod true Set cod_handling
				if data.SttIsCOD {
					shipmentMeta := shipment.ShipmentMetaToStruct()
					if shipmentMeta != nil && shipment.ShipmentMeta != nil {
						switch shipmentMeta.CodHandling {
						case model.SPECIALCOD:
							codHandling = model.SPECIALCOD
						case model.STANDARDCOD:
							codHandling = model.STANDARDCOD
						case "":
							codHandling = model.STANDARDCOD
						}
					}

				}

				if len(shipment.ShipmentPackets) > 0 {
					spcCode := shipment.ShipmentPackets[0].ShipmentPacketCustomerCode
					if spcCode == "" {
						return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
							"en": "Shipment Packet Customer Code cannot be empty",
							"id": "Shipment Packet Customer Code tidak boleh kosong",
						})
					}

					client, err := c.clientRepo.GetByCode(selfCtx, spcCode, params.Token)
					if err != nil || client == nil {
						errLog = err
						return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
							"en": "Client Not Found",
							"id": "Client Tidak Ditemukan",
						})
					}

					// validation parent only
					if client.Data.ClientParentID > 0 {
						return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
							"en": "Client is not parent",
							"id": "Client bukan parent",
						})
					}

					spcBranchCode := shipment.ShipmentPackets[0].ShipmentPacketCustomerBranchCode
					if spcBranchCode == "" {
						return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
							"en": "Shipment Packet Customer Branch Code cannot be empty",
							"id": "Shipment Packet Customer Branch Code tidak boleh kosong",
						})
					}

					clientBranch, err := c.clientRepo.GetByCode(selfCtx, spcBranchCode, params.Token)
					if err != nil || client == nil {
						return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
							"en": "Client Branch Not Found",
							"id": "Client Branch Tidak Ditemukan",
						})
					}

					// validation parent ID
					if clientBranch.Data.ClientParentID != client.Data.ClientID {
						return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
							"en": "Client parent incorrect",
							"id": "Client induk salah",
						})
					}

					// set check tariff should be get from client
					reqCheckTariff.RequestCalculateTariff.AccountType = model.CLIENT
					reqCheckTariff.RequestCalculateTariff.AccountRefID = clientBranch.Data.ClientID

					bookedForExternalCode = clientBranch.Data.ClientElexysCode

					// COD Total Tarif
					if data.SttIsCOD {

						if codHandling == model.SPECIALCOD {
							// COD Retail

							codConfig, err := c.codConfigRepo.ViewCodConfig(selfCtx, params.Token)
							if err != nil || codConfig == nil || len(codConfig.Data) == 0 {
								errLog = err
								return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
									"en": "COD configuration not found",
									"id": "Konfigurasi COD tidak ditemukan",
								})
							}

							config := codConfig.Data[0]
							for _, data := range codConfig.Data {
								if data.CcoType == model.CcoTypeRetail {
									config = data
									break
								}
							}

							ccoProductTypeList := strings.Split(config.CcoProductType, ",")
							for i, ccoProductType := range ccoProductTypeList {
								if ccoProductType == data.SttProductType {
									break
								}
								if i == len(ccoProductTypeList)-1 {
									return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
										"en": "Product type is not supported for COD services",
										"id": "Tipe produk tidak didukung untuk layanan COD",
									})
								}
							}

							// validasi type cod configuration
							if config.CcoType != model.CcoTypeRetail {
								return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
									"en": "COD Retail configuration not found",
									"id": "Konfigurasi COD Retail tidak ditemukan",
								})
							}

							if data.SttGoodsEstimatePrice < config.CcoMinPrice {
								return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
									"en": "Minimum goods prices for COD services is " + strconv.FormatFloat(config.CcoMinPrice, 'f', 0, 64),
									"id": "Harga barang minimum untuk layanan COD adalah " + strconv.FormatFloat(config.CcoMinPrice, 'f', 0, 64),
								})
							}

							if data.SttGoodsEstimatePrice > config.CcoMaxPrice {
								return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
									"en": "Maximum goods prices for COD services is " + strconv.FormatFloat(config.CcoMaxPrice, 'f', 0, 64),
									"id": "Harga barang maksimum untuk layanan COD adalah " + strconv.FormatFloat(config.CcoMaxPrice, 'f', 0, 64),
								})
							}

							if config.CcoIsInsurance && data.SttInsuranceType != model.INSURANCEFREE {
								data.SttInsuranceType = model.INSURANCEFREE
								reqCheckTariff.RequestCalculateTariff.InsuranceType = model.INSURANCEFREE
							}

							percentageCodFee = config.CcoPercentageCod
							minimumCodFee = config.CcoMinCodFee
							originCommissionRate = config.CcoOriginCommissionRate
							destinationCommissionRate = config.CcoDestinationCommissionRate
							codIsInsurance = config.CcoIsInsurance
							codAmountConfigType = config.CcoCodType

							// check tariff return
							reqCalcTariff := *reqCheckTariff.RequestCalculateTariff
							reqCalcTariff.IsCod = false
							reqCalcTariff.IsDisablePromo = true
							reqCalcTariff.OriginID = reqCheckTariff.RequestCalculateTariff.DestinationID
							reqCalcTariff.DestinationID = reqCheckTariff.RequestCalculateTariff.OriginID
							reqCheckTariffReturn := &stt.CalculateTariffParams{
								RequestCalculateTariff: &reqCalcTariff,
								Token:                  params.Token,
							}
							checkTariffReturn, err := c.checkTariffRepo.TariffCalculationV2(selfCtx, reqCheckTariffReturn)
							if err != nil || checkTariffReturn == nil {
								errLog = err
								return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
									"en": "Check tarif return is not found",
									"id": "Cek tarif return tidak ditemukan",
								})
							}

							totalTariffReturn = checkTariffReturn.Data.TotalTariff

							reqCheckTariff.RequestCalculateTariff.ShipmentPrefix = shared.GetPrefixShipmentID(data.SttShipmentID)
							reqCheckTariff.RequestCalculateTariff.IsCod = true
							reqCheckTariff.RequestCalculateTariff.CodAmount = data.SttCODAmount

						} else {
							// COD Client
							rateVatShipment = c.cfg.RateVatShipment()
							rateVatCod = c.cfg.RateVatCod()

							reqCheckTariff.RequestCalculateTariff.IsCod = true
							reqCheckTariff.RequestCalculateTariff.CodAmount = data.SttCODAmount

							clientCodConfigAmount = clientBranch.Data.ClientCodConfigAmount
							clientPaymentMethod = "invoice"

							if clientBranch.Data.ClientCodConfigAmount == model.GoodsPriceTotalTarif {
								clientPaymentMethod = client.Data.ClientPaymentMethod
								clientCodShipmentDiscount = clientBranch.Data.ClientCodShipmentDiscount
							}

							if data.SttIsDFOD {
								clientPaymentMethod = model.ClientPaymentMethodSplitBill
								clientCodShipmentDiscount = clientBranch.Data.ClientCodShipmentDiscount
							}
						}

					}
				} else {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Shipment Packet Not Found",
						"id": "Shipment Packet Tidak Ditemukan",
					})
				}
			} else {
				clientCode := model.MappingShipmentPrefixClientCode[shared.GetPrefixShipmentID(data.SttShipmentID)]
				client, err := c.clientRepo.GetByCode(selfCtx, clientCode, params.Token)
				if err != nil || client == nil {
					errLog = err
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Client Not Found",
						"id": "Client Tidak Ditemukan",
					})
				}
				// set check tariff should be get from client
				reqCheckTariff.RequestCalculateTariff.AccountType = model.CLIENT
				reqCheckTariff.RequestCalculateTariff.AccountRefID = client.Data.ClientID

				bookedForExternalCode = client.Data.ClientElexysCode

				if client.Data.ClientIsDO {
					data.SttIsDO = client.Data.ClientIsDO

					// prefix DO, client data ClientIsDO true and ClientDOPaymentType free so set SttTotalAmount = 0
					if data.SttShipmentID != "" && shared.GetPrefixShipmentID(data.SttShipmentID) == model.DO &&
						client.Data.ClientDOPaymentType == model.FREE {
						clientIsDOPaymentTypeFree = true
						data.SttTotalAmount = 0
					}

				}
			}
		}

		IsPrefixCODCARetail := data.SttIsCOD && data.SttShipmentID != `` && model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(data.SttShipmentID)]
		IsCODPrefixShipmentFavorite := data.SttIsCOD && data.SttShipmentID != `` && model.IsShipmentFavorite[shared.GetPrefixShipmentID(data.SttShipmentID)]
		IsShipmentTokopedia := data.SttIsCOD && data.SttShipmentID != `` && model.IsShipmentTokopedia[shared.GetPrefixShipmentID(data.SttShipmentID)]

		if IsPrefixCODCARetail || (params.Source == model.MANUAL && data.SttIsCOD && (strings.EqualFold(referenceRole, model.CLIENT) || strings.EqualFold(referenceRole, model.PARTNERPOS))) || (params.Source == model.ALGO && IsCODPrefixShipmentFavorite) {
			reqCheckTariff.RequestCalculateTariff.IsCod = true
			reqCheckTariff.RequestCalculateTariff.CodAmount = data.SttCODAmount
		}

		if IsShipmentTokopedia && data.SttIsCOD {
			reqCheckTariff.RequestCalculateTariff.IsCod = true
			reqCheckTariff.RequestCalculateTariff.CodAmount = data.SttCODAmount
		}

		if IsShipmentTokopedia && data.SttIsDFOD {
			reqCheckTariff.RequestCalculateTariff.IsDfod = true
			reqCheckTariff.RequestCalculateTariff.IsCod = true
			reqCheckTariff.RequestCalculateTariff.CodAmount = data.SttCODAmount
		}

		if IsCODPrefixShipmentFavorite && data.DiscountFavoritePercentage > 0 {
			reqCheckTariff.RequestCalculateTariff.DiscountFavoritePercentage = data.DiscountFavoritePercentage
		}

		if data.SttTaxNumber != `` {
			reqCheckTariff.RequestCalculateTariff.IsHaveTaxID = true
		}

		isDisablePromo = !c.checkCalculateTariffPromoElligible(referenceRole, BookedForActor.Type, params.Source)

		promoAppliedTo := ""
		// if tarif get from algo and prefix  AG / AI / AD / ACA / AO / ACB / AP / AS, should be disable promo set false
		if params.Source == model.ALGO && data.SttShipmentID != "" && model.PrefixShipmentIDIsAllowdPromoDiscont[shared.GetPrefixShipmentID(data.SttShipmentID)] && c.cfg.EnablePromoBookingShipment() {
			isDisablePromo = false
			promoAppliedTo = model.PromoAppliedToShipment
		}

		reqCheckTariff.RequestCalculateTariff.IsDisablePromo = isDisablePromo

		reqCheckTariff.RequestCalculateTariff.GeneratePiecesCalculateTariff(data.SttPieces)

		// is Source parameter manual and Account Type Client
		isSourceManualAccountTypeClient := params.Source == model.MANUAL && params.AccountType == model.CLIENT

		if isSourceManualAccountTypeClient {
			clientBooking, err := c.clientRepo.GetByID(selfCtx, params.AccountRefID, params.Token)
			if err != nil || clientBooking == nil {
				errLog = err
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Client Not Found",
					"id": "Client Tidak Ditemukan",
				})
			}

			// fitur COD Total Tarif
			if data.SttIsCOD {
				rateVatShipment = c.cfg.RateVatShipment()
				rateVatCod = c.cfg.RateVatCod()

				actorExternalCode = clientBooking.Data.ClientElexysCode

				clientCodConfigAmount = clientBooking.Data.ClientCodConfigAmount
				clientPaymentMethod = "invoice"

				if clientBooking.Data.ClientCodConfigAmount == model.GoodsPriceTotalTarif {

					clientParent, err := c.clientRepo.GetByID(selfCtx, clientBooking.Data.ClientParentID, params.Token)
					if err != nil || clientParent == nil {
						errLog = err
						return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
							"en": "Client is not found",
							"id": "Client tidak ditemukan",
						})
					}

					clientPaymentMethod = clientParent.Data.ClientPaymentMethod
					clientCodShipmentDiscount = clientBooking.Data.ClientCodShipmentDiscount
				}

			}

			if data.SttIsDFOD {
				clientPaymentMethod = model.ClientPaymentMethodSplitBill
				clientCodShipmentDiscount = clientBooking.Data.ClientCodShipmentDiscount
			}

		}

		if IsPrefixCODCARetail || IsCODPrefixShipmentFavorite || IsShipmentTokopedia || (params.AccountType == model.PARTNER && data.SttIsCOD && params.Source == model.MANUAL) || codHandling == model.SPECIALCOD {

			codConfig, err := c.codConfigRepo.ViewCodConfig(selfCtx, params.Token)
			if err != nil || codConfig == nil || len(codConfig.Data) == 0 {
				errLog = err
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "COD configuration not found",
					"id": "Konfigurasi COD tidak ditemukan",
				})
			}

			// default config COD retail
			config := codConfig.Data[len(codConfig.Data)-1]
			configName := `COD`

			if IsPrefixCODCARetail || IsShipmentTokopedia || IsCODPrefixShipmentFavorite || data.SttIsDFOD {
				for _, v := range codConfig.Data {
					// Shipment COD CA
					if v.CcoType == model.CcoTypeCARetail && (IsPrefixCODCARetail || IsCODPrefixShipmentFavorite) && !data.SttIsDFOD {
						configName = `COD`
						config = v
						break
					}
					// tokopedia COD
					if v.CcoType == model.CcoTypeTokopedia && IsShipmentTokopedia && !data.SttIsDFOD {
						configName = `COD`
						config = v
						break
					}

					// shipment DFOD CA
					if v.CcoType == model.CcoTypeDFODCA && (IsPrefixCODCARetail || IsCODPrefixShipmentFavorite) && data.SttIsDFOD {
						configName = `DFOD`
						config = v
						break
					}

					// shipment DFOD retail
					if v.CcoType == model.CcoTypeDFODRetail && data.SttIsDFOD {
						configName = `DFOD`
						config = v
						break
					}

					//tokopedia dfod
					if v.CcoType == model.CcoTypeDFODTokopedia && IsShipmentTokopedia && data.SttIsDFOD {
						configName = `DFOD`
						config = v
						break
					}

				}
			}

			if IsPrefixCODCARetail || IsCODPrefixShipmentFavorite {
				shipmentPrefixList := strings.Split(config.CcoShipmentPrefix, ",")
				findPrefix := false
				if len(shipmentPrefixList) > 0 {
					prefix := shared.GetPrefixShipmentID(data.SttShipmentID)
					for _, shipPrefix := range shipmentPrefixList {
						if shipPrefix == prefix {
							findPrefix = true
							break
						}
					}
				}

				if !(config.CcoType == model.CcoTypeCARetail || config.CcoType == model.CcoTypeDFODCA) || !findPrefix {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": fmt.Sprintf("%s Customer Apps Retail configuration not found", configName),
						"id": fmt.Sprintf("Konfigurasi %s Customer Apps Retail tidak ditemukan", configName),
					})
				}

			}

			// C1 Specialcod get config cod retail
			if codHandling == model.SPECIALCOD {
				for _, v := range codConfig.Data {
					if v.CcoType == model.CcoTypeRetail {
						config = v
						break
					}
				}

				if config.CcoType != model.CcoTypeRetail {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "COD Retail configuration not found",
						"id": "Konfigurasi COD Retail tidak ditemukan",
					})
				}
			}

			ccoProductTypeList := strings.Split(config.CcoProductType, ",")
			for i, ccoProductType := range ccoProductTypeList {
				if ccoProductType == data.SttProductType {
					break
				}
				if i == len(ccoProductTypeList)-1 {
					return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": fmt.Sprintf("Product type is not supported for %s", configName),
						"id": fmt.Sprintf("Tipe produk tidak didukung untuk layanan %s", configName),
					})
				}
			}

			if !data.SttIsDFOD && data.SttGoodsEstimatePrice < config.CcoMinPrice {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Minimum goods prices for COD services is " + strconv.FormatFloat(config.CcoMinPrice, 'f', 0, 64),
					"id": "Harga barang minimum untuk layanan COD adalah " + strconv.FormatFloat(config.CcoMinPrice, 'f', 0, 64),
				})
			}

			if data.SttGoodsEstimatePrice > config.CcoMaxPrice {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Maximum goods prices for " + configName + " services is " + strconv.FormatFloat(config.CcoMaxPrice, 'f', 0, 64),
					"id": "Harga barang maksimum untuk layanan " + configName + " adalah " + strconv.FormatFloat(config.CcoMaxPrice, 'f', 0, 64),
				})
			}

			// untuk ARA ARB non DFOD, algo akan kirim insurance=basic namun di genesis di set sebagai FREE
			// untuk ARA ARB DFOD, jika config insurance true maka di set sebagai FREE
			if params.Source == model.ALGO && (IsPrefixCODCARetail || IsCODPrefixShipmentFavorite) {
				if !data.SttIsDFOD || (data.SttIsDFOD && config.CcoIsInsurance) {
					data.SttInsuranceType = model.INSURANCEFREE
				}
			}

			if config.CcoIsInsurance && data.SttInsuranceType != model.INSURANCEFREE {
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Insurance type is not supported for COD/DFOD services",
					"id": "Tipe asuransi tidak didukung untuk layanan COD/DFOD",
				})
			}

			percentageCodFee = config.CcoPercentageCod
			minimumCodFee = config.CcoMinCodFee
			originCommissionRate = config.CcoOriginCommissionRate
			destinationCommissionRate = config.CcoDestinationCommissionRate
			codIsInsurance = config.CcoIsInsurance
			codAmountConfigType = config.CcoCodType
			originMinCommission = config.CcoOriginMinCommission
			destinationMinCommission = config.CcoDestinationMinCommission

			// check tariff return
			reqCalcTariff := *reqCheckTariff.RequestCalculateTariff
			reqCalcTariff.IsCod = false
			reqCalcTariff.IsDfod = false
			reqCalcTariff.IsDisablePromo = true
			reqCalcTariff.OriginID = reqCheckTariff.RequestCalculateTariff.DestinationID
			reqCalcTariff.DestinationID = reqCheckTariff.RequestCalculateTariff.OriginID
			reqCheckTariffReturn := &stt.CalculateTariffParams{
				RequestCalculateTariff: &reqCalcTariff,
				Token:                  params.Token,
			}
			checkTariffReturn, err := c.checkTariffRepo.TariffCalculation(selfCtx, reqCheckTariffReturn)
			if err != nil || checkTariffReturn == nil {
				errLog = err
				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Check tarif return is not found",
					"id": "Cek tarif return tidak ditemukan",
				})
			}

			totalTariffReturn = checkTariffReturn.Data.TotalTariff
		}

		if params.AccountType == model.PARTNER && params.Source == model.MANUAL {
			promoAppliedTo = model.PromoAppliedToRetail
		}

		if IsPrefixCODCARetail || IsCODPrefixShipmentFavorite {
			promoAppliedTo = model.PromoAppliedToShipment
		}

		reqCheckTariff.RequestCalculateTariff.PromoAppliedTo = promoAppliedTo

		isShipmentIDC1C2 := data.SttShipmentID != `` && (shared.GetPrefixShipmentID(data.SttShipmentID) == model.C1 || shared.GetPrefixShipmentID(data.SttShipmentID) == model.C2)
		if isShipmentIDC1C2 {
			reqCheckTariff.RequestCalculateTariff.ShipmentPrefix = shared.GetPrefixShipmentID(data.SttShipmentID)
		}

		// Send param cod_handling to checktariff
		reqCheckTariff.RequestCalculateTariff.CodHandling = codHandling

		reqCheckTariff.RequestCalculateTariff.IsDfod = data.SttIsDFOD

		checkTariff, err := c.checkTariffRepo.TariffCalculation(selfCtx, reqCheckTariff)
		if err != nil || checkTariff == nil {
			errLog = err
			return nil, err
		}

		// validate total stt volume weight and total stt gross weight
		if err := stt.ValidateTotalWeight(checkTariff.Data.VolumeWeight, checkTariff.Data.GrossWeight, data.SttShipmentID, data.SttNoRefExternal); err != nil {
			errLog = err
			return nil, err
		}

		isCodFeeMoreThanSttGoodsEstimatePrice := !data.SttIsDFOD && data.SttIsCOD && ((params.AccountType == model.PARTNER && params.Source == model.MANUAL) || IsPrefixCODCARetail || IsCODPrefixShipmentFavorite) && checkTariff.Data.CodFee > data.SttGoodsEstimatePrice
		if isCodFeeMoreThanSttGoodsEstimatePrice {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt Goods Estimate Price must not be lower than COD Fee",
				"id": "Stt Goods Estimate Price tidak boleh lebih kecil dari COD Fee",
			})
		}

		if err := c.checkSaldo(selfCtx, stt.CheckSaldoParams{
			Source:         params.Source,
			AccountRefType: referenceRole,
			Token:          params.Token,
			Stt:            &data,
			CheckTariff:    &checkTariff.Data,
			IsCODDelivery:  actorIsCODDelivery,
		}); err != nil {
			errLog = err
			return nil, err
		}

		// build remarks piece history
		tempRemarksPieceHistory := model.RemarkPieceHistory{
			ActorExternalCode:   actorExternalCode,
			ActorExternalType:   params.AccountType,
			HistoryLocationName: cityOrigin.Name,

			// booking fee need to be excluded cod_fee to calculate penalty adjustment
			BookingFee: c.getBookingFee(reqCheckTariff, checkTariff),
			BookingFeeAfterDiscount: func() float64 {
				if reqCheckTariff.RequestCalculateTariff.IsCod && checkTariff.Data.CodFeeAfterDiscount > 0 {
					return checkTariff.Data.TotalTariffAfterDiscount - checkTariff.Data.CodFeeAfterDiscount
				}
				return checkTariff.Data.TotalTariffAfterDiscount
			}(),
			VolumeWeightDiscount:     checkTariff.Data.VolumeWeightDiscount,
			ClientCodBookingDiscount: checkTariff.Data.CODBookingDiscount,
			ChargeableWeight:         checkTariff.Data.ChargeableWeight,
			HistoryDistrictCode:      districtOrigin.Data.Code,
			HistoryDistrictName:      districtOrigin.Data.Name,
			HistoryDataAdjustment: &model.HistoryDataAdjustment{
				IsPromo:                  checkTariff.Data.IsPromo,
				TotalTariff:              checkTariff.Data.TotalTariff,
				TotalTariffAfterDiscount: checkTariff.Data.TotalTariffAfterDiscount,
			},
			SttWeightAttachFiles: params.SttWeightAttachFiles,
		}

		// set STT meta
		sttMeta := model.SttMeta{
			EstimateSLA:             c.generateEstimaeSlaByProductType(checkTariff.Data.EstimateSLA, data.SttProductType, now, cityOrigin.Timezone),
			RoundingDiff:            checkTariff.Data.RoundingDiff,
			OriginCityName:          cityOrigin.Name,
			OriginDistrictName:      districtOrigin.Data.Name,
			DestinationCityName:     cityDestination.Name,
			DestinationDistrictName: districtDestination.Data.Name,
			SttBookedByCountry:      cityOrigin.Country.Code,
			DetailCalculateRetailTariff: []model.DetailCalculateRetailTariff{
				{
					Status:       model.BKD,
					IsCalculated: false,
				},
			},
			PostalCodeDestination: data.PostalCodeDestination,

			VolumeWeightDiscount:      checkTariff.Data.VolumeWeightDiscount,
			ClientPaymentMethod:       clientPaymentMethod,
			ClientCodConfigAmount:     clientCodConfigAmount,
			ClientCodShipmentDiscount: clientCodShipmentDiscount,
			RateVatShipment:           rateVatShipment,
			RateVatCod:                rateVatCod,
			ListDiscount:              checkTariff.Data.ListDiscount,
			SttFtzCIPL:                data.SttFtzCIPL,
			SttFtzRecipientEmail:      data.SttFtzRecipientEmail,
			PostalCodeOrigin:          data.PostalCodeOrigin,
			SttFtzIdentityNumber:      data.SttFtzIdentityNumber,
		}

		sttMeta = c.generateSttMetaPartnerPosInterpack(&data, sttMeta, referenceRole)
		if shipmentSubscriptionPriority {
			sttMeta.PrioritySubscription = true
		}

		if data.SttProductType == model.INTERPACK {
			sttMeta.SttAttachFiles = data.SttAttachFiles
			sttMeta.SttCommodityDetail = data.SttCommodityDetail
			sttMeta.SttRecipientEmail = data.SttRecipientEmail
			sttMeta.SttKtpImage = data.SttKtpImage
			sttMeta.SttTaxImage = data.SttTaxImage
		}

		sttMeta.SttFtzAttachFiles = data.SttFtzAttachFiles
		sttMeta.SttFtzRecipientEmail = data.SttFtzRecipientEmail
		sttMeta.SttFtzKtpImage = data.SttFtzKtpImage
		sttMeta.SttFtzTaxImage = data.SttFtzTaxImage

		isSpecialCODShipmentC1C2OrCODCARetailOrShipmentFavoriteOrAccountPartnerSttCOD := (data.SttShipmentID != `` && (shared.GetPrefixShipmentID(data.SttShipmentID) == model.C1 || shared.GetPrefixShipmentID(data.SttShipmentID) == model.C2) && codHandling == model.SPECIALCOD) || IsPrefixCODCARetail || IsCODPrefixShipmentFavorite || (params.AccountType == model.PARTNER && data.SttIsCOD && params.Source == model.MANUAL)
		if isSpecialCODShipmentC1C2OrCODCARetailOrShipmentFavoriteOrAccountPartnerSttCOD {
			sttMeta.CodConfiguration = &model.CodConfiguration{
				PercentageCodFee:             percentageCodFee,
				MinCodFee:                    minimumCodFee,
				PosOriginCommissionRate:      originCommissionRate,
				PosDestinationCommissionRate: destinationCommissionRate,
				CodIsInsurance:               codIsInsurance,
				CodAmountConfigType:          codAmountConfigType,
			}
			sttMeta.TotalTariffReturn = totalTariffReturn
		}

		if data.SttIsDFOD {
			sttMeta.CodConfiguration = nil
			sttMeta.DfodConfiguration = &model.DfodConfiguration{
				DfodFee:                  checkTariff.Data.CodFee,
				OriginMinCommission:      originMinCommission,
				DestinationMinCommission: destinationMinCommission,
				CodConfiguration: model.CodConfiguration{
					PercentageCodFee:             percentageCodFee,
					MinCodFee:                    minimumCodFee,
					PosOriginCommissionRate:      originCommissionRate,
					PosDestinationCommissionRate: destinationCommissionRate,
					CodIsInsurance:               codIsInsurance,
					CodAmountConfigType:          codAmountConfigType,
				},
			}
			sttMeta.TotalTariffReturn = totalTariffReturn
		}

		// add retail tarif when stt booked by pos for pos
		if referenceRole == model.POS && BookedForActor.Type == model.POS {
			sttMeta.RetailTariff = &checkTariff.Data
			sttMeta.RetailTariff.ListDiscount = make([]model.PromoDiscount, 0)
		}

		// flag paid/unpaid
		paymentStatus := ``
		isPaymentStatusShouldUnpaid := IsPrefixCODCARetail || (c.cfg.EnableFeatureHoldCommission() && data.SttShipmentID != `` && model.IsPrefixShipmentPaymentHoldValid[shared.GetPrefixShipmentID(data.SttShipmentID)])
		if isPaymentStatusShouldUnpaid {
			paymentStatus = model.UNPAID
		}

		if referenceRole == model.POS && params.Source == model.ALGO {
			clientTariffBeforeDiscount := c.getClientTariffBeforeDiscount(checkTariff)

			cityRateAfterDiscount := checkTariff.Data.PublishRateAfterDiscount + checkTariff.Data.ShippingSurchargeRateAfterDiscount
			forwardRateAfterDiscount := checkTariff.Data.OriginDistrictRateAfterDiscount + checkTariff.Data.DestinationDistrictRateAfterDiscount
			shippingCostAfterDiscount := cityRateAfterDiscount + forwardRateAfterDiscount

			sttMeta.ClientTariff = &model.ClientTariff{
				BeforeDiscount: clientTariffBeforeDiscount,
				AfterDiscount: model.ClientTariffDetailWithDiscount{
					ClientTariffDetail: model.ClientTariffDetail{
						CityRates:               cityRateAfterDiscount,
						ForwardRates:            forwardRateAfterDiscount,
						ShippingCost:            shippingCostAfterDiscount,
						CommoditySurcharge:      checkTariff.Data.CommoditySurchargeAfterDiscount,
						HeavyWeightSurcharge:    checkTariff.Data.HeavyWeightSurchargeAfterDiscount,
						DocumentSurcharge:       checkTariff.Data.DocumentSurchargeAfterDiscount,
						InsuranceRates:          checkTariff.Data.InsuranceRatesAfterDiscount,
						WoodpackingRates:        checkTariff.Data.WoodpackingRatesAfterDiscount,
						TotalTariff:             checkTariff.Data.TotalTariffAfterDiscount,
						TaxRates:                checkTariff.Data.TaxRates,
						BMTaxRate:               checkTariff.Data.BMTaxRate,
						PPNTaxRate:              checkTariff.Data.PPNTaxRate,
						PPHTaxRate:              checkTariff.Data.PPHTaxRate,
						OriginDistrictRate:      checkTariff.Data.OriginDistrictRateAfterDiscount,
						DestinationDistrictRate: checkTariff.Data.DestinationDistrictRateAfterDiscount,
						PublishRate:             checkTariff.Data.PublishRateAfterDiscount,
						ShippingSurchargeRate:   checkTariff.Data.ShippingSurchargeRateAfterDiscount,
						CodAmount:               checkTariff.Data.CodAmount,
						CodFee:                  checkTariff.Data.CodFeeAfterDiscount,
					},
					Discount:                 checkTariff.Data.Discount,
					DiscountType:             checkTariff.Data.DiscountType,
					IsPromo:                  checkTariff.Data.IsPromo,
					IsDiscountExceedMaxPromo: checkTariff.Data.IsDiscountExceedMaxPromo,
					TotalDiscount:            checkTariff.Data.TotalDiscount,
				},
			}
		}

		if len(params.GoodsNames) > 0 {
			sttMeta.GoodsNames = params.GoodsNames
		}

		sttCreate = generateSttCreate(data, checkTariff, params, now)
		sttCreate.Stt.SttTotalAmount = c.getSttTotalAmount(clientIsDOPaymentTypeFree, checkTariff)
		sttCreate.Stt.SttCommodityID = commodity.Data.CommodityID
		sttCreate.Stt.SttBookedByType = referenceRole
		sttCreate.Stt.SttMeta = sttMeta.ToString()
		sttCreate.Stt.SttUpdatedActorRole = dbr.NewNullString(referenceRole)
		sttCreate.Stt.SttCommodityName = c.getCommodityName(commodity, cityOrigin)
		sttCreate.Stt.SttCommodityHsCode = commodity.Data.HsCode
		sttCreate.Stt.SttOriginCityName = districtOrigin.Data.City.Name
		sttCreate.Stt.SttDestinationCityName = districtDestination.Data.City.Name
		sttCreate.Stt.SttOriginDistrictName = districtOrigin.Data.Name
		sttCreate.Stt.SttOriginDistrictUrsaCode = districtOrigin.Data.UrsaCode
		sttCreate.Stt.SttDestinationDistrictName = districtDestination.Data.Name
		sttCreate.Stt.SttDestinationDistrictUrsaCode = districtDestination.Data.UrsaCode
		sttCreate.Stt.SttBookedByCode = referenceCode
		sttCreate.Stt.SttPaymentStatus = paymentStatus
		sttCreate.SttPieceHistory.HistoryActorRole = referenceRole

		signedSttWeightAttachFile, _ := c.GenerateSignedURLs(ctx, &sttCreate.Stt, strings.Join(params.SttWeightAttachFiles, ","))
		tempRemarksPieceHistory.SttWeightAttachFileSigneds = signedSttWeightAttachFile

		sttCreate.SttPieceHistory.HistoryRemark = tempRemarksPieceHistory.EncodeToString()

		switch params.Source {
		case model.MANUAL:
			if err := c.generateSttManual(selfCtx, &stt.RequestGenerateStt{
				SttCreate:           &sttCreate,
				SttData:             &data,
				SttRequest:          params,
				ReferenceName:       params.AccountRefName,
				Index:               i,
				BookedForActor:      BookedForActor,
				DestinationDistrict: districtDestination,
			}); err != nil {
				errLog = err
				return nil, err
			}

		case model.ALGO:
			if err := c.generateSttShipmentID(selfCtx, &stt.RequestGenerateStt{
				SttCreate:           &sttCreate,
				SttData:             &data,
				SttRequest:          params,
				ReferenceName:       params.AccountRefName,
				Index:               i,
				BookedForActor:      &model.Actor{},
				DestinationDistrict: districtDestination,
			}); err != nil {
				errLog = err
				return nil, err
			}

		case model.API:
			sttCreate.Stt.SttSource = model.API
			/**
			 * source API
			 */
		default:
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Invalid Source format",
				"id": "Format Source tidak benar",
			})
		}

		for _, pieces := range data.SttPieces {
			sttCreate.SttPieces = append(sttCreate.SttPieces, model.SttPiece{
				SttPieceLength:       pieces.SttPieceLength,
				SttPieceWidth:        pieces.SttPieceWidth,
				SttPieceHeight:       pieces.SttPieceHeight,
				SttPieceGrossWeight:  pieces.SttPieceGrossWeight,
				SttPieceVolumeWeight: pieces.SttPieceVolumeWeight,
			})
		}

		sttCreate.SttOptionalRate = append(sttCreate.SttOptionalRate, model.SttOptionalRate{
			SttOptionalRateName:   checkTariff.Data.InsuranceName,
			SttOptionalRateRate:   checkTariff.Data.InsuranceRates,
			SttOptionalRateParams: model.INSURANCE,
		})

		if data.SttIsWoodpacking {
			sttCreate.SttOptionalRate = append(sttCreate.SttOptionalRate, model.SttOptionalRate{
				SttOptionalRateName:   strings.Title(model.WOODPACKING),
				SttOptionalRateRate:   checkTariff.Data.WoodpackingRates,
				SttOptionalRateParams: model.WOODPACKING,
			})
		}

		sttDue, err := c.GenerateSttDue(selfCtx, &sttCreate.Stt, params.Token)
		if err != nil {
			return nil, err
		}

		sttCustomFlag := c.checkNewDfodRulesAndUpdateMetaSttCustomFlag(selfCtx, CheckDfodRulesParam{
			sttCreate:  &sttCreate.Stt,
			sttMeta:    &sttMeta,
			cityOrigin: cityOrigin,
			token:      params.Token,
		})

		sttMeta.PriorityTier = c.getProfilePriorityTier(selfCtx, params.AccountRefID, sttCreate.Stt, params.Token)
		sttCreate.Stt.SttMeta = sttMeta.ToString()
		createSttCustomFlag := []model.SttCustomFlag{}
		createSttCustomFlag = append(createSttCustomFlag, c.getSttCustomFlagNewForm(sttCreate.Stt.SttNo, params.Stt[0].IsNewForm, now))

		paramsCreateStt = append(paramsCreateStt, model.SttCreate{
			IsSttManual:           sttCreate.IsSttManual,
			ShipmentPackageID:     sttCreate.ShipmentPackageID,
			Stt:                   sttCreate.Stt,
			SttPieces:             sttCreate.SttPieces,
			SttOptionalRate:       sttCreate.SttOptionalRate,
			SttPieceHistory:       sttCreate.SttPieceHistory,
			CheckTariff:           &checkTariff.Data,
			OriginDistrict:        districtOrigin,
			DestinationDistrict:   districtDestination,
			BookedForExternalCode: bookedForExternalCode,
			BookedByExternalCode:  actorExternalCode,
			Commodity:             commodity,
			ElexysTariff:          sttCreate.ElexysTariff,
			SttManual:             sttCreate.SttManual,
			SttDue:                sttDue,
			SttCustomFlag:         sttCustomFlag,
			CreateSttCustomFlag:   createSttCustomFlag,
		})
	}

	generatedStt, err := c.sttRepo.Create(selfCtx, &model.SttCreateParams{
		SttCreate: paramsCreateStt,
		IsElexys:  c.cfg.IsElexysConfig(),
	})

	if err != nil {
		errLog = err
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to create STT Booking, Please resubmit again",
			"id": "Gagal membuat STT Booking, Tolong dikirim ulang",
		})
	}

	// publish stt assessment
	go func() {
		// count goroutine
		uuid := uuid.New().String()
		shared.IncreaseTotalGoRoutineCount(uuid)
		defer shared.DecreaseTotalGoRoutineCount(uuid)

		firstStt := paramsCreateStt[0].Stt
		isNeedAssesment, isClientNeedAssessment := c.checkAssessment(context.Background(), checkAssessmentParams{
			source:      params.Source,
			sttClientID: firstStt.SttClientID,
			token:       params.Token,
		})

		publishAssessmentParams := c.getPublishAssessmentParams(firstStt.SttShipmentID, mapShipmentAlgo, mapData)
		publishAssessmentData := publishAssessmentData{
			sttID: firstStt.SttID,
			sttNo: firstStt.SttNo,
			clientAssessment: stt.ClientAssessment{
				ClientID:               firstStt.SttClientID,
				ClientIsNeedAssessment: isClientNeedAssessment,
			},
			isNeedAssessment: isNeedAssesment,
		}
		if c.equalsInThreeDecimal(publishAssessmentParams.grossWeightBefore, publishAssessmentParams.grossWeightAfter) {
			publishAssessmentData.isNeedAssessment = false
		}
		c.publishAssessment(publishAssessmentData, publishAssessmentParams)
	}()

	var wg sync.WaitGroup
	/* publish message to calculate retail tariff */
	if sttCreate.Stt.SttBookedByType == model.POS && sttCreate.Stt.SttBookedForType == model.CLIENT {
		wg.Add(1)

		isDisablePromo = true

		payloadCalculateRetailTariff := stt.CalculateRetailTariffRequest{
			OriginID:          reqCheckTariff.RequestCalculateTariff.OriginID,
			DestinationID:     reqCheckTariff.RequestCalculateTariff.DestinationID,
			CommodityID:       reqCheckTariff.RequestCalculateTariff.CommodityID,
			ProductType:       reqCheckTariff.RequestCalculateTariff.ProductType,
			AccountType:       paramsCreateStt[0].Stt.SttBookedByType,
			AccountRefID:      paramsCreateStt[0].Stt.SttBookedBy,
			GoodsPrice:        reqCheckTariff.RequestCalculateTariff.GoodsPrice,
			CodAmount:         reqCheckTariff.RequestCalculateTariff.CodAmount,
			IsCod:             false,
			InsuranceType:     reqCheckTariff.RequestCalculateTariff.InsuranceType,
			IsWoodpacking:     reqCheckTariff.RequestCalculateTariff.IsWoodpacking,
			IsHaveTaxID:       reqCheckTariff.RequestCalculateTariff.IsHaveTaxID,
			Pieces:            reqCheckTariff.RequestCalculateTariff.Pieces,
			IsBookingPurposes: reqCheckTariff.RequestCalculateTariff.IsBookingPurposes,
			HistoryStatus:     []string{model.BKD},
			SttNo:             paramsCreateStt[0].Stt.SttNo,
			IsDisablePromo:    isDisablePromo,
		}

		go func() {
			defer wg.Done()
			c.CalculateRetailTariff(context.Background(), &payloadCalculateRetailTariff)
		}()
	}

	go func() {
		// count goroutine
		uuid := uuid.New().String()
		shared.IncreaseTotalGoRoutineCount(uuid)
		defer shared.DecreaseTotalGoRoutineCount(uuid)
		shipmentID := sttCreate.Stt.SttShipmentID
		isHoldCommision := shipmentID != "" && params.Source == model.ALGO && model.IsShipmentHoldCommission[shared.GetPrefixShipmentID(shipmentID)]
		c.gatewaySttUc.BookingCommission(context.Background(), &gateway_stt.BookingCommissionRequest{
			// total_tariff need to be deducted by cod_fee when check_tariff with is_cod true
			// when check_tariff with is_cod true, total_tariff will be added with cod_fee
			SttCreate: func(params model.SttCreate) *model.SttCreate {
				createStt := params
				shipmentID := createStt.Stt.SttShipmentID
				IsTokopediaCOD := shipmentID != `` && model.IsShipmentTokopedia[shared.GetPrefixShipmentID(shipmentID)]

				if createStt.Stt.SttIsCOD && createStt.Stt.SttCODFee > 0 &&
					(shipmentID == `` || IsTokopediaCOD || shared.GetPrefixShipmentID(shipmentID) == model.C1 || shared.GetPrefixShipmentID(shipmentID) == model.C2 || model.MappingShipmentPrefixCODCustomerAppsRetail[shared.GetPrefixShipmentID(shipmentID)] || model.IsShipmentFavorite[shared.GetPrefixShipmentID(shipmentID)]) {
					createStt.CheckTariff.TotalTariff = createStt.CheckTariff.TotalTariff - createStt.Stt.SttCODFee
				}
				createStt.CheckTariff.TotalTariff = createStt.CheckTariff.TotalTariff - createStt.CheckTariff.CODBookingDiscount
				if clientIsDOPaymentTypeFree {
					createStt.CheckTariff.TotalTariff = 0
				}
				return &createStt
			}(paramsCreateStt[0]),
			SttCreateRequest: &params.Stt[0],
			Token:            params.Token,
			AccountRefType:   referenceRole,
			AccountRefID:     params.AccountRefID,

			PartnerPosParentID:         BookedForActor.PosParentID(),
			PartnerPosBranchCommission: BookedForActor.PosBranchCommission(),

			ClientPaymentMethod:       clientPaymentMethod,
			ClientCodConfigAmount:     clientCodConfigAmount,
			ClientCodShipmentDiscount: clientCodShipmentDiscount,
			BookingReturn:             totalTariffReturn / 2,
			CodHandling:               codHandling,
			Currency:                  currency, IsHoldCommision: isHoldCommision, PartnerPosIsPickup: partnerPosIsPickup, Source: params.Source,
		})
	}()

	go c.gatewaySttUc.SttSubmit(context.Background(), &gateway_stt.SttSubmitRequest{SttCreate: &paramsCreateStt[0], PercentageCodFee: percentageCodFee, MinCodFee: minimumCodFee, Token: params.Token, CODHandling: codHandling, SttAttachFiles: params.SttWeightAttachFiles})

	// messaging service
	go func(sttID int64) { // sttID arguments here
		stt := &paramsCreateStt[0].Stt
		if stt != nil {
			c.messageGatewayUc.SendMessage(context.Background(), &model.SendMessageRequest{
				RecieverNumber:                   model.RecieverNumber{PackageSender: stt.SttSenderPhone, PackageReceiver: stt.SttRecipientPhone},
				PackageType:                      shared.GetPackageType(stt.SttIsCOD, stt.SttIsDFOD),
				EventStatus:                      model.BKD,
				Token:                            params.Token,
				IsAllowToSendLinkAdjustRecipient: c.isAllowedToRelabel(stt.SttNo),
			}, stt)
		}
	}(generatedStt[0].SttID) // Why do we need to pass this as an arguments?

	wg.Wait()
	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.saveCustomerSttManual(ctx, params, sttCreate.Stt, dataDistrictOriginDestination{districtDestination: paramsCreateStt[0].DestinationDistrict})
	})

	res.Stt = generatedStt
	c.createSttManualResponse(paramsCreateStt, &res)

	//Publish bulk stt repair
	c.publishBulkSTTRepair(ctx, paramPubBulkSTTRepair{bulkID: params.BulkID, bulkSTTRowNumber: params.BulkSTTRowNumber, sttModel: sttCreate.Stt, generatedStt: generatedStt, isSttManual: paramsCreateStt[0].IsSttManual})
	c.createSttSetResponse(selfCtx, &res, params.Token, sttCreate.Stt)
	return &res, nil
}

func validatePartnerData(partnerBooking *model.Partner, data stt.Stt, params *stt.CreateSttRequest) error {
	isPosBranchAndAllowPosCabangShipment := partnerBooking.Data.PartnerPosParentID > 0 && len(data.SttShipmentID) > 0 && !model.IsAllowPosCabangShipment[shared.GetPrefixShipmentID(data.SttShipmentID)]
	if isPosBranchAndAllowPosCabangShipment {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner POS is branch and only allow shipment id with prefix [AD/ACB/B2/T1/TKLP/ARB]",
			"id": "Partner POS adalah cabang dan shiment id hanya boleh dengan prefix [AD/ACB/B2/T1/TKLP/ARB]",
		})
	}

	if data.SttIsCOD && !partnerBooking.Data.PartnerIsCodBooking && params.Source == model.MANUAL {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"error_type": "ERROR_COD_NOT_ALLOWED",
			"en":         "Partner POS is not allowed for COD services",
			"id":         "Partner POS tidak di izinkan untuk layanan COD",
		})
	}
	return nil
}

func (c *sttCtx) getClientTariffBeforeDiscount(checkTariff *model.CheckTariffResponse) model.ClientTariffDetail {
	clientTariffBeforeDiscount := model.ClientTariffDetail{
		CityRates:               checkTariff.Data.CityRates,
		ForwardRates:            checkTariff.Data.ForwardRates,
		ShippingCost:            checkTariff.Data.ShippingCost,
		CommoditySurcharge:      checkTariff.Data.CommoditySurcharge,
		HeavyWeightSurcharge:    checkTariff.Data.HeavyWeightSurcharge,
		DocumentSurcharge:       checkTariff.Data.DocumentSurcharge,
		InsuranceRates:          checkTariff.Data.InsuranceRates,
		WoodpackingRates:        checkTariff.Data.WoodpackingRates,
		TotalTariff:             checkTariff.Data.TotalTariff,
		TaxRates:                checkTariff.Data.TaxRates,
		BMTaxRate:               checkTariff.Data.BMTaxRate,
		PPNTaxRate:              checkTariff.Data.PPNTaxRate,
		PPHTaxRate:              checkTariff.Data.PPHTaxRate,
		OriginDistrictRate:      checkTariff.Data.OriginDistrictRate,
		DestinationDistrictRate: checkTariff.Data.DestinationDistrictRate,
		PublishRate:             checkTariff.Data.PublishRate,
		ShippingSurchargeRate:   checkTariff.Data.ShippingSurchargeRate,
		CodAmount:               checkTariff.Data.CodAmount,
		CodFee:                  checkTariff.Data.CodFee,
	}
	return clientTariffBeforeDiscount
}

func (c *sttCtx) equalsInThreeDecimal(weightBefore float64, weightAfter float64) bool {
	before := int(math.Trunc(weightBefore * 1000))
	after := int(math.Trunc(weightAfter * 1000))
	return before == after
}

func (c *sttCtx) generateSttMetaPartnerPosInterpack(params *stt.Stt, sttMeta model.SttMeta, referenceRole string) model.SttMeta {
	if params.SttProductType == model.INTERPACK {
		sttMeta.SttAttachFiles = params.SttAttachFiles
		sttMeta.SttCommodityDetail = params.SttCommodityDetail
		sttMeta.SttRecipientEmail = params.SttRecipientEmail
		sttMeta.SttKtpImage = params.SttKtpImage
		sttMeta.SttTaxImage = params.SttTaxImage
		sttMeta.SttInterTaxNumber = params.SttInterTaxNumber
		sttMeta.SttIdentityNumber = params.SttIdentityNumber
		sttMeta.SttCIPL = params.SttCIPL
	}

	return sttMeta
}

func (c *sttCtx) deleteCacheNoRefExternals(ctx context.Context, params *stt.CreateSttRequest) {
	for _, data := range params.Stt {
		if data.SttNoRefExternal != "" {
			key := fmt.Sprintf("%v:%v", model.CacheRefNoExternal, data.SttNoRefExternal)
			c.sttRepo.DeleteCacheOnce(ctx, key)
		}
	}
}
