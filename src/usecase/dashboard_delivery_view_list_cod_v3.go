package usecase

import (
	"context"
	"strings"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/repository"
	dashboardV2 "github.com/Lionparcel/hydra/src/usecase/dashboard_v2"
	dashboardV3 "github.com/Lionparcel/hydra/src/usecase/dashboard_v3"
)

func (c *dashboardDeliveryCtx) ViewListCODDashboardV3(ctx context.Context, params *dashboardV2.ViewDashboardCODListRequest) (*shared.Pagination, error) {
	opName := "dashboardDeliveryCtx-ViewListCODDashboardV3"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var (
		errLog           error
		res              = &shared.Pagination{}
		detailDashboards = []dashboardV3.DetailDashboardCODListV3Response{}
		sttDeliveries    = []model.DeliveryWithCodAmount{}
		err              error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "err": shared.CheckErrorNil(errLog)})
	}()

	if err = params.Validate(); err != nil {
		errLog = err
		return nil, err
	}

	partnerData, err := c.getPartnerByIDDashboard(selfCtx, params.PartnerID, params.Token)
	if err != nil {
		errLog = err
		return nil, err
	}

	dataCourier, err := c.getAllCourierFilterViewListCODDashboardV3(selfCtx, params, partnerData)
	if err != nil {
		errLog = err
		return nil, err
	}

	res = &shared.Pagination{
		Data: &detailDashboards,
		Meta: shared.Meta{
			Page:  params.Page,
			Limit: params.Limit,
		},
	}
	sttDeliveries, err = c.deliveryRepo.SelectDeliveryDashboardGroupingWithoutStt(selfCtx, &model.DeliveryCodDashboardGroupingParams{
		IsNeedDriverName:       true,
		DeliveryPartnerID:      params.PartnerID,
		DeliveryCreatedAtStart: &params.CustomStartDate,
		DeliveryCreatedAtEnd:   &params.CustomEndDate,
		NotInDelTransferTaks:   true,
		WithoutPagination:      true,
	})
	if err != nil {
		errLog = err
		return nil, shared.ERR_UNEXPECTED_DB
	}
	if lenCourier := len(dataCourier.Data); lenCourier == 0 {
		return res, nil
	}
	dataSttDeliveries := c.mappingDataSttDeliveryDashboard(sttDeliveries, dataCourier.Data)
	params.IsNeedCountPendingRec = true

	detailDashboards, err = c.generateDetailDashboardV3(selfCtx, dataSttDeliveries, params, partnerData)
	if err != nil {
		errLog = err
		return res, err
	}
	return res, errLog
}

func (c *dashboardDeliveryCtx) isValidMapCityDetailDashboardV3(specificDelivery model.DeliveryWithCodAmount, params *dashboardV2.ViewDashboardCODListRequest) bool {
	if params.DestinationCityIDInCommas != `` {
		var mapCityIDs = make(map[string]bool)
		cityIDs := strings.Split(params.DestinationCityIDInCommas, ",")
		for _, v := range cityIDs {
			mapCityIDs[v] = true
		}

		if !mapCityIDs[specificDelivery.LoadRemark().SttDestinationCityID] {
			return false
		}
	}
	return true
}

func (c *dashboardDeliveryCtx) updateDataDetailDashboardV3(specificDeliveries []model.DeliveryWithCodAmount, params *dashboardV2.ViewDashboardCODListRequest, detailDashboard *dashboardV3.DetailDashboardCODListV3Response) {
	for _, specificDelivery := range specificDeliveries {
		// validate for dashboard reconcile driver
		isPodNotCod := (specificDelivery.FinishedStatus != nil && *specificDelivery.FinishedStatus == model.POD) && !specificDelivery.LoadRemark().SttIsCOD
		if len(params.DriverPhone) > 0 && isPodNotCod {
			continue
		}

		if isValid := c.isValidMapCityDetailDashboardV3(specificDelivery, params); !isValid {
			continue
		}

		c.updateDetailDashboardV3(detailDashboard, specificDelivery)
	}
}

func (c *dashboardDeliveryCtx) generateDetailDashboardV3(ctx context.Context, sttDeliveries []model.DeliveryWithCodAmount, params *dashboardV2.ViewDashboardCODListRequest, partnerData *model.Partner) ([]dashboardV3.DetailDashboardCODListV3Response, error) {
	var detailDashboards []dashboardV3.DetailDashboardCODListV3Response
	dataChunks := c.chunkSliceSttDeliveries(sttDeliveries, 100)
	for _, chunk := range dataChunks {
		var driverPhones []string
		mapSttDelByPhone := map[string]dashboardV3.DetailDashboardCODListV3Response{}
		for _, val := range chunk {
			driverPhone := shared.CleanPhoneNumberRegex.ReplaceAllString(val.DriverPhone, "0")
			mapSttDelByPhone[driverPhone] = dashboardV3.DetailDashboardCODListV3Response{
				DeliveryDriverID:            int(val.DriverID.Int64),
				DeliveryDriverPhone:         val.DriverPhone,
				DeliveryDriverName:          val.DriverName,
				DeliveryPartnerID:           partnerData.Data.ID,
				DeliveryPartnerName:         partnerData.Data.Name,
				DeliveryPartnerPhone:        partnerData.Data.PhoneNumber,
				DeliveryPartnerContactPhone: partnerData.Data.PartnerContactPhoneNumbers,
				STTDeposit:                  make([]dashboardV3.STTDepositResponse, 0),
				STTInDelivery:               make([]dashboardV3.STTInDeliveryResponse, 0),
			}
			if val.PartnerID > 0 {
				driverPhones = append(driverPhones, driverPhone)
			}
		}
		// get data specific deliveries by driver phones
		specificDeliveries, err := c.deliveryRepo.SelectDeliveryDashboardWithSttPiece(ctx, &model.DeliveryCodDashboardParams{
			DeliveryPartnerID:          partnerData.Data.ID,
			WhereInDeliveryDriverPhone: driverPhones,
			DeliveryCreatedDateFrom:    params.CustomStartDate,
			DeliveryCreatedDateTo:      params.CustomEndDate,
			NotInDelTransferTaks:       true,
		})
		if err != nil {
			return nil, err
		}
		detailDashboardsBuild, err := c.generateDetailDashboardV3Build(ctx, params, mapSttDelByPhone, specificDeliveries)
		if err != nil {
			return nil, err
		}
		detailDashboards = append(detailDashboards, detailDashboardsBuild...)
	}
	return detailDashboards, nil
}

func (c *dashboardDeliveryCtx) updateDetailDashboardV3(detailDashboard *dashboardV3.DetailDashboardCODListV3Response, delivery model.DeliveryWithCodAmount) {
	detailDashboard.TotalSTT++

	switch {
	case delivery.FinishedStatus == nil:
		detailDashboard.TotalSTTDEL++
		detailDashboard.TotalSTTDELPieces += int(delivery.SttTotalPiece.Int64)
	case *delivery.FinishedStatus == model.POD:
		detailDashboard.TotalSTTPOD++
		detailDashboard.TotalSTTPODPieces += int(delivery.SttTotalPiece.Int64)
		c.updatePODDetailsV3(detailDashboard, delivery)
	case *delivery.FinishedStatus == model.DEX:
		c.updateDetailsGeneric(detailDashboard, delivery, model.DEX)
	case *delivery.FinishedStatus == model.CODREJ:
		c.updateDetailsGeneric(detailDashboard, delivery, model.CODREJ)
	}
}

func (c *dashboardDeliveryCtx) handleUpdatedAtIsNotNilUpdatePODDetailsV3(detailDashboard *dashboardV3.DetailDashboardCODListV3Response, delivery model.DeliveryWithCodAmount) {
	updatedAtIsNotNil := delivery.UpdatedAt != nil
	createdAtAndUpdatedAtEqual := shared.StartDate(delivery.CreatedAt).Equal(shared.StartDate(*delivery.UpdatedAt))
	if updatedAtIsNotNil && createdAtAndUpdatedAtEqual {
		detailDashboard.TotalSTTPODSameDay++
	}
	if updatedAtIsNotNil && !createdAtAndUpdatedAtEqual {
		detailDashboard.TotalSTTPODDifferentDay++
	}
}

func (c *dashboardDeliveryCtx) updatePODDetailsV3(detailDashboard *dashboardV3.DetailDashboardCODListV3Response, delivery model.DeliveryWithCodAmount) {
	c.handleUpdatedAtIsNotNilUpdatePODDetailsV3(detailDashboard, delivery)

	deliveryIsCollected := delivery.LoadRemark().DeliveryIsCollected
	qrisPaymentMethod := delivery.LoadRemark().PaymentMethod == model.QRIS
	freePaymentMethod := delivery.LoadRemark().PaymentMethod == model.PaymentMethodFree

	detailDashboard.UpdateDetailDeliveryQrisFree(delivery)

	if deliveryIsCollected && !freePaymentMethod {
		totalCollected := delivery.LoadRemark().SttCODAmount
		detailDashboard.TotalCollected += totalCollected
		detailDashboard.CODAmountDeposit.Collected += totalCollected
	}
	if !deliveryIsCollected && !qrisPaymentMethod {
		totalOutstanding := delivery.LoadRemark().SttCODAmount
		detailDashboard.TotalOutstanding += totalOutstanding
		detailDashboard.CODAmountDeposit.Pending += totalOutstanding
	}
}

func (c *dashboardDeliveryCtx) handleHavePendingViewListCODDashboard(ctx context.Context, params *dashboardV2.ViewDashboardCODListRequest, sttDelivery model.DeliveryWithCodAmount, detailDashboard *dashboardV3.DetailDashboardCODListV3Response) error {
	var (
		startDateHavePending time.Time
		endDateHavePending   = params.CustomEndDate
	)
	if !params.IsNeedCountPendingRec {
		return nil
	}
	startPendingDate, _ := shared.ParseUTC7(shared.FormatDateTime, c.timeRepo.Now(time.Now()).Format(shared.FormatDate))
	startDateHavePending = shared.StartDate(startPendingDate.AddDate(0, 0, -1))
	endDateHavePending = shared.EndDate(startPendingDate.AddDate(0, 0, -1))

	// check is have pending reconcile
	if c.cfg.ReconcileDateRelease().Before(endDateHavePending) {
		startDateHavePending = c.getConfigRangeTimeHavePendingViewListCODDashboard(startPendingDate)
		totalPending, err := c.deliveryRepo.CountDriverPendingReconcile(ctx, &model.CountDriverPendingReconcileParams{
			StartDate:   startDateHavePending,
			EndDate:     endDateHavePending,
			DriverPhone: sttDelivery.DriverPhone,
			PartnerID:   detailDashboard.DeliveryPartnerID,
		})
		if err != nil {
			return err
		}
		if totalPending > 0 && params.CustomEndDate.After(startDateHavePending) {
			detailDashboard.IsHavePendingReconcile = true
		}
	}
	return nil
}
func (c *dashboardDeliveryCtx) handleSTTViewListCODDashboard(detailDashboard *dashboardV3.DetailDashboardCODListV3Response) {
	// stt deposit dex collected and outstanding
	detailDashboard.STTDeposit = append(detailDashboard.STTDeposit, dashboardV3.STTDepositResponse{
		Status:          strings.ToLower(model.DEX),
		Collected:       detailDashboard.TotalSTTDEXCollected,
		CollectedPieces: detailDashboard.TotalSTTDEXCollectedPieces,
		Pending:         detailDashboard.TotalSTTDEXOutstanding,
		PendingPieces:   detailDashboard.TotalSTTDEXOutstandingPieces,
	})
	// stt deposit codrej collected and outstanding
	detailDashboard.STTDeposit = append(detailDashboard.STTDeposit, dashboardV3.STTDepositResponse{
		Status:          strings.ToLower(model.CODREJ),
		Collected:       detailDashboard.TotalSTTCODREJCollected,
		CollectedPieces: detailDashboard.TotalSTTCODREJCollectedPieces,
		Pending:         detailDashboard.TotalSTTCODREJOutstanding,
		PendingPieces:   detailDashboard.TotalSTTCODREJOutstandingPieces,
	})
	// stt in delivery status DEL
	detailDashboard.STTInDelivery = append(detailDashboard.STTInDelivery, dashboardV3.STTInDeliveryResponse{
		Status: strings.ToLower(model.DEL),
		Count:  detailDashboard.TotalSTTDEL,
		Pieces: detailDashboard.TotalSTTDELPieces,
	})
	// stt in delivery status POD
	detailDashboard.STTInDelivery = append(detailDashboard.STTInDelivery, dashboardV3.STTInDeliveryResponse{
		Status: strings.ToLower(model.POD),
		Count:  detailDashboard.TotalSTTPOD,
		Pieces: detailDashboard.TotalSTTPODPieces,
	})
	// stt in delivery status DEX
	detailDashboard.STTInDelivery = append(detailDashboard.STTInDelivery, dashboardV3.STTInDeliveryResponse{
		Status: strings.ToLower(model.DEX),
		Count:  detailDashboard.TotalSTTDEXOutstanding + detailDashboard.TotalSTTDEXCollected,
		Pieces: detailDashboard.TotalSTTDEXOutstandingPieces + detailDashboard.TotalSTTDEXCollectedPieces,
	})
	// stt in delivery status CODREJ
	detailDashboard.STTInDelivery = append(detailDashboard.STTInDelivery, dashboardV3.STTInDeliveryResponse{
		Status: strings.ToLower(model.CODREJ),
		Count:  detailDashboard.TotalSTTCODREJCollected + detailDashboard.TotalSTTCODREJOutstanding,
		Pieces: detailDashboard.TotalSTTCODREJCollectedPieces + detailDashboard.TotalSTTCODREJOutstandingPieces,
	})
}

func (c *dashboardDeliveryCtx) getPartnerIDs(ctx context.Context, params *dashboardV2.ViewDashboardCODListRequest) (partnerIds []int, err error) {
	if params.IsAllowSearchPartnerName {
		partner, err := c.partnerRepo.SelectWithLimit(ctx, model.PartnerWithParams{Search: params.Search, Limit: 200}, params.Token)
		if err != nil {
			return nil, shared.ERR_UNEXPECTED_DB
		}
		for _, p := range partner.Data {
			partnerIds = append(partnerIds, p.ID)
		}
	}
	return
}

func (c *dashboardDeliveryCtx) getConfigRangeTimeHavePendingViewListCODDashboard(date time.Time) time.Time {
	startDate := date.AddDate(0, 0, -c.cfg.PendingReconcileMaxPeriodeLookup())
	if c.cfg.ReconcileDateRelease().After(startDate) {
		startDate = c.cfg.ReconcileDateRelease()
	}

	return shared.StartDate(startDate)
}

func (c *dashboardDeliveryCtx) getAllCourierFilterViewListCODDashboardV3(ctx context.Context, params *dashboardV2.ViewDashboardCODListRequest, partnerData *model.Partner) (*repository.GetAllCourierFilterResp, error) {
	dataCourier, err := c.algoRepo.GetAllCourierFilterWithTypeExclude(ctx, repository.GetAllCourierFilterParam{
		Keyword:            params.Search,
		Origin:             partnerData.Data.PartnerExternalCode,
		Status:             "ACTIVE",
		ExcludeCourierType: "TRUCK",
	})
	if err != nil || dataCourier == nil {
		return nil, err
	}
	return dataCourier, nil
}

func (c *dashboardDeliveryCtx) chunkSliceSttDeliveries(slice []model.DeliveryWithCodAmount, chunkSize int) [][]model.DeliveryWithCodAmount {
	var chunks [][]model.DeliveryWithCodAmount
	for i := 0; i < len(slice); i += chunkSize {
		end := i + chunkSize
		if end > len(slice) {
			end = len(slice)
		}
		chunks = append(chunks, slice[i:end])
	}
	return chunks
}

func (c *dashboardDeliveryCtx) generateDetailDashboardV3Build(ctx context.Context, params *dashboardV2.ViewDashboardCODListRequest,
	mapSttDelByPhone map[string]dashboardV3.DetailDashboardCODListV3Response,
	specificDeliveries []model.DeliveryWithCodAmount) ([]dashboardV3.DetailDashboardCODListV3Response, error) {

	var detailDashboards = make([]dashboardV3.DetailDashboardCODListV3Response, 0)
	mapSpecificDelByPhone := map[string][]model.DeliveryWithCodAmount{}
	for _, delivery := range specificDeliveries {
		mapSpecificDelByPhone[delivery.DriverPhone] = append(mapSpecificDelByPhone[delivery.DriverPhone], delivery)
	}
	for driverPhone, detailDashboard := range mapSttDelByPhone {
		valSpecificDeliveries, ok := mapSpecificDelByPhone[driverPhone]
		if ok {
			c.updateDataDetailDashboardV3(valSpecificDeliveries, params, &detailDashboard)

			// set STT deposit and STT in delivery
			c.handleSTTViewListCODDashboard(&detailDashboard)
		}
		// check is have pending reconcile
		if err := c.handleHavePendingViewListCODDashboard(ctx, params, model.DeliveryWithCodAmount{
			DriverPhone: driverPhone,
		}, &detailDashboard); err != nil {
			return nil, err
		}

		detailDashboards = append(detailDashboards, detailDashboard)
	}
	return detailDashboards, nil
}
