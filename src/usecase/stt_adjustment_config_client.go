package usecase

import (
	"context"

	"github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *sttCtx) configSttClient(ctx context.Context, data checkSttPrefix) *stt.SttAdjustmentConfigResponse {
	if !data.isSttClient {
		return &stt.SttAdjustmentConfigResponse{}
	}

	currentStatus := data.dataStt.SttLastStatusID
	isAfterPup := c.isAfterStatusPup(data.sttHistories, currentStatus)

	res := &stt.SttAdjustmentConfigResponse{
		Config: c.buildSttConfigClient(isAfterPup, data),
	}

	c.checkInterpackDocumentInternational(ctx, data, res)
	c.populateRespInterpackAndFtzPos(res, data.cityOrigin.FreeTradeZone)
	return res
}

func (c *sttCtx) buildSttConfigClient(isAfterPup bool, data checkSttPrefix) stt.Config {
	rulesCod := stt.EditableConfig
	if data.dataStt.SttIsCOD || data.dataStt.SttIsDFOD {
		rulesCod = stt.NonEditableConfig
	}
	return stt.Config{
		Client:              newFieldRule(true, stt.NonEditableConfig),
		PaymentMethod:       newFieldRule(true, stt.NonEditableConfig),
		ShipmentID:          newFieldRule(true, stt.NonEditableConfig),
		ManualSttNo:         newFieldRule(false, stt.NonEditableConfig),
		SttNoRefExternal:    newFieldRule(false, stt.NonEditableConfig),
		SenderName:          newFieldRule(true, c.adjustEditableRule(isAfterPup, stt.EditableConfig)),
		SenderPhone:         newFieldRule(true, c.adjustEditableRule(isAfterPup, stt.EditableConfig)),
		SenderAddress:       newFieldRule(true, c.adjustEditableRule(isAfterPup, stt.EditableConfig)),
		SenderDistrict:      newFieldRule(true, stt.NonEditableConfig),
		SenderPostalCode:    newFieldRule(false, stt.NonEditableConfig),
		SenderSave:          newFieldRule(false, stt.NonEditableConfig),
		RecipientName:       newFieldRule(true, c.adjustEditableRule(isAfterPup, stt.EditableConfig)),
		RecipientPhone:      newFieldRule(true, c.adjustEditableRule(isAfterPup, stt.EditableConfig)),
		RecipientAddress:    newFieldRule(true, c.adjustEditableRule(isAfterPup, stt.EditableConfig)),
		RecipientDistrict:   newFieldRule(true, c.adjustEditableRule(isAfterPup, rulesCod)),
		RecipientPostalCode: newFieldRule(false, c.adjustEditableRule(isAfterPup, rulesCod)),
		RecipientSave:       newFieldRule(false, stt.NonEditableConfig),
		Commodity:           newFieldRule(true, stt.NonEditableConfig),
		ProductType:         newFieldRule(true, stt.NonEditableConfig),
		GoodsPrice:          newFieldRule(true, stt.NonEditableConfig),
		Insurance:           newFieldRule(false, stt.NonEditableConfig),
		CODAmount:           newFieldRule(false, stt.NonEditableConfig),
		GrossWeight:         newFieldRule(true, c.adjustEditableRule(isAfterPup, stt.EditableConfig)),
		Dimension:           newFieldRule(true, c.adjustEditableRule(isAfterPup, stt.EditableConfig)),
		ChargeableWeight:    newFieldRule(false, stt.NonEditableConfig),
		AdjustPiece:         newFieldRule(false, stt.NoShowConfig),
		ServiceType:         newFieldRule(true, stt.NonEditableConfig),
		Tariff:              newFieldRule(true, stt.ShowConfig),
	}
}
