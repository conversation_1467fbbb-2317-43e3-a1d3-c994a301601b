package stt

import (
	"context"
	"fmt"
	"math"
	"regexp"
	"strings"
	"time"

	validation "github.com/go-ozzo/ozzo-validation/v4"
	"github.com/go-ozzo/ozzo-validation/v4/is"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
)

// UpdateSttRequest ...
type UpdateSttRequest struct {
	AccountName                 string
	AccountType                 string
	AccountRefName              string
	AccountRefType              string
	AccountID                   int64
	AccountRefID                int
	Source                      string
	SttID                       int64    `json:"stt_id"`
	SttIsWoodpacking            bool     `json:"stt_is_woodpacking"`
	SttTaxNumber                string   `json:"stt_tax_number"`
	SttNoRefExternal            string   `json:"stt_no_ref_external"`
	SttGoodsEstimatePrice       float64  `json:"stt_goods_estimate_price"`
	SttGoodsStatus              string   `json:"stt_goods_status"`
	SttTotalAmount              float64  `json:"stt_total_amount"`
	SttDestinationCityID        string   `json:"stt_destination_city_id"`
	SttDestinationDistrictID    string   `json:"stt_destination_district_id"`
	SttSenderName               string   `json:"stt_sender_name"`
	SttSenderPhone              string   `json:"stt_sender_phone"`
	SttSenderAddress            string   `json:"stt_sender_address"`
	SttRecipientName            string   `json:"stt_recipient_name"`
	SttRecipientAddress         string   `json:"stt_recipient_address"`
	SttRecipientAddressType     string   `json:"stt_recipient_address_type"`
	SttRecipientPhone           string   `json:"stt_recipient_phone"`
	SttProductType              string   `json:"stt_product_type"`
	SttWeightAttachFiles        []string `json:"stt_weight_attach_files"`
	SttOriginDistrictRate       float64
	SttDestinationDistrictRate  float64
	SttPublishRate              float64
	SttShippingSurchargeRate    float64
	SttDocumentSurchargeRate    float64
	SttHeavyweightSurchargeRate float64
	SttCommoditySurchargeRate   float64
	SttTaxRate                  float64
	SttGrossWeight              float64
	SttVolumeWeight             float64
	SttChargeableWeight         float64
	SttCommodityCode            string `json:"stt_commodity_code"`
	SttCODAmount                float64
	SttCODFee                   float64
	SttIsDO                     bool
	SttUpdatedAt                time.Time
	SttUpdatedBy                int
	IsMixpack                   bool
	SttPieces                   []model.SttPiece `json:"stt_pieces" faker:"-"`
	SttNextCommodity            string           `json:"stt_next_commodity"`
	SttPiecePerPack             int              `json:"stt_piece_per_pack"`
	SttInsuranceType            string           `json:"stt_insurance_type"`
	SttPostalCodeDestination    string           `json:"postal_code_destination"`
	SttAttachFiles              []string         `json:"stt_attach_files"`
	SttCommodityDetail          string           `json:"stt_commodity_detail"`
	SttShipmentID               string
	DiscountFavoritePercentage  float64 `json:"discount_favorite_percentage"`
	SttBookedbyCountry          string
	Token                       string
	HubID                       int             `json:"hub_id"`
	HubName                     string          `json:"hub_name"`
	HubOriginCity               string          `json:"hub_origin_city"`
	HubDistrictCode             string          `json:"hub_district_code"`
	SttRecipientEmail           string          `json:"stt_recipient_email"`
	IsRelabelFromExternalSource bool            `json:"is_relabel_from_external_source"`
	SttKtpImage                 string          `json:"stt_ktp_image"`
	SttTaxImage                 string          `json:"stt_tax_image"`
	SttIdentityNumber           string          `json:"stt_identity_number"`
	SttInterTaxNumber           string          `json:"stt_inter_tax_number"`
	SttCustomFlag               []SttCustomFlag `json:"stt_custom_flag"`

	SttPiecesIDNeedToBeArchieved []int64 `json:"-" faker:"-"`
	AccountRoleName              string
}

type SttCustomFlag struct {
	SCFKey   string `json:"scf_key"`
	SCFValue string `json:"scf_value"`
}

// UpdateSttResponse ...
type UpdateSttResponse struct {
	SttID               int     `json:"stt_id"`
	IsDiscount          bool    `json:"is_discount"`
	TotalDiscount       float64 `json:"total_discount"`
	TotalAfterDiscount  float64 `json:"total_after_discount"`
	TotalBeforeDiscount float64 `json:"total_before_discount"`
}

func (c *UpdateSttRequest) Validate() error {

	c.SttTaxNumber = strings.TrimSpace(c.SttTaxNumber)

	if err := validation.Validate(c.SttTaxNumber, validation.Required); err == nil {
		errTaxInvalid := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Invalid Tax Number format",
			"id": "Format Nomor Pajak tidak benar",
		})

		if c.SttBookedbyCountry != model.CountryMY {
			taxNumber := strings.ReplaceAll(strings.ReplaceAll(c.SttTaxNumber, ".", ""), "-", "")
			if err := validation.Validate(taxNumber, is.Digit); err != nil {
				return errTaxInvalid
			}

			if err := validation.Validate(len(taxNumber), validation.Max(16), validation.Min(15)); err != nil {
				return errTaxInvalid
			}
		}
	}

	c.SttGoodsStatus = strings.ReplaceAll(strings.TrimSpace(c.SttGoodsStatus), " ", "")
	if _, ok := model.IsGoodsStatusValid[c.SttGoodsStatus]; !ok && c.SttGoodsStatus != `` {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Invalid Goods Status format",
			"id": "Format Goods Status tidak benar",
		})
	}

	c.SttSenderName = strings.TrimSpace(c.SttSenderName)
	c.SttSenderAddress = strings.TrimSpace(c.SttSenderAddress)
	c.SttSenderPhone = shared.FormatPhonenumberIndonesia(strings.TrimSpace(c.SttSenderPhone))
	if err := ValidateCustomerData(c.SttSenderName, c.SttSenderPhone, c.SttSenderAddress, "Sender"); err != nil {
		return err
	}

	c.SttRecipientName = strings.TrimSpace(c.SttRecipientName)
	c.SttRecipientAddress = strings.TrimSpace(c.SttRecipientAddress)
	c.SttRecipientPhone = shared.FormatPhonenumberIndonesia(strings.TrimSpace(c.SttRecipientPhone))
	if err := ValidateCustomerData(c.SttRecipientName, c.SttRecipientPhone, c.SttRecipientAddress, "Recipient"); err != nil {
		return err
	}

	if c.SttRecipientAddressType != "" {
		c.SttRecipientAddressType = strings.ToLower(c.SttRecipientAddressType)
		if !model.ValidRecipientAddressType[c.SttRecipientAddressType] {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Invalid Stt Recipient Address Type",
				"id": "Stt Recipient Address Type Tidak Benar",
			})
		}
	}

	errCodeEmpty := func(source string, types string) error {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("%s %s Code must be filled", source, types),
			"id": fmt.Sprintf("Kode %s %s harus di isi", types, source),
		})
	}

	errCodeFormat := func(source string, types string) error {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("%s %s Code must be in valid format", source, types),
			"id": fmt.Sprintf("Kode %s %s harus dalam format yang benar", types, source),
		})
	}

	c.SttDestinationCityID = strings.ToUpper(strings.ReplaceAll(strings.TrimSpace(c.SttDestinationCityID), " ", ""))
	if err := validation.Validate(c.SttDestinationCityID, validation.Required); err != nil {
		return errCodeEmpty("Destination", "City")
	}

	if err := validation.Validate(c.SttDestinationCityID, validation.Length(3, 3), is.Alpha); err != nil {
		return errCodeFormat("Destination", "City")
	}

	c.SttDestinationDistrictID = strings.ToUpper(strings.ReplaceAll(strings.TrimSpace(c.SttDestinationDistrictID), " ", ""))
	if err := validation.Validate(c.SttDestinationDistrictID, validation.Required); err != nil {
		return errCodeEmpty("Destination", "District")
	}

	c.SttCommodityCode = strings.TrimSpace(c.SttCommodityCode)
	if err := validation.Validate(c.SttCommodityCode, validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Commodity Code must be filled",
			"id": "Commodity Code harus di isi",
		})
	}

	c.SttProductType = strings.ReplaceAll(strings.TrimSpace(strings.ToUpper(c.SttProductType)), " ", "")
	if err := validation.Validate(c.SttProductType, validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Product Type must be filled",
			"id": "Product Type harus di isi",
		})
	}

	if c.SttPostalCodeDestination != `` {
		/* validation postal code */
		isMatch, err := regexp.MatchString(`^[A-Z0-9-,]*$`, c.SttPostalCodeDestination)
		if err != nil || !isMatch {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid Postal Code",
				"id": "Postal Code Tidak Valid",
			})
		}

		// set default first zip code if the input is multiple
		arrPostalCodeDestination := strings.Split(c.SttPostalCodeDestination, ",")
		if len(arrPostalCodeDestination) > 1 {
			c.SttPostalCodeDestination = strings.TrimSpace(arrPostalCodeDestination[0])
		}
	}

	if err := validation.Validate(len(c.SttPieces), validation.Required, validation.Min(1), validation.Max(15)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Pieces/Koli min 1 and max 15",
			"id": "Minimal 1 dan Maksimum 15 Pieces/Koli",
		})
	}

	for i, piece := range c.SttPieces {

		if err := validateWeightSttPieces(piece); err != nil {
			return err
		}

		if err := validation.Validate(piece.SttPieceHeight, validation.Required, validation.Min(0.1)); err != nil {
			c.SttPieces[i].SttPieceHeight = shared.HeightDefault
		}

		if err := validation.Validate(piece.SttPieceWidth, validation.Required, validation.Min(0.1)); err != nil {
			c.SttPieces[i].SttPieceWidth = shared.WidthDefault
		}

		if err := validation.Validate(piece.SttPieceLength, validation.Required, validation.Min(0.1)); err != nil {
			c.SttPieces[i].SttPieceLength = shared.LenghtDefault
		}

		volumeWeight := shared.CalculateVolumeWeight(c.SttPieces[i].SttPieceLength, c.SttPieces[i].SttPieceWidth, c.SttPieces[i].SttPieceHeight, c.SttProductType)

		if err := validation.Validate(volumeWeight, validation.Required, validation.Min(model.MIN_WEIGHT_NEW), validation.Max(model.MAX_VOLUME_WEIGHT_PER_PIECE)); err != nil {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": fmt.Sprintf("Pieces Volume Weight must be higher than 0 and lower than %.f", model.MAX_VOLUME_WEIGHT_PER_PIECE),
				"id": fmt.Sprintf("Berat Volume Pieces harus lebih besar dari 0 dan kurang dari  %.f", model.MAX_VOLUME_WEIGHT_PER_PIECE),
			})
		}

		c.SttVolumeWeight += volumeWeight
		c.SttGrossWeight += piece.SttPieceGrossWeight
		c.SttPieces[i].SttPieceVolumeWeight = volumeWeight
	}

	if err := validation.Validate(c.SttVolumeWeight, validation.Required, validation.Min(model.MIN_WEIGHT_NEW), validation.Max(400.0)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Total Pieces Volume Weight must be higher than 0 and lower than 400",
			"id": "Total Berat Volume Pieces harus lebih besar dari 0 dan kurang dari 400",
		})
	}

	if err := validation.Validate(c.SttGrossWeight, validation.Required, validation.Min(model.MIN_WEIGHT_NEW), validation.Max(model.MAX_TOTAL_GROSS_WEIGHT)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("Total Pieces Gross Weight must be higher than 0 and lower than %.f", model.MAX_TOTAL_GROSS_WEIGHT),
			"id": fmt.Sprintf("Total Berat Kotor Pieces harus lebih besar dari 0 dan kurang dari %.f", model.MAX_TOTAL_GROSS_WEIGHT),
		})
	}

	if err := validateIdentityNumbers(c.SttIdentityNumber); err != nil {
		return err
	}

	if err := validateInterTaxNumber(c.SttInterTaxNumber); err != nil {
		return err
	}

	c.CalculateWeight()

	return nil
}

func validateWeightSttPieces(piece model.SttPiece) error {
	if err := validation.Validate(piece.SttPieceGrossWeight, validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Pieces Gross Weight is required to be filled",
			"id": "Berat Kotor Pieces harus di isi",
		})
	}

	if err := validation.Validate(piece.SttPieceGrossWeight, validation.Min(model.MIN_WEIGHT_NEW), validation.Max(model.MAX_GROSS_WEIGHT_PER_PIECE)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("Pieces Gross Weight must be higher than 0 and lower than %.f", model.MAX_GROSS_WEIGHT_PER_PIECE),
			"id": fmt.Sprintf("Berat Kotor Pieces harus lebih besar dari 0 dan kurang dari %.f", model.MAX_GROSS_WEIGHT_PER_PIECE),
		})
	}

	return nil
}

func validateInterTaxNumber(interTaxNumber string) error {
	if interTaxNumber == "" {
		return nil
	}
	if err := validation.Validate(interTaxNumber, validation.Match(shared.AlphaNumericRegex)); err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "The Tax ID can only contain numeric digits and alphabet",
			"id": "Nomor registrasi pajak hanya dapat diisi oleh angka dan huruf",
		})
	}
	if err := validation.Validate(len(interTaxNumber), validation.Max(model.MAX_LENGTH_ID_NO)); err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "The maximum length of the Tax ID is 100 characters",
			"id": "Maksimum panjang nomor registrasi pajak adalah 100 karakter",
		})
	}
	return nil
}

func validateIdentityNumbers(identityNumber string) error {
	if identityNumber == "" {
		return nil
	}
	if err := validation.Validate(identityNumber, validation.Match(shared.AlphaNumericRegex)); err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "The Identity number can only contain numeric digits and alphabet",
			"id": "Nomor identitas hanya dapat diisi oleh angka dan huruf",
		})
	}

	if err := validation.Validate(len(identityNumber), validation.Max(model.MAX_LENGTH_ID_NO)); err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "The maximum length of the identification number is 100 characters",
			"id": "Maksimum panjang nomor identitas adalah 100 karakter",
		})
	}
	return nil
}

// CalculateWeight ...
func (c *UpdateSttRequest) CalculateWeight() {

	c.SttVolumeWeight = shared.RoundWeight(c.SttVolumeWeight, c.SttProductType)
	c.SttGrossWeight = shared.RoundWeight(c.SttGrossWeight, c.SttProductType)

	c.SttChargeableWeight = c.SttVolumeWeight
	if c.SttGrossWeight > c.SttVolumeWeight {
		c.SttChargeableWeight = c.SttGrossWeight
	}
}

func (c *UpdateSttRequest) ValidateSttReverseJourney(currentStts []model.SttDetailResult) *UpdateSttRequest {

	currentStt := currentStts[0]
	c.SttTaxNumber = currentStt.SttTaxNumber
	c.SttGoodsEstimatePrice = currentStt.SttGoodsEstimatePrice
	c.SttGoodsStatus = currentStt.SttGoodsStatus
	c.SttTotalAmount = currentStt.SttTotalAmount
	c.SttDestinationCityID = currentStt.SttDestinationCityID
	c.SttDestinationDistrictID = currentStt.SttDestinationDistrictID
	c.SttProductType = currentStt.SttProductType
	c.SttCommodityCode = currentStt.SttCommodityCode
	c.SttNextCommodity = currentStt.SttNextCommodity
	c.SttPiecePerPack = currentStt.SttPiecePerPack
	c.SttInsuranceType = currentStt.SttInsuranceType

	sttPieces := []model.SttPiece{}

	for _, stt := range currentStts {
		sttPieces = append(sttPieces, stt.SttPiece)
	}

	c.SttPieces = sttPieces

	return c
}

func (c *UpdateSttRequest) GetGrossWeightUpdate() float64 {
	totalPieces := len(c.SttPieces)
	var totalGross float64

	if totalPieces == 0 {
		return totalGross
	}
	for _, piece := range c.SttPieces {
		totalGross += piece.SttPieceGrossWeight
	}
	return math.Ceil(totalGross*100) / 100
}

type UpdateSttParams struct {
	SttReverse                     model.Stt
	SttMetaReverse                 model.SttMeta
	SttStatusAfterAdjusted         string
	SttPieceHistory                []model.SttPieceHistory
	Commodity                      model.Commodity
	PartnerPosIsPickup             bool
	BookedByActor                  model.Actor
	CityOrigin                     model.City
	DistrictOrigin                 model.District
	CityDestination                model.City
	DistrictDestination            model.District
	CalculateTarifPieceArray       []CalculateTariffPieces
	SttPieceHistoryAdjustmentArray []model.SttPieceHistoryAdjustment
	ReqCheckTariff                 CalculateTariffParams
	IsHaveTaxNumber                bool
	IsDisablePromo                 bool
	SttProductType                 string
	AccountType                    string
	AccountRefID                   int
	IsWoodpacking                  bool
	InsuranceType                  string
	ClientIsDOPaymentTypeFree      bool
	CodHandling                    string
	SttBookedBy                    int
	SttBookedByType                string

	SttIsDO                bool
	SttTotalAmount         float64
	IsPrefixCODCARetail    bool
	IsPrefixShipmentFavCOD bool

	ClientPaymentMethod       string
	ClientCodConfigAmount     string
	RateVatShipment           float64
	RateVatCod                float64
	ClientCodShipmentDiscount float64
	IsDFOD                    bool
	TotalTariffReturn         float64
	IsCalcTariffReturn        bool
	CodAmountConfigType       string
	PercentageCodFee          float64
	MinCodFee                 float64
	IsTopupCA                 bool
	PaymentStatus             string
	IsInvoiceCreated          bool
	IsHoldCommission          bool
	CodBookingDiscount        float64
	SttCODAmount              float64
	SttCODFee                 float64
	SttTotalAmountCOD         float64

	CheckTariffResponse model.CheckTariffResponse

	HistorySttArray             []model.SttPieceHistory
	HistoryStatus               []string
	DetailCalculateRetailTariff []model.DetailCalculateRetailTariff

	IsCreditDebitToBookedBy      bool
	IsZeroCreditCommission       bool
	IsDebitToBookedFor           bool
	IsOnlyDebitTransaction       bool
	CreditDebitActorID           int
	CreditDebitActorType         string
	IsZeroCreditDebit            bool
	IsZeroCreditDebitTransaction bool

	SttUpdatedAt time.Time

	SttAttachFiles []string
}

type BuildParamsUpdateStt func(context.Context, UpdateSttParams) (UpdateSttParams, error)

type ClientAssessment struct {
	ClientID               int
	ClientIsNeedAssessment bool
}
