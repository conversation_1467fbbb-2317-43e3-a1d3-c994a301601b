package stt

import (
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
)

const (
	EditableConfig    = "EDITABLE"
	NonEditableConfig = "NON_EDITABLE"
	ShowConfig        = "SHOW"
	NoShowConfig      = "NON_SHOW"
)

type SttAdjustmentConfigRequest struct {
	SttNo          string `json:"stt_no" query:"stt_no" form:"stt_no"`
	AccountType    string
	AccountRefType string
	Token          string
}

type FieldRule struct {
	IsRequired bool   `json:"is_required"`
	Rule       string `json:"rule"`
}

type Config struct {
	Client                  FieldRule `json:"client"`
	PaymentMethod           FieldRule `json:"payment_method"`
	ShipmentID              FieldRule `json:"shipment_id"`
	ManualSttNo             FieldRule `json:"manual_stt_no"`
	SttNoRefExternal        FieldRule `json:"stt_no_ref_external"`
	SenderName              FieldRule `json:"sender_name"`
	SenderPhone             FieldRule `json:"sender_phone"`
	SenderAddress           FieldRule `json:"sender_address"`
	SenderDistrict          FieldRule `json:"sender_district"`
	SenderPostalCode        FieldRule `json:"sender_postal_code"`
	SenderSave              FieldRule `json:"sender_save"`
	RecipientName           FieldRule `json:"recipient_name"`
	RecipientPhone          FieldRule `json:"recipient_phone"`
	RecipientAddress        FieldRule `json:"recipient_address"`
	RecipientDistrict       FieldRule `json:"recipient_district"`
	RecipientPostalCode     FieldRule `json:"recipient_postal_code"`
	RecipientSave           FieldRule `json:"recipient_save"`
	Commodity               FieldRule `json:"commodity"`
	ProductType             FieldRule `json:"product_type"`
	GoodsPrice              FieldRule `json:"goods_price"`
	Insurance               FieldRule `json:"insurance"`
	CODAmount               FieldRule `json:"cod_amount"`
	GrossWeight             FieldRule `json:"gross_weight"`
	Dimension               FieldRule `json:"dimension"`
	ChargeableWeight        FieldRule `json:"chargeable_weight"`
	AdjustPiece             FieldRule `json:"adjust_piece"`
	InterCIPL               FieldRule `json:"inter_cipl"`
	InterIdentityNo         FieldRule `json:"inter_identity_no"`
	InterTaxNo              FieldRule `json:"inter_tax_no"`
	InterEmail              FieldRule `json:"inter_email"`
	InterImageTaxNo         FieldRule `json:"inter_image_tax_no"`
	InterImageIdentityNo    FieldRule `json:"inter_image_identity_no"`
	InterImageBeforePacking FieldRule `json:"inter_image_before_packing"`
	InterImageAfterPacking  FieldRule `json:"inter_image_after_packing"`
	FtzCIPL                 FieldRule `json:"ftz_cipl"`
	FtzIdentityNo           FieldRule `json:"ftz_identity_no"`
	FtzTaxNo                FieldRule `json:"ftz_tax_no"`
	FtzEmail                FieldRule `json:"ftz_email"`
	FtzImageTaxNo           FieldRule `json:"ftz_image_tax_no"`
	FtzImageIdentityNo      FieldRule `json:"ftz_image_identity_no"`
	FtzImageBeforePacking   FieldRule `json:"ftz_image_before_packing"`
	FtzImageAfterPacking    FieldRule `json:"ftz_image_after_packing"`
	FtzShipmentStatus       FieldRule `json:"ftz_shipment_status"`
	ServiceType             FieldRule `json:"service_type"`
	Tariff                  FieldRule `json:"tariff"`
}

type SttAdjustmentConfigResponse struct {
	Config Config `json:"config"`
}

func (c *SttAdjustmentConfigRequest) Validate() error {
	if c.SttNo == "" {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Stt No cannot be empty",
			"id": "Stt No tidak boleh kosong",
		})
	}

	if !IsSttNoFormatForAdjusment(c.SttNo) {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Stt No format is invalid",
			"id": "Format Stt No tidak valid",
		})
	}
	return nil
}

func IsSttNoFormatForAdjusment(sttNo string) bool {
	if model.IsPrefixSttValidSttAdjustmentConfig[sttNo[:2]] {
		return shared.SttFormatLike.MatchString(sttNo)
	}
	return false
}
