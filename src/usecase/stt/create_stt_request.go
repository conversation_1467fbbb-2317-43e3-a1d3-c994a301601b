package stt

import (
	"fmt"
	"math"
	"regexp"
	"strings"
	"time"

	"github.com/abiewardani/dbr/v2"

	validation "github.com/go-ozzo/ozzo-validation/v4"
	"github.com/go-ozzo/ozzo-validation/v4/is"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
	customProcess "github.com/Lionparcel/hydra/src/usecase/custom_process"
	"github.com/Lionparcel/hydra/src/usecase/elexys"
	"github.com/Lionparcel/hydra/src/usecase/general"
)

var (
	Sender    = `Sender`
	Recipient = `Recipient`

	EdIdTranslate = map[string]string{
		Recipient: "Penerima",
		Sender:    "Pengirim",
	}

	MisbookingFromCustomer = `Permintaan perubahan alamat dari Customer`
	MisbookingFromPos      = `Permintaan perubahan alamat dari POS`
)

// CreateSttRequest ...
type CreateSttRequest struct {
	Stt                  []Stt                              `json:"stt"`
	ElexysTariff         elexys.CheckTariffEstimationElexys `json:"elexys_tariff`
	DistrictOrigin       model.DistrictData
	DistrictDestination  model.DistrictData
	Commodity            model.CommodityBase
	Partner              model.PartnerBase
	SttWeightAttachFiles []string `json:"stt_weight_attach_files"`
	ShipmentPackageID    int64
	AccountName          string
	AccountType          string
	AccountRefName       string
	AccountRefType       string
	AccountID            int64
	Token                string
	AccountRefID         int
	Source               string
	GoodsNames           []string `json:"goods_names"`

	BulkID           int `json:"bulk_id,omitempty"`
	BulkSTTRowNumber int `json:"bulk_stt_row_number,omitempty"`

	SttIdentityNumber    string `json:"stt_identity_number"`
	SttFtzIdentityNumber string `json:"stt_ftz_identity_number"`

	SaveCustomerSender    bool `json:"save_customer_sender"`
	SaveCustomerRecipient bool `json:"save_customer_recipient"`
}

func (c *CreateSttRequest) ReplaceInputCharNonASCII() {
	for i := 0; i < len(c.Stt); i++ {
		c.Stt[i].ReplaceInputCharNonASCII()
	}
}

// CreateSttManualRequest ...
type CreateSttManualRequest struct {
	Stt                  Stt                                `json:"stt"`
	ElexysTariff         elexys.CheckTariffEstimationElexys `json:"elexys_tariff"`
	SttWeightAttachFiles []string                           `json:"stt_weight_attach_files"`
	AccountType          string
	AccountID            int64
	Token                string
	AccountRole          string
	AccountRefID         int
	Source               string

	BulkID           int `json:"bulk_id"`
	BulkSTTRowNumber int `json:"bulk_stt_row_number"`

	SaveCustomerSender    bool `json:"save_customer_sender"`
	SaveCustomerRecipient bool `json:"save_customer_recipient"`
}

// CreateSttShipmentRequest ...
type CreateSttShipmentRequest struct {
	Stt                  Stt                                `json:"stt"`
	ShipmentPackageID    int64                              `json:"shipment_package_id"`
	ElexysTariff         elexys.CheckTariffEstimationElexys `json:"elexys_tariff"`
	DistrictOrigin       model.DistrictData                 `json:"district_origin"`
	DistrictDestination  model.DistrictData                 `json:"district_destination"`
	Commodity            model.CommodityBase                `json:"commodity"`
	Partner              model.PartnerBase                  `json:"partner"`
	SttWeightAttachFiles []string                           `json:"stt_weight_attach_files"`
	AccountType          string
	AccountID            int64
	Token                string
	AccountRole          string
	AccountRefID         int
	Source               string
	GoodsNames           []string `json:"goods_names"`
}

// CreateSttManualForClient ...
type CreateSttManualForClient struct {
	Stt                    Stt                                `json:"stt"`
	SttClient              int                                `json:"stt_client"`
	ElexysTariff           elexys.CheckTariffEstimationElexys `json:"elexys_tariff"`
	TicketCode             string                             `json:"ticket_code"`
	OtherShipperTicketCode []model.OtherShipperTicketCode     `json:"other_shipper_ticket_code"`
	BagLilo                LiloBagRequest                     `json:"bag_genesis"`
	SttWeightAttachFiles   []string                           `json:"stt_weight_attach_files"`
	AccountType            string
	AccountID              int64
	AccountName            string
	Token                  string
	AccountRefID           int
	AccountRefName         string
	AccountRefType         string
	AccountRoleName        string
	Source                 string
	GoodsNames             []string
	IsSttCrossDocking      bool

	BulkID           int `json:"bulk_id,omitempty"`
	BulkSTTRowNumber int `json:"bulk_stt_row_number,omitempty"`

	SaveCustomerSender    bool `json:"save_customer_sender"`
	SaveCustomerRecipient bool `json:"save_customer_recipient"`
}

func (c *CreateSttManualForClient) ReplaceInputCharNonASCII() {
	c.Stt.ReplaceInputCharNonASCII()
}

func (c *CreateSttManualForClient) Validation() error {
	if err := c.Stt.Validate(0, c.Source); err != nil {
		return err
	}

	if err := c.Stt.ValidateInterpack(0, c.Source); err != nil {
		return err
	}

	if err := c.SttNoNonCDValidation(); err != nil {
		return err
	}

	return nil
}

func (c *CreateSttManualForClient) SttNoNonCDValidation() error {
	if c.Stt.SttNo == "" || c.IsSttCrossDocking {
		return nil
	}

	validatePrefix := func(sttNo, prefix, enMessage, idMessage string) error {
		if !strings.HasPrefix(sttNo, prefix) {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": enMessage,
				"id": idMessage,
			})
		}
		return nil
	}

	if c.AccountType == model.INTERNAL {
		return validatePrefix(c.Stt.SttNo, model.PrefixClient,
			"Can only use STT manual with prefix 98LP",
			"Hanya dapat menggunakan STT manual dengan prefix 98LP")
	}

	if c.AccountRefType == model.POS {
		return validatePrefix(c.Stt.SttNo, model.PrefixAutoBookingForClient,
			"Can only use STT manual with prefix 19LP",
			"Hanya dapat menggunakan STT manual dengan prefix 19LP")
	}

	return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "Cannot use STT manual",
		"id": "Tidak bisa menggunakan STT manual",
	})
}

type LiloBagRequest struct {
	DestinationCityCode string   `json:"destination_city_code"`
	CustomGrossWeight   float64  `json:"custom_gross_weight"`
	CustomLength        float64  `json:"custom_length"`
	CustomWidth         float64  `json:"custom_width"`
	CustomHeight        float64  `json:"custom_height"`
	OriginCityCode      string   `json:"origin_city_code"`
	BagVendorNo         string   `json:"bag_vendor_no"`
	BagStt              []string `json:"bag_stt"`
	CrossdockChildAwb   []string `json:"crossdock_child_awb"`
	BagCommodityGroup   string   `json:"bag_commodity_group"`
	BagProductType      string   `json:"bag_product_type"`
}

// Stt ...
type Stt struct {
	SttNo                      string  `json:"stt_no"`
	SttShipmentID              string  `json:"stt_shipment_id"`
	SttClientID                int     `json:"stt_client_id"`
	SttPosID                   int     `json:"stt_pos_id"`
	SttTaxNumber               string  `json:"stt_tax_number"`
	SttGoodsEstimatePrice      float64 `json:"stt_goods_estimate_price"`
	SttGoodsStatus             string  `json:"stt_goods_status"`
	SttNoRefExternal           string  `json:"stt_no_ref_external"`
	SttNoRefExternalAdditional string  `json:"stt_no_ref_external_additional"`
	SttSource                  string
	SttTotalAmount             float64

	SttOriginCityID          string `json:"stt_origin_city_id"`
	SttDestinationCityID     string `json:"stt_destination_city_id"`
	SttOriginDistrictID      string `json:"stt_origin_district_id"`
	SttDestinationDistrictID string `json:"stt_destination_district_id"`

	SttSenderName    string `json:"stt_sender_name"`
	SttSenderPhone   string `json:"stt_sender_phone"`
	SttSenderAddress string `json:"stt_sender_address"`

	SttRecipientName        string `json:"stt_recipient_name"`
	SttRecipientAddress     string `json:"stt_recipient_address"`
	SttRecipientPhone       string `json:"stt_recipient_phone"`
	SttRecipientAddressType string `json:"stt_recipient_address_type"`

	SttProductType string `json:"stt_product_type"`

	SttOriginDistrictRate      float64
	SttDestinationDistrictRate float64

	SttPublishRate           float64
	SttShippingSurchargeRate float64
	SttDocumentSurchargeRate float64

	SttHeavyweightSurchargeRate float64
	SttCommoditySurchargeRate   float64

	SttTaxRate float64

	SttGrossWeight      float64
	SttVolumeWeight     float64
	SttChargeableWeight float64

	SttCommodityCode string `json:"stt_commodity_code"`
	SttInsuranceType string `json:"stt_insurance_type"`
	SttTotalPiece    int
	SttLastStatusID  string
	SttClientSttID   string
	SttVendorSttID   string

	SttCODAmount float64 `json:"stt_cod_amount"`
	SttCODFee    float64 `json:"stt_cod_fee"`
	SttIsCOD     bool    `json:"stt_is_cod"`
	SttIsDFOD    bool    `json:"stt_is_dfod"`

	SttIsDO bool

	SttIsWoodpacking bool `json:"stt_is_woodpacking"`

	SttBookedAt time.Time
	SttBookedBy int

	SttCreatedAt time.Time
	SttCreatedBy int
	SttUpdatedAt time.Time
	SttUpdatedBy int

	IsMixpack bool `json:"is_mixpack"`

	SttPieces []*SttPieces `json:"stt_pieces" faker:"-"`

	SttNextCommodity string `json:"stt_next_commodity"`
	SttPiecePerPack  int    `json:"stt_piece_per_pack"`

	PostalCodeDestination string `json:"postal_code_destination"`

	SttDestinationCityCodeForBag string `json:"stt_destination_city_code_for_bag"`

	SttAttachFiles     []string `json:"stt_attach_files"`
	SttCommodityDetail string   `json:"stt_commodity_detail"`

	DiscountFavoritePercentage float64 `json:"discount_favorite_percentage"`
	SttRecipientEmail          string  `json:"stt_recipient_email"`
	SttKtpImage                string  `json:"stt_ktp_image"`
	SttTaxImage                string  `json:"stt_tax_image"`
	SttInterTaxNumber          string  `json:"stt_inter_tax_number,omitempty"`
	SttIdentityNumber          string  `json:"stt_identity_number,omitempty"`
	IsNewForm                  bool    `json:"is_new_form"`

	SttFtzIdentityNumber string `json:"stt_ftz_identity_number"`
	SttFtzRecipientEmail string `json:"stt_ftz_recipient_email"`

	// CIPL
	SttCIPL    []model.SttCIPL `json:"stt_cipl"`
	SttFtzCIPL []model.SttCIPL `json:"stt_ftz_cipl"`

	PostalCodeOrigin string `json:"postal_code_origin"`

	SttFtzAttachFiles []string `json:"stt_ftz_attach_files"`
	SttFtzKtpImage    string   `json:"stt_ftz_ktp_image"`
	SttFtzTaxImage    string   `json:"stt_ftz_tax_image"`
}

func (c *Stt) ReplaceInputCharNonASCII() {
	c.SttSenderName = shared.ReplaceInvalidCharsNonASCII(c.SttSenderName)
	c.SttSenderAddress = shared.ReplaceInvalidCharsNonASCII(c.SttSenderAddress)
	c.SttRecipientName = shared.ReplaceInvalidCharsNonASCII(c.SttRecipientName)
	c.SttRecipientAddress = shared.ReplaceInvalidCharsNonASCII(c.SttRecipientAddress)
	c.SttCommodityDetail = shared.ReplaceInvalidCharsNonASCII(c.SttCommodityDetail)
}

func (s *Stt) IsCodValidation() bool {
	if s.SttIsCOD && !s.SttIsDFOD {
		if s.SttCODAmount == 0 || s.SttCODAmount != s.SttGoodsEstimatePrice {
			return true
		}
	}

	return false
}

// SttPieces ...
type SttPieces struct {
	SttID                int64
	SttPieceID           int64   `json:"stt_piece_id"`
	SttPieceLength       float64 `json:"stt_piece_length"`
	SttPieceWidth        float64 `json:"stt_piece_width"`
	SttPieceHeight       float64 `json:"stt_piece_height"`
	SttPieceGrossWeight  float64 `json:"stt_piece_gross_weight"`
	SttPieceVolumeWeight float64 `json:"stt_piece_volume_weight"`
}

// SttOptionalRate ...
type SttOptionalRate struct {
	SttID                 int64
	SttOptionalRateSttID  int64   `json:"stt_id"`
	SttOptionalRateName   string  `json:"stt_optional_rate_name"`
	SttOptionalRateRate   float64 `json:"stt_optional_rate_rate"`
	SttOptionalRateParams string  `json:"stt_optional_rate_params"`
}

// SttPieceHistory ...
type SttPieceHistory struct {
	SttPieceHistorySttPieceID int64
	SttPieceHistoryStatus     string
	SttPieceHistoryLocation   string
	SttPieceHistoryMeta       string

	SttPieceHistoryActorID   string
	SttPieceHistoryActorName string
	SttPieceHistoryActorRole string
	SttPieceHistoryRemark    string
	SttPieceHistoryReffID    int

	SttPieceHistoryCreatedBy int
	SttPieceHistoryCreatedAt time.Time
}

// CalculateTariffParams ...
type CalculateTariffParams struct {
	RequestCalculateTariff *RequestCalculateTariff
	Token                  string
}

// RequestCalculateTariff ...
type RequestCalculateTariff struct {
	OriginID         string                  `json:"origin_id"`
	DestinationID    string                  `json:"destination_id"`
	CommodityID      int                     `json:"commodity_id"`
	ProductType      string                  `json:"product_type"`
	AccountType      string                  `json:"account_type"`
	AccountRefID     int                     `json:"account_ref_id"`
	GoodsPrice       float64                 `json:"goods_price"`
	CodAmount        float64                 `json:"cod_amount"`
	IsCod            bool                    `json:"is_cod"`
	IsDfod           bool                    `json:"is_dfod"`
	InsuranceType    string                  `json:"insurance_type"`
	IsWoodpacking    bool                    `json:"is_woodpacking"`
	IsHaveTaxID      bool                    `json:"is_have_tax_id"`
	IsTariffNotRound bool                    `json:"is_tariff_not_round"`
	Pieces           []CalculateTariffPieces `json:"pieces"`

	PromoAppliedTo string `json:"promo_applied_to"`
	IsDisablePromo bool   `json:"is_disable_promo"`

	IsBookingPurposes bool            `json:"is_booking_puposes"`
	Commodity         model.Commodity `json:"commodity"`

	IsWithoutCodConfiguration bool    `json:"is_without_cod_configuration"`
	CodPercentageRetail       float64 `json:"cod_percentage_cod_retail"`
	MinCodValueRetail         float64 `json:"min_cod_value_cod_retail"`
	CodTypeRetail             string  `json:"cod_type_cod_retail"`

	ShipmentPrefix             string  `json:"shipment_prefix"`
	CodHandling                string  `json:"cod_handling"`
	DiscountFavoritePercentage float64 `json:"discount_favorite_percentage"`
}

type RequestCancelStt struct {
	SttNo           string `json:"stt_no"`
	Reason          string `json:"reason"`
	AccountType     string
	AccountRefName  string
	AccountRefType  string
	AccountRefID    int
	AccountName     string
	AccountRoleName string
	Token           string
	AccountID       int64

	// Penambahan payload untuk STT Baru
	SttGoodsStatus   string `json:"stt_goods_status"`
	SttTaxNumber     string `json:"stt_tax_number"`
	SttCommodityCode string `json:"stt_commodity_code"`
	SttProductType   string `json:"stt_product_type"`

	IsGenerateNewStt         bool   `json:"is_generate_new_stt" form:"is_generate_new_stt"`
	SttBookedBy              int    `json:"stt_booked_by" form:"stt_booked_by"`
	SttDestinationDistrictID string `json:"stt_destination_district_id" form:"stt_destination_district_id"`
	SttDestinationCityID     string `json:"stt_destination_city_id" form:"stt_destination_city_id"`
	SttReceiptName           string `json:"stt_receipt_name" form:"stt_receipt_name"`
	SttReceiptAddress        string `json:"stt_receipt_address" form:"stt_receipt_address"`
	SttReceiptAddressType    string `json:"stt_receipt_address_type" form:"stt_receipt_address_type"`
	SttReceiptPhone          string `json:"stt_receipt_phone" form:"stt_receipt_phone"`
	SttPiecePerPack          int    `json:"stt_piece_per_pack" form:"stt_piece_per_pack"`
	SttNextCommodity         string `json:"stt_next_commodity" form:"stt_next_commodity"`
	HubID                    int    `json:"hub_id" form:"hub_id"`
	HubName                  string `json:"hub_name" form:"hub_name"`
	HubOriginCity            string `json:"hub_origin_city" form:"hub_origin_city"`
	HubDistrictCode          string `json:"hub_district_code" form:"hub_district_code"`
	SttNoReference           string `json:"stt_no_reference" form:"stt_no_reference"`

	// Penambahan payload untuk FTZ untuk STT Baru
	SttFtzRecipientEmail string    `json:"stt_ftz_recipient_email" form:"stt_ftz_recipient_email"`
	SttFtzIdentityNumber string    `json:"stt_ftz_identity_number" form:"stt_ftz_identity_number"`
	SttFtzTaxImage       string    `json:"stt_ftz_tax_image" form:"stt_ftz_tax_image"`
	SttFtzKtpImage       string    `json:"stt_ftz_ktp_image" form:"stt_ftz_ktp_image"`
	SttFtzAttachFiles    []string  `json:"stt_ftz_attach_files" form:"stt_ftz_attach_files"`
	SttFtzCIPL           []SttCIPL `json:"stt_ftz_cipl" form:"stt_ftz_cipl"`
}

type ResponseCancelStt struct {
	SttSuccessRefNo []string `json:"stt_success_ref_no,omitempty"`
}

func (c *RequestCancelStt) Validate() error {
	if c.SttNo == `` {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "STT Number must be filled",
			"id": "Nomor STT harus diisi",
		})
	}

	if c.Reason == `` {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "STT cancel reason must be filled",
			"id": "Alasan pembatalan STT harus diisi",
		})
	}
	return nil
}

// CalculateTariffPieces ...
type CalculateTariffPieces struct {
	PieceLength      float64 `json:"piece_length"`
	PieceWidth       float64 `json:"piece_width"`
	PieceHeight      float64 `json:"piece_height"`
	PieceGrossWeight float64 `json:"piece_gross_weight"`
}

type ValidateJumbopackParams struct {
	Source       string
	AccountType  string
	IsPartnerPcu bool
	SenderName   string
	SenderPhone  string
}

// GeneratePiecesCalculateTariff ...
func (c *RequestCalculateTariff) GeneratePiecesCalculateTariff(pieces []*SttPieces) {
	for _, piece := range pieces {
		c.Pieces = append(c.Pieces, CalculateTariffPieces{
			PieceLength:      piece.SttPieceLength,
			PieceWidth:       piece.SttPieceWidth,
			PieceHeight:      piece.SttPieceHeight,
			PieceGrossWeight: piece.SttPieceGrossWeight,
		})
	}
}

func (c *Stt) ValidateInterpack(i int, source string) error {
	if c.SttProductType == model.INTERPACK && c.SttTotalPiece > 1 {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Interpack only allow max 1 piece",
			"id": "Interpack hanya boleh 1 koli",
		})
	}

	return nil
}

func (c *Stt) GetSummaryGrossWeight() float64 {
	totalPieces := len(c.SttPieces)
	var totalGross float64

	if totalPieces == 0 {
		return totalGross
	}
	for _, piece := range c.SttPieces {
		totalGross += piece.SttPieceGrossWeight
	}
	return math.Ceil(totalGross*100) / 100
}

func (c *Stt) ValidateJumbopack(params ValidateJumbopackParams) error {
	if params.Source == model.MANUAL {
		return nil
	}
	if model.IsJumbopack[c.SttProductType] && (params.Source != model.ALGO || params.AccountType != model.PARTNER || !params.IsPartnerPcu) {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf(`Failed to create shipment id! shipment id <strong>“%s”</strong> can only be processed by POS PCU because the shipment uses jumbopack product. Silahkan hubungi pengirim %s, %s untuk melakukan pengembalian.`, c.SttShipmentID, params.SenderName, params.SenderPhone),
			"id": fmt.Sprintf(`Penambahan shipment id gagal! shipment id <strong>“%s”</strong> hanya bisa diproses oleh POS PCU karena pengiriman menggunakan product jumbopack. Please contact the sender %s, %s to make a return.`, c.SttShipmentID, params.SenderName, params.SenderPhone),
		})
	}

	return nil
}

// Validate ...
func (c *Stt) Validate(i int, source string) error {
	/* validation product interpack */
	if strings.EqualFold(c.SttProductType, model.INTERPACK) {
		if c.PostalCodeDestination == `` {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Postal Code is required",
				"id": "Postal Code harus diisi",
			})
		}
	}

	/* validation postal code */
	if c.PostalCodeDestination != `` {
		isMatch, err := regexp.MatchString(`^[A-Z0-9-,]*$`, c.PostalCodeDestination)
		if err != nil || !isMatch {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid Postal Code",
				"id": "Postal Code Tidak Valid",
			})
		}

		// set default first zip code if the input is multiple
		arrPostalCodeDestination := strings.Split(c.PostalCodeDestination, ",")
		if len(arrPostalCodeDestination) > 1 {
			c.PostalCodeDestination = strings.TrimSpace(arrPostalCodeDestination[0])
		}
	}

	c.SttNo = strings.ToUpper(strings.ReplaceAll(strings.TrimSpace(c.SttNo), " ", ""))
	c.SttShipmentID = strings.ToUpper(strings.ReplaceAll(strings.TrimSpace(c.SttShipmentID), " ", ""))

	c.SttNoRefExternal = strings.ReplaceAll(strings.TrimSpace(c.SttNoRefExternal), " ", "")
	if err := validation.Validate(len(c.SttNoRefExternal), validation.Max(model.MAX_LENGTH_EXT_REF_NO)); err != nil && c.SttNoRefExternal != `` {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("External Reference Number length must lower than or equal to %d ", model.MAX_LENGTH_EXT_REF_NO),
			"id": fmt.Sprintf("Panjang External Reference Number harus dibawah atau sama dengan %d", model.MAX_LENGTH_EXT_REF_NO),
		})
	}

	c.SttGoodsStatus = strings.ReplaceAll(strings.TrimSpace(c.SttGoodsStatus), " ", "")
	if _, ok := model.IsGoodsStatusValid[c.SttGoodsStatus]; !ok && c.SttGoodsStatus != `` {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Invalid Goods Status format",
			"id": "Format Goods Status tidak benar",
		})
	}

	c.SttSenderName = strings.TrimSpace(c.SttSenderName)
	c.SttSenderAddress = strings.TrimSpace(c.SttSenderAddress)
	c.SttSenderPhone = strings.TrimSpace(c.SttSenderPhone)
	c.SttSenderPhone = shared.FormatPhonenumberIndonesia(c.SttSenderPhone)
	if err := ValidateCustomerData(c.SttSenderName, c.SttSenderPhone, c.SttSenderAddress, Sender); err != nil {
		return err
	}

	c.SttRecipientName = strings.TrimSpace(c.SttRecipientName)
	c.SttRecipientAddress = strings.TrimSpace(c.SttRecipientAddress)
	c.SttRecipientPhone = strings.TrimSpace(c.SttRecipientPhone)
	c.SttRecipientPhone = shared.FormatPhonenumberIndonesia(c.SttRecipientPhone)
	if err := ValidateCustomerData(c.SttRecipientName, c.SttRecipientPhone, c.SttRecipientAddress, Recipient); err != nil {
		return err
	}

	c.SttRecipientAddressType = strings.TrimSpace(c.SttRecipientAddressType)

	if c.SttRecipientAddressType != "" && !model.ValidRecipientAddressType[c.SttRecipientAddressType] {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Invalid recipient address type",
			"id": "Tipe alamat penerima tidak valid",
		})
	}

	prefixShipment := ``
	if len(c.SttShipmentID) > 1 && source == model.ALGO {
		prefixShipment = shared.GetPrefixShipmentID(c.SttShipmentID)
	}
	if prefixShipment != model.AO {
		if strings.EqualFold(c.SttSenderAddress, c.SttRecipientAddress) {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Sender and Recepient address cannot be the same",
				"id": "Alamat pengirim dan penerima tidak boleh sama",
			})
		}
	}

	errCodeEmpty := func(source string, types string) error {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("%s %s Code cannot be empty", source, types),
			"id": fmt.Sprintf("Kode %s %s tidak boleh kosong", types, source),
		})
	}

	errCodeFormat := func(source string, types string) error {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("%s %s Code must be in valid format", source, types),
			"id": fmt.Sprintf("Kode %s %s harus dalam format yang benar", types, source),
		})
	}

	c.SttOriginCityID = strings.ToUpper(strings.ReplaceAll(strings.TrimSpace(c.SttOriginCityID), " ", ""))
	if err := validation.Validate(c.SttOriginCityID, validation.Required); err != nil {
		return errCodeEmpty("Origin", "City")
	}

	if err := validation.Validate(c.SttOriginCityID, validation.Length(3, 3), is.Alpha); err != nil {
		return errCodeFormat("Origin", "City")
	}

	c.SttOriginDistrictID = strings.ToUpper(strings.ReplaceAll(strings.TrimSpace(c.SttOriginDistrictID), " ", ""))
	if err := validation.Validate(c.SttOriginDistrictID, validation.Required); err != nil {
		return errCodeEmpty("Origin", "District")
	}

	c.SttDestinationCityID = strings.ToUpper(strings.ReplaceAll(strings.TrimSpace(c.SttDestinationCityID), " ", ""))
	if err := validation.Validate(c.SttDestinationCityID, validation.Required); err != nil {
		return errCodeEmpty("Destination", "City")
	}

	if err := validation.Validate(c.SttDestinationCityID, validation.Length(3, 3), is.Alpha); err != nil {
		return errCodeFormat("Destination", "City")
	}

	c.SttDestinationDistrictID = strings.ToUpper(strings.ReplaceAll(strings.TrimSpace(c.SttDestinationDistrictID), " ", ""))
	if err := validation.Validate(c.SttDestinationDistrictID, validation.Required); err != nil {
		return errCodeEmpty("Destination", "District")
	}

	c.SttCommodityCode = strings.TrimSpace(c.SttCommodityCode)
	if err := validation.Validate(c.SttCommodityCode, validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Commodity Code must be filled",
			"id": "Commodity Code harus di isi",
		})
	}

	c.SttInsuranceType = strings.ReplaceAll(strings.TrimSpace(c.SttInsuranceType), " ", "")
	if err := validation.Validate(c.SttInsuranceType, validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Insurance Type must be filled",
			"id": "Insurance Type harus di isi",
		})
	}

	if _, ok := model.IsInsuranceValid[c.SttInsuranceType]; !ok {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Invalid Insurance Type format",
			"id": "Format Insurance Type tidak valid",
		})
	}

	c.SttProductType = strings.ReplaceAll(strings.TrimSpace(strings.ToUpper(c.SttProductType)), " ", "")
	if err := validation.Validate(c.SttProductType, validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Product Type must be filled",
			"id": "Product Type harus di isi",
		})
	}

	if err := validation.Validate(c.SttGoodsEstimatePrice, validation.When((source == model.ALGO && c.SttInsuranceType != model.INSURANCEFREE), validation.Required), validation.Min(model.MIN_GOODS_PRICE)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Goods Estimate Price must be higher than or equal to 0",
			"id": "Harga Perkiraan Barang harus lebih besar dari atau sama dengan 0",
		})
	}

	if err := c.validateSttIdentityNumber(); err != nil {
		return err
	}

	if err := c.validateSttInterTaxNumber(); err != nil {
		return err
	}

	if err := c.validateSttPieces(); err != nil {
		return err
	}

	if err := c.validateCIPL(); err != nil {
		return err
	}

	return nil
}

func isExternalTKP01(sttNoRefExternal string) bool {
	if len(sttNoRefExternal) < 5 {
		return false
	}

	prefix := sttNoRefExternal[:5]
	return shared.IsLiloPrefix(prefix)
}

func ValidateTotalWeight(volumeWeight float64, grossWeight float64, shipmentId, sttNoRefExternal string) error {
	if err := validation.Validate(volumeWeight, validation.Required, validation.Min(model.MIN_VOLUME_WEIGHT)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Total Pieces Volume Weight must be higher than 0.01",
			"id": "Total Berat Volume Pieces harus lebih besar dari 0.01",
		})
	}

	if err := validation.Validate(volumeWeight, validation.Required, validation.Max(model.MAX_TOTAL_VOLUME_WEIGHT)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("Total Pieces Volume Weight must be lower than %.f", model.MAX_TOTAL_VOLUME_WEIGHT),
			"id": fmt.Sprintf("Total Berat Volume Pieces harus kurang dari %.f", model.MAX_TOTAL_VOLUME_WEIGHT),
		})
	}

	if err := validation.Validate(grossWeight, validation.Required, validation.Min(model.MIN_WEIGHT_NEW)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Total Pieces Gross Weight must be higher than 0",
			"id": "Total Berat Kotor Pieces harus lebih besar dari 0",
		})
	}

	if err := validation.Validate(grossWeight, validation.Required, validation.Max(model.MAX_TOTAL_GROSS_WEIGHT)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("Total Pieces Gross Weight must be lower than %.f", model.MAX_TOTAL_GROSS_WEIGHT),
			"id": fmt.Sprintf("Total Berat Kotor Pieces harus kurang dari %.f", model.MAX_TOTAL_GROSS_WEIGHT),
		})
	}

	return nil
}

func (c *Stt) ValidateTaxNumber(taxNumber string) error {
	taxNumber = strings.TrimSpace(taxNumber)

	if err := validation.Validate(taxNumber, validation.Required); err == nil {
		errTaxInvalid := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Invalid Tax Number format",
			"id": "Format Nomor Pajak tidak benar",
		})
		taxNumber = strings.ReplaceAll(strings.ReplaceAll(taxNumber, ".", ""), "-", "")
		if err := validation.Validate(taxNumber, is.Digit); err != nil {
			return errTaxInvalid
		}

		if err := validation.Validate(len(taxNumber), validation.Max(16), validation.Min(15)); err != nil {
			return errTaxInvalid
		}
	}

	return nil
}

// CalculateWeight ...
func (c *Stt) CalculateWeight() {
	volumeWeight := shared.RoundWeight(c.SttVolumeWeight, c.SttProductType)
	grossWeight := shared.RoundWeight(c.SttGrossWeight, c.SttProductType)

	c.SttChargeableWeight = volumeWeight
	if grossWeight > volumeWeight {
		c.SttChargeableWeight = grossWeight
	}
}

// ValidateWeight ...
func ValidateWeight(grossWeight float64, volumeWeight float64) error {
	if err := validation.Validate(grossWeight, validation.When(volumeWeight == 0, validation.Required), validation.Min(0.1)); err != nil {
		if strings.Contains(err.Error(), validation.ErrRequired.Error()) {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Goods Weight is required to be filled",
				"id": "Berat Barang harus di isi",
			})
		}

		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Invalid Goods Weight format",
			"id": "Format Berat Barang tidak valid",
		})
	}

	if err := validation.Validate(volumeWeight, validation.When(grossWeight == 0, validation.Required), validation.Min(0.1)); err != nil {
		if strings.Contains(err.Error(), validation.ErrRequired.Error()) {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Goods Volume is required to be filled",
				"id": "Volume Barang harus di isi",
			})
		}

		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Invalid Goods Volume format",
			"id": "Format Volume Barang tidak valid",
		})
	}

	return nil
}

// ValidateCustomerData ...
func ValidateCustomerData(name string, phone string, addres string, types string) error {
	if err := validation.Validate(name, validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("%s Name cannot empty", types),
			"id": fmt.Sprintf("Nama %s tidak boleh kosong", EdIdTranslate[types]),
		})
	}

	if err := validation.Validate(name, validation.Length(1, 0)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("%s Name cannot less than 1 character", types),
			"id": fmt.Sprintf("Nama %s tidak boleh kurang dari 1 karakter", EdIdTranslate[types]),
		})
	}

	if err := validation.Validate(addres, validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("%s Address cannot empty", types),
			"id": fmt.Sprintf("Alamat %s tidak boleh kosong", EdIdTranslate[types]),
		})
	}

	if err := validation.Validate(phone, validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("%s Number cannot empty", types),
			"id": fmt.Sprintf("Nomor %s tidak boleh kosong", EdIdTranslate[types]),
		})
	}

	if len(phone) > model.MAX_LENGTH_PHONE_NUMBER {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("%s Number length cannot greater than %d", types, model.MAX_LENGTH_PHONE_NUMBER),
			"id": fmt.Sprintf("Panjang Nomor %s tidak boleh lebih dari %d", EdIdTranslate[types], model.MAX_LENGTH_PHONE_NUMBER),
		})
	}

	// errPhoneInvalid := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
	// 	"en": fmt.Sprintf("%s Number can only be filled with digit and + mark in the beginning of the sentence", types),
	// 	"id": fmt.Sprintf("Nomor %s hanya boleh diisi dengan angka dan tanda + diawal kalimat", EdIdTranslate[types]),
	// })

	if err := validation.Validate(phone, validation.Match(shared.NumberRegex)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("%s Number must be in digit only", types),
			"id": fmt.Sprintf("Nomor %s harus dalam digit", EdIdTranslate[types]),
		})
	}

	return nil
}

// SttSubmitParams to ALGO
type SttSubmitParams struct {
	SubmitSttRequest SubmitSttRequest
	Token            string
}

// Use for middleware service webhook
type EmbedSubmitSttRequestMiddleware struct {
	SubmitSttRequest
	TypeStruct string `json:"type_struct"`
	ClientCode string `json:"client_code"`
}

// SubmitSttRequest ...
type SubmitSttRequest struct {
	SttNo                                 string                    `json:"stt_no"`
	Sender                                Customer                  `json:"sender"`
	Recipient                             Customer                  `json:"recipient"`
	ServiceType                           string                    `json:"service_type"`
	Product                               string                    `json:"product"`
	Pieces                                int                       `json:"pieces"`
	GrossWeight                           float64                   `json:"gross_weight"`
	VolumeWeight                          float64                   `json:"volume_weight"`
	ChargeableWeight                      float64                   `json:"chargeable_weight"`
	PublishRate                           float64                   `json:"publish_rate"`
	OriginDistrictRate                    float64                   `json:"origin_district_rate"`
	DestinationDistrictRate               float64                   `json:"destination_district_rate"`
	ForwardRate                           float64                   `json:"forward_rate"`
	ShippingSurchargeRate                 float64                   `json:"shipping_surcharge_rate"`
	CommoditySurchargeRate                float64                   `json:"commodity_surcharge_rate"`
	HeavyWeightSurchargeRate              float64                   `json:"heavy_weight_surcharge_rate"`
	IsCod                                 bool                      `json:"is_cod"`
	IsDfod                                bool                      `json:"is_dfod"`
	IsWoodPacking                         bool                      `json:"is_wood_packing"`
	WoodPackingRate                       float64                   `json:"wood_packing_rate"`
	IsInsurance                           bool                      `json:"is_insurance"`
	InsuranceRate                         float64                   `json:"insurance_rate"`
	InsuranceType                         string                    `json:"insurance_type"`
	GoodsValue                            float64                   `json:"goods_value"`
	CodValue                              float64                   `json:"cod_value"`
	CodAmount                             float64                   `json:"cod_amount"`
	CodFee                                float64                   `json:"cod_fee"`
	CashCollected                         int                       `json:"cash_collected"`
	IsCashOut                             bool                      `json:"is_cash_out"`
	Origin                                string                    `json:"origin"`
	Destination                           string                    `json:"destination"`
	CurrentStatus                         string                    `json:"current_status"`
	StatusCode                            string                    `json:"status_code"`
	ExternalID                            string                    `json:"external_id"`
	ShipmentID                            string                    `json:"shipment_id"`
	CreatedOn                             time.Time                 `json:"created_on"`
	UpdatedOn                             time.Time                 `json:"updated_on"`
	IsShipmentDone                        bool                      `json:"is_shipment_done"`
	DocumentSurcharge                     float64                   `json:"document_surcharge"`
	ReturnDetails                         *general.ReturnDetails    `json:"return_details,omitempty"`
	Location                              string                    `json:"location"`
	City                                  string                    `json:"city"`
	Remarks                               string                    `json:"remarks"`
	UpdatedBy                             string                    `json:"updated_by"`
	TotalTariff                           float64                   `json:"total_tariff"`
	IsPromo                               bool                      `json:"is_promo"`
	Discount                              float64                   `json:"discount"`
	TotalDiscount                         float64                   `json:"total_discount"`
	PublishRateBeforeDiscount             float64                   `json:"publish_rate_before_discount"`
	ShippingSurchargeRateBeforeDiscount   float64                   `json:"shipping_surcharge_rate_before_discount"`
	OriginDistrictRateBeforeDiscount      float64                   `json:"origin_district_rate_before_discount"`
	DestinationDistrictRateBeforeDiscount float64                   `json:"destination_district_rate_before_discount"`
	ForwardRateBeforeDiscount             float64                   `json:"forward_rate_before_discount"`
	DocumentSurchargeBeforeDiscount       float64                   `json:"document_surcharge_before_discount"`
	CommoditySurchargeBeforeDiscount      float64                   `json:"commodity_surcharge_before_discount"`
	HeavyWeightSurchargeBeforeDiscount    float64                   `json:"heavy_weight_surcharge_before_discount"`
	WoodpackingRatesBeforeDiscount        float64                   `json:"woodpacking_rates_before_discount"`
	InsuranceRatesBeforeDiscount          float64                   `json:"insurance_rates_before_discount"`
	TotalTariffBeforeDiscount             float64                   `json:"total_tariff_before_discount"`
	ShipmentIDReferenceReverseJourney     string                    `json:"shipment_id_reference_reverse_journey"`
	SttNoReferenceReverseJourney          string                    `json:"stt_no_reference_reverse_journey"`
	IsReturnDO                            bool                      `json:"is_return_do"`
	IsInvoiceCreated                      bool                      `json:"is_invoice_created,omitempty"`
	IsTopupCA                             bool                      `json:"is_topup_ca,omitempty"`
	PercentageCodFee                      float64                   `json:"percentage_cod_fee"`
	MinCodFee                             float64                   `json:"min_cod_fee"`
	AgentName                             string                    `json:"agent_name,omitempty"`
	HubID                                 int                       `json:"hub_id,omitempty"`
	HubName                               string                    `json:"hub_name,omitempty"`
	SttAttachFiles                        []string                  `json:"stt_attach_files"`
	UrlPicture                            string                    `json:"url_picture"`
	BmTaxRate                             float64                   `json:"bm_tax_rate"`
	PpnTaxRate                            float64                   `json:"ppn_tax_rate"`
	PphTaxRate                            float64                   `json:"pph_tax_rate"`
	InsuranceAdminFee                     float64                   `json:"insurance_admin_fee"`
	ReturnFeeBasic                        float64                   `json:"return_fee_basic"`
	ReturnFee                             float64                   `json:"return_fee"`
	ShippingCost                          float64                   `json:"shipping_cost"`
	SttWeightAttachFileSigneds            []string                  `json:"stt_weight_attach_file_signeds"`
	PriorityTier                          bool                      `json:"is_priority_tier,omitempty"`
	PrioritySubscription                  bool                      `json:"is_priority_subscription,omitempty"`
	SttJourneyType                        string                    `json:"stt_journey_type"`
	RecipientDistrictCode                 string                    `json:"recipient_district_code"`
	Attachment                            []string                  `json:"attachment"`
	SttBookedAt                           string                    `json:"stt_booked_at"`
	SttBookedBy                           int                       `json:"stt_booked_by"`
	SttBookedName                         string                    `json:"stt_booked_name"`
	SttBookedByCode                       string                    `json:"stt_booked_by_code"`
	SttBookedByType                       string                    `json:"stt_booked_by_type"`
	BookedByTier                          string                    `json:"stt_booked_by_tier"`
	OriginCityID                          string                    `json:"origin_city_id"`
	DestinationCityID                     string                    `json:"destination_city_id"`
	EstimatedSLA                          EstimatedSLA              `json:"estimated_sla"`
	TotalPiece                            int                       `json:"total_piece"`
	Commodity                             Commodity                 `json:"commodity"`
	Source                                string                    `json:"source"`
	TotalDimLength                        float64                   `json:"total_dim_length"`
	TotalDimWidth                         float64                   `json:"total_dim_width"`
	TotalDimHeight                        float64                   `json:"total_dim_height"`
	PiecesDetails                         []model.SttPieceForClient `json:"pieces_details"`
}

func (c *EmbedSubmitSttRequestMiddleware) CheckEmpty() {
	if c.ShipmentID == "" {
		c.ShipmentID = c.ShipmentIDReferenceReverseJourney
	}
}

type EstimatedSLA struct {
	Range     string `json:"range"`
	StartDate string `json:"start_date"`
	EndDate   string `json:"end_date"`
}

type Commodity struct {
	Code         string `json:"code"`
	Name         string `json:"name"`
	IsDg         bool   `json:"is_dg"`
	IsQuarantine bool   `json:"is_quarantine"`
}

// Customer ...
type Customer struct {
	Name    string `json:"name"`
	Address string `json:"address"`
	Phone   string `json:"phone"`
}

// RequestGenerateStt ...
type RequestGenerateStt struct {
	SttCreate           *model.SttCreate
	SttData             *Stt
	SttRequest          *CreateSttRequest
	ReferenceName       string
	Index               int
	BookedForActor      *model.Actor
	DestinationDistrict *model.District
	SttManual           *model.SttManual
}

type SttCreateParams struct {
	Stt                   *Stt
	CheckTariff           *model.CheckTariffBase
	Now                   *time.Time
	Route                 string
	BookedBy              int
	BookedName            string
	BookedRole            string
	BookedCode            string
	IsSttManual           bool
	AccountID             int
	AccountName           string
	Remarks               string
	DestinationDistrict   *model.District
	OriginDistrict        *model.District
	Commodity             *model.Commodity
	BookedForExternalCode string
	BookedByExternalCode  string
	ElexysTariff          *elexys.CheckTariffEstimationElexys
	BookedForActor        *model.Actor
	SttManual             *model.SttManual
}

func GenerateSttCreate(params *SttCreateParams) *model.SttCreate {
	sttCreate := model.SttCreate{
		Stt: model.Stt{
			SttNo:                    params.Stt.SttNo,
			SttShipmentID:            params.Stt.SttShipmentID,
			SttTaxNumber:             params.Stt.SttTaxNumber,
			SttGoodsEstimatePrice:    params.Stt.SttGoodsEstimatePrice,
			SttGoodsStatus:           params.Stt.SttGoodsStatus,
			SttNoRefExternal:         params.Stt.SttNoRefExternal,
			SttOriginCityID:          params.Stt.SttOriginCityID,
			SttDestinationCityID:     params.Stt.SttDestinationCityID,
			SttOriginDistrictID:      params.Stt.SttOriginDistrictID,
			SttDestinationDistrictID: params.Stt.SttDestinationDistrictID,
			SttSenderName:            params.Stt.SttSenderName,
			SttSenderAddress:         params.Stt.SttSenderAddress,
			SttSenderPhone:           params.Stt.SttSenderPhone,
			SttRecipientName:         params.Stt.SttRecipientName,
			SttRecipientAddress:      params.Stt.SttRecipientAddress,
			SttRecipientPhone:        params.Stt.SttRecipientPhone,
			SttRecipientAddressType:  dbr.SetNullString(params.Stt.SttRecipientAddressType),
			SttProductType:           params.Stt.SttProductType,
			SttInsuranceType:         params.Stt.SttInsuranceType,

			SttOriginDistrictRate:       params.CheckTariff.OriginDistrictRate,
			SttDestinationDistrictRate:  params.CheckTariff.DestinationDistrictRate,
			SttPublishRate:              params.CheckTariff.PublishRate,
			SttShippingSurchargeRate:    params.CheckTariff.ShippingSurchargeRate,
			SttDocumentSurchargeRate:    params.CheckTariff.DocumentSurcharge,
			SttCommoditySurchargeRate:   params.CheckTariff.CommoditySurcharge,
			SttHeavyweightSurchargeRate: params.CheckTariff.HeavyWeightSurcharge,
			SttBMTaxRate:                params.CheckTariff.BMTaxRate,
			SttPPNTaxRate:               params.CheckTariff.PPNTaxRate,
			SttPPHTaxRate:               params.CheckTariff.PPHTaxRate,
			SttTotalAmount:              params.CheckTariff.TotalTariff,

			SttHeavyweightSurchargeRemark: params.CheckTariff.HeavyWeightSurchargeRemarks,

			SttGrossWeight:      params.CheckTariff.GrossWeight,
			SttVolumeWeight:     params.CheckTariff.VolumeWeight,
			SttChargeableWeight: params.CheckTariff.ChargeableWeight,
			SttCommodityCode:    params.Stt.SttCommodityCode,
			SttTotalPiece:       params.Stt.SttTotalPiece,
			SttLastStatusID:     model.BKD,
			SttClientSttID:      params.Stt.SttClientSttID,
			SttVendorSttID:      params.Stt.SttVendorSttID,
			SttIsCOD:            params.Stt.SttIsCOD,
			SttIsDFOD:           params.Stt.SttIsDFOD,
			SttCODAmount:        params.Stt.SttCODAmount,
			SttCODFee:           params.Stt.SttCODFee,
			SttIsDO:             params.Stt.SttIsDO,
			SttCounter:          params.Stt.SttTotalPiece,
			SttBookedAt:         *params.Now,
			SttBookedBy:         params.BookedBy,
			SttBookedName:       params.BookedName,
			SttBookedByType:     params.BookedRole,
			SttCreatedAt:        *params.Now,
			SttCreatedBy:        int(params.AccountID),
			SttCreatedName:      params.AccountName,
			SttUpdatedAt:        *params.Now,
			SttUpdatedBy:        int(params.AccountID),
			SttUpdatedName:      params.AccountName,
			SttRoute:            params.Route,
			SttUpdatedActorName: dbr.NewNullString(params.BookedName),
			SttUpdatedActorRole: dbr.NewNullString(params.BookedRole),
			SttUpdatedActorID:   dbr.NewNullInt64(params.BookedBy),
			SttNextCommodity:    params.Stt.SttNextCommodity,
			SttPiecePerPack:     params.Stt.SttPiecePerPack,

			SttCommodityName:               params.Commodity.Data.CommodityName,
			SttCommodityHsCode:             params.Commodity.Data.HsCode,
			SttOriginCityName:              params.OriginDistrict.Data.City.Name,
			SttDestinationCityName:         params.DestinationDistrict.Data.City.Name,
			SttOriginDistrictName:          params.OriginDistrict.Data.Name,
			SttDestinationDistrictName:     params.DestinationDistrict.Data.Name,
			SttOriginDistrictUrsaCode:      params.OriginDistrict.Data.UrsaCode,
			SttDestinationDistrictUrsaCode: params.DestinationDistrict.Data.UrsaCode,
			SttBookedByCode:                params.BookedCode,
			SttBookedForID:                 params.BookedForActor.ID,
			SttBookedForName:               params.BookedForActor.Name,
			SttBookedForCode:               params.BookedForActor.Code,
			SttBookedForType:               params.BookedForActor.Type,
		},
		SttPieceHistory: model.SttPieceHistory{
			HistoryStatus:      model.BKD,
			HistoryLocation:    params.Stt.SttOriginCityID,
			HistoryActorName:   params.BookedName,
			HistoryActorRole:   params.BookedRole,
			HistoryActorID:     params.BookedBy,
			HistoryCreatedAt:   *params.Now,
			HistoryCreatedBy:   int(params.AccountID),
			HistoryCreatedName: params.AccountName,
			HistoryRemark:      params.Remarks,
		},
		IsSttManual:           params.IsSttManual,
		CheckTariff:           params.CheckTariff,
		OriginDistrict:        params.OriginDistrict,
		DestinationDistrict:   params.DestinationDistrict,
		BookedForExternalCode: params.BookedForExternalCode,
		BookedByExternalCode:  params.BookedByExternalCode,
		Commodity:             params.Commodity,
		ElexysTariff: &model.SttElexys{
			SEElexysTotalTariff:              params.ElexysTariff.PublishRate + params.ElexysTariff.ForwardRate + params.ElexysTariff.ShippingSurchargeRate + params.ElexysTariff.DocumentSurchargeRate + params.ElexysTariff.CommoditySurchargeRate + params.ElexysTariff.HeavyWeightSurchargeRate + params.ElexysTariff.InsuranceRate + params.ElexysTariff.WoodPackingRate,
			SEElexysForwardRate:              params.ElexysTariff.ForwardRate,
			SEElexysPublishRate:              params.ElexysTariff.PublishRate,
			SEElexysShippingSurchargeRate:    params.ElexysTariff.ShippingSurchargeRate,
			SEElexysDocumentSurchargeRate:    params.ElexysTariff.DocumentSurchargeRate,
			SEElexysCommoditySurchargeRate:   params.ElexysTariff.CommoditySurchargeRate,
			SEElexysHeavywieghtSurchargeRate: params.ElexysTariff.HeavyWeightSurchargeRate,
			SEElexysInsuranceRate:            params.ElexysTariff.InsuranceRate,
			SEElexysWoodpackingRate:          params.ElexysTariff.WoodPackingRate,
			SEElexysEtd:                      params.ElexysTariff.ETD,
			SEElexysGrossWeight:              params.ElexysTariff.GrossWeight,
			SEElexysVolumeWeight:             params.ElexysTariff.VolumeWeight,
			SEElexysChargeableWeight:         params.ElexysTariff.ChargeableWeight,
			SEElexysIsCodArea:                params.ElexysTariff.IsCodArea,
			SEElexysWeight:                   params.ElexysTariff.Weight,
			SEElexysTotalNormalTariff:        params.ElexysTariff.TotalNormalTariff,
			SEElexysTotalBasicTariff:         params.ElexysTariff.TotalBasicTariff,
		},
		SttManual: params.SttManual,
	}

	/**
	 * check if COD
	 */
	if params.Stt.SttIsCOD {
		sttCreate.Stt.SttCODAmount = params.Stt.SttCODAmount
		sttCreate.Stt.SttCODFee = params.Stt.SttCODFee
	}

	for _, pieces := range params.Stt.SttPieces {
		sttCreate.SttPieces = append(sttCreate.SttPieces, model.SttPiece{
			SttPieceLength:       pieces.SttPieceLength,
			SttPieceWidth:        pieces.SttPieceWidth,
			SttPieceHeight:       pieces.SttPieceHeight,
			SttPieceGrossWeight:  pieces.SttPieceGrossWeight,
			SttPieceVolumeWeight: pieces.SttPieceVolumeWeight,
		})
	}

	sttCreate.SttOptionalRate = append(sttCreate.SttOptionalRate, model.SttOptionalRate{
		SttOptionalRateName:   params.CheckTariff.InsuranceName,
		SttOptionalRateRate:   params.CheckTariff.InsuranceRates,
		SttOptionalRateParams: model.INSURANCE,
	})

	if params.Stt.SttIsWoodpacking {
		sttCreate.SttOptionalRate = append(sttCreate.SttOptionalRate, model.SttOptionalRate{
			SttOptionalRateName:   strings.Title(model.WOODPACKING),
			SttOptionalRateRate:   params.CheckTariff.WoodpackingRates,
			SttOptionalRateParams: model.WOODPACKING,
		})
	}

	return &sttCreate
}

type CheckSaldoParams struct {
	Source         string
	AccountRefType string
	Token          string
	IsCODDelivery  bool
	Stt            *Stt
	CheckTariff    *model.CheckTariffBase

	AccountRefID int
}

type FavoriteShipmentElexysParams struct {
	ShipmentID string
	Token      string
	Source     string
}

// CreateStt ...
type CreateStt struct {
	Stt            Stt `json:"stt"`
	AccountType    string
	AccountID      int64
	AccountName    string
	Token          string
	AccountRefID   int
	AccountRefName string
	AccountRefType string
	Source         string
	Commodity      *model.Commodity
	CheckTariff    *model.CheckTariffResponse

	BookedFor           BookedActorDetail
	BookedBy            BookedActorDetail
	DestinationLocation LocationDetail
	OriginLocation      LocationDetail
	Now                 time.Time
}

type CreateSttManualReverseJourney struct {
	SttReference         *model.Stt
	ReverseJourneyActor  BookedActorDetail
	ReverseJourneyStatus string
	CustomProcess        *customProcess.InsertCustomProcessData
	SttPieceHistories    []model.SttPieceHistory
	CreateStt
	ReverseDestination model.ReverseDestination

	SttCustomFlag []SttCustomFlagRequest `json:"stt_custom_flag"`
	PartnerModel  *model.Partner
}

type SttCustomFlagRequest struct {
	ScfKey   string `json:"scf_key"`
	ScfValue string `json:"scf_value"`
}

type SttManualReverseJourneyDefaultConsoleMisroute struct {
	ReverseJourneyChargedPosID     int
	ReverseJourneyChargedConsoleID int
}

type SttManualReverseJourneyCheckSpecialCod struct {
	IsC1SpecialCOD          bool
	IsSttOriginC1SpecialCOD bool
	CodHandling             string
}

type CreateSttDetail struct {
	CreateStt *CreateStt
	SttCreate *model.SttCreate
	SttMeta   model.SttMeta
}

func (c *CreateSttManualReverseJourney) Valiedate(sttReverseJourney model.SttDetailResult) error {
	if !model.IsEligibleSttStatusReverseJourneyValid[c.ReverseJourneyStatus] {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid reverse journey status",
			"id": "Status reverse journey tidak valid",
		})
	}

	if c.AccountRefType != model.POS || c.AccountRefID <= 0 {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid Account Type format",
			"id": "Format Account Type tidak valid",
		})
	}

	if c.CreateStt.BookedFor.ID <= 0 || c.CreateStt.BookedFor.Type == `` {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Booked for is required to be filled",
			"id": "Booked for harus diisi",
		})
	}

	if sttReverseJourney.SttBookedForType != model.POS {
		if (sttReverseJourney.SttBookedForID > 0 && c.CreateStt.BookedFor.ID != sttReverseJourney.SttBookedForID) ||
			(sttReverseJourney.SttBookedForType != `` && c.CreateStt.BookedFor.Type != sttReverseJourney.SttBookedForType) {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Booked for is miss match with previous stt",
				"id": "Booked for tidak sama dengan stt sebelumnya",
			})
		}
	}

	if sttReverseJourney.SttBookedForType == model.POS {
		if (c.CreateStt.BookedFor.ID != c.AccountRefID) ||
			(c.CreateStt.BookedFor.Type != c.AccountRefType) {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Booked for is miss match",
				"id": "Booked for tidak sama",
			})
		}
	}

	if c.CustomProcess == nil && c.ReverseJourneyStatus != model.CNX {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Custom Process Data is not yet specified",
			"id": "Custom Process Data belum ditentukan",
		})
	}

	if len(c.SttPieceHistories) == 0 && c.ReverseJourneyStatus == model.CNX {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt Piece Histories Data is not yet specified",
			"id": "Stt Piece Histories Data belum ditentukan",
		})
	}

	sttMeta := sttReverseJourney.SttMetaToStruct()
	if sttMeta == nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to parse stt meta reverse journey",
			"id": "Gagal memparsing stt meta reverse journey",
		})
	}

	isPrefixReturnReverseJourney := model.IsPrefixValidReturnReverseJourney[shared.GetPrefixSttNo(sttReverseJourney.SttNo)]
	isReturnFromReroute := c.ReverseJourneyActor.Type == model.CONSOLE && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.ReverseJourneyStatusStt == model.REROUTE
	isPrefixReverseJourneyAndNotFromRerouteAndValidStatus := isPrefixReturnReverseJourney && !model.IsStatusReturnReverseJourney[c.ReverseJourneyStatus] && !isReturnFromReroute
	if isPrefixReverseJourneyAndNotFromRerouteAndValidStatus {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt can only be updated to RTSHQ",
			"id": "Stt hanya bisa di update ke RTSHQ",
		})
	}

	return nil
}

type SharedDetailParams struct {
	Stt                          model.SttDetailResult
	ReverseJourneyStatus         string
	IsPrefixReturnReverseJourney bool
	ReverseChargedPosID          int
	ReverseChargedConsoleID      int
	CodHandling                  string
	RootShipmentPrefix           string
}

type ProcessCreateReverseJourneyParams struct {
	CalculateRetailTariffRequest
	SttNo                string
	RootSttNo            string
	ReverseSttShipmentID string
	LastStatusReverse    string
	IsReverseFromRTS     bool
	RootSttIsCod         bool
	IsReverseJourney     bool
	BookedByType         string
	BookedForType        string
	RtsHQPenaltyAmount   float64
	UpdateByID           int
	UpdateByType         string
	PartnerPosParentID   int
}

// dummy stt for tracking using bag no lilo
func (s *Stt) IsSttBagLilo() bool {
	return shared.IsBagNoReff(s.SttNoRefExternal)
}

func (s *Stt) IsShipmentGoodsPriceValid(bookedClient *model.Client) bool {
	isNotTKP01 := !s.IsSttBagLilo()
	clientIsGoodsPrice := bookedClient.Data.ClientCodConfigAmount == model.GoodsPrice

	return isNotTKP01 && clientIsGoodsPrice &&
		(s.SttShipmentID == "" || (s.SttShipmentID != "" && model.IsShipmentClientCodGoodsPrice[shared.GetPrefixShipmentID(s.SttShipmentID)]))
}

func (s *Stt) IsCodFeeNotValid(bookedClient *model.Client) bool {
	isShipmentTokopediaOrPrefixLilo := (s.SttShipmentID != "" && model.IsShipmentTokopedia[shared.GetPrefixShipmentID(s.SttShipmentID)]) || (s.SttNoRefExternal != "" && shared.IsLiloPrefix(s.SttNoRefExternal))
	if !s.SttIsCOD || isShipmentTokopediaOrPrefixLilo {
		return false
	}

	shipmentGoodsPriceValid := s.IsShipmentGoodsPriceValid(bookedClient)
	codValueIsNotTheSame := s.SttCODAmount != s.SttGoodsEstimatePrice

	return s.SttCODFee < 0 || s.SttCODAmount < 1 || s.SttGoodsEstimatePrice < 1 || (!shipmentGoodsPriceValid && codValueIsNotTheSame)
}

func (s *Stt) IsCodUnderMinimumCodAmount() bool {
	isCod := s.SttIsCOD && !s.SttIsDFOD
	if isCod && s.SttCODAmount < 10000 {
		return true
	}
	return false
}

func (c *Stt) validateSttInterTaxNumber() error {
	if c.SttInterTaxNumber != "" {
		if err := validation.Validate(c.SttInterTaxNumber, validation.Match(shared.AlphaNumericRegex)); err != nil {
            return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
                "en": "The Tax ID can only contain numeric digits and alphabet",
                "id": "Nomor registrasi pajak hanya dapat diisi oleh angka dan huruf",
            })
        }
		if len(c.SttInterTaxNumber) > 100 {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "The maximum length of the Tax ID is 100 characters",
				"id": "Maksimum panjang nomor registrasi pajak adalah 100 karakter",
			})
		}
	}
	return nil
}

func (c *Stt) validateSttPieces() error {
	if err := validation.Validate(len(c.SttPieces), validation.Required, validation.Min(model.MIN_PIECE_NUMBER)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("Total pieces cannot be less than %d", model.MIN_PIECE_NUMBER),
			"id": fmt.Sprintf("Total pieces tidak boleh kurang dari %d", model.MIN_PIECE_NUMBER),
		})
	}

	if err := validation.Validate(len(c.SttPieces), validation.Required, validation.Max(model.MAX_PIECE_NUMBER)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("Total pieces cannot be more than %d", model.MAX_PIECE_NUMBER),
			"id": fmt.Sprintf("Total pieces tidak boleh lebih dari %d", model.MAX_PIECE_NUMBER),
		})
	}

	for i, piece := range c.SttPieces {

		if err := validation.Validate(piece.SttPieceGrossWeight, validation.Required, validation.Min(model.MIN_WEIGHT_NEW)); err != nil {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Pieces Gross Weight must be higher than 0",
				"id": "Berat Kotor Pieces harus lebih besar dari 0",
			})
		}

		if err := validation.Validate(piece.SttPieceGrossWeight, validation.Required, validation.Max(model.MAX_GROSS_WEIGHT_PER_PIECE)); err != nil {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": fmt.Sprintf("Pieces Gross Weight must be less than %.f", model.MAX_GROSS_WEIGHT_PER_PIECE),
				"id": fmt.Sprintf("Berat Kotor Pieces harus kurang dari %.f", model.MAX_GROSS_WEIGHT_PER_PIECE),
			})
		}

		if err := validation.Validate(piece.SttPieceHeight, validation.Required, validation.Min(0.1)); err != nil {
			c.SttPieces[i].SttPieceHeight = shared.HeightDefault
		}

		if err := validation.Validate(piece.SttPieceWidth, validation.Required, validation.Min(0.1)); err != nil {
			c.SttPieces[i].SttPieceWidth = shared.WidthDefault
		}

		if err := validation.Validate(piece.SttPieceLength, validation.Required, validation.Min(0.1)); err != nil {
			c.SttPieces[i].SttPieceLength = shared.LenghtDefault
		}

		volumeWeight := shared.CalculateVolumeWeight(c.SttPieces[i].SttPieceLength, c.SttPieces[i].SttPieceWidth, c.SttPieces[i].SttPieceHeight, c.SttProductType)
		// TODO enable this validation on 15 oct
		// if err := validation.Validate(volumeWeight, validation.Required, validation.Min(model.MIN_VOLUME_WEIGHT)); err != nil {
		// 	return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
		// 		"en": "Pieces Volume Weight must be higher than 0.01",
		// 		"id": "Berat Volume Pieces harus lebih besar dari 0.01",
		// 	})
		// }

		if err := validation.Validate(volumeWeight, validation.Required, validation.Max(model.MAX_VOLUME_WEIGHT_PER_PIECE)); err != nil {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": fmt.Sprintf("Pieces Volume Weight must be less than %.f", model.MAX_VOLUME_WEIGHT_PER_PIECE),
				"id": fmt.Sprintf("Berat Volume Pieces harus kurang dari %.f", model.MAX_VOLUME_WEIGHT_PER_PIECE),
			})
		}

		c.SttPieces[i].SttPieceVolumeWeight = volumeWeight
	}

	c.SttTotalPiece = len(c.SttPieces)

	return nil
}

func (c *Stt) validateCIPL() error {
	if len(c.SttCIPL) > 15 || len(c.SttFtzCIPL) > 15 {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "The maximum number of items in a CIPL is 15 items",
			"id": "Jumlah item CIPL maksimum adalah 15 item",
		})
	}

	validationCIPL := func(items *[]model.SttCIPL) error {
		for i := range *items {
			(*items)[i].ItemDetail = strings.TrimSpace((*items)[i].ItemDetail)
			(*items)[i].ItemDetailEn = strings.TrimSpace((*items)[i].ItemDetailEn)
			(*items)[i].CommodityHsCode = strings.TrimSpace((*items)[i].CommodityHsCode)

			if (*items)[i].ItemDetail == "" {
				return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Item detail required",
					"id": "Detail barang wajib diisi",
				})
			}

			if (*items)[i].Quantity <= 0 {
				return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Quantity required",
					"id": "Kuantitas wajib diisi",
				})
			}

			if (*items)[i].ItemPrice <= 0 {
				return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Good price required",
					"id": "Harga barang wajib diisi",
				})
			}

		}
		return nil
	}

	if err := validationCIPL(&c.SttCIPL); err != nil {
		return err
	}

	if err := validationCIPL(&c.SttFtzCIPL); err != nil {
		return err
	}

	return nil
}

func (c *Stt) validateSttIdentityNumber() error {
	if c.SttIdentityNumber != "" {
		if err := validation.Validate(c.SttIdentityNumber, validation.Match(shared.AlphaNumericRegex)); err != nil {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "The Identity number can only contain numeric digits and alphabet",
				"id": "Nomor identitas hanya dapat diisi oleh angka dan huruf",
			})
		}
		if len(c.SttIdentityNumber) > 100 {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "The maximum length of the identification number is 100 characters",
				"id": "Maksimum panjang nomor identitas adalah 100 karakter",
			})
		}
	}
	return nil
}
