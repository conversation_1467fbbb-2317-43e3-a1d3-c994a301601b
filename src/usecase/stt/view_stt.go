package stt

import (
	"strings"
	"time"

	"github.com/abiewardani/dbr/v2"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/usecase/general"

	validation "github.com/go-ozzo/ozzo-validation/v4"

	"github.com/Lionparcel/hydra/src/model"
)

// ViewSttRequest ...
type ViewSttRequest struct {
	Search                string   `json:"search"`
	SearchLike            string   `json:"search_like"`
	BookedStart           string   `json:"booked_start"`
	BookedEnd             string   `json:"booked_end"`
	OriginDistrictID      string   `json:"origin_district_id"`
	DestinationDistrictID string   `json:"destination_district_id"`
	ClientPartnerID       string   `json:"client_partner_id"`
	ProductType           string   `json:"product_type"`
	Status                string   `json:"status"`
	IsCod                 bool     `json:"is_cod"`
	IsDfod                bool     `json:"is_dfod"`
	InsuranceType         string   `json:"insurance_type"`
	InsuranceTypeArray    []string `json:"insurance_type_array"`
	ProductTypeArray      []string `json:"product_type_array"`
	OriginCityID          string   `json:"origin_city_id"`
	DestinationCityID     string   `json:"destination_city_id"`
	OrderBy               string   `json:"order_by"`
	SortBy                string   `json:"sort_by"`
	Limit                 int      `json:"limit"`
	Offset                int      `json:"offset"`
	Page                  int      `json:"page"`
	NoLimit               bool     `json:"no_limit"`
	Token                 string   `json:"token"`
	IsTotalData           bool     `json:"is_total_data"`
	PaymentStatus         string   `json:"payment_status"`
	MinPayDate            string   `json:"min_pay_date"`
	MaxPayDate            string   `json:"max_pay_date"`
	IsPaidUnpaid          bool     `json:"is_paid_unpaid"`
	SenderPhoneNumber     string   `json:"sender_phone_number"`
	RecipientPhoneNumber  string   `json:"recipient_phone_number"`
	AccountType           string   `json:"account_type"`
}

// ViewSttResponse ...
type ViewSttResponse struct {
	SttID                       int64             `json:"stt_id"`
	SttNo                       string            `json:"stt_no"`
	SttShipmentID               string            `json:"stt_shipment_id"`
	SttRecipientAddress         string            `json:"stt_recipient_address"`
	SttCommodityCode            string            `json:"stt_commodity_code"`
	SttCommodityName            string            `json:"stt_commodity_name"`
	SttClientID                 int               `json:"stt_client_id"`
	SttPosID                    int               `json:"stt_pos_id"`
	SttIsCOD                    bool              `json:"stt_is_cod"`
	SttIsDFOD                   bool              `json:"stt_is_dfod"`
	SttProductType              string            `json:"stt_product_type"`
	SttChargeableWeight         float64           `json:"stt_chargeable_weight"`
	SttTotalPiece               int               `json:"stt_total_piece"`
	SttLastStatusID             string            `json:"stt_last_status_id"`
	SttWarningStatus            string            `json:"stt_warning_status"`
	SttLastStatusIDDescription  string            `json:"stt_last_status_id_description"`
	SttWarningStatusDescription string            `json:"stt_warning_status_description"`
	SttCounter                  int               `json:"stt_counter"`
	SttInsuranceType            string            `json:"stt_insurance_type"`
	SttOriginCity               string            `json:"stt_origin_city"`
	SttDestinationCity          string            `json:"stt_destination_city"`
	SttTaxNumber                string            `json:"stt_tax_number"`
	SttHsCode                   string            `json:"stt_hs_code"`
	SttBookedName               string            `json:"stt_booked_name"`
	SttBilledTo                 string            `json:"stt_billed_to"`
	SttCreatedName              string            `json:"stt_created_name"`
	SttCreatedAt                time.Time         `json:"stt_created_at"`
	SttUpdatedAt                time.Time         `json:"stt_updated_at"`
	SttElexys                   general.SttElexys `json:"stt_elexys"`
	SttPaymentStatus            string            `json:"stt_payment_status"`
	SttPaymentDateAt            dbr.NullTime      `json:"stt_payment_date_at"`
	SttUpdatedByName            string            `json:"stt_updated_by_name"`
	SttDestinationCityName      string            `json:"stt_destination_city_name"`
	SttOriginCityName           string            `json:"stt_origin_city_name"`
	SttDestinationDistrictName  string            `json:"stt_destination_district_name"`
	SttOriginDistrictName       string            `json:"stt_origin_district_name"`
	TotalSTTDex                 int               `json:"total_stt_dex"`
	DexCODRejReason             dbr.NullString    `json:"dex_codrej_reason"`
	SttAssessmentStatus         string            `json:"stt_assessment_status"`
	SttSenderName               string            `json:"stt_sender_name"`
	IsNewForm                   bool              `json:"is_new_form"`
	SttBookedForCode            string            `json:"stt_booked_for_code"`
    SttBookedForName            string            `json:"stt_booked_for_name"`
    SttRecipientName            string            `json:"stt_recipient_name"`
}

type MapViewSttResponseRequest struct {
	Stt                model.Stt
	SttCommodityName   string
	SttCommodityHsCode string
	SttDex             map[string]int
	SttOptionalRates   []model.SttOptionalRate
	SttCustomFlag      model.SttCustomFlag
}

func MapViewSttResponse(req MapViewSttResponseRequest) ViewSttResponse {
	sttLastStatusIDDescription := ``
	sttWarningStatusDescription := ``
	for i, v := range model.SttStatus {
		if strings.ToUpper(req.Stt.SttLastStatusID) == i {
			sttLastStatusIDDescription = v
		}
		if req.Stt.SttWarningStatus != `` {
			sttWarningStatusDescription = "Kesalahan pada rute Kota Tujuan atau paket berada di Kota Tujuan yang salah"
		}
	}
	response := ViewSttResponse{
		SttID:                       req.Stt.SttID,
		SttNo:                       req.Stt.SttNo,
		SttShipmentID:               req.Stt.SttShipmentID,
		SttOriginCity:               req.Stt.SttOriginCityID,
		SttDestinationCity:          req.Stt.SttDestinationCityID,
		SttRecipientAddress:         req.Stt.SttRecipientAddress,
		SttCommodityCode:            req.Stt.SttCommodityCode,
		SttCommodityName:            req.SttCommodityName,
		SttClientID:                 req.Stt.SttClientID,
		SttPosID:                    req.Stt.SttPosID,
		SttIsCOD:                    req.Stt.SttIsCOD,
		SttIsDFOD:                   req.Stt.SttIsDFOD,
		SttProductType:              req.Stt.SttProductType,
		SttChargeableWeight:         req.Stt.SttChargeableWeight,
		SttTotalPiece:               req.Stt.SttTotalPiece,
		SttLastStatusID:             req.Stt.SttLastStatusID,
		SttWarningStatus:            req.Stt.SttWarningStatus,
		SttLastStatusIDDescription:  sttLastStatusIDDescription,
		SttWarningStatusDescription: sttWarningStatusDescription,
		SttInsuranceType:            model.GetInsuranceTypeForSttOptionalRates(req.Stt.SttInsuranceType, req.SttOptionalRates),
		SttCounter:                  req.Stt.SttCounter,
		SttHsCode:                   req.SttCommodityHsCode,
		SttTaxNumber:                req.Stt.SttTaxNumber,
		SttBookedName:               req.Stt.SttBookedName,
		SttBilledTo:                 req.Stt.SttBilledTo,
		SttCreatedName:              req.Stt.SttCreatedName,
		SttCreatedAt:                req.Stt.SttCreatedAt,
		SttUpdatedAt:                req.Stt.SttUpdatedAt,
		SttPaymentStatus:            req.Stt.SttPaymentStatus,
		SttPaymentDateAt:            req.Stt.SttPaymentDateAt,
		SttUpdatedByName:            req.Stt.SttUpdatedName,
		SttDestinationCityName:      req.Stt.SttDestinationCityName,
		SttOriginCityName:           req.Stt.SttOriginCityName,
		SttOriginDistrictName:       req.Stt.SttOriginDistrictName,
		SttDestinationDistrictName:  req.Stt.SttDestinationDistrictName,
		TotalSTTDex:                 req.SttDex[req.Stt.SttNo],
		SttAssessmentStatus:         req.Stt.SttAssessmentStatus.String,
		SttSenderName:               req.Stt.SttSenderName,
		IsNewForm:                   req.SttCustomFlag.GetSttCustomFlagValue(),
		SttBookedForCode:            req.Stt.SttBookedForCode,
		SttBookedForName:            req.Stt.SttBookedForName,
		SttRecipientName:            req.Stt.SttRecipientName,
	}
	return response
}

type ViewSttForManifestRequest struct {
	Date            string `json:"date" form:"date" query:"date"`
	ProductType     string `json:"product_type" form:"product_type" query:"product_type"`
	Status          string `json:"status" form:"status" query:"status"`
	SortBy          string `json:"sort_by" form:"sort_by" query:"sort_by"`
	Page            int    `json:"page" form:"page" query:"page"`
	Limit           int    `json:"limit" form:"limit" query:"limit"`
	AccountReffID   int    `json:"-"`
	AccountReffType string `json:"-"`
	Token           string `json:"-"`
	StatusIn        []string
}

type ViewSttForManifestResponse struct {
	SttNo            string    `json:"stt_no"`
	ElexysSttNo      string    `json:"elexys_stt_no"`
	ProductType      string    `json:"product_type"`
	TotalPieces      int       `json:"total_pieces"`
	GrossWeight      float64   `json:"gross_weight"`
	VolumeWeight     float64   `json:"volume_weight"`
	ChargeableWeight float64   `json:"chargeable_weight"`
	Status           string    `json:"status"`
	CreatedAt        time.Time `json:"created_at"`
	SttNoRefExternal string    `json:"stt_no_ref_external"`
	SttShipmentID    string    `json:"stt_shipment_id"`
	BookingID        string    `json:"booking_id"`
}

// Validate ...
func (c *ViewSttRequest) Validate() error {
	c.Search = strings.TrimSpace(c.Search)
	var (
		arrayInsuranse, arrayProduct []string
		minPayDate, maxPayDate       time.Time
		err                          error
	)

	if c.ProductType != "" {
		arrayProduct = strings.Split(c.ProductType, ",")
	}

	if c.InsuranceType != "" {
		arrayInsuranse = strings.Split(c.InsuranceType, ",")
	}

	if c.MinPayDate != "" {
		minPayDate, err = shared.ParseUTC7(shared.FormatDate, c.MinPayDate)
		if err != nil {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid min pay date format",
				"id": "Invalid format min pay date",
			})
		}
	}

	if c.MaxPayDate != `` {
		maxPayDate, err = shared.ParseUTC7(shared.FormatDate, c.MaxPayDate)
		if err != nil {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid max pay date format",
				"id": "Invalid format max pay date",
			})
		}
	}

	if c.MinPayDate != "" && c.MaxPayDate != `` {
		if maxPayDate.Before(minPayDate) {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Max date cannot less than the from min date",
				"id": "Tanggal maksimal tidak boleh kurang dari tanggal minimal",
			})
		}
	}

	c.InsuranceTypeArray = arrayInsuranse
	c.ProductTypeArray = arrayProduct

	if err := validation.Validate(c.Limit, validation.Min(1), validation.Required); err != nil {
		c.Limit = 10
	}

	if !c.NoLimit {
		if err := validation.Validate(c.Limit, validation.Max(10), validation.Required); err != nil {
			c.Limit = 10
		}
	}

	if err := validation.Validate(c.Page, validation.Min(1), validation.Required); err != nil {
		c.Page = model.DefaultPage
	}
	if !c.IsPaidUnpaid {
		c.OrderBy = `stt_created_at`
	}

	c.SortBy = strings.ToUpper(strings.TrimSpace(c.SortBy))
	if c.SortBy != `` {
		if err := validation.Validate(c.SortBy, validation.In(model.SortByAsc, model.SortByDesc)); err != nil {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid sort by",
				"id": "Sort By tidak valid",
			})
		}
	}

	return nil
}

func (c *ViewSttForManifestRequest) Validate() error {
	if err := validation.Validate(c.Date, validation.Required); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Date cannot be empty",
			"id": "Date tidak boleh kosong",
		})
	}

	if err := validation.Validate(c.Date, validation.Date(shared.FormatDate)); err != nil {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Wrong date format",
			"id": "Format tanggal salah",
		})
	}

	now, _ := shared.ParseUTC7(shared.FormatDate, time.Now().Format(shared.FormatDate))
	date, _ := shared.ParseUTC7(shared.FormatDate, c.Date)
	if date.After(now) {
		return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Date cannot be later than today",
			"id": "Date tidak boleh lebih dari hari ini",
		})
	}

	if c.SortBy != `` {
		c.SortBy = strings.ToUpper(strings.TrimSpace(c.SortBy))
		if err := validation.Validate(c.SortBy, validation.In(model.SortByAsc, model.SortByDesc)); err != nil {
			return shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid sort by",
				"id": "Sort By tidak valid",
			})
		}
	} else {
		c.SortBy = model.SortByAsc
	}

	c.Status = strings.ToUpper(strings.TrimSpace(c.Status))
	if c.Status != `` {
		c.StatusIn = strings.Split(c.Status, ",")
	}

	c.ProductType = strings.TrimSpace(c.ProductType)

	if c.Limit <= 0 {
		c.Limit = 500 // default max process pickup manifest
	}

	if c.Page <= 0 {
		c.Page = 1 // default page process pickup manifest
	}

	return nil
}

type GetViewSttResponseParams struct {
	Data                []model.Stt
	MapSttOptionalRates map[int64][]model.SttOptionalRate
	MapSttCustomFlags   map[int64]model.SttCustomFlag
	SttDex              map[string]int
	Token               string
}
