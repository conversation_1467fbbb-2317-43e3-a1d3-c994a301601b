package stt

import (
	"time"

	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/general"
)

type SttDetailResponse struct {
	SttID         int64  `json:"stt_id"`
	SttNo         string `json:"stt_no"`
	SttShipmentID string `json:"stt_shipment_id"`

	SttClient *model.ClientBase `json:"stt_client"`

	SttOriginAddress          string `json:"stt_origin_address"`
	SttOriginCityID           string `json:"stt_origin_city_id"`
	SttOriginCityName         string `json:"stt_origin_city_name"`
	SttOriginCity             int    `json:"stt_origin_city"`
	SttOriginDistrictID       string `json:"stt_origin_district_id"`
	SttOriginDistrictName     string `json:"stt_origin_district_name"`
	SttOriginDistrictUrsaCode string `json:"stt_origin_district_ursa_code"`

	SttDestinationAddress          string `json:"stt_destination_address"`
	SttDestinationCityID           string `json:"stt_destination_city_id"`
	SttDestinationCityName         string `json:"stt_destination_city_name"`
	SttDestinationCity             int    `json:"stt_destination_city"`
	SttDestinationDistrictID       string `json:"stt_destination_district_id"`
	SttDestinationDistrictName     string `json:"stt_destination_district_name"`
	SttDestinationDistrictUrsaCode string `json:"stt_destination_district_ursa_code"`

	SttNoRefExternal string `json:"stt_no_ref_external"`

	SttSenderName    string `json:"stt_sender_name"`
	SttSenderPhone   string `json:"stt_sender_phone" `
	SttSenderAddress string `json:"stt_sender_address"`

	SttRecipientName    string `json:"stt_recipient_name"`
	SttRecipientPhone   string `json:"stt_recipient_phone"`
	SttRecipientAddress string `json:"stt_recipient_address"`

	SttCommodityID           int     `json:"stt_commodity_id"`
	SttCommodityName         string  `json:"stt_commodity_name"`
	SttCommodityCode         string  `json:"stt_commodity_code"`
	SttProductTypeName       string  `json:"stt_product_type_name"`
	SttTotalPiece            int     `json:"stt_total_piece"`
	SttTotalGross            float64 `json:"stt_total_gross"`
	SttTotalVolume           float64 `json:"stt_total_volume"`
	SttTotalChargeableWeight float64 `json:"stt_total_chargeable_weight"`
	SttGoodsEstimatePrice    float64 `json:"stt_goods_estimate_price"`
	SttTaxNumber             string  `json:"stt_tax_number"`
	SttInsuranceName         string  `json:"stt_insurance_name"`
	SttBookedBy              string  `json:"stt_booked_by"`
	SttBookedFor             string  `json:"stt_booked_for"`
	SttBilledTo              string  `json:"stt_billed_to"`
	SttSource                string  `json:"stt_source"`
	SttCOD                   string  `json:"stt_cod"`
	SttReturnDO              string  `json:"stt_return_do"`

	SttCODAmount                float64 `json:"stt_cod_amount"`
	SttCODFee                   float64 `json:"stt_cod_fee"`
	SttShipmentPrice            float64 `json:"stt_shipment_price"`
	SttShippingSurchargeRate    float64 `json:"stt_shipping_surcharge_rate"`
	SttDocumentSurchargeRate    float64 `json:"stt_document_surcharge_rate"`
	SttCommoditySurchargeRate   float64 `json:"stt_commodity_surcharge_rate"`
	SttHeavyweightSurchargeRate float64 `json:"stt_heavyweight_surcharge_rate"`
	SttInsuranceRate            float64 `json:"stt_insurance_rate"`
	SttWoodpackingRate          float64 `json:"stt_woodpacking_rate"`
	SttTaxRate                  float64 `json:"stt_tax_rate"`
	SttCityRate                 float64 `json:"stt_city_rate"`
	SttForwardRate              float64 `json:"stt_forward_rate"`

	SttReasonMapping *model.Reason `json:"stt_reason_mapping"`
	SttTotalTariff   float64       `json:"stt_total_tariff"`

	SttPieces                   []SttPieces `json:"stt_piece"`
	SttSenderStatus             string      `json:"stt_sender_status"`
	SttLastStatus               string      `json:"stt_last_status"`
	SttLastStatusDescription    string      `json:"stt_last_status_description"`
	SttLastStatusCounter        int         `json:"stt_last_status_counter"`
	SttWarningStatus            string      `json:"stt_warning_status"`
	SttWarningStatusDescription string      `json:"stt_warning_status_description"`
	SttCreatedAt                time.Time   `json:"stt_created_at"`
	SttCreatedBy                string      `json:"stt_created_by"`
	SttUpdatedAt                time.Time   `json:"stt_updated_at"`
	SttUpdatedBy                string      `json:"stt_updated_by"`

	SttIsAllowEdit   bool `json:"stt_is_allow_edit"`
	SttIsAllowCancel bool `json:"stt_is_allow_cancel"`

	SttNextCommodity string `json:"stt_next_commodity"`
	SttPiecePerPack  int    `json:"stt_piece_per_pack"`

	SttElexys general.SttElexys `json:"stt_elexys"`

	SttBookedById    int    `json:"stt_booked_by_id"`
	SttBookedByType  string `json:"stt_booked_by_type"`
	SttBookedByCode  string `json:"stt_booked_by_code"`
	SttBookedForId   int    `json:"stt_booked_for_id"`
	SttBookedForType string `json:"stt_booked_for_type"`
	SttBookedForCode string `json:"stt_booked_for_code"`

	SttRecipientAddressType string `json:"stt_recipient_address_type"`

	CheckoutPaymentMethod string `json:"checkout_payment_method"`
	PostalCodeDestination string `json:"postal_code_destination"`
	LastStatusSttReturn   string `json:"last_status_stt_return"`

	// retail promo response
	IsPromo                   bool    `json:"stt_is_promo"`
	SttDiscount               float64 `json:"stt_discount"`
	TotalDiscount             float64 `json:"stt_total_discount"`
	TotalTariffAfterDiscount  float64 `json:"stt_total_tariff_after_discount"`
	TotalTariffBeforeDiscount float64 `json:"stt_total_tariff_before_discount"`

	SttRegionID   string `json:"stt_region_id"`
	SttRegionName string `json:"stt_region_name"`

	SttReturnFee float64 `json:"stt_return_fee"`
	CodHandling  string  `json:"cod_handling"`

	SttIsDFOD bool `json:"stt_is_dfod"`
	SttIsCOD  bool `json:"stt_is_cod"`

	//promo discount favorite AP/AS shipment
	SttDiscountFavoritePercentage float64 `json:"stt_discount_favorite_percentage"`

	DetailSttReverseJourney *DetailSttReverseJourney `json:"detail_stt_reverse_journey,omitempty"`

	SttCommodityDetail   string                `json:"stt_commodity_detail"`
	SttBookedByCountry   string                `json:"stt_booked_by_country"`
	SttIsWoodPacking     bool                  `json:"stt_is_woodpacking"`
	SttAttachFiles       []string              `json:"stt_attach_files"`
	SttInsuranceAdminFee float64               `json:"stt_insurance_admin_fee"`
	ListDiscount         []model.PromoDiscount `json:"list_discount"`

	SttWeightAttachFiles []string `json:"stt_weight_attach_files"`
	RefundRemarks        string   `json:"refund_remarks"`
	SttRecipientEmail    string   `json:"stt_recipient_email"`
	SttKtpImage          string   `json:"stt_ktp_image"`
	SttTaxImage          string   `json:"stt_tax_image"`
	GoodsNames           []string `json:"goods_names"`

	SttIdentityNumber        string `json:"stt_identity_number"`
	SttInterTaxNumber        string `json:"stt_inter_tax_number"`
	SttCommodityIsQuarantine bool   `json:"stt_commodity_is_quarantine"`
	IsDangerousGoods         bool   `json:"is_dangerous_goods"`
	IsNewForm                bool   `json:"is_new_form"`

	SttCipl              []model.SttCIPL `json:"stt_cipl"`
	SttFtzCipl           []model.SttCIPL `json:"stt_ftz_cipl"`
	SttFtzIdentityNumber string          `json:"stt_ftz_identity_number"`
	SttFtzAttachFiles    []string        `json:"stt_ftz_attach_files"`
	SttFtzRecipientEmail string          `json:"stt_ftz_recipient_email"`
	SttFtzKtpImage       string          `json:"stt_ftz_ktp_image"`
	SttFtzTaxImage       string          `json:"stt_ftz_tax_image"`
}

type DetailSttReverseJourney struct {
	RootReverseSttNo         string `json:"root_reverse_stt_no"`
	RootReverseShipmentID    string `json:"root_reverse_stt_shipment_id"`
	RootReverseLastStatusStt string `json:"root_reverse_stt_last_status_id"`
	CodHandling              string `json:"cod_handling"`
}

type SttDetailRequest struct {
	SttID             int
	AccountType       string
	AccountRefID      int
	AccountRefType    string
	IsWithEligibility bool `json:"is_with_eligibility" query:"is_with_eligibility" form:"is_with_eligibility"`
	IsAuth            bool `json:"is_auth" query:"is_auth" form:"is_auth"`
	Token             string
	AccountRoleName   string
}

func (s *SttDetailResponse) GetSttWeightAttachFiles(sttWeightAttachFiles []string) []string {
	if len(sttWeightAttachFiles) > 0 {
		return sttWeightAttachFiles
	}
	return []string{}
}
