package stt

import "testing"

func TestValidateRequest_Validate(t *testing.T) {
	type fields struct {
		ValidateType  string
		NoRefExternal string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		{
			name: "test no ref external must be filled",
			fields: fields{
				ValidateType:  "no_ref_external",
				NoRefExternal: "",
			},
			wantErr: true,
		},
		{
			name: "test no ref external minimum length is 3",
			fields: fields{
				ValidateType:  "no_ref_external",
				NoRefExternal: "a",
			},
			wantErr: true,
		},
		{

			name: "test no ref external valid",
			fields: fields{
				ValidateType:  "no_ref_external",
				NoRefExternal: "abc",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ValidateRequest{
				ValidateType:  tt.fields.ValidateType,
				NoRefExternal: tt.fields.NoRefExternal,
			}
			if err := c.Validate(); (err != nil) != tt.wantErr {
				t.<PERSON><PERSON>("ValidateRequest.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
