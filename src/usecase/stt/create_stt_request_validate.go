package stt

import (
	"github.com/Lionparcel/hydra/shared"
	validation "github.com/go-ozzo/ozzo-validation/v4"
	"regexp"
	"strings"
)

func (c *Stt) validatePostalCode() error {
	postalCodeDestination, err := setAndValidateDataPostalCode(c.PostalCodeDestination)
	if err != nil {
		return err
	}
	c.PostalCodeDestination = postalCodeDestination

	postalCodeOrigin, err := setAndValidateDataPostalCode(c.PostalCodeOrigin)
	if err != nil {
		return err
	}
	c.PostalCodeOrigin = postalCodeOrigin

	return nil
}

func (c *SttCIPL) Validate() error {
	itemDetail := strings.TrimSpace(c.ItemDetail)
	if err := validation.Validate(itemDetail, validation.Required); err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Item detail required",
			"id": "Detail barang wajib diisi",
		})
	}
	if err := validation.Validate(c.Quantity, validation.Required); err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Quantity required",
			"id": "Kuantitas wajib diisi",
		})
	}
	if err := validation.Validate(c.ItemPrice, validation.Required); err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Good price required",
			"id": "Harga barang wajib diisi",
		})
	}
	return nil
}

func validateItemsCIPL(data []SttCIPL) error {
	if len(data) == 0 {
		return nil
	}
	for _, cipl := range data {
		if err := cipl.Validate(); err != nil {
			return err
		}
	}
	return nil
}

func setAndValidateDataPostalCode(dataPostalCode string) (postalCode string, err error) {
	if len(dataPostalCode) == 0 {
		return
	}
	isMatch, err := regexp.MatchString(`^[A-Z0-9-,]*$`, dataPostalCode)
	if err != nil || !isMatch {
		return postalCode, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": "Invalid Postal Code",
			"id": "Postal Code Tidak Valid",
		})
	}

	// set default first zip code if the input is multiple
	arrPostalCodeDestination := strings.Split(dataPostalCode, ",")
	if len(arrPostalCodeDestination) > 1 {
		postalCode = arrPostalCodeDestination[0]
	}
	return strings.TrimSpace(postalCode), nil
}
