package stt

import (
	"fmt"

	validation "github.com/go-ozzo/ozzo-validation/v4"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
)

type ValidateRequest struct {
	ValidateType  string `json:"validate_type"`
	NoRefExternal string `json:"no_ref_external"`
}

type ValidateResponse struct {
	IsAllowBooking bool   `json:"is_allow_booking"`
	ErrorMessage   string `json:"error_message"`
}

var allowedValidationType = map[string]bool{
	"no_ref_external": true,
}

func (c *ValidateRequest) Validate() error {

	if err := validation.Validate(c.ValidateType, validation.Required); err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Validate Type is required to be filled",
			"id": "Tipe validasi harus diisi",
		})
	}

	if !allowedValidationType[c.ValidateType] {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Validate Type is not allowed",
			"id": "Tipe validasi tidak diizinkan",
		})
	} else {
		if err := validation.Validate(c.NoRefExternal, validation.Required); err != nil {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "External Reference Number is required to be filled",
				"id": "Nomor Referensi Eksternal harus diisi",
			})
		}

		if err := validation.Validate(len(c.NoRefExternal), validation.Min(3)); err != nil {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "The minimum length for an external reference number is 3 characters.",
				"id": "Minimum nomor referensi eksternal adalah 3 karakter",
			})
		}

		if err := validation.Validate(len(c.NoRefExternal), validation.Max(model.MAX_LENGTH_EXT_REF_NO)); err != nil && c.NoRefExternal != `` {
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": fmt.Sprintf("External Reference Number length must lower than or equal to %d ", model.MAX_LENGTH_EXT_REF_NO),
				"id": fmt.Sprintf("Panjang External Reference Number harus dibawah atau sama dengan %d", model.MAX_LENGTH_EXT_REF_NO),
			})
		}
	}

	return nil
}

