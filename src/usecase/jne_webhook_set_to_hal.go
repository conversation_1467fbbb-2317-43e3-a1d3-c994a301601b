package usecase

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/Lionparcel/go-lptool/v2/lputils"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	customProcess "github.com/Lionparcel/hydra/src/usecase/custom_process"
	"github.com/Lionparcel/hydra/src/usecase/jne"
	"github.com/Lionparcel/hydra/src/usecase/stt_activity"
)

func (c *jneCtx) setToHal(ctx context.Context, params *jne.JneWebhookRequest, sttDetail []model.SttDetailResult) (err error) {
	opName := "UsecaseJNE-setToHal"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": err})

		go lputils.TrackGoroutine(func(goCtx context.Context) {
			c.partnerLog.Insert(goCtx, &model.PartnerLog{
				Action:  model.PLSttJNEHal,
				RefID:   params.OrderID,
				Request: params,
				Response: func() interface{} {
					if err != nil {
						return err.Error()
					}
					return jne.JneWebhookResponse{Success: true}
				}(),
			})
		})
	}()

	// Stt Detail
	sttRow := sttDetail[0]

	// sttData := map[string]customProcess.SttData{}
	now, _ := shared.ParseUTC7(shared.FormatDateTime, c.timeRepo.Now(time.Now()).Format(shared.FormatDateTime))
	sttHistories := []customProcess.SttHistory{}
	// for _, sttDetail := range sttDetails {
	if model.IsNotAllowUpdateCustomProcess[sttRow.SttLastStatusID] || sttRow.SttLastStatusID == params.SttStatus {
		err = errors.New("Stt Status not allowed")
		return
	}

	/**
	 * Trucking status cannot updated to SCRAP
	 */
	if model.TruckingStatusMapping[sttRow.SttLastStatusID] && model.IsNotAllowedCustomProcessStatusForLastStatusTrucking[params.SttStatus] {
		err = errors.New("Stt Status not allowed")
		return
	}

	if model.IsNotAllowUpdateToHAL[sttRow.SttLastStatusID] {
		err = errors.New("Stt Status not allowed")
		return
	}

	//  update stt to Hal
	accountToken, err := c.repoAccount.GetTokenGenesis(selfCtx)
	if err != nil || accountToken == nil {
		err = shared.ErrUnexpected
		return
	}

	// get detail destination stt
	cityDestination, errCity := c.repoCity.Get(selfCtx, sttRow.Stt.SttDestinationCityID, accountToken.Data.Token)
	if errCity != nil || cityDestination == nil {
		err = errors.New("City destination is not found")
		return
	}

	sttGrossWeight := 0.0
	sttVolumeWeight := 0.0
	sttChargeableWeight := 0.0
	listSttUpdateTime := []stt_activity.SttActivityRequestDetail{}
	for _, val := range sttDetail {
		sttHistories = append(sttHistories, customProcess.SttHistory{
			SttHistory: model.SttPieceHistory{
				SttPieceID:       val.SttPiece.SttPieceID,
				HistoryStatus:    model.HAL,
				HistoryLocation:  val.SttDestinationCityID,
				HistoryActorID:   model.AccountJNE.ActorID,
				HistoryActorName: model.AccountJNE.ActorName,
				HistoryActorRole: model.VENDOR,
				HistoryCreatedAt: now,
				HistoryCreatedBy: model.AccountJNE.ActorID,

				HistoryCreatedName: model.AccountJNE.ActorName,
			},
			SttID: val.Stt.SttID,
		})
		sttGrossWeight += val.SttGrossWeight
		sttVolumeWeight += val.SttVolumeWeight
		sttChargeableWeight += val.SttChargeableWeight

		// Define variable for update status time
		listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
			SttNo:         sttRow.SttNo,
			SttStatus:     params.SttStatus,
			SttStatusTime: now,
		})
	}

	// if totalSttSuccess != 0 {
	createCustomProcess, err := c.customProcessRepo.Create(selfCtx, &customProcess.InsertCustomProcessData{
		CustomProcess: &model.CustomProcess{
			CustomProcessTotalSTT:              1,
			CustomProcessTotalPiece:            len(sttHistories),
			CustomProcessTotalGrossWeight:      sttGrossWeight,
			CustomProcessTotalVolumeWeight:     sttVolumeWeight,
			CustomProcessTotalChargeableWeight: sttChargeableWeight,
			CustomProcessLatestStatus:          model.HAL,
			CustomProcessCreatedAt:             now,
			CustomProcessCreatedBy:             model.AccountJNE.ActorID,
			CustomProcessCreatedName:           model.AccountJNE.ActorName,
			CustomProcessUpdatedAt:             now,
			CustomProcessUpdatedName:           model.AccountJNE.ActorName,
			CustomProcessPartnerID:             model.AccountJNE.ActorID,
			CustomProcessPartnerCode:           model.AccountJNE.ActorCode,
			CustomProcessPartnerName:           model.AccountJNE.ActorName,
		},
		SttHistories: sttHistories,
	})

	if err != nil || createCustomProcess == nil {
		err = errors.New("An error occurred while create Custom Process")
		return
	}

	/*
	 * Updating list time stt status
	 */
	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.sttActivityUc.UpdateSttTime(goCtx, &stt_activity.SttActivityRequest{
			ListSttData: listSttUpdateTime,
		})
	}, int((5 * time.Minute).Seconds()))

	// publish message to PubSub\
	if model.IsPublishStatusCustomProcess[params.SttStatus] {
		c.bgStatusSubmit(&bgGatewaySttStatusSubmitReq{
			StatusSubmitParams: &model.UpdateSttStatusWithExtendForMiddleware{
				UpdateSttStatus: &model.UpdateSttStatus{
					SttNo: func() string {
						if sttRow.SttElexysNo.Valid {
							return sttRow.SttElexysNo.Value()
						}
						return sttRow.SttNo
					}(),
					Datetime:   now.UTC(),
					StatusCode: params.SttStatus,
					Location:   sttRow.SttDestinationCityID,
					Remarks:    fmt.Sprintf(`Paket diupdate oleh %s`, model.AccountJNE.ActorName),
					City:       cityDestination.Name,
					UpdatedBy:  model.AccountJNE.ActorName,
					UpdatedOn:  now.UTC(),
				},
				ServiceType:      model.PACKAGESERVICE,
				Product:          sttRow.SttProductType,
				Pieces:           sttRow.SttTotalPiece,
				GrossWeight:      sttRow.SttGrossWeight,
				VolumeWeight:     sttRow.SttVolumeWeight,
				ChargeableWeight: sttRow.SttChargeableWeight,
				BookedForType:    sttRow.SttBookedForType,
			},
			History:     c.mappingToSttPieceHistory(sttHistories),
			PartnerName: model.AccountJNE.ActorName,
		})
	}

	if !model.IsPublishStatusCustomProcess[params.SttStatus] && strings.EqualFold(sttRow.SttBookedForType, model.CLIENT) {
		go lputils.TrackGoroutine(func(goCtx context.Context) {
			err := c.middlewareRepo.SubmitDataToMiddleware(goCtx, &model.UpdateSttStatusWithExtendForMiddleware{
				UpdateSttStatus: &model.UpdateSttStatus{
					SttID: sttRow.SttID,
					SttNo: func() string {
						if sttRow.SttElexysNo.Valid {
							return sttRow.SttElexysNo.Value()
						}
						return sttRow.SttNo
					}(),
					Datetime:   now.UTC(),
					StatusCode: params.SttStatus,
					Location:   cityDestination.Code,
					Remarks:    fmt.Sprintf(`Paket diupdate oleh %s`, model.AccountJNE.ActorName),
					City:       cityDestination.Name,
					UpdatedBy:  model.AccountJNE.ActorName,
					UpdatedOn:  now.UTC(),
				},
				ServiceType:      model.PACKAGESERVICE,
				Product:          sttRow.SttProductType,
				Pieces:           sttRow.SttTotalPiece,
				GrossWeight:      sttRow.SttGrossWeight,
				VolumeWeight:     sttRow.SttVolumeWeight,
				ChargeableWeight: sttRow.SttChargeableWeight,
			})
			if err != nil {
				logger.Ef(`Submit-DataToMiddlewares Error %s`, err.Error())
			}
		}, int((5 * time.Minute).Seconds()))
	}

	go lputils.TrackGoroutine(func(goCtx context.Context) {
		// publish to pubsub dtpol commission
		if model.IsAllowToPubsubDTPOLCommission[params.SttStatus] {
			cityDestination, err := c.repoCity.Get(goCtx, sttRow.SttDestinationCityID, accountToken.Data.Token)
			if err == nil {
				localTime, err := shared.ParseTimeWithLocation(createCustomProcess.CustomProcess.CustomProcessCreatedAt.Format(time.RFC3339), time.RFC3339, shared.MappingTimzoneToTimeLocation[cityDestination.Timezone])
				if err == nil && createCustomProcess != nil {
					c.gatewaySttStatus.GoberDTPOLCommission(goCtx, &model.GoberDTPOLCommission{
						SttNo:              sttRow.SttNo,
						UpdatedAtLocalTime: localTime,
						UpdatedAtWIB:       createCustomProcess.CustomProcess.CustomProcessCreatedAt,
						SttLastStatus:      params.SttStatus,
					})
				}
			}
		}
	}, int((10 * time.Minute).Seconds()))

	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.requestPriorityDelivery.UpdateIsShowToZero(goCtx, sttRow.SttNo)
	}, int((5 * time.Minute).Seconds()))

	return
}
