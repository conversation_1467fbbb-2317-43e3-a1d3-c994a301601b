package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/Lionparcel/go-lptool/v2/lputils"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/cargo"
	"github.com/Lionparcel/hydra/src/usecase/cargo_reserve"
	"github.com/Lionparcel/hydra/src/usecase/cargo_v2"
	"github.com/Lionparcel/hydra/src/usecase/general"
	readytocargo "github.com/Lionparcel/hydra/src/usecase/ready_to_cargo"
	retrycargo "github.com/Lionparcel/hydra/src/usecase/retry_cargo"
	"github.com/Lionparcel/hydra/src/usecase/stt_activity"
	"github.com/Lionparcel/hydra/src/usecase/stt_due"
	"github.com/Lionparcel/hydra/src/usecase/stt_piece_history_remark_helper"
	"github.com/abiewardani/dbr/v2"
)

func (c *cargoV2Ctx) CreateCargoV2Sabre(ctx context.Context, params *cargo_v2.CreateCargoV2Request) (*cargo_v2.CreateCargoV2Response, error) {
	opName := "UsecaseCargo-CreateCargoV2Sabre"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	lastStatusCargo := model.CargoStatusMapping[strings.ToUpper(params.CargoType)]
	var res *cargo_v2.CreateCargoV2Response
	var err error
	credential := &model.CredentialRestAPI{
		Token:    params.Token,
		ClientID: params.AccountID,
	}
	reqTime := c.timeRepo.Now(time.Now())
	var AwbNo string
	status := `failed`
	isUseSabre := true

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": err})

		resTime := c.timeRepo.Now(time.Now())

		go c.partnerLog.Insert(context.Background(), &model.PartnerLog{
			Action: model.PLCreateCargoProcess,
			RefID:  params.CargoNo,
			Request: map[string]interface{}{
				"triggerred_by": "ngen-response",
				"usecase":       "CreateCargoV2-UseSabre",
				"is_rtc":        params.IsFromRtc,
				"is_use_sabre":  isUseSabre,
				"awb_no":        AwbNo,
				"request":       params,
				"request_time":  reqTime.Format("2006-01-02 15:04:05"),
			},
			Response: map[string]interface{}{
				"time_span": func() string {
					diff := resTime.Sub(reqTime)
					minutes := int(diff.Minutes())
					seconds := int(diff.Seconds()) % 60
					return fmt.Sprintf("%d:%d", minutes, seconds)
				}(),
				"status":        status,
				"response":      res,
				"response_time": resTime.Format("2006-01-02 15:04:05"),
				"error":         shared.CheckErrorNil(err),
			},
		})
	}()

	errDB := shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "An error occurred while querying db",
		"id": "Terjadi kesalahan pada saat query db",
	})

	/**
	 * Validation request
	 * Check cargo no is already exist or not ?
	 */
	if err := params.Validate(); err != nil {
		return res, err
	}

	for i, v := range params.NgenBooking.FlightPlanForEcargo {
		if c.cfg.EnableFlightDateTime() {
			_, err := shared.ParseUTC7(shared.FormatDateTime, v.FlightDateTime)
			if err != nil {
				return nil, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
					"en": "Invalid flight_date_time Date Format",
					"id": "Invalid Format tanggal flight_date_time",
				})
			}
			params.NgenBooking.FlightPlanForEcargo[i].FlightDate = ""
		} else {
			params.NgenBooking.FlightPlanForEcargo[i].FlightDateTime = ""
		}

	}

	if strings.EqualFold(params.CargoType, model.TRUCK) {
		params.CargoNo = shared.GenerateCodeUnique(`TUC`, params.DestinationCityCode, strconv.Itoa(int(c.timeRepo.Now(time.Now()).UnixNano())/int(time.Millisecond)), `-`)
	}

	res = &cargo_v2.CreateCargoV2Response{
		CargoNo: params.CargoNo,
	}

	if params.CargoNo != `` {
		cargoData, _ := c.cargoRepo.Get(selfCtx, &model.CargoViewParams{
			CargoNo: params.CargoNo,
		})
		if cargoData != nil && cargoData.CargoID > 0 {
			return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Cargo No is already exist",
				"id": "Cargo No sudah ada",
			})
		}
	}

	/** Validation partner who processing cargo */
	if !model.IsAllowedAccountTypeCargo[params.PartnerTypeToken] {
		return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner Type Invalid",
			"id": "Tipe Partner tidak valid",
		})
	}

	/** get partner from token **/
	cargoPartner, err := c.partnerRepo.GetByID(selfCtx, params.PartnerID, params.Token)
	if err != nil {
		return res, errDB
	}
	if cargoPartner == nil || cargoPartner.Data.PartnerLocation == nil {
		return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner not found",
			"id": "Partner tidak ditemukan",
		})
	}
	partnerCityCode := cargoPartner.Data.PartnerLocation.CityCode
	loc, err := time.LoadLocation(model.TimeZoneToLocation[cargoPartner.Data.PartnerLocation.City.Timezone])
	if err != nil {
		return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid timezone",
			"id": "Zona waktu tidak valid",
		})
	}
	params.NowTime = params.NowTime.In(loc)

	// get partner origin detail
	partnerOrigin, err := c.partnerLocationRepo.Get(selfCtx, &model.PartnerLocationParamsGet{CityCode: params.OriginCityCode}, params.Token)
	if err != nil || partnerOrigin == nil {
		return res, errDB
	}

	// get partner destination detail
	partnerDestination, err := c.partnerLocationRepo.Get(selfCtx, &model.PartnerLocationParamsGet{CityCode: params.DestinationCityCode}, params.Token)
	if err != nil || partnerDestination == nil {
		return res, errDB
	}

	err = c.getHubInfoFromRTC(selfCtx, params)
	if err != nil {
		return res, errDB
	}

	/** Split data
	 * split and get data to the original source
	 */
	bagNoWithDataRaw := map[string][]model.BagWithSTT{}
	sttNoWithDataRaw := map[string][]model.SttDetailResult{}
	sttNoWithBag := map[int]string{}
	isSttNoHaveBag := map[string]bool{}
	expiredCache := model.CacheBagOrSttProcessCargoV2Expired
	if strings.EqualFold(lastStatusCargo, model.CARGOPLANE) {
		expiredCache = time.Duration(c.cfg.ProcessCargoV2CacheExpired()) * time.Minute
	}

	for _, bagOrStt := range params.BagOrStt {
		key := fmt.Sprintf(`%s:%s:%s`, model.CacheBagOrSttProcessCargoV2, bagOrStt.BagNo, bagOrStt.SttNo)
		if ok := c.cacheRepo.CreateCacheOnce(selfCtx, key, expiredCache); !ok {
			err = shared.NewMultiStringBadRequestError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Contains stt/bag that still processing cargo",
				"id": "Berisikan stt/bag yang cargonya sedang di proses",
			})
			return nil, err
		}
		defer func() {
			if !strings.EqualFold(lastStatusCargo, model.CARGOPLANE) || !params.IsFromRtc || params.IsCargoPlaneManual {
				c.cacheRepo.DeleteCache(selfCtx, key)
			}

			if status == `failed` && strings.EqualFold(lastStatusCargo, model.CARGOPLANE) {
				c.cacheRepo.DeleteCache(selfCtx, key)
			}
		}()

		if bagOrStt.BagNo != `` {
			data, _ := c.bagRepo.SelectBagWithSTT(selfCtx, &model.BagViewParams{
				BagCode:             bagOrStt.BagNo,
				IsDeletedFalse:      true,
				IsNotAvailableFalse: true,
			})
			if data == nil {
				bagNoWithDataRaw[bagOrStt.BagNo] = []model.BagWithSTT{}
			} else {
				bagNoWithDataRaw[bagOrStt.BagNo] = data
			}
		}
		if bagOrStt.SttNo != `` {
			sttDetail, _ := c.sttRepo.SelectDetailBagLatest(selfCtx, 0, bagOrStt.SttNo, true, true)
			if sttDetail == nil || len(sttDetail) == 0 {
				sttNoWithDataRaw[bagOrStt.SttNo] = []model.SttDetailResult{}
			} else {
				sttNoWithDataRaw[sttDetail[0].Stt.SttNo] = sttDetail

			}
		}
	}

	/**
	 * Get Commodity group by code
	 */
	cargoCommodity, err := c.commodityGroupRepo.Select(selfCtx, &model.CommodityGroupRequest{
		Token: params.Token,
		Code:  params.CargoCommodity,
	})
	if err != nil {
		return res, errDB
	}
	if cargoCommodity == nil || len(cargoCommodity.Data) == 0 {
		return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Cargo Commodity not found",
			"id": "Cargo Commodity tidak ditemukan",
		})
	}

	/**
	 * Get Product cargo by code
	 */

	cargoProductType, err := c.cargoProductTypeRepo.Select(selfCtx, &model.CargoProductTypeRequest{
		Token: params.Token,
		Code:  params.ProductCargo,
	})
	if err != nil {
		return res, errDB
	}
	if cargoProductType == nil || len(cargoProductType.Data) == 0 {
		return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Cargo Product not found",
			"id": "Cargo Product tidak ditemukan",
		})
	}

	/**
	 * get total pcs cargo
	 * get failed stt or bag no
	 * get success stt or bag no
	 */
	totalPcs := 0
	failedSttNo := map[string]bool{}
	// TODO TAKEOUT WHEN ELEXYS ROLL OUT
	failedSttNoWithElexysStt := map[string]general.STTFailedGeneralResponse{}
	listFailedSttNo := []general.STTFailedGeneralResponse{}
	successSttNoWithData := map[string]cargo_v2.CargoDetailCreate{}
	sttNoArr := []string{}

	/**
	 * Looping bag
	 * validate stt last status on bag, is allowed or not
	 * validate stt commodity on bag is match to commodity cargo
	 * validate stt product on bag is match to product cargo
	 */

	statusBag := map[string]bool{}
	bagCode := []string{}
	reqRemoveRtc := []readytocargo.ReadytToCargoPubsub{}
	for _, bagData := range bagNoWithDataRaw {
		//Bag not found
		if len(bagData) < 1 {
			res = &cargo_v2.CreateCargoV2Response{
				CargoNo:        params.CargoNo,
				TotalSttFailed: len(listFailedSttNo),
				ListSttFailed:  listFailedSttNo,
			}
			return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Bag No is not found",
				"id": "Nomer Bag tidak ditemukan",
			})
		}

		tmpTotalSttFailedOnBag := 0
		getTotalSttOnBag := 0
		for idx, bag := range bagData {
			if idx == 0 {
				getTotalSttOnBag++
				continue
			}
			if bag.Stt.SttNo == bagData[0].Stt.SttNo {
				continue
			}
			getTotalSttOnBag++
		}

		//Validation cross docking, last status, product & commodity stt on bag
		for idx, bag := range bagData {

			if idx > 0 {
				if bag.Stt.SttNo == bagData[0].Stt.SttNo {
					continue
				}
			}

			//last status stt on bag validation
			if model.IsNotAllowUpdateToCargo[bag.Stt.SttLastStatusID] {
				failedSttNo[bag.Stt.SttNo] = true
				// TODO TAKEOUT WHEN ELEXYS ROLL OUT
				failedSttNoWithElexysStt[bag.Stt.SttNo] = general.STTFailedGeneralResponse{
					SttNo:       bag.Stt.SttNo,
					SttElexysNo: bag.SttElexysNo.Value(),
				}
				tmpTotalSttFailedOnBag++
				continue
			}

			if exist, _ := failedSttNo[bag.Stt.SttNo]; !exist {
				successSttNoWithData[bag.Stt.SttNo] = cargo_v2.CargoDetailCreate{
					Stt: bag.Stt,
					SttPiece: func() []model.SttPiece {
						res := []model.SttPiece{}
						for _, b := range bagData {
							if bag.Stt.SttNo == b.Stt.SttNo {
								res = append(res, b.SttPiece)
							}
						}
						return res
					}(),
					BagDetail: model.BagDetail{
						BagCode:     bag.BagCode,
						BagDetailID: bag.BagDetailID,
					},
				}
			}
			sttNoArr = append(sttNoArr, bag.Stt.SttNo)

			if bag.BagID != nil {
				if statusBag[*bag.BagCode] == false {
					bagCode = append(bagCode, *bag.BagCode)
					statusBag[*bag.BagCode] = true
				}

				bagging, _ := c.bagRepo.Get(context.Background(), &model.Bag{BagID: *bag.BagID})

				if bagging != nil && err == nil {
					data := readytocargo.ReadytToCargoPubsub{
						ActorId:             int(params.AccountID),
						ActorCode:           bagging.BagOriginCityCode,
						ActorName:           params.AccountName,
						WeightAfter:         0 - bagging.BagCustomGrossWeight,
						WeightBefore:        bagging.BagCustomGrossWeight,
						Id:                  *bag.BagID,
						RtcType:             readytocargo.RtcTypeBagging,
						Flag:                readytocargo.REMOVE,
						OriginCityCode:      bagging.BagOriginCityCode,
						OriginCityName:      bagging.BagOriginCityName,
						DestinationCityCode: bagging.BagDestinationCityCode,
						DestinationCityName: bagging.BagDestinationCityName,
						PartnerCityCode:     partnerCityCode,
						PartnerId:           bagging.BagPartnerID,
						PartnerCode:         bagging.BagPartnerCode,
						PartnerType:         model.CONSOLE,
						CommodityGroup:      "",
						ProductType:         bagging.BagProductType,
						TotalPieceAfter:     0 - 1,
						TotalPieceBefore:    1,
						HubID:               params.HubID,
						HubName:             params.HubName,
						CreatedByProcess:    readytocargo.CreateCargo,
					}
					reqRemoveRtc = append(reqRemoveRtc, data)

					if c.cfg.UseRemoveProducerRtc() {
						go c.readyToCargo.SendPubsub(context.Background(), data)
					}
				} else {
					logger.Ef(`failed get baging : %s invalid`, *bag.BagID)
				}

			}
		}

		if tmpTotalSttFailedOnBag < getTotalSttOnBag {
			totalPcs++
		}
	}

	/**
	 * Looping stt
	 * validate stt last status, is allowed or not
	 * validate stt commodity is match to commodity cargo
	 * validate stt product is match to product cargo
	 */
	for sttNo, sttData := range sttNoWithDataRaw {
		//Stt not found
		if len(sttData) < 1 {
			res = &cargo_v2.CreateCargoV2Response{
				CargoNo:        params.CargoNo,
				TotalSttFailed: len(listFailedSttNo),
				ListSttFailed:  listFailedSttNo,
			}
			return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt No is not found",
				"id": "Nomer Stt tidak ditemukan",
			})
		}

		//Validation cross docking, last status, product & commodity stt
		baggingList := map[int]bool{}
		for idx, stt := range sttData {

			if idx > 0 {
				if stt.Stt.SttNo == sttData[0].Stt.SttNo {
					continue
				}
			}

			//last status stt validation
			if model.IsNotAllowUpdateToCargo[stt.Stt.SttLastStatusID] {
				failedSttNo[sttNo] = true
				// TODO TAKEOUT WHEN ELEXYS ROLL OUT
				failedSttNoWithElexysStt[sttNo] = general.STTFailedGeneralResponse{
					SttNo:       sttNo,
					SttElexysNo: stt.SttElexysNo.Value(),
				}
				continue
			}
			sttNoArr = append(sttNoArr, sttNo)

			if stt.BagDetailBagID != nil {
				if !baggingList[*stt.BagDetailBagID] {
					baggingList[*stt.BagDetailBagID] = true

					bagging, _ := c.bagRepo.Get(context.Background(), &model.Bag{BagID: *stt.BagDetailBagID})
					if bagging != nil && err == nil {
						data := readytocargo.ReadytToCargoPubsub{
							ActorId:             int(params.AccountID),
							ActorCode:           bagging.BagOriginCityCode,
							ActorName:           params.AccountName,
							WeightBefore:        bagging.BagCustomGrossWeight,
							Id:                  bagging.BagID,
							WeightAfter:         0 - bagging.BagCustomGrossWeight,
							Flag:                readytocargo.REMOVE,
							RtcType:             readytocargo.RtcTypeBagging,
							OriginCityCode:      bagging.BagOriginCityCode,
							OriginCityName:      bagging.BagOriginCityName,
							DestinationCityCode: bagging.BagDestinationCityCode,
							DestinationCityName: bagging.BagDestinationCityName,
							PartnerCityCode:     partnerCityCode,
							PartnerId:           bagging.BagPartnerID,
							PartnerCode:         bagging.BagPartnerCode,
							PartnerType:         model.CONSOLE,
							CommodityGroup:      "",
							ProductType:         bagging.BagProductType,
							TotalPieceAfter:     0 - 1,
							TotalPieceBefore:    1,
							HubID:               params.HubID,
							HubName:             params.HubName,
							CreatedByProcess:    readytocargo.CreateCargo,
						}

						reqRemoveRtc = append(reqRemoveRtc, data)
						if c.cfg.UseRemoveProducerRtc() {
							go c.readyToCargo.SendPubsub(context.Background(), data)
						}
					} else {
						logger.Ef(`failed get bagging : %s invalid`, *stt.BagDetailBagID)
					}
				}

				sttNoWithBag[*stt.BagDetailBagID] = stt.SttNo
				isSttNoHaveBag[stt.SttNo] = true
			}

			data := readytocargo.ReadytToCargoPubsub{
				ActorId:             int(params.AccountID),
				ActorCode:           stt.SttOriginCityID,
				ActorName:           params.AccountName,
				WeightAfter:         0 - stt.SttGrossWeight,
				WeightBefore:        stt.SttGrossWeight,
				Id:                  int(stt.SttID),
				Flag:                readytocargo.REMOVE,
				OriginCityCode:      stt.SttOriginCityID,
				OriginCityName:      stt.SttOriginCityName,
				DestinationCityCode: stt.SttDestinationCityID,
				DestinationCityName: stt.SttDestinationCityName,
				PartnerCityCode:     partnerCityCode,
				PartnerId:           1,
				PartnerCode:         "Console",
				PartnerType:         model.CONSOLE,
				CommodityGroup:      stt.SttCommodityCode,
				ProductType:         stt.SttProductType,
				TotalPieceAfter:     0 - stt.SttTotalPiece,
				TotalPieceBefore:    stt.SttTotalPiece,
				RtcType:             readytocargo.RtcTypeStt,
				HubID:               params.HubID,
				HubName:             params.HubName,
				CreatedByProcess:    readytocargo.CreateCargo,
			}

			reqRemoveRtc = append(reqRemoveRtc, data)
			if c.cfg.UseRemoveProducerRtc() {
				go c.readyToCargo.SendPubsub(context.Background(), data)
			}
		}

		//if sttNo is not in totalFailedSttNo map increase totalPcs
		if exist, _ := failedSttNo[sttNo]; !exist {
			if !isSttNoHaveBag[sttNo] {
				totalPcs += len(sttData)
			}

			successSttNoWithData[sttData[0].Stt.SttNo] = cargo_v2.CargoDetailCreate{
				Stt: sttData[0].Stt,
				SttPiece: func() []model.SttPiece {
					res := []model.SttPiece{}
					for _, stt := range sttData {
						res = append(res, stt.SttPiece)
					}
					return res
				}(),
				BagDetail: sttData[0].BagDetail,
			}
		}
	}

	// send remove rtc
	if !c.cfg.UseRemoveProducerRtc() {
		go c.readyToCargo.SendPubsubRemove(context.Background(), reqRemoveRtc)
	}

	if len(sttNoArr) > 0 {
		//check stt payment status
		err = c.sttPaymentValidation(ctx, sttNoArr)
		if err != nil {
			return nil, err
		}
	}

	/** Listing list failed stt **/
	for sttNo := range failedSttNo {
		// TODO TAKEOUT WHEN ELEXYS ROLL OUT
		/**
		 * delete SttElexysNo params
		 */
		listFailedSttNo = append(listFailedSttNo, general.STTFailedGeneralResponse{
			SttNo:       sttNo,
			SttElexysNo: failedSttNoWithElexysStt[sttNo].SttElexysNo,
		})
	}

	// assign cargo piece valueg
	totalPcs += len(sttNoWithBag)

	/**
	 * Validate if cargo type is cargo plane auto/manual
	 * max totalPcs is 15
	 */
	if params.CargoActualPiece > model.MaxCargoPlanePiece && lastStatusCargo == model.CARGOPLANE {
		res = &cargo_v2.CreateCargoV2Response{
			CargoNo:        params.CargoNo,
			TotalSttFailed: len(listFailedSttNo),
			ListSttFailed:  listFailedSttNo,
		}
		return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Max Cargo Plane Piece cannot more than 15",
			"id": "Max Cargo Plane Piece tidak boleh lebih dari 15",
		})
	}

	/** Initialize req data to create cargo **/
	var dimension string
	if len(params.Dimensions) > 0 {
		dimensionCustom, err := json.Marshal(roundingDimensions(c.cfg, params.Dimensions, params.CargoType))
		if err == nil {
			dimension = string(dimensionCustom)
		}
	}

	cutoffTime, cargoOverLimitH0, err := c.getCutoffTime(selfCtx, params)
	if err != nil {
		return nil, err
	}

	if c.cfg.IsActiveCargoRecalculate() {
		params.ActualTotalCargoVolumeWeight, params.ActualTotalCargoGrossWeight = c.recalculateNgen(selfCtx, params)
	}
	now, _ := shared.ParseUTC7(shared.FormatDateTime, c.timeRepo.Now(time.Now()).Format(shared.FormatDateTime))
	req := model.CreateDetailCargoTrx{}
	req.IsFromRtc = params.IsFromRtc
	req.Cargo = model.Cargo{
		CargoType: params.CargoType,
		CargoIsCargoPlaneManual: func() bool {
			if lastStatusCargo == model.CARGOPLANE {
				return params.IsCargoPlaneManual
			}
			return false
		}(),
		CargoOriginCityCode:            params.OriginCityCode,
		CargoOriginAirportCode:         params.OriginAirportCode,
		CargoDestinationCityCode:       params.DestinationCityCode,
		CargoDestinationAirportCode:    params.DestinationAirportCode,
		CargoCommodityGroupCode:        params.CargoCommodity,
		CargoCargoProductTypeCode:      params.ProductCargo,
		CargoCreatedAt:                 now,
		CargoCreatedBy:                 params.AccountID,
		CargoCreatedName:               params.AccountName,
		CargoPartnerName:               cargoPartner.Data.Name,
		CargoPartnerCode:               cargoPartner.Data.Code,
		CargoPartnerType:               cargoPartner.Data.Type,
		CargoPartnerID:                 params.PartnerID,
		CargoTotalCustomGrossWeight:    params.ActualTotalCargoGrossWeight,
		CargoTotalCustomVolumeWeight:   params.ActualTotalCargoVolumeWeight,
		CargoTotalEstimateGrossWeight:  params.EstTotalCargoGrossWeight,
		CargoTotalEstimateVolumeWeight: params.EstTotalCargoVolumeWeight,
		CargoTotalStt:                  len(successSttNoWithData),
		CargoEstLengthDimension:        params.EstimationDimension.CargoEstLengthDimension,
		CargoEstWidthDimension:         params.EstimationDimension.CargoEstWidthDimension,
		CargoEstHeightDimension:        params.EstimationDimension.CargoEstHeightDimension,
		CargoActualLengthDimension:     params.ActualDimension.CargoActualLengthDimension,
		CargoActualWidthDimension:      params.ActualDimension.CargoActualWidthDimension,
		CargoActualHeightDimension:     params.ActualDimension.CargoActualHeightDimension,
		CargoNog:                       params.Nog,
		CargoEstimateDepartureDate:     now,
		CargoActualPiece:               params.CargoActualPiece,
		CargoHubID:                     params.HubID,
		CargoHubName:                   params.HubName,
		CargoIsFromRtc:                 params.IsFromRtc,
		CargoDimensionCustom:           dbr.NewNullString(dimension),
		CargoOverCutoffLimitH0:         cargoOverLimitH0,
		CargoOverCutoffLimit:           params.CargoOverCutoffLimit,
		CargoRtcTurnAuto:               params.CargoRtcTurnAuto,
		CargoIsRetryFromRtc:            params.RebookingID > 0 && params.IsFromRtc,
		CargoIsUseSabre:                params.IsUseSabre,
	}

	/** hit horde get airport name & city name **/
	originCity, err := c.cityRepo.Get(selfCtx, params.OriginCityCode, params.Token)
	if err != nil || originCity == nil {
		res = &cargo_v2.CreateCargoV2Response{
			CargoNo:        req.CargoNo,
			TotalSttFailed: len(listFailedSttNo),
			ListSttFailed:  listFailedSttNo,
		}
		return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Origin City is not found",
			"id": "Origin City tidak ditemukan",
		})
	}
	destinationCity, err := c.cityRepo.Get(selfCtx, params.DestinationCityCode, params.Token)
	if err != nil || destinationCity == nil {
		res = &cargo_v2.CreateCargoV2Response{
			CargoNo:        req.CargoNo,
			TotalSttFailed: len(listFailedSttNo),
			ListSttFailed:  listFailedSttNo,
		}
		return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Destination City is not found",
			"id": "Destination City tidak ditemukan",
		})
	}
	destinationAirport := &model.Airport{}
	originAirport := &model.Airport{}
	if lastStatusCargo == model.CARGOPLANE {
		originAirport, err = c.airportRepo.Get(selfCtx, params.OriginAirportCode, params.Token)
		if err != nil || originAirport == nil {
			res = &cargo_v2.CreateCargoV2Response{
				CargoNo:        req.CargoNo,
				TotalSttFailed: len(listFailedSttNo),
				ListSttFailed:  listFailedSttNo,
			}
			return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Origin Airport is not found",
				"id": "Origin Airport tidak ditemukan",
			})
		}
		destinationAirport, err = c.airportRepo.Get(selfCtx, params.DestinationAirportCode, params.Token)
		if err != nil || destinationAirport == nil {
			res = &cargo_v2.CreateCargoV2Response{
				CargoNo:        req.CargoNo,
				TotalSttFailed: len(listFailedSttNo),
				ListSttFailed:  listFailedSttNo,
			}
			return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Destination Airport is not found",
				"id": "Destination Airport tidak ditemukan",
			})
		}
	}
	req.GenerateRemark(model.CargoRemark{
		TotalFlight:         params.TotalFlight,
		SequentialFlight:    params.SequentialFlight,
		OriginCityName:      originCity.Name,
		DestinationCityName: destinationCity.Name,
		AirportOriginName: func() string {
			if lastStatusCargo == model.CARGOPLANE {
				return originAirport.AirportName
			}
			return ""
		}(),
		AirportDestinationName: func() string {
			if lastStatusCargo == model.CARGOPLANE {
				return destinationAirport.AirportName
			}
			return ""
		}(),
		NgenCreatedAt: func() string {
			if lastStatusCargo == model.CARGOPLANE && params.IsCargoPlaneManual {
				return params.NgenCreatedAt
			}
			return ""
		}(),
	})

	/** If all stt is failed **/
	if len(successSttNoWithData) < 1 {
		res = &cargo_v2.CreateCargoV2Response{
			CargoNo:        params.CargoNo,
			TotalSttFailed: len(listFailedSttNo),
			ListSttFailed:  listFailedSttNo,
		}
		return res, nil
	}

	// Define variable for update status time
	listSttUpdateTime := []stt_activity.SttActivityRequestDetail{}

	/** Create cargo
	 * add cargo
	 * add cargo detail every success stt
	 * add stt piece history on every success stt
	 * update stt piece last status on every success stt
	 * update stt last status on every success stt
	 */

	/**
	 * Booking awb to NGen
	 * if cargo is plane and use awb_no & flight_no from ngen
	 */
	FlightNo := ""
	FlightNoRetryCargo := ""
	var ArrData time.Time
	var DepData time.Time
	var errNgen error
	var awbReserveStatus string
	var awbID int
	var isUseAwbReserve bool
	cargoFlightReq := []model.CargoFlight{}
	requestNgen := new(model.NgenBookingRequest)
	ngenRes := new(model.NgenBookingResponse)

	if lastStatusCargo == model.CARGOPLANE && !params.IsCargoPlaneManual && len(successSttNoWithData) > 0 {

		if len(params.NgenBooking.FlightPlanForEcargo) > 0 &&
			len(params.NgenBooking.FlightPlanForEcargo[0].FlightNo) > 0 {
			FlightNoRetryCargo = params.NgenBooking.FlightPlanForEcargo[0].FlightNo
		}

		// ShipperConsigneeDetails request
		shipperConsignee := model.ShipperConsigneeDetails{
			ConigneeAccountNo:    "0",
			ConigneeAddress:      partnerDestination.Data.Partner.Address,
			ConigneeEMailAddress: partnerDestination.Data.Partner.PartnerContactEmail,
			ConigneeMobileNo:     partnerDestination.Data.Partner.PartnerContactPhoneNumbers,
			ConigneeName:         "LION PARCEL " + partnerDestination.Data.CityCode,
			ConigneePhoneNo:      partnerDestination.Data.Partner.PhoneNumber,
			ShipperAccountNo:     "0",
			ShipperAddress:       partnerOrigin.Data.Partner.Address,
			ShipperEMailAddress:  partnerOrigin.Data.Partner.PartnerContactEmail,
			ShipperMobileNo:      partnerOrigin.Data.Partner.PartnerContactPhoneNumbers,
			ShipperName:          "LION PARCEL " + partnerOrigin.Data.CityCode,
			ShipperPhoneNo:       partnerOrigin.Data.Partner.PhoneNumber,
		}

		// dimension request
		dimensionNgen := []model.DimensionArray{}
		for _, val := range params.Dimensions {
			dimensionNgen = append(dimensionNgen, model.DimensionArray{
				Height: strconv.FormatInt(int64(val.Height), 10),
				Length: strconv.FormatInt(int64(val.Length), 10),
				Width:  strconv.FormatInt(int64(val.Width), 10),
				Pieces: strconv.FormatInt(int64(val.Pieces), 10),
			})
		}

		// FlightPlanForEcargo request
		flightPlanForEcargo := []model.FlightPlanForEcargo{}
		if len(params.NgenBooking.FlightPlanForEcargo) > 0 {
			for _, val := range params.NgenBooking.FlightPlanForEcargo {
				IsRootStation := 0
				if val.IsRootStation {
					IsRootStation = 1
				}

				thresholdTime := ""
				if c.cfg.HydraEnableThresholdNgen() {
					thresholdTime = strconv.FormatInt(int64(val.FlightThresholdTime), 10)
					if thresholdTime == "0" || thresholdTime == "" {
						thresholdTime = c.cfg.HydraThresholdNgen()
					}
					if params.IsUseSabre && c.cfg.NgenForceThreshold() {
						thresholdTime = c.cfg.NgenForceThresholdTime()
					}
				}
				flightPlan := model.FlightPlanForEcargo{
					DailyFlightSNo:    "",
					IsRootStation:     IsRootStation,
					FlightNo:          val.FlightNo,
					FlightDate:        val.FlightDate,
					FlightDateTime:    val.FlightDateTime,
					FlightOrigin:      val.FlightOrigin,
					FlightDestination: val.FlightDestination,
					ThresholdTime:     thresholdTime,
				}
				flightPlanForEcargo = append(flightPlanForEcargo, flightPlan)

				// get origin airport location
				originAirport, err := c.airportRepo.Get(selfCtx, val.FlightOrigin, params.Token)
				if err != nil || originAirport == nil {
					err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Airport Origin Not Found",
						"id": "Airport asal tidak ditemukan",
					})
				}
				// get destination airport location
				destinationAirport, err := c.airportRepo.Get(selfCtx, val.FlightDestination, params.Token)
				if err != nil || destinationAirport == nil {
					err = shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Airport Destination Not Found",
						"id": "Airport tujuan tidak ditemukan",
					})
				}

				remark := ``
				now := c.timeRepo.Now(time.Now())
				dataRemark := model.CargoRemark{}
				dataRemark.GenerateCargoRemarkLocation(originAirport, destinationAirport)
				dataByte, _ := json.Marshal(&dataRemark)
				if err == nil {
					remark = string(dataByte)
				}
				depatureDate, _ := shared.ParseUTC7(shared.FormatDateTime, val.FlightDepDatetime)
				arrivalDate, _ := shared.ParseUTC7(shared.FormatDateTime, val.FlightArrDatetime)

				cargoFlightReq = append(cargoFlightReq, model.CargoFlight{
					CFNumber:             val.FlightNo,
					CFRemarks:            dbr.NewNullString(remark),
					CFAirportOrigin:      val.FlightOrigin,
					CFAirportDestination: val.FlightDestination,
					CFEstDepartureTime:   &depatureDate,
					CFEstArrivalTime:     &arrivalDate,
					CFCreatedAt:          now,
					CFUpdatedAt:          &now,
				})
			}
		}

		// request ngen

		if c.cfg.EnableNgenNog() {
			requestNgen = &model.NgenBookingRequest{
				GrossWeight:            fmt.Sprintf("%.2f", params.ActualTotalCargoGrossWeight),
				VolumeWeight:           fmt.Sprintf("%.2f", params.ActualTotalCargoVolumeWeight),
				Origin:                 params.OriginAirportCode,
				Destination:            params.DestinationAirportCode,
				Username:               c.cfg.NgenUsername(),
				ProductName:            params.NgenBooking.ProductName,
				Pieces:                 strconv.FormatInt(int64(params.CargoActualPiece), 10),
				AWBNo:                  params.NgenBooking.AWBNo,
				AWBPrefix:              params.NgenBooking.AWBPrefix,
				SHC:                    params.NgenBooking.SHC,
				EcargoUniqueID:         params.NgenBooking.EcargoUniqueID,
				CommodityCode:          cargoCommodity.Data[0].CommodityGroupCode,
				DimensionArray:         dimensionNgen,
				ShipperConsigneeDetail: []model.ShipperConsigneeDetails{shipperConsignee},
				FlightPlanForEcargo:    flightPlanForEcargo,
				NOG:                    params.Nog,
			}
		} else {
			requestNgen = &model.NgenBookingRequest{
				GrossWeight:            fmt.Sprintf("%.2f", params.ActualTotalCargoGrossWeight),
				VolumeWeight:           fmt.Sprintf("%.2f", params.ActualTotalCargoVolumeWeight),
				Origin:                 params.OriginAirportCode,
				Destination:            params.DestinationAirportCode,
				Username:               c.cfg.NgenUsername(),
				ProductName:            params.NgenBooking.ProductName,
				Pieces:                 strconv.FormatInt(int64(params.CargoActualPiece), 10),
				AWBNo:                  params.NgenBooking.AWBNo,
				AWBPrefix:              params.NgenBooking.AWBPrefix,
				SHC:                    params.NgenBooking.SHC,
				EcargoUniqueID:         params.NgenBooking.EcargoUniqueID,
				CommodityCode:          cargoCommodity.Data[0].CommodityGroupCode,
				DimensionArray:         dimensionNgen,
				ShipperConsigneeDetail: []model.ShipperConsigneeDetails{shipperConsignee},
				FlightPlanForEcargo:    flightPlanForEcargo,
			}
		}

		if !params.IsFromRtc {
			// get cargo reserve data
			if len(requestNgen.FlightPlanForEcargo) > 0 &&
				len(requestNgen.FlightPlanForEcargo[0].FlightNo) > 0 &&
				len(strings.Split(requestNgen.FlightPlanForEcargo[0].FlightNo, "-")) > 1 {
				airlinePrefix := requestNgen.FlightPlanForEcargo[0].FlightNo
				airlinePrefix = strings.Split(airlinePrefix, "-")[0]
				AWBNo, id, err := c.cargoReserveUc.GetRecommendation(selfCtx, cargo_reserve.GetCargoReserveParams{
					FlightPrefix: airlinePrefix,
					PartnerID:    params.PartnerID,
					PartnerType:  cargoPartner.Data.Type,
					Token:        params.Token,
				})
				if err != nil {
					return res, errDB
				}
				requestNgen.IsUpdate = "N"
				requestNgen.AWBNo = AWBNo

				if len(AWBNo) > 0 {
					isUseAwbReserve = true
					AwbNo = AWBNo
					awbID = id
					res.IsCargoUsingAwb = true
					req.Cargo.CargoIsUseAwbReserve = isUseAwbReserve
				}
			}
		}

		// if booking cargo from rtc
		enableBookNgenV3 := false

		if !isUseAwbReserve {
			enableBookNgenV3 = true
		}

		if enableBookNgenV3 && (params.CargoRtcTurnAuto || (!params.IsCargoPlaneManual && !params.IsFromRtc)) {
			// get token ngen
			tokenNgen, err := c.ngenRepo.Login(selfCtx, &model.NgenRequestLogin{
				Username: c.cfg.NgenUsername(),
				Password: c.cfg.NgenPassword(),
			})
			if err != nil {
				go c.deleteCacheForBagOrStt(context.Background(), params)

				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Failed to login Ngen",
					"id": "Gagal login Ngen",
				})
			}

			// total pieces validation
			if params.CargoActualPiece < 1 {

				return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Total pieces must be higher than 0",
					"id": "Total pieces harus lebih besar dari 0",
				})
			}

			// create booking ngen
			requestNgen.IsUpdate = "N"
			ngenRes, errNgen = c.ngenRepo.CreateBooking(selfCtx, requestNgen, tokenNgen.Data, &shared.CustomConfigClientRequest{Timeout: c.cfg.MaxCustomTimeoutNgen()})
			defer func() {
				go c.partnerLog.Insert(context.Background(), &model.PartnerLog{
					Action: model.PLNgenRequest,
					RefID:  AwbNo,
					Request: map[string]interface{}{
						"request": requestNgen,
						"request_time": func() time.Time {
							if ngenRes != nil {
								return ngenRes.RequestTime
							}
							return time.Time{}
						}(),
						"status_cargo_no": "auto_awb",
						"status_cargo_from": func() string {
							if params.IsFromRtc {
								return "rtc"
							}
							return "manual"
						}(),
					},
					Response: map[string]interface{}{
						"response": ngenRes,
						"response_time": func() time.Time {
							if ngenRes != nil {
								return ngenRes.ResponseTime
							}
							return time.Time{}
						}(),
						"error_detail": shared.CheckErrorNil(errNgen),
						"error":        errNgen,
					},
				})
			}()
			if errNgen != nil || ngenRes == nil {

				go c.deleteCacheForBagOrStt(context.Background(), params)

				if !strings.Contains(errNgen.Error(), "Client.Timeout") {
					res = &cargo_v2.CreateCargoV2Response{
						CargoNo:        "",
						TotalSttFailed: len(listFailedSttNo),
						ListSttFailed:  listFailedSttNo,
					}
					logger.E(errNgen)
				}
			}

			if ngenRes != nil && len(ngenRes.AWBDetail) > 0 {
				AwbNo = ngenRes.AWBDetail[0].AWBNo
				FlightNo = ngenRes.AWBDetail[0].FlightNo
			}

			ArrData = func() time.Time {
				arrDate, _ := shared.ParseUTC7(shared.FormatDateTime, params.ArrivalDate)
				res := shared.UTC7(arrDate)
				return res
			}()
			DepData = func() time.Time {
				depDate, _ := shared.ParseUTC7(shared.FormatDateTime, params.DepartureDate)
				res := shared.UTC7(depDate)
				return res
			}()

			if errNgen != nil && strings.Contains(errNgen.Error(), "Client.Timeout") {
				awbReserveStatus = model.CargoReserveStatusFailed

				go c.deleteCacheForBagOrStt(context.Background(), params)
			}

			if errNgen != nil && params.CargoRtcTurnAuto {

				go c.deleteCacheForBagOrStt(context.Background(), params)

			}
		}
	}

	req.Cargo.CargoNo = func() string {
		if lastStatusCargo == model.CARGOPLANE && !params.IsCargoPlaneManual {
			return AwbNo
		}
		return params.CargoNo
	}()

	req.Cargo.CargoVehicleNo = func() string {
		if lastStatusCargo == model.CARGOPLANE && !params.IsCargoPlaneManual {
			return FlightNo
		}
		return params.CargoVehicleNo
	}()

	depDateTime, _ := shared.ParseUTC7(shared.FormatDateTime, params.DepartureDate)
	arrDateTime, _ := shared.ParseUTC7(shared.FormatDateTime, params.ArrivalDate)

	if params.IsCargoPlaneManual {
		arrDate := strings.Split(params.ArrivalDate, " ")
		depDate := strings.Split(params.DepartureDate, " ")

		if arrDate[1] == "00:00:00" && depDate[1] == "00:00:00" {
			arrDateTime = now.Add(time.Hour * 2)
			depDateTime = now
		}
	}

	req.Cargo.CargoDepartureDate = func() time.Time {
		if lastStatusCargo == model.CARGOPLANE && !params.IsCargoPlaneManual && !DepData.IsZero() {
			return DepData
		}

		return depDateTime
	}()

	req.Cargo.CargoArrivalDate = func() time.Time {
		if lastStatusCargo == model.CARGOPLANE && !params.IsCargoPlaneManual && !ArrData.IsZero() {
			return ArrData
		}

		return arrDateTime
	}()

	for _, sttNoWithData := range successSttNoWithData {
		tmpSttPieceHistory := []model.SttPieceHistory{}
		for _, sttPiece := range sttNoWithData.SttPiece {
			req.SttCargoDetail = append(req.SttCargoDetail, model.CargoDetail{
				CargoDetailSttID:      sttPiece.SttPieceSttID,
				CargoDetailSttPieceID: sttPiece.SttPieceID,
				CargoDetailCreatedAt:  c.timeRepo.Now(time.Now()),
				CargoDetailBagNo: func() string {
					if sttNoWithData.BagDetail.BagCode != nil {
						return *sttNoWithData.BagDetail.BagCode
					}
					return ""
				}(),
			})

			var (
				cargoNumber = req.CargoNo
				truckNumber = func() string {
					if lastStatusCargo == model.CARGOTRUCK {
						return req.CargoVehicleNo
					}
					return ""
				}()
				trainNumber = func() string {
					if lastStatusCargo == model.CARGOTRAIN {
						return req.CargoVehicleNo
					}
					return ""
				}()
				shipNumber = func() string {
					if lastStatusCargo == model.CARGOSHIP {
						return req.CargoVehicleNo
					}
					return ""
				}()
			)

			tempRemarksPieceHistory := model.RemarkPieceHistory{
				HistoryLocationName: cargoPartner.Data.PartnerLocation.City.Name,
			}

			if sttNoWithData.Stt.SttLastStatusID == model.BKD {
				//Force PUP
				sttMeta := sttNoWithData.Stt.SttMetaToStruct()
				if sttMeta == nil {
					// get origin booking city
					sttMeta = &model.SttMeta{}
					bookingCity, err := c.cityRepo.Get(selfCtx, sttNoWithData.Stt.SttOriginCityID, params.Token)
					if err == nil && bookingCity != nil {
						sttMeta.OriginCityName = bookingCity.Name
					}
				}

				// assign remarks piece history
				tempRemarksPieceHistory.HistoryLocationName = sttMeta.OriginCityName
				tempRemarksPieceHistory.HistoryDistrictName = sttMeta.OriginDistrictName
				tempRemarksPieceHistory.DriverName = model.AccountSystem.ActorName

				// PUPC
				if sttNoWithData.Stt.SttBookedByType == model.POS {
					partnerData, err := c.partnerRepo.GetByID(ctx, sttNoWithData.Stt.SttBookedBy, params.Token)
					if err == nil && partnerData != nil && partnerData.Data.PartnerPosType == model.Branch {
						tmpSttPieceHistory = append(tmpSttPieceHistory, model.SttPieceHistory{
							SttPieceID:         sttPiece.SttPieceID,
							HistoryStatus:      model.PUPC,
							HistoryLocation:    sttNoWithData.Stt.SttOriginCityID,
							HistoryActorID:     model.AccountSystem.ActorID,
							HistoryActorName:   model.AccountSystem.ActorName,
							HistoryActorRole:   model.AccountSystem.ActorName,
							HistoryCreatedAt:   now,
							HistoryCreatedName: model.AccountSystem.ActorName,
							HistoryRemark:      tempRemarksPieceHistory.ToString(),
						})

						// Adding time CARGO status PUPC
						listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
							SttNo:         sttNoWithData.Stt.SttNo,
							SttStatus:     model.PUPC,
							SttStatusTime: now,
						})
					}
				}

				// PUP
				tmpSttPieceHistory = append(tmpSttPieceHistory, model.SttPieceHistory{
					SttPieceID:         sttPiece.SttPieceID,
					HistoryStatus:      model.PUP,
					HistoryLocation:    sttNoWithData.Stt.SttOriginCityID,
					HistoryActorID:     model.AccountSystem.ActorID,
					HistoryActorName:   model.AccountSystem.ActorName,
					HistoryActorRole:   model.AccountSystem.ActorName,
					HistoryCreatedAt:   now,
					HistoryCreatedName: model.AccountSystem.ActorName,
					HistoryRemark:      tempRemarksPieceHistory.ToString(),
				})

				// Adding time CARGO status PUP
				listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
					SttNo:         sttNoWithData.Stt.SttNo,
					SttStatus:     model.PUP,
					SttStatusTime: now,
				})

				// Set district name value if exist on baggingPartner
				districtName := ""
				if cargoPartner.Data.PartnerLocation.District != nil {
					districtName = cargoPartner.Data.PartnerLocation.District.Name
				}

				tempRemarksPieceHistory = model.RemarkPieceHistory{
					HistoryLocationName: cargoPartner.Data.PartnerLocation.City.Name,
					HistoryDistrictName: districtName,
				}

				//Force STI-SC
				tmpSttPieceHistory = append(tmpSttPieceHistory, model.SttPieceHistory{
					SttPieceID:         sttPiece.SttPieceID,
					HistoryStatus:      model.STISC,
					HistoryLocation:    cargoPartner.Data.PartnerLocation.CityCode,
					HistoryActorID:     model.AccountSystem.ActorID,
					HistoryActorName:   model.AccountSystem.ActorName,
					HistoryActorRole:   model.AccountSystem.ActorName,
					HistoryCreatedAt:   now,
					HistoryCreatedName: model.AccountSystem.ActorName,
					HistoryRemark:      tempRemarksPieceHistory.ToString(),
				})

				// Adding time CARGO status STI-SC
				listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
					SttNo:         sttNoWithData.Stt.SttNo,
					SttStatus:     model.STISC,
					SttStatusTime: now,
				})

				//Force STI
				SetHub(c.cityRepo, c.districtRepo, &stt_piece_history_remark_helper.SetHubParams{
					Ctx:                 selfCtx,
					Token:               params.Token,
					HubID:               params.HubID,
					HubName:             params.HubName,
					HubOriginCity:       params.HubOriginCity,
					HubDistrictCode:     params.HubDistrictCode,
					RemarksPieceHistory: &tempRemarksPieceHistory,
				})
				tmpSttPieceHistory = append(tmpSttPieceHistory, model.SttPieceHistory{
					SttPieceID:         sttPiece.SttPieceID,
					HistoryStatus:      model.STI,
					HistoryLocation:    cargoPartner.Data.PartnerLocation.CityCode,
					HistoryActorID:     model.AccountSystem.ActorID,
					HistoryActorName:   model.AccountSystem.ActorName,
					HistoryActorRole:   model.AccountSystem.ActorName,
					HistoryCreatedAt:   now,
					HistoryCreatedName: model.AccountSystem.ActorName,
					HistoryRemark:      tempRemarksPieceHistory.ToString(),
				})

				// Adding time CARGO status STI
				listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
					SttNo:         sttNoWithData.Stt.SttNo,
					SttStatus:     model.STI,
					SttStatusTime: now,
				})
			}

			// if last status pupc
			if sttNoWithData.Stt.SttLastStatusID == model.PUPC {
				sttMeta := sttNoWithData.Stt.SttMetaToStruct()
				if sttMeta == nil {
					// get origin booking city
					sttMeta = &model.SttMeta{}
					bookingCity, err := c.cityRepo.Get(selfCtx, sttNoWithData.Stt.SttOriginCityID, params.Token)
					if err == nil && bookingCity != nil {
						sttMeta.OriginCityName = bookingCity.Name
					}
				}

				// assign remarks piece history
				tempRemarksPieceHistory.HistoryLocationName = sttMeta.OriginCityName
				tempRemarksPieceHistory.HistoryDistrictName = sttMeta.OriginDistrictName
				tempRemarksPieceHistory.DriverName = model.AccountSystem.ActorName

				// PUP
				tmpSttPieceHistory = append(tmpSttPieceHistory, model.SttPieceHistory{
					SttPieceID:         sttPiece.SttPieceID,
					HistoryStatus:      model.PUP,
					HistoryLocation:    sttNoWithData.Stt.SttOriginCityID,
					HistoryActorID:     model.AccountSystem.ActorID,
					HistoryActorName:   model.AccountSystem.ActorName,
					HistoryActorRole:   model.AccountSystem.ActorName,
					HistoryCreatedAt:   now,
					HistoryCreatedName: model.AccountSystem.ActorName,
					HistoryRemark:      tempRemarksPieceHistory.ToString(),
				})

				// Adding time CARGO status PUP
				listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
					SttNo:         sttNoWithData.Stt.SttNo,
					SttStatus:     model.PUP,
					SttStatusTime: now,
				})

				// Set district name value if exist on baggingPartner
				districtName := ""
				if cargoPartner.Data.PartnerLocation.District != nil {
					districtName = cargoPartner.Data.PartnerLocation.District.Name
				}

				tempRemarksPieceHistory = model.RemarkPieceHistory{
					HistoryLocationName: cargoPartner.Data.PartnerLocation.City.Name,
					HistoryDistrictName: districtName,
				}

				//Force STI-SC
				tmpSttPieceHistory = append(tmpSttPieceHistory, model.SttPieceHistory{
					SttPieceID:         sttPiece.SttPieceID,
					HistoryStatus:      model.STISC,
					HistoryLocation:    cargoPartner.Data.PartnerLocation.CityCode,
					HistoryActorID:     model.AccountSystem.ActorID,
					HistoryActorName:   model.AccountSystem.ActorName,
					HistoryActorRole:   model.AccountSystem.ActorName,
					HistoryCreatedAt:   now,
					HistoryCreatedName: model.AccountSystem.ActorName,
					HistoryRemark:      tempRemarksPieceHistory.ToString(),
				})

				// Adding time CARGO status STI-SC
				listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
					SttNo:         sttNoWithData.Stt.SttNo,
					SttStatus:     model.STISC,
					SttStatusTime: now,
				})

				//Force STI
				SetHub(c.cityRepo, c.districtRepo, &stt_piece_history_remark_helper.SetHubParams{
					Ctx:                 selfCtx,
					Token:               params.Token,
					HubID:               params.HubID,
					HubName:             params.HubName,
					HubOriginCity:       params.HubOriginCity,
					HubDistrictCode:     params.HubDistrictCode,
					RemarksPieceHistory: &tempRemarksPieceHistory,
				})
				tmpSttPieceHistory = append(tmpSttPieceHistory, model.SttPieceHistory{
					SttPieceID:         sttPiece.SttPieceID,
					HistoryStatus:      model.STI,
					HistoryLocation:    cargoPartner.Data.PartnerLocation.CityCode,
					HistoryActorID:     model.AccountSystem.ActorID,
					HistoryActorName:   model.AccountSystem.ActorName,
					HistoryActorRole:   model.AccountSystem.ActorName,
					HistoryCreatedAt:   now,
					HistoryCreatedName: model.AccountSystem.ActorName,
					HistoryRemark:      tempRemarksPieceHistory.ToString(),
				})

				// Adding time CARGO status STI
				listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
					SttNo:         sttNoWithData.Stt.SttNo,
					SttStatus:     model.STI,
					SttStatusTime: now,
				})
			}

			// if last status pup
			if sttNoWithData.Stt.SttLastStatusID == model.PUP {
				//Force STI-SC
				tmpSttPieceHistory = append(tmpSttPieceHistory, model.SttPieceHistory{
					SttPieceID:         sttPiece.SttPieceID,
					HistoryStatus:      model.STISC,
					HistoryLocation:    cargoPartner.Data.PartnerLocation.CityCode,
					HistoryActorID:     model.AccountSystem.ActorID,
					HistoryActorName:   model.AccountSystem.ActorName,
					HistoryActorRole:   model.AccountSystem.ActorName,
					HistoryCreatedAt:   now,
					HistoryCreatedName: model.AccountSystem.ActorName,
					HistoryRemark:      tempRemarksPieceHistory.ToString(),
				})

				// Adding time CARGO status STI-SC
				listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
					SttNo:         sttNoWithData.Stt.SttNo,
					SttStatus:     model.STISC,
					SttStatusTime: now,
				})

				//Force STI
				SetHub(c.cityRepo, c.districtRepo, &stt_piece_history_remark_helper.SetHubParams{
					Ctx:                 selfCtx,
					Token:               params.Token,
					HubID:               params.HubID,
					HubName:             params.HubName,
					HubOriginCity:       params.HubOriginCity,
					HubDistrictCode:     params.HubDistrictCode,
					RemarksPieceHistory: &tempRemarksPieceHistory,
				})
				tmpSttPieceHistory = append(tmpSttPieceHistory, model.SttPieceHistory{
					SttPieceID:         sttPiece.SttPieceID,
					HistoryStatus:      model.STI,
					HistoryLocation:    cargoPartner.Data.PartnerLocation.CityCode,
					HistoryActorID:     model.AccountSystem.ActorID,
					HistoryActorName:   model.AccountSystem.ActorName,
					HistoryActorRole:   model.AccountSystem.ActorName,
					HistoryCreatedAt:   now,
					HistoryCreatedName: model.AccountSystem.ActorName,
					HistoryRemark:      tempRemarksPieceHistory.ToString(),
				})

				// Adding time CARGO status STI
				listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
					SttNo:         sttNoWithData.Stt.SttNo,
					SttStatus:     model.STI,
					SttStatusTime: now,
				})
			}

			// Set district name value if exist on baggingPartner
			districtName := ""
			if cargoPartner.Data.PartnerLocation.District != nil {
				districtName = cargoPartner.Data.PartnerLocation.District.Name
			}

			tempRemarksPieceHistory.CargoNumber = cargoNumber
			tempRemarksPieceHistory.TruckNumber = truckNumber
			tempRemarksPieceHistory.TrainNumber = trainNumber
			tempRemarksPieceHistory.ShipNumber = shipNumber
			tempRemarksPieceHistory.HistoryDistrictName = districtName
			SetHub(c.cityRepo, c.districtRepo, &stt_piece_history_remark_helper.SetHubParams{
				Ctx:                 selfCtx,
				Token:               params.Token,
				HubID:               params.HubID,
				HubName:             params.HubName,
				HubOriginCity:       params.HubOriginCity,
				HubDistrictCode:     params.HubDistrictCode,
				RemarksPieceHistory: &tempRemarksPieceHistory,
			})
			tmpSttPieceHistory = append(tmpSttPieceHistory, model.SttPieceHistory{
				SttPieceID:         sttPiece.SttPieceID,
				HistoryStatus:      lastStatusCargo,
				HistoryLocation:    cargoPartner.Data.PartnerLocation.CityCode,
				HistoryActorID:     cargoPartner.Data.ID,
				HistoryActorName:   cargoPartner.Data.Name,
				HistoryActorRole:   params.PartnerTypeToken,
				HistoryCreatedAt:   now,
				HistoryCreatedName: params.AccountName,
				HistoryCreatedBy:   params.AccountID,
				HistoryRemark:      tempRemarksPieceHistory.ToString(),
			})

			// Adding time CARGO status Last Status Cargo
			listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
				SttNo:         sttNoWithData.Stt.SttNo,
				SttStatus:     lastStatusCargo,
				SttStatusTime: now,
			})

			req.Cargo.CargoTotalSttPiece++
			req.Cargo.CargoTotalGrossWeight += sttPiece.SttPieceGrossWeight
			req.Cargo.CargoTotalVolumeWeight += sttPiece.SttPieceVolumeWeight
		}
		req.SttHistory = append(req.SttHistory, model.SttAndPieceHistory{
			SttID:        int(sttNoWithData.Stt.SttID),
			Status:       lastStatusCargo,
			History:      tmpSttPieceHistory,
			SttActorID:   params.PartnerID,
			SttActorRole: params.PartnerTypeToken,
			SttActorName: cargoPartner.Data.Name,
		})
	}

	//ReAssign value of cargo total stt piece
	req.Cargo.CargoTotalSttPiece = totalPcs

	if lastStatusCargo == model.CARGOPLANE && params.IsCargoPlaneManual {
		depDate, _ := shared.ParseUTC7(shared.FormatDateTime, params.DepartureDate)
		arrDate, _ := shared.ParseUTC7(shared.FormatDateTime, params.ArrivalDate)
		cargoFlightReq = append(cargoFlightReq, model.CargoFlight{
			CFNumber:             params.CargoVehicleNo,
			CFRemarks:            dbr.NewNullString(req.CargoRemark),
			CFAirportOrigin:      params.OriginAirportCode,
			CFAirportDestination: params.DestinationAirportCode,
			CFEstDepartureTime:   &depDate,
			CFEstArrivalTime:     &arrDate,
			CFCreatedAt:          now,
			CFUpdatedAt:          &now,
		})
	}

	// request cargo retry
	cargoRetryRequest := model.CreateDetailCargoRetry{
		TotalFlight:      params.TotalFlight,
		SequentialFlight: params.SequentialFlight,
		CreateDetailCargoTrx: model.CreateDetailCargoTrx{
			Cargo:               req.Cargo,
			SttCargoDetail:      req.SttCargoDetail,
			SttHistory:          req.SttHistory,
			RtcID:               int64(params.RtcID),
			IsFromRtc:           params.IsFromRtc,
			CargoFlight:         cargoFlightReq,
			BagCode:             bagCode,
			CuttoffTime:         shared.CheckTimeZeroToStringLayout(cutoffTime, "15:04:05"),
			CutoffDate:          shared.CheckTimeZeroToStringLayout(cutoffTime, "2006-01-02"),
			IsOverCutoffLimit:   req.CargoOverCutoffLimit,
			IsOverCutoffLimitH0: cargoOverLimitH0,
			RetryCargoID:        int64(params.RebookingID),
		},
		NgenBookingRequest: requestNgen,
	}

	// marshal response booking nGen
	ngenResByte := []byte{}
	if ngenRes != nil {
		ngenResByte, err = json.Marshal(ngenRes)
		if err != nil {
			go c.deleteCacheForBagOrStt(context.Background(), params)

			return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to marshal ngenRes",
				"id": "Gagal marshal ngenRes",
			})
		}
	}

	// marshal request retry cargo
	cargoRetryRequestByte, err := json.Marshal(cargoRetryRequest)
	if err != nil {
		go c.deleteCacheForBagOrStt(context.Background(), params)

		return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed to marshal cargoRetryRequest",
			"id": "Gagal marshal cargoRetryRequest",
		})
	}

	// retry cargo request
	retryCargoReq := model.RetryCargo{
		RcNo:                        req.Cargo.CargoNo,
		RcType:                      req.CargoType,
		RcVehicleNo:                 req.CargoVehicleNo,
		RcOriginCityCode:            params.OriginCityCode,
		RcOriginAirportCode:         params.OriginAirportCode,
		RcDestinationAirportCode:    params.DestinationAirportCode,
		RcDestinationCityCode:       params.DestinationCityCode,
		RcPartnerName:               cargoPartner.Data.Name,
		RcPartnerCode:               cargoPartner.Data.Code,
		RcPartnerType:               cargoPartner.Data.Type,
		RcPartnerID:                 params.PartnerID,
		RcTotalStt:                  len(successSttNoWithData),
		RcTotalSttPiece:             req.Cargo.CargoTotalSttPiece,
		RcTotalGrossWeight:          req.Cargo.CargoTotalGrossWeight,
		RcTotalVolumeWeight:         req.Cargo.CargoTotalVolumeWeight,
		RcTotalCustomGrossWeight:    params.ActualTotalCargoGrossWeight,
		RcTotalCustomVolumeWeight:   params.ActualTotalCargoVolumeWeight,
		RcTotalEstimateGrossWeight:  params.EstTotalCargoGrossWeight,
		RcTotalEstimateVolumeWeight: params.EstTotalCargoVolumeWeight,
		RcCargoProductTypeCode:      params.ProductCargo,
		RcCommodityGroupCode:        params.CargoCommodity,
		RcEstLengthDimension:        params.EstimationDimension.CargoEstLengthDimension,
		RcEstWidthDimension:         params.EstimationDimension.CargoEstWidthDimension,
		RcEstHeightDimension:        params.EstimationDimension.CargoEstHeightDimension,
		RcActualLengthDimension:     params.ActualDimension.CargoActualLengthDimension,
		RcActualWidthDimension:      params.ActualDimension.CargoActualWidthDimension,
		RcActualHeightDimension:     params.ActualDimension.CargoActualHeightDimension,
		RcNog:                       params.Nog,
		RcStatus:                    model.RETRY_BOOKING_STATUS_PROCESS,
		RcRequest:                   string(cargoRetryRequestByte),
		RcResponse:                  string(ngenResByte),
		RcCreatedAt:                 now,
		RcCreatedBy:                 params.AccountID,
		RcCreatedName:               params.AccountName,
		RcUpdatedAt:                 &now,
		RcUpdatedBy:                 params.AccountID,
		RcUpdatedName:               params.AccountName,
		RcDepartureDate:             req.Cargo.CargoDepartureDate,
		RcArrivalDate:               req.Cargo.CargoArrivalDate,
		RcEstimateDepartureDate:     now,
		RcActualPiece:               params.CargoActualPiece,
		RcHubID:                     params.HubID,
		RcHubName:                   params.HubName,
		RcIsCargoPlaneManual:        params.IsCargoPlaneManual,
		RcOverCutoffLimit:           params.CargoOverCutoffLimit,
		RcRtcTurnAuto:               params.CargoRtcTurnAuto,
		RcIsUseSabre:                params.IsUseSabre,
		RcOverCutoffLimitH0:         cargoOverLimitH0,
	}
	req.RtcID = int64(params.RtcID)
	req.RetryCargoID = int64(params.RebookingID)

	// assign retry cargo remark
	dataByte, err := json.Marshal(model.CargoRemark{
		TotalFlight:         params.TotalFlight,
		SequentialFlight:    params.SequentialFlight,
		OriginCityName:      originCity.Name,
		DestinationCityName: destinationCity.Name,
		AirportOriginName: func() string {
			if lastStatusCargo == model.CARGOPLANE {
				return originAirport.AirportName
			}
			return ""
		}(),
		AirportDestinationName: func() string {
			if lastStatusCargo == model.CARGOPLANE {
				return destinationAirport.AirportName
			}
			return ""
		}(),
	})
	if err != nil {
		retryCargoReq.RcRemark = ``
	}
	retryCargoReq.RcRemark = string(dataByte)

	retryCargoDetailReq := []model.RetryCargoDetail{}
	for _, val := range req.SttCargoDetail {
		retryCargoDetailReq = append(retryCargoDetailReq, model.RetryCargoDetail{
			RcdSttID:      val.CargoDetailSttID,
			RcdSttPieceID: val.CargoDetailSttPieceID,
			RcdCreatedAt:  val.CargoDetailCreatedAt,
			RcdBagNo:      val.CargoDetailBagNo,
			RcdIsDeleted:  false,
		})
	}

	isCargoPlanAndRTC := func() bool {
		if lastStatusCargo == model.CARGOPLANE && !params.IsCargoPlaneManual && params.IsFromRtc {
			return true
		}
		return false
	}()

	isUseAwbReserveNgenLogic := false
	isNotUseAwbReserveNgenLogic := false

	if (c.cfg.RetryCargoEnable() && isUseAwbReserve && !isCargoPlanAndRTC) || (c.cfg.RetryCargoEnable() && isUseAwbReserve) {
		isUseAwbReserveNgenLogic = true
	}

	if !isUseAwbReserve && !isCargoPlanAndRTC {
		isNotUseAwbReserveNgenLogic = true
	}

	if isUseAwbReserveNgenLogic {
		// request insert retry booking cargo
		retryCargoReq.RcStatus = model.PROCESS_IMMEDIATE
		retryCargoReq.RcNo = AwbNo
		retryCargoReq.RcVehicleNo = func() string {
			if len(retryCargoReq.RcVehicleNo) > 0 {
				return retryCargoReq.RcVehicleNo
			}
			return FlightNoRetryCargo
		}()
		_, err = c.retryCargoRepo.Create(selfCtx, &model.RetryCargoParams{
			RetryCargo:          retryCargoReq,
			RetryCargoDetail:    retryCargoDetailReq,
			AwbStatus:           model.CargoReserveStatusOnProcess,
			AwbID:               awbID,
			IsAwbReserve:        isUseAwbReserve,
			IsPublishRetryCargo: true,
			RetryPubsubPayload: model.RetryPubsubPayload{
				AwbNo:     AwbNo,
				PublishAt: now.String(),
				PublishBy: strconv.FormatInt(int64(params.AccountID), 10),
			},
			RtcID: int64(params.RtcID),
		})
		if err != nil {
			go c.deleteCacheForBagOrStt(context.Background(), params)

			return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to create retry cargo",
				"id": "Gagal membuat retry cargo",
			})
		}
		if params.RebookingID > 0 {
			// soft delete retry cargo
			go c.retryCargoRepo.Delete(ctx, params.RebookingID)
		}
	}

	awbNoNgenSabre := shared.GenerateCodeUnique(`FLIGHT`, params.DestinationCityCode, strconv.Itoa(int(c.timeRepo.Now(time.Now()).UnixNano())/int(time.Millisecond)), `-`)

	if params.RebookingID > 0 {
		paramsRetryCargo := &model.RetryCargoViewParams{}
		paramsRetryCargo.FilterByRetryID = params.RebookingID
		retryData, _ := c.retryCargoRepo.Get(selfCtx, paramsRetryCargo)

		if retryData != nil {
			awbNoNgenSabre = retryData.RcNo
		}

	}

	if strings.ToUpper(params.CargoType) == model.PLANE && (errNgen != nil || ngenRes == nil) {
		retryCargoReq.RcNo = awbNoNgenSabre
		retryCargoReq.RcStatus = model.RETRY_BOOKING_STATUS_FAILED
		retryCargoReq.RcVehicleNo = func() string {
			if len(retryCargoReq.RcVehicleNo) > 0 {
				return retryCargoReq.RcVehicleNo
			}
			return FlightNoRetryCargo
		}()
		retryCargoReq.RcIsRetryFromRtc = func() bool {
			if params.RebookingID > 0 {
				return true
			}
			return false
		}()
		retryCargoReq.RcResponse = errNgen.Error()
		// request insert retry booking cargo
		if c.getExistKeyRetryCargo(selfCtx, params) == 0 && params.RebookingID < 1 {
			retryId, err := c.retryCargoRepo.Create(selfCtx, &model.RetryCargoParams{
				RetryCargo:       retryCargoReq,
				RetryCargoDetail: retryCargoDetailReq,
				AwbStatus:        awbReserveStatus,
				AwbID:            awbID,
				RtcID:            int64(params.RtcID),
			})
			if err != nil {
				return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Failed to create retry cargo",
					"id": "Gagal membuat retry cargo",
				})
			}

			c.setCreateRetryCargo(context.Background(), params, retryId)
		}

		if params.RebookingID > 0 {
			// soft delete retry cargo
			_, err := c.retryCargoRepo.Create(selfCtx, &model.RetryCargoParams{
				RetryCargo:       retryCargoReq,
				RetryCargoDetail: retryCargoDetailReq,
				AwbStatus:        awbReserveStatus,
				AwbID:            awbID,
				RtcID:            int64(params.RtcID),
			})
			if err != nil {
				return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Failed to create retry cargo",
					"id": "Gagal membuat retry cargo",
				})
			}
			go c.retryCargoRepo.Delete(ctx, params.RebookingID)
		}
		return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Booking Ngen Problem Error : " + errNgen.Error(),
			"id": "Terjadi Error Booking Ngen : " + errNgen.Error(),
		})

	}

	if isNotUseAwbReserveNgenLogic || params.CargoRtcTurnAuto {
		if errNgen == nil {
			dataCargo, err := c.cargoRepo.CreateCargoDetailTrx(selfCtx, &model.CreateDetailCargoTrx{
				Cargo:            req.Cargo,
				SttCargoDetail:   req.SttCargoDetail,
				SttHistory:       req.SttHistory,
				CargoFlight:      cargoFlightReq,
				BagCode:          bagCode,
				IsAwbReserve:     isUseAwbReserve,
				RtcID:            int64(params.RtcID),
				IsFromRtc:        params.IsFromRtc,
				AwbReserveStatus: awbReserveStatus,
				AwbID:            awbID,
				RetryCargoID:     int64(params.RebookingID),
			})
			if err != nil {
				res = &cargo_v2.CreateCargoV2Response{
					CargoNo:        req.CargoNo,
					TotalSttFailed: len(listFailedSttNo),
					ListSttFailed:  listFailedSttNo,
				}
				go c.deleteCacheForBagOrStt(context.Background(), params)

				return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "Failed to create cargo",
					"id": "Gagal membuat cargo",
				})
			}
			if params.RebookingID > 0 {
				// soft delete retry cargo
				go c.retryCargoRepo.Delete(ctx, params.RebookingID)
			}
			err = c.sttDueUC.InsertSttDueFromCargo(context.Background(), &stt_due.GenerateSttDueTargetStidestParams{
				CargoData: dataCargo,
				Token:     params.Token,
				ActFrom:   "cargo:" + opName,
			})
		}

		/*
		 * Updating list time stt status
		 */

		go c.sttActivityUc.UpdateSttTime(context.Background(), &stt_activity.SttActivityRequest{
			ListSttData: listSttUpdateTime,
		})

		go func() {
			batchIntervalInSecond := 60 * 5
			goCtx, cancel := lputils.CreateContext(batchIntervalInSecond)
			defer cancel()

			sttNos := []string{}
			for sttNo, data := range successSttNoWithData {
				sttNos = append(sttNos, sttNo)
				c.readyToCargo.UpdateInactiveRTCBySttId(goCtx, int(data.Stt.SttID))

				statusSubmitParams := &model.UpdateSttStatusWithExtendForMiddleware{
					UpdateSttStatus: &model.UpdateSttStatus{
						SttNo:      data.Stt.GetSttElexysNoOrSttNo(),
						Datetime:   now.UTC(),
						StatusCode: lastStatusCargo,
						Location:   originCity.Code,
						Remarks:    fmt.Sprintf(`Paket dicargo oleh %s`, cargoPartner.Data.Name),
						City:       originCity.Name,
						UpdatedBy:  cargoPartner.Data.Name,
						UpdatedOn:  now.UTC(),
					},
					ServiceType:      model.PACKAGESERVICE,
					Product:          data.Stt.SttProductType,
					Pieces:           data.Stt.SttTotalPiece,
					GrossWeight:      data.Stt.SttGrossWeight,
					VolumeWeight:     data.Stt.SttVolumeWeight,
					ChargeableWeight: data.Stt.SttChargeableWeight,
					BookedForType:    data.Stt.SttBookedForType,
				}
				sttPieceHistories := GetSttPieceHistoriesFromSttAndPieceHistories(req.SttHistory, int(data.Stt.SttID))
				AppendLastAndSystemStatus(AppendLastAndSystemStatusParams{
					StatusSubmitParams: statusSubmitParams,
					SttPieceHistories:  sttPieceHistories,
					PartnerName:        cargoPartner.Data.Name,
				})
				c.gatewaySttStatusUc.StatusSubmit(goCtx, statusSubmitParams)
				key := fmt.Sprintf(`%s:%s:%s`, model.CacheBagOrSttProcessCargoV2, "", data.Stt.SttNo)
				c.cacheRepo.DeleteCache(goCtx, key)

			}
			if len(sttNos) > 0 {
				c.deleteCacheForBagOrStt(goCtx, params)
			}
			c.sttDueRepo.UpdateIsShowBulk(goCtx, &model.STTDueUpdateIsShow{STTNos: sttNos})
		}()

		go func() {
			if c.cfg.LUWJISTIK_ACTIVE() {
				for _, data := range successSttNoWithData {
					if data.Stt.SttProductType == model.INTERPACK && lastStatusCargo == model.CARGOTRUCK && data.Stt.SttDestinationCityID == req.CargoDestinationCityCode { // cargo truck // destination cargo dan stt sama
						_ = c.luwjistik.BookingFromCargo(context.Background(), data, credential)
					}
				}
			}
		}()
	}

	if isCargoPlanAndRTC && !params.CargoRtcTurnAuto {

		searchThreshold := 0
		if len(params.NgenBooking.FlightPlanForEcargo) > 0 {
			searchThreshold = params.NgenBooking.FlightPlanForEcargo[0].FlightThresholdTime
		}

		departureDate := ""
		departureDates := strings.Split(params.DepartureDate, " ")
		if len(departureDates) > 0 {
			departureDate = departureDates[0]
		}

		flightSchedules, err := c.cargoSearchFlightUc.SearchFlightForBooking(selfCtx, &cargo.CargoConfigurationSearchFlightFindingRequest{
			DepartureDate:                departureDate,
			DepartureDateConfig:          params.DepartureDate,
			OriginCityCode:               params.OriginCityCode,
			OriginAirportCode:            params.OriginAirportCode,
			DestinationAirportCode:       params.DestinationAirportCode,
			ActualTotalCargoGrossweight:  params.ActualTotalCargoGrossWeight,
			ActualTotalCargoVolumeweight: params.ActualTotalCargoVolumeWeight,
			ThresholdTime:                searchThreshold,
			PartnerID:                    int64(params.PartnerID),
			AccountID:                    int64(params.AccountID),
			UserToken:                    params.Token,
			IsFromBooking:                true,
			RequestID:                    opName,
			HostURL:                      params.HostUrl,
		})
		if err != nil {
			go c.deleteCacheForBagOrStt(context.Background(), params)
			retryCargoReq.RcNo = awbNoNgenSabre
			retryCargoReq.RcStatus = model.RETRY_BOOKING_STATUS_FAILED
			// request insert retry booking cargo
			retryCargoReq.RcVehicleNo = func() string {
				if len(retryCargoReq.RcVehicleNo) > 0 {
					return retryCargoReq.RcVehicleNo
				}
				return FlightNoRetryCargo
			}()
			retryCargoReq.RcIsRetryFromRtc = func() bool {
				if params.RebookingID > 0 {
					return true
				}
				return false
			}()
			if c.getExistKeyRetryCargo(selfCtx, params) == 0 {
				retryId, err := c.retryCargoRepo.Create(selfCtx, &model.RetryCargoParams{
					RetryCargo:       retryCargoReq,
					RetryCargoDetail: retryCargoDetailReq,
					AwbStatus:        awbReserveStatus,
					AwbID:            awbID,
					RtcID:            int64(params.RtcID),
				})
				if err != nil {
					return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
						"en": "Failed to create retry cargo",
						"id": "Gagal membuat retry cargo",
					})
				}
				c.setCreateRetryCargo(context.Background(), params, retryId)

			}

			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to search flight schedule",
				"id": "Gagal mencari jadwal penerbangan",
			})
		}

		getAirportData := func(ctx context.Context, airportCode string) *model.Airport {
			airport, err := c.airportRepo.Get(ctx, airportCode, params.Token)
			if err != nil || airport == nil {
				return &model.Airport{
					AirportCode: airportCode,
				}
			}
			return airport
		}

		req.Cargo.CargoNo = awbNoNgenSabre
		cargoRetryRequest := model.CreateDetailCargoRetry{
			CreateDetailCargoTrx: model.CreateDetailCargoTrx{
				Cargo:          req.Cargo,
				SttCargoDetail: req.SttCargoDetail,
				SttHistory:     req.SttHistory,
				IsFromRtc:      req.IsFromRtc,
				BagCode:        bagCode,
				RtcID:          int64(params.RtcID),
			},
			NgenBookingRequest: requestNgen,
		}

		for _, fs := range flightSchedules {
			isRootStation := 1
			thresholdTime := c.cfg.NgenForceThresholdTime()
			if len(fs.Itineraries) == 1 {
				isRootStation = 0
			}

			var sfs model.SabreFlightSchedule
			for _, itinerary := range fs.Itineraries {
				// for data GENESIS
				remark := ``
				dataRemark := model.CargoRemark{}
				dataRemark.GenerateCargoRemarkLocation(
					getAirportData(selfCtx, itinerary.OriginAirportCode),
					getAirportData(selfCtx, itinerary.DestinationAirportCode),
				)
				dataByte, _ := json.Marshal(&dataRemark)
				if err == nil {
					remark = string(dataByte)
				}
				depatureDate, _ := shared.ParseUTC7(shared.FormatDateTime, itinerary.DepartureDate)
				arrivalDate, _ := shared.ParseUTC7(shared.FormatDateTime, itinerary.ArrivalDate)
				sfs.CargoFlight = append(sfs.CargoFlight, model.CargoFlight{
					CFNumber:             itinerary.FlightNo,
					CFRemarks:            dbr.NewNullString(remark),
					CFAirportOrigin:      itinerary.OriginAirportCode,
					CFAirportDestination: itinerary.DestinationAirportCode,
					CFEstDepartureTime:   &depatureDate,
					CFEstArrivalTime:     &arrivalDate,
					CFCreatedAt:          now,
					CFUpdatedAt:          &now,
				})

				// data for NGEN
				flightPlan := model.FlightPlanForEcargo{
					DailyFlightSNo: itinerary.DailyFlightNo,
					FlightNo:       itinerary.FlightNo,
					// FlightDate:        itinerary.DepartureDate, // for now ngen use date time only
					FlightDateTime:    itinerary.DepartureDate,
					FlightOrigin:      itinerary.OriginAirportCode,
					FlightDestination: itinerary.DestinationAirportCode,
					IsRootStation:     isRootStation,
					ThresholdTime:     thresholdTime,
				}
				sfs.FlightPlanForEcargo = append(sfs.FlightPlanForEcargo, flightPlan)
			}

			cargoRetryRequest.SabreFlightSchedules = append(cargoRetryRequest.SabreFlightSchedules, sfs)
		}

		cargoRetryRequest.TotalFlight = len(cargoRetryRequest.SabreFlightSchedules)

		rcRemark := retryCargoReq.RcRemarkToStruct()
		rcRemark.TotalFlight = cargoRetryRequest.TotalFlight
		retryCargoReq.RcRemark = rcRemark.ToString()

		cargoRetryRequestByte, err := json.Marshal(cargoRetryRequest)
		if err != nil {
			go c.deleteCacheForBagOrStt(context.Background(), params)

			return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to marshal cargoRetryRequest",
				"id": "Gagal marshal cargoRetryRequest",
			})
		}

		AwbNo = awbNoNgenSabre
		retryCargoReq.RcRequest = string(cargoRetryRequestByte)
		retryCargoReq.RcStatus = model.RETRY_BOOKING_STATUS_PROCESS_SABRE
		retryCargoReq.RcNo = AwbNo
		retryCargoReq.RcBookingRequestID = ""
		retryCargoReq.RcVehicleNo = func() string {
			if len(retryCargoReq.RcVehicleNo) > 0 {
				return retryCargoReq.RcVehicleNo
			}
			return FlightNoRetryCargo
		}()
		retryCargoReq.RcIsRetryFromRtc = func() bool {
			if params.RebookingID > 0 {
				return true
			}
			return false
		}()

		rc := &model.RetryCargoParams{
			RetryCargo:       retryCargoReq,
			RetryCargoDetail: retryCargoDetailReq,
			RtcID:            int64(params.RtcID),
		}
		_, err = c.retryCargoRepo.Create(selfCtx, rc)
		if err != nil {
			go c.deleteCacheForBagOrStt(context.Background(), params)

			return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to create retry cargo",
				"id": "Gagal membuat retry cargo",
			})
		}

		c.retryCargoUc.SendPubsubSabre(selfCtx, &retrycargo.RequestPubSubCargoSabre{
			RcID: rc.RetryCargo.RcID,
		})
	}

	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.inactiveRtc(goCtx, successSttNoWithData)
	}, 60*5)

	res = &cargo_v2.CreateCargoV2Response{
		CargoNo:          req.CargoNo,
		TotalSttSuccess:  len(successSttNoWithData),
		TotalSttFailed:   len(listFailedSttNo),
		ListSttFailed:    listFailedSttNo,
		IsCargoUsingAwb:  isUseAwbReserve,
		BookingRequestID: awbNoNgenSabre,
	}

	if res.TotalSttSuccess > 0 {
		status = `success`
		if res.TotalSttFailed > 0 {
			status = `partially`
		}
	}

	if status != `failed` {
		go lputils.TrackGoroutine(func(goCtx context.Context) {
			encodedPayload := params.StructToEncodedString()
			key := model.SetCacheJsonPayloadCargoV2(int64(params.AccountID), encodedPayload)
			c.cacheRepo.DeleteCache(goCtx, key)
			if c.getExistKeyRetryCargo(goCtx, params) > 0 {
				go c.retryCargoRepo.Delete(goCtx, int(c.getExistKeyRetryCargo(goCtx, params)))
			}
		}, 60*5)
	}

	if c.cfg.IsForceRtcInactive() && params.RtcID > 0 {
		go func() {
			time.Sleep(time.Duration(c.cfg.ForceInactiveRtcAfterSecond()) * time.Second)
			c.readyToCargoRepo.SetInactive(context.Background(), params.RtcID)
		}()
	}

	return res, nil
}
