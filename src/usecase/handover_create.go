package usecase

import (
	"context"
	"fmt"
	"time"

	"github.com/Lionparcel/go-lptool/lputils"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/general"
	"github.com/Lionparcel/hydra/src/usecase/handover"
	"github.com/Lionparcel/hydra/src/usecase/stt_activity"
	"github.com/Lionparcel/hydra/src/usecase/stt_piece_history_remark_helper"
)

func (c *handoverCtx) CreateHandover(ctx context.Context, form *handover.HandoverRequest) (*handover.HandoverResponse, error) {
	opName := "UsecaseHandover-CreateHandover"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	res := new(handover.HandoverResponse)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": res, "err": err})
	}()
	if err = form.Validate(c.cfg); err != nil {
		return nil, err
	}

	partnerInfo, err := c.createHandoverGetPartnerInfo(selfCtx, form)
	if err != nil {
		return nil, err
	}

	now, _ := shared.ParseUTC7(shared.FormatDateTime, c.timeRepo.Now(time.Now()).Format(shared.FormatDateTime))
	sttDetail, handoverData, err := c.createHandover(selfCtx, form, partnerInfo, now)
	if err != nil {
		return nil, err
	}

	req := &handover.RequestCreateHandoverProcess{
		HandoverData:           *handoverData,
		AccountID:              form.AccountID,
		AccountName:            form.AccountName,
		PartnerType:            form.PartnerType,
		DestinationPartnerCode: form.DestinationPartnerCode,
		HandoverCreatedAt:      now,
		Token:                  form.Token,
		HubID:                  form.HubID,
		HubName:                form.HubName,
		HubOriginCity:          form.HubOriginCity,
		HubDistrictCode:        form.HubDistrictCode,
	}
	isSttNoSuccess, sttFailed, handoverData, sttPieceHistoriesPerSTT := c.processCreateHandover(selfCtx, req, partnerInfo, sttDetail)

	totalSttNoFailed := len(sttFailed)
	totalSttNoSuccess := len(isSttNoSuccess)
	res = &handover.HandoverResponse{
		HandoverID:      handoverData.HandoverID,
		TotalSttSuccess: totalSttNoSuccess,
		TotalSttFailed:  totalSttNoFailed,
		SttFailed:       sttFailed,
	}

	// if all stt failed
	if totalSttNoSuccess == 0 {
		// DELETE HANDOVER
		err := c.handoverRepo.DeleteByID(selfCtx, handoverData.HandoverID)
		if err != nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed delete handover",
				"id": "Gagal menghapus handover",
			})
		}

		handoverData = &model.Handover{}
		res.HandoverID = handoverData.HandoverID
		return res, nil
	}

	// if success
	handoverData.HandoverTotalStt = totalSttNoSuccess
	err = c.handoverRepo.Update(selfCtx, handoverData)
	if err != nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed update handover",
			"id": "Gagal mengubah handover",
		})
	}
	c.createHandoverPublishToAlgo(form, isSttNoSuccess, partnerInfo, now, sttPieceHistoriesPerSTT)

	go func(stts []model.SttDetailResult) {
		sttNos := []string{}
		for i := range stts {
			c.rtcUc.UpdateInactiveRTCBySttId(context.Background(), int(stts[i].SttID))
			sttNos = append(sttNos, stts[i].SttNo)
		}

		c.sttDueRepo.UpdateBulk(context.Background(), &model.STTDueUpdateBulkParams{
			SttNo:           sttNos,
			SttTargetStatus: []string{model.STIDEST, model.STI},
			FieldInsertSttDue: &model.STTDueModel{
				SdIsShow:    false,
				SdUpdatedAt: now,
			},
			UpdateFields:    []string{`sd_target_status`, `sd_is_show`, `sd_updated_at`},
			IsSttNoRequired: true,
		})

	}(sttDetail)

	return res, nil
}

func (c handoverCtx) createHandoverGetPartnerInfo(ctx context.Context, form *handover.HandoverRequest) (partnerInfo *handover.HandoverPartnerInfo, err error) {
	opName := "UsecaseHandover-createHandoverGetPartnerInfo"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res := new(handover.HandoverResponse)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": res, "err": err})
	}()

	partnerInfo, err = c.createHandoverGetHandoverPartner(selfCtx, form)
	if err != nil {
		return nil, err
	}

	if form.DestinationPartnerType == model.PARTNER {
		destinationPartner, err := c.createHandoverGetDestinationPartner(selfCtx, form)
		if err != nil {
			return nil, err
		}

		// assign value destination partner
		partnerInfo.DestinationPartnerID = destinationPartner.Data.ID
		partnerInfo.DestinationPartnerCode = destinationPartner.Data.Code
		partnerInfo.DestinationPartnerName = destinationPartner.Data.Name
		partnerInfo.DestinationExternalPartnerCode = destinationPartner.Data.PartnerExternalCode
		partnerInfo.DestinationCity = destinationPartner.DestinationCity
		partnerInfo.HandoverTo = fmt.Sprintf(`%s [%s]`, partnerInfo.DestinationPartnerName, partnerInfo.DestinationExternalPartnerCode)
	}

	if form.DestinationPartnerCode != model.Luwjistik && partnerInfo.DestinationCity.Code != form.DestinationCityID {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Destination partner city is invalid",
			"id": "Kota tujuan partner tidak valid",
		})
	}

	if !partnerInfo.Data.IsIncomingCoverageConsole(&partnerInfo.DestinationCity, false) {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf("City not in coverage CONSOLE %s", partnerInfo.Data.Code),
			"id": fmt.Sprintf("Kota tidak dalam jangkauan CONSOLE %s", partnerInfo.Data.Code),
		})
	}
	return partnerInfo, err
}

func (c *handoverCtx) createHandoverGetHandoverPartner(ctx context.Context, form *handover.HandoverRequest) (partnerInfo *handover.HandoverPartnerInfo, err error) {
	opName := "UsecaseHandover-createHandoverGetHandoverPartner"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": partnerInfo, "err": err})
	}()

	handoverPartner, err := c.partnerRepo.GetByID(selfCtx, form.PartnerID, form.Token)
	if err != nil || handoverPartner == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid partner account",
			"id": "Akun partner tidak valid",
		})
	}

	if handoverPartner.Data.PartnerLocation == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf("Cons with id %d is not yet mapped with location", form.PartnerID),
			"id": fmt.Sprintf("Cons dengan id %d belum dipetakan dengan lokasi", form.PartnerID),
		})
	}

	if handoverPartner.Data.PartnerLocation.City == nil {
		partnerCity, err := c.cityRepo.Get(selfCtx, handoverPartner.Data.PartnerLocation.CityCode, form.Token)
		if err != nil || partnerCity == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Invalid city partner or console",
				"id": "Kota partner atau console tidak valid",
			})
		}
		handoverPartner.Data.PartnerLocation.City = partnerCity
	}

	mapVendorHandover := model.MappingVendorHandoverTo
	mapVendorHandover[model.TypeVendorInternal] = form.Remarks

	vendorCode := form.DestinationPartnerCode
	handoverTo := mapVendorHandover[vendorCode]
	partnerInfo = &handover.HandoverPartnerInfo{
		Partner:         *handoverPartner,
		DestinationCity: *handoverPartner.Data.PartnerLocation.City,
		VendorCode:      vendorCode,
		HandoverTo:      handoverTo,
	}
	if handoverPartner.Data.PartnerLocation.District != nil {
		partnerInfo.PartnerDistrictName = handoverPartner.Data.PartnerLocation.District.Name
	}
	return partnerInfo, nil
}

func (c *handoverCtx) createHandoverGetDestinationPartner(ctx context.Context, form *handover.HandoverRequest) (partnerInfo *handover.HandoverPartnerInfo, err error) {
	opName := "UsecaseHandover-createHandoverGetDestinationPartner"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": partnerInfo, "err": err})
	}()

	destinationPartner, err := c.partnerRepo.GetByID(selfCtx, form.DestinationPartnerID, form.Token)
	if err != nil || destinationPartner == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid destination partner account",
			"id": "Akun partner tujuan tidak valid",
		})
	}

	if destinationPartner.Data.PartnerLocation == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf("Cons with id %d is not yet mapped with location", form.PartnerID),
			"id": fmt.Sprintf("Cons dengan id %d belum dipetakan dengan lokasi", form.PartnerID),
		})
	}

	destinationCity := destinationPartner.Data.PartnerLocation.City
	if destinationPartner.Data.PartnerLocation.City == nil {
		destinationCity, err = c.cityRepo.Get(selfCtx, form.DestinationCityID, form.Token)
		if err != nil || destinationCity == nil {
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Invalid destination city partner or sub-console",
				"id": "Kota tujuan partner atau sub-console tidak valid",
			})
		}
	}

	partnerInfo = &handover.HandoverPartnerInfo{
		Partner:         *destinationPartner,
		DestinationCity: *destinationCity,
	}
	return partnerInfo, nil
}

func (c *handoverCtx) createHandover(ctx context.Context, form *handover.HandoverRequest, partnerInfo *handover.HandoverPartnerInfo, handoverCreatedAt time.Time) (sttDetail []model.SttDetailResult, handoverData *model.Handover, err error) {
	opName := "UsecaseHandover-createHandover"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": partnerInfo, "err": err})
	}()

	err = c.sttPaymentUc.SttPaymentValidation(selfCtx, form.HandoverSttNo)
	if err != nil {
		return nil, nil, err
	}

	sttDetail, err = c.sttPieceRepo.SelectDetail(selfCtx, &model.SttPiecesViewParam{
		ListSttNo: form.HandoverSttNo,
	})
	if err != nil {
		return nil, nil, shared.ERR_UNEXPECTED_DB
	}
	if len(sttDetail) == 0 {
		return nil, nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Stt number not found",
			"id": "Stt number tidak ditemukan",
		})
	}

	districtByCode := make(map[string]*model.District)
	sttByDistrictVendor := make(map[string]int)
	for _, sttDetailItem := range sttDetail {
		// get information district and city
		detailDistrict, ok := districtByCode[sttDetailItem.SttDestinationDistrictID]
		if !ok {
			detailDistrict, err = c.districtRepo.GetByCode(
				selfCtx,
				&model.CredentialRestAPI{Token: form.Token},
				sttDetailItem.SttDestinationDistrictID,
			)
			if err != nil || detailDistrict == nil {
				return nil, nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"en": "An error occurred while getting Detail District",
					"id": "Terjadi kesalahan pada saat getting Detail District",
				})
			}
			districtByCode[sttDetailItem.SttDestinationDistrictID] = detailDistrict
		}

		// Hanya interpack yang tidak memiliki vendor code yang akan di assign ke luwjistik cc: @Faridz
		vendorCode := detailDistrict.Data.VendorCode
		sttByDistrictVendor[vendorCode] += 1
	}

	switch len(sttByDistrictVendor) {
	case 1:
		// Jika semua STT adalah Non Vendor / 1 Vendor yang sama dan di assign ke vendor lain
		for vendorCode := range sttByDistrictVendor {
			// Untuk Non Vendor akan Error jika input DestinationPartnerCode tidak sesuai (jika tidak sama dengan kosong)
			if (vendorCode == "" && model.IsCreateHandoverValidVendor[form.DestinationPartnerCode]) || (vendorCode != "" && vendorCode != form.DestinationPartnerCode) {
				return nil, nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
					"id": "Sebagian kecamatan pada STT tidak sesuai area vendor. Silakan cek kembali",
					"en": "Some districts in STT do not match the vendor area. Please check again",
				})
			}
			break
		}
	default:
		// Jika STT diassign untuk Vendor Campuran dan ada STT yang tidak sesuai dengan vendor
		// Pada Logic hanya memeriksa apakah user memasukkan DestinationPartnerCode, karena jika Vendor lebih dari satu, ada kemungkinan ada STT yang tidak sesuai dengan vendor
		if len(form.DestinationPartnerCode) > 0 {
			return nil, nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"id": "Sebagian STT harus dihandover ke vendor lain. Silakan cek kembali",
				"en": "Some STT must be handed over to another vendor. Please check again",
			})
		}
	}

	handoverData, err = c.handoverRepo.Create(selfCtx, &model.Handover{
		HandoverPartnerID:              partnerInfo.Data.ID,
		HandoverPartnerCode:            partnerInfo.Data.Code,
		HandoverPartnerName:            partnerInfo.Data.Name,
		HandoverArrivalCityCode:        partnerInfo.DestinationCity.Code,
		HandoverArrivalCityName:        partnerInfo.DestinationCity.Name,
		HandoverDestinationPartnerID:   partnerInfo.DestinationPartnerID,
		HandoverDestinationPartnerCode: partnerInfo.DestinationPartnerCode,
		HandoverDestinationPartnerName: partnerInfo.DestinationPartnerName,
		HandoverVendorCode:             partnerInfo.VendorCode,
		HandoverCreatedBy:              form.AccountID,
		HandoverCreatedName:            form.AccountName,
		HandoverCreatedAt:              handoverCreatedAt,
		HandoverUpdatedBy:              form.AccountID,
		HandoverUpdatedName:            form.AccountName,
		HandoverUpdatedAt:              &handoverCreatedAt,
	})
	if err != nil {
		return nil, nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed create handover",
			"id": "Gagal membuat handover",
		})
	}

	return sttDetail, handoverData, nil
}

func (c *handoverCtx) processCreateHandover(ctx context.Context, req *handover.RequestCreateHandoverProcess, partnerInfo *handover.HandoverPartnerInfo, sttDetail []model.SttDetailResult) (isSttNoSuccess map[string]handover.HandoverDetailCreate, STTFailed []general.STTFailedGeneralResponse, handoverData *model.Handover, sttPieceHistoriesPerSTT map[string][]model.SttPieceHistory) {
	opName := "UsecaseHandover-processCreateHandover"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": partnerInfo})
	}()

	isSttNoSuccess = map[string]handover.HandoverDetailCreate{}
	STTFailed = []general.STTFailedGeneralResponse{}
	mapExistsHistoryStiDest := c.createHandoverCheckHistoryStiDest(selfCtx, sttDetail)
	sttPieceHistoriesPerSTT = make(map[string][]model.SttPieceHistory)
	for _, val := range sttDetail {
		// build request to insert handover detail, and history stt pieces
		// Define variable for update status time
		sttFailed := c.createHandoverValidateStt(selfCtx, val, partnerInfo, req.DestinationPartnerCode)
		if sttFailed != nil {
			STTFailed = append(STTFailed, *sttFailed)
			continue
		}

		sttMeta := c.createHandoverValidateSttMeta(selfCtx, val, req.Token)
		req.MapExistsHistoryStiDest = mapExistsHistoryStiDest
		req.OriginCityName = sttMeta.OriginCityName
		req.OriginDistrictName = sttMeta.OriginDistrictName
		sttPieceHistory, listSttUpdateTime := c.createHandoverGeneratePieceHistory(selfCtx, req, val, partnerInfo)

		err := c.handoverDetailRepo.CreateTx(selfCtx, &model.CreateHandoverDetail{
			HandoverDetail: &model.HandoverDetail{
				HandoverDetailHandoverID:  req.HandoverData.HandoverID,
				HandoverDetailSttID:       int(val.SttID),
				HandoverDetailSttPieceID:  int(val.SttPieceID),
				HandoverDetailProductType: val.SttProductType,
			},
			History: sttPieceHistory,
		})
		if err != nil {
			STTFailed = append(STTFailed, general.STTFailedGeneralResponse{
				SttNo:       val.SttNo,
				SttElexysNo: val.SttElexysNo.Value(),
			})
			continue
		}
		// add here to update handover
		req.HandoverData.HandoverTotalGrossWeight += val.SttPieceGrossWeight
		req.HandoverData.HandoverTotalVolumeWeight += val.SttPieceVolumeWeight
		req.HandoverData.HandoverTotalPiece++
		isSttNoSuccess[val.SttNo] = handover.HandoverDetailCreate{
			Stt:      val.Stt,
			SttPiece: append(isSttNoSuccess[val.SttNo].SttPiece, val.SttPiece),
		}

		go c.sttActivityUc.UpdateSttTime(context.Background(), &stt_activity.SttActivityRequest{
			ListSttData: listSttUpdateTime,
		})

		sttPieceHistoriesPerSTT[val.SttNo] = append(sttPieceHistoriesPerSTT[val.SttNo], sttPieceHistory...)
	}
	return isSttNoSuccess, STTFailed, &req.HandoverData, sttPieceHistoriesPerSTT
}

func (c *handoverCtx) createHandoverValidateStt(ctx context.Context, sttData model.SttDetailResult, partnerInfo *handover.HandoverPartnerInfo, destinationPartnerCode string) (sttFailed *general.STTFailedGeneralResponse) {
	opName := "UsecaseHandover-createHandoverValidateStt"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": destinationPartnerCode, "result": sttFailed})
	}()

	// check last status
	if model.IsNotAllowedStatusHandover[sttData.SttPieceLastStatusID] {
		return &general.STTFailedGeneralResponse{
			SttNo:       sttData.SttNo,
			SttElexysNo: sttData.SttElexysNo.Value(),
		}
	}

	// check if already update to HND before
	if sttData.SttLastStatusID == model.CLAIM {
		history, err := c.sttPieceHistoryRepo.Get(selfCtx, &model.SttPieceHistoryViewParam{
			SttPieceHistorySttPieceID: sttData.SttPieceID,
			SttPieceHistoryStatus:     model.HND,
		})
		if err != nil || history != nil {
			return &general.STTFailedGeneralResponse{
				SttNo:       sttData.SttNo,
				SttElexysNo: sttData.SttElexysNo.Value(),
			}
		}
	}

	if destinationPartnerCode != model.Luwjistik && sttData.SttDestinationCityID != partnerInfo.DestinationCity.Code {
		return &general.STTFailedGeneralResponse{
			SttNo:       sttData.SttNo,
			SttElexysNo: sttData.SttElexysNo.Value(),
		}
	}
	return nil
}

func (c *handoverCtx) createHandoverValidateSttMeta(ctx context.Context, sttData model.SttDetailResult, token string) (sttMeta *model.SttMeta) {
	sttMeta = sttData.SttMetaToStruct()
	if sttMeta == nil {
		sttMeta = &model.SttMeta{}
		// get origin booking city
		sttMeta.OriginCityName = ""
		bookingCity, err := c.cityRepo.Get(ctx, sttData.SttOriginCityID, token)
		if err == nil && bookingCity != nil {
			sttMeta.OriginCityName = bookingCity.Name
		}
	}
	return sttMeta
}

func (c *handoverCtx) createHandoverCheckHistoryStiDest(ctx context.Context, sttDetail []model.SttDetailResult) (mapExistsStiDest map[string]bool) {
	opName := "UsecaseHandover-createHandoverCheckHistoryStiDest"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": mapExistsStiDest})
	}()

	mapExistsStiDest = map[string]bool{}
	listSttNoWithPieceID := map[string][]int64{}
	for _, val := range sttDetail {
		listSttNoWithPieceID[val.Stt.SttNo] = append(listSttNoWithPieceID[val.Stt.SttNo], val.SttPiece.SttPieceID)
	}
	for sttNo, pieceID := range listSttNoWithPieceID {
		histories, err := c.sttPieceHistoryRepo.SelectWithSpecificStatusAndPieceID(ctx, &model.SttPieceHistoryViewParam{
			SttPieceHistorySttPieceIDWhereIn: pieceID,
			SttPieceHistoryStatusWhereIn:     []string{model.STIDEST},
		})
		if err != nil || len(histories) < 1 {
			continue
		}
		isExistsStiDest := false
		for _, history := range histories {
			isExistsStiDest = isExistsStiDest || history.HistoryStatus == model.STIDEST
		}
		mapExistsStiDest[sttNo] = isExistsStiDest
	}
	return mapExistsStiDest
}

func (c *handoverCtx) createHandoverGeneratePieceHistory(ctx context.Context, req *handover.RequestCreateHandoverProcess, sttData model.SttDetailResult, partnerInfo *handover.HandoverPartnerInfo) (sttPieceHistory []model.SttPieceHistory, listSttUpdateTime []stt_activity.SttActivityRequestDetail) {
	opName := "UsecaseHandover-createHandoverGeneratePieceHistory"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": sttPieceHistory})
	}()

	tempRemarksPieceHistory := model.RemarkPieceHistory{
		HistoryLocationName: req.OriginCityName,
		HistoryDistrictName: req.OriginDistrictName,
	}

	pieceHistory := model.SttPieceHistory{
		SttPieceID:         int64(sttData.SttPieceID),
		HistoryLocation:    sttData.SttOriginCityID,
		HistoryActorID:     model.AccountSystem.ActorID,
		HistoryActorName:   model.AccountSystem.ActorName,
		HistoryCreatedAt:   req.HandoverCreatedAt,
		HistoryCreatedBy:   model.AccountSystem.ActorID,
		HistoryCreatedName: model.AccountSystem.ActorName,
		HistoryRemark:      tempRemarksPieceHistory.ToString(),
	}
	sttUpdateTime := stt_activity.SttActivityRequestDetail{
		SttNo:         sttData.SttNo,
		SttStatusTime: req.HandoverCreatedAt,
	}

	// Define variable for update status time
	sttPieceHistory = make([]model.SttPieceHistory, 0)
	listSttUpdateTime = []stt_activity.SttActivityRequestDetail{}
	switch sttData.SttPieceLastStatusID {
	case model.BKD:
		// if last status BKD , add PUP-C to history
		if sttData.Stt.SttBookedByType == model.POS {
			partnerData, err := c.partnerRepo.GetByID(selfCtx, sttData.Stt.SttBookedBy, req.Token)
			isPartnerTypeBranch := err == nil && partnerData != nil && partnerData.Data.PartnerPosType == "branch"
			if isPartnerTypeBranch {
				// if last status PUP, add PUP-C to history
				pieceHistory.HistoryStatus = model.PUPC
				pieceHistory.HistoryActorRole = model.SUBCONSOLE
				sttPieceHistory = append(sttPieceHistory, pieceHistory)
				// Adding time HANDOVER status PUP-C
				sttUpdateTime.SttStatus = model.PUPC
				listSttUpdateTime = append(listSttUpdateTime, sttUpdateTime)
			}
		}
		fallthrough
	case model.PUPC:
		tempRemarksPieceHistory.DriverName = model.AccountSystem.ActorName
		pieceHistory.HistoryStatus = model.PUP
		pieceHistory.HistoryActorRole = model.POS
		pieceHistory.HistoryRemark = tempRemarksPieceHistory.ToString()
		sttPieceHistory = append(sttPieceHistory, pieceHistory)
		// Adding time HANDOVER status PUP
		sttUpdateTime.SttStatus = model.PUP
		listSttUpdateTime = append(listSttUpdateTime, sttUpdateTime)
		tempRemarksPieceHistory.DriverName = ""
		fallthrough
	case model.PUP:
		// if last status PUP, add STI-SC to history
		pieceHistory.HistoryStatus = model.STISC
		pieceHistory.HistoryActorRole = model.SUBCONSOLE
		pieceHistory.HistoryRemark = tempRemarksPieceHistory.ToString()
		sttPieceHistory = append(sttPieceHistory, pieceHistory)
		// Adding time HANDOVER status STISC
		sttUpdateTime.SttStatus = model.STISC
		listSttUpdateTime = append(listSttUpdateTime, sttUpdateTime)
		fallthrough
	case model.STISC:
		// if last status STI-SC, add STI to history
		pieceHistory.HistoryStatus = model.STI
		pieceHistory.HistoryActorRole = model.CONSOLE
		sttPieceHistory = append(sttPieceHistory, pieceHistory)
		// Adding time HANDOVER status STI
		sttUpdateTime.SttStatus = model.STI
		listSttUpdateTime = append(listSttUpdateTime, sttUpdateTime)
	}

	pieceHistoryHND, sttUpdateTimeHND := c.createHandoverGeneratePieceHistoryHND(selfCtx, req, sttData, partnerInfo)
	sttPieceHistory = append(sttPieceHistory, pieceHistoryHND...)
	listSttUpdateTime = append(listSttUpdateTime, sttUpdateTimeHND...)
	return sttPieceHistory, listSttUpdateTime
}

func (c *handoverCtx) createHandoverGeneratePieceHistoryHND(ctx context.Context, req *handover.RequestCreateHandoverProcess, sttData model.SttDetailResult, partnerInfo *handover.HandoverPartnerInfo) (sttPieceHistory []model.SttPieceHistory, listSttUpdateTime []stt_activity.SttActivityRequestDetail) {

	tempRemarksPieceHistory := model.RemarkPieceHistory{
		HistoryLocationName: partnerInfo.Data.PartnerLocation.City.Name,
		HistoryDistrictName: partnerInfo.PartnerDistrictName,
	}

	sttPieceHistory = make([]model.SttPieceHistory, 0)
	listSttUpdateTime = []stt_activity.SttActivityRequestDetail{}

	// dont force to STIDEST if update status to INTHND
	isAllowUpdateSTIDEST := req.DestinationPartnerCode != model.Luwjistik && !model.MapLastStatusNotAllowUpdateStiDest[sttData.SttPieceLastStatusID] && !req.MapExistsHistoryStiDest[sttData.Stt.SttNo]
	if isAllowUpdateSTIDEST {
		// if last status STI / BAGGING / CARGO / TRANSIT / MISROUTE / SHORTLAND, add STI-DEST to history
		sttPieceHistory = append(sttPieceHistory, model.SttPieceHistory{
			SttPieceID:         int64(sttData.SttPieceID),
			HistoryStatus:      model.STIDEST,
			HistoryLocation:    partnerInfo.Data.PartnerLocation.CityCode,
			HistoryActorID:     model.AccountSystem.ActorID,
			HistoryActorName:   model.AccountSystem.ActorName,
			HistoryActorRole:   model.CONSOLE,
			HistoryCreatedAt:   req.HandoverCreatedAt,
			HistoryCreatedBy:   model.AccountSystem.ActorID,
			HistoryCreatedName: model.AccountSystem.ActorName,
			HistoryRemark:      tempRemarksPieceHistory.ToString(),
		})

		// Adding time HANDOVER status STIDEST
		listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
			SttNo:         sttData.SttNo,
			SttStatus:     model.STIDEST,
			SttStatusTime: req.HandoverCreatedAt,
		})
	}

	statusCode := model.HND
	if sttData.SttProductType == model.INTERPACK && req.DestinationPartnerCode == model.Luwjistik {
		statusCode = model.INTHND
	}
	tempRemarksPieceHistory.HandoverTo = partnerInfo.HandoverTo
	SetHub(c.cityRepo, c.districtRepo, &stt_piece_history_remark_helper.SetHubParams{
		Ctx:                 ctx,
		Token:               req.Token,
		HubID:               req.HubID,
		HubName:             req.HubName,
		HubOriginCity:       req.HubOriginCity,
		HubDistrictCode:     req.HubDistrictCode,
		RemarksPieceHistory: &tempRemarksPieceHistory,
	})

	sttPieceHistory = append(sttPieceHistory, model.SttPieceHistory{
		SttPieceID:         int64(sttData.SttPieceID),
		HistoryStatus:      statusCode,
		HistoryLocation:    req.HandoverData.HandoverArrivalCityCode,
		HistoryActorID:     req.HandoverData.HandoverPartnerID,
		HistoryActorName:   req.HandoverData.HandoverPartnerName,
		HistoryActorRole:   req.PartnerType,
		HistoryCreatedAt:   req.HandoverCreatedAt,
		HistoryCreatedBy:   req.AccountID,
		HistoryCreatedName: req.AccountName,
		HistoryRemark:      tempRemarksPieceHistory.ToString(),
	})

	// Adding time HANDOVER status HND
	listSttUpdateTime = append(listSttUpdateTime, stt_activity.SttActivityRequestDetail{
		SttNo:         sttData.SttNo,
		SttStatus:     statusCode,
		SttStatusTime: req.HandoverCreatedAt,
	})

	return sttPieceHistory, listSttUpdateTime
}

func (c *handoverCtx) createHandoverPublishToAlgo(form *handover.HandoverRequest, isSttNoSuccess map[string]handover.HandoverDetailCreate, partnerInfo *handover.HandoverPartnerInfo, handoverCreatedAt time.Time, sttPieceHistoriesPerSTT map[string][]model.SttPieceHistory) {

	credential := &model.CredentialRestAPI{
		Token:    form.Token,
		ClientID: form.AccountID,
	}

	go lputils.TrackGoroutine(func(goCtx context.Context) {
		// Publish to ALGO
		for _, sttData := range isSttNoSuccess {

			c.createHandoverReleaseBookingCommission(goCtx, sttData.Stt)
			statusCode := model.HND
			if sttData.Stt.SttProductType == model.INTERPACK && form.DestinationPartnerCode == model.Luwjistik {
				statusCode = model.INTHND
				_ = c.luwjistikUc.BookingFromSTT(goCtx, sttData, credential)
			}
			statusSubmitParams := &model.UpdateSttStatusWithExtendForMiddleware{
				UpdateSttStatus: &model.UpdateSttStatus{
					SttNo:      sttData.Stt.GetValidSttElexysNo(),
					Datetime:   handoverCreatedAt.UTC(),
					StatusCode: statusCode,
					Location:   partnerInfo.Data.PartnerLocation.CityCode,
					Remarks:    fmt.Sprintf(`Paket dihandover oleh %s`, partnerInfo.Data.Name),
					City:       partnerInfo.Data.PartnerLocation.City.Name,
					UpdatedBy:  partnerInfo.Data.Name,
					UpdatedOn:  handoverCreatedAt.UTC(),
				},
				ServiceType:      model.PACKAGESERVICE,
				Product:          sttData.Stt.SttProductType,
				Pieces:           sttData.Stt.SttTotalPiece,
				GrossWeight:      sttData.Stt.SttGrossWeight,
				VolumeWeight:     sttData.Stt.SttVolumeWeight,
				ChargeableWeight: sttData.Stt.SttChargeableWeight,
				BookedForType:    sttData.Stt.SttBookedForType,
			}

			AppendLastAndSystemStatus(AppendLastAndSystemStatusParams{
				StatusSubmitParams: statusSubmitParams,
				SttPieceHistories:  sttPieceHistoriesPerSTT[sttData.Stt.SttNo],
				PartnerName:        partnerInfo.Data.Name,
			})

			c.gatewaySttStatusUc.StatusSubmit(goCtx, statusSubmitParams)

			c.processVendorOrder(goCtx, processVendorOrderParams{
				Form:        form,
				Stt:         sttData.Stt,
				PartnerInfo: partnerInfo,
				CreatedAt:   handoverCreatedAt,
			})
		}
	}, c.cfg.TimeoutVendorInMinutes(15)*60) // default timeout 15 minutes
}

type processVendorOrderParams struct {
	Form        *handover.HandoverRequest
	Stt         model.Stt
	PartnerInfo *handover.HandoverPartnerInfo
	CreatedAt   time.Time
}

func (c *processVendorOrderParams) Validate() error {
	if c.PartnerInfo == nil {
		return fmt.Errorf("[handoverCtx-processVendorOrder] stt_no %s is partner info is nil", c.Stt.SttNo)

	}
	if c.Form == nil || c.Form.Token == "" {
		return fmt.Errorf("[handoverCtx-processVendorOrder] stt_no %s is token is empty", c.Stt.SttNo)
	}
	return nil
}
func (c *handoverCtx) processVendorOrder(goCtx context.Context, params processVendorOrderParams) {
	if err := params.Validate(); err != nil {
		logger.E(err)
		return
	}
	switch {
	case c.cfg.NinjaBooking() && params.PartnerInfo.VendorCode == model.TypeVendorNINJA:
		c.ninjaUc.CreateOrderNinja(goCtx, &model.CreateOrderRequest{
			Stt:         params.Stt,
			Partner:     &params.PartnerInfo.Partner,
			StiDestTime: params.CreatedAt,
			Token:       params.Form.Token,
		})
	case c.cfg.JNEBooking() && params.PartnerInfo.VendorCode == model.TypeVendorJNE:
		_, _ = c.jneUc.JneCreateAirwaybill(goCtx, &model.JneCreateAirwaybillRequest{
			Stt:     params.Stt,
			Partner: &params.PartnerInfo.Partner,
			Token:   params.Form.Token,
		})
	case c.cfg.PtPosBooking() && params.PartnerInfo.VendorCode == model.TypeVendorPTPOS:
		_ = c.ptPosUc.CreateOrderPtPos(goCtx, &model.CreateOrderPtPosParams{
			Stt:     params.Stt,
			Partner: &params.PartnerInfo.Partner,
			Token:   params.Form.Token,
		})
	default:
		logger.Ef("[handoverCtx-processVendorOrder] stt_no %s is Invalid vendor code %s", params.Stt.SttNo, params.PartnerInfo.VendorCode)
	}
}

func (c *handoverCtx) createHandoverReleaseBookingCommission(goCtx context.Context, sttData model.Stt) {
	sttReqBookComm := sttData
	sttMeta := sttData.SttMetaToStruct()
	isReverseJourney := sttMeta != nil && sttMeta.DetailSttReverseJourney != nil && sttMeta.DetailSttReverseJourney.RootReverseSttNo != ``
	if isReverseJourney {
		sttData, _ := c.sttRepo.Get(goCtx, &model.SttViewDetailParams{
			Stt: model.Stt{
				SttNo: sttMeta.DetailSttReverseJourney.RootReverseSttNo,
			},
		})

		if sttData != nil && sttData.SttID > 0 {
			sttReqBookComm = *sttData
		}
	}

	if shared.CheckPrefixSttNoForReleaseBookingCommission(sttReqBookComm) {
		if err := c.gatewaySttUc.ReleaseBookingCommission(goCtx, sttReqBookComm); err != nil {
			logger.Ef(`ReleaseBookingCommission Error %s`, err.Error())
		}
	}
}
