package cbp_pickup

import (
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
)

type PickupManifestCBPParams struct {
	Cache       bool   `json:"cache"`
	Page        int    `json:"page"`
	Limit       int    `json:"limit"`
	IsTotalData bool   `json:"is_total_data"`
	IsCod       bool   `json:"is_cod"`
	PmcStatus   string `json:"pmc_status"`
	PmcClientId int    `json:"pmc_client_id"`
	IsLimit     bool
	Token       string
	PartnerID   int
	Field       string

	DbConnection string
}

func (p *PickupManifestCBPParams) Validate() error {
	if p.Page <= 0 {
		p.Page = 1
	}

	if p.Limit <= 0 {
		p.Limit = 10
	}

	return nil
}

func (p *PickupManifestCBPParams) ValidateV2() (err error) {
	if p.Page <= 0 {
		p.Page = 1
	}

	if p.Limit <= 0 {
		p.Limit = 10
	}

	if p.PartnerID <= 0 {
		err = shared.NewMultiStringValidationError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid Partner ID",
			"id": "Partner ID tidak valid",
		})
		return err
	}
	return nil
}

type ResponsePickupManifestCBP struct {
	SttID                      int        `json:"stt_id"`
	SttNo                      string     `json:"stt_no"`
	SttShipmentID              string     `json:"stt_shipment_id"`
	SttRecipientAddress        string     `json:"stt_recipient_address"`
	SttCommodityCode           string     `json:"stt_commodity_code"`
	SttCommodityName           string     `json:"stt_commodity_name"`
	SttClientID                int        `json:"stt_client_id"`
	SttPosID                   int        `json:"stt_pos_id"`
	SttIsCod                   bool       `json:"stt_is_cod"`
	SttIsDfod                  bool       `json:"stt_is_dfod"`
	SttProductType             string     `json:"stt_product_type"`
	SttChargeableWeight        float64    `json:"stt_chargeable_weight"`
	SttTotalPiece              int        `json:"stt_total_piece"`
	SttLastStatusID            string     `json:"stt_last_status_id"`
	SttCounter                 int        `json:"stt_counter"`
	SttInsuranceType           string     `json:"stt_insurance_type"`
	SttOriginCity              string     `json:"stt_origin_city"`
	SttDestinationCity         string     `json:"stt_destination_city"`
	SttTaxNumber               string     `json:"stt_tax_number"`
	SttHsCode                  string     `json:"stt_hs_code"`
	SttBookedName              string     `json:"stt_booked_name"`
	SttBilledTo                string     `json:"stt_billed_to"`
	SttCreatedName             string     `json:"stt_created_name"`
	SttCreatedAt               time.Time  `json:"stt_created_at"`
	SttUpdatedAt               time.Time  `json:"stt_updated_at"`
	SttPaymentStatus           string     `json:"stt_payment_status"`
	SttPaymentDateAt           *time.Time `json:"stt_payment_date_at"`
	SttUpdatedByName           string     `json:"stt_updated_by_name"`
	SttDestinationCityName     string     `json:"stt_destination_city_name"`
	SttOriginCityName          string     `json:"stt_origin_city_name"`
	SttDestinationDistrictName string     `json:"stt_destination_district_name"`
	SttOriginDistrictName      string     `json:"stt_origin_district_name"`
	PmcStatus                  string     `json:"pmc_status"`
}

func MappingDataResponsePickupManifestCBP(data []model.SttPickupCBP) (resData []ResponsePickupManifestCBP) {
	for i := 0; i < len(data); i++ {
		resData = append(resData, ResponsePickupManifestCBP{
			SttID:                      int(data[i].Stt.SttID),
			SttNo:                      data[i].Stt.SttNo,
			SttShipmentID:              data[i].Stt.SttShipmentID,
			SttRecipientAddress:        data[i].Stt.SttRecipientAddress,
			SttCommodityCode:           data[i].Stt.SttCommodityCode,
			SttCommodityName:           data[i].Stt.SttCommodityName,
			SttClientID:                data[i].Stt.SttClientID,
			SttPosID:                   data[i].Stt.SttPosID,
			SttIsCod:                   data[i].Stt.SttIsCOD,
			SttIsDfod:                  data[i].Stt.SttIsDFOD,
			SttProductType:             data[i].Stt.SttProductType,
			SttChargeableWeight:        data[i].Stt.SttChargeableWeight,
			SttTotalPiece:              data[i].Stt.SttTotalPiece,
			SttLastStatusID:            data[i].Stt.SttLastStatusID,
			SttCounter:                 data[i].Stt.SttCounter,
			SttInsuranceType:           data[i].Stt.SttInsuranceType,
			SttOriginCity:              data[i].Stt.SttOriginCityID,
			SttDestinationCity:         data[i].Stt.SttDestinationCityID,
			SttTaxNumber:               data[i].Stt.SttTaxNumber,
			SttHsCode:                  data[i].Stt.SttCommodityHsCode,
			SttBookedName:              data[i].Stt.SttBookedName,
			SttBilledTo:                data[i].Stt.SttBookedForCode + " - " + data[i].Stt.SttBookedForName,
			SttCreatedName:             data[i].Stt.SttCreatedName,
			SttCreatedAt:               data[i].Stt.SttCreatedAt,
			SttUpdatedAt:               data[i].Stt.SttUpdatedAt,
			SttPaymentStatus:           data[i].Stt.SttPaymentStatus,
			SttPaymentDateAt:           &data[i].Stt.SttPaymentDateAt.Time,
			SttUpdatedByName:           data[i].Stt.SttUpdatedName,
			SttDestinationCityName:     data[i].Stt.SttDestinationCityName,
			SttOriginCityName:          data[i].Stt.SttOriginCityName,
			SttDestinationDistrictName: data[i].Stt.SttDestinationDistrictName,
			SttOriginDistrictName:      data[i].Stt.SttOriginDistrictName,
			PmcStatus:                  data[i].PickupManifestCBP.PmcStatus,
		})
	}
	return resData
}
