package usecase

import "github.com/Lionparcel/hydra/src/model"

func setDataClientTariffBeforeDisc(data model.CheckTariffBase) model.ClientTariffDetail {
	return model.ClientTariffDetail{
		CityRates:               data.CityRates,
		ForwardRates:            data.ForwardRates,
		ShippingCost:            data.ShippingCost,
		CommoditySurcharge:      data.CommoditySurcharge,
		HeavyWeightSurcharge:    data.HeavyWeightSurcharge,
		DocumentSurcharge:       data.DocumentSurcharge,
		InsuranceRates:          data.InsuranceRates,
		WoodpackingRates:        data.WoodpackingRates,
		TotalTariff:             data.TotalTariff,
		TaxRates:                data.TaxRates,
		BMTaxRate:               data.BMTaxRate,
		PPNTaxRate:              data.PPNTaxRate,
		PPHTaxRate:              data.PPHTaxRate,
		OriginDistrictRate:      data.OriginDistrictRate,
		DestinationDistrictRate: data.DestinationDistrictRate,
		PublishRate:             data.PublishRate,
		ShippingSurchargeRate:   data.ShippingSurchargeRate,
		CodAmount:               data.CodAmount,
		CodFee:                  data.CodFee,
	}
}
