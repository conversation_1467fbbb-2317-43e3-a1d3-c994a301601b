package usecase

import (
	"context"

	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

func (c *sttCtx) configSttConsole(ctx context.Context, data checkSttPrefix) *stt.SttAdjustmentConfigResponse {
	if !c.isSttValidType(data) {
		return &stt.SttAdjustmentConfigResponse{}
	}

	isStatusSti := data.dataStt.SttLastStatusID == model.STI
	rules := c.getSttFieldRulesConsole(data, isStatusSti)

	res := &stt.SttAdjustmentConfigResponse{
		Config: c.buildSttConfigConsole(rules, isStatusSti),
	}
	
	c.checkInterpackDocumentInternational(ctx, data, res)
	c.populateRespInterpackAndFtzPos(res, data.cityOrigin.FreeTradeZone)
	return res
}

func (c *sttCtx) getSttFieldRulesConsole(data checkSttPrefix, isStatusSti bool) sttFieldRules {
	defaultRule := sttFieldRules{
		TariffRule:  stt.ShowConfig,
		WeightRule:  stt.EditableConfig,
		AdjustPiece: stt.NonEditableConfig,
	}

	switch {
	case data.isSttRetail:
		return defaultRule
	case data.isSttClient:
		return sttFieldRules{TariffRule: stt.NoShowConfig, WeightRule: stt.EditableConfig, AdjustPiece: stt.NonEditableConfig}
	case data.isSttCA:
		return sttFieldRules{TariffRule: stt.ShowConfig, WeightRule: stt.EditableConfig, AdjustPiece: stt.NonEditableConfig}
	case data.isSttTokopedia:
		return sttFieldRules{TariffRule: stt.NoShowConfig, WeightRule: stt.EditableConfig, AdjustPiece: stt.NonEditableConfig}
	case data.isSttBukalapak:
		return sttFieldRules{TariffRule: stt.NoShowConfig, WeightRule: stt.NonEditableConfig, AdjustPiece: c.adjustShowRule(isStatusSti, stt.NoShowConfig)}
	default:
		return defaultRule
	}
}

func (c *sttCtx) buildSttConfigConsole(r sttFieldRules, isStatusSti bool) stt.Config {
	return stt.Config{
		Client:              newFieldRule(true, stt.NonEditableConfig),
		PaymentMethod:       newFieldRule(true, stt.NonEditableConfig),
		ShipmentID:          newFieldRule(true, stt.NonEditableConfig),
		ManualSttNo:         newFieldRule(false, stt.NonEditableConfig),
		SttNoRefExternal:    newFieldRule(false, stt.NonEditableConfig),
		SenderName:          newFieldRule(true, stt.NonEditableConfig),
		SenderPhone:         newFieldRule(true, stt.NonEditableConfig),
		SenderAddress:       newFieldRule(true, stt.NonEditableConfig),
		SenderDistrict:      newFieldRule(true, stt.NonEditableConfig),
		SenderPostalCode:    newFieldRule(false, stt.NonEditableConfig),
		SenderSave:          newFieldRule(false, stt.NonEditableConfig),
		RecipientName:       newFieldRule(true, stt.NonEditableConfig),
		RecipientPhone:      newFieldRule(true, stt.NonEditableConfig),
		RecipientAddress:    newFieldRule(true, stt.NonEditableConfig),
		RecipientDistrict:   newFieldRule(true, stt.NonEditableConfig),
		RecipientPostalCode: newFieldRule(false, stt.NonEditableConfig),
		RecipientSave:       newFieldRule(false, stt.NonEditableConfig),
		Commodity:           newFieldRule(true, stt.NonEditableConfig),
		ProductType:         newFieldRule(true, stt.NonEditableConfig),
		GoodsPrice:          newFieldRule(true, stt.NonEditableConfig),
		Insurance:           newFieldRule(false, stt.NonEditableConfig),
		CODAmount:           newFieldRule(false, stt.NonEditableConfig),
		GrossWeight:         newFieldRule(true, c.adjustEditableRule(!isStatusSti, r.WeightRule)),
		Dimension:           newFieldRule(true, c.adjustEditableRule(!isStatusSti, r.WeightRule)),
		ChargeableWeight:    newFieldRule(false, stt.NonEditableConfig),
		AdjustPiece:         newFieldRule(false, r.AdjustPiece),
		ServiceType:         newFieldRule(true, stt.NonEditableConfig),
		Tariff:              newFieldRule(true, c.adjustShowRule(!isStatusSti, r.TariffRule)),
	}
}
