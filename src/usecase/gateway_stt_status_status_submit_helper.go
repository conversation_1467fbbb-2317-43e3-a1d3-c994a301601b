package usecase

import (
	"context"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
)

func (c *gatewaySttStatusCtx) isShipmentTokopedia(ctx context.Context, sttNo string) bool {
	sttDetail, err := c.sttRepo.Get(ctx, &model.SttViewDetailParams{
		Stt: model.Stt{
			SttNo: sttNo,
		},
	})
	if err != nil {
		return false
	}

	sttMeta := sttDetail.SttMetaToStruct()
	if isPrefixReverseJourney(sttDetail.SttNo) && sttMeta != nil {
		reverseJourneyDetail := sttMeta.DetailSttReverseJourney

		isSttShipmentReverseTokopedia := reverseJourneyDetail.RootReverseShipmentID != `` && model.IsShipmentTokopedia[shared.GetPrefixShipmentID(reverseJourneyDetail.RootReverseShipmentID)]
		isSttNoRefReverseTokopedia := reverseJourneyDetail.RootReverseSttNoRefExternal != `` && shared.IsShipmentTKP(reverseJourneyDetail.RootReverseSttNoRefExternal)
		if isSttShipmentReverseTokopedia || isSttNoRefReverseTokopedia {
			return true
		}
	}

	isSttShipmentTokopedia := sttDetail != nil && sttDetail.SttShipmentID != `` && model.IsShipmentTokopedia[shared.GetPrefixShipmentID(sttDetail.SttShipmentID)]
	isSttNoRefTokopedia := sttDetail.SttNoRefExternal != `` && shared.IsShipmentTKP(sttDetail.SttNoRefExternal)
	if isSttShipmentTokopedia || isSttNoRefTokopedia {
		return true
	}

	return false
}

func (c *gatewaySttStatusCtx) getReasonCode(ctx context.Context, sttPieceHistory []model.SttPieceHistoryCustom, params *model.UpdateSttStatusWithExtendForMiddleware) {
	if !c.isShipmentTokopedia(ctx, sttPieceHistory[0].SttNo) {
		return
	}
	switch params.UpdateSttStatus.StatusCode {
	case model.DEX, model.CODREJ:
		params.ReasonCode = sttPieceHistory[0].HistoryReason
		params.UpdateSttStatus.ReasonCode = sttPieceHistory[0].HistoryReason
	case model.RTS:
		sttPieceHistoryStatusDex, _ := c.sttPieceHistoryRepo.SelectBySttNo(ctx, &model.SttPieceHistoryViewParam{
			SttNoIn:                      []string{params.UpdateSttStatus.SttNo},
			SttPieceHistoryStatusWhereIn: []string{model.DEX, model.CODREJ},
			SortBy:                       `sph.history_created_at`,
			OrderDesc:                    true,
			Limit:                        1,
		})
		if len(sttPieceHistoryStatusDex) == 0 {
			return
		}
		params.ReasonCode = sttPieceHistoryStatusDex[0].HistoryReason
		params.UpdateSttStatus.ReasonCode = sttPieceHistoryStatusDex[0].HistoryReason
	}
}

func (c *gatewaySttStatusCtx) generateDataFromSttPieceHistoryReasonDEXCODREJ(ctx context.Context, params *model.UpdateSttStatusWithExtendForMiddleware, sttPieceHistory []model.SttPieceHistoryCustom) {
	allowedStatusToReturnReason := map[string]bool{
		model.DEX:    true,
		model.CODREJ: true,
	}
	if !allowedStatusToReturnReason[params.UpdateSttStatus.StatusCode] {
		return
	}

	reason, err := c.reasonRepo.GetDetail(ctx, &model.ReasonViewParams{
		StatusCode: params.UpdateSttStatus.StatusCode,
		ReasonCode: sttPieceHistory[0].HistoryReason,
	})
	if reason == nil || err != nil {
		return
	}

	reasonCode := reason.ReasonCode
	reasonTitle := reason.ReasonTitle

	if params.UpdateSttStatus.StatusCode == model.DEX {
		extReasonData, _ := c.reasonRepo.SelectReasonExternalDetail(ctx, []string{reasonCode})
		if len(extReasonData) > 0 && extReasonData[0].ReasonTitle != `` {
			reasonCode = extReasonData[0].ReasonCode
			reasonTitle = extReasonData[0].ReasonTitle
			params.UpdateSttStatus.Remarks = reasonTitle
		}
	}

	params.UpdateSttStatus.ReasonCode = reasonCode
	params.UpdateSttStatus.ReasonTitle = reasonTitle
	params.UpdateSttStatus.Reason = reasonTitle
	params.Reason = reasonTitle
	params.ReasonCode = reasonCode
}
