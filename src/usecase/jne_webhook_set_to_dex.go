package usecase

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/Lionparcel/go-lptool/lputils"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/jne"
	"github.com/Lionparcel/hydra/src/usecase/pod"
)

// setToDex
func (c *jneCtx) setToDex(ctx context.Context, params *jne.JneWebhookRequest, sttDetail []model.SttDetailResult) (err error) {
	opName := "UsecaseJNE-setToDex"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	lastStatusJNE := model.DEX

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]any{"param": params, "result": err})

		go lputils.TrackGoroutine(func(goCtx context.Context) {
			c.partnerLog.Insert(goCtx, &model.PartnerLog{
				Action:   model.PLSttJNEDex,
				RefID:    params.OrderID,
				Request:  params,
				Response: shared.CheckErrorOrSuccess(err),
			})
		})
	}()
	errQueryDB := errors.New("An error occurred while querying db")

	sttHistory, err := GetSttStatusHistoryBeforeAdjusment(selfCtx, c.sttHistoryRepo, sttDetail[0].SttNo, []string{model.INHUB, model.OUTHUB})
	if err != nil {
		return err
	}

	sttDetail[0].SttLastStatusID = sttHistory.HistoryStatus

	// Stt Detail
	sttRow := sttDetail[0]

	// get token genesis
	accountToken, errAccount := c.repoAccount.GetTokenGenesis(selfCtx)
	if errAccount != nil || accountToken == nil {
		err = errors.New("Failed to get token genesis")
		return err
	}

	// get detail delivery
	defaultDeliveryID := 0
	delivery, errDelivery := c.deliveryRepo.GetDetail(selfCtx, &model.DeliveryViewParam{
		SttNo:                   sttRow.SttNo,
		OrderBy:                 `delivery_id`,
		SortBy:                  model.SortByDesc,
		FinishedStatusWhereNull: true,
		PartnerType:             model.AccountJNE.ActorName,
	})
	if errDelivery == nil && delivery != nil {
		defaultDeliveryID = delivery.DeliveryID
	}

	errSttCannotUpdated := errors.New("STT cannot be updated")

	if defaultDeliveryID > 0 && model.IsNotAllowUpdateToDEX[delivery.Stt.SttLastStatusID] {
		return errSttCannotUpdated
	}

	if delivery == nil {
		stt, err := c.getSttAtSlaveAndMaster(selfCtx, sttRow.SttNo)
		if stt == nil || err != nil {
			return errQueryDB
		}

		if model.IsNotAllowUpdateToDEX[stt.SttLastStatusID] {
			return errSttCannotUpdated
		}
	}

	// get detail destination stt
	cityDestination, errCity := c.repoCity.Get(selfCtx, sttRow.Stt.SttDestinationCityID, accountToken.Data.Token)
	if errCity != nil {
		err = errQueryDB
		return
	}
	if cityDestination == nil {
		err = errors.New("City destination is not found")
		return err
	}

	attachmentSigned := shared.ReUploadSignedURLFromAWS(selfCtx, []string{params.Photo}, time.Second*time.Duration(c.cfg.ExpiredDurationProofUrl()), c.cfg.IsReUploadVendorProof())

	remarks := model.DeliveryRemarks{
		CreatedBy:        model.AccountJNE.ActorName,
		RecieverName:     params.ReceiverName,
		Attachment:       params.Photo,
		AttachmentSigned: strings.Join(attachmentSigned, ","),
	}

	now, _ := shared.ParseUTC7(shared.FormatDateTime, c.timeRepo.Now(time.Now()).Format(shared.FormatDateTime))
	createDex := pod.CreatePodDexParams{
		Status:     lastStatusJNE,
		SttID:      sttRow.SttID,
		ActorID:    model.AccountJNE.ActorID,
		ActorName:  model.AccountJNE.ActorName,
		ActorRole:  model.VENDOR,
		Remarks:    remarks.ToString(),
		DeliveryID: defaultDeliveryID,
		UpdatedAt:  now,
	}

	tempRemarksPieceHistory := model.RemarkPieceHistory{
		HistoryLocationName: cityDestination.Name,
		ReceiverName:        remarks.RecieverName,
		Attactments:         []string{params.Photo},
		AttachmentSigneds:   attachmentSigned,
	}

	reasonDescription := ""
	// get reason detail

	reasonVendorMapping, err := c.reasonVendorMapping.GetDetail(selfCtx, &model.ReasonVendorMappingViewParams{
		RvmVendor: model.JNE,
		RvmStatus: params.History[len(params.History)-1].StatusCode,
	})

	if err != nil {
		err = shared.ErrUnexpected
		return
	}

	reasonTitleWebhook := ``
	reasonTitle := model.JNEDefaultDEXReason
	if reasonVendorMapping != nil && params.History[len(params.History)-1].StatusCode != "" {
		reasonTitle = reasonVendorMapping.RvmReasonTitle
	}

	reason, errReason := c.reasonRepo.GetDetail(selfCtx, &model.ReasonViewParams{
		ReasonTitle: reasonTitle,
	})
	if errReason != nil || reason == nil {
		err = errQueryDB
		return
	}
	createDex.ReasonCode = reason.ReasonCode
	reasonDescription = reason.ReasonDescription
	reasonTitleWebhook = reason.ReasonTitle

	listSttNo := []string{}
	for _, sttPiece := range sttDetail {
		listSttNo = append(listSttNo, sttPiece.SttNo)
		createDex.Histories = append(createDex.Histories, model.SttPieceHistory{
			SttPieceID:          sttPiece.SttPieceID,
			HistoryStatus:       createDex.Status,
			HistoryLocation:     sttRow.SttDestinationCityID,
			HistoryActorID:      model.AccountJNE.ActorID,
			HistoryActorName:    model.AccountJNE.ActorName,
			HistoryActorRole:    model.VENDOR,
			HistoryReason:       createDex.ReasonCode,
			HistoryRecieverName: remarks.RecieverName,
			HistoryAttactment:   params.Photo,
			HistoryCreatedBy:    int(model.AccountJNE.ActorID),
			HistoryCreatedAt:    now,
			HistoryCreatedName:  model.AccountJNE.ActorName,
			HistoryRemark:       tempRemarksPieceHistory.ToString(),
		})
	}

	if errPod := c.podRepo.Create(selfCtx, &createDex); errPod != nil {
		err = shared.ErrUnexpected
		return
	}

	//  submit stt status to algo
	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.gatewaySttStatus.StatusSubmit(goCtx, &model.UpdateSttStatusWithExtendForMiddleware{
			UpdateSttStatus: &model.UpdateSttStatus{
				SttNo:      sttRow.SttNo,
				Datetime:   now.UTC(),
				StatusCode: createDex.Status,
				Location:   sttRow.SttDestinationCityID,
				Remarks: func() string {
					switch createDex.Status {
					case model.POD:
						return fmt.Sprintf(`Paket diterima oleh %s`, remarks.RecieverName)
					default:
						return `Paket gagal dikirim`
					}
				}(),
				City:             cityDestination.Name,
				UpdatedBy:        model.JNEName, // handle if history created_name already added
				UpdatedOn:        now.UTC(),
				UrlPictureSigned: attachmentSigned,
				Reason:           reasonTitleWebhook,
				ReasonCode:       createDex.ReasonCode,
				Attachments:      []string{params.Photo},
			},
			ServiceType:      model.PACKAGESERVICE,
			Product:          sttRow.SttProductType,
			Pieces:           sttRow.SttTotalPiece,
			GrossWeight:      sttRow.SttGrossWeight,
			VolumeWeight:     sttRow.SttVolumeWeight,
			ChargeableWeight: sttRow.SttChargeableWeight,
			BookedForType:    sttRow.SttBookedForType,
			UrlPicture:       params.Photo,
			Reason:           reasonTitleWebhook,
			ReasonCode:       createDex.ReasonCode,
		})
	})

	// publish to pubsub dtpol commission
	go lputils.TrackGoroutine(func(goCtx context.Context) {
		if !model.IsAllowToPubsubDTPOLCommission[createDex.Status] {
			return
		}

		if cityDestination == nil {
			return
		}

		localTime, err := shared.ParseTimeWithLocation(
			createDex.UpdatedAt.Format(time.RFC3339),
			time.RFC3339,
			shared.MappingTimzoneToTimeLocation[cityDestination.Timezone],
		)
		if err != nil {
			return
		}

		c.gatewaySttStatus.GoberDTPOLCommission(goCtx, &model.GoberDTPOLCommission{
			SttNo:              params.OrderID,
			UpdatedAtLocalTime: localTime,
			UpdatedAtWIB:       createDex.UpdatedAt,
			SttLastStatus:      createDex.Status,
		})
	})

	// messaging service
	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.messageGatewayUc.SendMessage(goCtx, &model.SendMessageRequest{
			RecieverNumber: model.RecieverNumber{
				PackageSender:   sttRow.SttSenderPhone,
				PackageReceiver: sttRow.SttRecipientPhone,
			},
			PackageType:       shared.GetPackageType(sttRow.SttIsCOD, sttRow.SttIsDFOD),
			EventStatus:       model.DEX,
			DriverName:        sttRow.SttSenderName,
			DriverPhoneNumber: sttRow.SttSenderPhone,
			UndeliveredReason: reasonDescription, ReasonCode: reason.ReasonCode,
			Token:      accountToken.Data.Token,
			Attachment: remarks.Attachment,
			IdDelivery: delivery.DeliveryID,
		}, &sttRow.Stt)
	})

	c.publishDexAssessment(publishDexAssessmentRequest{
		SttDetail:        sttRow,
		CreatePodDexData: createDex,
		Token:            accountToken.Data.Token,
		Attachment:       remarks.Attachment,
	})

	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.salesforce.SendPubsub(goCtx, model.PubsubCreateRTSParams{
			SttNo: sttRow.Stt.SttNo,
		})
	})

	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.rtcUc.UpdateInactiveRTCBySttId(goCtx, int(sttRow.SttID))
	})

	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.requestPriorityDelivery.UpdateIsShowToZero(goCtx, sttRow.SttNo)
	})

	return nil
}

func (c *jneCtx) getDaDeliveryDexAttempt(ctx context.Context, sttNo string) int {
	attempt, err := c.deliveryRepo.CountSttDex(ctx, &model.ViewDashboardDeliveryParams{
		SttNo: sttNo,
	})
	if err != nil {
		return 0
	}
	attempt++
	return attempt
}

type publishDexAssessmentRequest struct {
	SttDetail        model.SttDetailResult
	CreatePodDexData pod.CreatePodDexParams
	Token            string
	Attachment       string
}

func (c *jneCtx) publishDexAssessment(req publishDexAssessmentRequest) {
	go lputils.TrackGoroutine(func(goCtx context.Context) {
		daDeliveryDexAttempt := c.getDaDeliveryDexAttempt(goCtx, req.SttDetail.SttNo)
		c.gatewaySttStatus.PublishCreateDexAssessment(goCtx, &model.PublishCreateDexAssessment{
			Stt:                  req.SttDetail.Stt,
			DeliveryID:           req.CreatePodDexData.DeliveryID,
			Token:                req.Token,
			DaSourceDex:          model.DaSourceDexDriverApp,
			ReasonCode:           req.CreatePodDexData.ReasonCode,
			DaDeliveryDexAttempt: daDeliveryDexAttempt,
			DaDeliveryDexDate:    req.CreatePodDexData.UpdatedAt,
			Attachment:           req.Attachment,
			VendorName:           model.JNEName,
		})
	})
}
