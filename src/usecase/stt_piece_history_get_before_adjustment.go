package usecase

import (
	"context"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/repository"
)

func GetSttStatusHistoryBeforeAdjusment(ctx context.Context, repo repository.SttPieceHistoryRepository, sttNo string, sttStatusValid []string) (*model.SttPieceHistoryCustom, error) {

	histories, err := repo.SelectBySttNo(ctx, &model.SttPieceHistoryViewParam{
		SttNoIn:   []string{sttNo},
		SortBy:    `sph.history_id`,
		OrderDesc: true,
	})

	if err != nil && len(histories) < 1 {
		return nil, shared.ERR_UNEXPECTED_DB
	}

	history := &histories[0]
	if history.HistoryStatus != model.STTADJUSTED {
		return history, nil
	}

	// get last status before stt adjusted
	for _, h := range histories {
		assignHistory := (shared.IsInArrayString(sttStatusValid, h.HistoryStatus) && len(sttStatusValid) > 0) || len(sttStatusValid) == 0
		if h.HistoryStatus != model.STTADJUSTED && assignHistory {
			history = &h
			break
		}
	}

	return history, nil
}
