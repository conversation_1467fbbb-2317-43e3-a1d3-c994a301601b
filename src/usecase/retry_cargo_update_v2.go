package usecase

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/notification"
	rb "github.com/Lionparcel/hydra/src/usecase/retry_cargo"
)

func (c *retryCargoCtx) RetryCargoUpdateV2(ctx context.Context, params *rb.RetryCargoUpdateV2Request) (*rb.RetryCargoUpdateV2Response, error) {
	var (
		opName  = "UsecaseRetryCargo-RetryCargoUpdate"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		rc      = retryCargoUpdateV2{}
		res     = new(rb.RetryCargoUpdateV2Response)
		err     error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		go c.partnerLog.Insert(context.Background(), &model.PartnerLog{
			Action:  model.PLNgenRetryUpdateV2,
			RefID:   rc.rcNo,
			Request: params,
			Response: map[string]interface{}{
				"error":    shared.CheckErrorNil(rc.errlog),
				"response": res,
			},
		})

		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": err})
	}()

	rc = retryCargoUpdateV2{
		service: c,
		params:  params,
		now:     c.timeRepo.Now(time.Now()),
	}

	err = params.Validation()
	if err != nil {
		rc.errlog = fmt.Errorf("action invalid : %s", params.Action)
		return nil, err
	}

	// cache for cancel
	rcCache := fmt.Sprintf("%s-%d-cancel", model.CacheRetryCargo, params.ID)
	cacheTimeOut := time.Minute * time.Duration(c.cfg.CancelRetryCargoCacheTime())

	// cache for delete
	if params.Action != model.RETRY_BOOKING_STATUS_CANCEL {
		rcCache = fmt.Sprintf("%s-%d", model.CacheRetryCargo, params.ID)
		cacheTimeOut = time.Second * 60
		defer c.cacheRepo.DeleteCache(selfCtx, rcCache)
	}

	// execute create cache
	if !c.cacheRepo.CreateCacheOnce(selfCtx, rcCache, cacheTimeOut) {
		rc.errlog = rb.LogRetryCargoUpdateInProgress[params.Action]
		err = rb.ErrorRetryCargoUpdateInProgress[params.Action]
		return nil, err
	}

	funcs := []func(ctx context.Context) error{
		rc.getRetryCargo,
		rc.processBaseOnAction,
	}

	for i := range funcs {
		err = funcs[i](selfCtx)
		if err != nil {
			return nil, err
		}
	}

	res.Action = params.Action
	res.RetryCargoID = int(params.ID)

	return res, nil
}

type retryCargoUpdateV2 struct {
	service *retryCargoCtx
	params  *rb.RetryCargoUpdateV2Request
	now     time.Time

	rcNo               string
	retryCargoData     *model.RetryCargo
	updateActionParams *model.RetryCargo
	errlog             error
}

// main process
func (rc *retryCargoUpdateV2) getRetryCargo(ctx context.Context) error {
	var (
		opName  = "retryCargoUpdateV2-getRetryCargo"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		trace.Finish(map[string]interface{}{"error": err})
	}()

	retryCargo, err := rc.service.retryCargoRepo.Get(selfCtx, &model.RetryCargoViewParams{
		FilterByRetryIsDeletedFalse: true,
		FilterByRetryID:             rc.params.ID,
	})
	if err != nil {
		rc.errlog = err
		return shared.ERR_UNEXPECTED_DB
	}
	if retryCargo == nil {
		rc.errlog = fmt.Errorf("retry cargo not found with id : %d", rc.params.ID)
		err = shared.NewMultiStringBadRequestError(shared.HTTPErrorDataNotFound, map[string]string{
			"en": "Retry Cargo ID not found",
			"id": "Retry Cargo ID tidak ditemukan",
		})
		return err
	}

	rc.retryCargoData = retryCargo
	rc.rcNo = retryCargo.RcNo
	rc.updateActionParams = &model.RetryCargo{
		RcID: retryCargo.RcID,
	}

	return nil
}

// main process
func (rc *retryCargoUpdateV2) processBaseOnAction(ctx context.Context) error {
	var (
		opName  = "retryCargoUpdateV2-processBaseOnAction"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		trace.Finish(map[string]interface{}{"error": err})
	}()

	funcs := map[string]func(ctx context.Context) error{
		model.RETRY_BOOKING_STATUS_DELETE: rc.processBaseOnActionDelete,
		model.RETRY_BOOKING_STATUS_CANCEL: rc.processBaseOnActionCancel,
	}

	err = funcs[rc.params.Action](selfCtx)
	if err != nil {
		return err
	}

	return nil
}

func (rc *retryCargoUpdateV2) processBaseOnActionDelete(ctx context.Context) error {
	var (
		opName  = "retryCargoUpdateV2-processBaseOnActionDelete"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		trace.Finish(map[string]interface{}{"error": err})
	}()

	eligibleForDelete := map[string]bool{
		model.RETRY_BOOKING_STATUS_CANCEL: true,
		model.RETRY_BOOKING_STATUS_FAILED: true,
	}

	if !eligibleForDelete[rc.retryCargoData.RcStatus] {
		rc.errlog = fmt.Errorf("can't delete Retry Cargo with ID %d", rc.retryCargoData.RcID)
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("Can't delete Retry Cargo with ID %d", rc.retryCargoData.RcID),
			"id": fmt.Sprintf("Tidak dapat menghapus Retry Cargo dengan ID %d", rc.retryCargoData.RcID),
		})
	}

	rc.updateActionParams.RcIsDeleted = true

	err = rc.updateData(selfCtx)
	if err != nil {
		return err
	}

	return nil
}

func (rc *retryCargoUpdateV2) processBaseOnActionCancel(ctx context.Context) error {
	var (
		opName  = "retryCargoUpdateV2-processBaseOnActionCancel"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		trace.Finish(map[string]interface{}{"error": err})
	}()

	if !model.IsStatusProcess[rc.retryCargoData.RcStatus] {
		rc.errlog = fmt.Errorf("can't cancel Retry Cargo with ID %d", rc.retryCargoData.RcID)
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorUnprocessableEntity, map[string]string{
			"en": fmt.Sprintf("Can't cancel Retry Cargo with ID %d", rc.retryCargoData.RcID),
			"id": fmt.Sprintf("Tidak dapat membatalkan Retry Cargo dengan ID %d", rc.retryCargoData.RcID),
		})
	}

	rc.updateActionParams.RcNo = rc.retryCargoData.RcNo
	rc.updateActionParams.RcStatus = model.RETRY_BOOKING_STATUS_CANCEL
	if rc.retryCargoData.RcBookingRequestID != "" {
		rc.updateActionParams.RcNo = rc.retryCargoData.RcBookingRequestID

		//lock process with key RcBookingRequestID
		key := fmt.Sprintf("ngenwebhook-%s", rc.retryCargoData.RcBookingRequestID)
		if ok := rc.service.cacheRepo.CreateCacheOnce(selfCtx, key, time.Minute); !ok {
			rc.errlog = fmt.Errorf("booking request id in proccess to retry booking")
			return shared.NewMultiStringBadRequestError(shared.HTTPErrorDataNotFound, map[string]string{
				"en": "Booking Request ID In proccess to retry booking",
				"id": "Booking Request ID sedang di proses booking",
			})
		}
	}

	rcRquest := new(model.CreateDetailCargoRetry)
	if err := json.Unmarshal([]byte(rc.retryCargoData.RcRequest), &rcRquest); err != nil {
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorDataNotFound, map[string]string{
			"en": "Failed to unmarshal request",
			"id": "Gagal unmarshal request",
		})
	}

	go rc.service.DeleteCargoProcessCache(context.Background(), rcRquest.CreateDetailCargoTrx.SttCargoDetail)

	go rc.cancelNgen(context.Background())

	return nil
}

func (rc *retryCargoUpdateV2) cancelNgen(ctx context.Context) error {
	var (
		opName       = "retryCargoUpdateV2-cancelNgen"
		trace        = tracer.StartTrace(ctx, opName)
		selfCtx      = trace.Context()
		ngenResponse *model.NgenCancelResponse
		err          error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		trace.Finish(map[string]interface{}{"error": err})
	}()

	defer func() {
		rcCacheCancel := fmt.Sprintf("%s-%d-cancel", model.CacheRetryCargo, rc.updateActionParams.RcID)
		rc.service.cacheRepo.DeleteCache(selfCtx, rcCacheCancel)
		keyBookingId := fmt.Sprintf("ngenwebhook-%s", rc.updateActionParams.RcNo)
		rc.service.cacheRepo.DeleteCache(selfCtx, keyBookingId)

		go rc.service.partnerLog.Insert(context.Background(), &model.PartnerLog{
			Action:  model.PLNgenCancelAWBReserveV2,
			Request: rc.updateActionParams.RcNo,
			Response: map[string]interface{}{
				"error":    shared.CheckErrorNil(err),
				"time":     rc.now,
				"request":  rc.updateActionParams.RcNo,
				"response": ngenResponse,
			},
			RefID: rc.updateActionParams.RcNo,
		})
	}()

	resp, err := rc.service.ngenRepo.NgenCancel(selfCtx, &model.NgenCancelRequest{
		AWB: rc.updateActionParams.RcNo,
	})
	if err != nil || resp == nil {
		return err
	}

	ngenResponse = resp

	rc.updateActionParams.RcResponse = resp.Data

	isNeedUpdateData := rc.sendFcmCancelRetryCargo(selfCtx)
	if !isNeedUpdateData {
		return nil
	}

	err = rc.updateData(selfCtx)
	if err != nil {
		return err
	}

	return nil
}

func (rc *retryCargoUpdateV2) sendFcmCancelRetryCargo(ctx context.Context) bool {
	var (
		opName  = "retryCargoUpdateV2-sendFcmCancelRetryCargo"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		trace.Finish(map[string]interface{}{"error": err})
	}()

	isSuccess := strings.Contains(strings.ToLower(rc.updateActionParams.RcResponse), "cancelled") || strings.Contains(strings.ToLower(rc.updateActionParams.RcResponse), "Cancelled")

	dataCargoFCM := map[string]string{}
	cargoRemark := model.CargoRemark{}
	json.Unmarshal([]byte(rc.retryCargoData.RcRemark), &cargoRemark)

	dataCargoFCM["cargo_no"] = rc.updateActionParams.RcNo
	dataCargoFCM["status"] = "success"
	dataCargoFCM["message"] = "Berhasil cancel booking"
	dataCargoFCM["destination_city_code"] = rc.retryCargoData.RcDestinationCityCode
	dataCargoFCM["destination_city_name"] = cargoRemark.DestinationCityName
	bodyFCM := fmt.Sprintf("booking kargo %s - berhasil di batalkan", rc.updateActionParams.RcNo)

	if !isSuccess {
		dataCargoFCM["status"] = "failed"
		dataCargoFCM["message"] = "Gagal cancel booking"
		reason := rc.updateActionParams.GenerateCancelMessage()
		dataCargoFCM["reason_failed"] = reason
		bodyFCM = fmt.Sprintf("booking kargo %s - %s", rc.updateActionParams.RcNo, reason)
	}

	go rc.service.FirebaseUc.PublishFirebaseCloudMessaging(context.Background(), notification.FirebaseCloudMessagingRequest{
		Title:     "Cargo Notification",
		Body:      bodyFCM,
		HubID:     int64(rc.retryCargoData.RcHubID),
		PartnerID: int64(rc.retryCargoData.RcPartnerID),
		ReffID:    rc.updateActionParams.RcNo,
		Data:      dataCargoFCM,
	})

	return isSuccess
}

func (rc *retryCargoUpdateV2) updateData(ctx context.Context) error {
	var (
		opName  = "retryCargoUpdateV2-updateData"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		trace.Finish(map[string]interface{}{"error": err})
	}()

	funcs := []func(ctx context.Context) error{
		rc.updateRetryCargo,
		rc.updateCargoReserve,
	}
	for i := range funcs {
		err = funcs[i](ctx)
		if err != nil {
			return err
		}
	}

	return err
}

func (rc *retryCargoUpdateV2) updateRetryCargo(ctx context.Context) error {
	var (
		opName  = "retryCargoUpdateV2-updateRetryCargo"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		trace.Finish(map[string]interface{}{"error": err})
	}()

	_, err = rc.service.retryCargoRepo.Update(selfCtx, rc.updateActionParams, false, nil)
	if err != nil {
		rc.errlog = err
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed Update Retry Cargo",
			"id": "Gagal Update Retry Cargo",
		})
	}

	return nil
}

func (rc *retryCargoUpdateV2) updateCargoReserve(ctx context.Context) error {
	var (
		opName  = "retryCargoUpdateV2-updateCargoReserve"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		trace.Finish(map[string]interface{}{"error": err})
	}()

	cargoReserveData, err := rc.service.CargoReserveRepo.Get(selfCtx, model.CargoReserveParams{
		CargoReserve: model.CargoReserve{
			CrAwbNo: rc.rcNo,
		},
	})
	if err != nil {
		rc.errlog = fmt.Errorf("failed get cargo reserve with awb no : %s", rc.retryCargoData.RcNo)
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorDataNotFound, map[string]string{
			"en": "Cargo Reserve is not found",
			"id": "Cargo Reserve tidak ditemukan",
		})
	}

	isNoEligibleToUpdateCargoReserve := cargoReserveData == nil || (cargoReserveData.CrStatus != model.CargoReserveStatusOnProcess && cargoReserveData.CrStatus != model.CargoReserveStatusFailed)
	if isNoEligibleToUpdateCargoReserve {
		return nil
	}

	request := &model.CargoReserve{
		CrAwbNo: rc.rcNo,
		CrStatus: func() string {
			if rc.params.Action == model.RETRY_BOOKING_STATUS_CANCEL {
				return model.CargoReserveStatusInvalid
			}
			return model.CargoReserveStatusUnused
		}(),
		CrUpdatedAt: rc.now,
	}

	if err = rc.service.CargoReserveRepo.UpdateStatusByAWBNo(selfCtx, request); err != nil {
		rc.errlog = fmt.Errorf("failed update cargo reserve with awb no : %s", rc.retryCargoData.RcNo)
		return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Failed Update Cargo Reserve",
			"id": "Gagal Update Cargo Reserve",
		})
	}

	return nil
}
