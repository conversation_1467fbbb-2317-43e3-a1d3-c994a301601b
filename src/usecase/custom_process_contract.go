package usecase

import (
	"context"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/src/usecase/custom_process"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/src/repository"
	customProcess "github.com/Lionparcel/hydra/src/usecase/custom_process"
)

type CustomProcess interface {
	ViewCustomProcess(ctx context.Context, req *customProcess.ViewCustomProcessRequest) (*shared.Pagination, error)
	CreateCustomProcess(ctx context.Context, form *customProcess.CreateCustomProcessRequest) (*customProcess.CreateCustomProcessResponse, error)
	ViewDetailCustomProcess(ctx context.Context, req *customProcess.ViewDetailCustomProcessRequest) (*customProcess.ViewDetailCustomProcessResponse, error)
	ViewDetailStt(ctx context.Context, req *customProcess.DetailCustomProcessRequest) (*customProcess.DetailSttCustomProcessResponse, error)
	ViewDetailSttScan(ctx context.Context, req *customProcess.DetailCustomProcessRequest) (*customProcess.DetailSttCustomProcessResponse, error)
	UpdateForceUpdate(ctx context.Context, params *custom_process.ForceUpdateRequest) error
}

type customProcessCtx struct {
	customProcessRepo       repository.CustomProcessRepository
	partnerRepo             repository.PartnerRepository
	sttPieceRepo            repository.SttPiecesRepository
	commodityRepo           repository.CommodityRepository
	districtRepo            repository.DistrictRepository
	cityRepo                repository.CityRepository
	gatewaySttStatusUc      GatewaySttStatus
	gatewaySttUc            GatewayStt
	middlewareRepo          repository.MiddlewareCLient
	sttActivityUc           SttActivity
	sttPieceHistoryRepo     repository.SttPieceHistoryRepository
	deliveryRepo            repository.DeliveryRepository
	customProcessRoleRepo   repository.CustomProcessRoleRepository
	messageGatewayUc        MessageGateway
	sttPaymentUc            SttPayment
	accountRepo             repository.AccountRepository
	sttUc                   Stt
	cfg                     *config.Config
	clientRepo              repository.ClientRepository
	partnerLog              repository.PartnerLogRepository
	reasonRepo              repository.ReasonRepository
	sttRepo                 repository.SttRepository
	productRepo             repository.ProductTypeRepository
	repoCommodity           repository.CommodityRepository
	bagRepo                 repository.BagRepository
	bagVendorRepo           repository.BagVendorRepository
	shipmentRepo            repository.ShipmentRepository
	sttOptionalRateRepo     repository.SttOptionalRateRepository
	DictionaryError         shared.DictionaryError
	flagManagementRepo      repository.FlagManagementRepository
	timeRepo                repository.TimeRepository
	rtcUc                   ReadyToCargo
	requestPriorityDelivery RequestPriorityDelivery
	priorityDeliveryRepo    repository.PriorityDeliveryRepository
	sttDueRepo              repository.SttDueRepository
	deliveryManifestRepo    repository.DeliveryManifestRepository
}

func NewCustomProcess(
	customProcessRepo repository.CustomProcessRepository,
	partnerRepo repository.PartnerRepository,
	sttPieceRepo repository.SttPiecesRepository,
	commodityRepo repository.CommodityRepository,
	repoDistrict repository.DistrictRepository,
	cityRepo repository.CityRepository,
	gatewaySttStatusUc GatewaySttStatus,
	middlewareRepo repository.MiddlewareCLient,
	sttActivityUc SttActivity,
	sttPieceHistoryRepo repository.SttPieceHistoryRepository,
	deliveryRepo repository.DeliveryRepository,
	customProcessRoleRepo repository.CustomProcessRoleRepository,
	messageGatewayUc MessageGateway,
	sttPaymentUc SttPayment,
	accountRepo repository.AccountRepository,
	sttUc Stt,
	cfg *config.Config,
	clientRepo repository.ClientRepository,
	partnerLog repository.PartnerLogRepository,
	reasonRepo repository.ReasonRepository,
	sttRepo repository.SttRepository,
	productRepo repository.ProductTypeRepository,
	repoCommodity repository.CommodityRepository,
	bagRepo repository.BagRepository,
	bagVendorRepo repository.BagVendorRepository,
	shipmentRepo repository.ShipmentRepository,
	sttOptionalRateRepo repository.SttOptionalRateRepository,
	DictionaryError shared.DictionaryError,
	gatewaySttUc GatewayStt,
	flagManagementRepo repository.FlagManagementRepository,
	timeRepo repository.TimeRepository,
	rtcUc ReadyToCargo,
	requestPriorityDelivery RequestPriorityDelivery,
	priorityDeliveryRepo repository.PriorityDeliveryRepository,
	sttDueRepo repository.SttDueRepository,
	deliveryManifestRepo repository.DeliveryManifestRepository,
) CustomProcess {
	return &customProcessCtx{
		customProcessRepo:       customProcessRepo,
		partnerRepo:             partnerRepo,
		sttPieceRepo:            sttPieceRepo,
		commodityRepo:           commodityRepo,
		districtRepo:            repoDistrict,
		cityRepo:                cityRepo,
		gatewaySttStatusUc:      gatewaySttStatusUc,
		gatewaySttUc:            gatewaySttUc,
		middlewareRepo:          middlewareRepo,
		sttActivityUc:           sttActivityUc,
		sttPieceHistoryRepo:     sttPieceHistoryRepo,
		deliveryRepo:            deliveryRepo,
		messageGatewayUc:        messageGatewayUc,
		sttPaymentUc:            sttPaymentUc,
		customProcessRoleRepo:   customProcessRoleRepo,
		accountRepo:             accountRepo,
		sttUc:                   sttUc,
		cfg:                     cfg,
		clientRepo:              clientRepo,
		partnerLog:              partnerLog,
		reasonRepo:              reasonRepo,
		sttRepo:                 sttRepo,
		productRepo:             productRepo,
		repoCommodity:           repoCommodity,
		bagRepo:                 bagRepo,
		bagVendorRepo:           bagVendorRepo,
		shipmentRepo:            shipmentRepo,
		DictionaryError:         DictionaryError,
		sttOptionalRateRepo:     sttOptionalRateRepo,
		flagManagementRepo:      flagManagementRepo,
		timeRepo:                timeRepo,
		rtcUc:                   rtcUc,
		requestPriorityDelivery: requestPriorityDelivery,
		priorityDeliveryRepo:    priorityDeliveryRepo,
		sttDueRepo:              sttDueRepo,
		deliveryManifestRepo:    deliveryManifestRepo,
	}
}
