package usecase

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/Lionparcel/go-lptool/v2/lputils"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/general"
	"github.com/Lionparcel/hydra/src/usecase/sti_dest"
	"github.com/Lionparcel/hydra/src/usecase/stt"
	"github.com/Lionparcel/hydra/src/usecase/stt_activity"
	"github.com/Lionparcel/hydra/src/usecase/stt_piece_history_remark_helper"
	"github.com/abiewardani/dbr/v2"
)

func (c *stiDestCtx) CreateStiDest(ctx context.Context, req *sti_dest.CreateStiDestRequest) (result *sti_dest.CreateStiDestResponse, err error) {
	opName := "Usecase-stiDestCtx-CreateStiDest"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	result = &sti_dest.CreateStiDestResponse{}
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": result, "error": err})
	}()
	if err = req.Validate(); err != nil {
		return nil, err
	}

	now := c.timeRepo.Now(time.Now())
	partnerInfo, err := c.createStiDestGetPartnerInfo(selfCtx, req.PartnerID, req.Token)
	if err != nil {
		return result, err
	}

	resultParams, err := c.generateCreateStiDestParams(selfCtx, req, partnerInfo, now)
	if err != nil {
		return result, err
	}
	result.TotalSTTSuccess = len(resultParams.ListSttSuccess)
	result.STTFailed = resultParams.STTFailed
	result.TotalSTTFailed = len(result.STTFailed)

	err = c.sttPaymentUc.SttPaymentValidation(selfCtx, resultParams.ListSttNo)
	if err != nil {
		return result, err
	}

	data, err := c.stiDestRepo.CreateStiDest(selfCtx, &resultParams.Params)
	if err != nil {
		return result, shared.ERR_UNEXPECTED_DB
	}
	result.StiDestID = data.StiDestID

	/*
	 * Updating list time stt status
	 */
	go c.sttActivityUc.UpdateSttTime(context.Background(), &stt_activity.SttActivityRequest{
		ListSttData: resultParams.ListSttUpdateTime,
	})

	if result.TotalSTTSuccess > 0 {
		c.createStiDestPublishData(req, resultParams, partnerInfo, now)
	}

	go func(stts map[string]model.Stt) {
		for i := range stts {
			c.rtcUc.UpdateInactiveRTCBySttId(context.Background(), int(stts[i].SttID))
		}
	}(resultParams.ListSttSuccess)

	return result, nil
}

func (c *stiDestCtx) createStiDestSttDetailValidation(ctx context.Context, params sti_dest.CreateStiDestSttDetailValidationParams) (res sti_dest.CreateStiDestSttDetailValidationResult, err error) {
	opName := "Usecase-stiDestCtx-createStiDestSttDetailValidation"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params.SttNo, "result": res, "error": err})
	}()
	res.SttDetail, err = c.sttRepo.SelectDetailV2(selfCtx, &model.SttSelectDetailParams{SttNo: params.SttNo, IsNeedDecrypt: true})
	if err != nil || len(res.SttDetail) == 0 {
		res.STTFailed = &general.STTFailedGeneralResponse{
			SttNo: params.SttNo,
		}
		return res, nil
	}
	sttRow := res.SttDetail[0]

	// Get route leg type city-to-city
	routeleg, err := c.routeRepo.SelectRouteLeg(selfCtx, sttRow.SttOriginCityID, sttRow.SttDestinationCityID, model.CityToCity, params.Token)
	if err != nil || routeleg == nil {
		return res, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while get route leg",
			"id": "Terjadi kesalahan pada saat ambil route leg",
		})
	}
	res.RouteStatus = routeleg.RouteStatus(sttRow.Stt, params.PartnerCityCode)

	validationRes, err := c.sttUc.ValidateStatusIncomingLILO(selfCtx, &stt.ValidateShipmentLiloRequest{
		Stt:          sttRow.Stt,
		SttPieceID:   sttRow.SttPieceID,
		UpdateStatus: res.RouteStatus,
	})
	if err != nil {
		res.STTFailed = &general.STTFailedGeneralResponse{
			SttNo:       params.SttNo,
			SttElexysNo: sttRow.SttElexysNo.Value(),
			Error:       err.Error(),
		}
		return res, nil
	}
	if !validationRes.CheckIsShipmentValid() {
		res.STTFailed = &general.STTFailedGeneralResponse{
			SttNo:       params.SttNo,
			SttElexysNo: sttRow.SttElexysNo.Value(),
			Error:       "Unable to process STT",
		}
		return res, nil
	}

	res.STTFailed = c.validateSttDetail(selfCtx, sttRow, params.PartnerID, res.RouteStatus)
	return res, nil
}

func (c *stiDestCtx) validateSttDetail(ctx context.Context, sttRow model.SttDetailResult, partnerID int, routeStatus string) (sttFailed *general.STTFailedGeneralResponse) {
	opName := "Usecase-stiDestCtx-createStiDestGetSttDetail"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": sttRow, "result": sttFailed})
	}()

	switch {
	case sttRow.SttLastStatusID == model.CLAIM:
		// check if already update to STIDEST before
		history, err := c.sttHistoryRepo.Get(selfCtx, &model.SttPieceHistoryViewParam{
			SttPieceHistorySttPieceID: sttRow.SttPieceID,
			SttPieceHistoryStatus:     model.STIDEST,
		})
		isStatusUpdateStiDest := err == nil && history == nil
		if isStatusUpdateStiDest {
			break
		}
		fallthrough
	case !model.IsAllowUpdateToSTIDEST[sttRow.SttLastStatusID]: // validate last status stt piece
		fallthrough
	case sttRow.SttLastStatusID == model.PICKUP_TRUCKING && c.cfg.ConfigAllowScanSTIDESTIsPickupTrucking(): //validate last status pickup_trucking and check config
		return &general.STTFailedGeneralResponse{
			SttNo:       sttRow.SttNo,
			SttElexysNo: sttRow.SttElexysNo.Value(),
		}
	}

	if !c.IsValidActor(selfCtx, int64(sttRow.SttPieceID), partnerID, routeStatus) {
		return &general.STTFailedGeneralResponse{
			SttNo:       sttRow.SttNo,
			SttElexysNo: sttRow.SttElexysNo.Value(),
		}
	}
	return nil
}

func (c *stiDestCtx) createStiDestGetPartnerInfo(ctx context.Context, partnerID int, token string) (locationInfo *sti_dest.CreateStiDestPartnerInfo, err error) {
	opName := "Usecase-stiDestCtx-createStiDestGetPartnerInfo"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": partnerID, "result": locationInfo})
	}()

	// get detail partner that generate sti-dest
	stiDestPartner, err := c.partnerRepo.GetByID(selfCtx, partnerID, token)
	if err != nil || stiDestPartner == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting making HTTP request",
			"id": "Terjadi kesalahan pada saat HTTP request",
		})
	}
	if stiDestPartner.Data.PartnerLocation == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Partner is not yet mapped with location",
			"id": "Partner belum di Mapping dengan lokasi",
		})
	}

	// Validate city partner that generate sti-dest
	city, err := c.cityRepo.Get(selfCtx, stiDestPartner.Data.PartnerLocation.CityCode, token)
	if err != nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "An error occurred while getting STT and Piece",
			"id": "Terjadi kesalahan pada saat getting STT and Piece",
		})
	}
	if city == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "City Not Found",
			"id": "Kota tidak ditemukan",
		})
	}

	locationInfo = &sti_dest.CreateStiDestPartnerInfo{
		Partner:                *stiDestPartner,
		PartnerCityCode:        stiDestPartner.Data.PartnerLocation.CityCode,
		PartnerCityName:        stiDestPartner.Data.PartnerLocation.City.Name,
		PartnerCityNameStiDest: city.Name,
	}
	if stiDestPartner.Data.PartnerLocation.District != nil {
		locationInfo.PartnerDistrictName = stiDestPartner.Data.PartnerLocation.District.Name
	}
	return locationInfo, nil
}

func (c *stiDestCtx) generateCreateStiDestPieceHistory(ctx context.Context, req sti_dest.RequestBuildPieceDetail, sttDetail model.SttDetailResult, partnerInfo *sti_dest.CreateStiDestPartnerInfo) (history []model.SttPieceHistory, listSttUpdateTime []stt_activity.SttActivityRequestDetail) {
	opName := "Usecase-stiDestCtx-generateCreateStiDestPieceHistory"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": history})
	}()

	history = make([]model.SttPieceHistory, 0)
	listSttUpdateTime = make([]stt_activity.SttActivityRequestDetail, 0)
	tempRemarksPieceHistory := &model.RemarkPieceHistory{
		CargoNumber:         req.CargoNo,
		HistoryLocationName: req.SttCityName,
		HistoryDistrictName: sttDetail.SttOriginDistrictName,
	}
	pieceHistory := model.SttPieceHistory{
		SttPieceID:         int64(sttDetail.SttPiece.SttPieceID),
		HistoryLocation:    sttDetail.SttOriginCityID,
		HistoryActorID:     model.AccountSystem.ActorID,
		HistoryActorName:   model.AccountSystem.ActorName,
		HistoryCreatedAt:   req.StiDestCreatedAt,
		HistoryCreatedBy:   0,
		HistoryCreatedName: model.AccountSystem.ActorName,
		HistoryRemark:      tempRemarksPieceHistory.ToString(),
	}
	sttUpdatedTime := stt_activity.SttActivityRequestDetail{
		SttNo:         sttDetail.SttNo,
		SttStatusTime: req.StiDestCreatedAt,
	}

	switch sttDetail.SttPieceLastStatusID {
	case model.BKD:
		if sttDetail.Stt.SttBookedByType == model.POS {
			partnerData, err := c.partnerRepo.GetByID(selfCtx, sttDetail.Stt.SttBookedBy, req.Token)
			isPartnerPosBranch := err == nil && partnerData != nil && (partnerData.Data.PartnerPosType == "branch" || partnerData.Data.PartnerPosType == "pickup")
			if isPartnerPosBranch {
				pieceHistory.HistoryStatus = model.PUPC
				pieceHistory.HistoryActorRole = model.CONSOLE
				history = append(history, pieceHistory)
				// Adding time STI-DEST status STT
				sttUpdatedTime.SttStatus = model.PUPC
				listSttUpdateTime = append(listSttUpdateTime, sttUpdatedTime)
			}
		}
		fallthrough
	case model.PUPC:
		pieceHistory.HistoryStatus = model.PUP
		pieceHistory.HistoryActorRole = model.RuleForPosCODDelivery
		history = append(history, pieceHistory)
		// Adding time STI-DEST status PUP
		sttUpdatedTime.SttStatus = model.PUP
		listSttUpdateTime = append(listSttUpdateTime, sttUpdatedTime)
		fallthrough
	case model.PUP:
		pieceHistory.HistoryStatus = model.STI
		pieceHistory.HistoryActorRole = model.CONSOLE
		history = append(history, pieceHistory)
		// Adding time STI-DEST status STI
		sttUpdatedTime.SttStatus = model.STI
		listSttUpdateTime = append(listSttUpdateTime, sttUpdatedTime)
	}

	// if last status STI OR STI SC OR CARGO PLANE, TRUCK, TRAIN
	tempRemarksPieceHistory.HistoryLocationName = partnerInfo.PartnerCityNameStiDest
	tempRemarksPieceHistory.HistoryDistrictName = partnerInfo.PartnerDistrictName
	SetHub(c.cityRepo, c.repoDistrict, &stt_piece_history_remark_helper.SetHubParams{
		Ctx:                 selfCtx,
		Token:               req.Token,
		HubID:               req.HubID,
		HubName:             req.HubName,
		HubOriginCity:       req.HubOriginCity,
		HubDistrictCode:     req.HubDistrictCode,
		RemarksPieceHistory: tempRemarksPieceHistory,
	})
	pieceHistory.HistoryStatus = req.RouteStatus
	pieceHistory.HistoryLocation = partnerInfo.PartnerCityCode
	pieceHistory.HistoryActorID = partnerInfo.Data.ID
	pieceHistory.HistoryActorName = partnerInfo.Data.Name
	pieceHistory.HistoryActorRole = req.PartnerType
	pieceHistory.HistoryCreatedBy = int(req.AccountID)
	pieceHistory.HistoryCreatedName = req.AccountName
	pieceHistory.HistoryRemark = tempRemarksPieceHistory.ToString()
	history = append(history, pieceHistory)
	// Adding time STI-DEST status STI-DEST, MIS-ROUTE, TRANSIT
	sttUpdatedTime.SttStatus = req.RouteStatus
	listSttUpdateTime = append(listSttUpdateTime, sttUpdatedTime)

	return history, listSttUpdateTime
}

func (c *stiDestCtx) generateCreateStiDestParams(ctx context.Context, req *sti_dest.CreateStiDestRequest, partnerInfo *sti_dest.CreateStiDestPartnerInfo, stiDestCreatedAt time.Time) (result *sti_dest.GenerateCreateStiDestParams, err error) {
	opName := "Usecase-stiDestCtx-generateCreateStiDestParams"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var totalGrossWeight, totalVolumeWeight float64
	result = &sti_dest.GenerateCreateStiDestParams{}
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": result})
	}()

	result.ListSttSuccess = make(map[string]model.Stt)
	for _, v := range req.SttBag {
		dataStt, err := c.createStiDestSttDetailValidation(selfCtx, sti_dest.CreateStiDestSttDetailValidationParams{
			SttNo:           v.SttNo,
			Token:           req.Token,
			PartnerID:       partnerInfo.Data.ID,
			PartnerCityCode: partnerInfo.PartnerCityCode,
		})
		if err != nil {
			return nil, err
		}
		if dataStt.STTFailed != nil {
			result.STTFailed = append(result.STTFailed, *dataStt.STTFailed)
			continue
		}

		result.ListSttNo = append(result.ListSttNo, v.SttNo)
		totalGrossWeight += dataStt.SttDetail[0].SttGrossWeight
		totalVolumeWeight += dataStt.SttDetail[0].SttVolumeWeight
		resultParams, err := c.createStiDestBuildPieceDetail(selfCtx, sti_dest.RequestBuildPieceDetail{
			BagCode:          v.BagCode,
			IsOneBagScan:     v.IsOneBagScan,
			CargoNo:          v.CargoNo,
			PartnerType:      req.PartnerType,
			AccountID:        int(req.AccountID),
			AccountName:      req.AccountName,
			PartnerID:        req.PartnerID,
			PartnerCode:      req.PartnerCode,
			PartnerName:      req.PartnerName,
			HubID:            req.HubID,
			HubName:          req.HubName,
			HubOriginCity:    req.HubOriginCity,
			HubDistrictCode:  req.HubDistrictCode,
			RouteStatus:      dataStt.RouteStatus,
			StiDestCreatedAt: stiDestCreatedAt,
			Token:            req.Token,
		}, dataStt.SttDetail, partnerInfo)
		if err != nil {
			return nil, err
		}

		result.Params.StiDestDetail = append(result.Params.StiDestDetail, resultParams.Params.StiDestDetail...)
		result.Params.Stt = append(result.Params.Stt, resultParams.Params.Stt...)
		result.Params.History = append(result.Params.History, resultParams.Params.History...)
		result.SttPieces = append(result.SttPieces, resultParams.SttPieces...)
		result.ListSttUpdateTime = append(result.ListSttUpdateTime, resultParams.ListSttUpdateTime...)
		result.ListSttSuccess[dataStt.SttDetail[0].SttNo] = resultParams.ListSttSuccess[dataStt.SttDetail[0].SttNo]
	}
	result.Params.StiDest = model.StiDest{
		StiDestPartnerID:         req.PartnerID,
		StiDestPartnerCode:       req.PartnerCode,
		StiDestPartnerName:       req.PartnerName,
		StiDestTotalStt:          len(result.ListSttSuccess),
		StiDestTotalGrossWeight:  totalGrossWeight,
		StiDestTotalVolumeWeight: totalVolumeWeight,
		StiDestTotalPiece:        len(result.Params.StiDestDetail),
		StiDestStatusType:        model.STIDEST,
		StiDestArrivalCityCode:   partnerInfo.PartnerCityCode,
		StiDestArrivalCityName:   partnerInfo.PartnerCityName,
		StiDestStatusUpdatedAt:   stiDestCreatedAt,
		StiDestCreatedAt:         stiDestCreatedAt,
		StiDestCreatedBy:         int(req.AccountID),
		StiDestCreatedName:       req.AccountName,
		StiDestUpdatedAt:         &stiDestCreatedAt,
	}
	return result, nil
}

func (c *stiDestCtx) createStiDestBuildPieceDetail(ctx context.Context, req sti_dest.RequestBuildPieceDetail, sttDetail []model.SttDetailResult, partnerInfo *sti_dest.CreateStiDestPartnerInfo) (result *sti_dest.GenerateCreateStiDestParams, err error) {
	opName := "Usecase-stiDestCtx-createStiDestBuildPieceDetail"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	result = &sti_dest.GenerateCreateStiDestParams{}
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": result})
	}()

	// Get Information City STT
	cityStt, err := c.cityRepo.Get(selfCtx, sttDetail[0].SttOriginCityID, req.Token)
	if err != nil {
		return nil, shared.ERR_UNEXPECTED_DB
	}
	if cityStt == nil {
		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "City Not Found",
			"id": "Kota tidak ditemukan",
		})
	}
	req.SttCityName = cityStt.Name

	transitStatus := req.RouteStatus == model.TRANSIT
	missRoute := req.RouteStatus == model.MISROUTE
	result.ListSttSuccess = make(map[string]model.Stt)
	result.Params.Stt = append(result.Params.Stt, model.Stt{
		SttNo:               sttDetail[0].SttNo,
		SttLastStatusID:     req.RouteStatus,
		SttUpdatedActorID:   dbr.NewNullInt64(req.PartnerID),
		SttUpdatedActorRole: dbr.NewNullString(req.PartnerType),
		SttUpdatedActorName: dbr.NewNullString(req.PartnerName),
	})

	// insert piece to detail
	for _, val := range sttDetail {
		result.Params.StiDestDetail = append(result.Params.StiDestDetail, model.StiDestDetail{
			StiDestDetailSttNo:                val.SttNo,
			StiDestDetailSttID:                int(val.SttID),
			StiDestDetailBagNo:                req.BagCode,
			StiDestDetailIsTransit:            &transitStatus,
			StiDestDetailIsMissRoute:          &missRoute,
			StiDestDetailSttTotalPiece:        val.SttTotalPiece,
			StiDestDetailSttPieceID:           int(val.SttPieceID),
			StiDestDetailSttPieceNo:           val.SttPieceNo,
			StiDestDetailSttPieceGrossWeight:  val.SttPieceGrossWeight,
			StiDestDetailSttPieceVolumeWeight: val.SttPieceVolumeWeight,
			StiDestPickupPartnerID:            req.PartnerID,
			StiDestPickupPartnerCode:          req.PartnerCode,
			StiDestPickupPartnerName:          req.PartnerName,
			StiDestProductType:                val.SttProductType,
			StiDestDetailOneBagScan:           req.IsOneBagScan,
			StiDestDetailCreatedAt:            req.StiDestCreatedAt,
			StiDestDetailUpdatedAt:            &req.StiDestCreatedAt,
		})
		history, sttUpdatedTime := c.generateCreateStiDestPieceHistory(selfCtx, req, val, partnerInfo)
		result.SttPieces = append(result.SttPieces, val.SttPiece)
		result.Params.History = append(result.Params.History, history...)
		result.ListSttUpdateTime = append(result.ListSttUpdateTime, sttUpdatedTime...)
		val.Stt.SttLastStatusID = req.RouteStatus
		result.ListSttSuccess[val.SttNo] = val.Stt
	}
	return result, nil
}

func (c *stiDestCtx) sendStiDestToVendor(sttData model.Stt, partnerInfo *sti_dest.CreateStiDestPartnerInfo, stiDestCreatedAt time.Time, req *sti_dest.CreateStiDestRequest) {
	if c.cfg.NinjaBooking() && c.cfg.StiDestCreateToNinja() {
		c.ninjaUc.CreateOrderNinja(context.Background(), &model.CreateOrderRequest{
			Stt:         sttData,
			Partner:     &partnerInfo.Partner,
			StiDestTime: stiDestCreatedAt,
			Token:       req.Token,
		})
	}
	if c.cfg.JNEBooking() && c.cfg.StiDestCreateToJNE() {
		c.jneUc.JneCreateAirwaybill(context.Background(), &model.JneCreateAirwaybillRequest{
			Stt:     sttData,
			Partner: &partnerInfo.Partner,
			Token:   req.Token,
		})
	}

	if c.cfg.PtPosBooking() && c.cfg.StiDestCreateToPtPos() {
		// PT POST
		c.ptPosUc.CreateOrderPtPos(context.Background(), &model.CreateOrderPtPosParams{
			Stt:     sttData,
			Partner: &partnerInfo.Partner,
			Token:   req.Token,
		})
	}
}

type createStiDestPublishToAlgoParams struct {
	Req              *sti_dest.CreateStiDestRequest
	SttData          model.Stt
	PartnerInfo      *sti_dest.CreateStiDestPartnerInfo
	StiDestCreatedAt time.Time
	History          []model.SttPieceHistory
}

func (c *stiDestCtx) createStiDestPublishToAlgo(params *createStiDestPublishToAlgoParams) {
	req := params.Req
	sttData := params.SttData
	partnerInfo := params.PartnerInfo
	stiDestCreatedAt := params.StiDestCreatedAt
	if model.IsPublishStatusStiDesc[sttData.SttLastStatusID] {
		payload := &model.UpdateSttStatusWithExtendForMiddleware{
			UpdateSttStatus: &model.UpdateSttStatus{
				SttNo:      sttData.GetValidSttElexysNo(),
				Datetime:   stiDestCreatedAt.UTC(),
				StatusCode: sttData.SttLastStatusID,
				Location:   partnerInfo.PartnerCityCode,
				Remarks:    fmt.Sprintf(`Station transit in consolidator destination %s`, partnerInfo.Data.Name),
				City:       partnerInfo.PartnerCityName,
				Partner:    model.GenerateUpdateSttStatusPartnerData(partnerInfo.Partner),
				UpdatedBy:  partnerInfo.Data.Name,
				UpdatedOn:  stiDestCreatedAt.UTC(),
			},
			ServiceType:      model.PACKAGESERVICE,
			Product:          sttData.SttProductType,
			Pieces:           sttData.SttTotalPiece,
			GrossWeight:      sttData.SttGrossWeight,
			VolumeWeight:     sttData.SttVolumeWeight,
			ChargeableWeight: sttData.SttChargeableWeight,
			BookedForType:    sttData.SttBookedForType,
		}

		AppendLastAndSystemStatus(AppendLastAndSystemStatusParams{
			StatusSubmitParams: payload,
			SttPieceHistories:  params.History,
			PartnerName:        partnerInfo.Data.Name,
		})

		go lputils.TrackGoroutine(func(goCtx context.Context) {
			c.gatewaySttStatusUc.StatusSubmit(goCtx, payload)
		}, int((5 * time.Minute).Seconds()))

		if sttData.SttLastStatusID == model.STIDEST {
			c.sendStiDestToVendor(sttData, partnerInfo, stiDestCreatedAt, req)
		}
	} else if strings.EqualFold(sttData.SttBookedForType, model.CLIENT) {
		go lputils.TrackGoroutine(func(goCtx context.Context) {
			c.middlewareRepo.SubmitDataToMiddleware(goCtx, &model.UpdateSttStatusWithExtendForMiddleware{
				UpdateSttStatus: &model.UpdateSttStatus{
					SttNo:      sttData.GetValidSttElexysNo(),
					Datetime:   stiDestCreatedAt.UTC(),
					StatusCode: sttData.SttLastStatusID,
					Location:   partnerInfo.PartnerCityCode,
					Remarks:    fmt.Sprintf(`Station transit in consolidator destination %s`, partnerInfo.Data.Name),
					City:       partnerInfo.PartnerCityName,
					UpdatedBy:  partnerInfo.Data.Name,
					UpdatedOn:  stiDestCreatedAt.UTC(),
				},
				ServiceType:      model.PACKAGESERVICE,
				Product:          sttData.SttProductType,
				Pieces:           sttData.SttTotalPiece,
				GrossWeight:      sttData.SttGrossWeight,
				VolumeWeight:     sttData.SttVolumeWeight,
				ChargeableWeight: sttData.SttChargeableWeight,
			})
		}, int((5 * time.Minute).Seconds()))
	}
}

func (c *stiDestCtx) createStiDestPublishData(req *sti_dest.CreateStiDestRequest, resultParams *sti_dest.GenerateCreateStiDestParams, partnerInfo *sti_dest.CreateStiDestPartnerInfo, stiDestCreatedAt time.Time) {

	// publish to ALGO
	go func() {
		mapSttPiecesBySttID := make(map[int64][]model.SttPiece)
		mapSttHistoriesByPieceID := make(map[int64][]model.SttPieceHistory)
		mapSttHistoriesBySttID := make(map[int64][]model.SttPieceHistory)

		for _, sttPiece := range resultParams.SttPieces {
			mapSttPiecesBySttID[sttPiece.SttPieceSttID] = append(mapSttPiecesBySttID[sttPiece.SttPieceSttID], sttPiece)
		}

		for _, history := range resultParams.Params.History {
			mapSttHistoriesByPieceID[history.SttPieceID] = append(mapSttHistoriesByPieceID[history.SttPieceID], history)
		}

		for sttID, pieces := range mapSttPiecesBySttID {
			if len(pieces) > 0 {
				if histories, found := mapSttHistoriesByPieceID[pieces[0].SttPieceID]; found {
					mapSttHistoriesBySttID[sttID] = histories
				}
			}
		}

		for _, sttData := range resultParams.ListSttSuccess {
			histories := mapSttHistoriesBySttID[sttData.SttID]

			c.createStiDestPublishToAlgo(&createStiDestPublishToAlgoParams{Req: req, SttData: sttData, PartnerInfo: partnerInfo, StiDestCreatedAt: stiDestCreatedAt, History: histories})
		}
	}()

	// check if shortland
	go lputils.TrackGoroutine(func(goCtx context.Context) {
		c.CheckShortlandWithBagging(goCtx, req, req.SttBag, &partnerInfo.Partner)
	}, int((5 * time.Minute).Seconds()))

	// send message to user
	go lputils.TrackGoroutine(func(goCtx context.Context) {
		for _, stt := range resultParams.ListSttSuccess {
			if model.IsPublishStatusStiDesc[stt.SttLastStatusID] {
				c.messageGatewayUc.SendMessage(goCtx, &model.SendMessageRequest{
					RecieverNumber: model.RecieverNumber{
						PackageSender:   stt.SttSenderPhone,
						PackageReceiver: stt.SttRecipientPhone,
					},
					PackageType: shared.GetPackageType(stt.SttIsCOD, stt.SttIsDFOD),
					EventStatus: model.STIDEST,
					Token:       req.Token,
				}, &stt)
			}
		}
	}, int((5 * time.Minute).Seconds()))
}
