package model

import (
	"encoding/json"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/abiewardani/dbr/v2"

	"github.com/Lionparcel/hydra/shared/crypto"
)

const (
	// ECOMMERCE ...
	ECOMMERCE = `ecommerce`
	// BATAM ...
	BATAM = `batam`
	// RETURN ...
	RETURN = `return`
	// PERSONALEFFECT ...
	PERSONALEFFECT = `personal-effect`
	// EXBATAM ...
	EXBATAM = `transit`
	// GIVEN ...
	GIVEN = `given`
	// RETAIL
	RETAIL = `retail`
	// DIPLOMATICPUNCH
	DIPLOMATICPUNCH = `diplomatic-pouch`

	// MANUAL ...
	MANUAL = `manual`
	// CA ...
	CA = `customer-apps`
	// MP ...
	MP = `marketplace`
	// ALGO ...
	ALGO = `algo`
	// API ...
	API = `api`
	// CORPORATE ...
	CORPORATE = `corporate`

	// DistrictPrefix ...
	DistrictPrefix = `DIST`

	// PACKAGESERVICE ...
	PACKAGESERVICE = `PACKAGE`

	// DOCUMENTSERVICE ...
	DOCUMENTSERVICE = `DOCUMENT`

	// MARKETPLACE ...
	MARKETPLACE = `Marketplace`

	// ONEPACK ...
	ONEPACK = `ONEPACK`
	// REGPACK ...
	REGPACK = `REGPACK`
	// MIXPACK ...
	MIXPACK = `MIXPACK`
	// DORPACK ...
	DORPACK = `DORPACK`
	// SDPACK ...
	SDPACK = `SDPACK`
	// LANDPACK ...
	LANDPACK = `LANDPACK`
	// DOCUPACK ...
	DOCUPACK = `DOCUPACK`
	// INTERPACK ...
	INTERPACK = `INTERPACK`
	// BIGPACK ...
	BIGPACK = `BIGPACK`
	// JAGOPACK ...
	JAGOPACK = `JAGOPACK`
	// BOSSPACK ...
	BOSSPACK = `BOSSPACK`
	// JUMBOPACK ...
	JUMBOPACK = `JUMBOPACK`
	// JUMBOPACKH2H jumbopack product type (hub to hub)
	JUMBOPACKH2H = `JUMBOPACKH2H`
	// SAMEDAY ...
	SAMEDAY = `SAMEDAY`
	// VIPPACK ...
	VIPPACK = `VIPPACK`

	// CacheSttKey
	CacheSttKey = `stt`
	// CacheSttReport
	CacheSttReportKey = `stt_report`

	// MAX_TOTAL_GROSS_WEIGHT ...
	MAX_TOTAL_GROSS_WEIGHT = 400.0
	// MAX_TOTAL_VOLUME_WEIGHT ...
	MAX_TOTAL_VOLUME_WEIGHT = 400.0
	// MAX_GROSS_WEIGHT_PER_PIECE ...
	MAX_GROSS_WEIGHT_PER_PIECE = 400.0
	// MAX_VOLUME_WEIGHT_PER_PIECE ...
	MAX_VOLUME_WEIGHT_PER_PIECE = 400.0

	// MIN_WEIGHT ...
	MIN_WEIGHT     = 0.1
	MIN_WEIGHT_NEW = 0.001

	// MIN_VOLUME_WEIGHT
	MIN_VOLUME_WEIGHT = 0.01

	// MIN_GOODS_PRICE ...
	MIN_GOODS_PRICE = 0.1

	// MIN_PIECE_NUMBER ...
	MIN_PIECE_NUMBER = 1
	// MAX_PIECE_NUMBER ...
	MAX_PIECE_NUMBER = 15

	// MAX_LENGTH_EXT_REF_NO ...
	MAX_LENGTH_EXT_REF_NO = 100

	MAX_LENGTH_ID_NO = 100

	// COD_TYPE
	COD    = "cod"
	NonCOD = "non_cod"
	DFOD   = "dfod"

	// MIN_INSURANCE_BASIC_
	MIN_INSURANCE_BASIC = 50000000

	// STT_REPORT
	STT_REPORT                = `stt`
	SIMPLE_STT_REPORT         = `simple-stt`
	SHORTLAND_REPORT          = `shortland_report`
	POS_PARENT_REPORT         = `pos-parent`
	REQUEST_PRIOROTY_DELIVERY = `request-priority-delivery`

	// COD DELIVERY
	COD_PIC_SHUTTLE_SUMMARY = `cod-pic-shuttle-summary`
	COD_PIC_SHUTTLE         = `cod-pic-shuttle`

	// MAX_LENGTH_PHONE_NUMBER ...
	MAX_LENGTH_PHONE_NUMBER = 15

	// PICKUP_TRUCKING
	PICKUP_TRUCKING = "PICKUP_TRUCKING"
	// DROPOFF_TRUCKING
	DROPOFF_TRUCKING = "DROPOFF_TRUCKING"
	// TRUCKING_ACCOUNT_NAME
	TRUCKING_ACCOUNT_NAME = `trucking-system`

	FREE              = `free`
	PAID              = `paid`
	UNPAID            = `unpaid`
	CODPAYMENT        = `cod-payment`
	QRIS              = `QRIS`
	CASH              = `CASH`
	PaymentMethodFree = `FREE`

	AddressTypeHome   = `home`
	AddressTypeOffice = `office`

	BookingRetail    = `retail`
	BookingForClient = `client`
	BookingShipment  = `shipment`

	GoodsPrice           = `goods_price`
	GoodsPriceTotalTarif = `goods_price,total_tarif`

	Invoice = `invoice`

	// PosType
	AccountRolePartnerPosTypeBranch   = `branch`
	AccountRolePartnerPosTypePickup   = `pickup`
	AccountRolePartnerPosTypeParent   = `parent`
	AccountRolePartnerPosTypeAllChild = `all-child`

	// BookingCrossDockingFailed
	BookingCrossDockingFailed = `booking_failed`
	// BagCrossDockingFailed
	BagCrossDockingFailed = `bagging_failed`
	// SuffixNonCrossDockingFailed
	SuffixNonCrossDockingFailed = `_non_lilo`
	// AlertSttManualThresholdCtx
	AlertSttManualThresholdCtx = `alert-stt-manual-threshold`

	OTOPACK150 = "OTOPACK150"
	OTOPACK250 = "OTOPACK250"

	// HOLD BALANCE HISTORY
	HBH_REPORT = `hold_balance_history_report`

	STATUS_NOT_READY_TO_ARCHIVE = "0"
	STATUS_ELIGIBLE_TO_ARCHIVE  = "1"
	STATUS_READY_TO_ARCHIVE     = "2"

	// Stt Flag
	SttFlagKT  = "KT"
	SttFlagDO  = "DO"
	SttFlagH2H = "H2H"
	SttFlagRTS = "RTS"
	SttFlagRHQ = "RHQ"
	SttFlagRMB = "RMB"
	SttFlagRMR = "RMR"
	SttFlagCNX = "CNX"

	// Driver Source
	DRIVER_SOURCE_ALGO    = "ALGO"
	DRIVER_SOURCE_GENESIS = "GENESIS"
)

const (
	PARTITION_STATUS_NOT_READY_TO_ARCHIVE = iota
	PARTITION_STATUS_ELIGIBLE_TO_ARCHIVE
	PARTITION_STATUS_READY_TO_ARCHIVE
)

var (
	// IsPrefixSttValid ...
	IsPrefixSttValid = map[string]bool{
		PrefixClient:               true,
		PrefixPartner:              true,
		PrefixMixpack:              true,
		PrefixAutoClient:           true,
		PrefixAutoPartner:          true,
		PrefixAutoMixpack:          true,
		PrefixAutoDO:               true,
		PrefixAutoCA:               true,
		PrefixAutoCADO:             true,
		PrefixAutoBookingForClient: true,
		PrefixAutoCAACR:            true,
		PrefixAutoClientCCR:        true,
		// PrefixClientDO:             true,
		// PrefixAutoCADOR:            true,
	}

	// IsGoodsStatusValid ...
	IsGoodsStatusValid = map[string]bool{
		ECOMMERCE:       true,
		BATAM:           true,
		EXBATAM:         true,
		RETURN:          true,
		PERSONALEFFECT:  true,
		GIVEN:           true,
		RETAIL:          true,
		DIPLOMATICPUNCH: true,
	}

	// IsInsuranceValid ...
	IsInsuranceValid = map[string]bool{
		INSURANCEFREE:    true,
		INSURANCEBASIC:   true,
		INSURANCEPREMIUM: true,
	}

	// MappingBoolStatus ...
	MappingBoolStatus = map[bool]string{
		true:  "Yes",
		false: "No",
	}

	// MappingInsuranceType ...
	MappingInsuranceType = map[string]string{
		INSURANCEFREE:    INSURANCEFREENAME,
		INSURANCEBASIC:   INSURANCEBASICNAME,
		INSURANCEPREMIUM: INSURANCEPREMIUMNAME,
	}

	// MappingInsuranceTypeFromName ...
	MappingInsuranceTypeFromName = map[string]string{
		INSURANCEFREENAME:    INSURANCEFREE,
		INSURANCEBASICNAME:   INSURANCEBASIC,
		INSURANCEPREMIUMNAME: INSURANCEPREMIUM,
	}

	// STTFormat ...
	STTFormat = regexp.MustCompile(`(^(\d{2})LP(\d{13})$)`)

	// IsProductTypeValid ...
	IsProductTypeValid = map[string]bool{
		ONEPACK:    true,
		REGPACK:    true,
		MIXPACK:    true,
		DORPACK:    true,
		SDPACK:     true,
		LANDPACK:   true,
		DOCUPACK:   true,
		INTERPACK:  true,
		BIGPACK:    true,
		JAGOPACK:   true,
		BOSSPACK:   true,
		OTOPACK150: true,
		OTOPACK250: true,
		VIPPACK:    true,
	}

	PerformanceListProductTypeValid = map[string]bool{
		ONEPACK:   true,
		REGPACK:   true,
		JAGOPACK:  true,
		BIGPACK:   true,
		INTERPACK: true,
		DOCUPACK:  true,
		BOSSPACK:  true,
	}

	// LETTERS
	LETTERS = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890")

	// IsBookedTypeValid
	IsBookedTypeValid = map[string]bool{
		CLIENT:   true,
		PARTNER:  true,
		COSTUMER: true,
	}

	// IsAccountTypeValid ...
	IsAccountTypeValid = map[string]bool{
		POS:        true,
		CLIENT:     true,
		CONSOLE:    true,
		SUBCONSOLE: true,
		COSTUMER:   true,
	}

	// IsNotAllowUpdateToTrucking
	IsNotAllowUpdateToTrucking = map[string]bool{
		POD:            true,
		CNX:            true,
		SCRAP:          true,
		CLAIM:          true,
		CODREJ:         true,
		RTSHQ:          true,
		RTS:            true,
		STTADJUSTEDPOD: true,
		REROUTE:        true,
		DEL:            true,
		DEX:            true,
		SCRAPCD:        true,
		CNXCD:          true,
	}

	// TruckingStatus
	TruckingStatus = map[string]bool{
		PICKUP_TRUCKING:  true,
		DROPOFF_TRUCKING: true,
	}

	IsHistoryStatusActorMisroute = map[string]bool{
		CARGOPLANE: true,
		CARGOTRUCK: true,
		CARGOTRAIN: true,
		CARGOSHIP:  true,
		STI:        true,
	}

	// SttManifestColumn ...
	SttManifestColumn = []string{`Booking date`, `STT Number`, `Kota Tujuan`, `Total Koli`, `Total Berat`, `Product Name`, `Nama Pengirim`, `Alamat Pengirim`, `Nomor telepon Pengirim`, `Nama Penerima`, `Alamat Penerima`, `Nomor Telepon Penerima`, `NPWP Penerima`, `Komoditas`, `HSCODE`, `Status Barang`, `Harga Barang`, `B.M.`, `PPN`, `PPH`, `Total Pajak Barang`, `Tax payment`, `Current Status`, `Booked by`, `External Reference Number`, `Volume weight`, `Pieces per pack`, `Next commodity`, `Tax payment`}

	// SttManifestColumn ...
	SttManifestColumnEn = []string{"Booking date", "STT Number", "Desitnation City", "Total Pieces", "Total Weight", "Product Name", "Shipper Name", "Shipper Address", "Shipper Phone Number", "Consignee Name", "Consignee Address", "Consignee Phone Number", "Consignee Tax Number", "Commodity", "HSCODE", "Item Status", "Goods Value", "B.M.", "PPN", "PPH", "Total Tax Item", "Tax payment", "Current Status", "Booked by", "External Reference Number", "Volume weight", "Pieces per pack", "Next commodity", "Tax payment"}

	// SttReportColumn ...
	SttReportColumn = []string{`STT Number Genesis`, `STT Number Elexys`, `Booking Date`, `External Reference Number`, `Booking ID/Shipment ID`, `Vendor Booking Number`, `Shipper Name`, `Shipper Address`, `Consignee Name`, `Consignee Address`, `Consignee Phone`, `Booked By`, `Booked For`, `Client Code`, `Origin Ursa Code`, `Origin District`, `Origin City`, `Destination City`, `Destination District`, `Destination Ursa Code`, `Product Name`, `Commodity Name`, `Tax Registration Number`, `Bag Number`, `Cargo Type`, `Cargo Number`, `Pieces/Koli`, `Volume Weight`, `Gross Weight`, `Chargeable Weight`, `Goods Value`, `COD Amount`, `Publish Rate`, `Shipping Surcharge`, `Origin Forward Rate`, `Destination Forward Rate`, `Document Surcharge`, `Commodity Surcharge`, `Heavyweight Surcharge`, `Wood Crate Fee`, `Insurance Fee`, `COD Fee`, `Total Amount Rate`, `BM`, `PPN`, `PPH`, `POS Booking Discount`, `Insurance Discount`, `Discount to Customer`, `COD Discount`, `DTPOL Discount`, `Delivered By`, `Last Status`}

	// Valid Address Type
	ValidRecipientAddressType = map[string]bool{
		AddressTypeHome:   true,
		AddressTypeOffice: true,
	}

	// IsDeliveryTypeValid
	IsDeliveryTypeValid = map[string]bool{
		COD:    true,
		NonCOD: true,
		All:    true,
	}

	IsPrefixReverseJourney = map[string]bool{
		PrefixAutoReverseJourney:                        true,
		PrefixAutoReturnReverseJourney:                  true,
		PrefixAutoClientCCR:                             true,
		PrefixAutoCAACR:                                 true,
		PrefixAutoReturnReverseJourneyMissbokingReroute: true,
	}

	ArrayIsPrefixReverseJourneyPerformanceDelivery = []string{PrefixAutoReverseJourney, PrefixAutoReturnReverseJourney, PrefixAutoClientCCR, PrefixAutoCAACR}

	// IsAllowGenerateEstimateSLAProductType
	IsAllowGenerateEstimateSLAProductType = map[string]bool{
		ONEPACK:  true,
		BOSSPACK: true,
	}

	// SttCancelProductType
	SttCancelProductType = map[string]string{
		ONEPACK:    REGPACK,
		BOSSPACK:   REGPACK,
		REGPACK:    REGPACK,
		JAGOPACK:   JAGOPACK,
		BIGPACK:    BIGPACK,
		OTOPACK150: OTOPACK150,
		OTOPACK250: OTOPACK250,
		INTERPACK:  INTERPACK,
		VIPPACK:    REGPACK,
	}

	IsSttAllowUpdateHALCD = map[string]bool{
		PrefixClient:               true,
		PrefixAutoBookingForClient: true,
	}

	IsJumbopack = map[string]bool{
		JUMBOPACK:    true,
		JUMBOPACKH2H: true,
	}

	IsValidArchiveType = map[string]bool{
		STATUS_NOT_READY_TO_ARCHIVE: true,
		STATUS_ELIGIBLE_TO_ARCHIVE:  true,
		STATUS_READY_TO_ARCHIVE:     true,
	}

	IsReverseRetailPartner = map[string]bool{
		PrefixAutoPartner: true,
		PrefixPartner:     true,
		PrefixAutoCAACR:   true,
	}

	ExcludeCheckSaldoSttPrefix = map[string]bool{
		PrefixAutoBookingForClient: true,
		PrefixAutoClient:           true,
		PrefixClient:               true,
		PrefixAutoDO:               true,
		PrefixAutoCADO:             true,
		PrefixAutoCA:               true,
	}

	// IsPrefixSttValidSttAdjustmentConfig ...
	IsPrefixSttValidSttAdjustmentConfig = map[string]bool{
		PrefixPartner:                      true,
		PrefixAutoPartner:                  true,
		PrefixClient:                       true,
		PrefixAutoClient:                   true,
		PrefixAutoDO:                       true,
		PrefixAutoBookingForClient:         true,
		PrefixAutoCA:                       true,
		PrefixAutoMixpack:                  true,
		PrefixAutoReverseJourneyMisBooking: true,
		PrefixAutoReturnReverseJourney:     true,
		PrefixAutoCAACR:                    true,
		PrefixAutoClientCCR:                true,
	}
)

const (
	// SCFKeyDFODNewRules is a const STT Custom Flag Key of Attribute
	SCFKeyDFODNewRules = `dfod_new_rules`
	// SCFValueCARetailFinalStatus is a const STT Custom Flag Value of Attribute
	SCFValueCARetailFinalStatus = `ca_retail_final_status`
)

// Stt ...
type Stt struct {
	SttID                 int64   `json:"stt_id" db:"id" faker:"oneof: 1, 2"`
	SttNo                 string  `json:"stt_no" db:"stt_no"`
	SttShipmentID         string  `json:"stt_shipment_id" db:"stt_shipment_id"`
	SttClientID           int     `json:"stt_client_id" db:"stt_client_id"`
	SttPosID              int     `json:"stt_pos_id" db:"stt_pos_id"`
	SttTaxNumber          string  `json:"stt_tax_number" db:"stt_tax_number"`                     // here
	SttGoodsEstimatePrice float64 `json:"stt_goods_estimate_price" db:"stt_goods_estimate_price"` // here
	SttGoodsStatus        string  `json:"stt_goods_status" db:"stt_goods_status"`                 // here
	SttTotalAmount        float64 `json:"stt_total_amount" db:"stt_total_amount"`
	SttNoRefExternal      string  `json:"stt_no_ref_external" db:"stt_no_ref_external"` // here
	SttSource             string  `json:"stt_source" db:"stt_source"`                   // here

	SttOriginCityID          string `json:"stt_origin_city_id" db:"stt_origin_city_id"`
	SttDestinationCityID     string `json:"stt_destination_city_id" db:"stt_destination_city_id"`
	SttOriginDistrictID      string `json:"stt_origin_district_id" db:"stt_origin_district_id"`
	SttDestinationDistrictID string `json:"stt_destination_district_id" db:"stt_destination_district_id"`

	SttSenderName    string `json:"stt_sender_name" db:"stt_sender_name"`
	SttSenderPhone   string `json:"stt_sender_phone" db:"stt_sender_phone"`
	SttSenderAddress string `json:"stt_sender_address" db:"stt_sender_address"`

	SttRecipientName        string         `json:"stt_recipient_name" db:"stt_recipient_name"`
	SttRecipientAddress     string         `json:"stt_recipient_address" db:"stt_recipient_address"`
	SttRecipientAddressType dbr.NullString `json:"stt_recipient_address_type" db:"stt_recipient_address_type"`
	SttRecipientPhone       string         `json:"stt_recipient_phone" db:"stt_recipient_phone"`

	SttProductType string `json:"stt_product_type" db:"stt_product_type"`

	SttOriginDistrictRate      float64 `json:"stt_origin_district_rate" db:"stt_origin_district_rate"`
	SttDestinationDistrictRate float64 `json:"stt_destination_district_rate" db:"stt_destination_district_rate"`

	SttPublishRate           float64 `json:"stt_publish_rate" db:"stt_publish_rate"`
	SttShippingSurchargeRate float64 `json:"stt_shipping_surcharge_rate" db:"stt_shipping_surchage_rate"`
	SttDocumentSurchargeRate float64 `json:"stt_document_surcharge_rate" db:"stt_document_surcharge_rate"` // here

	SttCommoditySurchargeRate   float64 `json:"stt_commodity_surcharge_rate" db:"stt_commodity_surcharge_rate"`     // here
	SttHeavyweightSurchargeRate float64 `json:"stt_heavyweight_surcharge_rate" db:"stt_heavyweight_surcharge_rate"` // here

	SttBMTaxRate  float64 `json:"stt_bm_tax_rate" db:"stt_bm_tax_rate"`   // here
	SttPPNTaxRate float64 `json:"stt_ppn_tax_rate" db:"stt_ppn_tax_rate"` // here
	SttPPHTaxRate float64 `json:"stt_pph_tax_rate" db:"stt_pph_tax_rate"` // here

	SttGrossWeight      float64 `json:"stt_gross_weight" db:"stt_gross_weight"`
	SttVolumeWeight     float64 `json:"stt_volume_weight" db:"stt_volume_weight"`
	SttChargeableWeight float64 `json:"stt_chargeable_weight" db:"stt_chargeable_weight"`

	SttCommodityCode string `json:"stt_commodity_code" db:"stt_commodity_code"`
	SttCommodityID   int    `json:"stt_commodity_id,omitempty"`
	SttInsuranceType string `json:"stt_insurance_type" db:"stt_insurance_type"`
	SttTotalPiece    int    `json:"stt_total_piece" db:"stt_total_piece"`
	SttWarningStatus string `json:"stt_warning_status" db:"stt_warning_status"`
	SttCounter       int    `json:"stt_counter" db:"stt_counter"`
	SttLastStatusID  string `json:"stt_last_status_id" db:"stt_last_status_id"`
	SttClientSttID   string `json:"stt_client_stt_id" db:"stt_client_stt_id"`
	SttVendorSttID   string `json:"stt_vendor_stt_id" db:"stt_vendor_stt_id"`
	SttBilledTo      string `json:"stt_billed_to" db:"stt_billed_to"`

	SttCODAmount float64 `json:"stt_cod_amount" db:"stt_cod_amount"`
	SttCODFee    float64 `json:"stt_cod_fee" db:"stt_cod_fee"`
	SttIsDFOD    bool    `json:"stt_is_dfod" db:"stt_is_dfod"`
	SttIsCOD     bool    `json:"stt_is_cod" db:"stt_is_cod"` // here
	SttIsDO      bool    `json:"stt_is_do" db:"stt_is_do"`   // here

	SttMeta          string `json:"stt_meta" db:"stt_meta"`
	SttTroubleStatus string `json:"stt_trouble_status" db:"stt_trouble_status"`

	SttBookedAt     time.Time `json:"stt_booked_at" db:"stt_booked_at"` // here
	SttBookedName   string    `json:"stt_booked_name" db:"stt_booked_name"`
	SttBookedByType string    `json:"stt_booked_by_type" db:"stt_booked_by_type"`
	SttBookedBy     int       `json:"stt_booked_by" db:"stt_booked_by"`

	SttCreatedAt   time.Time `json:"stt_created_at" db:"stt_created_at"`
	SttCreatedName string    `json:"stt_created_name" db:"stt_created_name"` // here
	SttCreatedBy   int       `json:"stt_created_by" db:"stt_created_by"`     // here
	SttUpdatedAt   time.Time `json:"stt_updated_at" db:"stt_updated_at"`
	SttUpdatedBy   int       `json:"stt_updated_by" db:"stt_updated_by"`     // here
	SttUpdatedName string    `json:"stt_updated_name" db:"stt_updated_name"` // here

	// to save latest updated by client
	SttUpdatedActorID   dbr.NullInt64  `json:"stt_updated_actor_id" db:"stt_updated_actor_id"`
	SttUpdatedActorRole dbr.NullString `json:"stt_updated_actor_role" db:"stt_updated_actor_role"`
	SttUpdatedActorName dbr.NullString `json:"stt_updated_actor_name" db:"stt_updated_actor_name"`

	ShipmentAlgoID                string `json:"shipment_algo_id" db:"shipment_algo_id"`
	SttRoute                      string `json:"stt_route" db:"stt_route"`
	SttDeliveryAttempt            int    `json:"stt_delivery_attempt" db:"stt_delivery_attempt"`
	SttHeavyweightSurchargeRemark string `json:"stt_heavyweight_surcharge_remark" db:"stt_heavyweight_surcharge_remark"`

	SttNextCommodity string         `json:"stt_next_commodity" db:"stt_next_commodity"`
	SttPiecePerPack  int            `json:"stt_piece_per_pack" db:"stt_piece_per_pack"`
	SttElexysNo      dbr.NullString `json:"se_elexys_stt_no" db:"se_elexys_stt_no"`

	SttCommodityName               string       `json:"stt_commodity_name" db:"stt_commodity_name"`
	SttCommodityHsCode             string       `json:"stt_commodity_hs_code" db:"stt_commodity_hs_code"`
	SttOriginCityName              string       `json:"stt_origin_city_name" db:"stt_origin_city_name"`
	SttDestinationCityName         string       `json:"stt_destination_city_name" db:"stt_destination_city_name"`
	SttOriginDistrictName          string       `json:"stt_origin_district_name" db:"stt_origin_district_name"`
	SttOriginDistrictUrsaCode      string       `json:"stt_origin_district_ursa_code" db:"stt_origin_district_ursa_code"`
	SttDestinationDistrictName     string       `json:"stt_destination_district_name" db:"stt_destination_district_name"`
	SttDestinationDistrictUrsaCode string       `json:"stt_destination_district_ursa_code" db:"stt_destination_district_ursa_code"`
	SttBookedByCode                string       `json:"stt_booked_by_code" db:"stt_booked_by_code"`
	SttBookedForID                 int          `json:"stt_booked_for_id" db:"stt_booked_for_id"`
	SttBookedForName               string       `json:"stt_booked_for_name" db:"stt_booked_for_name"`
	SttBookedForCode               string       `json:"stt_booked_for_code" db:"stt_booked_for_code"`
	SttBookedForType               string       `json:"stt_booked_for_type" db:"stt_booked_for_type"`
	SttPaymentStatus               string       `json:"stt_payment_status" db:"stt_payment_status"`
	SttPaymentDateAt               dbr.NullTime `json:"stt_payment_date_at" db:"stt_payment_date_at"`
	ReasonDescription              string
	SttAssessmentStatus            dbr.NullString           `json:"stt_assessment_status" db:"stt_assessment_status"`
	SttSenderPhoneEncrypted        crypto.Encrypted[string] `json:"-" db:"stt_sender_phone_encrypted"`
	SttRecipientPhoneEncrypted     crypto.Encrypted[string] `json:"-" db:"stt_recipient_phone_encrypted"`
}

func (c *Stt) Route() []RouteDefault {
	res := make([]RouteDefault, 0)
	if c.SttRoute != `` {
		routeDefaultResponse := RouteDefaultResponse{}
		err := json.Unmarshal([]byte(c.SttRoute), &routeDefaultResponse)
		if err != nil {
			return res
		}
		res = routeDefaultResponse.Data
	}
	return res
}

func (c *Stt) RouteStatus(cityCode string) string {
	status := MISROUTE
	if c.SttDestinationCityID == cityCode {
		return STIDEST
	}
	route := c.Route()
	for _, val := range route {
		if val.LegOriginID == cityCode || val.LegDestinationID == cityCode {
			return TRANSIT
		}
	}
	return status
}

func (c *Stt) GetValidSttElexysNo() string {
	if c.SttElexysNo.Valid {
		return c.SttElexysNo.Value()
	}
	return strings.ToUpper(c.SttNo)
}

type SttDetailBagResult struct {
	Stt
	SttPiece
	Bag
	BagDetail
}

// SttViewParams ...
type SttViewParams struct {
	BasedFilter
	CodType                  string
	SttCreatedAt             time.Time
	PaymentStatus            string
	ListSttNo                []string
	Search                   string
	SearchLike               string
	SttPieceID               string
	BookedStart              string
	BookedEnd                string
	OriginDistrictID         string
	DestinationDistrictID    string
	ClientPartnerID          string
	ProductType              string
	Status                   string
	Statuses                 []string
	ArrivalCity              string
	IsCod                    bool
	IsDfod                   bool
	InsuranceType            string
	InsuranceTypeArray       []string
	ProductTypeArray         []string
	OriginCityID             string
	DestinationCityID        string
	PosIDWhereIn             []int
	ClientID                 int
	ShipmentID               string
	NoLimit                  bool
	BookedBy                 int
	BookedByType             string
	BookedForID              string
	BookedForType            string
	FromDate                 string
	EndDate                  string
	SttNumber                string
	UserType                 string
	SttNumberArray           []string
	CostumerPhone            string
	PieceNo                  int
	PosID                    int
	BagDetail                bool
	CreatedByArray           []int
	BookedName               string
	SttIDArray               []int
	BookByArray              []int
	LastStatusID             string
	UpdatedActorID           int
	UpdatedActorRole         string
	ClientIDWhereIn          []int
	IsSpecificBookedFor      bool
	IsWithDetail             bool
	JoinWithSttElexys        bool
	AllClient                bool
	AllPos                   bool
	IsNoDefaultQuery         bool
	CustomColumn             string
	SttNoElexysInArray       []string
	CustomSelect             string
	IsCustomSelect           bool
	IsCountData              bool
	IsSearchByPattern        bool
	IsJoinCargo              bool
	CargoType                string
	CargoPartnerID           int
	CargoOriginCityCode      string
	CargoDestinationCityCode string
	NotOriginCityID          string
	NotDestinationCityID     string
	IsSpecificBookedBy       bool
	MinPayDate               string
	MaxPayDate               string
	IsPaidUnpaid             bool
	SttUpdatedAtFrom         *time.Time
	SttUpdatedAtTo           *time.Time
	IsReadSttPayment         bool
	SenderPhone              string
	RecipientPhone           string
	SttNoShipmentIDSttNoEx   []string

	LastStatusIDWhereIn        []string
	LastStatusIDIncludeWhereIn []string
	LastStatusIDExcludeWhereIn []string
	SttNoRefExternal           string

	StatusNotIn            []string
	BookedAtFrom           time.Time
	BookedAtTo             time.Time
	SearchSttToRefExternal string
	RecipientPhoneArray    []string

	ExcludeSttPrefixIn []string

	SttIDAfter             int64
	IsEligibleArchivedType bool

	SttNoOrShipmentID     string
	SttAssessmentStatus   string
	AccountType           string
	IsUseDefaultUpdatedAt bool
	IsNeedDecrypt         bool
	DbConnection          string
	OriginCityIdsIn       []string
}

func (c *SttViewParams) SelectedColumn() string {
	if c.CustomColumn != `` {
		return c.CustomColumn
	}
	return `stt.*, stt_assessment_status`
}

type SttDetailTrackingJourneyParam struct {
	SttNo           string                   `json:"stt_no"`
	SttTrackingMeta *DetailSttReverseJourney `json:"stt_tracking_meta"`
}

type SttViewDetailParams struct {
	Stt
	Search          string
	SearchByPattern bool
	CustomColumns   string
	OrderBy         string
	SortBy          string
	Limit           int
	GetFromMaster   bool
	OriginCityIDsIn []string
}

// SttCreateParams ...
type SttCreateParams struct {
	SttCreate                   []SttCreate
	IsElexys                    bool
	IsMigrateData               bool
	IsRelabelFromExternalSource bool
}

type SttWithReverseJourneyViewParams struct {
	BasedFilter
	BookedStart      string
	BookedEnd        string
	Status           string
	Statuses         []string
	IsCod            bool
	ProductTypeArray []string
	NoLimit          bool
	BookedForID      int
	BookedForType    string
	IsNoDefaultQuery bool
	CustomColumn     string
	IsCountData      bool
	SenderPhone      string
	RecipientPhone   string

	LastStatusIDWhereIn        []string
	LastStatusIDIncludeWhereIn []string
	LastStatusIDExcludeWhereIn []string
	SttNoRefExternal           string

	StatusNotIn            []string
	SearchSttToRefExternal string
	RecipientPhoneArray    []string

	SelectWithSttReverseJourney bool
	IncludeSttPrefixIn          []string
}

// SttCreate ...
type SttCreate struct {
	IsSttManual           bool
	ShipmentPackageID     int64
	Stt                   Stt
	SttPieces             []SttPiece
	SttOptionalRate       []SttOptionalRate
	SttPieceHistory       SttPieceHistory
	ArrSttPieceHistory    []SttPieceHistory
	CheckTariff           *CheckTariffBase
	OriginDistrict        *District
	DestinationDistrict   *District
	Commodity             *Commodity
	BookedForExternalCode string
	BookedByExternalCode  string
	NotElexysSttSubmit    bool
	ElexysTariff          *SttElexys
	SttManual             *SttManual
	IsInvoiceCreated      bool
	SttDue                *STTDueModel

	// for case update
	DuplicateHistoriesForNewSttPiece []SttPieceHistory
	OldSttPiecesNeedToBeArchieved    []int64

	SttCustomFlag []SttCustomFlag

	CreateSttCustomFlag []SttCustomFlag // not only for dfod_pasti
}

// Reuseable STT struct
type SttBaseResponse struct {
	SttID                  int64                  `json:"stt_id"`
	SttNo                  string                 `json:"stt_no"`
	SttProductType         string                 `json:"stt_product_type"`
	SttTotalPiece          int                    `json:"stt_total_piece"`
	SttDestinationCityID   string                 `json:"stt_destination_city_id,omitempty"`
	SttDestinationCityName string                 `json:"stt_destination_city_name,omitempty"`
	SttOriginCityID        string                 `json:"stt_origin_city_id,omitempty"`
	SttOriginCityName      string                 `json:"stt_origin_city_name,omitempty"`
	SttVolumeWeight        float64                `json:"stt_volume_weight,omitempty"`
	SttGrossWeight         float64                `json:"stt_gross_weight,omitempty"`
	SttChargeableWeight    float64                `json:"stt_chargeable_weight,omitempty"`
	SttCommodityCode       string                 `json:"stt_commodity_code,omitempty"`
	SttCommodityName       string                 `json:"stt_commodity_name,omitempty"`
	SttLastStatusID        string                 `json:"stt_last_status_id,omitempty"`
	SttCODAmount           string                 `json:"stt_cod_amount,omitempty"`
	SttBookedAt            time.Time              `json:"stt_booked_at,omitempty"`
	Piece                  []SttPieceBaseResponse `json:"piece"`
	SttElexysNo            string                 `json:"stt_elexys_no,omitempty"`
}

// Reuseable STT Piece struct
type SttPieceBaseResponse struct {
	SttPieceID           int64   `json:"stt_piece_id"`
	SttPieceNo           int     `json:"stt_piece_no"`
	SttPieceGrossWeight  float64 `json:"stt_piece_gross_weight"`
	SttPieceVolumeWeight float64 `json:"stt_piece_volume_weight"`
}

type SttMeta struct {
	EstimateSLA                 string                        `json:"estimate_sla"`
	RoundingDiff                float64                       `json:"rounding_diff"`
	OriginCityName              string                        `json:"origin_city_name"`
	OriginDistrictName          string                        `json:"origin_district_name"`
	DestinationCityName         string                        `json:"destination_city_name"`
	DestinationDistrictName     string                        `json:"destination_district_name"`
	SttBookedByCountry          string                        `json:"stt_booked_by_country,omitempty"`
	TicketCode                  string                        `json:"ticket_code"`
	OtherShipperTicketCode      []OtherShipperTicketCode      `json:"other_shipper_ticket_code"`
	BookedByExternalType        string                        `json:"booked_by_external_type"`
	BookedByExternalCode        string                        `json:"booked_by_external_code"`
	BookedForExternalType       string                        `json:"booked_for_external_type"`
	BookedForExternalCode       string                        `json:"booked_for_external_code"`
	RetailTariff                *CheckTariffBase              `json:"retail_tariff,omitempty"`
	DetailCalculateRetailTariff []DetailCalculateRetailTariff `json:"detail_calculate_retail_tariff,omitempty"`
	PostalCodeDestination       string                        `json:"postal_code_destination,omitempty"`
	ClaimDetail                 *ClaimDetail                  `json:"claim_detail,omitempty"`
	DetailSttReverseJourney     *DetailSttReverseJourney      `json:"detail_stt_reverse_journey,omitempty"`
	IsSttCrossdocking           bool                          `json:"is_stt_crossdocking"`
	VolumeWeightDiscount        bool                          `json:"volume_weight_discount"`

	ClientPaymentMethod       string  `json:"client_payment_method"`
	ClientCodConfigAmount     string  `json:"client_cod_config_amount"`
	ClientCodShipmentDiscount float64 `json:"client_cod_shipment_discount"`
	RateVatShipment           float64 `json:"rate_vat_shipment"`
	RateVatCod                float64 `json:"rate_vat_cod"`

	CodRetailDetail   *CodConfiguration  `json:"cod_retail_data,omitempty"`
	CodConfiguration  *CodConfiguration  `json:"cod_configuration,omitempty"`
	DfodConfiguration *DfodConfiguration `json:"dfod_configuration,omitempty"`
	TotalTariffReturn float64            `json:"total_tariff_return"`

	ClientTariff *ClientTariff `json:"client_tariff,omitempty"`

	SttAttachFiles     []string            `json:"stt_attach_files,omitempty"`
	SttCommodityDetail string              `json:"stt_commodity_detail,omitempty"`
	ListDiscount       []PromoDiscount     `json:"list_discount"`
	ReverseDestination *ReverseDestination `json:"reverse_destination,omitempty"`
	SttRecipientEmail  string              `json:"stt_recipient_email,omitempty"`
	SttKtpImage        string              `json:"stt_ktp_image,omitempty"`
	SttTaxImage        string              `json:"stt_tax_image,omitempty"`
	GoodsNames         []string            `json:"goods_names,omitempty"`
	AssessmentRelabel  *AssessmentRelabel  `json:"assessment_relabel,omitempty"`
	// temporary due short development. will be move to stt model
	SttNoRefExternalAdditional string                 `json:"stt_no_ref_external_additional,omitempty"`
	PriorityTier               bool                   `json:"priority_tier"`
	SttFtzIdentityNumber       string                 `json:"stt_ftz_identity_number,omitempty"`
	SttInterTaxNumber          string                 `json:"stt_inter_tax_number,omitempty"`
	SttIdentityNumber          string                 `json:"stt_identity_number,omitempty"`
	SttCustomFlag              []SttMetaSttCustomFlag `json:"stt_custom_flag,omitempty"`
	PrioritySubscription       bool                   `json:"priority_subscription"`

	SttFtzRecipientEmail string    `json:"stt_ftz_recipient_email,omitempty"`

	PostalCodeOrigin string `json:"postal_code_origin,omitempty"`

	SttFtzAttachFiles    []string `json:"stt_ftz_attach_files,omitempty"`
	SttFtzKtpImage       string   `json:"stt_ftz_ktp_image,omitempty"`
	SttFtzTaxImage       string   `json:"stt_ftz_tax_image,omitempty"`

	// CIPL
	SttCIPL    []SttCIPL `json:"stt_cipl,omitempty"`
	SttFtzCIPL []SttCIPL `json:"stt_ftz_cipl,omitempty"`
}

type SttMetaSttCustomFlag struct {
	ScfKey   string `json:"scf_key"`
	ScfValue string `json:"scf_value"`
}

type AssessmentRelabel struct {
	RecipientAddressOld string    `json:"recipient_address_old"`
	RecipientAddressNew string    `json:"recipient_address_new"`
	RelabelAt           time.Time `json:"relabel_at"`
}

type CodConfiguration struct {
	PercentageCodFee             float64 `json:"percentage_cod_fee"`
	MinCodFee                    float64 `json:"min_cod_fee"`
	PosOriginCommissionRate      float64 `json:"pos_origin_commission_rate"`
	PosDestinationCommissionRate float64 `json:"pos_destination_commission_rate"`
	CodIsInsurance               bool    `json:"cod_is_insurance"`
	CodAmountConfigType          string  `json:"cod_amount_config_type"`
}

type DfodConfiguration struct {
	CodConfiguration
	DfodFee                  float64 `json:"dfod_fee"`
	OriginMinCommission      int     `json:"origin_min_commission"`
	DestinationMinCommission int     `json:"destination_min_commission"`
}

type ReverseDestination struct {
	Remarks                  string    `json:"remarks"`
	SttProductType           string    `json:"stt_product_type"`
	ReturnCityCode           string    `json:"return_city_code"`
	ReturnCityName           string    `json:"return_city_name"`
	ReturnDistrictCode       string    `json:"return_district_code"`
	ReturnDistrictName       string    `json:"return_district_name"`
	ReturnReceiptAddress     string    `json:"return_receipt_address"`
	ReturnReceiptAddressType string    `json:"return_receipt_address_type"`
	ReturnReceiptName        string    `json:"return_receipt_name"`
	ReturnReceiptPhone       string    `json:"return_receipt_phone"`
	SttDestinationZipCode    string    `json:"stt_destination_zip_code"`
	SttCommodityCode         string    `json:"stt_commodity_code"`
	SttGoodsStatus           string    `json:"stt_goods_status"`
	SttTaxNumber             string    `json:"stt_tax_number"`
	SttPiecePerPack          int       `json:"stt_piece_per_pack"`
	SttNextCommodity         string    `json:"stt_next_commodity"`
	IsMisbookingByInternal   bool      `json:"is_misbooking_by_internal"`
	SttFtzCIPL               []SttCIPL `json:"stt_ftz_cipl"`
	SttFtzRecipientEmail     string    `json:"stt_ftz_recipient_email"`
	SttFtzIdentityNumber     string    `json:"stt_ftz_identity_number"`
	SttFtzAttachFiles        []string  `json:"stt_ftz_attach_files"`
	SttFtzKtpImage           string    `json:"stt_ftz_ktp_image"`
	SttFtzTaxImage           string    `json:"stt_ftz_tax_image"`
}
type DetailCalculateRetailTariff struct {
	Status       string     `json:"status"`
	IsCalculated bool       `json:"is_calculated"`
	CalculatedAt *time.Time `json:"calculated_at"`
}

type OtherShipperTicketCode struct {
	TicketCode              string `json:"ticket_code"`
	Shipper                 string `json:"shipper"`
	EstimateSLA             string `json:"estimate_sla"`
	OriginCityName          string `json:"origin_city_name"`
	OriginDistrictName      string `json:"origin_district_name"`
	DestinationCityName     string `json:"destination_city_name"`
	DestinationDistrictName string `json:"destination_district_name"`
	BookedByExternalType    string `json:"booked_by_external_type"`
	BookedByExternalCode    string `json:"booked_by_external_code"`
	BookedForExternalType   string `json:"booked_for_external_type"`
	BookedForExternalCode   string `json:"booked_for_external_code"`
}

type SttCIPL struct {
	ItemDetail      string `json:"item_detail"`
	ItemDetailEn    string `json:"item_detail_en"`
	Quantity        int    `json:"quantity"`
	ItemPrice       int64  `json:"item_price"`
	CommodityHsCode string `json:"commodity_hs_code"` // hscode
}

type SttScrapViewParams struct {
	BasedFilter
	Status            string
	OriginCityID      string
	DestinationCityID string
	NoLimit           bool
	FromDate          string
	EndDate           string
	AllScrapCity      string
}
type SttScrapResult struct {
	Stt
	SttPiece
	SttPieceHistory
}

type SttCNXViewParams struct {
	BasedFilter
	HistoryStatus      string
	OriginCityIDs      []string
	OriginCityID       string
	DestinationCityIDs []string
	DestinationCityID  string
	StartDate          string
	EndDate            string
}

type SttForManifestViewParams struct {
	BasedFilter
	StartDate          string
	EndDate            string
	ClientID           int
	BookedBy           int
	BookedByType       string
	Status             string
	ProductType        string
	ProductTypes       []string
	StatusSttPosParent []string
	ListBookedBy       []int
	CustomColumns      string
	StatusIn           []string
}

type SttCNXResult struct {
	Stt
	SttPiece
	SttPieceHistory
}

// GetEstimasiSLA returns the estimated SLA for the given SttMeta.
// If the SttMeta is nil or the EstimateSLA is empty, it returns a hyphen.
func (r *SttMeta) GetEstimasiSLA() string {
	if r == nil || r.EstimateSLA == "" {
		return "-"
	}
	return r.EstimateSLA
}

func (r *SttMeta) GetSttBookedByCountry() string {
	if r.SttBookedByCountry == `` {
		return CountryID
	}
	return r.SttBookedByCountry
}

func (r *SttMeta) ToString() string {
	b, err := json.Marshal(r)
	if err != nil {
		return ``
	}
	return string(b)
}

func (c *Stt) SttMetaToStruct() *SttMeta {
	res := SttMeta{}
	err := json.Unmarshal([]byte(c.SttMeta), &res)
	if err != nil {
		return nil
	}

	return &res
}

func (c *Stt) GetSttMetaDetailReverseJourney() DetailSttReverseJourney {
	sttMeta := c.SttMetaToStruct()
	if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil {
		return *sttMeta.DetailSttReverseJourney
	}
	return DetailSttReverseJourney{}
}

func (r *SttMeta) IsEmptyDetailSttReverseJourney() bool {
	return !(r != nil && r.DetailSttReverseJourney != nil)
}

func (r *SttMeta) IsValidSttReverseJourney() bool {
	if r != nil && r.DetailSttReverseJourney != nil && IsSttStatusReturnToSender[r.DetailSttReverseJourney.ReverseJourneyStatusStt] {
		return true
	}

	return false
}

func (r *SttMeta) HasShipmentReverseJourney() bool {
	return r != nil && r.DetailSttReverseJourney != nil && r.DetailSttReverseJourney.ReverseShipmentID != ""
}

func (c *Stt) GetSttPaymentStatus() string {
	paymentStatus := c.SttPaymentStatus
	if c.SttPaymentStatus == `` {
		paymentStatus = PAID
	}
	return paymentStatus
}

func (c *Stt) GetSttElexysNoOrSttNo() string {
	if c.SttElexysNo.Valid {
		return c.SttElexysNo.Value()
	}
	return c.SttNo
}

func (c *Stt) IsSttCrossdocking() bool {
	TKPPrefix, _ := regexp.MatchString(`^TKP(01)?`, c.SttNoRefExternal)
	return TKPPrefix && c.SttNo[:2] == PrefixClient
}

func (c *Stt) IsNotSttExternalNoCrossdocking() bool {
	return c.SttNoRefExternal == `` || !(c.SttNoRefExternal != `` && strings.HasPrefix(c.SttNoRefExternal, TKP01))
}

func (c *Stt) IsSttCODRetail() bool {
	return c.SttIsCOD && c.SttBookedForType == POS && c.SttBookedByType == POS
}

type SttPerfectParams struct {
	BasedFilter
	Stt
	SttPiece
	SttPieceHistory
	SttLastStatusExclude []string
	SttHistoryStatuses   []string
	SttStartDate         time.Time
	SttEndDate           time.Time

	IsExludeWithPieceHistory bool
	UseMaster                bool
}

type SttPerfect struct {
	Stt
	SttPiece
	SttPieceHistory
}

type ClaimDetail struct {
	ClaimID         int    `json:"claim_id"`
	ExternalClaimID string `json:"external_claim_id"`
	IsAlreadyClaim  string `json:"is_already_claim"`
}
type DetailSttReverseJourney struct {
	ReverseSttNo                string `json:"reverse_stt_no"`
	ReverseShipmentID           string `json:"reverse_stt_shipment_id"`
	ReverseLastStatusStt        string `json:"reverse_stt_last_status_id"`
	ReverseJourneyStatusStt     string `json:"reverse_status_id"`
	RootReverseSttNo            string `json:"root_reverse_stt_no"`
	RootReverseShipmentID       string `json:"root_reverse_stt_shipment_id"`
	RootReverseLastStatusStt    string `json:"root_reverse_stt_last_status_id"`
	ReverseChargedPosID         int    `json:"reverse_charged_pos_id"`
	ReverseChargedConsoleID     int    `json:"reverse_charged_console_id"`
	RootReverseCodHandling      string `json:"root_reverse_cod_handling"`
	RootReverseSttNoRefExternal string `json:"root_reverse_stt_no_ref_external"`
}

type ReverseJourneyBookingActor struct {
	BookedActor            *Actor
	IsDebitToBookedFor     bool
	IsCreditToBookedFor    bool
	IsOnlyDebitTransaction bool
}

type ClientTariff struct {
	BeforeDiscount ClientTariffDetail             `json:"before_discount"`
	AfterDiscount  ClientTariffDetailWithDiscount `json:"after_discount"`
}

type ClientTariffDetail struct {
	CityRates               float64 `json:"city_rates"`
	ForwardRates            float64 `json:"forward_rates"`
	ShippingCost            float64 `json:"shipping_cost"`
	CommoditySurcharge      float64 `json:"commodity_surcharge"`
	HeavyWeightSurcharge    float64 `json:"heavy_weight_surcharge"`
	DocumentSurcharge       float64 `json:"document_surcharge"`
	InsuranceRates          float64 `json:"insurance_rates"`
	WoodpackingRates        float64 `json:"woodpacking_rates"`
	TotalTariff             float64 `json:"total_tariff"`
	TaxRates                float64 `json:"tax_rates"`
	BMTaxRate               float64 `json:"bm_tax_rate"`
	PPNTaxRate              float64 `json:"ppn_tax_rate"`
	PPHTaxRate              float64 `json:"pph_tax_rate"`
	OriginDistrictRate      float64 `json:"origin_district_rate"`
	DestinationDistrictRate float64 `json:"destination_district_rate"`
	PublishRate             float64 `json:"publish_rate"`
	ShippingSurchargeRate   float64 `json:"shipping_surcharge_rate"`
	CodAmount               float64 `json:"cod_amount"`
	CodFee                  float64 `json:"cod_fee"`
}

type ClientTariffDetailWithDiscount struct {
	ClientTariffDetail
	Discount                 float64 `json:"discount"`
	DiscountType             string  `json:"discount_type"`
	IsPromo                  bool    `json:"is_promo"`
	IsDiscountExceedMaxPromo bool    `json:"is_discount_exceed_max_promo"`
	TotalDiscount            float64 `json:"total_discount"`
}

func PopulateSttStatusDEL(val Stt) *Stt {
	return &Stt{
		SttNo:                          val.SttNo,
		SttRecipientName:               val.SttRecipientName,
		SttUpdatedAt:                   val.SttUpdatedAt,
		SttCODAmount:                   val.SttCODAmount,
		SttOriginCityID:                val.SttOriginCityID,
		SttDestinationCityID:           val.SttDestinationCityID,
		SttUpdatedActorRole:            val.SttUpdatedActorRole,
		SttProductType:                 val.SttProductType,
		SttSenderName:                  val.SttSenderName,
		SttBookedForType:               val.SttBookedForType,
		SttLastStatusID:                DEL,
		SttIsCOD:                       val.SttIsCOD,
		SttID:                          val.SttID,
		SttShipmentID:                  val.SttShipmentID,
		SttClientID:                    val.SttClientID,
		SttPosID:                       val.SttPosID,
		SttTaxNumber:                   val.SttTaxNumber,
		SttGoodsEstimatePrice:          val.SttGoodsEstimatePrice,
		SttGoodsStatus:                 val.SttGoodsStatus,
		SttTotalAmount:                 val.SttTotalAmount,
		SttNoRefExternal:               val.SttNoRefExternal,
		SttSource:                      val.SttSource,
		SttOriginDistrictID:            val.SttOriginDistrictID,
		SttDestinationDistrictID:       val.SttDestinationDistrictID,
		SttSenderPhone:                 val.SttSenderPhone,
		SttSenderAddress:               val.SttSenderAddress,
		SttRecipientAddress:            val.SttRecipientAddress,
		SttRecipientPhone:              val.SttRecipientPhone,
		SttOriginDistrictRate:          val.SttOriginDistrictRate,
		SttDestinationDistrictRate:     val.SttDestinationDistrictRate,
		SttPublishRate:                 val.SttPublishRate,
		SttShippingSurchargeRate:       val.SttShippingSurchargeRate,
		SttDocumentSurchargeRate:       val.SttDocumentSurchargeRate,
		SttCommoditySurchargeRate:      val.SttCommoditySurchargeRate,
		SttHeavyweightSurchargeRate:    val.SttHeavyweightSurchargeRate,
		SttBMTaxRate:                   val.SttBMTaxRate,
		SttPPNTaxRate:                  val.SttPPNTaxRate,
		SttPPHTaxRate:                  val.SttPPHTaxRate,
		SttGrossWeight:                 val.SttGrossWeight,
		SttVolumeWeight:                val.SttVolumeWeight,
		SttChargeableWeight:            val.SttChargeableWeight,
		SttCommodityCode:               val.SttCommodityCode,
		SttCommodityID:                 val.SttCommodityID,
		SttInsuranceType:               val.SttInsuranceType,
		SttTotalPiece:                  val.SttTotalPiece,
		SttWarningStatus:               val.SttWarningStatus,
		SttCounter:                     val.SttCounter,
		SttClientSttID:                 val.SttClientSttID,
		SttVendorSttID:                 val.SttVendorSttID,
		SttBilledTo:                    val.SttBilledTo,
		SttCODFee:                      val.SttCODFee,
		SttIsDO:                        val.SttIsCOD,
		SttMeta:                        val.SttMeta,
		SttTroubleStatus:               val.SttTroubleStatus,
		SttBookedAt:                    val.SttBookedAt,
		SttBookedName:                  val.SttBookedName,
		SttBookedByType:                val.SttBookedByType,
		SttBookedBy:                    val.SttBookedBy,
		SttCreatedAt:                   val.SttCreatedAt,
		SttCreatedName:                 val.SttCreatedName,
		SttCreatedBy:                   val.SttCreatedBy,
		SttUpdatedBy:                   val.SttUpdatedBy,
		SttUpdatedName:                 val.SttUpdatedName,
		SttUpdatedActorID:              val.SttUpdatedActorID,
		SttUpdatedActorName:            val.SttUpdatedActorName,
		ShipmentAlgoID:                 val.ShipmentAlgoID,
		SttRoute:                       val.SttRoute,
		SttDeliveryAttempt:             val.SttDeliveryAttempt,
		SttHeavyweightSurchargeRemark:  val.SttHeavyweightSurchargeRemark,
		SttNextCommodity:               val.SttNextCommodity,
		SttPiecePerPack:                val.SttPiecePerPack,
		SttElexysNo:                    val.SttElexysNo,
		SttCommodityName:               val.SttCommodityName,
		SttCommodityHsCode:             val.SttCommodityHsCode,
		SttOriginCityName:              val.SttOriginCityName,
		SttDestinationCityName:         val.SttDestinationCityName,
		SttOriginDistrictName:          val.SttOriginDistrictName,
		SttOriginDistrictUrsaCode:      val.SttOriginDistrictUrsaCode,
		SttDestinationDistrictName:     val.SttDestinationDistrictName,
		SttDestinationDistrictUrsaCode: val.SttDestinationDistrictUrsaCode,
		SttBookedByCode:                val.SttBookedByCode,
		SttBookedForID:                 val.SttBookedForID,
		SttBookedForName:               val.SttBookedForName,
		SttBookedForCode:               val.SttBookedForCode,
	}
}

func (s *Stt) GetPackageType() string {
	if s.SttIsCOD {
		return COD
	}

	return NonCOD
}

func (s *SttMeta) EstimateMaxSLA() int {
	parts := strings.Split(s.EstimateSLA, " ")

	if len(parts) < 3 {
		return 0
	}

	maxSLADaysStr := parts[2]

	maxSLADays, err := strconv.Atoi(maxSLADaysStr)
	if err != nil {
		return 0
	}

	return maxSLADays
}

func (s *Stt) GetRefNo() string {
	refNo := ``
	if s.SttShipmentID != "" {
		refNo = s.SttShipmentID
	}

	if s.SttNoRefExternal != "" {
		refNo = s.SttNoRefExternal
	}

	return refNo
}

func (c *Stt) EncryptFields() {
	c.SttSenderPhoneEncrypted = crypto.NewEncrypted(c.SttSenderPhone)
	c.SttRecipientPhoneEncrypted = crypto.NewEncrypted(c.SttRecipientPhone)
}

func (c *Stt) DecryptFields() {
	crypto.MappingDecrypted(c.SttSenderPhoneEncrypted, &c.SttSenderPhone)
	crypto.MappingDecrypted(c.SttRecipientPhoneEncrypted, &c.SttRecipientPhone)
}

type SttSelectDetailParams struct {
	SttID         int
	SttNo         string
	IsWithBag     bool
	IsNeedDecrypt bool

	Search          string
	SearchByPattern bool
}

func (c *Stt) GetSttNeedRelabel() bool {
	if c.SttMeta == `` {
		return false
	}
	sttMeta := c.SttMetaToStruct()
	if sttMeta != nil && sttMeta.AssessmentRelabel != nil {
		return true
	}
	return false
}

func (c *Stt) GetSttRelabel() *AssessmentRelabel {
	if c.SttMeta == `` {
		return nil
	}
	sttMeta := c.SttMetaToStruct()
	if sttMeta != nil && sttMeta.AssessmentRelabel != nil {
		return sttMeta.AssessmentRelabel
	}
	return nil
}

func (s *Stt) IsSttRetailByPrefixSttNo() bool {
	// stt origin
	isRetail := IsPrefixSttRetail[s.SttNo[:2]]
	if isRetail {
		return isRetail // do not need check reverse journey
	}

	// sttMeta := s.SttMetaToStruct()
	// if sttMeta != nil && sttMeta.DetailSttReverseJourney != nil {
	// 	isRetail = IsPrefixSttRetail[sttMeta.DetailSttReverseJourney.RootReverseSttNo[:2]]
	// }

	return isRetail
}

func (s *SttMeta) GetIsPriorityTier() bool {
	if s == nil {
		return false
	}
	return s.PriorityTier
}

func (s *SttMeta) GetIsPrioritySubscription() bool {
	if s == nil {
		return false
	}
	return s.PrioritySubscription
}

type SttRepair struct {
	Stt
	SttIsWoodPacking bool `json:"stt_is_woodpacking"`
	IsSttManual      bool `json:"is_stt_manual"`
}

type GetTrackingTokopediaResponse struct {
	Detail  DetailTrackingTokopedia          `json:"detail"`
	Awb     string                           `json:"awb"`
	History []TrackingEventTrackingTokopedia `json:"history"`
}
type DetailTrackingTokopedia struct {
	Sender        PersonOfInterestTrackingTokopedia `json:"sender"`
	Driver        DriverTrackingTokopedia           `json:"driver"`
	Receiver      PersonOfInterestTrackingTokopedia `json:"receiver"`
	FinalStatus   string                            `json:"final_status"`
	ShippedDate   string                            `json:"shipped_date,omitempty"`
	ActualAmount  int                               `json:"actual_amount"`
	Weight        int                               `json:"weight"`
	ServiceCode   string                            `json:"service_code"`
	DeliveredDate string                            `json:"delivered_date,omitempty"`
}

type PersonOfInterestTrackingTokopedia struct {
	Zipcode      string `json:"zipcode"`
	Name         string `json:"name"`
	Addr         string `json:"addr"`
	DistrictName string `json:"district_name,omitempty"`
	Geoloc       string `json:"geoloc,omitempty"`
}

type TrackingEventTrackingTokopedia struct {
	Receiver            string                   `json:"receiver,omitempty"`
	DateTime            string                   `json:"date_time"`
	Status              string                   `json:"status"`
	StatusCode          string                   `json:"status_code"`
	HubName             string                   `json:"hub_name"`
	AgentName           string                   `json:"agent_name"`
	HubCityName         string                   `json:"hub_city_name,omitempty"`
	DistrictName        string                   `json:"district_name,omitempty"`
	Note                string                   `json:"note"`
	ProblemReasonCode   string                   `json:"problem_reason_code,omitempty"`
	RescheduleTimeStart string                   `json:"reschedule_time_start,omitempty"`
	RescheduleTimeEnd   string                   `json:"reschedule_time_end,omitempty"`
	Driver              *DriverTrackingTokopedia `json:"driver,omitempty"`
	Proof               *ProofTrackingTokopedia  `json:"proof,omitempty"`
	Journey             string                   `json:"journey,omitempty"`
}

type DriverTrackingTokopedia struct {
	Name         string  `json:"name,omitempty"`
	Phone        string  `json:"phone,omitempty"`
	ID           string  `json:"id,omitempty"`
	CurrentLat   float64 `json:"current_lat,omitempty"`
	CurrentLong  float64 `json:"current_long,omitempty"`
	Photo        string  `json:"photo,omitempty"`
	LicensePlate string  `json:"license_plate,omitempty"`
}

type ProofTrackingTokopedia struct {
	Name         string  `json:"name"`
	Relationship string  `json:"relationship"`
	Photo        string  `json:"photo"`
	Latitude     float64 `json:"latitude"`
	Longitude    float64 `json:"longitude"`
}
