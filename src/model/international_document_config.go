package model

type InternationalDocumentData struct {
	IdcID                           int      `json:"idc_id"`
	IdcCountryID                    int      `json:"idc_country_id"`
	IdcIsOtherCommodity             bool     `json:"idc_is_other_commodity"`
	IdcIsReceiverEmail              bool     `json:"idc_is_receiver_email"`
	IdcIsBeforeAndAfterPackingImage bool     `json:"idc_is_before_and_after_packing_image"`
	IdcIsKtpImage                   bool     `json:"idc_is_ktp_image"`
	IdcIsNpwpImage                  bool     `json:"idc_is_npwp_image"`
	IdcStatus                       string   `json:"idc_status"`
	IdcIsIdentityNumber             bool     `json:"idc_is_identity_number"`
	IdcIsTaxIdentificationNumber    bool     `json:"idc_is_tax_identification_number"`
	Country                         *Country `json:"country"`
}

type InternationalDocumentConfig struct {
	Data *InternationalDocumentData `json:"data"`
}

type GetInternationalDocumentParams struct {
	OriginCityID      int
	DestinationCityID int
	Token             string
}
