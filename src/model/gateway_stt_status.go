package model

import (
	"context"
	"strings"
	"time"
)

const (
	AlgoPartnerPOS        = `POS`
	AlgoPartnerCONSOLE    = `Consolidator`
	AlgoPartnerSUBCONSOLE = `Sub-Consolidator`
)

var (
	// IsStatusIncomingDeliveryProcess ...
	IsStatusIncomingDeliveryProcess = map[string]bool{
		STIDEST:   true,
		STIDESTSC: true,
		HND:       true,
		HAL:       true,
		DEX:       true,
		RTS:       true,
		ODA:       true,
	}

	IsVendorNameValid = map[string]bool{
		NinjaName:     true,
		JNEName:       true,
		LuwjistikName: true,
		PTPOSName:     true,
	}

	GetVendorName = map[string]string{
		NinjaName:     AccountNinja.ActorName,
		JNEName:       AccountJNE.ActorName,
		LuwjistikName: AccountLuwjistik.ActorName,
		PTPOSName:     AccountPTPOS.ActorName,
	}
)

type SttPieceForClient struct {
	PieceID           int64   `json:"piece_id"`
	PieceLength       float64 `json:"piece_length"`
	PieceWidth        float64 `json:"piece_width"`
	PieceHeight       float64 `json:"piece_height"`
	PieceGrossWeight  float64 `json:"piece_gross_weight"`
	PieceVolumeWeight float64 `json:"piece_volume_weight"`
	SttID             int64   `json:"-"` // (exlude)
	LastStatusID      string  `json:"-"` // (exlude)
}

type UpdateSttStatus struct {
	SttID                 int64                      `json:"stt_id,omitempty"`
	SttNo                 string                     `json:"stt_no"`
	Datetime              time.Time                  `json:"datetime"`
	StatusCode            string                     `json:"status_code"`
	CurrentStatus         string                     `json:"current_status"`
	Location              string                     `json:"location"`
	City                  string                     `json:"city"`
	Remarks               string                     `json:"remarks"`
	Partner               UpdateSttStatusPartnerData `json:"partner"`
	UpdatedBy             string                     `json:"updated_by"`
	UpdatedOn             time.Time                  `json:"updated_on"`
	IsDfod                bool                       `json:"is_dfod,omitempty"`
	AgentName             string                     `json:"agent_name"`
	HubID                 int                        `json:"hub_id,omitempty"`
	HubName               string                     `json:"hub_name,omitempty"`
	ReasonCode            string                     `json:"reason_code,omitempty"`
	ReasonTitle           string                     `json:"reason_title"`
	DriverSource          DriverSource               `json:"driver_source"`
	PodRecipientRelation  SttStatusRecipientRelation `json:"pod_recipient_relation"`
	UrlPictureSigned      []string                   `json:"url_picture_signed"`
	Attachments           []string                   `json:"attachments"`
	RefSttNo              string                     `json:"ref_stt_no"`
	SttJourneyType        string                     `json:"stt_journey_type"`
	ShipmentID            string                     `json:"shipment_id"`
	Sender                UpdateSttStatusSender      `json:"sender"`
	IsCod                 bool                       `json:"is_cod,omitempty"`
	Reason                string                     `json:"reason"`
	CourierName           string                     `json:"courier_name"`
	CourierPhone          string                     `json:"courier_phone"`
	LastStatusPartnerType string                     `json:"last_status_partner_type"`
	LastStatusPartnerLoc  string                     `json:"last_status_partner_loc"`
	LastStatusPartnerName string                     `json:"last_status_partner_name"`
	LastStatusPartnerCode string                     `json:"last_status_partner_code"`
	IsInsurance           bool                       `json:"is_insurance"`
	TotalTariff           float64                    `json:"total_tariff"`
	InsuranceType         string                     `json:"insurance_type"`
	InsuranceRate         float64                    `json:"insurance_rate"`
	PiecesDetails         []SttPieceForClient        `json:"pieces_details"`
	NextSttNo             string                     `json:"next_stt_no"` // to hold new reverse journey number
	ListSystemStatus      []SystemStatus             `json:"list_system_status"`
	ClaimNo               string                     `json:"claim_no"`
}

type UpdateSttStatusSender struct {
	Name    string `json:"name"`
	Address string `json:"address"`
	Phone   string `json:"phone"`
}

type DriverSource struct {
	Id       int    `json:"id"`
	Source   string `json:"source"`
	Phone    string `json:"phone"`
	Name     string `json:"name"`
	AssignBy string `json:"assignBy"`
}

type SttStatusRecipientRelation struct {
	RelationId string `json:"relation_id"`
	Relation   string `json:"relation"`
}

type UpdateSttStatusPartnerData struct {
	ID                          int    `json:"partner_id"`
	PartnerExternalCode         string `json:"partner_external_code"`
	PartnerPhoneNumber          string `json:"partner_phone_number"`
	Address                     string `json:"address"`
	Lat                         string `json:"lat"`
	Long                        string `json:"long"`
	PartnerLocationDistrictName string `json:"partner_location_district_name"`
	PartnerCode                 string `json:"partner_code"`
	PartnerName                 string `json:"partner_name"`
}

func GenerateUpdateSttStatusPartnerData(partner Partner) UpdateSttStatusPartnerData {
	return UpdateSttStatusPartnerData{
		ID:                  partner.Data.ID,
		PartnerExternalCode: partner.Data.PartnerExternalCode,
		PartnerPhoneNumber:  partner.Data.PhoneNumber,
		PartnerCode:         partner.Data.Code,
		PartnerName:         partner.Data.Name,
		Address:             partner.Data.Address,
		Lat:                 partner.Data.Lat,
		Long:                partner.Data.Long,
		PartnerLocationDistrictName: func() string {
			if partner.Data.PartnerLocation != nil && partner.Data.PartnerLocation.District != nil {
				return partner.Data.PartnerLocation.District.Name
			}
			return ""
		}(),
	}
}

type UpdateSttStatusWithExtendForMiddleware struct {
	*UpdateSttStatus
	TypeStruct       string         `json:"type_struct"`
	ServiceType      string         `json:"service_type"`
	Product          string         `json:"product"`
	Pieces           int            `json:"pieces"`
	GrossWeight      float64        `json:"gross_weight"`
	VolumeWeight     float64        `json:"volume_weight"`
	ChargeableWeight float64        `json:"chargeable_weight"`
	UrlPicture       string         `json:"url_picture"`
	IsWithProofFile  bool           `json:"is_with_proof_file"`
	BookedForType    string         `json:"booked_for_type"`
	TotalCODAmount   float64        `json:"total_cod_amount"`
	ReasonCode       string         `json:"reason_code,omitempty"`
	Reason           string         `json:"reason"`
	Latitude         float64        `json:"latitude"`
	Longitude        float64        `json:"longitude"`
	Attempt          int            `json:"attempt"`
	ListSystemStatus []SystemStatus `json:"list_system_status"`
}

type SystemStatus struct {
	*UpdateSttStatus
	ServiceType      string  `json:"service_type"`
	Product          string  `json:"product"`
	Pieces           int     `json:"pieces"`
	GrossWeight      float64 `json:"gross_weight"`
	VolumeWeight     float64 `json:"volume_weight"`
	ChargeableWeight float64 `json:"chargeable_weight"`
	UrlPicture       string  `json:"url_picture"`
	IsWithProofFile  bool    `json:"is_with_proof_file"`
	BookedForType    string  `json:"booked_for_type"`
	TotalCODAmount   float64 `json:"total_cod_amount"`
	ReasonCode       string  `json:"reason_code"`
	Reason           string  `json:"reason"`
	Latitude         float64 `json:"latitude"`
	Longitude        float64 `json:"longitude"`
	Attempt          int     `json:"attempt"`
}

type GoberCodCommission struct {
	SttNo                     string  `json:"stt_no"`
	SttStatus                 string  `json:"stt_status"`
	SttAmountCod              float64 `json:"stt_amount_cod"`
	SttShipmentID             string  `json:"stt_shipment_id"`
	SttBookedBy               int     `json:"stt_booked_by"`
	SttBookedType             string  `json:"stt_booked_type"`
	SttBookedForID            int     `json:"stt_booked_for_id"`
	SttBookedForType          string  `json:"stt_booked_for_type"`
	SttIsCod                  bool    `json:"stt_is_cod"`
	SttActorPodBy             int     `json:"stt_actor_pod_by"`
	SttActorPodType           string  `json:"stt_actor_pod_type"`
	IsCodSettlement           bool    `json:"is_cod_settlement"`
	CodSettlement             float64 `json:"cod_settlement"`
	VatShipment               float64 `json:"vat_shipment"`
	VatCod                    float64 `json:"vat_cod"`
	GoodsEstimatePrice        float64 `json:"good_estimate_price"`
	CodDiscountPosOrigin      float64 `json:"cod_discount_pos_origin"`
	CodDiscountPosDestination float64 `json:"cod_discount_pos_destination"`
	IsSttReverseJourney       bool    `json:"is_stt_reverse_journey"`
	SttReverseNo              string  `json:"stt_reverse_no"`
	SttRootReverseNo          string  `json:"stt_root_reverse_no"`
	CodHandling               string  `json:"cod_handling"`
	SttOriginPosID            int     `json:"stt_origin_pos_id"`
	SttIsDfod                 bool    `json:"stt_is_dfod"`
	SttInsuranceRate          float64 `json:"stt_insurace_rate,omitempty"`
	PaymentMethod             string  `json:"payment_method"`
	IsSttCodRetail            bool    `json:"-"`

	SttCustomFlag []GoberCodCommissionSttCustomFlag `json:"stt_custom_flag"`
}

type GoberCodCommissionSttCustomFlag struct {
	ScfKey   string `json:"scf_key"`
	ScfValue string `json:"scf_value"`
}

type GoberUpdateCODPaymentMethod struct {
	SttNo                     string  `json:"stt_no"`
	SttStatus                 string  `json:"stt_status"`
	SttAmountCod              float64 `json:"stt_amount_cod"`
	SttShipmentID             string  `json:"stt_shipment_id"`
	SttBookedBy               int     `json:"stt_booked_by"`
	SttBookedType             string  `json:"stt_booked_type"`
	SttBookedForID            int     `json:"stt_booked_for_id"`
	SttBookedForType          string  `json:"stt_booked_for_type"`
	SttIsCod                  bool    `json:"stt_is_cod"`
	SttActorPodBy             int     `json:"stt_actor_pod_by"`
	SttActorPodType           string  `json:"stt_actor_pod_type"`
	IsCodSettlement           bool    `json:"is_cod_settlement"`
	CodSettlement             float64 `json:"cod_settlement"`
	VatShipment               float64 `json:"vat_shipment"`
	VatCod                    float64 `json:"vat_cod"`
	GoodsEstimatePrice        float64 `json:"good_estimate_price"`
	CodDiscountPosOrigin      float64 `json:"cod_discount_pos_origin"`
	CodDiscountPosDestination float64 `json:"cod_discount_pos_destination"`
	IsSttReverseJourney       bool    `json:"is_stt_reverse_journey"`
	SttReverseNo              string  `json:"stt_reverse_no"`
	CodHandling               string  `json:"cod_handling"`
	SttOriginPosID            int     `json:"stt_origin_pos_id"`
	SttIsDfod                 bool    `json:"stt_is_dfod"`
	SttInsuranceRate          float64 `json:"stt_insurace_rate,omitempty"`
	PaymentMethod             string  `json:"payment_method"`
	IsSttCodRetail            bool    `json:"-"`
}

type BuildGoberCodCommission func(context.Context, GoberCodCommission) (GoberCodCommission, error)

type GoberDTPOLCommission struct {
	SttNo                    string    `json:"stt_no"`
	UpdatedAtLocalTime       time.Time `json:"updated_at_local_time"`
	UpdatedAtWIB             time.Time `json:"updated_at_wib"`
	SttLastStatus            string    `json:"stt_last_status"`
	IsCargoPlane             bool      `json:"is_cargo_plane"`
	IsSttCodRetail           bool      `json:"is_stt_cod_retail"`
	IsSttDfod                bool      `json:"is_stt_dfod"`
	CodDiscountAmount        float64   `json:"cod_discount_amount"`
	IsSttCodShipmentFavorite bool      `json:"is_stt_cod_shipment_favorite"`
	IsReverseShipmentFavCOD  bool      `json:"is_stt_reverse_cod_shipment_favorite"`
	IsReverseShipmentFavDFOD bool      `json:"is_stt_reverse_dfod_shipment_favorite"`
}

type GoberRetryDTPOLCommission struct {
	SttNo string `json:"stt_no"`
	Token string
}

type PublishCreateDexAssessment struct {
	Stt                  Stt
	DeliveryID           int          `json:"delivery_id"`
	Token                string       `json:"token"`
	DaSourceDex          string       `json:"da_source_dex"`
	GeolocationPODDEX    string       `json:"geolocation_pod_dex"`
	AddressPODDEX        string       `json:"address_pod_dex"`
	CaptainCourierName   string       `json:"captain_courier_name"`
	CaptainCourierPhone  string       `json:"captain_courier_phone"`
	ReasonCode           string       `json:"reason_code"`
	DaDeliveryDexAttempt int          `json:"da_delivery_dex_attempt"`
	DaDeliveryDexDate    time.Time    `json:"da_delivery_dex_date"`
	Attachment           string       `json:"attachment"`
	RebuttalDex          *RebuttalDex `json:"rebuttal_dex"`
	VendorName           string       `json:"vendor_name"`
}

type GoberDTPOLCommissionDetailData struct {
	SttDetail       SttDetailResult
	SttHistory      SttPieceHistory
	CityDestination City
}

// SetBackwardTime subtracts one minute from the datetime and updated_on time fields of the list system status,
// but only if the status code is not the same as the current status.
func (c *UpdateSttStatusWithExtendForMiddleware) SetBackwardTime() {
	if c.ListSystemStatus == nil {
		c.ListSystemStatus = make([]SystemStatus, 0)
	}

	for index := range c.ListSystemStatus {
		if c.CurrentStatus == c.ListSystemStatus[index].StatusCode {
			continue
		}

		if !c.ListSystemStatus[index].Datetime.IsZero() {
			// subtract one minute from the datetime
			c.ListSystemStatus[index].Datetime = c.ListSystemStatus[index].Datetime.Add(-1 * time.Minute)
		}
		if !c.ListSystemStatus[index].UpdatedOn.IsZero() {
			// subtract one minute from the updated_on
			c.ListSystemStatus[index].UpdatedOn = c.ListSystemStatus[index].UpdatedOn.Add(-1 * time.Minute)
		}
	}
}

// CheckEmptyAndValid checks if the current status is empty and fills it with the status code.
// This function is used to normalize the data before sending it to the gateway.
func (c *UpdateSttStatusWithExtendForMiddleware) CheckEmptyAndValid() {
	// If the current status is empty, fill it with the status code.
	if c.CurrentStatus == "" {
		c.CurrentStatus = c.StatusCode
	}

	// If the status code is empty, fill it with the current status.
	if c.StatusCode == "" {
		c.StatusCode = c.CurrentStatus
	}

	// Iterate through the list of system status and fill the empty fields.
	for i := range c.ListSystemStatus {
		// If the current status is empty, fill it with the status code.
		if c.ListSystemStatus[i].CurrentStatus == "" {
			c.ListSystemStatus[i].CurrentStatus = c.ListSystemStatus[i].StatusCode
		}

		// If the status code is empty, fill it with the current status.
		if c.ListSystemStatus[i].StatusCode == "" {
			c.ListSystemStatus[i].StatusCode = c.ListSystemStatus[i].CurrentStatus
		}

		// If the shipment ID is empty, fill it with the shipment ID from the parent object.
		if c.ListSystemStatus[i].ShipmentID == "" {
			c.ListSystemStatus[i].ShipmentID = c.ShipmentID
		}

		// If the reference STT number is empty, fill it with the reference STT number from the parent object.
		if c.ListSystemStatus[i].RefSttNo == "" {
			c.ListSystemStatus[i].RefSttNo = c.RefSttNo
		}

		// If the STT journey type is empty, fill it with the STT journey type from the parent object.
		if c.ListSystemStatus[i].SttJourneyType == "" {
			c.ListSystemStatus[i].SttJourneyType = c.SttJourneyType
		}

		// Check data format attacment string to array string
		if len(c.ListSystemStatus[i].Attachments) == 1 {
			c.ListSystemStatus[i].Attachments = strings.Split(c.ListSystemStatus[i].Attachments[0], ",")
		}
		if len(c.ListSystemStatus[i].UrlPictureSigned) == 1 {
			c.ListSystemStatus[i].UrlPictureSigned = strings.Split(c.ListSystemStatus[i].UrlPictureSigned[0], ",")
		}
	}
}
