package model

import (
	"database/sql"
	"fmt"
	"strings"
	"time"
)

const (
	DispatchCodeFormat = "020106"
	DispatchCodeName   = "DIS"
)

type (
	Dispatch struct {
		DispatchID                  int64          `json:"dispatch_id" db:"dispatch_id"`
		DispatchNo                  string         `json:"dispatch_no" db:"dispatch_no"`
		DispatchStts                int            `json:"dispatch_stts" db:"dispatch_stts"`
		DispatchSttPieces           int            `json:"dispatch_stt_pieces" db:"dispatch_stt_pieces"`
		DispatchSttGrossWeights     float64        `json:"dispatch_stt_gross_weights" db:"dispatch_stt_gross_weights"`
		DispatchCreatedAt           time.Time      `json:"dispatch_created_at" db:"dispatch_created_at"`
		DispatchCreatedByID         int64          `json:"dispatch_created_by_id" db:"dispatch_created_by_id"`
		DispatchCreatedByName       string         `json:"dispatch_created_by_name" db:"dispatch_created_by_name"`
		DispatchCreatedByRole       string         `json:"dispatch_created_by_role" db:"dispatch_created_by_role"`
		DispatchMeta                sql.NullString `json:"dispatch_meta" db:"dispatch_meta"` // nullable field
		EligibleArchivedType        bool           `json:"eligible_archived_type" db:"eligible_archived_type"`
		DispatchPartnerId           int64          `json:"dispatch_partner_id" db:"dispatch_partner_id"`
		DispatchPartnerCode         string         `json:"dispatch_partner_code" db:"dispatch_partner_code"`
		DispatchPartnerName         string         `json:"dispatch_partner_name" db:"dispatch_partner_name"`
		DispatchDestinationCityId   string         `json:"dispatch_destination_city_id" db:"dispatch_destination_city_id"`
		DispatchDestinationCityName string         `json:"dispatch_destination_city_name" db:"dispatch_destination_city_name"`
	}

	DispatchDetail struct {
		DDID                      int64     `db:"dd_id" json:"dd_id"`
		DDDispatchID              int64     `db:"dd_dispatch_id" json:"dd_dispatch_id"`
		DDSttNo                   string    `db:"dd_stt_no" json:"dd_stt_no"`
		DDProductType             string    `db:"dd_product_type" json:"dd_product_type"`
		DDDestinationCityID       string    `db:"dd_destination_city_id" json:"dd_destination_city_id"`
		DDDestinationDistrictID   string    `db:"dd_destination_district_id" json:"dd_destination_district_id"`
		DDDestinationDistrictName string    `db:"dd_destination_district_name" json:"dd_destination_district_name"`
		DDDestinationURSAcode     string    `db:"dd_destination_ursa_code" json:"dd_destination_ursa_code"`
		DDRefNo                   string    `db:"dd_ref_no" json:"dd_ref_no"`
		DDDemNo                   string    `db:"dd_dem_no" json:"dd_dem_no"`
		DDPieces                  int       `db:"dd_pieces" json:"dd_pieces"`
		DDGrossWeight             float64   `db:"dd_gross_weight" json:"dd_gross_weight"`
		DDRecipientName           string    `db:"dd_recipient_name" json:"dd_recipient_name"`
		DDRecipientPhone          string    `db:"dd_recipient_phone" json:"dd_recipient_phone"`
		DDRecipientAddress        string    `db:"dd_recipient_address" json:"dd_recipient_address"`
		DDLastStatus              string    `db:"dd_last_status" json:"dd_last_status"`
		DDScanStatus              string    `db:"dd_scan_status" json:"dd_scan_status"`
		DDMeta                    string    `db:"dd_meta" json:"dd_meta"`
		DDCreatedAt               time.Time `db:"dd_created_at" json:"dd_created_at"`
		EligibleArchivedType      int8      `db:"eligible_archived_type" json:"eligible_archived_type"`
		DestinationCityName       string    `json:"-" db:"-"`
	}

	DispatchTemporary struct {
		ID                      int64         `json:"dt_id" db:"dt_id"`
		AccountID               int64         `json:"dt_account_id" db:"dt_account_id"`
		IsActive                bool          `json:"dt_is_active" db:"dt_is_active"`
		STTNo                   string        `json:"dt_stt_no" db:"dt_stt_no"`
		ProductType             string        `json:"dt_product_type" db:"dt_product_type"`
		DestinationCityID       string        `json:"dt_destination_city_id" db:"dt_destination_city_id"`
		DestinationCityName     string        `json:"dt_destination_city_name" db:"dt_destination_city_name"`
		DestinationDistrictID   string        `json:"dt_destination_district_id" db:"dt_destination_district_id"`
		DestinationDistrictName string        `json:"dt_destination_district_name" db:"dt_destination_district_name"`
		DestinationURSAcode     string        `json:"dt_destination_ursa_code" db:"dt_destination_ursa_code"`
		ScanNo                  string        `json:"dt_scan_no" db:"dt_scan_no"`
		RefNo                   string        `json:"dt_ref_no" db:"dt_ref_no"`
		DemNo                   string        `json:"dt_dem_no" db:"dt_dem_no"`
		Pieces                  int           `json:"dt_pieces" db:"dt_pieces"`
		GrossWeight             float64       `json:"dt_gross_weight" db:"dt_gross_weight"`
		LastStatus              string        `json:"dt_last_status" db:"dt_last_status"`
		RecipientName           string        `json:"dt_recipient_name" db:"dt_recipient_name"`
		RecipientPhone          string        `json:"dt_recipient_phone" db:"dt_recipient_phone"`
		RecipientAddress        string        `json:"dt_recipient_address" db:"dt_recipient_address"`
		ScanStatus              string        `json:"dt_scan_status" db:"dt_scan_status"`
		DemStatus               string        `json:"dt_dem_status" db:"dt_dem_status"`
		HubID                   int64         `json:"dt_hub_id" db:"dt_hub_id"`
		Meta                    string        `json:"dt_meta" db:"dt_meta"`
		CreatedAt               time.Time     `json:"dt_created_at" db:"dt_created_at"`
		CreatedBy               int64         `json:"dt_created_by" db:"dt_created_by"`
		UpdatedAt               sql.NullTime  `json:"dt_updated_at,omitempty" db:"dt_updated_at"`
		UpdatedBy               sql.NullInt64 `json:"dt_updated_by,omitempty" db:"dt_updated_by"`
		EligibleArchivedType    int8          `json:"eligible_archived_type" db:"eligible_archived_type"`
	}

	DispatchWithDetails struct {
		Dispatch
		DispatchDetails []DispatchDetail `json:"dispatch_details" db:"-"`
	}

	GetDispatchTemporaryParams struct {
		DispatchTaskId int64 `json:"dispatch_task_id" db:"-"`
		AccountID      int64 `json:"account_id" db:"-"`
		HubID          int64 `json:"hub_id" db:"-"`
		IsActive       *bool `json:"is_active" db:"-"`
	}

	GetDispatchParams struct {
		DispatchId int64 `json:"dispatch_id" db:"-"`
	}

	UpdateDispatchTemporaryParams struct {
		IsActive       bool          `json:"is_active" db:"-"`
		UpdatedBy      sql.NullInt64 `json:"updated_by" db:"-"`
		UpdatedAt      sql.NullTime  `json:"updated_at" db:"-"`
		DispatchTempId []int64       `json:"dispatch_temp_id" db:"-"`
	}

	CreateNewDispatchRequest struct {
		AccountId       int64   `json:"account_id" db:"-"`
		IsActive        bool    `json:"is_active" db:"-"`
		DispatchTempIds []int64 `json:"dispatch_temp_ids" db:"-"`
		DispatchWithDetails
	}

	GetDispatchTemporaryByAccountSttNo struct {
		SttNos    []string
		AccountID int64
		HubID     int64
	}
)

func (d *Dispatch) SetCode() {
	// Date format for dispatch code -> (DDMMYY)+(7 last digit from unix nano)
	// Example: *************
	unixTime := fmt.Sprintf("%d", time.Now().UnixNano())
	d.DispatchNo = fmt.Sprintf("%s%s%s", DispatchCodeName, time.Now().Format(DispatchCodeFormat), unixTime[len(unixTime)-7:])
}

func (d *Dispatch) TableName() string {
	return "dispatch"
}

func (d *DispatchDetail) TableName() string {
	return "dispatch_detail"
}

func (d *DispatchTemporary) TableName() string {
	return "dispatch_temporary"
}

func (d *Dispatch) SetCreatedById(id int64) *Dispatch {
	d.DispatchCreatedByID = id
	return d
}

func (d *Dispatch) SetCreatedByName(name string) *Dispatch {
	d.DispatchCreatedByName = name
	return d
}

func (d *Dispatch) SetCreatedByRole(role string) *Dispatch {
	d.DispatchCreatedByRole = role
	return d
}

func (d *Dispatch) SetPartnerDestinationCity(cityCode, cityName string) *Dispatch {
	d.DispatchDestinationCityId = cityCode
	d.DispatchDestinationCityName = cityName
	return d
}

func (d *Dispatch) SetPartner(id int64, code, name string) *Dispatch {
	d.DispatchPartnerId = id
	d.DispatchPartnerCode = code
	d.DispatchPartnerName = name

	return d
}

func (d *Dispatch) ResumeDispatch(details []DispatchDetail) *Dispatch {
	var (
		metas  []string
		sttNos = make(map[string]int)
	)
	for _, detail := range details {
		metas = append(metas, detail.DDMeta)
		d.EligibleArchivedType = detail.EligibleArchivedType == 1
		d.DispatchDestinationCityId = detail.DDDestinationCityID
		d.DispatchDestinationCityName = detail.DestinationCityName
		if strings.ToUpper(detail.DDScanStatus) == SUCCESS {
			sttNos[detail.DDSttNo] += 1
			d.DispatchSttPieces += detail.DDPieces
			d.DispatchSttGrossWeights += detail.DDGrossWeight
		}
	}

	d.DispatchStts += len(sttNos)
	d.DispatchCreatedAt = time.Now()
	return d
}

func (d *DispatchTemporary) ToDispatchDetail() *DispatchDetail {
	return &DispatchDetail{
		DDSttNo:                   d.STTNo,
		DDProductType:             d.ProductType,
		DDDestinationCityID:       d.DestinationCityID,
		DDDestinationDistrictID:   d.DestinationDistrictID,
		DDDestinationDistrictName: d.DestinationDistrictName,
		DDDestinationURSAcode:     d.DestinationURSAcode,
		DDRefNo:                   d.RefNo,
		DDDemNo:                   d.DemNo,
		DDPieces:                  d.Pieces,
		DDGrossWeight:             d.GrossWeight,
		DDRecipientName:           d.RecipientName,
		DDRecipientPhone:          d.RecipientPhone,
		DDRecipientAddress:        d.RecipientAddress,
		DDLastStatus:              d.LastStatus,
		DDScanStatus:              d.ScanStatus,
		DDMeta:                    d.Meta,
		DDCreatedAt:               d.CreatedAt,
		EligibleArchivedType:      d.EligibleArchivedType,
		DestinationCityName:       d.DestinationCityName,
	}
}
