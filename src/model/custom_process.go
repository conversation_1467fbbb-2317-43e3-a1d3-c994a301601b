package model

import "time"

const (
	MAX_STT_INPUTED_CUSTOM_PROCESS      = 50
	MIN_STT_INPUTED_CUSTOM_PROCESS      = 1
	MAX_CLAIM_NO_INPUTED_CUSTOM_PROCESS = 100
	RTSHQ_DEFAULT_NAME                  = "Head Office"
	RTSHQ_DEFAULT_ADDRESS               = "JALAN AGAVE RAYA NO 55, KEDOYA SELATAN, JAKARTA BARAT, 11520"
	RTSHQ_DEFAULT_PHONE_NUMBER          = "***********"
)

var (
	IsAllowedAccountTypeCustomProcess = map[string]bool{
		CONSOLE:    true,
		SUBCONSOLE: true,
	}

	IsPublishStatusCustomProcess = map[string]bool{
		HAL:         true,
		HALCD:       true,
		CNXCD:       true,
		SCRAP:       true,
		MISSING:     true,
		DAMAGE:      true,
		NOTRECEIVED: true,
		RTS:         true,
		REJECTED:    true,
		CLAIM:       true,
		RTSHQ:       true,
		ODA:         true,
		CI:          true,
		OCC:         true,
		REROUTE:     true,
		MISBOOKING:  true,
		SCRAPCD:     true,
		INHUB:       true,
		OUTHUB:      true,
	}

	IsEligibleSttUnpaidUpdateCustomProcess = map[string]bool{
		RTS:   true,
		RTSHQ: true,
		HAL:   true,
	}
	IsSttReturnForRtsRtshq = map[string]bool{
		RTS:     true,
		RTSHQ:   true,
		REROUTE: true,
	}

	IsValidReverseJourneyTracking = map[string]bool{
		RTS:     true,
		RTSHQ:   true,
		REROUTE: true,
		CNX:     true,
	}

	IsSttAllowEditCustomProcess = map[string]bool{
		RTS:     true,
		REROUTE: true,
	}
	IsCustomProcessStatusValidReleaseCommission = map[string]bool{
		CI:      true,
		RTS:     true,
		RTSHQ:   true,
		REROUTE: true,
		CLAIM:   true,
	}

	IsCustomProcessStatusReleaseBookingCommission = map[string]bool{
		CI:    true,
		CLAIM: true,
		RTS:   true,
		RTSHQ: true,
	}

	IsSttAllowEditProductTypeCustomProcess = map[string]bool{
		RTS:     true,
		REROUTE: true,
		RTSHQ:   true,
		CNX:     true,
	}
)

type CustomProcess struct {
	CustomProcessID                    int       `json:"custom_process_id" db:"custom_process_id"`
	CustomProcessTotalSTT              int       `json:"custom_process_total_stt" db:"custom_process_total_stt"`
	CustomProcessTotalPiece            int       `json:"custom_process_total_piece" db:"custom_process_total_piece"`
	CustomProcessLatestStatus          string    `json:"custom_process_latest_status" db:"custom_process_latest_status"`
	CustomProcessTotalGrossWeight      float64   `json:"custom_process_total_gross_weight" db:"custom_process_total_gross_weight"`
	CustomProcessTotalVolumeWeight     float64   `json:"custom_process_total_volume_weight" db:"custom_process_total_volume_weight"`
	CustomProcessTotalChargeableWeight float64   `json:"custom_process_total_chargeable_weight" db:"custom_process_total_chargeable_weight"`
	CustomProcessCreatedAt             time.Time `json:"custom_process_created_at" db:"custom_process_created_at"`
	CustomProcessCreatedBy             int       `json:"custom_process_created_by" db:"custom_process_created_by"`
	CustomProcessCreatedName           string    `json:"custom_process_created_name" db:"custom_process_created_name"`
	CustomProcessUpdatedAt             time.Time `json:"custom_process_updated_at" db:"custom_process_updated_at"`
	CustomProcessUpdatedName           string    `json:"custom_process_updated_name" db:"custom_process_updated_name"`
	CustomProcessPartnerID             int       `json:"custom_process_partner_id" db:"custom_process_partner_id"`
	CustomProcessPartnerCode           string    `json:"custom_process_partner_code" db:"custom_process_partner_code"`
	CustomProcessPartnerName           string    `json:"custom_process_partner_name" db:"custom_process_partner_name"`
	CustomProcessRemarks               string    `json:"custom_process_remarks" db:"custom_process_remarks"`
	CustomProcessAccountType           string    `json:"custom_process_account_type" db:"custom_process_account_type"`
}

type CustomProcessViewParams struct {
	BasedFilter
	StartDate        string
	EndDate          string
	Status           []string
	CreatedBy        []int64
	PartnerIDWhereIn []int
	AccountRoleName  string
	AccountType      string
}
