package model

import (
	"database/sql"
	"encoding/json"
	"time"

	json2 "github.com/Lionparcel/hydra/shared/json"

	"github.com/Lionparcel/hydra/src/usecase/general"
)

const (
	DRIVER            = `driver`
	ALGO_ACCOUNT_NAME = `algo-system`

	TRUE  = `true`
	FALSE = `false`

	PLlogDeliveryDataNoSQLError     = `log-err-delivery-data-no-sql`
	PLlogPodDataNoSQLError          = `log-err-pod-data-no-sql`
	PLlogDeliveryMasterDateNoSQLErr = `log-err-delivery-master-data-no-sql`
	PLlogDeliveryDateNoSQLErr       = `log-err-delivery-data-no-sql`

	CodReconReport         = `cod-recon-report`
	DelColectedStatusFalse = "Belum Setor"
	DelColectedStatusTrue  = "Sudah Setor"
)

const (
	ALGO_ACCOUNT_ID int = 1000
)

var (
	IsAllowedAccountTypePODDEX = map[string]bool{
		SUBCONSOLE: true,
		CONSOLE:    true,
		POS:        true,
	}

	IsAllowAccountTypeDEL = map[string]bool{
		SUBCONSOLE: true,
		CONSOLE:    true,
	}

	IsAllowDeliveredByTypeDEL = map[string]bool{
		SUBCONSOLE: true,
		CONSOLE:    true,
		VENDOR:     true,
	}

	IsAllowedAccountTypePODDEXOnProcessStt = map[string]bool{
		CONSOLE: true,
	}

	IsNotAllowedDeliveryHub2Hub = map[string]bool{
		JUMBOPACKH2H: true,
	}
)

type Delivery struct {
	ID               int        `json:"id" db:"id" bson:"id"`
	DriverPhone      string     `json:"driver_phone" db:"driver_phone" bson:"driver_phone"`
	DriverName       string     `json:"driver_name" db:"driver_name" bson:"driver_name"`
	PartnerID        int        `json:"partner_id" db:"partner_id" bson:"partner_id"`
	PartnerType      string     `json:"partner_type" db:"partner_type" bson:"partner_type"`
	SttNo            string     `json:"stt_no" db:"stt_no" bson:"stt_no"`
	SttID            int        `json:"stt_id" db:"stt_id" bson:"stt_id"`
	FinishedStatus   *string    `json:"finished_status" db:"finished_status" bson:"finished_status"`
	FinishedBy       int        `json:"finished_by" db:"finished_by" bson:"finished_by"`
	FinishedRole     string     `json:"finished_role" db:"finished_role" bson:"finished_role"`
	FinishedName     string     `json:"finished_name" db:"finished_name" bson:"finished_name"`
	Remarks          string     `json:"remarks" db:"remarks" bson:"remarks"`
	ReasonCode       string     `json:"reason_code" db:"reason_code" bson:"reason_code"`
	DeliveryMasterID int        `json:"delivery_master_id" db:"delivery_master_id" bson:"delivery_master_id"`
	PaymentMethod    string     `json:"payment_method" db:"payment_method" bson:"payment_method"`
	CreatedAt        time.Time  `json:"created_at" db:"created_at" bson:"created_at"`
	UpdatedAt        *time.Time `json:"updated_at" db:"updated_at" bson:"updated_at"`

	ReconcileCollectedAt sql.NullTime `json:"reconcile_collected_at" db:"reconcile_collected_at" bson:"reconcile_collected_at"`
}

type DeliveryRemarks struct {
	RecieverName        string           `json:"reciever_name"`
	Attachment          string           `json:"attachment"`
	CreatedBy           string           `json:"created_by"`
	ForcePod            bool             `json:"force_pod"`
	IsWithProofFile     string           `json:"is_with_proof_file"`
	OtherReason         string           `json:"other_reason,omitempty"`
	DeliveryIsCollected bool             `json:"delivery_is_collected"`
	DeliveryTransfer    DeliveryTransfer `json:"delivery_transfer"`

	SttIsCOD             bool    `json:"stt_is_cod" db:"stt_is_cod"`
	SttCODAmount         float64 `json:"stt_cod_amount" db:"stt_cod_amount"`
	SttDestinationCityID string  `json:"stt_destination_city_id" db:"stt_destination_city_id"`
	SttRecipientName     string  `json:"stt_recipient_name" db:"stt_recipient_name"`
	SttRecipientPhone    string  `json:"stt_recipient_phone" db:"stt_recipient_phone"`

	GeolocationPODDEX    string               `json:"geolocation_pod_dex"`
	AddressPODDEX        string               `json:"address_pod_dex"`
	CaptainCourierName   string               `json:"captain_courier_name"`
	CaptainCourierPhone  string               `json:"captain_courier_phone"`
	PaymentMethod        string               `json:"payment_method"`
	DriverSource         string               `json:"driver_source"`
	PodRecipientRelation PodRecipientRelation `json:"pod_recipient_relation"`
	IsAutoReconcile      bool                 `json:"is_auto_reconcile"`
	AttachmentSigned     string               `json:"attachment_signed"`
	Latitude             float64              `json:"latitude"`
	Longitude            float64              `json:"longitude"`
}

type PodRecipientRelation struct {
	RelationId string
	Relation   string
}

type DeliveryTransfer struct {
	From struct {
		Driver struct {
			Name  string `json:"name"`
			Phone string `json:"phone"`
		} `json:"driver"`
		Partner struct {
			Name string `json:"name"`
			Type string `json:"type"`
		} `json:"partner"`
	} `json:"from"`
	To struct {
		Driver struct {
			Name  string `json:"name"`
			Phone string `json:"phone"`
		} `json:"driver"`
		Partner struct {
			Name string `json:"name"`
			Type string `json:"type"`
		} `json:"partner"`
	} `json:"to"`
}

func (c *DeliveryRemarks) ToString() string {
	json, err := json.Marshal(c)
	if err != nil {
		return ``
	}
	return string(json)
}

func (c *Delivery) LoadRemark() DeliveryRemarks {
	DeliveryRemarks := DeliveryRemarks{}
	if err := json.Unmarshal([]byte(c.Remarks), &DeliveryRemarks); err != nil {
		return DeliveryRemarks
	}

	return DeliveryRemarks
}

type DeliveryViewParam struct {
	BasedFilter
	ID                      int
	Search                  string
	SearchNoSTT             string
	FinishedRole            []string
	PartnerID               int
	PartnerType             string
	FinishedBy              int
	FinishedByWhereIn       []int
	PartnerIDWhereIn        []int
	IsWithStt               bool
	IsWithDelivery          bool
	IsWithLatestDelivery    bool
	StartDate               string
	EndDate                 string
	FinishedStatusWhereIN   []string
	FinishedStatusWhereNull bool
	SttNo                   string
	SortBy                  string
	OrderBy                 string
	DeliveryMasterID        int
	DELByAlgo               bool
	FinishedStatus          string
	NoLimit                 bool
	SttID                   int
	DeliveryIDIn            []int
	SttNoIn                 []string
	CustomColumns           string
	Offset                  int
	CreatedDateFrom         time.Time
	CreatedDateTo           time.Time
	FetchDateRangeWithID    bool
	IsNotDefaultQuery       bool
	DriverPhone             string
	IsCOD                   bool
	SttDestinationCityIDIn  []string
	LastDeliveryID          int
	UseMaster               bool
	PaymentMethod           string
	CheckCODType            bool
	IsEligibleArchivedType  bool
	IsJoinToSttActivity     bool
	IsNeedDecrypt           bool
}

func (c *DeliveryViewParam) SelectedColumn() string {
	if c.CustomColumns != "" {
		return c.CustomColumns
	}
	return "*"
}

type DeliveryDetailResult struct {
	DeliveryID int `json:"delivery_id" db:"delivery_id"`
	Delivery
	Stt
	SttActivity

	// priority delivery
	PdStatusReturn *string `json:"pd_status_return" db:"pd_status_return"`
}

type ViewDashboardDeliveryParams struct {
	SttNo                 string
	PartnerID             int
	PartnerType           string
	Interval              string
	PartnerIDs            []int
	ExcludeFinishedStatus []string
}

type DeliveryMaster struct {
	DeliveryMasterID                int       `json:"delivery_master_id" bson:"delivery_master_id" db:"delivery_master_id"`
	DeliveryMasterTotalStt          int       `json:"delivery_master_total_stt" bson:"delivery_master_total_stt" db:"delivery_master_total_stt"`
	DeliveryMasterTotalSttPiece     int       `json:"delivery_master_total_stt_piece" bson:"delivery_master_total_stt_piece" db:"delivery_master_total_stt_piece"`
	DeliveryMasterTotalGrossWeight  float64   `json:"delivery_master_total_gross_weight" bson:"delivery_master_total_gross_weight" db:"delivery_master_total_gross_weight"`
	DeliveryMasterTotalVolumeWeight float64   `json:"delivery_master_total_volume_weight" bson:"delivery_master_total_volume_weight" db:"delivery_master_total_volume_weight"`
	DeliveryMasterPartnerID         int       `json:"delivery_master_partner_id" bson:"delivery_master_partner_id" db:"delivery_master_partner_id"`
	DeliveryMasterPartnerType       string    `json:"delivery_master_partner_type" bson:"delivery_master_partner_type" db:"delivery_master_partner_type"`
	DeliveryMasterPartnerName       string    `json:"delivery_master_partner_name" bson:"delivery_master_partner_name" db:"delivery_master_partner_name"`
	DeliveryMasterDriverID          int       `json:"delivery_master_driver_id" bson:"delivery_master_driver_id" db:"delivery_master_driver_id"`
	DeliveryMasterDriverName        string    `json:"delivery_master_driver_name" bson:"delivery_master_driver_name" db:"delivery_master_driver_name"`
	DeliveryMasterDriverPhone       string    `json:"delivery_master_driver_phone" bson:"delivery_master_driver_phone" db:"delivery_master_driver_phone"`
	DeliveryMasterDeliveredBy       int       `json:"delivery_master_delivered_by" bson:"delivery_master_delivered_by" db:"delivery_master_delivered_by"`
	DeliveryMasterDeliveredByType   string    `json:"delivery_master_delivered_by_type" bson:"delivery_master_delivered_by_type" db:"delivery_master_delivered_by_type"`
	DeliveryMasterDeliveredByName   string    `json:"delivery_master_delivered_by_name" bson:"delivery_master_delivered_by_name" db:"delivery_master_delivered_by_name"`
	DeliveryMasterDestinationCode   string    `json:"delivery_master_destination_code" bson:"delivery_master_destination_code" db:"delivery_master_destination_code"`
	DeliveryMasterDestinationName   string    `json:"delivery_master_destination_name" bson:"delivery_master_destination_name" db:"delivery_master_destination_name"`
	DeliveryMasterCreatedAt         time.Time `json:"delivery_master_created_at" bson:"delivery_master_created_at" db:"delivery_master_created_at"`
	DeliveryMasterCreatedBy         int       `json:"delivery_master_created_by" bson:"delivery_master_created_by" db:"delivery_master_created_by"`
	DeliveryMasterCreatedName       string    `json:"delivery_master_created_name" bson:"delivery_master_created_name" db:"delivery_master_created_name"`
}

type DeliveryMasterDetailResult struct {
	DeliveryMaster
	Delivery
	Stt
}

type DeliveryMasterCreateParams struct {
	DeliveryMaster *DeliveryMaster
	DeliveryDetail []DeliveryDetailCreateParams
}

type DeliveryDetailCreateParams struct {
	Delivery Delivery
	Stt
	Histories []SttPieceHistory
}

type DeliveryMasterCreateResponse struct {
	DeliveryMasterID int64
}

// ViewDetailSttResponse ...
type ViewDetailSttResponse struct {
	IsPaid bool `json:"is_paid"`
	IsAllowUpdateStatusResponse
	Stt                 *general.SttResponse `json:"stt"`
	SttAssessmentStatus string               `json:"stt_assessment_status"`
}

// IsAllowUpdateStatusResponse ...
type IsAllowUpdateStatusResponse struct {
	IsAllowUpdateStatus bool   `json:"is_allow_update_status"`
	ErrorMessage        string `json:"error_message"`
}

type DeliveryAggrefationModel struct {
	DeliveryID int `bson:"delivery_id"`
}

type DeliveryWithCodAmount struct {
	ID               int        `db:"id"`
	DriverPhone      string     `db:"driver_phone"`
	DriverName       string     `db:"driver_name"`
	PartnerID        int        `db:"partner_id"`
	PartnerType      string     `db:"partner_type"`
	SttNo            string     `db:"stt_no"`
	SttID            int        `db:"stt_id"`
	FinishedStatus   *string    `db:"finished_status"`
	FinishedBy       int        `db:"finished_by"`
	FinishedRole     string     `db:"finished_role"`
	FinishedName     string     `db:"finished_name"`
	Remarks          string     `db:"remarks"`
	ReasonCode       string     `db:"reason_code"`
	DeliveryMasterID int        `db:"delivery_master_id"`
	CreatedAt        time.Time  `db:"created_at"`
	UpdatedAt        *time.Time `db:"updated_at"`
	SttCodAmount     float64    `db:"stt_cod_amount"`

	SttTotalPiece sql.NullInt64 `json:"stt_total_piece"`
	DriverID      sql.NullInt64 `json:"driver_id"`
}

type DeliveryPicWithCodAmount struct {
	ID               int        `db:"id"`
	PicPhone         string     `db:"dp_pic_phonenumber"`
	PicName          string     `db:"dp_pic_name"`
	PartnerID        int        `db:"partner_id"`
	PartnerType      string     `db:"partner_type"`
	SttNo            string     `db:"stt_no"`
	SttID            int        `db:"stt_id"`
	FinishedStatus   *string    `db:"finished_status"`
	FinishedBy       int        `db:"finished_by"`
	FinishedRole     string     `db:"finished_role"`
	FinishedName     string     `db:"finished_name"`
	Remarks          string     `db:"remarks"`
	ReasonCode       string     `db:"reason_code"`
	DeliveryMasterID int        `db:"delivery_master_id"`
	CreatedAt        time.Time  `db:"dp_created_at"`
	UpdatedAt        *time.Time `db:"dp_updated_at"`
	CollectedStatus  bool       `db:"dp_collected_status"`
	SttCodAmount     float64    `db:"stt_cod_amount"`
}

type CountDriverPendingReconcileParams struct {
	StartDate   time.Time
	EndDate     time.Time
	DriverPhone string
	PartnerID   int
}

func (c *DeliveryPicWithCodAmount) LoadRemark() DeliveryRemarks {
	DeliveryRemarks := DeliveryRemarks{}
	if err := json.Unmarshal([]byte(c.Remarks), &DeliveryRemarks); err != nil {
		return DeliveryRemarks
	}

	return DeliveryRemarks
}

func (c *DeliveryWithCodAmount) LoadRemark() DeliveryRemarks {
	DeliveryRemarks := DeliveryRemarks{}
	if err := json.Unmarshal([]byte(c.Remarks), &DeliveryRemarks); err != nil {
		return DeliveryRemarks
	}

	return DeliveryRemarks
}

type DeliveryCodDashboardGroupingParams struct {
	BasedFilter
	IsAllowSearchPartnerName bool
	IsNeedDriverName         bool
	SttDestinationCityCodeIn []string
	DeliveryPartnerIDs       []int
	DeliveryPartnerID        int
	DeliveryCreatedAtStart   *time.Time
	DeliveryCreatedAtEnd     *time.Time
	Search                   string
	NotInDelTransferTaks     bool
	WithoutPagination        bool
	DriverPhone              string
	CodOnly                  bool
}

type DeliveryPicCodDashboardGroupingParams struct {
	BasedFilter
	IsAllowSearchPartnerName bool
	IsNeedPicName            bool
	SttDestinationCityCodeIn []string
	DeliveryPartnerIDs       []int
	DeliveryPartnerID        int
	DeliveryCreatedAtStart   *time.Time
	DeliveryCreatedAtEnd     *time.Time
	Search                   string
}

type DeliveryCodDashboardParams struct {
	DeliveryPartnerID       int
	DeliveryDriverName      string
	DeliveryDriverPhone     string
	DeliveryPartnerType     string
	DeliveryCreatedDateFrom time.Time
	DeliveryCreatedDateTo   time.Time
	NotInDelTransferTaks    bool

	WhereInDeliveryDriverPhone []string
}

type DeliveryPicCodDashboardParams struct {
	DeliveryPartnerID       int
	DeliveryPicName         string
	DeliveryPicPhone        string
	DeliveryPartnerType     string
	DeliveryCreatedDateFrom time.Time
	DeliveryCreatedDateTo   time.Time
}

func (c *DeliveryDetailCreateParams) GetSttDelData() *Stt {
	return &Stt{
		SttNo:                          c.SttNo,
		SttRecipientName:               c.SttRecipientName,
		SttUpdatedAt:                   c.SttUpdatedAt,
		SttCODAmount:                   c.SttCODAmount,
		SttOriginCityID:                c.SttOriginCityID,
		SttDestinationCityID:           c.SttDestinationCityID,
		SttUpdatedActorRole:            c.SttUpdatedActorRole,
		SttProductType:                 c.SttProductType,
		SttSenderName:                  c.SttSenderName,
		SttBookedForType:               c.SttBookedForType,
		SttLastStatusID:                DEL,
		SttIsCOD:                       c.SttIsCOD,
		SttID:                          c.SttID,
		SttShipmentID:                  c.SttShipmentID,
		SttClientID:                    c.SttClientID,
		SttPosID:                       c.SttPosID,
		SttTaxNumber:                   c.SttTaxNumber,
		SttGoodsEstimatePrice:          c.SttGoodsEstimatePrice,
		SttGoodsStatus:                 c.SttGoodsStatus,
		SttTotalAmount:                 c.SttTotalAmount,
		SttNoRefExternal:               c.SttNoRefExternal,
		SttSource:                      c.SttSource,
		SttOriginDistrictID:            c.SttOriginDistrictID,
		SttDestinationDistrictID:       c.SttDestinationDistrictID,
		SttSenderPhone:                 c.SttSenderPhone,
		SttSenderAddress:               c.SttSenderAddress,
		SttRecipientAddress:            c.SttRecipientAddress,
		SttRecipientPhone:              c.SttRecipientPhone,
		SttOriginDistrictRate:          c.SttOriginDistrictRate,
		SttDestinationDistrictRate:     c.SttDestinationDistrictRate,
		SttPublishRate:                 c.SttPublishRate,
		SttShippingSurchargeRate:       c.SttShippingSurchargeRate,
		SttDocumentSurchargeRate:       c.SttDocumentSurchargeRate,
		SttCommoditySurchargeRate:      c.SttCommoditySurchargeRate,
		SttHeavyweightSurchargeRate:    c.SttHeavyweightSurchargeRate,
		SttBMTaxRate:                   c.SttBMTaxRate,
		SttPPNTaxRate:                  c.SttPPNTaxRate,
		SttPPHTaxRate:                  c.SttPPHTaxRate,
		SttGrossWeight:                 c.SttGrossWeight,
		SttVolumeWeight:                c.SttVolumeWeight,
		SttChargeableWeight:            c.SttChargeableWeight,
		SttCommodityCode:               c.SttCommodityCode,
		SttCommodityID:                 c.SttCommodityID,
		SttInsuranceType:               c.SttInsuranceType,
		SttTotalPiece:                  c.SttTotalPiece,
		SttWarningStatus:               c.SttWarningStatus,
		SttCounter:                     c.SttCounter,
		SttClientSttID:                 c.SttClientSttID,
		SttVendorSttID:                 c.SttVendorSttID,
		SttBilledTo:                    c.SttBilledTo,
		SttCODFee:                      c.SttCODFee,
		SttIsDO:                        c.SttIsCOD,
		SttMeta:                        c.SttMeta,
		SttTroubleStatus:               c.SttTroubleStatus,
		SttBookedAt:                    c.SttBookedAt,
		SttBookedName:                  c.SttBookedName,
		SttBookedByType:                c.SttBookedByType,
		SttBookedBy:                    c.SttBookedBy,
		SttCreatedAt:                   c.SttCreatedAt,
		SttCreatedName:                 c.SttCreatedName,
		SttCreatedBy:                   c.SttCreatedBy,
		SttUpdatedBy:                   c.SttUpdatedBy,
		SttUpdatedName:                 c.SttUpdatedName,
		SttUpdatedActorID:              c.SttUpdatedActorID,
		SttUpdatedActorName:            c.SttUpdatedActorName,
		ShipmentAlgoID:                 c.ShipmentAlgoID,
		SttRoute:                       c.SttRoute,
		SttDeliveryAttempt:             c.SttDeliveryAttempt,
		SttHeavyweightSurchargeRemark:  c.SttHeavyweightSurchargeRemark,
		SttNextCommodity:               c.SttNextCommodity,
		SttPiecePerPack:                c.SttPiecePerPack,
		SttElexysNo:                    c.SttElexysNo,
		SttCommodityName:               c.SttCommodityName,
		SttCommodityHsCode:             c.SttCommodityHsCode,
		SttOriginCityName:              c.SttOriginCityName,
		SttDestinationCityName:         c.SttDestinationCityName,
		SttOriginDistrictName:          c.SttOriginDistrictName,
		SttOriginDistrictUrsaCode:      c.SttOriginDistrictUrsaCode,
		SttDestinationDistrictName:     c.SttDestinationDistrictName,
		SttDestinationDistrictUrsaCode: c.SttDestinationDistrictUrsaCode,
		SttBookedByCode:                c.SttBookedByCode,
		SttBookedForID:                 c.SttBookedForID,
		SttBookedForName:               c.SttBookedForName,
		SttBookedForCode:               c.SttBookedForCode,
	}
}

type CountDeliveryBySttStatusBulkParams struct {
	SttNo          []string
	FinishedStatus string
}

func GetPaymentMethodLabel(paymentMethod string) string {
	if paymentMethod == "" {
		return "-"
	}
	return paymentMethod
}

func (c *DeliveryRemarks) GetPaymentMethodLabel() string {
	switch c.PaymentMethod {
	case PaymentMethodFree:
		return "GRATIS"
	case "":
		return "-"
	default:
		return c.PaymentMethod
	}
}

func (c *DeliveryRemarks) EncodeToString() string {
	return json2.EncodeWithoutEscapeHTML(c)
}
