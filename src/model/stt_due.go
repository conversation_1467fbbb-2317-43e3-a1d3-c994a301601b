package model

import (
	"time"
)

type (
	// STTDueModel model
	STTDueModel struct {
		SdID                 int64     `json:"sd_id" db:"sd_id"`
		SdRefType            string    `json:"sd_ref_type" db:"sd_ref_type"`
		SdRefID              uint64    `json:"sd_ref_id" db:"sd_ref_id"`
		SdSttNo              string    `json:"sd_stt_no" db:"sd_stt_no"`
		SdRefNo              string    `json:"sd_ref_no" db:"sd_ref_no"`
		SdBagNo              string    `json:"sd_bag_no" db:"sd_bag_no"`
		SdBookedType         string    `json:"sd_booked_type" db:"sd_booked_type"`
		SdBookedID           uint64    `json:"sd_booked_id" db:"sd_booked_id"`
		SdBookedName         string    `json:"sd_booked_name" db:"sd_booked_name"`
		SdSttBookedAt        time.Time `json:"sd_stt_booked_at" db:"sd_stt_booked_at"`
		SdTargetStatus       string    `json:"sd_target_status" db:"sd_target_status"`
		SdTargetDueDate      time.Time `json:"sd_target_due_date" db:"sd_target_due_date"`
		SdIsShow             bool      `json:"sd_is_show" db:"sd_is_show"`
		SdArrDate            time.Time `json:"sd_arr_date" db:"sd_arr_date"`
		SdIsIntracity        uint16    `json:"sd_is_intracity" db:"sd_is_intracity"`
		SdCargoNo            string    `json:"sd_cargo_no" db:"sd_cargo_no"`
		SdCargoType          string    `json:"sd_cargo_type" db:"sd_cargo_type"`
		SdCreatedAt          time.Time `json:"sd_created_at" db:"sd_created_at"`
		SdUpdatedAt          time.Time `json:"sd_updated_at" db:"sd_updated_at"`
		SdMeta               string    `json:"sd_meta" db:"sd_meta"`
		EligibleArchivedType int       `json:"eligible_archived_type" db:"eligible_archived_type"`
	}

	STTDueModelTotal struct {
		TotalData int64 `json:"total_data" db:"__total__"`
	}

	STTDueWithSTTModel struct {
		STTDueModel
		Stt
		Deadline string `json:"deadline" db:"deadline"`
	}

	STTDueWithSTTAndPriorityDeliveryModel struct {
		STTDueModel
		Stt
		PriorityDelivery
		Deadline string `json:"deadline" db:"deadline"`
	}

	STTDueFindAllAndPaginateParameters struct {
		PerPage      int
		Offset       int
		Query        string
		SortBy       string
		SortColumn   string
		BookedType   string
		BookedID     string
		Deadline     string
		TargetStatus string
		STTDuePartnerParam
		IsForCount      bool
		IsForExportData bool
		IsForSummary    bool
		NowDateTime     string
	}

	STTDueSummaryAnalyticParameters struct {
		IsShow               int
		TargetStatus         string
		EligibleArchivedType int
		RefType              string
		RefID                uint64
		CargoType            string
		DayInterval          int
		IsNeedInterval       bool
	}

	SttNeedToSti struct {
		SttDueHistory
		STTDueModel
		Stt
		Sti
	}

	STTDuePartnerParam struct {
		PartnerType       string
		PartnerID         uint64
		BookedType        string
		BookedID          []int
		WithoutSubconsole bool
	}

	STTDueFilterParam struct {
		STTDuePartnerParam
		SttNos              []string
		TargetStatus        string
		CargoNos            []string
		CargoTypes          []string
		StatusShow          int
		EligibleArchiveType int
		OrderBy             string
		SortBy              string
		Limit               int
		Offset              int
		OnlyForIntervalDays int
		IsWithSttData       bool
	}

	GetSttNeedToStiParams struct {
		StiID       int
		PartnerType string
		PartnerID   int
	}

	STTDueSummaryData struct {
		Total        int64 `json:"total" db:"total"`
		TotalNow     int64 `json:"total_now" db:"total_now"`
		TotalOverdue int64 `json:"total_overdue" db:"total_overdue"`
		TotalBooked  int64 `json:"total_booked" db:"total_booked"`
	}

	STTDueSummaryAnalyticData struct {
		STTDueSummaryAnalyticDataCargo

		STTDueSummaryAnalyticDataDel
	}

	STTDueSummaryAnalyticDataCargo struct {
		Total           int64 `json:"total_stt_need_stidest" db:"total_stt_need_stidest"`
		TotalCargoPlane int64 `json:"total_cargo_plane" db:"total_cargo_plane"`
		TotalCargoTruck int64 `json:"total_cargo_truck" db:"total_cargo_truck"`
		TotalCargoShip  int64 `json:"total_cargo_ship" db:"total_cargo_ship"`
	}

	STTDueSummaryAnalyticDataDel struct {
		TotalDelNow          int64 `json:"total_del_now" db:"total_del_now"`
		TotalDelNowIntracity int64 `json:"total_del_now_intracity" db:"total_del_now_intracity"`
		TotalDelNowInterCity int64 `json:"total_del_now_intercity" db:"total_del_now_intercity"`

		TotalDelOverdue          int64 `json:"total_del_overdue" db:"total_del_overdue"`
		TotalDelOverdueIntracity int64 `json:"total_del_overdue_intracity" db:"total_del_overdue_intracity"`
		TotalDelOverdueInterCity int64 `json:"total_del_overdue_intercity" db:"total_del_overdue_intercity"`
	}

	STTDueFindAllBookedAndPaginateParameters struct {
		PerPage              int
		Offset               int
		Search               string
		TargetStatus         string
		EligibleArchivedType int
		SdIsShow             int
		NotBookedTypes       []string
		STTDuePartnerParam
		IsForCount bool
	}

	STTDueUpdateIsShow struct {
		IsShow       int      `json:"is_show" db:"is_show"`
		TargetStatus string   `json:"target_status" db:"target_status"`
		STTNos       []string `json:"stt_nos" db:"stt_nos"`
	}

	STTDueUpdateBulkParams struct {
		FieldInsertSttDue *STTDueModel
		SttNo             []string
		SttDueIDs         []int64
		SttTargetStatus   []string
		UpdateFields      []string

		// validation
		IsSttNoRequired            bool
		IsSttDueIDRequired         bool
		IsSttDueLastStatusRequired bool
	}

	CountSttDueParam struct {
		PartnerType string
		PartnerID   uint64
		BookedType  string
		BookedID    uint64
	}

	CountSttDueSTISCParam struct {
		ManifestID int64
	}

	UpdateSttDueBagNoParams struct {
		BagNo    string
		SttDatas map[string]Stt
	}

	SttDueStiDestDelFindAllAndPaginateParameter struct {
		PerPage         int
		Offset          int
		Query           string
		Deadline        string
		StatusReturn    string
		CargoType       string
		SttStatus       []string
		PartnerType     string
		PartnerID       uint64
		IsForCount      bool
		IsForExportData bool
	}

	CountSttDueSTISCResult struct {
		Total int `json:"total" db:"total"`
	}

	GetBySttNoParams struct {
		SttNo        string
		TargetStatus string
		IsShow       *bool
		BookedType   string
	}
)

const (
	// STTDueRepoNowDeadline ...
	STTDueRepoNowDeadline = "now"
	// STTDueRepoOverdueDeadline ...
	STTDueRepoOverdueDeadline = "overdue"

	// BookedTypePOS ...
	BookedTypePOS = "pos"
	// BookedTypeCLIENT ...
	BookedTypeCLIENT = "client"
	// BookedTypeSubConsole ...
	BookedTypeSubConsole = "sub-console"

	// STTDueIsShowFalse ...
	STTDueIsShowFalse = 0
	// STTDueIsShowTrue ...
	STTDueIsShowTrue = 1
)

func (m *STTDueFindAllAndPaginateParameters) IsValidNowDateTime() bool {
	if m.NowDateTime == "" {
		return false
	}

	_, err := time.Parse(`2006-01-02 15:04:05`, m.NowDateTime)
	if err != nil {
		return false
	}

	return true
}
