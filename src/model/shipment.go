package model

import (
	"context"
	"encoding/json"
	"math"
	"strings"
	"time"
)

const (
	// Prefix Shipment

	// AG ...
	AG = `AG` // Pickup Regular
	// AD ...
	AD = `AD` // Dropoff Regular
	// AP ...
	AP = `AP` // Pickup Favorite
	// AS ...
	AS = `AS` // Dropoff Favorite
	// AO ...
	AO = `AO` // Oleh-oleh
	// AI ...
	AI = `AI` // Paket Indomaret
	// T1 ...
	T1 = `T1` // Pickup Tokopedia
	// B1 ...
	B1 = `B1` // Pickup Bukalapak
	// B2 ...
	B2 = `B2` // Dropoff Bukalapak
	// TKLP ...
	TKLP = `TKLP` // Pickup Tokopedia
	// TSLP ...
	TSLP = `TSLP` // Tiktok Shop
	// BLP ...
	BLP = `BLP`
	// TKP ...
	TKP = `TKP`
	// CSA ...
	CSA = `CSA`
	// DO ...
	DO = `DO`
	// ACA ...
	ACA = `ACA`
	// ACB ...
	ACB = `ACB`
	// ACR
	ACR = `ACR`
	// CCR
	CCR = `CCR`
	// C1
	C1 = `C1`
	// C2
	C2 = `C2`
	//CP ...
	// TKP01 ...
	TKP01 = `TKP01`
	// TKP01-BAG
	TKP01_BAG = `TKP01-BAG`
	// ARA
	ARA = `ARA`
	// ARB
	ARB = `ARB`
	// CP
	CP = `CP`

	// TOKOPEDIA ...
	TOKOPEDIA = `Tokopedia`
	// BUKALAPAK ...
	BUKALAPAK         = `Bukalapak`
	BUKALAPAK_PICKUP  = `Bukalapak Pickup`
	BUKALAPAK_DROPOFF = `Bukalapak Dropoff`
	// CUSTOMERAPPS ...
	CUSTOMERAPPS           = `Customer Apps`
	CA_PICKUP_REGULAR      = `CA Pickup Regular`
	CA_DROPOFF_REGULAR     = `CA Dropoff Regular`
	CA_PICKUP_FAVORITE     = `CA Pickup Favorite`
	CA_DROPOFF_FAVORITE    = `CA Dropoff Favorite`
	CA_INDOMARET           = `CA Indomaret`
	CA_OLEH_OLEH           = `CA Oleh-oleh`
	CA_PICKUP_REGULER_COD  = `CA Pickup Regular COD`
	CA_DROPOFF_REGULER_COD = `CA Dropoff Regular COD `
	CA_COD_RETURN          = `CA COD Return`
	CA_DO_RETURN           = `CA DO Return`
	CLIENT_COD_RETURN      = `Client COD Return`
	CLIENT_PICKUP          = `Client Pickup`
	CA_USERNAME            = "ca"
	SHIPMENT_PICKUP_COD    = `Shipment Pickup COD`
	SHIPMENT_DROPOFF_COD   = `Shipment Dropoff COD`

	// CLIENT CODE FROM ALGO

	TOKOPEDIA_CLIENT_CODE    = `TKP`
	TOKOPEDIA_CLIENT_CODE_MP = `MP`
	// BUKALAPAK ...
	BUKALAPAK_CLIENT_CODE = `BLP`
	// CUSTOMERAPPS ...
	CUSTOMERAPPS_CLIENT_CODE = `CAP`
	// CUSTOMERAPPS_CODRETURN ...
	CUSTOMERAPPS_CODRETURN_CLIENT_CODE = `ACR`
	// CUSTOMERAPPS_DORETURN ...
	CUSTOMERAPPS_DORETURN_CLIENT_CODE = `DOR`
	// CLIENT_CODRETURN ...
	CLIENT_CODRETURN_CLIENT_CODE = `CCR`
	// COD_CA_RETAIL_CODE ..
	COD_CA_RETAIL_CODE = `ARA`
	// COD_CA_RETAIL_DROPOFF_CODE
	COD_CA_RETAIL_DROPOFF_CODE = `ARB`

	// SHIPMENT_REPORT
	ShipmentReport = `shipment-report`
	KulionerReport = `kulioner-report`

	// CLIENT_SHIPMENT_REPORT
	ClientShipmentReport = `client-shipment-report`

	// SHIPMENT cod_handling
	SPECIALCOD  = `specialcod`
	STANDARDCOD = `standardcod`

	ShipmentLabelHome   = `RUMAH`
	ShipmentLabelOffice = `KANTOR`

	// ShipmentStatusBKD ...
	ShipmentStatusBKD = `BKD`
	// ShipmentStatusCRRDON ...
	ShipmentStatusCRRDON = `CRRDON`
)

var (

	// IsShipmentC1CodHandling ...
	IsShipmentC1CodHandling = map[string]bool{
		SPECIALCOD:  true,
		STANDARDCOD: true,
	}

	// IsShipmentFavorite ...
	IsShipmentFavorite = map[string]bool{
		AP: true,
		AS: true,
	}

	// IsShipmentMarketplace ...
	IsShipmentMarketplace = map[string]bool{
		B2:   true,
		B1:   true,
		T1:   true,
		TKLP: true,
		TSLP: true,
	}

	// IsShipmentTokopedia ...
	IsShipmentTokopedia = map[string]bool{
		T1:   true,
		TKLP: true,
		TSLP: true,
	}

	IsShipmentBukalapak = map[string]bool{
		B1: true,
		B2: true,
	}

	MapShipmentPrefixClientCode = map[string]string{
		T1:   TKP,
		TKLP: TKP,
		TSLP: TKP,
		B1:   BLP,
		B2:   BLP,
	}

	// MappingShipmentPrefixGoodsStatus ...
	MappingShipmentPrefixGoodsStatus = map[string]string{
		B2:   ECOMMERCE,
		B1:   ECOMMERCE,
		T1:   ECOMMERCE,
		TKLP: ECOMMERCE,
		TSLP: ECOMMERCE,
	}

	// MappingShipmentPrefixBilledTo ...
	MappingShipmentPrefixBilledTo = map[string]string{
		TKLP: TOKOPEDIA,
		T1:   TOKOPEDIA,
		TSLP: TOKOPEDIA,
		B2:   BUKALAPAK,
		B1:   BUKALAPAK,
		DO:   CUSTOMERAPPS_DORETURN_CLIENT_CODE,
		ACR:  CUSTOMERAPPS_CODRETURN_CLIENT_CODE,
		CCR:  CLIENT_CODRETURN_CLIENT_CODE,
	}

	// MappingShipmentPrefixMaxStt ...
	MappingShipmentPrefixMaxStt = map[string]int{
		AP:   100,
		AS:   100,
		B1:   1,
		B2:   1,
		T1:   1,
		TKLP: 1,
		TSLP: 1,
		AG:   1,   // Pickup Regular
		AD:   100, // Dropoff Regular
		AI:   1,   // Paket Indomaret
		AO:   1,   // Oleh-oleh
		DO:   1,   // DO Return
		ACA:  1,   // Pickup Reguler COD
		ACB:  1,   // Dropoff Reguler COD
		ACR:  1,   // CA COD Return
		CCR:  1,   // Client COD Return
		ARA:  1,   // COD CA Retail
		ARB:  1,   // COD CA Retail DropOff
	}

	// MappingShipmentPrefixIsAdjustable ...
	MappingShipmentPrefixIsAdjustable = map[string]bool{
		AP: true,
		AS: true,
		// AD: true,
	}

	// MappingShipmentPrefixCustomerName
	MappingShipmentPrefixCustomerName = map[string]string{
		AG:   CUSTOMERAPPS,
		AD:   CUSTOMERAPPS,
		AP:   CUSTOMERAPPS,
		AS:   CUSTOMERAPPS,
		AO:   CUSTOMERAPPS,
		AI:   CUSTOMERAPPS,
		ARA:  CUSTOMERAPPS,
		ARB:  CUSTOMERAPPS,
		B1:   BUKALAPAK,
		B2:   BUKALAPAK,
		T1:   TOKOPEDIA,
		TKLP: TOKOPEDIA,
		TSLP: TOKOPEDIA,
		ACA:  CUSTOMERAPPS,
		ACB:  CUSTOMERAPPS,
		DO:   CUSTOMERAPPS,
		ACR:  CUSTOMERAPPS,
		CCR:  CUSTOMERAPPS,
	}

	MappingShipmentPrefixList = map[string]string{
		T1:   TOKOPEDIA,
		TKLP: TOKOPEDIA,
		TSLP: TOKOPEDIA,
		B1:   BUKALAPAK_PICKUP,
		B2:   BUKALAPAK_DROPOFF,
		AG:   CA_PICKUP_REGULAR,
		AD:   CA_DROPOFF_REGULAR,
		AP:   CA_PICKUP_FAVORITE,
		AS:   CA_DROPOFF_FAVORITE,
		AI:   CA_INDOMARET,
		AO:   CA_OLEH_OLEH,
		ACA:  CA_PICKUP_REGULER_COD,
		ACB:  CA_DROPOFF_REGULER_COD,
		ACR:  CA_COD_RETURN,
		CCR:  CLIENT_COD_RETURN,
		DO:   CA_DO_RETURN,
		C1:   CLIENT_PICKUP,
		C2:   CLIENT_PICKUP,
		ARA:  SHIPMENT_PICKUP_COD,
		ARB:  SHIPMENT_DROPOFF_COD,
	}

	// MappingShipmentPrefixCustomerName
	MappingShipmentPrefixClientCode = map[string]string{
		AG:   CUSTOMERAPPS_CLIENT_CODE,
		AD:   CUSTOMERAPPS_CLIENT_CODE,
		AP:   CUSTOMERAPPS_CLIENT_CODE,
		AS:   CUSTOMERAPPS_CLIENT_CODE,
		AO:   CUSTOMERAPPS_CLIENT_CODE,
		AI:   CUSTOMERAPPS_CLIENT_CODE,
		DO:   CUSTOMERAPPS_DORETURN_CLIENT_CODE,
		B1:   BUKALAPAK_CLIENT_CODE,
		B2:   BUKALAPAK_CLIENT_CODE,
		T1:   TOKOPEDIA_CLIENT_CODE,
		TKLP: TOKOPEDIA_CLIENT_CODE,
		TSLP: TOKOPEDIA_CLIENT_CODE,
		ACA:  CUSTOMERAPPS_CLIENT_CODE,
		ACB:  CUSTOMERAPPS_CLIENT_CODE,
		ACR:  CUSTOMERAPPS_CODRETURN_CLIENT_CODE,
		CCR:  CLIENT_CODRETURN_CLIENT_CODE,
		ARA:  CUSTOMERAPPS_CLIENT_CODE,
		ARB:  CUSTOMERAPPS_CLIENT_CODE,
	}

	PrefixShipmentIDIsAllowdPromoDiscont = map[string]bool{
		AG:  true,
		AI:  true,
		AD:  true,
		ACA: true,
		AO:  true,
		ACB: true,
		AP:  true,
		AS:  true,
		ARA: true,
		ARB: true,
	}

	PrefixShipmentIDIsNotAllowedToUseClientTarif = map[string]bool{
		AP: true,
		AS: true,
	}
	IsShipmentPrefixFavorite = map[string]bool{
		AP: true,
		AS: true,
	}

	// IsPrefixShipmentValid ...
	IsPrefixShipmentValid = map[string]bool{
		AG:   true,
		AD:   true,
		AP:   true,
		AS:   true,
		AO:   true,
		AI:   true,
		T1:   true,
		B1:   true,
		B2:   true,
		DO:   true,
		TKLP: true,
		TSLP: true,
		ACA:  true,
		ACB:  true,
		ACR:  true,
		CCR:  true,
		C1:   true,
		C2:   true,
		ARA:  true,
		ARB:  true,
		CP:   true,
	}

	// IsNotAllowToEditForSttAdjustmentPOD
	IsNotAllowToEditForSttAdjustmentPOD = map[string]bool{
		TOKOPEDIA_CLIENT_CODE:    true,
		BUKALAPAK_CLIENT_CODE:    true,
		CUSTOMERAPPS_CLIENT_CODE: true,
	}

	// AG, AD, AO, ACA, ACB, AI, ARA, ARB
	IsPrefixShipmentPaymentHoldValid = map[string]bool{
		AG:  true,
		AD:  true,
		AO:  true,
		AI:  true,
		ACA: true,
		ACB: true,
		ARA: true,
		ARB: true,
	}

	IsPrefixShipmentRTSPaymentHoldValid = map[string]bool{
		AG:  true,
		AD:  true,
		AO:  true,
		AI:  true,
		ACA: true,
		ACB: true,

		AP: true,
		AS: true,
	}

	//IsShipmentUpdateDisable
	MappingShipmentDisableEditStt = map[string]bool{
		B2: true,
		B1: true,
	}

	MappingShipmentCODValid = map[string]bool{
		ACA: true,
		ACB: true,
		ARA: true,
		ARB: true,
	}

	MappingShipmentCustomerApps = map[string]bool{
		AG: true,
		AD: true,
		AP: true,
		AS: true,
		AO: true,
	}

	// MappingShipmentPrefixCustomerApps
	MappingShipmentPrefixCustomerApps = map[string]bool{
		AG:  true,
		AD:  true,
		AP:  true,
		AS:  true,
		AO:  true,
		AI:  true,
		ACA: true,
		ACB: true,
		ACR: true,
	}

	// MappingShipmentPrefixPosBranchBookingCommission
	MappingShipmentPrefixPosBranchBookingCommission = map[string]bool{
		AD:   true,
		ACB:  true,
		B2:   true,
		T1:   true,
		TKLP: true,
		TSLP: true,
		ARA:  true,
		ARB:  true,
	}

	IsPrefixShipmentNonPosFavoriteCA = map[string]bool{
		AG:  true,
		AD:  true,
		AO:  true,
		AI:  true,
		ACA: true,
		ACB: true,
		C1:  true,
		C2:  true,
	}

	IsPrefixShipmentNonPosFavoriteCAToAlgo = map[string]bool{
		AG:  true,
		AD:  true,
		AI:  true,
		ACA: true,
		ACB: true,
	}

	IsPrefixShipmentCAToAlgoRTS = map[string]bool{
		AG:  true,
		AD:  true,
		AI:  true,
		ACA: true,
		ACB: true,
	}

	IsPrefixShipmentBookingPOS = map[string]bool{
		AP:    true,
		AS:    true,
		C1:    true,
		C2:    true,
		DO:    true,
		TKP01: true,
		B2:    true,
		B1:    true,
		T1:    true,
		TKLP:  true,
		TSLP:  true,
		ARA:   true,
		ARB:   true,
		TKP:   true,
	}

	IsPrefixShipmentAllowFlagInvoice = map[string]bool{
		AG:  true,
		AD:  true,
		AI:  true,
		ACA: true,
		ACB: true,
		AP:  true,
		AS:  true,
		ARA: true,
		ARB: true,
	}

	// MappingShipmentPrefixCustomerApps
	MappingShipmentPrefixCODCustomerAppsRetail = map[string]bool{
		ARA: true,
		ARB: true,
	}

	MappingShipmentPrefixApAs = map[string]bool{
		AP: true,
		AS: true,
	}

	MappingShipmentPrefixC1 = map[string]bool{
		C1: true,
		C2: true,
	}

	IsValidShipmentPrefixCA = map[string]bool{
		AG:  true,
		AD:  true,
		AP:  true,
		AS:  true,
		AO:  true,
		AI:  true,
		ACA: true,
		ACB: true,
		ACR: true,
		ARA: true,
		ARB: true,
	}

	MappingAllowedEditMapping = map[string]bool{
		// Allowed Shipment to Edit
		AD:   true,
		ACA:  true,
		ARB:  true,
		ACB:  true,
		AO:   true,
		AI:   true,
		ARA:  true,
		T1:   true,
		TKLP: true,
		TSLP: true,
		C1:   true,
		C2:   true,

		// Allowed STT External to Edit
		TKP01: true,
		TKP:   true,

		// Allowed STT No to Edit
		PrefixClient:               true,
		PrefixAutoBookingForClient: true,
		PrefixAutoDO:               true,
		PrefixAutoClient:           true,
	}

	// IsShipmentPrefixEnableCOD
	IsShipmentPrefixEnableCOD = map[string]bool{
		ARA: true,
		ARB: true,
		AP:  true,
		AS:  true,
	}

	IsShipmentHoldCommission = map[string]bool{
		T1:   true,
		TKLP: true,
		TSLP: true,
		B1:   true,
		B2:   true,
		C1:   true,
		C2:   true,
	}

	IsShipmentPrefixSpecialCOD = map[string]bool{
		C1: true,
		C2: true,
	}

	// IsShipmentCod
	IsShipmentCod = map[string]bool{
		ARA: true,
		ARB: true,
		AP:  true,
		AS:  true,
		C1:  true,
		C2:  true,
	}

	IsNoRefExternal = map[string]bool{
		TKLP: true,
		TSLP: true,
	}

	IsShipmentClientCodGoodsPrice = map[string]bool{
		C1: true,
		C2: true,
	}

	ExcludeCheckSaldoShipmentPrefix = map[string]bool{
		AG: true,
		AD: true,
		AO: true,
		C1: true,
		C2: true,
	}
)

type (
	// ShipmentAlgo ...
	ShipmentAlgo struct {
		NumberOfStt                int64    `json:"number_of_stt"`
		Packets                    []Packet `json:"packets"`
		AgentCode                  string   `json:"agent_code"`
		ShipmentID                 string   `json:"shipment_id"`
		BookingID                  string   `json:"booking_id"`
		DiscountFavoritePercentage float64  `json:"discount_favorite_percentage"`
		GroupID                    string   `json:"group_id"`
		TotalShipmentGroup         int      `json:"total_shipment_group"`
	}

	// Shipment ...
	Shipment struct {
		ShipmentID              int64   `json:"shipment_id" db:"shipment_id"`
		ShipmentAlgoID          string  `json:"shipment_algo_id" db:"shipment_algo_id"`
		ShipmentShipmentID      string  `json:"shipment_shipment_id" db:"shipment_shipment_id"`
		ShipmentBookingID       string  `json:"shipment_booking_id" db:"shipment_booking_id"`
		ShipmentAlgoNumberOfStt int64   `json:"shipment_algo_number_of_stt" db:"shipment_algo_number_of_stt"`
		ShipmentAlgoAgentCode   string  `json:"shipment_algo_agent_code" db:"shipment_algo_agent_code"`
		ShipmentGroupBookingID  string  `json:"shipment_group_booking_id" db:"shipment_group_booking_id"`
		IsCancelShipment        bool    `json:"is_cancel_shipment" db:"is_cancel_shipment"`
		ShipmentMeta            *string `json:"shipment_meta" db:"shipment_meta"`
		ShipmentPackets         []ShipmentPacket
		ShipmentCreatedAt       time.Time `json:"shipment_created_at" db:"shipment_created_at"`
		ShipmentUpdatedAt       time.Time `json:"shipment_updated_at" db:"shipment_updated_at"`
	}

	// ShipmentMeta ...
	ShipmentMeta struct {
		CodHandling                string  `json:"cod_handling"`
		DiscountFavoritePercentage float64 `json:"discount_favorite_percentage"`
		TotalShipmentGroup         int     `json:"total_shipment_group,omitempty"`
		PrioritySubscription       bool    `json:"priority_subscription,omitempty"`
	}

	// ShipmentPacket ...
	ShipmentPacket struct {
		ShipmentPacketID                 int64   `json:"shipment_packet_id" db:"shipment_packet_id"`
		ShipmentPacketShipmentID         int64   `json:"shipment_packet_shipment_id" db:"shipment_packet_shipment_id"`
		ShipmentPacketSender             string  `json:"shipment_packet_sender" db:"shipment_packet_sender"`
		ShipmentPacketRecipient          string  `json:"shipment_packet_recipient" db:"shipment_packet_recipient"`
		ShipmentPacketProduct            string  `json:"shipment_packet_product" db:"shipment_packet_product"`
		ShipmentPacketServiceType        string  `json:"shipment_packet_service_type" db:"shipment_packet_service_type"`
		ShipmentPacketCommodity          string  `json:"shipment_packet_commodity" db:"shipment_packet_commodity"`
		ShipmentPacketIsCod              bool    `json:"shipment_packet_is_cod" db:"shipment_packet_is_cod"`
		ShipmentPacketIsDfod             bool    `json:"shipment_packet_is_dfod" db:"shipment_packet_is_dfod"`
		ShipmentPacketIsWoodpacking      bool    `json:"shipment_packet_is_woodpacking" db:"shipment_packet_is_woodpacking"`
		ShipmentPacketIsInsurance        bool    `json:"shipment_packet_is_insurance" db:"shipment_packet_is_insurance"`
		ShipmentPacketGoodsValue         float64 `json:"shipment_packet_goods_value" db:"shipment_packet_goods_value"`
		ShipmentPacketCodValue           float64 `json:"shipment_packet_cod_value" db:"shipment_packet_cod_value"`
		ShipmentPacketCodFee             float64 `json:"shipment_packet_cod_fee" db:"shipment_packet_cod_fee"`
		ShipmentPacketNoOfPieces         int64   `json:"shipment_packet_no_of_pieces" db:"shipment_packet_no_of_pieces"`
		ShipmentPacketCustomerCode       string  `json:"shipment_packet_customer_code" db:"shipment_packet_customer_code"`
		ShipmentPacketItems              string  `json:"shipment_packet_items" db:"shipment_packet_items"`
		ShipmentPacketSTTNo              string  `json:"shipment_packet_stt_no" db:"shipment_packet_stt_no"`
		ShipmentPacketCustomerBranchCode string  `json:"shipment_packet_customer_branch_code" db:"shipment_packet_customer_branch_code"`
		STT                              ShipmentSTT
		ShipmentPacketCreatedAt          time.Time `json:"shipment_packet_created_at" db:"shipment_packet_created_at"`
		ShipmentPacketUpdatedAt          time.Time `json:"shipment_packet_updated_at" db:"shipment_packet_updated_at"`
		ShipmentPacketTotalTariff        float64   `json:"shipment_packet_total_tariff" db:"shipment_packet_total_tariff"`
	}
	ShipmentPacketDB struct {
		ShipmentPacketID                 *int64   `json:"shipment_packet_id" db:"shipment_packet_id"`
		ShipmentPacketShipmentID         *int64   `json:"shipment_packet_shipment_id" db:"shipment_packet_shipment_id"`
		ShipmentPacketSender             *string  `json:"shipment_packet_sender" db:"shipment_packet_sender"`
		ShipmentPacketRecipient          *string  `json:"shipment_packet_recipient" db:"shipment_packet_recipient"`
		ShipmentPacketProduct            *string  `json:"shipment_packet_product" db:"shipment_packet_product"`
		ShipmentPacketServiceType        *string  `json:"shipment_packet_service_type" db:"shipment_packet_service_type"`
		ShipmentPacketCommodity          *string  `json:"shipment_packet_commodity" db:"shipment_packet_commodity"`
		ShipmentPacketIsCod              *bool    `json:"shipment_packet_is_cod" db:"shipment_packet_is_cod"`
		ShipmentPacketIsDfod             *bool    `json:"shipment_packet_is_dfod" db:"shipment_packet_is_dfod"`
		ShipmentPacketIsWoodpacking      *bool    `json:"shipment_packet_is_woodpacking" db:"shipment_packet_is_woodpacking"`
		ShipmentPacketIsInsurance        *bool    `json:"shipment_packet_is_insurance" db:"shipment_packet_is_insurance"`
		ShipmentPacketGoodsValue         *float64 `json:"shipment_packet_goods_value" db:"shipment_packet_goods_value"`
		ShipmentPacketCodValue           *float64 `json:"shipment_packet_cod_value" db:"shipment_packet_cod_value"`
		ShipmentPacketCodFee             *float64 `json:"shipment_packet_cod_fee" db:"shipment_packet_cod_fee"`
		ShipmentPacketNoOfPieces         *int64   `json:"shipment_packet_no_of_pieces" db:"shipment_packet_no_of_pieces"`
		ShipmentPacketCustomerCode       *string  `json:"shipment_packet_customer_code" db:"shipment_packet_customer_code"`
		ShipmentPacketItems              *string  `json:"shipment_packet_items" db:"shipment_packet_items"`
		ShipmentPacketSTTNo              *string  `json:"shipment_packet_stt_no" db:"shipment_packet_stt_no"`
		ShipmentPacketCustomerBranchCode *string  `json:"shipment_packet_customer_branch_code" db:"shipment_packet_customer_branch_code"`
		STT                              ShipmentSTTDB
		ShipmentPacketCreatedAt          *time.Time `json:"shipment_packet_created_at" db:"shipment_packet_created_at"`
		ShipmentPacketUpdatedAt          *time.Time `json:"shipment_packet_updated_at" db:"shipment_packet_updated_at"`
		ShipmentPacketTotalTariff        *float64   `json:"shipment_packet_total_tariff" db:"shipment_packet_total_tariff"`
	}

	// ShipmentPacketSubject ...
	ShipmentPacketSubject struct {
		Name        string `json:"name"`
		Phone       string `json:"phone"`
		Address     string `json:"address"`
		District    string `json:"district"`
		Destination string `json:"destination"`
		Label       string `json:"label"`
		Origin      string `json:"origin"`
	}

	// ShipmentPacketItemsValue ...
	ShipmentPacketItemsValue struct {
		Name   string  `json:"name"`
		Width  float64 `json:"width"`
		Height float64 `json:"height"`
		Length float64 `json:"length"`
		Weight float64 `json:"weight"`
	}

	// ShipmentSTT ...
	ShipmentSTT struct {
		SttID                            int64     `json:"stt_id" db:"id" faker:"oneof: 1, 2"`
		SttNo                            string    `json:"stt_no" db:"stt_no"`
		SttClientID                      int       `json:"stt_client_id" db:"stt_client_id"`
		SttPosID                         int       `json:"stt_pos_id" db:"stt_pos_id"`
		SttTaxNumber                     string    `json:"stt_tax_number" db:"stt_tax_number"`
		SttGoodsEstimatePrice            float64   `json:"stt_goods_estimate_price" db:"stt_goods_estimate_price"`
		SttGoodsStatus                   string    `json:"stt_goods_status" db:"stt_goods_status"`
		SttTotalAmount                   float64   `json:"stt_total_amount" db:"stt_total_amount"`
		SttNoRefExternal                 string    `json:"stt_no_ref_external" db:"stt_no_ref_external"`
		SttSource                        string    `json:"stt_source" db:"stt_source"`
		SttOriginCityID                  string    `json:"stt_origin_city_id" db:"stt_origin_city_id"`
		SttDestinationCityID             string    `json:"stt_destination_city_id" db:"stt_destination_city_id"`
		SttOriginDistrictID              string    `json:"stt_origin_district_id" db:"stt_origin_district_id"`
		SttDestinationDistrictID         string    `json:"stt_destination_district_id" db:"stt_destination_district_id"`
		SttDestinationDistrictType       string    `json:"stt_destination_district_type" db:"stt_destination_district_type"`
		SttDestinationDistrictVendorCode string    `json:"stt_destination_district_vendor_code" db:"stt_destination_district_vendor_code"`
		SttSenderName                    string    `json:"stt_sender_name" db:"stt_sender_name"`
		SttSenderPhone                   string    `json:"stt_sender_phone" db:"stt_sender_phone"`
		SttSenderAddress                 string    `json:"stt_sender_address" db:"stt_sender_address"`
		SttRecipientName                 string    `json:"stt_recipient_name" db:"stt_recipient_name"`
		SttRecipientAddress              string    `json:"stt_recipient_address" db:"stt_recipient_address"`
		SttRecipientPhone                string    `json:"stt_recipient_phone" db:"stt_recipient_phone"`
		SttProductType                   string    `json:"stt_product_type" db:"stt_product_type"`
		SttOriginDistrictRate            float64   `json:"stt_origin_district_rate" db:"stt_origin_district_rate"`
		SttDestinationDistrictRate       float64   `json:"stt_destination_district_rate" db:"stt_destination_district_rate"`
		SttPublishRate                   float64   `json:"stt_publish_rate" db:"stt_publish_rate"`
		SttShippingSurchargeRate         float64   `json:"stt_shipping_surcharge_rate" db:"stt_shipping_surchage_rate"`
		SttDocumentSurchargeRate         float64   `json:"stt_document_surcharge_rate" db:"stt_document_surcharge_rate"`
		SttCommoditySurchargeRate        float64   `json:"stt_commodity_surcharge_rate" db:"stt_commodity_surcharge_rate"`
		SttHeavyweightSurchargeRate      float64   `json:"stt_heavyweight_surcharge_rate" db:"stt_heavyweight_surcharge_rate"`
		SttBMTaxRate                     float64   `json:"stt_bm_tax_rate" db:"stt_bm_tax_rate"`
		SttPPNTaxRate                    float64   `json:"stt_ppn_tax_rate" db:"stt_ppn_tax_rate"`
		SttPPHTaxRate                    float64   `json:"stt_pph_tax_rate" db:"stt_pph_tax_rate"`
		SttGrossWeight                   float64   `json:"stt_gross_weight" db:"stt_gross_weight"`
		SttVolumeWeight                  float64   `json:"stt_volume_weight" db:"stt_volume_weight"`
		SttChargeableWeight              float64   `json:"stt_chargeable_weight" db:"stt_chargeable_weight"`
		SttCommodityCode                 string    `json:"stt_commodity_code" db:"stt_commodity_code"`
		SttInsuranceType                 string    `json:"stt_insurance_type" db:"stt_insurance_type"`
		SttTotalPiece                    int       `json:"stt_total_piece" db:"stt_total_piece"`
		SttWarningStatus                 string    `json:"stt_warning_status" db:"stt_warning_status"`
		SttCounter                       int       `json:"stt_counter" db:"stt_counter"`
		SttLastStatusID                  string    `json:"stt_last_status_id" db:"stt_last_status_id"`
		SttClientSttID                   string    `json:"stt_client_stt_id" db:"stt_client_stt_id"`
		SttVendorSttID                   string    `json:"stt_vendor_stt_id" db:"stt_vendor_stt_id"`
		SttBilledTo                      string    `json:"stt_billed_to" db:"stt_billed_to"`
		SttCODAmount                     float64   `json:"stt_cod_amount" db:"stt_cod_amount"`
		SttIsCOD                         bool      `json:"stt_is_cod" db:"stt_is_cod"`
		SttIsDFOD                        bool      `json:"stt_is_dfod" db:"stt_is_dfod"`
		SttIsDO                          bool      `json:"stt_is_do" db:"stt_is_do"`
		SttMeta                          string    `json:"stt_meta" db:"stt_meta"`
		SttBookedAt                      time.Time `json:"stt_booked_at" db:"stt_booked_at"`
		SttBookedName                    string    `json:"stt_booked_name" db:"stt_booked_name"`
		SttBookedBy                      int       `json:"stt_booked_by" db:"stt_booked_by"`
		SttCreatedAt                     time.Time `json:"stt_created_at" db:"stt_created_at"`
		SttCreatedName                   string    `json:"stt_created_name" db:"stt_created_name"`
		SttCreatedBy                     int       `json:"stt_created_by" db:"stt_created_by"`
		SttUpdatedAt                     time.Time `json:"stt_updated_at" db:"stt_updated_at"`
		SttUpdatedBy                     int       `json:"stt_updated_by" db:"stt_updated_by"`
		SttUpdatedName                   string    `json:"stt_updated_name" db:"stt_updated_name"`
		ShipmentAlgoID                   string    `json:"shipment_algo_id" db:"shipment_algo_id"`
		SttPieces                        []SttPiece
	}
	ShipmentSTTDB struct {
		SttID                       *int64     `json:"stt_id" db:"id" faker:"oneof: 1, 2"`
		SttNo                       *string    `json:"stt_no" db:"stt_no"`
		SttShipmentID               *string    `json:"stt_shipment_id" db:"stt_shipment_id"`
		SttClientID                 *int       `json:"stt_client_id" db:"stt_client_id"`
		SttPosID                    *int       `json:"stt_pos_id" db:"stt_pos_id"`
		SttTaxNumber                *string    `json:"stt_tax_number" db:"stt_tax_number"`
		SttGoodsEstimatePrice       *float64   `json:"stt_goods_estimate_price" db:"stt_goods_estimate_price"`
		SttGoodsStatus              *string    `json:"stt_goods_status" db:"stt_goods_status"`
		SttTotalAmount              *float64   `json:"stt_total_amount" db:"stt_total_amount"`
		SttNoRefExternal            *string    `json:"stt_no_ref_external" db:"stt_no_ref_external"`
		SttSource                   *string    `json:"stt_source" db:"stt_source"`
		SttOriginCityID             *string    `json:"stt_origin_city_id" db:"stt_origin_city_id"`
		SttDestinationCityID        *string    `json:"stt_destination_city_id" db:"stt_destination_city_id"`
		SttOriginDistrictID         *string    `json:"stt_origin_district_id" db:"stt_origin_district_id"`
		SttDestinationDistrictID    *string    `json:"stt_destination_district_id" db:"stt_destination_district_id"`
		SttSenderName               *string    `json:"stt_sender_name" db:"stt_sender_name"`
		SttSenderPhone              *string    `json:"stt_sender_phone" db:"stt_sender_phone"`
		SttSenderAddress            *string    `json:"stt_sender_address" db:"stt_sender_address"`
		SttRecipientName            *string    `json:"stt_recipient_name" db:"stt_recipient_name"`
		SttRecipientAddress         *string    `json:"stt_recipient_address" db:"stt_recipient_address"`
		SttRecipientPhone           *string    `json:"stt_recipient_phone" db:"stt_recipient_phone"`
		SttProductType              *string    `json:"stt_product_type" db:"stt_product_type"`
		SttOriginDistrictRate       *float64   `json:"stt_origin_district_rate" db:"stt_origin_district_rate"`
		SttDestinationDistrictRate  *float64   `json:"stt_destination_district_rate" db:"stt_destination_district_rate"`
		SttPublishRate              *float64   `json:"stt_publish_rate" db:"stt_publish_rate"`
		SttShippingSurchargeRate    *float64   `json:"stt_shipping_surcharge_rate" db:"stt_shipping_surchage_rate"`
		SttDocumentSurchargeRate    *float64   `json:"stt_document_surcharge_rate" db:"stt_document_surcharge_rate"`
		SttCommoditySurchargeRate   *float64   `json:"stt_commodity_surcharge_rate" db:"stt_commodity_surcharge_rate"`
		SttHeavyweightSurchargeRate *float64   `json:"stt_heavyweight_surcharge_rate" db:"stt_heavyweight_surcharge_rate"`
		SttBMTaxRate                *float64   `json:"stt_bm_tax_rate" db:"stt_bm_tax_rate"`
		SttPPNTaxRate               *float64   `json:"stt_ppn_tax_rate" db:"stt_ppn_tax_rate"`
		SttPPHTaxRate               *float64   `json:"stt_pph_tax_rate" db:"stt_pph_tax_rate"`
		SttGrossWeight              *float64   `json:"stt_gross_weight" db:"stt_gross_weight"`
		SttVolumeWeight             *float64   `json:"stt_volume_weight" db:"stt_volume_weight"`
		SttChargeableWeight         *float64   `json:"stt_chargeable_weight" db:"stt_chargeable_weight"`
		SttCommodityCode            *string    `json:"stt_commodity_code" db:"stt_commodity_code"`
		SttInsuranceType            *string    `json:"stt_insurance_type" db:"stt_insurance_type"`
		SttTotalPiece               *int       `json:"stt_total_piece" db:"stt_total_piece"`
		SttWarningStatus            *string    `json:"stt_warning_status" db:"stt_warning_status"`
		SttCounter                  *int       `json:"stt_counter" db:"stt_counter"`
		SttLastStatusID             *string    `json:"stt_last_status_id" db:"stt_last_status_id"`
		SttClientSttID              *string    `json:"stt_client_stt_id" db:"stt_client_stt_id"`
		SttVendorSttID              *string    `json:"stt_vendor_stt_id" db:"stt_vendor_stt_id"`
		SttBilledTo                 *string    `json:"stt_billed_to" db:"stt_billed_to"`
		SttCODAmount                *float64   `json:"stt_cod_amount" db:"stt_cod_amount"`
		SttIsCOD                    *bool      `json:"stt_is_cod" db:"stt_is_cod"`
		SttIsDFOD                   *bool      `json:"stt_is_dfod" db:"stt_is_dfod"`
		SttIsDO                     *bool      `json:"stt_is_do" db:"stt_is_do"`
		SttMeta                     *string    `json:"stt_meta" db:"stt_meta"`
		SttBookedAt                 *time.Time `json:"stt_booked_at" db:"stt_booked_at"`
		SttBookedName               *string    `json:"stt_booked_name" db:"stt_booked_name"`
		SttBookedBy                 *int       `json:"stt_booked_by" db:"stt_booked_by"`
		SttCreatedAt                *time.Time `json:"stt_created_at" db:"stt_created_at"`
		SttCreatedName              *string    `json:"stt_created_name" db:"stt_created_name"`
		SttCreatedBy                *int       `json:"stt_created_by" db:"stt_created_by"`
		SttUpdatedAt                *time.Time `json:"stt_updated_at" db:"stt_updated_at"`
		SttUpdatedBy                *int       `json:"stt_updated_by" db:"stt_updated_by"`
		SttUpdatedName              *string    `json:"stt_updated_name" db:"stt_updated_name"`
		SttPieces                   []SttPiece
	}

	// ShipmentViewParams ...
	ShipmentViewParams struct {
		ShipmentAlgoID           string
		ShipmentShipmentID       string
		ShipmentBookingID        string
		ShipmentGroupBookingID   string
		ShipmentPacketID         int64
		ShipmentPacketShipmentID int64
		BasedFilter              BasedFilter
		SortBy                   string
		Status                   string
		IsNoDefaultQuery         bool
		CustomColumn             string
		IsCountData              bool
	}

	// AlgoAuthResponse ...
	AlgoAuthResponse struct {
		Token     string    `json:"token"`
		Type      string    `json:"type"`
		ExpiredAt time.Time `json:"expired_at"`
	}
	// AlgoAuthRequest ...
	AlgoAuthRequest struct {
		Username string `json:"username"`
		Password string `json:"password"`
		Role     string `json:"role"`
	}

	// ShipmentPacketViewParams ...
	ShipmentPacketViewParams struct {
		ShipmentID               string
		ShipmentPacketID         int64
		ShipmentPacketIDs        []int64
		ShipmentPacketShipmentID int64
		SttEmpty                 bool
		SttNo                    string
	}

	ShipmentAlgoData struct {
		NumberOfStt int      `json:"number_of_stt"`
		Packet      []Packet `json:"packet"`
		AgentCode   string   `json:"agent_code"`
	}

	Packet struct {
		Sender         Sender        `json:"sender"`
		Recipient      Recipient     `json:"recipient"`
		Product        string        `json:"product"`
		ServiceType    string        `json:"service_type"`
		Commodity      string        `json:"commodity"`
		IsCod          bool          `json:"is_cod"`
		IsDfod         bool          `json:"is_dfod"`
		IsWoodPacking  bool          `json:"is_wood_packing"`
		IsInsurance    bool          `json:"is_insurance"`
		GoodsValue     float64       `json:"goods_value"`
		CodValue       float64       `json:"cod_value"`
		CodFee         float64       `json:"cod_fee"`
		NoOfPieces     int64         `json:"no_of_pieces"`
		CustomerCode   string        `json:"customer_code"`
		CustomerBranch string        `json:"customer_branch"`
		Items          []Items       `json:"items"`
		ShipmentID     string        `json:"shipment_id"`
		CodHandling    string        `json:"cod_handling"`
		Label          string        `json:"label"`
		TotalTariff    float64       `json:"total_tariff"`
		Subscription   *Subscription `json:"subscription"`
	}

	Subscription struct {
		ID           int          `json:"id"`
		BenefitFlag  []string     `json:"benefit_flag"`
		ExtraBenefit ExtraBenefit `json:"extra_benefit"`
	}

	ExtraBenefit struct {
		FreeInsurance   FreeInsurance   `json:"free_insurance"`
		OnTimeGuarantee OnTimeGuarantee `json:"on_time_guarantee"`
	}

	FreeInsurance struct {
		Active   bool  `json:"active"`
		MaxPrice int64 `json:"max_price"`
	}

	OnTimeGuarantee struct {
		Active         bool   `json:"active"`
		Type           string `json:"type"`
		Value          int64  `json:"value"`
		DisburseOption string `json:"disburse_option"`
		OnTimeStatus   string `json:"on_time_status"`
	}

	Sender struct {
		Name        string `json:"name"`
		Address     string `json:"address"`
		Origin      string `json:"origin"`
		Destination string `json:"destination"`
		Phone       string `json:"phone"`
		District    string `json:"district"`
	}

	Recipient struct {
		Name        string `json:"name"`
		Address     string `json:"address"`
		Destination string `json:"destination"`
		Phone       string `json:"phone"`
		District    string `json:"district"`
		Label       string `json:"label"`
	}

	Items struct {
		Name   string  `json:"name"`
		Weight float64 `json:"weight"`
		Height float64 `json:"height"`
		Length float64 `json:"length"`
		Width  float64 `json:"width"`
	}

	ShipmentAccessWeigth struct {
		DestinationCityCode string
		OriginCityCode      string
		ShipmentID          string
	}

	ShipmentAlgoResolutionCentre struct {
		SttNo            string  `json:"stt_no"`
		ShipmentID       string  `json:"shipment_id"`
		LastStatus       string  `json:"last_status"`
		LastStatusAt     string  `json:"last_status_at"`
		CreatedAt        string  `json:"created_at"`
		SenderName       string  `json:"sender_name"`
		SenderPhone      string  `json:"sender_phone_number"`
		RecipientName    string  `json:"recipient_name"`
		RecipientPhone   string  `json:"recipient_phone_number"`
		ProductType      string  `json:"product_type"`
		DimLength        float64 `json:"dim_length"`
		DimHeight        float64 `json:"dim_height"`
		DimWidth         float64 `json:"dim_width"`
		GrossWeight      float64 `json:"gross_weight"`
		ChargeableWeight float64 `json:"chargeable_weight"`
		VolumeWeight     float64 `json:"volume_weight"`
		TotalPiece       int     `json:"total_piece"`
		IsCOD            bool    `json:"is_cod"`
		IsDFOD           bool    `json:"is_dfod"`
		IsInsurance      bool    `json:"is_insurance"`
		CodAmount        float64 `json:"cod_amount"`
		GoodsPrice       float64 `json:"goods_price"`
		CommodityCode    string  `json:"commodity_code"`
	}
	ShipmentViewAlgoByIDParams struct {
		Ctx        context.Context
		ShipmentID string
		IsKulioner bool
	}
	ShipmentViewAlgoGroupByIDParams struct {
		Ctx            context.Context
		GroupBookingID string
		IsKulioner     bool
	}
)

type AlgoResponseError struct {
	ErrorID int `json:"error_id"`
	Message struct {
		En string `json:"en"`
		ID string `json:"id"`
	} `json:"message"`
}

func (r *ShipmentMeta) ToString() string {
	b, err := json.Marshal(r)
	if err != nil {
		return ``
	}
	return string(b)
}

func (r *Packet) GetShipmentRecipientAddressType() string {
	if r.Label == "" {

		return ""
	}

	if strings.ToUpper(r.Label) == ShipmentLabelOffice {
		return AddressTypeOffice
	}

	return AddressTypeHome
}

func (c *Shipment) ShipmentMetaToStruct() *ShipmentMeta {
	res := ShipmentMeta{}
	shipmentMeta := ``
	if c.ShipmentMeta != nil {
		shipmentMeta = *c.ShipmentMeta
	}
	err := json.Unmarshal([]byte(shipmentMeta), &res)
	if err != nil {
		return nil
	}

	return &res
}

func (c *ShipmentPacket) ShipmentPacketItemsToStruct() []ShipmentPacketItemsValue {
	itemsValues := make([]ShipmentPacketItemsValue, 0)
	if err := json.Unmarshal([]byte(c.ShipmentPacketItems), &itemsValues); err != nil {
		return itemsValues
	}
	return itemsValues
}

func (c *ShipmentPacket) GetGrossWeightTotal() float64 {
	shipmentPacketItem := c.ShipmentPacketItemsToStruct()
	var totalGrossWeight float64
	if len(shipmentPacketItem) == 0 {
		return totalGrossWeight
	}
	for _, item := range shipmentPacketItem {
		totalGrossWeight += item.Weight
	}

	return math.Ceil(totalGrossWeight*100) / 100
}

func (c *Shipment) IsShipmentNotFound() bool {
	if c == nil {
		return true
	}

	if c.ShipmentPackets == nil {
		return true
	}

	if len(c.ShipmentPackets) == 0 {
		return true
	}

	return false
}

func (c *Shipment) IsShipmentBukalapakEmptyOrigin(prefix string) bool {
	if !IsShipmentBukalapak[prefix] {
		return false
	}

	if c == nil {
		return false
	}

	if c.ShipmentPackets == nil {
		return false
	}

	var senderData Sender
	err := json.Unmarshal([]byte(c.ShipmentPackets[0].ShipmentPacketSender), &senderData)
	if err != nil {
		return false
	}

	if senderData.Origin == "" {
		return true
	}

	return false
}
