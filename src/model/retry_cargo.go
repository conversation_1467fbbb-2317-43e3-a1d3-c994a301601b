package model

import (
	"encoding/json"
	"regexp"
	"strings"
	"time"

	"github.com/abiewardani/dbr/v2"
)

const (
	RETRY_BOOKING_STATUS_CANCEL                     = "cancel"
	RETRY_BOOKING_STATUS_PROCESS                    = "process"
	RETRY_BOOKING_STATUS_PROCESS_SABRE              = "process-sabre"
	RETRY_BOOKING_STATUS_DELETE                     = "delete"
	RETRY_BOOKING_STATUS_FAILED                     = "failed"
	RETRY_BOOKING_STATUS_SUCCESS                    = "success"
	RETRY_BOOKING_STATUS_PROCESS_IMMEDIATE          = "process-immediate"
	RETRY_BOOKING_STATUS_PROCESS_IMMEDIATE_TRACKING = "process-immediate-tracking"
	RETRY_BOOKING_STATUS_PROCESS_WEBHOOK            = "process-webhook"
	RETRY_BOOKING_IS_DELETED                        = true

	LionAir       = "Lion Air"
	LionAirPrefix = "JT"

	BatikAir       = "Batik Air"
	BatikAirPrefix = "ID"

	SuperAirJet       = "Super Air Jet"
	SuperAirJetPrefix = "IU"

	WingsAir       = "Wings Air"
	WingsAirPrefix = "IW"
)

var (
	IsNotValidRetryCargoForScanCargo = map[string]bool{
		RETRY_BOOKING_STATUS_PROCESS:                    true,
		RETRY_BOOKING_STATUS_PROCESS_IMMEDIATE:          true,
		RETRY_BOOKING_STATUS_PROCESS_IMMEDIATE_TRACKING: true,
	}

	prefixToAirline = map[string]string{
		LionAirPrefix:     LionAir,
		BatikAirPrefix:    BatikAir,
		SuperAirJetPrefix: SuperAirJet,
		WingsAirPrefix:    WingsAir,
	}
)

// RetryCargo ..
type RetryCargo struct {
	RcID                        int64          `json:"rc_id" db:"rc_id"`
	RcNo                        string         `json:"rc_no" db:"rc_no"`
	RcActualPiece               int            `json:"rc_actual_piece" db:"rc_actual_piece"`
	RcType                      string         `json:"rc_type" db:"rc_type"`
	RcVehicleNo                 string         `json:"rc_vehicle_no" db:"rc_vehicle_no"`
	RcOriginCityCode            string         `json:"rc_origin_city_code" db:"rc_origin_city_code"`
	RcOriginAirportCode         string         `json:"rc_origin_airport_code" db:"rc_origin_airport_code"`
	RcDestinationAirportCode    string         `json:"rc_destination_airport_code" db:"rc_destination_airport_code"`
	RcDestinationCityCode       string         `json:"rc_destination_city_code" db:"rc_destination_city_code"`
	RcRemark                    string         `json:"rc_remarks" db:"rc_remarks"`
	RcPartnerName               string         `json:"rc_partner_name" db:"rc_partner_name"`
	RcPartnerCode               string         `json:"rc_partner_code" db:"rc_partner_code"`
	RcPartnerType               string         `json:"rc_partner_type" db:"rc_partner_type"`
	RcPartnerID                 int            `json:"rc_partner_id" db:"rc_partner_id"`
	RcTotalStt                  int            `json:"rc_total_stt" db:"rc_total_stt"`
	RcTotalSttPiece             int            `json:"rc_total_stt_piece" db:"rc_total_stt_piece"`
	RcTotalGrossWeight          float64        `json:"rc_total_gross_weight" db:"rc_total_gross_weight"`
	RcTotalVolumeWeight         float64        `json:"rc_total_volume_weight" db:"rc_total_volume_weight"`
	RcTotalCustomGrossWeight    float64        `json:"rc_total_custom_gross_weight" db:"rc_total_custom_gross_weight"`
	RcTotalCustomVolumeWeight   float64        `json:"rc_total_custom_volume_weight" db:"rc_total_custom_volume_weight"`
	RcTotalEstimateGrossWeight  float64        `json:"rc_total_estimate_gross_weight" db:"rc_total_estimate_gross_weight"`
	RcTotalEstimateVolumeWeight float64        `json:"rc_total_estimate_volume_weight" db:"rc_total_estimate_volume_weight"`
	RcCargoProductTypeCode      string         `json:"rc_cargo_product_type_code" db:"rc_cargo_product_type_code"`
	RcCommodityGroupCode        string         `json:"rc_commodity_group_code" db:"rc_commodity_group_code"`
	RcEstimateDepartureDate     time.Time      `json:"rc_estimation_departure_date" db:"rc_estimation_departure_date"`
	RcDepartureDate             time.Time      `json:"rc_departure_date" db:"rc_departure_date"`
	RcArrivalDate               time.Time      `json:"rc_arrival_date" db:"rc_arrival_date"`
	RcDepartureDateNgen         *time.Time     `json:"rc_departure_date_ngen" db:"rc_departure_date_ngen"`
	RcArrivalDateNgen           *time.Time     `json:"rc_arrival_date_ngen" db:"rc_arrival_date_ngen"`
	RcIsCargoPlaneManual        bool           `json:"rc_is_cargo_plane_manual" db:"rc_is_cargo_plane_manual"`
	RcDepartureNgen             dbr.NullString `json:"rc_departure_ngen" db:"rc_departure_ngen"`
	RcArrivalNgen               dbr.NullString `json:"rc_arrival_ngen" db:"rc_arrival_ngen"`
	RcDepartureTimezoneNgen     dbr.NullString `json:"rc_departure_timezone_ngen" db:"rc_departure_timezone_ngen"`
	RcArrivalTimezoneNgen       dbr.NullString `json:"rc_arrival_timezone_ngen" db:"rc_arrival_timezone_ngen"`
	RcEstLengthDimension        float64        `json:"rc_est_length_dimension" db:"rc_est_length_dimension"`
	RcEstWidthDimension         float64        `json:"rc_est_width_dimension" db:"rc_est_width_dimension"`
	RcEstHeightDimension        float64        `json:"rc_est_height_dimension" db:"rc_est_height_dimension"`
	RcActualLengthDimension     float64        `json:"rc_actual_length_dimension" db:"rc_actual_length_dimension"`
	RcActualWidthDimension      float64        `json:"rc_actual_width_dimension" db:"rc_actual_width_dimension"`
	RcActualHeightDimension     float64        `json:"rc_actual_height_dimension" db:"rc_actual_height_dimension"`
	RcNog                       string         `json:"rc_nog" db:"rc_nog"`
	RcStatus                    string         `json:"rc_status" db:"rc_status"`
	RcIsDeleted                 bool           `json:"rc_is_deleted" db:"rc_is_deleted"`
	RcRetrying                  int            `json:"rc_retrying" db:"rc_retrying"`
	RcRequest                   string         `json:"rc_request" db:"rc_request"`
	RcResponse                  string         `json:"rc_response" db:"rc_response"`
	RcCreatedAt                 time.Time      `json:"rc_created_at" db:"rc_created_at"`
	RcCreatedBy                 int            `json:"rc_created_by" db:"rc_created_by"`
	RcCreatedName               string         `json:"rc_created_name" db:"rc_created_name"`
	RcUpdatedAt                 *time.Time     `json:"rc_updated_at" db:"rc_updated_at"`
	RcUpdatedBy                 int            `json:"rc_updated_by" db:"rc_updated_by"`
	RcUpdatedName               string         `json:"rc_updated_name" db:"rc_updated_name"`
	RcHubID                     int            `json:"rc_hub_id" db:"rc_hub_id"`
	RcHubName                   string         `json:"rc_hub_name" db:"rc_hub_name"`
	RcBookingRequestID          string         `json:"rc_booking_request_id" db:"rc_booking_request_id"`
	RcShcCode                   string         `json:"rc_shc_code" db:"rc_shc_code"`
	RcShcDescription            string         `json:"rc_shc_description" db:"rc_shc_description"`
	RcIsRetryFromRtc            bool           `json:"rc_is_retry_from_rtc" db:"rc_is_retry_from_rtc"`
	RcRtcTurnAuto               bool           `json:"rc_rtc_turn_auto" db:"rc_rtc_turn_auto"`
	RcOverCutoffLimit           bool           `json:"rc_over_cutoff_limit" db:"rc_over_cutoff_limit"`
	RcIsUseSabre                bool           `json:"rc_is_use_sabre" db:"rc_is_use_sabre"`
	RcOverCutoffLimitH0         bool           `json:"rc_over_cutoff_limit_h0" db:"rc_over_cutoff_limit_h0"`
}

func (r *RetryCargo) RcRemarkToStruct() CargoRemark {
	cr := CargoRemark{}
	json.Unmarshal([]byte(r.RcRemark), &cr)

	return cr
}

type RetryCargoWithDetail struct {
	RcID                        int                       `json:"rc_id" db:"rc_id"`
	RcNo                        dbr.NullString            `json:"rc_no" db:"rc_no"`
	RcType                      dbr.NullString            `json:"rc_type" db:"rc_type"`
	RcResponse                  dbr.NullString            `json:"-" db:"rc_response"`
	RcReasonFailed              string                    `json:"rc_reason_failed" db:"-"`
	RcActualPiece               int                       `json:"rc_actual_piece" db:"rc_actual_piece"`
	RcVehicleNo                 dbr.NullString            `json:"rc_vehicle_no" db:"rc_vehicle_no"`
	RcOriginCityCode            string                    `json:"rc_origin_city_code" db:"rc_origin_city_code"`
	RcOriginCityName            string                    `json:"rc_origin_city_name" db:"rc_origin_city_name"`
	RcOriginAirportCode         dbr.NullString            `json:"rc_origin_airport_code" db:"rc_origin_airport_code"`
	RcDestinationAirportCode    dbr.NullString            `json:"rc_destination_airport_code" db:"rc_destination_airport_code"`
	RcDestinationCityCode       string                    `json:"rc_destination_city_code" db:"rc_destination_city_code"`
	RcDestinationCityName       string                    `json:"rc_destination_city_name" db:"rc_destination_city_name"`
	RcRemark                    dbr.NullString            `json:"rc_remarks" db:"rc_remarks"`
	RcPartnerName               dbr.NullString            `json:"rc_partner_name" db:"rc_partner_name"`
	RcPartnerCode               dbr.NullString            `json:"rc_partner_code" db:"rc_partner_code"`
	RcPartnerType               dbr.NullString            `json:"rc_partner_type" db:"rc_partner_type"`
	RcPartnerID                 int                       `json:"rc_partner_id" db:"rc_partner_id"`
	RcTotalStt                  int                       `json:"rc_total_stt" db:"rc_total_stt"`
	RcTotalSttPiece             int                       `json:"rc_total_stt_piece" db:"rc_total_stt_piece"`
	RcTotalGrossWeight          float64                   `json:"rc_total_gross_weight" db:"rc_total_gross_weight"`
	RcTotalVolumeWeight         float64                   `json:"rc_total_volume_weight" db:"rc_total_volume_weight"`
	RcTotalCustomGrossWeight    float64                   `json:"rc_total_custom_gross_weight" db:"rc_total_custom_gross_weight"`
	RcTotalCustomVolumeWeight   float64                   `json:"rc_total_custom_volume_weight" db:"rc_total_custom_volume_weight"`
	RcTotalEstimateGrossWeight  float64                   `json:"rc_total_estimate_gross_weight" db:"rc_total_estimate_gross_weight"`
	RcTotalEstimateVolumeWeight float64                   `json:"rc_total_estimate_volume_weight" db:"rc_total_estimate_volume_weight"`
	RcCargoProductTypeCode      dbr.NullString            `json:"rc_cargo_product_type_code" db:"rc_cargo_product_type_code"`
	RcCommodityGroupCode        string                    `json:"rc_commodity_group_code" db:"rc_commodity_group_code"`
	RcCommodityGroupName        string                    `json:"rc_commodity_group_name" db:"rc_commodity_group_name"`
	RcEstimateDepartureDate     time.Time                 `json:"rc_estimation_departure_date" db:"rc_estimation_departure_date"`
	RcDepartureDate             time.Time                 `json:"rc_departure_date" db:"rc_departure_date"`
	RcArrivalDate               time.Time                 `json:"rc_arrival_date" db:"rc_arrival_date"`
	RcDepartureDateNgen         *time.Time                `json:"rc_departure_date_ngen" db:"rc_departure_date_ngen"`
	RcArrivalDateNgen           *time.Time                `json:"rc_arrival_date_ngen" db:"rc_arrival_date_ngen"`
	RcIsCargoPlaneManual        bool                      `json:"rc_is_cargo_plane_manual" db:"rc_is_cargo_plane_manual"`
	RcDepartureNgen             dbr.NullString            `json:"rc_departure_ngen" db:"rc_departure_ngen"`
	RcArrivalNgen               dbr.NullString            `json:"rc_arrival_ngen" db:"rc_arrival_ngen"`
	RcDepartureTimezoneNgen     dbr.NullString            `json:"rc_departure_timezone_ngen" db:"rc_departure_timezone_ngen"`
	RcArrivalTimezoneNgen       dbr.NullString            `json:"rc_arrival_timezone_ngen" db:"rc_arrival_timezone_ngen"`
	RcEstLengthDimension        float64                   `json:"rc_est_length_dimension" db:"rc_est_length_dimension"`
	RcEstWidthDimension         float64                   `json:"rc_est_width_dimension" db:"rc_est_width_dimension"`
	RcEstHeightDimension        float64                   `json:"rc_est_height_dimension" db:"rc_est_height_dimension"`
	RcActualLengthDimension     float64                   `json:"rc_actual_length_dimension" db:"rc_actual_length_dimension"`
	RcActualWidthDimension      float64                   `json:"rc_actual_width_dimension" db:"rc_actual_width_dimension"`
	RcActualHeightDimension     float64                   `json:"rc_actual_height_dimension" db:"rc_actual_height_dimension"`
	RcNog                       dbr.NullString            `json:"rc_nog" db:"rc_nog"`
	RcStatus                    dbr.NullString            `json:"rc_status" db:"rc_status"`
	RcIsDeleted                 bool                      `json:"rc_is_deleted" db:"rc_is_deleted"`
	RcRetrying                  int                       `json:"rc_retrying" db:"rc_retrying"`
	RcCreatedAt                 time.Time                 `json:"rc_created_at" db:"rc_created_at"`
	RcCreatedBy                 int                       `json:"rc_created_by" db:"rc_created_by"`
	RcCreatedName               dbr.NullString            `json:"rc_created_name" db:"rc_created_name"`
	RcUpdatedAt                 *time.Time                `json:"rc_updated_at" db:"rc_updated_at"`
	RcUpdatedBy                 int                       `json:"rc_updated_by" db:"rc_updated_by"`
	RcUpdatedName               dbr.NullString            `json:"rc_updated_name" db:"rc_updated_name"`
	RcHubID                     int                       `json:"rc_hub_id" db:"rc_hub_id"`
	RcHubName                   string                    `json:"rc_hub_name" db:"rc_hub_name"`
	RcIsUseSabre                bool                      `json:"rc_is_use_sabre" db:"rc_is_use_sabre"`
	RcDetail                    []RetryCargoDetailWithStt `json:"rc_detail"`
	RcBookingRequestID          string                    `json:"rc_booking_request_id" db:"rc_booking_request_id"`
	RcOverCutoffLimitH0         bool                      `json:"rc_over_cutoff_limit_h0" db:"rc_over_cutoff_limit_h0"`
	RcRequest                   string                    `json:"-" db:"rc_request"`
	IsFromRtc                   bool                      `json:"is_from_rtc"`
	RcShcCode                   string                    `json:"rc_shc_code"`
	RcShcDescription            string                    `json:"rc_shc_description"`
	RcCargoDimensionCustom      []DimensionCustom         `json:"rc_cargo_dimension_custom"`
	IsOverCutoffTime            bool                      `json:"is_over_cutoff_time"`
	CutoffTime                  string                    `json:"cutoff_time"`
	CutoffDate                  string                    `json:"cutoff_date"`
	FlightPlanForEcargo         []FlightPlanForEcargoV2   `json:"flight_plan_for_ecargo"`
}

type DimensionCustom struct {
	SttOrBag     string  `json:"stt_or_bag"`
	Height       float64 `json:"height"`
	Length       float64 `json:"length"`
	Width        float64 `json:"width"`
	Pieces       float64 `json:"pieces"`
	GrossWeight  float64 `json:"gross_weight"`
	VolumeWeight float64 `json:"volume_weight"`
}

type FlightPlanForEcargoV2 struct {
	DailyFlightSNo      string `json:"daily_flights_no"`
	IsRootStation       bool   `json:"is_root_station"`
	FlightNo            string `json:"flight_no"`
	FlightDate          string `json:"flight_date"`
	FlightDateTime      string `json:"flight_date_time"`
	FlightOrigin        string `json:"flight_origin"`
	FlightDestination   string `json:"flight_destination"`
	FlightArrDatetime   string `json:"flight_arr_datetime"`
	FlightDepDatetime   string `json:"flight_dep_datetime"`
	FlightThresholdTime int    `json:"flight_threshold_time"`
}

// RetryCargoDetail ..
type RetryCargoDetailWithStt struct {
	RcdID                      int     `json:"rcd_id"`
	RcdSttID                   int64   `json:"rcd_stt_id"`
	RcdSttNo                   string  `json:"rcd_stt_no"`
	RcdSttProductType          string  `json:"rcd_stt_product_type"`
	RcdsttTotalPiece           int     `json:"rcd_stt_total_piece"`
	RcdSttDestinationCityId    string  `json:"rcd_stt_destination_city_id"`
	RcdsttDestinationCityName  string  `json:"rcd_stt_destination_city_name"`
	RcdSttOriginCityId         string  `json:"rcd_stt_origin_city_id"`
	RcdSttOriginCityName       string  `json:"rcd_stt_origin_city_name"`
	RcdsttCommodityCode        string  `json:"stt_commodity_code"`
	RcdsttCommodityName        string  `json:"stt_commodity_name"`
	RcdsttChargeableWeight     float64 `json:"stt_chargeable_weight"`
	RcdBagNo                   string  `json:"rcd_bag_no"`
	RcdSttGrossWeight          float32 `json:"rcd_stt_gross_wight"`
	RcdSttVolumeWeight         float32 `json:"rcd_stt_volume_weight"`
	RcdBagCustomGrossWeight    float64 `json:"rcd_bag_custom_gross_weight"`
	RcdBookingID               string  `json:"rcd_booking_id"`
	RcdSttShipmentID           string  `json:"rcd_stt_shipment_id"`
	RcdSttNoRefExternal        string  `json:"rcd_stt_no_ref_external"`
	SttCommodityGroupCode      string  `json:"stt_commodity_group_code"`
	SttCommodityGroupName      string  `json:"stt_commodity_group_name"`
	SttCommodityShcCode        string  `json:"stt_commodity_shc_code"`
	SttCommodityShcDescription string  `json:"stt_commodity_shc_description"`
}

func (rcd *RetryCargoWithDetail) DefineCutoff(rcRequest *CreateDetailCargoRetry, rtcHistoryCreated, now time.Time) *RetryCargoWithDetail {
	if rcRequest != nil {
		cutoff, _ := time.Parse("15:04:05", rcRequest.CreateDetailCargoTrx.CuttoffTime)
		rcd.IsOverCutoffTime = !(now.Hour() < cutoff.Hour() || (now.Hour() == cutoff.Hour() && now.Minute() < cutoff.Minute()))
		rcd.CutoffTime = rcRequest.CreateDetailCargoTrx.CuttoffTime
	}
	if now.After(rtcHistoryCreated) {
		rcd.IsOverCutoffTime = true
	}
	return rcd
}

func (rcd *RetryCargoWithDetail) DefineFlightPlanForECargo(rcRequest *CreateDetailCargoRetry, isUseSabre bool) *RetryCargoWithDetail {
	rcd.FlightPlanForEcargo = make([]FlightPlanForEcargoV2, 0)
	rcd.RcIsUseSabre = isUseSabre
	if rcRequest != nil {
		if !isUseSabre && rcRequest.NgenBookingRequest != nil {
			for _, flightPlan := range rcRequest.NgenBookingRequest.FlightPlanForEcargo {
				rcd.FlightPlanForEcargo = append(rcd.FlightPlanForEcargo, flightPlan.ToV2())
			}
		}
	}
	return rcd
}

type RetryCargoResult struct {
	RetryCargo
	RetryCargoDetail
}

type RetryCargoParams struct {
	RetryCargo          RetryCargo
	RetryCargoDetail    []RetryCargoDetail
	IsAwbReserve        bool
	AwbStatus           string
	AwbID               int
	IsPublishRetryCargo bool
	RetryPubsubPayload  RetryPubsubPayload
	RtcID               int64
}

type RetryCargoLastIDParams struct {
	OriginCity  []string
	Status      []string
	HubID       int
	PartnerID   int
	PartnerType string
}

type RetryPubsubPayload struct {
	RetryID   int    `json:"retry_id"`
	AwbNo     string `json:"awb_number"`
	PublishAt string `json:"publish_at"`
	PublishBy string `json:"publish_by"`
}

type RetryCargoViewParams struct {
	FilterByRetryStatus         string `json:"filter_by_retry_status"`
	FilterByRetryIsDeletedTrue  bool   `json:"filter_by_retry_is_deleted_true"`
	FilterByRetryIsDeletedFalse bool   `json:"filter_by_retry_is_deleted_false"`
	FilterByRetryID             int    `json:"filter_by_id"`
	FilterByRcNo                string
	FilterByRcBookingRequestID  string
	CreatedAtPrevious           *time.Time
	CreatedAtNow                *time.Time
	LastReadCargo               *time.Time
	ReadCargoNow                *time.Time
	PartnerID                   int
	FilterByRetryStatusWhereIn  []string
	FilterByUpdateAtLessNow     time.Time `json:"filter_by_updated_at_less"`
	CreatedByID                 int
	UseMasterDB                 bool
	TimeDiff                    int
	RcResponseIsNull            bool
	BasedFilter
	IsEligibleArchivedType bool
}

var IsStatusProcess = map[string]bool{
	RETRY_BOOKING_STATUS_PROCESS:                    true,
	RETRY_BOOKING_STATUS_PROCESS_IMMEDIATE:          true,
	RETRY_BOOKING_STATUS_PROCESS_IMMEDIATE_TRACKING: true,
	RETRY_BOOKING_STATUS_PROCESS_WEBHOOK:            true,
}

// [{"Bookingid":"","AWBNO":"990-26027960","StatusMessage":"Incorrect Request"}]
type RetryCargoCancelResponse struct {
	BookingID     string `json:"Bookingid"`
	AwbNo         string `json:"AWBNO"`
	StatusMessage string `json:"StatusMessage"`
}

func (m RetryCargo) GenerateMessage() string {
	if m.RcStatus == RETRY_BOOKING_STATUS_SUCCESS {
		return "Berhasil Booking"
	} else if m.RcStatus == RETRY_BOOKING_STATUS_FAILED {
		rcResponse := strings.ToLower(m.RcResponse)
		if strings.Contains(rcResponse, "flight is not") || (strings.Contains(rcResponse, "complete route") && strings.Contains(rcResponse, "flight not available")) {
			return "Penerbangan tidak tersedia"
		} else if strings.Contains(rcResponse, "embargo") {
			return "Embargo Pengiriman"
		} else if strings.Contains(rcResponse, "rate") {
			return "Tarif tidak ditemukan"
		} else if strings.Contains(rcResponse, "credit") {
			return "Batas saldo Agen perlu diatur ulang"
		} else if strings.Contains(rcResponse, "bct has breached") {
			return "Penerbangan " + m.RcVehicleNo + " telah melewati batas waktu maksimum booking"
		} else if strings.Contains(rcResponse, "bookingrequestid failed") && strings.Contains(rcResponse, "with max retry attempts") {
			return "Gagal di proses oleh nGen, coba ulang nanti"
		} else if strings.Contains(rcResponse, "bookingrequestid") && strings.Contains(rcResponse, "bookingrequestid cancelled") {
			return "sudah dibatalkan Admin Konsolidator"
		} else if strings.Contains(rcResponse, "bookingrequestid") && strings.Contains(rcResponse, "awb status cancelled") {
			return "sudah dibatalkan Admin Konsolidator"
		} else {
			return "Kendala sistem"
		}
	}

	return ""
}

func (m RetryCargo) GenerateCancelMessage() string {
	rcResponse := strings.ToLower(m.RcResponse)
	if strings.Contains(rcResponse, "under process") {
		return "booking sedang di proses"
	} else if strings.Contains(rcResponse, "system busy") {
		return "sistem partner sedang sibuk"
	}

	var resps []RetryCargoCancelResponse
	json.Unmarshal([]byte(m.RcResponse), &resps)
	if len(resps) > 0 && resps[0].StatusMessage != "" {
		return resps[0].StatusMessage
	}

	return ""
}

func (m *RetryCargoWithDetail) GenerateReasonFailed() {
	if m.RcStatus.Value() == RETRY_BOOKING_STATUS_FAILED {
		rcResponse := strings.ToLower(m.RcResponse.Value())

		vehicleNo := regexp.MustCompile(`[A-Z]{2}-\d{4}`).FindString(m.RcResponse.Value())
		if vehicleNo == `` {
			vehicleNo = m.RcVehicleNo.Value()
		}

		if strings.Contains(rcResponse, "conversion failed") || strings.Contains(rcResponse, "execution timeout expired") || strings.Contains(rcResponse, "system busy") || strings.Contains(rcResponse, "rollback transaction") || strings.Contains(rcResponse, "processing another request") {

			m.RcReasonFailed = "Server pada partner kami sedang sibuk"

		} else if strings.Contains(rcResponse, "flight is not") {

			m.RcReasonFailed = "Penerbangan tidak tersedia"

		} else if strings.Contains(rcResponse, "embargo") {

			keyReason := "Reason : "
			reason := ""

			reasonIndex := strings.Index(m.RcResponse.Value(), keyReason)
			// if "Reason : " found
			if reasonIndex != -1 {
				reason = m.RcResponse.Value()[reasonIndex+len(keyReason):]
			}

			m.RcReasonFailed = "Penerbangan mengalami Embargo karena alasan [" + reason + "]"

		} else if strings.Contains(rcResponse, "rate not available") {

			m.RcReasonFailed = "Tarif kargo untuk rute pada kargo ini belum tersedia"

		} else if strings.Contains(rcResponse, "credit") {

			m.RcReasonFailed = "Keterbatasan saldo Agen"

		} else if strings.Contains(rcResponse, "bct has breached") {

			m.RcReasonFailed = "Penerbangan " + vehicleNo + " telah melewati batas waktu maksimum booking"

		} else if strings.Contains(rcResponse, "flight") && strings.Contains(rcResponse, "cancel") {

			airline := " "
			if len(vehicleNo) > 1 {
				if value, ok := prefixToAirline[vehicleNo[:2]]; ok {
					airline = " " + value + " "
				}
			}

			m.RcReasonFailed = "Penerbangan" + airline + "dengan no." + vehicleNo + " telah dibatalkan pihak bandara"

		} else if strings.Contains(rcResponse, "invalid character") {

			re := regexp.MustCompile(`'([^']*)'`)
			invalidCharacter := re.FindString(rcResponse)

			m.RcReasonFailed = "Karakter " + invalidCharacter + " tidak dimengerti sistem."

		} else if strings.Contains(rcResponse, "invalid") && strings.Contains(rcResponse, "flightdate") {

			m.RcReasonFailed = "Tanggal penerbangan tidak valid"

		} else if strings.Contains(rcResponse, "gross weight") && strings.Contains(rcResponse, "volume weight") && strings.Contains(rcResponse, "pieces") {

			m.RcReasonFailed = "Berat, volume dan koli harus lebih dari 0"

		} else if strings.Contains(rcResponse, "complete route") && strings.Contains(rcResponse, "flight not available") {

			m.RcReasonFailed = "Penerbangan tidak tersedia"

		} else if strings.Contains(rcResponse, "Failed with") && strings.Contains(rcResponse, "max retry attempts") {
			m.RcReasonFailed = "Penerbangan tidak tersedia"

		} else if strings.Contains(rcResponse, "bookingrequestid failed") && strings.Contains(rcResponse, "with max retry attempts") {
			m.RcReasonFailed = "Permintaan booking gagal diproses, coba beberapa saat lagi"

		} else if strings.Contains(rcResponse, "bookingrequestid") && strings.Contains(rcResponse, "bookingrequestid cancelled") {
			m.RcReasonFailed = "Permintaan booking gagal diproses, karena sudah dibatalkan Admin Konsolidator"

		} else if strings.Contains(rcResponse, "bookingrequestid") && strings.Contains(rcResponse, "awb status cancelled") {
			m.RcReasonFailed = "Permintaan booking gagal diproses, karena sudah dibatalkan Admin Konsolidator"
		} else {

			m.RcReasonFailed = "Kendala sistem"

		}
	} else if m.RcStatus.Value() == RETRY_BOOKING_STATUS_CANCEL {
		m.RcReasonFailed = "Booking telah dibatalkan"
	}
}

func (m *RetryCargoWithDetail) GenerateCreateDetailCargoRetry() *CreateDetailCargoRetry {
	cr := CreateDetailCargoRetry{}
	if err := json.Unmarshal([]byte(m.RcRequest), &cr); err != nil {
		return nil
	}

	return &cr
}
