package model

import "strings"

const (
	StatusKulionerORDWTC = `ORDWTC`
	StatusKulionerORDPRC = `ORDPRC`
	StatusKulionerORDBKD = `ORDBKD`
	StatusKulionerORDDLV = `ORDDLV`
	StatusKulionerORDRCV = `ORDRCV`
	StatusKulionerCNCWRF = `CNCWRF`

	// Exclude this status, only depend on StatusKulionerCNCWRF
	StatusKulionerCNCNRF = `CNCNRF`

	StatusKulionerPackageCNX = `CNX`

	ViewMerchant = "MERCHANT"
	ViewCustomer = "CUSTOMER"
)

var EligKulionerStatus = []string{StatusKulionerORDPRC, StatusKulionerORDBKD, StatusKulionerORDDLV, StatusKulionerORDRCV, StatusKulionerCNCWRF}
var EligHistoriesKulionerStatus = []string{StatusKulionerORDWTC, StatusKulionerORDPRC, StatusKulionerORDBKD, StatusKulionerORDDLV, StatusKulionerORDRCV, StatusKulionerCNCWRF, StatusKulionerCNCNRF}

type AlgoPosListOrderRequest struct {
	MerchantCode    string `query:"merchant_code"`
	Status          string `query:"status"`
	OrPackageStatus string `query:"or_package_status"`
	Q               string `query:"q"`
	QType           string `query:"q_type"`
	StartDate       string `query:"start_date"`
	EndDate         string `query:"end_date"`
	RangeBy         string `query:"range_by"`
	Page            string `query:"page"`
	Limit           string `query:"limit"`
	ViewAs          string `query:"view_as"`
	IsWithProduct   string `query:"is_with_product"`

	PartnerID int `query:"-"`
	Token     string
}

type (
	AlgoPosListOrderResponse struct {
		AlgoPostListOrderResponseMeta struct {
			Page         int `json:"page"`
			Limit        int `json:"limit"`
			TotalRecords int `json:"total_records"`
		} `json:"meta"`
		Orders []AlgoPosListOrderDetail `json:"orders"`
	}

	AlgoPosListProductDetail struct {
		ID                  int      `json:"id"`
		ProductID           int      `json:"product_id"`
		Name                string   `json:"name"`
		ImageURLs           []string `json:"image_urls"`
		PriceBeforeDiscount float64  `json:"price_before_discount"`
		PriceAfterDiscount  float64  `json:"price_after_discount"`
		Quantity            int      `json:"quantity"`
		BuyPrice            float64  `json:"buy_price"`
		TotalPrice          float64  `json:"total_price"`
	}

	AlgoPosListHistoryDetail struct {
		ID        int    `json:"id"`
		OrderID   int    `json:"order_id"`
		OldStatus string `json:"old_status"`
		NewStatus string `json:"new_status"`
		CreatedAt string `json:"created_at"`
		CreatedBy string `json:"created_by"`
	}

	AlgoPosListOrderDetail struct {
		ID                         int     `json:"id"`
		OrderID                    string  `json:"order_id"`
		AccountID                  int     `json:"account_id"`
		Status                     string  `json:"status"`
		IsFreeInsurance            bool    `json:"is_free_insurance"`
		ServiceFeeBeforeDiscount   float64 `json:"service_fee_before_discount"`
		ServiceFeeAfterDiscount    float64 `json:"service_fee_after_discount"`
		PackagingFeeBeforeDiscount float64 `json:"packaging_fee_before_discount"`
		PackagingFeeAfterDiscount  float64 `json:"packaging_fee_after_discount"`
		CancelledReason            string  `json:"cancelled_reason"`
		InvoiceID                  string  `json:"invoice_id"`
		InvoiceStatus              string  `json:"invoice_status"`
		ProductType                string  `json:"product_type"`
		ShipmentID                 string  `json:"shipment_id"`
		SttNo                      string  `json:"stt_no"`
		PackageStatus              string  `json:"package_status"`
		MerchantID                 int     `json:"merchant_id"`
		MerchantName               string  `json:"merchant_name"`
		MerchantMitraID            int     `json:"merchant_mitra_id"`
		MerchantCode               string  `json:"merchant_code"`
		MerchantPhoneNumber        string  `json:"merchant_phone_number"`
		MerchantAddress            string  `json:"merchant_address"`
		MerchantLocation           string  `json:"merchant_location"`

		CreatedAt string `json:"created_at"`
		UpdatedAt string `json:"updated_at"`
		DeletedAt string `json:"deleted_at"`
		DeletedBy string `json:"deleted_by"`

		PaidAt                  string `json:"paid_at"`
		ProcessedAt             string `json:"processed_at"`
		DelayedAt               string `json:"delayed_at"`
		DeliveredAt             string `json:"delivered_at"`
		ReceivedAt              string `json:"received_at"`
		CanceledWithoutRefundAt string `json:"canceled_without_refund_at"`
		PaymentExpiredAt        string `json:"payment_expired_at"`
		CanceledWithRefundAt    string `json:"canceled_with_refund_at"`

		TotalProduct         int                        `json:"total_product"`
		TotalQuantity        int                        `json:"total_quantity"`
		GrandTotalPrice      float64                    `json:"grand_total_price"`
		RecipientName        string                     `json:"recipient_name"`
		RecipientPhoneNumber string                     `json:"recipient_phone_number"`
		RecipientAddress     string                     `json:"recipient_address"`
		IsMarkProcessed      bool                       `json:"is_mark_processed"`
		Products             []AlgoPosListProductDetail `json:"products"`
	}
	AlgoPosMarkAsProcessRequest struct {
		ID int64 `query:"id" json:"id"`

		PartnerID int
		Token     string `json:"-"`
	}
	AlgoPosMarkAsProcessParam struct {
		MerchantCode string `query:"merchant_code" json:"merchant_code"`
		ID           int64  `query:"id" json:"id"`
	}
	AlgoPosMarkAsProcessResponse struct {
		IsMarkAsProcessed bool `json:"is_mark_processed"`
	}
)

type (
	AlgoPosViewOrderRequest struct {
		ID           int    `json:"id"`
		MerchantCode string `query:"merchant_code"`
		PartnerID    int    `json:"partner_id"`
		Token        string `query:"-"`
	}

	AlgoPosViewOrderResponse struct {
		Order     AlgoPosListOrderDetail     `json:"order"`
		Products  []AlgoPosListProductDetail `json:"products"`
		Histories []AlgoPosListHistoryDetail `json:"histories"`
	}

	AlgoPosViewOrderV2Response struct {
		Order     AlgoPosViewOrderV2Detail   `json:"order"`
		Histories []AlgoPosListHistoryDetail `json:"histories"`
	}
)

type ProccessOrderRequest struct {
	IDs []int64 `form:"id" json:"id"`

	PartnerID int
	Token     string
}

type ProccessOrderParams struct {
	MerchantCode string  `form:"merchant_code" json:"merchant_code"`
	IDs          []int64 `form:"id" json:"id"`
}

type (
	AlgoPosSummaryStatusRequest struct {
		MerchantCode    string `query:"merchant_code"`
		Status          string `query:"status"`
		OrPackageStatus string `query:"or_package_status"`
		Q               string `query:"q"`
		QType           string `query:"q_type"`
		StartDate       string `query:"start_date"`
		EndDate         string `query:"end_date"`
		RangeBy         string `query:"range_by"`
		Page            string `query:"page"`
		Limit           string `query:"limit"`

		PartnerID int `query:"-"`
		Token     string
	}

	AlgoPosSummaryStatusResponse struct {
		Result struct {
			ORDWTC int `json:"ORDWTC"`
			ORDPRC int `json:"ORDPRC"`
			ORDBKD int `json:"ORDBKD"`
			ORDDLV int `json:"ORDDLV"`
			ORDRCV int `json:"ORDRCV"`
			CNCWRF int `json:"CNCWRF"`
			CNCNRF int `json:"CNCNRF"`
		} `json:"result"`
	}
	AlgoPosListOrderV2Response struct {
		AlgoPostListOrderResponseMeta struct {
			Page         int `json:"page"`
			Limit        int `json:"limit"`
			TotalRecords int `json:"total_records"`
		} `json:"meta"`
		Orders []AlgoPosListOrderV2Detail `json:"orders"`
	}
	AlgoPosListOrderV2Detail struct {
		ID                   int64                      `json:"id"`
		Status               string                     `json:"status"`
		OrderID              string                     `json:"order_id"`
		OrderShipmentID      string                     `json:"order_shipment_id"`
		OrderSttNo           string                     `json:"order_stt_no"`
		IsMarkProcessed      bool                       `json:"is_mark_processed"`
		CreatedAt            string                     `json:"created_at"`
		UpdatedAt            string                     `json:"updated_at"`
		PaidAt               string                     `json:"paid_at"`
		RecipientName        string                     `json:"recipient_name"`
		RecipientPhoneNumber string                     `json:"recipient_phone_number"`
		RecipientAddress     string                     `json:"recipient_address"`
		ProductType          string                     `json:"product_type"`
		TotalMerchant        int64                      `json:"total_merchant"`
		TotalPrice           float64                    `json:"total_price"`
		PackageStatus        string                     `json:"package_status"`
		Merchants            []AlgoPosListOrderMerchant `json:"merchants"`
	}
	AlgoPosListOrderMerchant struct {
		Name          string  `json:"name"`
		PhoneNumber   string  `json:"phone_number"`
		Address       string  `json:"address"`
		Thumbnail     string  `json:"thumbnail"`
		LatLon        string  `json:"lat_lon"`
		TotalProduct  int64   `json:"total_product"`
		TotalQuantity int64   `json:"total_quantity"`
		TotalPrice    float64 `json:"total_price"`
		TotalWeight   float64 `json:"total_weight"`

		MerchantProducts []AlgoPosProductDetail `json:"products"`
	}
	AlgoPosProductDetail struct {
		ID           int64   `json:"id"`
		Name         string  `json:"name"`
		Thumbnail    string  `json:"thumbnail"`
		Quantity     int64   `json:"quantity"`
		Price        float64 `json:"price"`
		TotalPrice   float64 `json:"total_price"`
		Note         string  `json:"note"`
		HandlingType string  `json:"handling_type"`
	}
	AlgoPosListOrderMerchantWithProducts struct {
		Name          string                 `json:"name"`
		PhoneNumber   string                 `json:"phone_number"`
		Address       string                 `json:"address"`
		Thumbnail     string                 `json:"thumbnail"`
		LatLon        string                 `json:"lat_lon"`
		TotalProduct  int64                  `json:"total_product"`
		TotalQuantity int64                  `json:"total_quantity"`
		TotalPrice    float64                `json:"total_price"`
		TotalWeight   float64                `json:"total_weight"`
		Products      []AlgoPosProductDetail `json:"products"`
	}
	AlgoPosViewOrderV2Detail struct {
		ID                   int64                                  `json:"id"`
		Status               string                                 `json:"status"`
		OrderID              string                                 `json:"order_id"`
		OrderShipmentID      string                                 `json:"order_shipment_id"`
		OrderSttNo           string                                 `json:"order_stt_no"`
		IsMarkProcessed      bool                                   `json:"is_mark_processed"`
		CreatedAt            string                                 `json:"created_at"`
		UpdatedAt            string                                 `json:"updated_at"`
		RecipientName        string                                 `json:"recipient_name"`
		RecipientPhoneNumber string                                 `json:"recipient_phone_number"`
		RecipientAddress     string                                 `json:"recipient_address"`
		ProductType          string                                 `json:"product_type"`
		TotalMerchant        int64                                  `json:"total_merchant"`
		TotalPrice           float64                                `json:"total_price"`
		Merchants            []AlgoPosListOrderMerchantWithProducts `json:"merchants"`
	}
)

func (a *AlgoPosListOrderDetail) StatusCNCNRFToCNCWRF() string {
	if strings.EqualFold(a.Status, StatusKulionerCNCNRF) {
		return StatusKulionerCNCWRF
	}
	return a.Status
}

func (a *AlgoPosViewOrderV2Detail) StatusCNCNRFToCNCWRF() string {
	if strings.EqualFold(a.Status, StatusKulionerCNCNRF) {
		return StatusKulionerCNCWRF
	}
	return a.Status
}

func (a *AlgoPosSummaryStatusResponse) CNCNRFAddToCNCWRF() {
	a.Result.CNCWRF += a.Result.CNCNRF
}

func (a *AlgoPosListOrderRequest) IsContainStatus(status string) bool {
	if a.Status == `` {
		return false
	}
	statuses := strings.Split(a.Status, ",")
	for _, s := range statuses {
		if strings.EqualFold(s, status) {
			return true
		}
	}
	return false
}

func (a *AlgoPosSummaryStatusRequest) IsContainStatus(status string) bool {
	if a.Status == `` {
		return false
	}
	statuses := strings.Split(a.Status, ",")
	for _, s := range statuses {
		if strings.EqualFold(s, status) {
			return true
		}
	}
	return false
}
func (a *AlgoPosListHistoryDetail) NewStatusCNCNRFToCNCWRF() string {
	if strings.EqualFold(a.NewStatus, StatusKulionerCNCNRF) {
		return StatusKulionerCNCWRF
	}
	return a.NewStatus
}
