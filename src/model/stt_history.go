package model

import (
	"encoding/json"
	"fmt"
	"strings"
	"time"

	json2 "github.com/Lionparcel/hydra/shared/json"

	"github.com/abiewardani/dbr/v2"

	"github.com/Lionparcel/hydra/config/elastic"
)

// SttPieceHistory ...
type SttPieceHistory struct {
	HistoryID       int    `json:"history_id" db:"history_id" faker:"oneof: 1, 2"`
	SttPieceID      int64  `json:"stt_piece_id" db:"stt_piece_id"`
	HistoryStatus   string `json:"history_status" db:"history_status"`
	HistoryLocation string `json:"history_location" db:"history_location"`
	HistoryMeta     string `json:"history_meta" db:"history_meta"`

	HistoryActorID   int    `json:"history_actor_id" db:"history_actor_id"`
	HistoryActorName string `json:"history_actor_name" db:"history_actor_name"`
	HistoryActorRole string `json:"history_actor_role" db:"history_actor_role"`
	HistoryRemark    string `json:"history_remarks" db:"history_remarks"`
	HistoryReffID    int    `json:"history_reff_id" db:"history_reff_id"`

	HistoryReason       string `json:"history_reason" db:"history_reason"`
	HistoryRecieverName string `json:"history_receiver_name" db:"history_receiver_name"`
	HistoryAttactment   string `json:"history_attachment" db:"history_attachment"`

	HistoryCreatedBy   int       `json:"history_created_by" db:"history_created_by"`
	HistoryCreatedName string    `json:"history_created_name" db:"history_created_name"`
	HistoryCreatedAt   time.Time `json:"history_created_at" db:"history_created_at"`

	// helper
	HistoryReasonOrigin string `json:"-" db:"-"`
}

type SttPieceHistoryOnlyIDAndStatus struct {
	HistoryID     int    `json:"history_id" db:"history_id" faker:"oneof: 1, 2"`
	HistoryStatus string `json:"history_status" db:"history_status"`
}

type MisRoute struct {
	HistoryID       int    `json:"history_id" db:"history_id" faker:"oneof: 1, 2"`
	SttPieceID      int64  `json:"stt_piece_id" db:"stt_piece_id"`
	HistoryStatus   string `json:"history_status" db:"history_status"`
	HistoryLocation string `json:"history_location" db:"history_location"`
	HistoryMeta     string `json:"history_meta" db:"history_meta"`

	HistoryActorID   int    `json:"history_actor_id" db:"history_actor_id"`
	HistoryActorName string `json:"history_actor_name" db:"history_actor_name"`
	HistoryActorRole string `json:"history_actor_role" db:"history_actor_role"`
	HistoryRemark    string `json:"history_remarks" db:"history_remarks"`
	HistoryReffID    int    `json:"history_reff_id" db:"history_reff_id"`

	HistoryReason       string `json:"history_reason" db:"history_reason"`
	HistoryRecieverName string `json:"history_receiver_name" db:"history_receiver_name"`
	HistoryAttactment   string `json:"history_attachment" db:"history_attachment"`

	HistoryCreatedBy   int       `json:"history_created_by" db:"history_created_by"`
	HistoryCreatedName string    `json:"history_created_name" db:"history_created_name"`
	HistoryCreatedAt   time.Time `json:"history_created_at" db:"history_created_at"`

	SttID                 int64   `json:"stt_id" db:"id" faker:"oneof: 1, 2"`
	SttNo                 string  `json:"stt_no" db:"stt_no"`
	SttShipmentID         string  `json:"stt_shipment_id" db:"stt_shipment_id"`
	SttClientID           int     `json:"stt_client_id" db:"stt_client_id"`
	SttPosID              int     `json:"stt_pos_id" db:"stt_pos_id"`
	SttTaxNumber          string  `json:"stt_tax_number" db:"stt_tax_number"`                     // here
	SttGoodsEstimatePrice float64 `json:"stt_goods_estimate_price" db:"stt_goods_estimate_price"` // here
	SttGoodsStatus        string  `json:"stt_goods_status" db:"stt_goods_status"`                 // here
	SttTotalAmount        float64 `json:"stt_total_amount" db:"stt_total_amount"`
	SttNoRefExternal      string  `json:"stt_no_ref_external" db:"stt_no_ref_external"` // here
	SttSource             string  `json:"stt_source" db:"stt_source"`                   // here

	SttOriginCityID          string `json:"stt_origin_city_id" db:"stt_origin_city_id"`
	SttDestinationCityID     string `json:"stt_destination_city_id" db:"stt_destination_city_id"`
	SttOriginDistrictID      string `json:"stt_origin_district_id" db:"stt_origin_district_id"`
	SttDestinationDistrictID string `json:"stt_destination_district_id" db:"stt_destination_district_id"`

	SttSenderName    string `json:"stt_sender_name" db:"stt_sender_name"`
	SttSenderPhone   string `json:"stt_sender_phone" db:"stt_sender_phone"`
	SttSenderAddress string `json:"stt_sender_address" db:"stt_sender_address"`

	SttRecipientName    string `json:"stt_recipient_name" db:"stt_recipient_name"`
	SttRecipientAddress string `json:"stt_recipient_address" db:"stt_recipient_address"`
	SttRecipientPhone   string `json:"stt_recipient_phone" db:"stt_recipient_phone"`

	SttProductType string `json:"stt_product_type" db:"stt_product_type"`

	SttOriginDistrictRate      float64 `json:"stt_origin_district_rate" db:"stt_origin_district_rate"`
	SttDestinationDistrictRate float64 `json:"stt_destination_district_rate" db:"stt_destination_district_rate"`

	SttPublishRate           float64 `json:"stt_publish_rate" db:"stt_publish_rate"`
	SttShippingSurchargeRate float64 `json:"stt_shipping_surcharge_rate" db:"stt_shipping_surchage_rate"`
	SttDocumentSurchargeRate float64 `json:"stt_document_surcharge_rate" db:"stt_document_surcharge_rate"` // here

	SttCommoditySurchargeRate   float64 `json:"stt_commodity_surcharge_rate" db:"stt_commodity_surcharge_rate"`     // here
	SttHeavyweightSurchargeRate float64 `json:"stt_heavyweight_surcharge_rate" db:"stt_heavyweight_surcharge_rate"` // here

	SttBMTaxRate  float64 `json:"stt_bm_tax_rate" db:"stt_bm_tax_rate"`   // here
	SttPPNTaxRate float64 `json:"stt_ppn_tax_rate" db:"stt_ppn_tax_rate"` // here
	SttPPHTaxRate float64 `json:"stt_pph_tax_rate" db:"stt_pph_tax_rate"` // here

	SttGrossWeight      float64 `json:"stt_gross_weight" db:"stt_gross_weight"`
	SttVolumeWeight     float64 `json:"stt_volume_weight" db:"stt_volume_weight"`
	SttChargeableWeight float64 `json:"stt_chargeable_weight" db:"stt_chargeable_weight"`

	SttCommodityCode string `json:"stt_commodity_code" db:"stt_commodity_code"`
	SttCommodityID   int    `json:"stt_commodity_id,omitempty"`
	SttInsuranceType string `json:"stt_insurance_type" db:"stt_insurance_type"`
	SttTotalPiece    int    `json:"stt_total_piece" db:"stt_total_piece"`
	SttWarningStatus string `json:"stt_warning_status" db:"stt_warning_status"`
	SttCounter       int    `json:"stt_counter" db:"stt_counter"`
	SttLastStatusID  string `json:"stt_last_status_id" db:"stt_last_status_id"`
	SttClientSttID   string `json:"stt_client_stt_id" db:"stt_client_stt_id"`
	SttVendorSttID   string `json:"stt_vendor_stt_id" db:"stt_vendor_stt_id"`
	SttBilledTo      string `json:"stt_billed_to" db:"stt_billed_to"`

	SttCODAmount float64 `json:"stt_cod_amount" db:"stt_cod_amount"`
	SttCODFee    float64 `json:"stt_cod_fee" db:"stt_cod_fee"`
	SttIsCOD     bool    `json:"stt_is_cod" db:"stt_is_cod"` // here
	SttIsDO      bool    `json:"stt_is_do" db:"stt_is_do"`   // here

	SttMeta          string `json:"stt_meta" db:"stt_meta"`
	SttTroubleStatus string `json:"stt_trouble_status" db:"stt_trouble_status"`

	SttBookedAt     time.Time `json:"stt_booked_at" db:"stt_booked_at"` // here
	SttBookedName   string    `json:"stt_booked_name" db:"stt_booked_name"`
	SttBookedByType string    `json:"stt_booked_by_type" db:"stt_booked_by_type"`
	SttBookedBy     int       `json:"stt_booked_by" db:"stt_booked_by"`

	SttCreatedAt   time.Time `json:"stt_created_at" db:"stt_created_at"`
	SttCreatedName string    `json:"stt_created_name" db:"stt_created_name"` // here
	SttCreatedBy   int       `json:"stt_created_by" db:"stt_created_by"`     // here
	SttUpdatedAt   time.Time `json:"stt_updated_at" db:"stt_updated_at"`
	SttUpdatedBy   int       `json:"stt_updated_by" db:"stt_updated_by"`     // here
	SttUpdatedName string    `json:"stt_updated_name" db:"stt_updated_name"` // here

	// to save latest updated by client
	SttUpdatedActorID   dbr.NullInt64  `json:"stt_updated_actor_id" db:"stt_updated_actor_id"`
	SttUpdatedActorRole dbr.NullString `json:"stt_updated_actor_role" db:"stt_updated_actor_role"`
	SttUpdatedActorName dbr.NullString `json:"stt_updated_actor_name" db:"stt_updated_actor_name"`

	ShipmentAlgoID                string `json:"shipment_algo_id" db:"shipment_algo_id"`
	SttRoute                      string `json:"stt_route" db:"stt_route"`
	SttDeliveryAttempt            int    `json:"stt_delivery_attempt" db:"stt_delivery_attempt"`
	SttHeavyweightSurchargeRemark string `json:"stt_heavyweight_surcharge_remark" db:"stt_heavyweight_surcharge_remark"`

	SttNextCommodity string         `json:"stt_next_commodity" db:"stt_next_commodity"`
	SttPiecePerPack  int            `json:"stt_piece_per_pack" db:"stt_piece_per_pack"`
	SttElexysNo      dbr.NullString `json:"se_elexys_stt_no" db:"se_elexys_stt_no"`

	SttCommodityName               string `json:"stt_commodity_name" db:"stt_commodity_name"`
	SttCommodityHsCode             string `json:"stt_commodity_hs_code" db:"stt_commodity_hs_code"`
	SttOriginCityName              string `json:"stt_origin_city_name" db:"stt_origin_city_name"`
	SttDestinationCityName         string `json:"stt_destination_city_name" db:"stt_destination_city_name"`
	SttOriginDistrictName          string `json:"stt_origin_district_name" db:"stt_origin_district_name"`
	SttOriginDistrictUrsaCode      string `json:"stt_origin_district_ursa_code" db:"stt_origin_district_ursa_code"`
	SttDestinationDistrictName     string `json:"stt_destination_district_name" db:"stt_destination_district_name"`
	SttDestinationDistrictUrsaCode string `json:"stt_destination_district_ursa_code" db:"stt_destination_district_ursa_code"`
	SttBookedByCode                string `json:"stt_booked_by_code" db:"stt_booked_by_code"`
	SttBookedForID                 int    `json:"stt_booked_for_id" db:"stt_booked_for_id"`
	SttBookedForName               string `json:"stt_booked_for_name" db:"stt_booked_for_name"`
	SttBookedForCode               string `json:"stt_booked_for_code" db:"stt_booked_for_code"`
	SttBookedForType               string `json:"stt_booked_for_type" db:"stt_booked_for_type"`

	SttPieceSttID        int64   `json:"stt_id" db:"stt_id"`
	SttPieceLength       float64 `json:"stt_piece_length" db:"stt_piece_length"`
	SttPieceWidth        float64 `json:"stt_piece_width" db:"stt_piece_width"`
	SttPieceHeight       float64 `json:"stt_piece_height" db:"stt_piece_height"`
	SttPieceGrossWeight  float64 `json:"stt_piece_gross_weight" db:"stt_piece_gross_weight"`
	SttPieceVolumeWeight float64 `json:"stt_piece_volume_weight" db:"stt_piece_volume_weight"`
	SttPieceNo           int     `json:"stt_piece_no" db:"stt_piece_no"`
	SttPieceLastStatusID string  `json:"stt_piece_last_status_id" db:"stt_piece_last_status_id"`
}

type SttPieceHistoryCustom struct {
	HistoryID       int    `json:"history_id" db:"history_id" faker:"oneof: 1, 2"`
	SttPieceID      int64  `json:"stt_piece_id" db:"stt_piece_id"`
	HistoryStatus   string `json:"history_status" db:"history_status"`
	HistoryLocation string `json:"history_location" db:"history_location"`
	HistoryMeta     string `json:"history_meta" db:"history_meta"`

	HistoryActorID   int    `json:"history_actor_id" db:"history_actor_id"`
	HistoryActorName string `json:"history_actor_name" db:"history_actor_name"`
	HistoryActorRole string `json:"history_actor_role" db:"history_actor_role"`
	HistoryRemark    string `json:"history_remarks" db:"history_remarks"`
	HistoryReffID    int    `json:"history_reff_id" db:"history_reff_id"`

	HistoryReason       string `json:"history_reason" db:"history_reason"`
	HistoryRecieverName string `json:"history_receiver_name" db:"history_receiver_name"`
	HistoryAttactment   string `json:"history_attachment" db:"history_attachment"`

	HistoryCreatedBy   int       `json:"history_created_by" db:"history_created_by"`
	HistoryCreatedName string    `json:"history_created_name" db:"history_created_name"`
	HistoryCreatedAt   time.Time `json:"history_created_at" db:"history_created_at"`
	SttNo              string    `json:"stt_no" db:"stt_no"`
}

// Template ..
func (c *SttPieceHistory) Template() *elastic.DynamicTemplate {
	mappings := elastic.NewMappings().
		AddDynamicTemplate("history_id_field", elastic.MatchConditions{
			Match:            "history_id",
			MatchMappingType: "double",
			Mapping: elastic.MatchMapping{
				Type: "double",
			},
		}).
		AddDynamicTemplate("stt_piece_id_field", elastic.MatchConditions{
			Match:            "stt_piece_id",
			MatchMappingType: "double",
			Mapping: elastic.MatchMapping{
				Type: "double",
			},
		}).
		AddDynamicTemplate("history_status_field", elastic.MatchConditions{
			Match:            "history_status",
			MatchMappingType: "string",
			Mapping: elastic.MatchMapping{
				Type: "text",
				Fields: map[string]elastic.Field{
					"keyword": {
						Type:        "keyword",
						IgnoreAbove: 256,
					},
				},
			},
		}).
		AddDynamicTemplate("history_location_field", elastic.MatchConditions{
			Match:            "history_location",
			MatchMappingType: "string",
			Mapping: elastic.MatchMapping{
				Type: "text",
				Fields: map[string]elastic.Field{
					"keyword": {
						Type:        "keyword",
						IgnoreAbove: 256,
					},
				},
			},
		}).
		AddDynamicTemplate("history_meta_field", elastic.MatchConditions{
			Match:            "history_meta",
			MatchMappingType: "string",
			Mapping: elastic.MatchMapping{
				Type: "text",
				Fields: map[string]elastic.Field{
					"keyword": {
						Type:        "keyword",
						IgnoreAbove: 256,
					},
				},
			},
		}).
		AddDynamicTemplate("history_actor_id_field", elastic.MatchConditions{
			Match:            "history_actor_id",
			MatchMappingType: "double",
			Mapping: elastic.MatchMapping{
				Type: "double",
			},
		}).
		AddDynamicTemplate("history_actor_name_field", elastic.MatchConditions{
			Match:            "history_actor_name",
			MatchMappingType: "string",
			Mapping: elastic.MatchMapping{
				Type: "text",
				Fields: map[string]elastic.Field{
					"keyword": {
						Type:        "keyword",
						IgnoreAbove: 256,
					},
				},
			},
		}).
		AddDynamicTemplate("history_actor_role_field", elastic.MatchConditions{
			Match:            "history_actor_role",
			MatchMappingType: "string",
			Mapping: elastic.MatchMapping{
				Type: "text",
				Fields: map[string]elastic.Field{
					"keyword": {
						Type:        "keyword",
						IgnoreAbove: 256,
					},
				},
			},
		}).
		AddDynamicTemplate("history_remarks_field", elastic.MatchConditions{
			Match:            "history_remarks",
			MatchMappingType: "string",
			Mapping: elastic.MatchMapping{
				Type: "text",
				Fields: map[string]elastic.Field{
					"keyword": {
						Type:        "keyword",
						IgnoreAbove: 256,
					},
				},
			},
		}).
		AddDynamicTemplate("history_reff_id_field", elastic.MatchConditions{
			Match:            "history_reff_id",
			MatchMappingType: "double",
			Mapping: elastic.MatchMapping{
				Type: "double",
			},
		}).
		AddDynamicTemplate("history_receiver_name_field", elastic.MatchConditions{
			Match:            "history_receiver_name",
			MatchMappingType: "string",
			Mapping: elastic.MatchMapping{
				Type: "text",
				Fields: map[string]elastic.Field{
					"keyword": {
						Type:        "keyword",
						IgnoreAbove: 256,
					},
				},
			},
		}).
		AddDynamicTemplate("history_reason_field", elastic.MatchConditions{
			Match:            "history_reason",
			MatchMappingType: "string",
			Mapping: elastic.MatchMapping{
				Type: "text",
				Fields: map[string]elastic.Field{
					"keyword": {
						Type:        "keyword",
						IgnoreAbove: 256,
					},
				},
			},
		}).
		AddDynamicTemplate("history_attachment_field", elastic.MatchConditions{
			Match:            "history_attachment",
			MatchMappingType: "string",
			Mapping: elastic.MatchMapping{
				Type: "text",
				Fields: map[string]elastic.Field{
					"keyword": {
						Type:        "keyword",
						IgnoreAbove: 256,
					},
				},
			},
		}).
		AddDynamicTemplate("history_created_by_field", elastic.MatchConditions{
			Match:            "history_created_by",
			MatchMappingType: "double",
			Mapping: elastic.MatchMapping{
				Type: "double",
			},
		}).
		AddDynamicTemplate("history_created_name", elastic.MatchConditions{
			Match:            "history_created_name",
			MatchMappingType: "string",
			Mapping: elastic.MatchMapping{
				Type: "text",
				Fields: map[string]elastic.Field{
					"keyword": {
						Type:        "keyword",
						IgnoreAbove: 256,
					},
				},
			},
		}).
		AddDynamicTemplate("history_created_at_field", elastic.MatchConditions{
			Match:            "history_created_at",
			MatchMappingType: "string",
			Mapping: elastic.MatchMapping{
				Type: "text",
				Fields: map[string]elastic.Field{
					"keyword": {
						Type:        "keyword",
						IgnoreAbove: 256,
					},
				},
			},
		})

	return &elastic.DynamicTemplate{
		Settings: map[string]interface{}{
			"index.refresh_interval": "1s",
		},
		Mappings: mappings,
	}
}

// SttPieceHistoryViewParam ...
type SttPieceHistoryViewParam struct {
	SttPieceHistoryID                int
	SttPieceHistorySttPieceID        int64
	SttPieceHistorySttPieceIDWhereIn []int64
	SttID                            int64
	StartDate                        string
	EndDate                          string
	SttPieceHistoryStatus            string
	SttPieceHistoryStatusWhereIn     []string
	Order                            bool
	OrderDesc                        bool
	Limit                            uint64
	SttNo                            string
	HistoryLocation                  string
	IsJoinWithSttPiece               bool
	SttNoIn                          []string
	SortBy                           string
	IsFromMaster                     bool
	EndDateOnly                      time.Time
}

type MisRouteViewParam struct {
	StartDate             string
	EndDate               string
	SttPieceHistoryStatus string
	OriginCityID          string
	DestinationCityID     string
	MissRouteCityID       string
}

func GenerateStatusSttHistory(status, name, actor, actorExternalCode string, remark RemarkPieceHistory, productType string, countryName, accountType, reason string, customProcessRemark, cityNameINTHND string, districtNameINTHND string, remarkOCCEXP string, actorRole, countryNameHistory string, historyReason string, reasonMaskingDex string) string {
	var statusHistory string
	switch status {
	case BKD:
		statusHistory = fmt.Sprintf(`BOOKED BY LION PARCEL %s.`, actor)
	case PUP, PUPC:
		statusHistory = fmt.Sprintf(`PUP FROM LION PARCEL %s BY %s.`, actor, remark.DriverName)
	case STISC:
		statusHistory = fmt.Sprintf(`STI-SC AT %s.`, actor)
	case STI:
		statusHistory = fmt.Sprintf(`STI AT %s.`, actor)
	case BAGGING:
		statusHistory = fmt.Sprintf(`CONS GENERATION. [%s - CONS GENERATION]`, remark.BagNumber)
	case CARGOPLANE:
		statusHistory = fmt.Sprintf(`Paketmu akan diterbangkan dengan pesawat dari Kota %s, %s`, cityNameINTHND, districtNameINTHND)
	case CARGOTRUCK:
		if productType == INTERPACK {
			statusHistory = fmt.Sprintf(`Paketmu akan diberangkatkan ke Negara %s dari Kota %s, %s`, countryName, remark.HistoryLocationName, remark.HistoryDistrictName)
		} else {
			statusHistory = fmt.Sprintf(`[TRUCK NO. : %s] TRUCK BOOKING %s.`, remark.TruckNumber, actor)
		}
	case CARGOTRAIN:
		statusHistory = fmt.Sprintf(`[TRAIN NO. : %s] TRAIN BOOKING %s.`, remark.TrainNumber, actor)
	case CARGOSHIP:
		statusHistory = fmt.Sprintf(`[SHIP NO. : %s] SHIP BOOKING %s.`, remark.ShipNumber, actor)
	case TRANSIT:
		statusHistory = `Paketmu sedang transit`
	case STIDEST:
		if productType == INTERPACK {
			statusHistory = fmt.Sprintf(`Paket telah sampai di Negara %s`, countryName)
		} else {
			statusHistory = fmt.Sprintf(`[ECARGO NO. %s] STI-DEST AT %s.`, remark.CargoNumber, actor)
		}
	case STIDESTSC:
		statusHistory = fmt.Sprintf(`[ECARGO NO. %s] STI-DEST-SC AT %s.`, remark.CargoNumber, actor)
	case HND:
		statusHistory = fmt.Sprintf(`HANDOVER TO %s FROM %s.`, remark.HandoverTo, actor)
	case DEL:
		if productType == INTERPACK {
			statusHistory = "Paketmu diantar ke alamat penerima oleh Kurir. Pastikan nomor penerima dapat dihubungi oleh kurir"
		} else {
			statusHistory = fmt.Sprintf(`DELIVERED BY %s FROM %s [%s].`, remark.DriverName, actor, actorExternalCode)
		}
	case DEX:
		if reasonMaskingDex == historyReason {

			statusHistory = fmt.Sprintf(`Paketmu gagal diantarkan karena %s. Silakan hubungi CS untuk mendapatkan informasi lebih lanjut.`, reason)
		} else {
			statusHistory = fmt.Sprintf(`Paketmu gagal diantarkan karena %s. Percobaan pengiriman ulang akan dilakukan secara berkala. Pastikan alamat dan kontak Penerima sudah sesuai.`, reason)

		}
	case POD:
		if productType == INTERPACK {
			statusHistory = `Paketmu telah sampai di tujuan & diterima`
		} else {
			statusHistory = fmt.Sprintf(`RECEIPT BY %s.`, remark.ReceiverName)
		}
	case MISROUTE:
		statusHistory = fmt.Sprintf(`MISROUTE AT %s.`, actor)
	case REROUTE:
		statusHistory = fmt.Sprintf(`Paketmu akan dikirim kembali ke alamat tujuan yang sesuai dengan nomor resi terbaru %s`, customProcessRemark)
	case SHORTLAND:
		statusHistory = fmt.Sprintf(`SHORTLAND AT %s.`, actor)
	case CNX:
		statusHistory = fmt.Sprintf(`Pengiriman paketmu telah dibatalkan karena alasan %s.`, reason)
		if remark.SttNoReference != `` {
			statusHistory += fmt.Sprintf(` Cek nomor resi terbaru %s.`, remark.SttNoReference)
		}
	case STTADJUSTED:
		statusHistory = fmt.Sprintf(`STT NUMBER. ADJUSTED BY %s.`, name)
	case STTREMOVE:
		statusHistory = fmt.Sprintf(`STT NUMBER. REMOVE BY %s.`, name)
	case CODREJ:
		statusHistory = fmt.Sprintf(`Paket COD mu dikembalikan ke alamat pengirim karena %s.`, reason)
	case PICKUPTRUCKING:
		cityName := remark.HistoryLocationName
		districtName := remark.HistoryDistrictName

		statusHistory = fmt.Sprintf(`Paketmu telah diberangkatkan dengan truk dari Kota %s, %s`, cityName, districtName)
	case DROPOFFTRUCKING:
		cityName := remark.HistoryLocationName
		districtName := remark.HistoryDistrictName

		statusHistory = fmt.Sprintf(`Paketmu telah sampai dengan truk di Kota %s, %s`, cityName, districtName)
	case SCRAP:
		fallthrough
	case RTS:
		statusHistory = fmt.Sprintf(`Paketmu akan dikembalikan ke alamat pengirim dengan nomor resi baru %s`, remark.CustomProcessRemarks)
		if productType == INTERPACK {
			statusHistory = `Paketmu dikembalikan ke alamat pengirim karena alasan tertentu.`
		}
		if accountType != BookingForClient {
			statusHistory = fmt.Sprintf(`Paket akan dikembalikan ke pengirim dengan nomor STT baru %s`, remark.CustomProcessRemarks)
		}
	case RTSHQ:
		statusHistory = fmt.Sprintf(`Paket dikembalikan ke Lion Parcel Pusat dengan nomor resi baru %s.`, remark.CustomProcessRemarks)
		if accountType != BookingForClient {
			statusHistory = fmt.Sprintf(`Paket dikembalikan ke Lion Parcel Pusat dengan nomor STT baru %s`, remark.CustomProcessRemarks)
		}
	case DAMAGE:
		statusHistory = "Pengiriman paketmu mengalami kendala."
		fallthrough
	case ODA:
		fallthrough
	case REJECTED:
		statusHistory = fmt.Sprintf(`Paketmu akan dialihkan menggunakan armada lain karena %s.`, reason)
	case HAL:
		cityName := remark.HistoryLocationName
		districtName := remark.HistoryDistrictName

		statusHistory = fmt.Sprintf("Paketmu berada di Gudang Lion Parcel %s, %s karena %s.", cityName, districtName, reason)
	case OCC:
		if productType == INTERPACK {
			statusHistory = fmt.Sprintf("Paketmu dalam proses pengecekan Bea Cukai untuk dikirim ke tujuan %s.", countryName)
		}
	case MISSING:
		statusHistory = "Pengiriman paketmu mengalami kendala."
		if strings.EqualFold(actor, AccountNinja.ActorName) {
			statusHistory = fmt.Sprintf(`Pengiriman paketmu mengalami kendala karena %s.`, customProcessRemark)
		}
		if productType == INTERPACK {
			statusHistory = fmt.Sprintf(`Pengiriman paketmu mengalami kendala karena %s.`, reason)
		}
	case MISBOOKING:
		statusHistory = fmt.Sprintf(`Terjadi kesalahan pada paketmu saat diproses Agen.`)
	case NOTRECEIVED:
		statusHistory = fmt.Sprintf(`Pengiriman paketmu masih dalam proses`)
	case SCRAPCD:
		statusHistory = fmt.Sprintf(`STT Dimusnahkan karena bagging telah diterima`)
	case HALCD:
		statusHistory = remark.CustomProcessRemarks
	case CNXCD:
		statusHistory = remark.CustomProcessRemarks
	case INTSTI:
		cityName := cityNameINTHND
		districtName := districtNameINTHND
		statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Transit Lion Parcel %s, %s`, cityName, districtName)
	case OCCEXP:
		historyLocationName := remarkOCCEXP
		statusHistory = fmt.Sprintf(`Paketmu sedang diperiksa Bea Cukai untuk dikirim ke negara %s`, historyLocationName)
		if strings.ToUpper(actor) != Luwjistik && actorRole != VENDOR {
			statusHistory = fmt.Sprintf(`Paketmu sedang diperiksa Bea Cukai untuk dikirim ke negara %s`, countryName)
		}
	case OCCIMP:
		historyLocationName := remark.HistoryLocationName
		statusHistory = fmt.Sprintf(`Paketmu telah selesai diperiksa Bea Cukai dan telah diterima di negara %s`, historyLocationName)
		if strings.ToUpper(actor) != Luwjistik && actorRole != VENDOR {
			statusHistory = fmt.Sprintf(`Paketmu telah selesai diperiksa Bea Cukai dan telah diterima di negara %s`, countryName)
		}
	case OCCHAL:
		historyLocationName := remark.HistoryLocationName
		reason := remark.CustomProcessRemarks

		statusHistory = fmt.Sprintf(`Paketmu masih berada di Gudang Lion Parcel %s`, historyLocationName)
		if strings.ToUpper(actor) != Luwjistik && actorRole != VENDOR {
			statusHistory = fmt.Sprintf(`Paketmu berada di Gudang Lion Parcel %s %s karena %s`, countryNameHistory, historyLocationName, reason)
		}
	case INHUB:
		statusHistory = fmt.Sprintf(`Paketmu sampai di Gudang Lion Parcel %s, %s`, remark.HistoryLocationName, remark.HistoryDistrictName)
		if len(remark.CustomProcessRemarks) != 0 {
			vehicleNumber := remark.CustomProcessRemarks
			statusHistory = fmt.Sprintf("%s dengan nomor kendaraan %s", statusHistory, vehicleNumber)
		}
	case OUTHUB:
		statusHistory = fmt.Sprintf(`Paketmu telah diberangkatkan dari Gudang Lion Parcel %s, %s`, remark.HistoryLocationName, remark.HistoryDistrictName)
		if len(remark.CustomProcessRemarks) != 0 {
			vehicleNumber := remark.CustomProcessRemarks
			statusHistory = fmt.Sprintf("%s dengan nomor kendaraan %s", statusHistory, vehicleNumber)
		}
	case KONDISPATCH:
		statusHistory = `Paketmu telah ditugaskan ke kurir dan siap diantar.`
	default:
		statusHistory = fmt.Sprintf(`STT NUMBER. UPDATED BY %s.`, name)
	}

	return statusHistory
}

type RemarkPieceHistory struct {
	DriverName                  string                 `json:"driver_name"`
	DriverPhone                 string                 `json:"driver_phone"`
	ReceiverName                string                 `json:"receiver_name"`
	CargoNumber                 string                 `json:"cargo_number"`
	BagNumber                   string                 `json:"bag_number"`
	TruckNumber                 string                 `json:"truck_number"`
	TrainNumber                 string                 `json:"train_number"`
	ShipNumber                  string                 `json:"ship_ship"`
	HandoverTo                  string                 `json:"handover_to"`
	ExternalCode                string                 `json:"external_code"`
	Attactments                 []string               `json:"attactments"`
	SttWeightAttachFiles        []string               `json:"stt_weight_attach_files,omitempty"`
	SttWeightAttachFileSigneds  []string               `json:"stt_weight_attach_file_signeds,omitempty"`
	ActorExternalCode           string                 `json:"actor_external_code"`
	ActorExternalType           string                 `json:"actor_external_type"`
	HistoryLocationName         string                 `json:"history_location_name"`
	HistoryDistrictCode         string                 `json:"history_district_code"`
	HistoryDistrictName         string                 `json:"history_district_name"`
	VehicleNumber               string                 `json:"vehicle_number"`
	LatestStatusBeforeAdjusment string                 `json:"latest_status_before_adjustment"`
	ChargeableWeight            float64                `json:"chargeable_weight"`
	BookingFee                  float64                `json:"booking_fee"`
	ClientCodBookingDiscount    float64                `json:"client_cod_booking_discount"`
	BookingFeeAfterDiscount     float64                `json:"booking_fee_after_discount"`
	Comment                     string                 `json:"comment"`
	IsWithProofFile             string                 `json:"is_with_proof_file"`
	ClaimNo                     string                 `json:"claim_no"`
	CustomProcessRemarks        string                 `json:"custom_process_remarks"`
	OtherReason                 string                 `json:"other_reason,omitempty"`
	HistoryDataAdjustment       *HistoryDataAdjustment `json:"history_data_adjustment,omitempty"`
	IsForceCustomStatus         bool                   `json:"is_force_custom_status,omitempty"`
	HubID                       int                    `json:"hub_id"`
	HubName                     string                 `json:"hub_name"`
	HubCityName                 string                 `json:"hub_city_name"`
	HubOriginCity               string                 `json:"hub_origin_city"`
	HubDistrictName             string                 `json:"hub_district_name"`
	HubDestinationID            int                    `json:"hub_destination_id"`
	HubDestinationType          string                 `json:"hub_destination_type"`
	HubDestinationName          string                 `json:"hub_destination_name"`
	HubDestinationCity          string                 `json:"hub_destination_city"`
	SttNoReference              string                 `json:"stt_no_reference"`
	AttachmentSigneds           []string               `json:"attachment_signeds"`
	Latitude                    float64                `json:"latitude"`
	Longitude                   float64                `json:"longitude"`
	VolumeWeightDiscount        bool                   `json:"volume_weight_discount"`
}

func (r *RemarkPieceHistory) ToString() string {
	b, err := json.Marshal(r)
	if err != nil {
		return ``
	}
	return string(b)
}

func (r *RemarkPieceHistory) GetAllAttachments() []string {
	atts := []string{}
	atts = append(atts, r.Attactments...)
	atts = append(atts, r.SttWeightAttachFiles...)
	return atts
}

func (r *SttPieceHistory) RemarkPieceHistoryToStruct() *RemarkPieceHistory {
	res := RemarkPieceHistory{}
	err := json.Unmarshal([]byte(r.HistoryRemark), &res)
	if err != nil {
		return nil
	}

	return &res
}

func (r *SttPieceHistoryCustom) RemarkPieceHistoryToStruct() *RemarkPieceHistory {
	res := RemarkPieceHistory{}
	err := json.Unmarshal([]byte(r.HistoryRemark), &res)
	if err != nil {
		return nil
	}

	return &res
}

type SttPieceHistoryBulk struct {
	SttPieceHistory []SttPieceHistory
	SttID           int
}

type SttPieceHistoryResult struct {
	Stt             Stt
	SttPiece        SttPiece
	SttPieceHistory SttPieceHistory
	Cargo           Cargo
}

type SttPieceHistoryUpdate struct {
	SttID                  int
	SttPieceHistories      []SttPieceHistory
	IsWithUpdateDelivery   bool
	DeliveryRemarks        string
	DeliveryFinishedStatus string
	UpdatedAt              *time.Time
}

type HistoryDataAdjustment struct {
	ProductNameBeforeAdjustment           string  `json:"product_name_before,omitempty"`
	SenderNameBeforeAdjustment            string  `json:"sender_name_before,omitempty"`
	SenderPhoneBeforeAdjustment           string  `json:"sender_phone_before,omitempty"`
	SenderAddressBeforeAdjustment         string  `json:"sender_address_before,omitempty"`
	RecipientNameBeforeAdjustment         string  `json:"recipient_name_before,omitempty"`
	RecipientPhoneBeforeAdjustment        string  `json:"recipient_phone_before,omitempty"`
	RecipientAddressBeforeAdjustment      string  `json:"recipient_address_before,omitempty"`
	RecipientAddressTypeBeforeAdjustment  string  `json:"recipient_address_type_before,omitempty"`
	DestinationCityIDBeforeAdjustment     string  `json:"destination_city_id_before,omitempty"`
	DestinationDistrictIDBeforeAdjustment string  `json:"destination_district_id_before,omitempty"`
	CommodityCodeBeforeAdjustment         string  `json:"commodity_code_before,omitempty"`
	GoodsStatusBeforeAdjustment           string  `json:"goods_status_before,omitempty"`
	InsuranceTypeBeforeAdjustment         string  `json:"insurance_type_before,omitempty"`
	TaxNumberBeforeAdjustment             string  `json:"tax_number_before,omitempty"`
	PiecePerPackBeforeAdjustment          int     `json:"piece_per_pack_before,omitempty"`
	PieceGrossWeightBeforeAdjustment      float64 `json:"piece_gross_weight_before,omitempty"`
	PieceHeightBeforeAdjustment           float64 `json:"piece_height_before,omitempty"`
	PieceWidthBeforeAdjustment            float64 `json:"piece_width_before,omitempty"`
	PieceLengthBeforeAdjustment           float64 `json:"piece_length_before,omitempty"`

	ProductNameAfterAdjustment           string  `json:"product_name_after,omitempty"`
	SenderNameAfterAdjustment            string  `json:"sender_name_after,omitempty"`
	SenderPhoneAfterAdjustment           string  `json:"sender_phone_after,omitempty"`
	SenderAddressAfterAdjustment         string  `json:"sender_address_after,omitempty"`
	RecipientNameAfterAdjustment         string  `json:"recipient_name_after,omitempty"`
	RecipientPhoneAfterAdjustment        string  `json:"recipient_phone_after,omitempty"`
	RecipientAddressAfterAdjustment      string  `json:"recipient_address_after,omitempty"`
	RecipientAddressTypeAfterAdjustment  string  `json:"recipient_address_type_after,omitempty"`
	DestinationCityIDAfterAdjustment     string  `json:"destination_city_id_after,omitempty"`
	DestinationDistrictIDAfterAdjustment string  `json:"destination_district_id_after,omitempty"`
	CommodityCodeAfterAdjustment         string  `json:"commodity_code_after,omitempty"`
	GoodsStatusAfterAdjustment           string  `json:"goods_status_after,omitempty"`
	InsuranceTypeAfterAdjustment         string  `json:"insurance_type_after,omitempty"`
	TaxNumberAfterAdjustment             string  `json:"tax_number_after,omitempty"`
	PiecePerPackAfterAdjustment          int     `json:"piece_per_pack_after,omitempty"`
	PieceGrossWeightAfterAdjustment      float64 `json:"piece_gross_weight_after,omitempty"`
	PieceHeightAfterAdjustment           float64 `json:"piece_height_after,omitempty"`
	PieceWidthAfterAdjustment            float64 `json:"piece_width_after,omitempty"`
	PieceLengthAfterAdjustment           float64 `json:"piece_length_after,omitempty"`

	IsPromo                  bool    `json:"is_promo"`
	TotalTariff              float64 `json:"total_tariff"`
	TotalTariffAfterDiscount float64 `json:"total_tariff_after_discount"`
}

type SttPieceSttHistoriesResult struct {
	SttPiece          SttPiece
	SttPieceHistories []SttPieceHistory
}

func (r *RemarkPieceHistory) EncodeToString() string {
	return json2.EncodeWithoutEscapeHTML(r)
}

type SttPieceHistoryWithStt struct {
	SttPieceHistory
	SttDetailReverseJourney DetailSttReverseJourney
}
