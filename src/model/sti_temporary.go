package model

import (
	"encoding/json"
	"time"

	"github.com/abiewardani/dbr/v2"
)

type StiTemporary struct {
	StID                 int            `db:"st_id" json:"st_id"`
	StAccountID          int            `db:"st_account_id" json:"st_account_id"`
	StIsActive           int            `db:"st_is_active" json:"st_is_active"`
	StSttNo              string         `db:"st_stt_no" json:"st_stt_no"`
	StProduct            string         `db:"st_product" json:"st_product"`
	StOrigin             string         `db:"st_origin" json:"st_origin"`
	StDestination        string         `db:"st_destination" json:"st_destination"`
	StDeadlineReturn     time.Time      `db:"st_deadline_return" json:"st_deadline_return"`
	StRegionID           string         `db:"st_region_id" json:"st_region_id"`
	StMeta               string         `db:"st_meta" json:"st_meta"`
	StCreatedAt          time.Time      `db:"st_created_at" json:"st_created_at"`
	StUpdatedAt          *time.Time     `db:"st_updated_at" json:"st_updated_at"`
	HubID                int            `db:"hub_id" json:"hub_id"`
	EligibleArchivedType int            `db:"eligible_archived_type" json:"eligible_archived_type"`
	StBookedType         dbr.NullString `db:"st_booked_type" json:"st_booked_type"`
	StBookedID           int            `db:"st_booked_id" json:"st_booked_id"`
	StBookedName         dbr.NullString `db:"st_booked_name" json:"st_booked_name"`
	SttMeta              string         `db:"stt_meta" json:"stt_meta"`
}

type StiTemporaryMeta struct {
	BagNo               string  `json:"bag_no"`
	IsStiDest           int     `json:"is_sti_dest"`
	StatusReturn        string  `json:"status_return"`
	Flag                string  `json:"flag"`
	Pieces              int     `json:"pieces"`
	RefNo               string  `json:"ref_no"`
	IsPaid              bool    `json:"is_paid"`
	DeadlineReturn      string  `json:"deadline_return"`
	GrossWeight         float64 `json:"gross_weight"`
	IsDangerousGoods    bool    `json:"is_dangerous_goods"`
	SttAssessmentStatus string  `json:"stt_assessment_status"`
	SttNeedToRelabel    bool    `json:"stt_need_to_relabel"`
}

func (c *StiTemporary) StMetaToStruct() *StiTemporaryMeta {
	res := StiTemporaryMeta{}

	err := json.Unmarshal([]byte(c.StMeta), &res)
	if err != nil {
		return nil
	}

	return &res
}

func (r *StiTemporaryMeta) ToString() string {
	b, err := json.Marshal(r)
	if err != nil {
		return ``
	}
	return string(b)
}
