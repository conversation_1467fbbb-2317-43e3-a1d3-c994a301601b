package model

import (
	"encoding/json"
	"time"
)

type StiScTemporary struct {
	SstID          int64     `db:"sst_id"`           // bigint, auto-increment
	SstAccountID   int64     `db:"sst_account_id"`   // bigint
	SstIsActive    bool      `db:"sst_is_active"`    // tinyint, default 1
	SstSttNo       string    `db:"sst_stt_no"`       // varchar(50)
	SstRefNo       string    `db:"sst_ref_no"`       // varchar(50)
	SstProduct     string    `db:"sst_product"`      // varchar(50)
	SstOrigin      string    `db:"sst_origin"`       // varchar(10)
	SstDestination string    `db:"sst_destination"`  // varchar(10)
	SstPodDate     time.Time `db:"sst_pod_date"`     // timestamp
	SstGrossWeight float64   `db:"sst_gross_weight"` // int
	SstTotalPiece  int       `db:"sst_total_piece"`  // int
	SstIsPaid      bool      `db:"sst_is_paid"`      // tinyint
	SstBookedType  string    `db:"sst_booked_type"`  // varchar(50)
	SstBookedID    int64     `db:"sst_booked_id"`    // bigint
	SstBookedName  string    `db:"sst_booked_name"`  // varchar(255)
	SstMeta        string    `db:"sst_meta"`         // text
	SstCreatedAt   time.Time `db:"sst_created_at"`   // timestamp, default CURRENT_TIMESTAMP
	SstUpdatedAt   time.Time `db:"sst_updated_at"`   // timestamp, default CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
}

type StiScTemporaryMeta struct {
	StatusReturn string `json:"status_return"`
}

func (s *StiScTemporaryMeta) ToString() string {
	b, err := json.Marshal(s)
	if err != nil {
		return ``
	}
	return string(b)
}
