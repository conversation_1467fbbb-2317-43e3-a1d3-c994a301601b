package model

import "fmt"

var (
	SttStatus = map[string]string{
		"BKD":          "Paket sudah terbooking pada system Lion Parcel",
		"PUP":          "Paket sudah dijemput oleh shuttle dari POS menuju Warehouse Lion Parcel",
		"STI-SC":       "Paket sudah berada di Sub Consolidator Kota Asal",
		"STO-SC":       "Paket sedang dalam perjalanan menuju Consolidator Kota Asal",
		"STI":          "Paket sudah berada di Consolidator Kota Asal",
		"BAGGING":      "Paket sudah disortir berdasarkan masing-masing Kota Tujuan untuk diberikan Surat Muatan Udara (SMU)",
		"E-CARGO":      "Paket sudah dijadwalkan penerbangan ke Kota Tujuan",
		"TRUCK":        "Paket sudah dijadwalkan untuk perjalanan darat ke Kota Tujuan menggunakan Truk",
		"TRAIN":        "Paket sudah dijadwalkan untuk perjalanan darat ke Kota Tujuan menggunakan Kereta",
		"SHIP":         "Paket sudah dijadwalkan untuk perjalanan laut ke Kota Tujuan menggunakan Kapal",
		"TRANSIT":      "Paket sudah berada di pusat transit Kota Tujuan",
		"STI-DEST":     "Paket sudah berada di Consolidator Kota Tujuan",
		"STO-DEST":     "Paket telah dijemput oleh subcons dan sedang menuju warehouse subcons",
		"STI DEST-SC":  "Paket sudah berada di Sub Consolidator Kota Tujuan",
		"STO DEST-SC":  "Paket sedang dalam perjalanan menuju Consolidator Kota Tujuan",
		"DEL":          "Paket sedang dalam pengiriman oleh Kurir",
		"POD":          "Paket sudah diterima.",
		"DEX":          "Paket tidak terkirim ke alamat tujuan karena alasan tertentu",
		"CNX":          "Paket sudah dibatalkan oleh Pengirim atau Penerima",
		"STT REMOVE":   "Paket sudah dihapus/dihilangkan dari No. AWB",
		"SCRAP":        "Paket sudah berada di Kota Tujuan namun paket tidak dapat dikirim karena beberapa alasan :\n1. Alamat Penerima tidak ditemukan\n2. No Telepon Penerima tidak dapat dihubungi\n3. Pengirim tidak mau paket dikembalikan",
		"SHORTLAND":    "Paket tidak ditemukan atau paket tidak berada di Kota Tujuan",
		"MIS-ROUTE":    "Kesahalan pada rute Kota Tujuan atau paket berada di Kota Tujuan yang salah",
		"STT ADJUSTED": "Detail STT sudah disesuaikan pada detail STT yang sebenarnya",
		"HAL":          "Paket ditahan di Consolidator Kota Tujuan karena beberapa alasan",
		"RTS":          "Pengirim meminta paket untuk dikembalikan",
		"ODA":          "Paket berada di area yang tidak bisa dijangkau oleh Consolidator",
		"CODREJ":       "Paket COD sudah dikembalikan ke Pengirim",
		"SDEX":         "DEX berdasarkan permintaan",
		"SPOD":         "POD berdasarkan permintaan",
		"CARGO PLANE":  "Paket sudah dijadwalkan penerbangan ke Kota Tujuan",
		"CARGO TRUCK":  "Paket sudah dijadwalkan untuk perjalanan darat ke Kota Tujuan menggunakan Truk",
		"CARGO TRAIN":  "Paket sudah dijadwalkan untuk perjalanan darat ke Kota Tujuan menggunakan Kereta",
		"CARGO SHIP":   "Paket sudah dijadwalkan untuk perjalanan laut ke Kota Tujuan menggunakan Kapal",
		"REJECTED":     "Paket tidak diterima oleh tim kargo, karena tidak sesuai dengan regulasi cargo plane",
		"IN-HUB":       "Paket telah sampai di Hub",
		"OUT-HUB":      "Paket telah berangkat dari Hub",
	}

	/*
	 * Please only modify this model for stt tracking purpouse
	 */
	SttStatusForSttTracking = map[string]string{
		BKD:             "Booking",
		PUP:             "Pickup",
		STISC:           "Station Transit in Subconsolidator",
		STOSC:           "Station Transit out Subconsolidator",
		STI:             "Station Transit in Consolidator",
		BAGGING:         "Bagging",
		CARGOPLANE:      "Cargo plane",
		STTREMOVE:       "STT Remove",
		REJECTED:        "Rejected",
		CARGOTRUCK:      "Cargo truck",
		CARGOSHIP:       "Cargo ship",
		CARGOTRAIN:      "Cargo Train",
		SHORTLAND:       "Shortland",
		TRANSIT:         "Transit",
		MISROUTE:        "Misroute",
		STIDEST:         "Station Transit in Destination(Consolidator)",
		STIDESTSC:       "Station Transit in Destination(Subconsolidator)",
		HND:             "Handover",
		ODA:             "Out of Delivery Area",
		DEL:             "Delivery",
		POD:             "Proof of Delivery",
		DEX:             "Delivery with Exception",
		CODREJ:          "COD Reject",
		HAL:             "Hold at location",
		RTS:             "Return to shipper",
		SCRAP:           "Scrap",
		CLAIM:           "Claim",
		CNX:             "Cancel",
		STTADJUSTED:     "STT Adjusted",
		STTADJUSTEDPOD:  "STT ADJUSTED after POD",
		PICKUPTRUCKING:  "Pick Up Trucking",
		DROPOFFTRUCKING: "Drop Off Trucking",
		NOTRECEIVED:     "Not Received",
		DAMAGE:          "Damaged",
		MISSING:         "Missing",
		REROUTE:         "Paket yang mengalami misroute akan dikirim ulang ke rute tujuan yang sesuai",
		MISBOOKING:      "Misbooking",

		CI:      "Claim Internal",
		RTSHQ:   "Return to Shipper Headquarter",
		OCC:     "On Custom Clearance",
		SCRAPCD: "Scrap-Crossdocking",
		TFDREQ:  "Transfer Delivery Request",
		DELTRF:  "Delivery Transfered",
		TFDCNC:  "Transfer Delivery Cancel",
		HALCD:   "HALCD-Crossdocking",
		CNXCD:   "CNXCD-Crossdocking",

		INTHND: "International - Handover",
		INTSTI: "International - Station Transit in",
		OCCEXP: "On Custom Clearance - Export",
		OCCIMP: "On Custom Clearance - Import",
		OCCHAL: "On Custom Clearance - Hold at Location",
		OCRIMP: "On Custom Release - Import",
		RCCIMP: "Release custom clearance",

		INHUB:  "Paket telah sampai di Hub",
		OUTHUB: "Paket telah berangkat dari Hub",

		KONDISPATCH: "Konsol Dispatch",
		STLDISPATCH: "Shuttle Dispatch",
	}
	/*
	 * Please only modify this model for stt tracking purpouse
	 */
	SttStatusForSttTrackingEnglish = map[string]string{
		RTS:         "Returned to shipper",
		SCRAP:       "Scrap",
		OCCHAL:      "On hold by customs",
		RCCIMP:      "Release custom clearance",
		NOTRECEIVED: "Not received",
		HAL:         "Hold at location",
		REJECTED:    "Rejected",
		MISBOOKING:  "Misbooking",
		REROUTE:     "Reroute",
		CNX:         "Cancel",
	}
	/*
	 * Please only modify this model for stt tracking purpouse
	 * for client
	 */
	SttStatusForSttTrackingClient = map[string]string{
		BKD:             "Diproses Agen",
		PUP:             "Ke Gudang LP",
		STISC:           "Di Gudang Transit",
		STOSC:           "Di Gudang Transit Out",
		STI:             "Di Gudang LP",
		CARGOPLANE:      "Akan diterbangkan",
		CARGOTRUCK:      "Akan diberangkatkan",
		CARGOSHIP:       "Akan diberangkatkan",
		CARGOTRAIN:      "Akan diberangkatkan",
		TRANSIT:         "Sedang Transit",
		STIDEST:         "Tiba di Gudang LP",
		STIDESTSC:       "Di Gudang Transit",
		DEL:             "Dalam Pengantaran",
		POD:             "Paket diterima",
		DEX:             "Dikirim Ulang",
		CODREJ:          "Paket dikembalikan",
		HAL:             "Paket gagal dikirim",
		RTS:             "Return to shipper",
		CLAIM:           "Dalam proses klaim",
		CNX:             "Pengiriman batal",
		STTADJUSTED:     "Penyesuaian harga",
		STTADJUSTEDPOD:  "Penyesuaian Harga",
		PICKUPTRUCKING:  "Telah berangkat",
		DROPOFFTRUCKING: "Truck sampai di kota",
		REJECTED:        "Pergantian armada",
		NOTRECEIVED:     "Kendala Pengiriman",
		DAMAGE:          "Kendala Pengiriman",
		MISSING:         "Kendala Pengiriman",
		REROUTE:         "Paket dikirim ulang ke rute tujuan yang sesuai",
		MISBOOKING:      "Kendala Pengiriman",

		RTSHQ:   "Paket Dikembalikan",
		OCC:     "Dicek Bea Cukai",
		SCRAPCD: "Scrap-Crossdocking",
		TFDREQ:  "Transfer Delivery Request",
		DELTRF:  "Delivery Transfered",
		TFDCNC:  "Transfer Delivery Cancel",
		HALCD:   "HALCD-Crossdocking",
		CNXCD:   "CNXCD-Crossdocking",

		INTSTI: "Di Gudang Transit",
		OCCEXP: "Sedang Diperiksa",
		OCCIMP: "Selesai Diperiksa",
		OCCHAL: "Proses Sortir",
		OCRIMP: "Dirilis Bea Cukai",
		RCCIMP: "Release custom clearance",

		INHUB:  "Paket telah sampai di Hub",
		OUTHUB: "Paket telah berangkat dari Hub",

		KONDISPATCH: "Akan diantarkan",
	}

	/*
	 * Please only modify this model for stt tracking purpouse
	 * for client In Englih
	 */
	SttStatusForSttTrackingClientEnglish = map[string]string{
		RTS:         "Returned to shipper",
		OCCHAL:      "On hold by customs",
		RCCIMP:      "Release custom clearance",
		NOTRECEIVED: "Shipping issue",
		HAL:         "Package undelivered",
		REJECTED:    "Shipment fleet changed",
		MISBOOKING:  "Shipping issue",
		REROUTE:     "Destination shipment changed",
		CNX:         "Shipment canceled",
	}

	/*
	 * Please only modify this model for stt tracking purpouse
	 * for client hidden status
	 */
	SttStatusForSttTrackingClientHiddenStatus = map[string]string{
		STTREMOVE: "STT Remove",
	}

	CargoStatusMapping = map[string]string{
		TRUCK: CARGOTRUCK,
		TRAIN: CARGOTRAIN,
		PLANE: CARGOPLANE,
		SHIP:  CARGOSHIP,
	}

	TruckingStatusMapping = map[string]bool{
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
	}

	MaskingVendorName = map[string]string{
		"ninja":     "NX",
		"jne":       "JN",
		"luwjistik": "LUW",
		"ptpos":     "PI",
		"pi":        "PI",
	}

	MaskingActorRole = map[string]string{
		"pos":    "POS",
		"client": "CLIENT",
	}

	DELDEX = "DEL-DEX"

	// Booking: Paket sudah terbooking pada system Lion Parcel
	BKD = "BKD"

	// Pickup: Paket sudah dijemput oleh shuttle dari POS menuju Warehouse Lion Parcel
	PUP = "PUP"

	// Pickup Child: Paket sudah dijemput oleh shuttle dari POS menuju Warehouse Lion Parcel
	PUPC = "PUP-C"

	// Station Transit in Subconsolidator: Paket sudah berada di Sub Consolidator Kota Asal
	STISC = "STI-SC"

	// Station Transit out Subconsolidator: Paket sedang dalam perjalanan menuju Consolidator Kota Asal
	STOSC = "STO-SC"

	// Station Transit in Consolidator: Paket sudah berada di Consolidator Kota Asal
	STI = "STI"

	// Bagging: Paket sudah disortir berdasarkan masing-masing Kota Tujuan untuk diberikan Surat Muatan Udara (SMU)
	BAGGING = "BAGGING"

	// E-CARGO: Paket sudah dijadwalkan penerbangan ke Kota Tujuan
	ECARGO = "E-CARGO"

	// TRUCK: Paket sudah dijadwalkan untuk perjalanan darat ke Kota Tujuan menggunakan Truk
	TRUCK = "TRUCK"

	// TRAIN: Paket sudah dijadwalkan untuk perjalanan darat ke Kota Tujuan menggunakan Kereta
	TRAIN = "TRAIN"

	// PLANE: Paket sudah dijadwalkan penerbangan ke Kota Tujuan/Kota Transit
	PLANE = "PLANE"

	// SHIP: Paket sudah dijadwalkan untuk perjalanan laut ke Kota Tujuan menggunakan Kapal
	SHIP = "SHIP"

	// Transit: Paket sudah berada di pusat transit Kota Tujuan
	TRANSIT = "TRANSIT"

	// Station Transit in Destination(Consolidator): Paket sudah berada di Consolidator Kota Tujuan
	STIDEST = "STI-DEST"

	// Station Transit out Destination(Consolidator): Paket sedang dalam perjalanan menuju Consolidator Kota Tujuan
	STODEST = "STO-DEST"

	// Station Transit in Destination(Subconsolidator): Paket sudah berada di Sub Consolidator Kota Tujuan
	STIDESTSC = "STI DEST-SC"

	// Station Transit out Destination(Subconsolidator): Paket sedang dalam perjalanan menuju Consolidator Kota Tujuan
	STODESTSC = "STO DEST-SC"

	// Handover: Paket sedang dalam perjalanan menuju Subconsolidator atau Subagent (Ninja/JNE/PT POS) atau POS
	HND = "HND"

	// Delivery: Paket sedang dalam pengiriman oleh Kurir
	DEL = "DEL"

	// Proof of Delivery: Paket sudah diterima.
	POD = "POD"

	// Delivery with Exception: Paket tidak terkirim ke alamat tujuan karena alasan tertentu
	DEX = "DEX"

	// Cancel: Paket sudah dibatalkan oleh Pengirim atau Penerima
	CNX = "CNX"

	// Cancel Crossdocking: Paket sudah dibatalkan oleh Pengirim atau Penerima
	CNXCD = "CNXCD"

	// STT REMOVE: Paket sudah dihapus/dihilangkan dari No. Cargo atau No. Bagging
	STTREMOVE = "STT REMOVE"

	// Scrap: Paket sudah berada di Kota Tujuan namun paket tidak dapat dikirim karena beberapa alasan
	SCRAP = "SCRAP"

	// Scrap Crossdocking: Paket sudah berada di Kota Tujuan namun paket tidak dapat dikirim karena beberapa alasan
	SCRAPCD = "SCRAP-CD"

	// Shortland: Paket tidak ditemukan atau paket tidak berada di Kota Tujuan
	SHORTLAND = "SHORTLAND"

	// MISROUTE: Kesahalan pada rute Kota Tujuan atau paket berada di Kota Tujuan yang salah
	MISROUTE = "MIS-ROUTE"

	// MISBOOKING: Terdapat kesalahan booking pada paket yang dikirim.
	// Kesalahan bisa saja pada alamat, dll
	MISBOOKING = "MISBOOKING"

	// MISSING: Paket yang dikirim hilang saat dalam proses pengiriman ke konsol destination atau konsol transit
	MISSING = "MISSING"

	// DAMAGE: Paket yang dikirim rusak saat dalam proses pengiriman ke konsol destination atau konsol transit
	DAMAGE = "DAMAGE"

	// NOTRECEIVED: Paket yang dikirim tidak diterima oleh penerima
	NOTRECEIVED = "NOT RECEIVED"

	// STTADJUSTED: Detail STT sudah disesuaikan pada detail STT yang sebenarnya
	STTADJUSTED = "STT ADJUSTED"

	// STTADJUSTEDPOD: Detail STT sudah disesuaikan pada detail STT yang sebenarnya
	STTADJUSTEDPOD = "STT ADJUSTED POD"

	// Hold at location: Paket ditahan di Consolidator Kota Tujuan karena beberapa alasan
	HAL = "HAL"

	// Hold at location Crossdocking: Paket ditahan di Consolidator Kota Tujuan karena beberapa alasan
	HALCD = "HALCD"

	// Return to shipper: Pengirim meminta paket untuk dikembalikan
	RTS = "RTS"

	// Return to shipper Headquarter: Pengirim meminta paket untuk dikembalikan
	RTSHQ = "RTSHQ"

	// Re-route: Paket yang mengalami misroute akan dikirim ulang ke rute tujuan yang sesuai
	REROUTE = "REROUTE"

	// Out of Delivery Area: Paket berada di area yang tidak bisa dijangkau oleh Consolidator
	ODA = "ODA"

	// COD Reject: Paket COD sudah dikembalikan ke Pengirim
	CODREJ = "CODREJ"

	// SDEX: DEX berdasarkan permintaan
	SDEX = "SDEX"

	// SPOD: POD berdasarkan permintaan
	SPOD = "SPOD"

	// REJECTED: Paket tidak diterima oleh tim kargo, karena tidak sesuai dengan regulasi cargo plane
	REJECTED = "REJECTED"

	// CARGOPLANE: Paket sudah dijadwalkan penerbangan ke Kota Tujuan
	CARGOPLANE = "CARGO PLANE"

	// CARGOTRUCK: Paket sudah dijadwalkan untuk perjalanan darat ke Kota Tujuan menggunakan Truk
	CARGOTRUCK = "CARGO TRUCK"

	// CARGOTRAIN: Paket sudah dijadwalkan untuk perjalanan darat ke Kota Tujuan menggunakan Kereta
	CARGOTRAIN = "CARGO TRAIN"

	// CARGOSHIP: Paket sudah dijadwalkan untuk perjalanan laut ke Kota Tujuan menggunakan Kapal
	CARGOSHIP = "CARGO SHIP"

	// CLAIM: Paket sedang dalam proses claim money back guarantee karena beberapa alasan seperti:
	//
	// 1. Tidak sampai tepat waktu saat menggunakan produk pengiriman onepack
	// 2. Barang telah diasuransikan dan hilang diperjalanan
	CLAIM = "CLAIM"

	// CLAIM_SUBMITTED: Paket sedang dalam proses klaim
	CLAIM_SUBMITTED = "submitted"
	// CLAIM_APPROVED: Klaim paket sudah disetujui
	CLAIM_APPROVED = "approved"

	// CLAIM_REJECTED: Klaim paket sudah ditolak
	CLAIM_REJECTED = "rejected"

	// CLAIM_FINISHED: Klaim paket sudah selesai
	CLAIM_FINISHED = "finished"

	// Claim Internal: Claim di buat oleh internal, dimana paket yang di bebankan ke konsol yang menyatakan sudah di setujui
	// dan sudah di bayarkan (paket mana yang sudah masuk pengajuan claim dan mana yang masih dalam proses ada claim selain dari customer)
	CI = "CI"

	// On Custom Clearance: Paket sedang dalam proses bea cukai
	OCC = "OCC"

	OUTSTANDING = "OUTSTANDING"
	COLLECTED   = "COLLECTED"

	// PICKUPTRUCKING: Paket sedang dalam perjalanan menggunakan truk menuju warehouse
	PICKUPTRUCKING = "PICKUP_TRUCKING"

	// DROPOFFTRUCKING: Paket sudah diantar oleh truk ke alamat tujuan
	DROPOFFTRUCKING = "DROPOFF_TRUCKING"

	PICKUP  = "PICKUP"
	DROPOFF = "DROP_OFF"

	// CARGO: Paket sedang dalam proses pengiriman menggunakan cargo
	CARGO = "CARGO"

	CANCEL            = "CANCEL"
	PROCESS_IMMEDIATE = "process-immediate"

	// DELTRF: Delivery Transfered
	DELTRF = "DELTRF"

	// TFDREQ: Transfer Delivery Request
	TFDREQ = "TFDREQ"

	// TFDCNC: Transfer Delivery Cancel
	TFDCNC = "TFDCNC"

	// INTHND: International - Handover
	INTHND = "INTHND"

	// INTSTI: International - Station Transit in
	INTSTI = "INT-STI"

	// OCCEXP: On Custom Clearance - Export
	OCCEXP = "OCC-EXP"

	// OCCHAL: On Custom Clearance - Import
	OCCIMP = "OCC-IMP"

	// OCCHAL: On Custom Clearance - Hold at Location
	OCCHAL = "OCC-HAL"
	OCRIMP = "OCR-IMP"
	RCCIMP = "RCC-IMP"

	// IN HUB: Paket telah sampai di Hub
	INHUB = "IN-HUB"

	// OUT HUB: Paket telah berangkat dari Hub
	OUTHUB = "OUT-HUB"

	// DISPATCH
	DISPATCH = "DISPATCH"

	// DEM
	DEM = "DEM"

	// KONDISPATCH
	KONDISPATCH = "KONDISPATCH"

	// STLDISPATCH
	STLDISPATCH = "STLDISPATCH"
)

var (
	StatusMappingOrder = map[string]int{
		BKD:        1,
		PUP:        1,
		STISC:      1,
		STI:        1,
		BAGGING:    1,
		ECARGO:     1,
		TRUCK:      1,
		TRAIN:      1,
		PLANE:      1,
		CARGOPLANE: 1,
		CARGOTRUCK: 1,
		CARGOTRAIN: 1,
	}

	// IsStatusWithReason ...
	IsStatusWithReason = map[string]bool{
		CNX:    true,
		DEX:    true,
		CODREJ: true,
	}

	IsAllowToCancelInternal = map[string]bool{
		BKD:             true,
		PUP:             true,
		PUPC:            true,
		STTADJUSTED:     true,
		STI:             true,
		STIDEST:         true,
		BAGGING:         true,
		HALCD:           true,
		CARGOPLANE:      true,
		CARGOTRAIN:      true,
		CARGOTRUCK:      true,
		CARGOSHIP:       true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		SHORTLAND:       true,
		OCC:             true,
		REJECTED:        true,
		INHUB:           true,
		OUTHUB:          true,
	}

	IsNotAllowToEditInternal = map[string]bool{
		POD:            true,
		STTADJUSTEDPOD: true,
		CNX:            true,
		SCRAP:          true,
		CODREJ:         true,
		RTS:            true,
		RTSHQ:          true,
		REROUTE:        true,
	}

	IsAllowToEditInternalPOD = map[string]bool{
		POD:            true,
		STTADJUSTEDPOD: true,
	}

	IsAllowToCancelByConsolidator = map[string]bool{
		STI:      true,
		STIDEST:  true,
		REJECTED: true,
		// SHORTLAND: true,
	}

	IsAllowToCancelBySubconsolidator = map[string]bool{
		SHORTLAND: true,
	}

	IsAllowToCancelByPos = map[string]bool{
		BKD:         true,
		STTADJUSTED: true,
		PUP:         true,
	}

	IsAllowToCancelByPosChild = map[string]bool{
		BKD:         true,
		STTADJUSTED: true,
		PUP:         true,
		PUPC:        true,
	}

	IsAllowToCancelByClientBranch = map[string]bool{
		BKD:         true,
		STTADJUSTED: true,
		PUP:         true,
	}

	IsAllowToCancelByClientParent = map[string]bool{
		BKD:         true,
		STTADJUSTED: true,
		PUP:         true,
		SHORTLAND:   true,
	}

	// IsStatusAfterPUP ...
	IsStatusAfterPUP = map[string]bool{
		PUP: true,
	}

	// IsStatusBeforePUP ...
	IsStatusBeforePUP = map[string]bool{
		BKD:         true,
		STTADJUSTED: true,
	}

	// MapValidStatusAdjustmendBeforePup ...
	MapValidStatusAdjustmendBeforePup = map[string]bool{
		BKD:         true,
		STTADJUSTED: true,
		REJECTED:    true,
	}

	// IsInternalStatusBeforePUP ...
	IsInternalStatusBeforePUP = map[string]bool{
		BKD:             true,
		STTADJUSTED:     true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
	}

	// MapValidInternalStatusAdjustmentBeforePUP ...
	MapValidInternalStatusAdjustmentBeforePUP = map[string]bool{
		BKD:             true,
		STTADJUSTED:     true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		REJECTED:        true,
	}

	// IsInternalAfterStatusPUP ...
	IsInternalAfterStatusPUP = map[string]bool{
		BKD:             true,
		STTADJUSTED:     true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		STISC:           true,
		STI:             true,
		PUP:             true,
		REJECTED:        true,
	}

	// IsStatusBeforeSTI ...
	IsStatusBeforeSTI = map[string]bool{
		BKD:         true,
		PUP:         true,
		STISC:       true,
		STTADJUSTED: true,
	}

	IsStatusClaimed = map[string]bool{
		CLAIM: true,
		CI:    true,
	}

	IsAllowToEditForSttAdjustmentPOD = map[string]bool{
		POD: true,
	}

	IsNotAllowEditPosForMP = map[string]bool{
		BKD:         true,
		PUP:         true,
		STTADJUSTED: true,
	}

	IsGetCityFromHistoryLocation = map[string]bool{
		INTSTI:     true,
		CARGOPLANE: true,
		OCCEXP:     true,
	}

	IsAllowToCancelReverseJourney = map[string]bool{
		BKD:             true,
		PUP:             true,
		PUPC:            true,
		STTADJUSTED:     true,
		STI:             true,
		STIDEST:         true,
		BAGGING:         true,
		HALCD:           true,
		CARGOPLANE:      true,
		CARGOTRAIN:      true,
		CARGOTRUCK:      true,
		CARGOSHIP:       true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		SHORTLAND:       true,
		OCC:             true,
		REJECTED:        true,
		RTS:             true,
		RTSHQ:           true,
		REROUTE:         true,
	}

	MapHistoryStatusCalculateRetailTariff = map[string]bool{
		BKD:            true,
		STTADJUSTED:    true,
		STTADJUSTEDPOD: true,
	}

	MapSttJourneyType = map[string]string{
		RTSHQ:   "returnhq",
		RTS:     "return",
		CNX:     "cancel",
		REROUTE: "reroute",
	}

	// IsStatusAfterPUP ...
	IsStatusAfterPUPPUPC = map[string]bool{
		PUP:  true,
		PUPC: true,
	}
)

var (
	/** Mapping status update (START)
	 * This mapping is used to check the last state whether the update to the new state is allowed
	 * example: IsAllowUpdateToPUP[input_the_laststatus]
	 */

	// IsAllowUpdateToPUP ...
	IsAllowUpdateToPUP = map[string]bool{
		BKD: true,
	}
	// IsAllowUpdateToSTI ...
	IsAllowUpdateToSTI = map[string]bool{
		BKD:         true,
		PUPC:        true,
		PUP:         true,
		STISC:       true,
		STTADJUSTED: true,
		MISSING:     true,
		DAMAGE:      true,
		NOTRECEIVED: true,
		OCC:         true,
		STOSC:       true,
		HALCD:       true,
		BAGGING:     true,
		CI:          true,
	}

	// IsAllowUpdateToSTISC ...
	IsAllowUpdateToSTISC = map[string]bool{
		BKD:             true,
		PUP:             true,
		PUPC:            true,
		STTADJUSTED:     true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		MISSING:         true,
		DAMAGE:          true,
		NOTRECEIVED:     true,
		OCC:             true,
		CI:              true,
	}
	// IsAllowUpdateToBagging ...
	IsAllowUpdateToBagging = map[string]bool{
		BKD:             true,
		PUP:             true,
		STISC:           true,
		STOSC:           true,
		STI:             true,
		ECARGO:          true,
		TRUCK:           true,
		TRAIN:           true,
		TRANSIT:         true,
		STIDEST:         true,
		STODEST:         true,
		STIDESTSC:       true,
		STODESTSC:       true,
		STTREMOVE:       true,
		SHORTLAND:       true,
		MISROUTE:        true,
		STTADJUSTED:     true,
		HAL:             true,
		RTS:             true,
		ODA:             true,
		SDEX:            true,
		SPOD:            true,
		BAGGING:         true,
		HALCD:           true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		MISSING:         true,
		DAMAGE:          true,
		NOTRECEIVED:     true,
		OCC:             true,
		STOSC:           true,
		CI:              true,
		INHUB:           true,
		OUTHUB:          true,
	}
	// IsAllowUpdateToSTIDEST ...
	IsAllowUpdateToSTIDEST = map[string]bool{
		BKD:             true,
		PUP:             true,
		PUPC:            true,
		STISC:           true,
		STI:             true,
		BAGGING:         true,
		HALCD:           true,
		TRANSIT:         true,
		ECARGO:          true,
		TRUCK:           true,
		TRAIN:           true,
		MISROUTE:        true,
		HND:             true,
		SHORTLAND:       true,
		STTADJUSTED:     true,
		CARGOPLANE:      true,
		CARGOTRAIN:      true,
		CARGOTRUCK:      true,
		CARGOSHIP:       true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		MISSING:         true,
		DAMAGE:          true,
		NOTRECEIVED:     true,
		OCC:             true,
		STOSC:           true,
		INTHND:          true,
		INTSTI:          true,
		OCCEXP:          true,
		OCCHAL:          true,
		OCCIMP:          true,
		CI:              true,
		INHUB:           true,
		OUTHUB:          true,
		STLDISPATCH:     true,
		KONDISPATCH:     true,
		RCCIMP:          true,
	}

	// IsAllowUpdateToSTIDEST ...
	IsAllowUpdateToShortland = map[string]bool{
		CARGOPLANE:      true,
		CARGOTRAIN:      true,
		CARGOTRUCK:      true,
		CARGOSHIP:       true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
	}

	// IsCargoStatus ...
	IsCargoStatus = map[string]bool{
		ECARGO: true,
		TRUCK:  true,
		TRAIN:  true,
	}
	// IsAllowUpdateToSTIDESTSC ...
	IsAllowUpdateToSTIDESTSC = map[string]bool{
		BKD:             true,
		PUP:             true,
		PUPC:            true,
		STISC:           true,
		STOSC:           true,
		STI:             true,
		BAGGING:         true,
		HALCD:           true,
		ECARGO:          true,
		TRUCK:           true,
		TRAIN:           true,
		TRANSIT:         true,
		STIDEST:         true,
		STODEST:         true,
		STODESTSC:       true,
		HND:             true,
		STTREMOVE:       true,
		SHORTLAND:       true,
		MISROUTE:        true,
		STTADJUSTED:     true,
		HAL:             true,
		RTS:             true,
		ODA:             true,
		SDEX:            true,
		SPOD:            true,
		CARGOPLANE:      true,
		CARGOTRAIN:      true,
		CARGOTRUCK:      true,
		CARGOSHIP:       true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		MISSING:         true,
		DAMAGE:          true,
		NOTRECEIVED:     true,
		CI:              true,
		OCC:             true,
		STOSC:           true,
	}
	// IsAllowUpdateToCNX ...
	IsAllowUpdateToCNX = map[string]bool{
		BKD:         true,
		PUP:         true,
		STISC:       true,
		STOSC:       true,
		STI:         true,
		BAGGING:     true,
		HALCD:       true,
		ECARGO:      true,
		TRUCK:       true,
		TRAIN:       true,
		TRANSIT:     true,
		STIDEST:     true,
		STODEST:     true,
		STIDESTSC:   true,
		STODESTSC:   true,
		HND:         true,
		DEL:         true,
		DEX:         true,
		STTREMOVE:   true,
		SHORTLAND:   true,
		MISROUTE:    true,
		STTADJUSTED: true,
		HAL:         true,
		RTS:         true,
		ODA:         true,
		CODREJ:      true,
		SDEX:        true,
		SPOD:        true,
	}

	// IsAllowUpdateToSTTREMOVE ...
	IsAllowUpdateToSTTREMOVE = map[string]bool{
		BKD:         true,
		PUP:         true,
		STISC:       true,
		STOSC:       true,
		STI:         true,
		BAGGING:     true,
		HALCD:       true,
		ECARGO:      true,
		TRUCK:       true,
		TRAIN:       true,
		TRANSIT:     true,
		STIDEST:     true,
		STODEST:     true,
		STIDESTSC:   true,
		STODESTSC:   true,
		HND:         true,
		DEL:         true,
		DEX:         true,
		STTREMOVE:   true,
		SHORTLAND:   true,
		MISROUTE:    true,
		STTADJUSTED: true,
		HAL:         true,
		RTS:         true,
		ODA:         true,
		CODREJ:      true,
		SDEX:        true,
		SPOD:        true,
		CLAIM:       true,
		STOSC:       true,
	}
	// IsAllowUpdateToHND ...
	IsAllowUpdateToHND = map[string]bool{
		BKD:             true,
		PUP:             true,
		PUPC:            true,
		STISC:           true,
		STOSC:           true,
		STI:             true,
		BAGGING:         true,
		HALCD:           true,
		ECARGO:          true,
		TRUCK:           true,
		TRAIN:           true,
		CARGOSHIP:       true,
		PLANE:           true,
		TRANSIT:         true,
		STIDEST:         true,
		STODEST:         true,
		STIDESTSC:       true,
		STODESTSC:       true,
		STTREMOVE:       true,
		SHORTLAND:       true,
		MISROUTE:        true,
		STTADJUSTED:     true,
		HAL:             true,
		ODA:             true,
		SDEX:            true,
		SPOD:            true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		MISSING:         true,
		DAMAGE:          true,
		NOTRECEIVED:     true,
		CI:              true,
	}

	IsAllowUpdateSTTInterpackToHND = map[string]bool{
		STI:       true,
		BAGGING:   true,
		TRANSIT:   true,
		STTREMOVE: true,
		STIDEST:   true,
	}

	// IsAllowUpdateToDEL ...
	IsAllowUpdateToDEL = map[string]bool{
		BKD:             true,
		PUP:             true,
		PUPC:            true,
		STISC:           true,
		STOSC:           true,
		STI:             true,
		BAGGING:         true,
		HALCD:           true,
		ECARGO:          true,
		TRUCK:           true,
		TRAIN:           true,
		TRANSIT:         true,
		STIDEST:         true,
		STODEST:         true,
		STIDESTSC:       true,
		STODESTSC:       true,
		HND:             true,
		DEX:             true,
		STTREMOVE:       true,
		SHORTLAND:       true,
		MISROUTE:        true,
		STTADJUSTED:     true,
		HAL:             true,
		ODA:             true,
		SDEX:            true,
		SPOD:            true,
		CARGOPLANE:      true,
		CARGOTRAIN:      true,
		CARGOTRUCK:      true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		MISSING:         true,
		DAMAGE:          true,
		NOTRECEIVED:     true,
		CI:              true,
		STOSC:           true,
		DELTRF:          true,
		TFDREQ:          true,
		TFDCNC:          true,
		INTHND:          true,
		INTSTI:          true,
		OCCEXP:          true,
		OCCHAL:          true,
		OCCIMP:          true,
		CODREJ:          true,
		KONDISPATCH:     true,
		STLDISPATCH:     true,
	}

	IsAllowUpdateToDELDriverApp = map[string]bool{
		BKD:             true,
		PUP:             true,
		PUPC:            true,
		STISC:           true,
		STOSC:           true,
		STI:             true,
		BAGGING:         true,
		HALCD:           true,
		ECARGO:          true,
		TRUCK:           true,
		TRAIN:           true,
		TRANSIT:         true,
		STIDEST:         true,
		STODEST:         true,
		STIDESTSC:       true,
		STODESTSC:       true,
		HND:             true,
		DEX:             true,
		STTREMOVE:       true,
		SHORTLAND:       true,
		MISROUTE:        true,
		STTADJUSTED:     true,
		HAL:             true,
		ODA:             true,
		SDEX:            true,
		SPOD:            true,
		CARGOPLANE:      true,
		CARGOTRAIN:      true,
		CARGOTRUCK:      true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		MISSING:         true,
		DAMAGE:          true,
		NOTRECEIVED:     true,
		CI:              true,
		STOSC:           true,
		DELTRF:          true,
		TFDREQ:          true,
		TFDCNC:          true,
		INTHND:          true,
		INTSTI:          true,
		OCCEXP:          true,
		OCCHAL:          true,
		OCCIMP:          true,
		CODREJ:          true,
		INHUB:           true,
		OUTHUB:          true,
		KONDISPATCH:     true,
		STLDISPATCH:     true,
		RCCIMP:          true,
	}

	// IsAllowUpdateToDELDEM
	IsAllowUpdateToDELDEM = map[string]bool{
		BKD:             true,
		PUP:             true,
		PUPC:            true,
		STISC:           true,
		STOSC:           true,
		STI:             true,
		BAGGING:         true,
		HALCD:           true,
		ECARGO:          true,
		TRUCK:           true,
		TRAIN:           true,
		TRANSIT:         true,
		STIDEST:         true,
		STODEST:         true,
		STIDESTSC:       true,
		STODESTSC:       true,
		HND:             true,
		DEX:             true,
		STTREMOVE:       true,
		SHORTLAND:       true,
		MISROUTE:        true,
		STTADJUSTED:     true,
		HAL:             true,
		ODA:             true,
		SDEX:            true,
		SPOD:            true,
		CARGOPLANE:      true,
		CARGOTRAIN:      true,
		CARGOTRUCK:      true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		MISSING:         true,
		DAMAGE:          true,
		NOTRECEIVED:     true,
		CI:              true,
		STOSC:           true,
		DELTRF:          true,
		TFDREQ:          true,
		TFDCNC:          true,
		INTHND:          true,
		INTSTI:          true,
		OCCEXP:          true,
		OCCHAL:          true,
		OCCIMP:          true,
		CODREJ:          true,
		INHUB:           true,
		OUTHUB:          true,
		KONDISPATCH:     true,
		STLDISPATCH:     true,
	}

	IsNotAllowUpdateToPODorDEXorCODREJ = map[string]map[string]bool{
		POD:    IsNotAllowUpdateToPOD,
		DEX:    IsNotAllowUpdateToDEX,
		CODREJ: IsNotAllowUpdateToCODREJ,
	}

	// IsNotAllowUpdateToPOD ...
	IsNotAllowUpdateToPOD = map[string]bool{
		POD:            true,
		SCRAP:          true,
		CNX:            true,
		CLAIM:          true,
		RTSHQ:          true,
		RTS:            true,
		CODREJ:         true,
		STTADJUSTEDPOD: true,
		REROUTE:        true,
		MISBOOKING:     true,
		CARGOSHIP:      true,
		SHIP:           true,
		SCRAPCD:        true,
		DELTRF:         true,
		TFDREQ:         true,
		TFDCNC:         true,
		DEX:            true,
		STIDEST:        true,
		HAL:            true,
		CODREJ:         true,
		INHUB:          true,
		OUTHUB:         true,
	}

	// IsNotAllowUpdateToDEX ...
	IsNotAllowUpdateToDEX = map[string]bool{
		POD:            true,
		SCRAP:          true,
		CNX:            true,
		CLAIM:          true,
		RTSHQ:          true,
		RTS:            true,
		CODREJ:         true,
		STTADJUSTEDPOD: true,
		REROUTE:        true,
		MISBOOKING:     true,
		CARGOSHIP:      true,
		SHIP:           true,
		SCRAPCD:        true,
		DELTRF:         true,
		TFDREQ:         true,
		TFDCNC:         true,
		DEX:            true,
		STIDEST:        true,
		HAL:            true,
		CODREJ:         true,
		INHUB:          true,
		OUTHUB:         true,
	}

	// IsNotAllowUpdateToCODREJ ...
	IsNotAllowUpdateToCODREJ = map[string]bool{
		STIDEST: true,
		HAL:     true,
		CODREJ:  true,
		INHUB:   true,
		OUTHUB:  true,
	}

	IsNotAllowUpdateToHAL = map[string]bool{
		DEL: true,
	}

	IsNotAllowUpdateToRTS = map[string]bool{
		TRANSIT: true,
		DEL:     true,
		INHUB:   true,
		OUTHUB:  true,
	}

	// IsNotAllowUpdateCustomProcess ...
	IsNotAllowUpdateCustomProcess = map[string]bool{
		POD:            true,
		SCRAP:          true,
		CNX:            true,
		STTADJUSTEDPOD: true,
		RTSHQ:          true,
		CLAIM:          true,
		RTS:            true,
	}

	// IsAllowUpdateMisbooking
	IsAllowUpdateMisbooking = map[string]bool{
		STIDEST:     true,
		TRANSIT:     true,
		STIDESTSC:   true,
		DEL:         true,
		DEX:         true,
		HAL:         true,
		INHUB:       true,
		OUTHUB:      true,
		KONDISPATCH: true,
		STLDISPATCH: true,
	}

	// IsNotAllowUpdateMisbooking
	IsNotAllowUpdateMisbooking = map[string]bool{
		POD:        true,
		CNX:        true,
		CODREJ:     true,
		RTS:        true,
		RTSHQ:      true,
		CLAIM:      true,
		CI:         true,
		SCRAP:      true,
		MISSING:    true,
		MISBOOKING: true,
		REROUTE:    true,
	}

	IsAllowUpdateRejected = map[string]bool{
		CARGOPLANE: true,
		STTREMOVE:  true,
	}

	// IsAllowUpdateReroute
	IsAllowUpdateReroute = map[string]bool{
		MISROUTE:   true,
		MISBOOKING: true,
	}

	// IsAllowUpdateReroute
	IsAllowUpdateRTSHQ = map[string]bool{
		STIDESTSC:   true,
		DEX:         true,
		CODREJ:      true,
		MISROUTE:    true,
		NOTRECEIVED: true,
		MISSING:     true,
		DAMAGE:      true,
		RTS:         true,
		STIDEST:     true,
		HAL:         true,
		KONDISPATCH: true,
		STLDISPATCH: true,
	}

	// IsNotAllowUpdateCustomProcessNew ...
	IsNotAllowUpdateCustomProcessNew = map[string]bool{
		RTS:   true,
		CLAIM: true,
		RTSHQ: true,
	}

	IsAllowedStatusCustomProcessNew = map[string]bool{
		MISSING:     true,
		NOTRECEIVED: true,
		DAMAGE:      true,
	}

	IsAllowUpdateAfterMisbookingLastStatus = map[string]bool{
		REROUTE: true,
		MISSING: true,
		CLAIM:   true,
		CI:      true,
		RTS:     true,
		RTSHQ:   true,
	}

	IsNotAllowedStatusCustomProcessOCC = map[string]bool{
		BKD:   true,
		PUP:   true,
		STI:   true,
		STISC: true,
		OCC:   true,
		CLAIM: true,
		RTSHQ: true,
		OCC:   true,
		STOSC: true,
	}

	IsNotAllowedLastStatusDamageUpdateCustomProcess = map[string]bool{
		DEL:    true,
		POD:    true,
		CODREJ: true,
		SCRAP:  true,
		DAMAGE: true,
	}

	IsAllowedLastStatusMisrouteUpdateCustomProcess = map[string]bool{
		CI:      true,
		CLAIM:   true,
		MISSING: true,
		REROUTE: true,
		RTS:     true,
		RTSHQ:   true,
	}

	IsNotAllowedLastStatusNotReceiveUpdateCustomProcess = map[string]bool{
		POD:         true,
		CNX:         true,
		CODREJ:      true,
		SCRAP:       true,
		NOTRECEIVED: true,
		DEL:         true,
	}

	IsAllowedStatusCustomProcessRTSHQ = map[string]bool{
		TRANSIT:     true,
		MISROUTE:    true,
		STIDESTSC:   true,
		STIDEST:     true,
		DEX:         true,
		CODREJ:      true,
		MISBOOKING:  true,
		NOTRECEIVED: true,
		MISSING:     true,
		DAMAGE:      true,
		HAL:         true,
		KONDISPATCH: true,
		STLDISPATCH: true,
	}

	IsAllowedLastStatusCIUpdateCustomProcess = map[string]bool{
		CLAIM: true,
		RTS:   true,
	}

	IsAllowedStatusReturnToRtsRtshq = map[string]bool{
		CODREJ:      true,
		MISROUTE:    true,
		STIDESTSC:   true,
		DEX:         true,
		MISBOOKING:  true,
		NOTRECEIVED: true,
		MISSING:     true,
		DAMAGE:      true,
		CI:          true,
		STIDEST:     true,
		HAL:         true,
		KONDISPATCH: true,
		STLDISPATCH: true,
	}

	IsAllowedLastStatusCodRejUpdateCustomProcess = map[string]bool{
		RTS:   true,
		RTSHQ: true,
		DEL:   true,
		HAL:   true,
	}

	// IsNotAllowUpdateToCargo ...
	IsNotAllowUpdateToCargo = map[string]bool{
		POD:            true,
		DEL:            true,
		HND:            true,
		SCRAP:          true,
		CNX:            true,
		DEX:            true,
		CARGOPLANE:     true,
		CARGOTRUCK:     true,
		CARGOSHIP:      true,
		CARGOTRAIN:     true,
		STIDEST:        true,
		STTADJUSTEDPOD: true,
		RTSHQ:          true,
		CLAIM:          true,
		CODREJ:         true,
		REROUTE:        true,
		MISBOOKING:     true,
		SCRAPCD:        true,
		CNXCD:          true,
		RTS:            true,
	}

	SttNotAllowedToRTC = map[string]bool{
		MISROUTE: true,
	}

	// IsNotAllowEditCargo ...
	IsNotAllowEditCargo = map[string]bool{
		POD:            true,
		DEL:            true,
		HND:            true,
		SCRAP:          true,
		CNX:            true,
		DEX:            true,
		STTADJUSTEDPOD: true,
		CLAIM:          true,
		RTSHQ:          true,
		CODREJ:         true,
		REROUTE:        true,
		MISBOOKING:     true,
		SCRAPCD:        true,
		CNXCD:          true,
	}

	// IsAllowUpdateToDELByGenesis ...
	IsAllowUpdateToDELByGenesis = map[string]bool{
		STIDEST:         true,
		STIDESTSC:       true,
		DEX:             true,
		SHORTLAND:       true,
		HAL:             true,
		ODA:             true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		MISSING:         true,
		NOTRECEIVED:     true,
		OCC:             true,
		DELTRF:          true,
		TFDREQ:          true,
		TFDCNC:          true,
		CI:              true,
		INHUB:           true,
		OUTHUB:          true,
		KONDISPATCH:     true,
		STLDISPATCH:     true,
	}

	// STATUS DELIVERY TRANSFER TAKS
	IsDeliveryTransferTaks = map[string]bool{
		DELTRF: true,
		TFDREQ: true,
		TFDCNC: true,
	}

	IsAllowStatusForManifest = map[string]bool{
		BKD:         true,
		STTADJUSTED: true,
	}

	IsAllowToPubsubDTPOLCommission = map[string]bool{
		POD:    true,
		HAL:    true,
		CODREJ: true,
	}

	IsAllowToPubsubRehitDTPOLCommission = map[string]bool{
		POD:            true,
		CODREJ:         true,
		STTADJUSTEDPOD: true,
		RTS:            true,
		RTSHQ:          true,
	}

	// IsNotAllowForceDelProcess ...
	IsNotAllowForceDelProcess = map[string]bool{
		POD:   true,
		SCRAP: true,
		CNX:   true,
		DEL:   true,
		// CODREJ: true,
		CLAIM:   true,
		RTSHQ:   true,
		OCC:     true,
		SCRAPCD: true,
		CNXCD:   true,
	}

	IsNotAllowedCustomProcessStatusForLastStatusTrucking = map[string]bool{
		SCRAP: true,
	}

	IsAllowedStatusTruckingApp = map[string]bool{
		DROPOFF: true,
		PICKUP:  true,
	}

	STTCollectionStatus = map[string]string{
		OUTSTANDING: "Belum Setor",
		COLLECTED:   "Sudah Setor",
	}

	STTStatusDeliveryQuery = map[StatusSTTDelivery]string{
		STT_DELIVERY_ON_PROGRESS: DEL,
		STT_DELIVERY_FAILED:      fmt.Sprintf("%s,%s", CODREJ, DEX),
		STT_DELIVERY_SUCCESS:     POD,
	}

	/**Mapping status update (END)*/

	ShipmentFinished   = `FINISHED`
	ShipmentInProgress = `IN_PROGRESS`

	IsSttStatusShipmentFinished = map[string]bool{
		POD:            true,
		STTADJUSTEDPOD: true,
		CNX:            true,
		SCRAP:          true,
		MISSING:        true,
		DAMAGE:         true,
		CODREJ:         true,
		RTS:            true,
		RTSHQ:          true,
		CLAIM:          true,
		CI:             true,
	}

	IsSttStatusGenesis = map[string]bool{
		BKD:             true,
		PUP:             true,
		STISC:           true,
		STI:             true,
		BAGGING:         true,
		HALCD:           true,
		TRANSIT:         true,
		STIDEST:         true,
		STIDESTSC:       true,
		HND:             true,
		DEL:             true,
		POD:             true,
		DEX:             true,
		CNX:             true,
		STTREMOVE:       true,
		SCRAP:           true,
		SHORTLAND:       true,
		MISROUTE:        true,
		MISSING:         true,
		DAMAGE:          true,
		NOTRECEIVED:     true,
		STTADJUSTED:     true,
		STTADJUSTEDPOD:  true,
		HAL:             true,
		RTS:             true,
		RTSHQ:           true,
		ODA:             true,
		CODREJ:          true,
		REJECTED:        true,
		CARGOPLANE:      true,
		CARGOTRUCK:      true,
		CARGOTRAIN:      true,
		CI:              true,
		OCC:             true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		CANCEL:          true,
		ECARGO:          true,
		TRUCK:           true,
		TRAIN:           true,
		PLANE:           true,
		CARGO:           true,
		PICKUP:          true,
		DROPOFF:         true,
		CLAIM:           true,
		REROUTE:         true,
		PUPC:            true,
		MISBOOKING:      true,
		CARGOSHIP:       true,
		DISPATCH:        true,
		DEM:             true,
	}

	IsSttStatusReturnToSender = map[string]bool{
		RTS:     true,
		RTSHQ:   true,
		REROUTE: true,
		CNX:     true,
	}

	IsEligibleSttStatusReverseJourneyValid = map[string]bool{
		RTS:     true,
		RTSHQ:   true,
		REROUTE: true,
		CNX:     true,
	}

	IsSttStatusReverseJourneyZeroTariff = map[string]bool{
		RTSHQ: true,
		CNX:   true,
	}

	IsSttStatusReverseJorneyReroute = map[string]bool{
		MISROUTE:   true,
		MISBOOKING: true,
	}

	IsSttHistoryStatusValidforReleaseCommission = map[string]bool{
		BKD:         true,
		STTADJUSTED: true,
		CANCEL:      true,
	}

	IsStatusbeforeSTI = map[string]bool{
		BKD:     true,
		BAGGING: true,
	}

	IsAllowUpdateShipmentStatusLilo = map[string]bool{
		STI: true,
	}
	IsSttStatusRTSRTSHQ = map[string]bool{
		RTS:   true,
		RTSHQ: true,
	}

	IsAllowUpdateStatusVendorWhenStatusCI = map[string]bool{
		HAL: true,
		RTS: true,
		DEX: true,
		POD: true,
	}

	IsJNENotAvailableForceUpdateStatus = map[string]bool{
		RTS: true,
	}

	IsAllowUpdateCustomStatusScrapCD = map[string]bool{
		MISSING:         true,
		DAMAGE:          true,
		ODA:             true,
		HAL:             true,
		MISROUTE:        true,
		MISBOOKING:      true,
		HND:             true,
		SHORTLAND:       true,
		NOTRECEIVED:     true,
		OCC:             true,
		TRANSIT:         true,
		STIDESTSC:       true,
		STIDEST:         true,
		DEL:             true,
		DEX:             true,
		CODREJ:          true,
		BKD:             true,
		PUPC:            true,
		PUP:             true,
		STI:             true,
		BAGGING:         true,
		HALCD:           true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		CARGO:           true,
		CARGOPLANE:      true,
		CARGOTRUCK:      true,
		CARGOSHIP:       true,
		CARGOTRAIN:      true,
		STTREMOVE:       true,
	}

	IsNotAllowUpdateAfterScrapCDLastStatus = map[string]bool{
		MISSING:     true,
		DAMAGE:      true,
		ODA:         true,
		HAL:         true,
		MISROUTE:    true,
		MISBOOKING:  true,
		REROUTE:     true,
		RTS:         true,
		RTSHQ:       true,
		HND:         true,
		SCRAP:       true,
		SHORTLAND:   true,
		CLAIM:       true,
		CI:          true,
		NOTRECEIVED: true,
		OCC:         true,
	}

	IsMisrouteOrMisbooking = map[string]bool{
		MISROUTE:   true,
		MISBOOKING: true,
	}

	// for COD || DFOD
	IsNotAllowSttReverseJourneyUpdateToRTSHQ = map[string]bool{
		RTS:        true,
		REROUTE:    true,
		MISBOOKING: true,
		MISROUTE:   true,
	}

	IsForInterpack = map[string]bool{
		OCCEXP: true,
		OCCIMP: true,
		OCCHAL: true,
		OCRIMP: true,
		RCCIMP: true,
	}

	// IsAllowCustomStatusReroute
	IsAllowCustomStatusReroute = map[string]bool{
		MISROUTE:   true,
		MISBOOKING: true,
		CI:         true,
	}

	IsAllowCustomStatusRTSHQ = map[string]bool{
		MISROUTE:    true,
		STIDESTSC:   true,
		DEX:         true,
		CODREJ:      true,
		NOTRECEIVED: true,
		MISSING:     true,
		DAMAGE:      true,
		STIDEST:     true,
		HAL:         true,
		KONDISPATCH: true,
		STLDISPATCH: true,
	}

	IsLastStatusNotAllowCreateStiScIfStatusExists = map[string]bool{
		CLAIM:       true,
		STTADJUSTED: true,
	}

	MapLastStatusNotAllowUpdateStiDest = map[string]bool{
		STIDEST:   true,
		STIDESTSC: true,
	}

	// MapStatusAllowUpdateToDELByGenesis ...
	MapStatusAllowUpdateToDELByGenesis = map[string]bool{
		STIDEST:         true,
		STIDESTSC:       true,
		DEX:             true,
		SHORTLAND:       true,
		HAL:             true,
		ODA:             true,
		PICKUPTRUCKING:  true,
		DROPOFFTRUCKING: true,
		MISSING:         true,
		NOTRECEIVED:     true,
		OCC:             true,
		DELTRF:          true,
		TFDREQ:          true,
		TFDCNC:          true,
		CI:              true,
		HND:             true,
		CODREJ:          true,
		KONDISPATCH:     true,
		STLDISPATCH:     true,
	}

	IsStatusReturnReverseJourney = map[string]bool{
		RTS:     true,
		RTSHQ:   true,
		REROUTE: true,
	}

	IsStatusReturnRtsHal = map[string]bool{
		HAL: true,
	}

	// IsAllowUpdateInHub
	IsAllowUpdateInHub = map[string]bool{
		STI:        true,
		BAGGING:    true,
		CARGO:      true,
		CARGOPLANE: true,
		CARGOSHIP:  true,
		CARGOTRAIN: true,
		CARGOTRUCK: true,
		OUTHUB:     true,
		STIDEST:    true,
		TRANSIT:    true,
		MISROUTE:   true,
		HAL:        true,
		INHUB:      true,
	}

	// IsNotAllowUpdateInHub
	IsNotAllowUpdateInHub = map[string]bool{
		BKD:             true,
		PUP:             true,
		STISC:           true,
		STOSC:           true,
		PICKUP:          true,
		DROPOFFTRUCKING: true,
		DEL:             true,
		DEX:             true,
		CODREJ:          true,
		POD:             true,
		RTS:             true,
		RTSHQ:           true,
		MISBOOKING:      true,
		MISROUTE:        true,
		REROUTE:         true,
		CNX:             true,
		ODA:             true,
		OCCHAL:          true,
		OCCEXP:          true,
		CLAIM:           true,
		CI:              true,
		MISSING:         true,
		DAMAGE:          true,
		KONDISPATCH:     true,
		STLDISPATCH:     true,
	}

	// IsAllowUpdateOutHub
	IsAllowUpdateOutHub = map[string]bool{
		STI:        true,
		INHUB:      true,
		OUTHUB:     true,
		BAGGING:    true,
		CARGO:      true,
		CARGOPLANE: true,
		CARGOSHIP:  true,
		CARGOTRAIN: true,
		CARGOTRUCK: true,
		STIDEST:    true,
		TRANSIT:    true,
		MISROUTE:   true,
		HAL:        true,
		DEL:        true,
		CNX:        true,
		STTREMOVE:  true,
		REJECTED:   true,
		MISBOOKING: true,
	}

	// IsNotAllowUpdateOutHub
	IsNotAllowUpdateOutHub = map[string]bool{
		BKD:             true,
		PUP:             true,
		STISC:           true,
		STOSC:           true,
		PICKUP:          true,
		DROPOFFTRUCKING: true,
		DEL:             true,
		DEX:             true,
		CODREJ:          true,
		POD:             true,
		RTS:             true,
		RTSHQ:           true,
		MISBOOKING:      true,
		MISROUTE:        true,
		REROUTE:         true,
		CNX:             true,
		ODA:             true,
		OCCHAL:          true,
		OCCEXP:          true,
		CLAIM:           true,
		CI:              true,
		MISSING:         true,
		DAMAGE:          true,
	}

	IsAllowUpdateHub = map[string]struct {
		Allowed    map[string]bool
		NotAllowed map[string]bool
	}{
		INHUB: {
			Allowed:    IsAllowUpdateInHub,
			NotAllowed: IsNotAllowUpdateInHub,
		},
		OUTHUB: {
			Allowed:    IsAllowUpdateOutHub,
			NotAllowed: IsNotAllowUpdateOutHub,
		},
	}

	IsAllowToTheSameStatus = map[string]bool{
		INHUB:  true,
		OUTHUB: true,
	}

	IsStatusHub = map[string]bool{
		INHUB:  true,
		OUTHUB: true,
	}

	// Konsol Dispatch / Shuttle Dispatch
	IsAllowKonsolDispatchOrShuttleDispatch = map[string]bool{
		BKD:         true,
		PUP:         true,
		PUPC:        true,
		STISC:       true,
		STOSC:       true,
		STI:         true,
		BAGGING:     true,
		CARGO:       true,
		CARGOPLANE:  true,
		CARGOSHIP:   true,
		CARGOTRAIN:  true,
		CARGOTRUCK:  true,
		TRANSIT:     true,
		STIDEST:     true,
		HND:         true,
		STTREMOVE:   true,
		SHORTLAND:   true,
		STTADJUSTED: true,
		HAL:         true,
		ODA:         true,
		INHUB:       true,
		OUTHUB:      true,
	}

	IsNotAllowKonsolDispatchOrShuttleDispatch = map[string]bool{
		RTS:            true,
		RTSHQ:          true,
		POD:            true,
		MISBOOKING:     true,
		MISROUTE:       true,
		REROUTE:        true,
		CI:             true,
		CLAIM:          true,
		CNX:            true,
		STTADJUSTEDPOD: true,
	}

	IsNotAllowForceStatusKonDispatch = map[string]bool{
		CI:       true,
		MISROUTE: true,
	}

	IsNotAllowNotReceived = map[string]bool{
		DEL: true,
	}

	IsAllowReasonSendToWebhookAlgo = map[string]bool{
		DEX:      true,
		CODREJ:   true,
		CNX:      true,
		REJECTED: true,
	}

	IsAllowAdjustmentSttAfterPUP = map[string]bool{
		PUP:   true,
		STISC: true,
		STOSC: true,
		STI:   true,
	}
)

func SttStatusShipmentFinished() []string {
	sttStatus := make([]string, 0)
	for status, _ := range IsSttStatusShipmentFinished {
		sttStatus = append(sttStatus, status)
	}

	return sttStatus
}

func GenerateStatusLabel(status string, countryID, isExternal bool) string {
	trackingEnglish := SttStatusForSttTrackingEnglish
	tracking := SttStatusForSttTracking

	if isExternal {
		trackingEnglish = SttStatusForSttTrackingClientEnglish
		tracking = SttStatusForSttTrackingClient
	}

	// english will be get from english
	// if not exist use from bahasa indonesia
	if !countryID {
		s, ok := trackingEnglish[status]
		if ok {
			return s
		}
	}
	return tracking[status]
}
