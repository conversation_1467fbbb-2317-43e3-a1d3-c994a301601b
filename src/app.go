package src

import (
	"fmt"

	golptool "github.com/Lionparcel/go-lptool/v2"
	"github.com/Lionparcel/hydra/src/delivery/v1/pod"
	"github.com/prometheus/client_golang/prometheus/promhttp"

	"github.com/go-playground/validator/v10"
	"github.com/jasonlvhit/gocron"
	"github.com/labstack/echo"
	"github.com/labstack/echo/middleware"
	echoMiddleware "github.com/labstack/echo/middleware"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/registry"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/metric"
	"github.com/Lionparcel/hydra/shared/tracer"
	v1 "github.com/Lionparcel/hydra/src/delivery/v1"
	v2 "github.com/Lionparcel/hydra/src/delivery/v2"
	v3 "github.com/Lionparcel/hydra/src/delivery/v3"
	v4 "github.com/Lionparcel/hydra/src/delivery/v4"
)

// Server ...
type Server struct {
	httpServer    *echo.Echo
	uc            registry.UsecaseRegistry
	ucModule      registry.ModuleUsecaseRegistry
	config        *config.Config
	stopScheduler chan bool
	scheduler     *gocron.Scheduler
}

// InitServer ...
func InitServer(cfg *config.Config) *Server {
	if tracer.UseOtel {
		golptool.InitTracer(cfg.HostOtelCollector(), cfg.APMServiceName(), cfg.Environment())
	} else {
		tracer.InitOpenTracing()
	}

	echoServer := echo.New()
	echoServer.Use(tracer.OtelMiddleware(cfg.APMServiceName()))
	echoServer.Use(echoMiddleware.LoggerWithConfig(echoMiddleware.LoggerConfig{
		Format: `{"time":"${time_rfc3339_nano}","id":"${id}","remote_ip":"${remote_ip}","host":"${host}",` +
			`"method":"${method}","uri":"${uri}","status":${status},"error":"${error}",` +
			`"latency_human":"${latency_human}"}` + "\n",
	}))

	echoServer.Use(middleware.RequestID())
	echoServer.Use(echoMiddleware.Recover())
	echoServer.Use(metric.Middleware)

	// set tracer
	tracer.InitOpenTracing()

	echoServer.Validator = &CustomValidator{validator: validator.New()}
	ucCommon := registry.NewUsecaseRegistry(*cfg)
	ucModule := registry.NewModuleUsecaseRegistry(*cfg, ucCommon)

	return &Server{httpServer: echoServer, uc: ucCommon, ucModule: ucModule, config: cfg}
}

// Run ...
func (s *Server) Run() {
	// authDelivery := v1.NewAuthDelivery(s.uc.Auth())
	// authGroup := s.httpServer.Group(s.config.ServiceName() + `/v1/auth`)
	// authDelivery.Mount(authGroup, middleware.JWTVerify(s.config.Token().PublicKey, true), s.config.Token().PublicKey)
	s.httpServer.GET("/metric",func(c echo.Context) error {
		promhttp.Handler().ServeHTTP(c.Response(), c.Request())
		return nil
	})
	// Stt Manual
	sttManualDelivery := v1.NewSttManualDelivery(s.uc.SttManual(), s.config)
	sttManualGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/stt_manual`, tracer.EchoRestTracerMiddleware)
	sttManualDelivery.Mount(sttManualGroup, s.config.Token().PublicKey)

	// Stt
	SttDelivery := v1.NewSttDelivery(s.uc.Stt(), s.uc.GatewayStt(), s.config, s.uc.SttPayment(), shared.NewDictionaryError(), s.uc.RiskClassification())
	sttGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/stt`, tracer.EchoRestTracerMiddleware)
	SttDelivery.Mount(sttGroup, s.config.Token().PublicKey)

	// Stt Promo
	SttPromoDelivery := v1.NewSttPromoDelivery(s.config, s.uc.SttPromo())
	sttpromoGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/stt_promo`, tracer.EchoRestTracerMiddleware)
	SttPromoDelivery.Mount(sttpromoGroup, s.config.Token().PublicKey)

	// Pickup Manifest
	pickupManifestDelivery := v1.NewPickupManifestDelivery(s.uc.PickupManifest())
	pickupManifestGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/pickup/manifest`, tracer.EchoRestTracerMiddleware)
	pickupManifestDelivery.Mount(pickupManifestGroup, s.config.Token().PublicKey)

	// Dashboard Delivery
	dashboardDelivery := v1.NewDashboardDelivery(s.uc.DashboardDelivery())
	dashboardDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/dashboard-delivery`, tracer.EchoRestTracerMiddleware)
	dashboardDelivery.Mount(dashboardDeliveryGroup, s.config.Token().PublicKey)

	// Sti
	stiDelivery := v1.NewStiDelivery(s.uc.Sti())
	stiDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/sti`, tracer.EchoRestTracerMiddleware)
	stiDelivery.Mount(stiDeliveryGroup, s.config.Token().PublicKey)

	// Sti Sc
	stiScDelivery := v1.NewStiScDelivery(s.uc.StiSc())
	stiScGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/sti/sc`, tracer.EchoRestTracerMiddleware)
	stiScDelivery.Mount(stiScGroup, s.config.Token().PublicKey)

	// Sto Sc
	stoScDelivery := v1.NewStoScDelivery(s.config, s.uc.StoSc())
	stoScGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/sto-sc`, tracer.EchoRestTracerMiddleware)
	stoScDelivery.Mount(stoScGroup, s.config.Token().PublicKey)

	// Bag
	bagDelivery := v1.NewBagDelivery(s.uc.Bag(), s.uc.BagVendor(), s.config, shared.NewDictionaryError())
	bagGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/bag`, tracer.EchoRestTracerMiddleware)
	bagDelivery.Mount(bagGroup, s.config.Token().PublicKey)

	// Sti Dest
	stiDestDelivery := v1.NewStiDestDelivery(s.uc.StiDest())
	stiDestGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/sti-dest`, tracer.EchoRestTracerMiddleware)
	stiDestDelivery.Mount(stiDestGroup, s.config.Token().PublicKey)

	// Sti Dest Sc
	stiDestScDelivery := v1.NewStiDestScDelivery(s.uc.StiDestSc())
	stiDestScGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/sti-dest/sc`, tracer.EchoRestTracerMiddleware)
	stiDestScDelivery.Mount(stiDestScGroup, s.config.Token().PublicKey)

	// Handover
	handoverDelivery := v1.NewHandoverDelivery(s.uc.Handover())
	handoverGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/handover`, tracer.EchoRestTracerMiddleware)
	handoverDelivery.Mount(handoverGroup, s.config.Token().PublicKey)
	// Shortland
	shortlandDelivery := v1.NewShortlandDelivery(s.uc.Shortland())
	shortlandGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/shortland`, tracer.EchoRestTracerMiddleware)
	shortlandDelivery.Mount(shortlandGroup, s.config.Token().PublicKey)

	// DeliveryManifest
	deliveryManifestDelivery := v1.NewDeliveryManifestDelivery(s.uc.DeliveryManifest())
	deliveryManifestGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/delivery_manifest`, tracer.EchoRestTracerMiddleware)
	deliveryManifestDelivery.Mount(deliveryManifestGroup, s.config.Token().PublicKey)

	// Pod
	podDelivery := pod.NewPodDelivery(s.uc.Pod(), shared.NewDictionaryError(), s.config)
	podGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/pod`, tracer.EchoRestTracerMiddleware)
	podDelivery.Mount(podGroup, s.config.Token().PublicKey)

	// Pickup Corporate
	pickupCorporateDelivery := v1.NewPickupCorporateDelivery(v1.SetPickupCorporateUc(s.uc.PickupCorporate()), v1.SetPickupCorporateCfg(s.config))
	pickupCorporateGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/pickup-corporate`, tracer.EchoRestTracerMiddleware)
	pickupCorporateDelivery.Mount(pickupCorporateGroup, s.config.Token().PublicKey)

	reasonDelivery := v1.NewReasonDelivery(s.uc.Reason())
	reasonGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/reason`, tracer.EchoRestTracerMiddleware)
	reasonDelivery.Mount(reasonGroup, s.config.Token().PublicKey)

	customProcessDelivery := v1.NewCustomProcessDelivery(s.uc.CustomProcess())
	customProcessGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/custom-process`, tracer.EchoRestTracerMiddleware)
	customProcessDelivery.Mount(customProcessGroup, s.config.Token().PublicKey)

	logFailedSttElexysDelivery := v1.NewLogFailedSttElexysDelivery(s.uc.LogSttFailedElexys())
	logFailedSttElexysGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/log-failed-stt-elexys`, tracer.EchoRestTracerMiddleware)
	logFailedSttElexysDelivery.Mount(logFailedSttElexysGroup, s.config.Token().PublicKey)

	logMessageServiceDelivery := v1.NewLogMessageServiceDelivery(s.uc.LogMessage(), s.config)
	logMessageServiceGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/log-message-service`, tracer.EchoRestTracerMiddleware)
	logMessageServiceDelivery.Mount(logMessageServiceGroup, s.config.Token().PublicKey)

	utilDelivery := v1.NewUtilsDelivery(s.uc.Util())
	utilGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/util`, tracer.EchoRestTracerMiddleware)
	utilDelivery.Mount(utilGroup, s.config.Token().PublicKey)

	// V3
	// Stt V3
	gatewaySttDeliveryV3 := v3.NewSttDelivery(
		s.uc.GatewaySttTracking(),
		s.uc.GatewaySttStatus(),
		s.uc.GatewayStt(),
		s.config,
	)
	gatewaySttV3Group := s.httpServer.Group(s.config.ServiceName()+`/v3/gateway/stt`, tracer.EchoRestTracerMiddleware)
	gatewaySttDeliveryV3.Mount(gatewaySttV3Group, s.config.Token().PublicKey)

	// V4
	// Stt V4
	gatewaySttDeliveryV4 := v4.NewSttDelivery(
		s.uc.GatewaySttTracking(),
		s.uc.GatewaySttStatus(),
		s.uc.GatewayStt(),
	)
	gatewaySttGroupV4 := s.httpServer.Group(s.config.ServiceName()+`/v4/gateway/stt`, tracer.EchoRestTracerMiddleware)
	gatewaySttDeliveryV4.Mount(gatewaySttGroupV4, s.config.Token().PublicKey)

	// Gateway Delivery
	gatewayDelivery := v1.NewGatewaySttDeliveryDelivery(s.uc.GatewaySttStatus())
	gatewayDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/gateway/delivery`, tracer.EchoRestTracerMiddleware)
	gatewayDelivery.Mount(gatewayDeliveryGroup, s.config.Token().PublicKey)

	// Gateway Payment
	gatewayPayment := v1.NewGatewayPaymentDelivery(s.uc.GatewayPayment())
	gatewayPaymentGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/gateway/payment`, tracer.EchoRestTracerMiddleware)
	gatewayPayment.Mount(gatewayPaymentGroup, s.config.Token().PublicKey)

	// Cargo Delivery
	cargoDelivery := v1.NewCargoDelivery(s.uc.Cargo(), s.uc.CargoSearchFlight(), s.config)
	cargoDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/cargo`, tracer.EchoRestTracerMiddleware)
	cargoDelivery.Mount(cargoDeliveryGroup, s.config.Token().PublicKey)

	// On Process Delivery
	onProcessDelivery := v1.NewOnProcessDelivery(s.uc.OnProcess())
	onProcessDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/on-process`, tracer.EchoRestTracerMiddleware)
	onProcessDelivery.Mount(onProcessDeliveryGroup, s.config.Token().PublicKey)

	// Sabre
	sabreDelivery := v1.NewSabreDelivery(s.config)
	sabreGroup := s.httpServer.Group(s.config.ServiceName()+`/sabre`, tracer.EchoRestTracerMiddleware)
	sabreDelivery.Mount(sabreGroup, s.config.Token().PublicKey)

	// Del
	onDelDelivery := v1.NewDelDelivery(s.uc.Del())
	onDelDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/del`, tracer.EchoRestTracerMiddleware)
	onDelDelivery.Mount(onDelDeliveryGroup, s.config.Token().PublicKey)

	// Ngen
	nGenDelivery := v1.NewNgenDelivery(s.uc.Ngen())
	nGenDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/ngen`, tracer.EchoRestTracerMiddleware)
	nGenDelivery.Mount(nGenDeliveryGroup, s.config.Token().PublicKey)

	// Ninja
	ninjaDelivery := v1.NewNinjaDelivery(s.uc.Ninja())
	ninjaDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/ninja`, tracer.EchoRestTracerMiddleware)
	ninjaDelivery.Mount(ninjaDeliveryGroup, s.config.Token().PublicKey)

	// Elexys
	elexysDelivery := v1.NewElexysDelivery(s.uc.Elexys())
	elexysDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/elexys`, tracer.EchoRestTracerMiddleware)
	elexysDelivery.Mount(elexysDeliveryGroup, s.config.Token().PublicKey)

	// Report
	reportDelivery := v1.NewReportDelivery(s.uc.Report(), s.uc.SttTransaction())
	reportDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/report`, tracer.EchoRestTracerMiddleware)
	reportDelivery.Mount(reportDeliveryGroup, s.config.Token().PublicKey)

	// SttElexys
	sttElexysDelivery := v1.NewSttElexysDelivery(s.uc.SttElexys())
	sttElexysDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/stt-elexys`, tracer.EchoRestTracerMiddleware)
	sttElexysDelivery.Mount(sttElexysDeliveryGroup, s.config.Token().PublicKey)

	// Report V2
	reportDeliveryV2 := v2.NewReportDeliveryV2(s.uc.ReportV2(), s.uc.ReportV3())
	reportDeliveryV2Group := s.httpServer.Group(s.config.ServiceName()+`/v2/report`, tracer.EchoRestTracerMiddleware)
	reportDeliveryV2.Mount(reportDeliveryV2Group, s.config.Token().PublicKey)

	// Report V3
	reportDeliveryV3 := v3.NewReportDeliveryV3(s.uc.ReportV3())
	reportDeliveryV3Group := s.httpServer.Group(s.config.ServiceName()+`/v3/report`, tracer.EchoRestTracerMiddleware)
	reportDeliveryV3.Mount(reportDeliveryV3Group, s.config.Token().PublicKey)

	// Health Check
	healthDelivery := v1.NewHealthDelivery(s.uc.Health())
	healthGroup := s.httpServer.Group(s.config.ServiceName() + `/v1/health`)
	healthDelivery.Mount(healthGroup, s.config.Token().PublicKey)

	//Partner Log
	partnerLogDelivery := v1.NewPartnerLogDelivery(s.uc.PartnerLog())
	partnerLogGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/partner-log`, tracer.EchoRestTracerMiddleware)
	partnerLogDelivery.Mount(partnerLogGroup, s.config.Token().PublicKey)

	//Stt Transaction
	sttTransactionDelivery := v1.NewSttTransactionDelivery(s.uc.SttTransaction())
	sttTransactionGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/stt-transaction`, tracer.EchoRestTracerMiddleware)
	sttTransactionDelivery.Mount(sttTransactionGroup, s.config.Token().PublicKey)

	// Cargo V2
	cargoDeliveryV2 := v2.NewCargoDeliveryV2(s.uc.CargoV2(), s.config)
	cargoDeliveryV2Group := s.httpServer.Group(s.config.ServiceName()+`/v2/cargo`, tracer.EchoRestTracerMiddleware)
	cargoDeliveryV2.Mount(cargoDeliveryV2Group, s.config.Token().PublicKey)

	// Kejarcuan
	kejarcuanDelivery := v1.NewKejarcuanDelivery(s.uc.Kejarcuan(), s.config)
	kejarcuanDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/kejarcuan`, tracer.EchoRestTracerMiddleware)
	kejarcuanDelivery.Mount(kejarcuanDeliveryGroup, s.config.Token().PublicKey)

	// GatewayTrucking
	gatewayTruckingDelivery := v1.NewGatewayTruckingDelivery(s.uc.GatewayTrucking())
	gatewayTruckingDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/gateway/trucking`, tracer.EchoRestTracerMiddleware)
	gatewayTruckingDelivery.Mount(gatewayTruckingDeliveryGroup, s.config.Token().PublicKey)

	// JNE
	jneDelivery := v1.NewJneDelivery(s.uc.JNE())
	jneDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/jne`, tracer.EchoRestTracerMiddleware)
	jneDelivery.Mount(jneDeliveryGroup, s.config.Token().PublicKey)

	// RetryCargo
	retryCargoDelivery := v1.NewRetryCargoDelivery(s.uc.RetryCargo(), *s.config)
	retryCargoGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/retry_cargo`, tracer.EchoRestTracerMiddleware)
	retryCargoDelivery.Mount(retryCargoGroup, s.config.Token().PublicKey)

	// InstantBooking
	instantBookingDelivery := v1.NewInstantBookingDelivery(s.uc.InstantBooking())
	instantBookingGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/instant-booking`, tracer.EchoRestTracerMiddleware)
	instantBookingDelivery.Mount(instantBookingGroup, s.config.Token().PublicKey)

	// InstantBooking
	sttV2Delivery := v2.NewSttDelivery(s.uc.Stt(), s.uc.SttV2(), s.config, s.uc.Algo())
	sttV2Group := s.httpServer.Group(s.config.ServiceName()+`/v2/stt`, tracer.EchoRestTracerMiddleware)
	sttV2Delivery.Mount(sttV2Group, s.config.Token().PublicKey)

	// On Process Delivery
	onProcessV2Delivery := v2.NewOnProcessDelivery(s.uc.OnProcessV2())
	onProcessV2DeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v2/on-process`, tracer.EchoRestTracerMiddleware)
	onProcessV2Delivery.Mount(onProcessV2DeliveryGroup, s.config.Token().PublicKey)

	// Dashboard V2
	dashboardV2Delivery := v2.NewDashboardDelivery(s.uc.DashboardV2())
	dashboardV2DeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v2/dashboard`, tracer.EchoRestTracerMiddleware)
	dashboardV2Delivery.Mount(dashboardV2DeliveryGroup, s.config.Token().PublicKey)

	// Stt Manual V2
	sttManualV2Delivery := v2.NewSttManualV2Delivery(s.uc.SttManualV2())
	sttManualV2Group := s.httpServer.Group(s.config.ServiceName()+`/v2/stt_manual`, tracer.EchoRestTracerMiddleware)
	sttManualV2Delivery.Mount(sttManualV2Group, s.config.Token().PublicKey)

	LuwjistikDelivery := v1.NewLuwjistikDelivery(s.uc.Luwjistik(), *s.config)
	LuwjistikGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/luwjistik`, tracer.EchoRestTracerMiddleware)
	LuwjistikDelivery.Mount(LuwjistikGroup, s.config.Token().PublicKey)

	// Dashboard V1
	bulkDownloadDelivery := v1.NewBulkDownloadDelivery(s.uc.BulkDownload())
	bulkDownloadGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/bulk-download`, tracer.EchoRestTracerMiddleware)
	bulkDownloadDelivery.Mount(bulkDownloadGroup, s.config.Token().PublicKey)

	// Stt Manual V3
	sttManualV3Delivery := v3.NewSttManualV3Delivery(s.uc.SttManualV3())
	sttManualV3Group := s.httpServer.Group(s.config.ServiceName()+`/v3/stt_manual`, tracer.EchoRestTracerMiddleware)
	sttManualV3Delivery.Mount(sttManualV3Group, s.config.Token().PublicKey)

	// Pod V2
	podV2Delivery := v2.NewPodDelivery(s.uc.PodV2())
	podV2Group := s.httpServer.Group(s.config.ServiceName()+`/v2/pod`, tracer.EchoRestTracerMiddleware)
	podV2Delivery.Mount(podV2Group, s.config.Token().PublicKey)

	//Checkout
	checkoutDelivery := v1.NewCheckoutDelivery(s.uc.Checkout())
	checkoutGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/checkout`, tracer.EchoRestTracerMiddleware)
	checkoutDelivery.Mount(checkoutGroup, s.config.Token().PublicKey)

	//incomingOutgoing
	incomingOutgoingDelivery := v1.NewIncomingOutgoingDelivery(s.uc.IncomingOutgoing())
	incomingOutgoingGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/incoming-outgoing`, tracer.EchoRestTracerMiddleware)
	incomingOutgoingDelivery.Mount(incomingOutgoingGroup, s.config.Token().PublicKey)

	// Progressive Commision
	progressiveCommissionDelivery := v1.NewProgressiveCommissionDelivery(s.uc.ProgressiveCommission(), s.config)
	progressiveCommissionGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/progressive-commission`, tracer.EchoRestTracerMiddleware)
	progressiveCommissionDelivery.Mount(progressiveCommissionGroup, s.config.Token().PublicKey)

	//CustomProcessRole
	customProcessRoleDelivery := v1.NewCustomProcessRoleDelivery(s.uc.CustomProcessRole())
	customProcessRoleGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/custom-process-role`, tracer.EchoRestTracerMiddleware)
	customProcessRoleDelivery.Mount(customProcessRoleGroup, s.config.Token().PublicKey)

	// Read STT Paid
	readSttPaidDelivery := v1.NewReadSttPaid(s.uc.ReadSttPaid())
	readSttPaidGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/read-stt-paid`, tracer.EchoRestTracerMiddleware)
	readSttPaidDelivery.Mount(readSttPaidGroup, s.config.Token().PublicKey)

	// Rebuttal Dex
	rebuttalDexDelivery := v1.NewRebuttalDexDelivery(s.uc.RebuttalDex())
	rebuttalDexGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/rebuttal-dex`, tracer.EchoRestTracerMiddleware)
	rebuttalDexDelivery.Mount(rebuttalDexGroup, s.config.Token().PublicKey)

	// Webhook Delivery
	webhookDelivery := v1.NewWebhookDelivery(s.uc.Stt(), s.uc.Webhook(), s.config)
	webhookDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/webhook`, tracer.EchoRestTracerMiddleware)
	webhookDelivery.Mount(webhookDeliveryGroup, s.config.Token().PublicKey)

	// Report V4
	reportDeliveryV4 := v4.NewReportDeliveryV4(s.uc.ReportV4())
	reportDeliveryV4Group := s.httpServer.Group(s.config.ServiceName()+`/v4/report`, tracer.EchoRestTracerMiddleware)
	reportDeliveryV4.Mount(reportDeliveryV4Group, s.config.Token().PublicKey)

	// Corporate Dashboard
	corporateDashboard := v1.NewCorporateDashboardDelivery(s.uc.CorporateDashboard())
	corporateDashboardGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/corporate-dashboard`, tracer.EchoRestTracerMiddleware)
	corporateDashboard.Mount(corporateDashboardGroup, s.config.Token().PublicKey)

	// Ninja V2
	ninjaV2Delivery := v2.NewNinjaDelivery(s.uc.NinjaV2())
	ninjaV2DeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v2/ninja`, tracer.EchoRestTracerMiddleware)
	ninjaV2Delivery.Mount(ninjaV2DeliveryGroup, s.config.Token().PublicKey)

	// ClaimStatus
	claimStatus := v1.NewClaimStatusDelivery(s.uc.ClaimStatus())
	claimStatusGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/claim-status`, tracer.EchoRestTracerMiddleware)
	claimStatus.Mount(claimStatusGroup, s.config.Token().PublicKey)

	// Sender Dashboard
	senderDashboard := v1.NewSenderDashboardDelivery(s.uc.CorporateDashboard())
	senderDashboardGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/sender-dashboard`, tracer.EchoRestTracerMiddleware)
	senderDashboard.Mount(senderDashboardGroup, s.config.Token().PublicKey)

	// POS Dashboard
	posDashboard := v1.NewPosDashboardDelivery(s.uc.CorporateDashboard())
	posDashboardGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/pos-dashboard`, tracer.EchoRestTracerMiddleware)
	posDashboard.Mount(posDashboardGroup, s.config.Token().PublicKey)

	// History Manifest
	historyManifestDashboard := v1.NewHistoryManifestDelivery(s.uc.HistoryManifest())
	historyManifestDashboardGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/history-manifest`, tracer.EchoRestTracerMiddleware)
	historyManifestDashboard.Mount(historyManifestDashboardGroup, s.config.Token().PublicKey)

	// Ready To Cargo
	readyToCargoDelivery := v1.NewReadyToCargoDelivery(s.uc.ReadyToCargo(), s.config)
	readyToCargoDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/ready-to-cargo`, tracer.EchoRestTracerMiddleware)
	readyToCargoDelivery.Mount(readyToCargoDeliveryGroup, s.config.Token().PublicKey)

	// STI v2
	stiDeliveryV2 := v2.NewStiDelivery(s.uc.Sti())
	stiDeliveryGroupV2 := s.httpServer.Group(s.config.ServiceName()+`/v2/sti`, tracer.EchoRestTracerMiddleware)
	stiDeliveryV2.Mount(stiDeliveryGroupV2, s.config.Token().PublicKey)

	// Sti Dest V2
	stiDestDeliveryV2 := v2.NewStiDestDelivery(s.uc.StiDest())
	stiDestGroupV2 := s.httpServer.Group(s.config.ServiceName()+`/v2/sti-dest`, tracer.EchoRestTracerMiddleware)
	stiDestDeliveryV2.Mount(stiDestGroupV2, s.config.Token().PublicKey)

	// Gateway Cargo
	gatewayCargo := v1.NewGatewayCargoDelivery(s.uc.GatewayCargo(), s.config)
	gatewayCargoGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/gateway/cargo`, tracer.EchoRestTracerMiddleware)
	gatewayCargo.Mount(gatewayCargoGroup, s.config.Token().PublicKey)

	// Gateway STT
	gatewaySttDelivery := v1.NewGatewaySTTDelivery(s.uc.GatewayStt(), s.uc.Stt())
	gatewaySttGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/gateway/stt`, tracer.EchoRestTracerMiddleware)
	gatewaySttDelivery.Mount(gatewaySttGroup, s.config.Token().PublicKey)

	// Cargo Reserve
	cargoReserveDelivery := v1.NewCargoReserveDelivery(s.uc.CargoReserve(), s.config, shared.NewDictionaryError())
	cargoReserveGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/cargo-reserve`, tracer.EchoRestTracerMiddleware)
	cargoReserveDelivery.Mount(cargoReserveGroup, s.config.Token().PublicKey)

	// Resolution Center
	resolutionCentreDelivery := v1.NewResolutionCentreDelivery(s.config, s.uc.ResolutionCentre())
	resolutionCentreGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/resolution-center`, tracer.EchoRestTracerMiddleware)
	resolutionCentreDelivery.Mount(resolutionCentreGroup, s.config.Token().PublicKey)

	// Discussion Forum
	discussionForumDelivery := v1.NewDiscussionForumDelivery(s.config, s.uc.DiscussionForum())
	discussionForumGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/discussion-forum`, tracer.EchoRestTracerMiddleware)
	discussionForumDelivery.Mount(discussionForumGroup, s.config.Token().PublicKey)

	// Resolution Center
	salesforceDelivery := v1.NewSalesForceDelivery(s.config, s.uc.Salesforce())
	salesforceGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/sales-force`, tracer.EchoRestTracerMiddleware)
	salesforceDelivery.Mount(salesforceGroup, s.config.Token().PublicKey)

	// Stt Transaction V2
	sttTransactionV2Delivery := v2.NewSttTransactionV2Delivery(s.config, s.uc.SttTransaction())
	sttTransactionV2Group := s.httpServer.Group(s.config.ServiceName()+`/v2/stt-transaction`, tracer.EchoRestTracerMiddleware)
	sttTransactionV2Delivery.Mount(sttTransactionV2Group, s.config.Token().PublicKey)

	// Stt Transaction V2
	firebaseV1Delivery := v1.NewFirebaseDelivery(s.config, s.uc.Firebase())
	firebaseV1Group := s.httpServer.Group(s.config.ServiceName()+`/v1/firebase`, tracer.EchoRestTracerMiddleware)
	firebaseV1Delivery.Mount(firebaseV1Group, s.config.Token().PublicKey)

	// Notification Penalty
	NotificationPenaltyV1Delivery := v1.NewNotificationPenaltyDelivery(s.uc.NotificationPenalty(), s.config)
	NotificationPenaltyV1Group := s.httpServer.Group(s.config.ServiceName()+`/v1/notification-penalty`, tracer.EchoRestTracerMiddleware)
	NotificationPenaltyV1Delivery.Mount(NotificationPenaltyV1Group, s.config.Token().PublicKey)

	// Dashboard Delivery V2
	dashboardDeliveryV2 := v2.NewDashboardDeliveryV2(s.config, s.uc.DashboardDelivery())
	dashboardDeliveryV2Group := s.httpServer.Group(s.config.ServiceName()+`/v2/dashboard-delivery`, tracer.EchoRestTracerMiddleware)
	dashboardDeliveryV2.Mount(dashboardDeliveryV2Group, s.config.Token().PublicKey)

	// Hold Balance History
	HoldBalanceHistoryV1Delivery := v1.NewHoldBalanceHistoryDelivery(s.uc.HoldBalanceHistory())
	HoldBalanceHistoryV1Group := s.httpServer.Group(s.config.ServiceName()+`/v1/hold_balance_history`, tracer.EchoRestTracerMiddleware)
	HoldBalanceHistoryV1Delivery.Mount(HoldBalanceHistoryV1Group, s.config.Token().PublicKey)

	// Upload
	UploadV1Delivery := v1.NewUploadDelivery(s.config, s.uc.Upload())
	UploadV1Group := s.httpServer.Group(s.config.ServiceName()+`/v1/upload`, tracer.EchoRestTracerMiddleware)
	UploadV1Delivery.Mount(UploadV1Group, s.config.Token().PublicKey)

	// Log RTC
	LogRtcV1Delivery := v1.NewLogRtcDelivery(s.config, s.uc.LogRtc())
	LogRtcV1Group := s.httpServer.Group(s.config.ServiceName()+`/v1/log-rtc`, tracer.EchoRestTracerMiddleware)
	LogRtcV1Delivery.Mount(LogRtcV1Group, s.config.Token().PublicKey)

	// Dex Assessment
	DexAssessment := v1.NewDexAssessmentDelivery(s.uc.DexAssessment(), s.config)
	dexAssessmentGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/dex-assessment`, tracer.EchoRestTracerMiddleware)
	DexAssessment.Mount(dexAssessmentGroup, s.config.Token().PublicKey)

	// Dex Assessment Dashboard
	DexAssessmentDashboard := v1.NewDexAssessmentDashboardDelivery(s.uc.DexAssessmentDashboard(), s.config)
	dexAssessmentDashboardGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/dex-assessment/dashboard`, tracer.EchoRestTracerMiddleware)
	DexAssessmentDashboard.Mount(dexAssessmentDashboardGroup, s.config.Token().PublicKey)

	// Dex Assessment Dashboard Consolidator
	DaDashboardConsolidator := v1.NewDexAssessmentDashboardConsolidatorDelivery(s.ucModule.DexAssessmentDashboardConsolidator(), s.config)
	daDashboardConsolidatorGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/dex-assessment-dashboard-consolidator`, tracer.EchoRestTracerMiddleware)
	DaDashboardConsolidator.Mount(daDashboardConsolidatorGroup, s.config.Token().PublicKey)

	// Shortlink
	ShortlinkDelivery := v1.NewShortlinkDelivery(s.uc.Shortlink())
	ShortlinkV1Group := s.httpServer.Group(s.config.ServiceName()+`/v1/shortlink`, tracer.EchoRestTracerMiddleware)
	ShortlinkDelivery.Mount(ShortlinkV1Group, s.config.Token().PublicKey)

	// Stt Clearance
	ClearanceDelivery := v1.NewClearanceDelivery(s.uc.Clearance(), s.config)
	ClearanceGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/clearance`, tracer.EchoRestTracerMiddleware)
	ClearanceDelivery.Mount(ClearanceGroup, s.config.Token().PublicKey)

	// Stt Clearance
	CbpPickupDelivery := v1.NewPickupManifestCbpDelivery(s.uc.PickupManifestCbp(), s.config)
	PickupManifestCbpGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/cbp/pickup`, tracer.EchoRestTracerMiddleware)
	CbpPickupDelivery.Mount(PickupManifestCbpGroup, s.config.Token().PublicKey)

	// Release
	ReleaseDelivery := v1.NewReleaseDelivery(s.uc.Release())
	ReleaseV1Group := s.httpServer.Group(s.config.ServiceName()+`/v1/release`, tracer.EchoRestTracerMiddleware)
	ReleaseDelivery.Mount(ReleaseV1Group, s.config.Token().PublicKey)

	// Request Priority Delivery Delivery
	RequestPriorityDeliveryDelivery := v1.NewRequestPriorityDeliveryDelivery(s.config, s.uc.RequestPriorityDelivery())
	RequestPriorityDelivery := s.httpServer.Group(s.config.ServiceName()+`/v1/request-priority-delivery`, tracer.EchoRestTracerMiddleware)
	RequestPriorityDeliveryDelivery.Mount(RequestPriorityDelivery, s.config.Token().PublicKey)

	// Priority Delivery Management
	PriorityManagementDelivery := v1.NewPriorityDeliveryManagementDelivery(s.config, s.uc.PriorityManagementDelivery())
	PriorityManagementV1Group := s.httpServer.Group(s.config.ServiceName()+`/v1/priority-delivery-management`, tracer.EchoRestTracerMiddleware)
	PriorityManagementDelivery.Mount(PriorityManagementV1Group, s.config.Token().PublicKey)

	// PT.POS
	ptposDelivery := v1.NewPtPOSDelivery(*s.config, s.uc.PtPosUC())
	ptposDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/ptpos`, tracer.EchoRestTracerMiddleware)
	ptposDelivery.Mount(ptposDeliveryGroup, s.config.Token().PublicKey)

	// RetryCargo V2
	retryCargoV2Delivery := v2.NewRetryCargoV2Delivery(s.uc.RetryCargo(), *s.config)
	retryCargoV2Group := s.httpServer.Group(s.config.ServiceName()+`/v2/retry_cargo`, tracer.EchoRestTracerMiddleware)
	retryCargoV2Delivery.Mount(retryCargoV2Group, s.config.Token().PublicKey)

	s.setupDeliveryGroups()

	// STI SC
	stiScV2Delivery := v2.NewStiScV2Delivery(v2.SetSTIScDeliveryUc(s.uc.StiSc()), v2.SetSTIScDeliveryConfig(s.config))
	stiScV2Group := s.httpServer.Group(s.config.ServiceName()+`/v2/sti/sc`, tracer.EchoRestTracerMiddleware)
	stiScV2Delivery.Mount(stiScV2Group, s.config.Token().PublicKey)

	// FEEDBACK
	feedbackDelivery := v1.NewFeedbackDelivery(s.uc.Feedback())
	feedbackGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/feedback`, tracer.EchoRestTracerMiddleware)
	feedbackDelivery.Mount(feedbackGroup, s.config.Token().PublicKey)

	// FRAUD ANALYSIS
	fraudAnalysisDelivery := v1.NewFraudAnalysisDelivery(s.config, s.uc.RiskClassification())
	fraudAnalysisGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/fraud-analysis`, tracer.EchoRestTracerMiddleware)
	fraudAnalysisDelivery.Mount(fraudAnalysisGroup, s.config.Token().PublicKey)

	// Dispatch
	dispatchDelivery := v1.NewDispatchDelivery(s.uc.Dispatch())
	dispatchGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/dispatch`, tracer.EchoRestTracerMiddleware)
	dispatchDelivery.Mount(dispatchGroup, s.config.Token().PublicKey)

	// kulioner
	kulionerDelivery := v1.NewKulionerDelivery(s.uc.Kulioner(), s.config, shared.NewDictionaryError())
	kulionerDeliveryGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/kulioner`, tracer.EchoRestTracerMiddleware)
	kulionerDelivery.Mount(kulionerDeliveryGroup, s.config.Token().PublicKey)
	
	quoteGroupDelivery := v1.NewQuoteGroupDelivery(s.uc.QuoteGroup())
	quoteGroupGroup := s.httpServer.Group(s.config.ServiceName()+`/v1/quote-group`, tracer.EchoRestTracerMiddleware)
	quoteGroupDelivery.Mount(quoteGroupGroup, s.config.Token().PublicKey)

	if err := s.httpServer.Start(fmt.Sprintf(":%d", s.config.Port())); err != nil {
		logger.E(err)
	}
}

// CustomValidator ...
type CustomValidator struct {
	validator *validator.Validate
}

// Validate ...
func (cv *CustomValidator) Validate(i interface{}) error {
	return cv.validator.Struct(i)
}

// Scheduler ..
func (s *Server) Scheduler() {
	scheduler := gocron.NewScheduler()
	s.scheduler = scheduler

	fmt.Println(`Start Scheduler`)
	// scheduler.Every(4).Hours().Do(s.uc.Cargo().SchedulerUpdateEstimationTimeCargoNgen)
	// scheduler.Every(4).Hours().Do(s.uc.CargoV2().SchedulerUpdateEstimationTimeCargoNgenV2)
	// scheduler.Every(10).Minutes().Do(s.uc.Elexys().SchedulerRetryPickupManifest)

	// for testing in dev
	// scheduler.Every(10).Minutes().Do(s.uc.Cargo().SchedulerUpdateEstimationTimeCargoNgen)
	// scheduler.Every(10).Minutes().Do(s.uc.CargoV2().SchedulerUpdateEstimationTimeCargoNgenV2)

	//Scheduler delete data on gcs related to table bulk_download
	scheduler.Every(1).Day().At("00:00").Do(s.uc.ReportV2().SchedulerDeletedFileBulkDownload)

	stopped := scheduler.Start()
	s.stopScheduler = stopped
	<-stopped
	fmt.Println(`Stop Scheduler`)
}

func (s *Server) GetEchoEngine() *echo.Echo {
	return s.httpServer
}

func (s *Server) GetScheduler() chan bool {
	if s.scheduler != nil {
		s.scheduler.Clear()
		return s.stopScheduler
	}

	return nil
}

type DeliveryConfig struct {
	Name       string
	NewHandler func() interface{} // Use interface{} to handle different types
}

func (s *Server) mountDeliveryGroups(deliveries []DeliveryConfig) {
	for _, delivery := range deliveries {
		group := s.httpServer.Group(s.config.ServiceName()+delivery.Name, tracer.EchoRestTracerMiddleware)
		handler := delivery.NewHandler()

		switch h := handler.(type) {
		case v1.SttDueDelivery: // STT Due
			h.Mount(group, s.config.Token().PublicKey)
		case v3.DashboardDeliveryV3: // Dashboard Delivery V3
			h.Mount(group, s.config.Token().PublicKey)
		default:
			panic("unknown handler type")
		}
	}
}

func (s *Server) setupDeliveryGroups() {
	deliveries := []DeliveryConfig{
		{
			Name: "/v1/stt-due",
			NewHandler: func() interface{} {
				return v1.NewSttDueDelivery(v1.SetSTTDueDeliveryConfig(s.config), v1.SetSTTDueDeliveryUc(s.uc.SttDueUC()))
			},
		},
		{
			Name:       "/v3/dashboard-delivery",
			NewHandler: func() interface{} { return v3.NewDashboardDeliveryV3(s.config, s.uc.DashboardDelivery()) },
		},
	}

	s.mountDeliveryGroups(deliveries)
}
