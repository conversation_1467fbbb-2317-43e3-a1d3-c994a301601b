package repository

import (
	"context"
	"time"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	ctxRollout "github.com/rollout/rox-go/v5/core/context"
)

type flagManagementRepositoryCtx struct {
	cfg *config.Config
}

type FlagManagementRepository interface {
	CloudBeesEnableMappingS3Link(ctx context.Context) bool
	CloudBeesIsSendMessageDELAfterTransferDelivery(ctx context.Context) bool
	CloudBeesEnableBookingInterpack(ctx context.Context) bool
	CloudBeesWebURL(ctx context.Context) string

	CloudBeesRTSHQDefaultName(ctx context.Context) string
	CloudBeesRTSHQDefaultAddress(ctx context.Context) string
	CloudBeesRTSHQDefaultPhoneNumber(ctx context.Context) string
	CloudBeesRTSHQDefaultDistrictCode(ctx context.Context) string
	CloudBeesRTSHQDefaultAddressType(ctx context.Context) string
	CloudBeesDEXAssessmentDashboardCache(ctx context.Context) time.Duration
	ShipmentCAGrossWeight(ctx context.Context, shipmentAccessWeigth model.ShipmentAccessWeigth) bool
	CloudBeesSamedayCutOffTime(ctx context.Context) string
	CloudBeesSttHandleMissingStatusDate(ctx context.Context) string
	CloudBeesHoldSttPenalty(ctx context.Context) *model.HoldSttPenalty
	CloudBeesDFODPastiProgram(ctx context.Context) bool
	CloudBeesCodDfodDayThreshold(ctx context.Context) time.Duration
	CloudBeesDisableCodDfodForLimitedNumber(ctx context.Context) bool
}

func NewFlagManagementRepository(
	cfg *config.Config,
) FlagManagementRepository {
	return &flagManagementRepositoryCtx{
		cfg: cfg,
	}
}

func (c *flagManagementRepositoryCtx) ShipmentCAGrossWeight(ctx context.Context, shipmentAccessWeigth model.ShipmentAccessWeigth) bool {
	opName := `flagManagementRepositoryCtx-ShipmentCAGrossWeight`
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": shipmentAccessWeigth})
	}()

	rolloutCtx := ctxRollout.NewContext(map[string]interface{}{
		"shipment_id":           shipmentAccessWeigth.ShipmentID,
		"origin_city_code":      shipmentAccessWeigth.OriginCityCode,
		"destination_city_code": shipmentAccessWeigth.DestinationCityCode,
	})
	return c.cfg.FlagManagement().CloudBees.ShipmentCAGrossWeight.IsEnabled(rolloutCtx)
}

func (c *flagManagementRepositoryCtx) CloudBeesEnableMappingS3Link(ctx context.Context) bool {
	opName := `flagManagementRepositoryCtx-CloudBeesEnableMappingS3Link`
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{})
	}()

	return c.cfg.FlagManagement().CloudBees.EnableMappingS3Link.IsEnabled(nil)
}

func (c *flagManagementRepositoryCtx) CloudBeesIsSendMessageDELAfterTransferDelivery(ctx context.Context) bool {
	opName := `flagManagementRepositoryCtx-CloudBeesIsSendMessageDELAfterTransferDelivery`
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{})
	}()

	return c.cfg.FlagManagement().CloudBees.IsSendMessageDELAfterTransferDelivery.IsEnabled(nil)
}

func (c *flagManagementRepositoryCtx) CloudBeesEnableBookingInterpack(ctx context.Context) bool {
	opName := `flagManagementRepositoryCtx-CloudBeesEnableBookingInterpack`
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{})
	}()

	return c.cfg.FlagManagement().CloudBees.EnableBookingInterpack.IsEnabled(nil)
}

func (c *flagManagementRepositoryCtx) CloudBeesWebURL(ctx context.Context) string {
	opName := `flagManagementRepositoryCtx-CloudBeesWebURL`
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{})
	}()
	return c.cfg.FlagManagement().CloudBees.WebURL.GetValueAsString(nil)
}
func (c *flagManagementRepositoryCtx) CloudBeesRTSHQDefaultName(ctx context.Context) string {
	opName := `flagManagementRepositoryCtx-RTSHQDefaultName`

	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{})
	}()

	return c.cfg.FlagManagement().CloudBees.RTSHQDefaultName.GetValueAsString(nil)
}

func (c *flagManagementRepositoryCtx) CloudBeesRTSHQDefaultAddress(ctx context.Context) string {
	opName := `flagManagementRepositoryCtx-RTSHQDefaultAddress`
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{})
	}()

	return c.cfg.FlagManagement().CloudBees.RTSHQDefaultAddress.GetValueAsString(nil)
}

func (c *flagManagementRepositoryCtx) CloudBeesRTSHQDefaultPhoneNumber(ctx context.Context) string {
	opName := `flagManagementRepositoryCtx-RTSHQDefaultPhoneNumber`
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{})
	}()

	return c.cfg.FlagManagement().CloudBees.RTSHQDefaultPhoneNumber.GetValueAsString(nil)
}

func (c *flagManagementRepositoryCtx) CloudBeesRTSHQDefaultDistrictCode(ctx context.Context) string {
	opName := `flagManagementRepositoryCtx-RTSHQDefaultDistrictCode`
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{})
	}()

	return c.cfg.FlagManagement().CloudBees.RTSHQDefaultDistrictCode.GetValueAsString(nil)
}

func (c *flagManagementRepositoryCtx) CloudBeesRTSHQDefaultAddressType(ctx context.Context) string {
	opName := `flagManagementRepositoryCtx-RTSHQDefaultAddressType`
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{})
	}()

	return c.cfg.FlagManagement().CloudBees.RTSHQDefaultAddressType.GetValueAsString(nil)
}

func (c *flagManagementRepositoryCtx) CloudBeesDEXAssessmentDashboardCache(ctx context.Context) time.Duration {
	opName := `flagManagementRepositoryCtx-DEXAssessmentDashboardCache`
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	val := 0

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"result": val})
	}()

	val = c.cfg.FlagManagement().CloudBees.DEXAssessmentDashboardCache.GetValue(nil)
	return time.Duration(val) * time.Minute
}

func (c *flagManagementRepositoryCtx) CloudBeesSamedayCutOffTime(ctx context.Context) string {
	opName := `flagManagementRepositoryCtx-CloudBeesSamedayCutOffTime`
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	val := 0

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"result": val})
	}()

	return c.cfg.FlagManagement().CloudBees.SamedayCutOffTime.GetValue(nil)
}

func (c *flagManagementRepositoryCtx) CloudBeesHoldSttPenalty(ctx context.Context) *model.HoldSttPenalty {
	opName := `flagManagementRepositoryCtx-CloudBeesHoldSttPenalty`
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	val := new(model.HoldSttPenalty)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"result": val})
	}()

	val.PenaltyAmount = c.cfg.FlagManagement().CloudBees.HoldSttPenaltyAmount.GetValue(nil)
	val.PenaltyType = c.cfg.FlagManagement().CloudBees.HoldSttPenaltyType.GetValue(nil)
	val.PenaltyMin = c.cfg.FlagManagement().CloudBees.HoldSttPenaltyMin.GetValue(nil)
	val.PenaltyMax = c.cfg.FlagManagement().CloudBees.HoldSttPenaltyMax.GetValue(nil)

	return val
}

func (c *flagManagementRepositoryCtx) CloudBeesDFODPastiProgram(ctx context.Context) bool {
	opName := `flagManagementRepositoryCtx-CloudBeesDFODPastiProgram`
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{})
	}()

	return c.cfg.FlagManagement().CloudBees.DFODPastiProgram.IsEnabled(nil)
}
