package repository

import (
	"context"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/config/cache"
	"github.com/Lionparcel/hydra/config/database"
	"github.com/Lionparcel/hydra/src/model"
)

type shipmentRepositoryCtx struct {
	cfg   *config.Config
	DB    database.DbrDatabase
	Cache cache.Client
}

// ShipmentRepository ...
type ShipmentRepository interface {
	GetShipmentAlgoByID(params model.ShipmentViewAlgoByIDParams) (*model.ShipmentAlgo, error)
	GetShipmentAlgoByGroupBookingID(params model.ShipmentViewAlgoGroupByIDParams) (*model.ShipmentAlgo, error)
	Create(ctx context.Context, shipment *model.Shipment) error
	Get(ctx context.Context, shipment *model.ShipmentViewParams) (*model.Shipment, error)
	GetShipment(ctx context.Context, params *model.ShipmentViewParams) (*model.Shipment, error)
	Select(ctx context.Context, params *model.ShipmentViewParams) ([]model.Shipment, error)
	CreateBulk(ctx context.Context, shipment []model.Shipment) error
	CancelShipment(ctx context.Context, shipment *model.Shipment) error
	UpdateShipmentGroupBookingID(ctx context.Context, shipment *model.Shipment) error
	UpdateShipmentPackageSender(ctx context.Context, params *model.ShipmentPacket) error
	GetShipmentMiddleware(ctx context.Context, shipmentID string, token string) (*model.ShipmentAlgo, error)
	GetShipmentAlgoResolutionCentreByID(ctx context.Context, shipmentID string) (*model.ShipmentAlgoResolutionCentre, error)
}

// NewShipmentRepository ...
func NewShipmentRepository(cfg *config.Config) ShipmentRepository {
	return &shipmentRepositoryCtx{
		cfg: cfg,
		DB:  cfg.DB(),
	}
}
