package repository

import (
	"context"

	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/abiewardani/dbr/v2"
)

func (c *sttDueRepository) StiDestDelFindAllPaginate(ctx context.Context, params model.SttDueStiDestDelFindAllAndPaginateParameter) ([]model.STTDueWithSTTAndPriorityDeliveryModel, int64, error) {
	var (
		opName  = "sttDueRepo.StiDestDelFindAllPaginate"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		sttDues     []model.STTDueWithSTTAndPriorityDeliveryModel
		sttDueTotal model.STTDueModelTotal
		err         error
		totalData   int64

		table = c.DB.SlaveAnalytics()

		sttDue = table.Select(
			`sd_id`, `sd_ref_type`, `sd_ref_id`, `sd_stt_no`, `sd_ref_no`, `sd_cargo_no`,
			`sd_booked_type`, `sd_cargo_type`, `sd_target_status`,
			`sd_target_due_date`, `sd_is_show`, `sd_meta`, `stt_due.eligible_archived_type`, `stt.eligible_archived_type`,
			`CASE WHEN sd_target_due_date = '0000-00-00 00:00:00' THEN '' WHEN sd_target_due_date >= now() THEN 'Saat ini' ELSE 'Telat' END AS deadline`,
			`stt_product_type`, `stt_gross_weight`, `stt_last_status_id`, `stt_total_piece`, `stt_origin_city_id`, `stt_origin_city_name`, `stt_updated_at`, `stt_updated_name`,
		)

		sttDueCount = table.Select(`COUNT(sd_id) AS __total__`)
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": sttDue, "error": err})
	}()

	c.filterFindAllAndPaginateStiDestDel(sttDue, params)
	if _, err = sttDue.LoadContext(selfCtx, &sttDues); err != nil {
		return nil, totalData, err
	}

	if !params.IsForExportData {
		// get total data
		params.IsForCount = true
		c.filterFindAllAndPaginateStiDestDel(sttDueCount, params)
		if err = sttDueCount.LoadOneContext(selfCtx, &sttDueTotal); err != nil {
			return nil, totalData, err
		}
	}

	totalData = sttDueTotal.TotalData

	return sttDues, totalData, err
}

func (c *sttDueRepository) filterFindAllAndPaginateStiDestDel(table *dbr.SelectStmt, params model.SttDueStiDestDelFindAllAndPaginateParameter) {
	table.From(`stt_due`).
		Join(`stt`, `sd_stt_no = stt_no`)

	table.Where(`stt_due.eligible_archived_type = ?`, 0)
	table.Where(`stt.eligible_archived_type = ?`, 0)
	table.Where(`sd_ref_type = ?`, params.PartnerType)
	table.Where(`sd_ref_id = ?`, params.PartnerID)
	table.Where(`sd_is_show = ?`, 1)
	table.Where("sd_target_status IN ('STI-DEST', 'DEL')")

	switch params.Deadline {
	case model.STTDueRepoNowDeadline:
		table.Where(`sd_arr_date >= date_sub(now(), interval 7 day)`).
			Where("sd_target_due_date >= NOW()")
	case model.STTDueRepoOverdueDeadline:
		table.Where(`sd_arr_date >= date_sub(now(), interval 7 day)`).
			Where("sd_target_due_date < NOW()")
	default:
		table.Where(`sd_arr_date >= date_sub(now(), interval 7 day) OR sd_arr_date = '0000-00-00'`)
	}

	if params.Query != "" {
		paramQuery := "%" + params.Query + "%"
		table.Where(`(sd_stt_no LIKE ? OR sd_ref_no LIKE ? OR sd_cargo_no LIKE ?)`, paramQuery, paramQuery, paramQuery)
	}

	if params.StatusReturn != "" {
		table.Join(`priority_delivery`, `sd_stt_no = pd_stt_no`)
		table.Where(`priority_delivery.eligible_archived_type = 0 and pd_is_show = 1`)

		if params.StatusReturn == "POD" {
			table.Where(`pd_status_return = 'POD' and ((pd_flag = '' AND pd_deadline_return <= CONCAT(DATE(NOW()), ' 23:59:59')) OR pd_flag != '')`)
		}
		if params.StatusReturn == "RTS" {
			table.Where(`pd_status_return = 'RTS' and pd_deadline_return <= DATE_ADD(CONCAT(DATE(NOW()), ' 23:59:59'), INTERVAL 1 DAY)`)
		}
		if params.StatusReturn == "HAL" {
			table.Where(`pd_status_return ?`, params.StatusReturn)
		}
	}

	if params.CargoType != "" {
		table.Where(`sd_cargo_type = ?`, params.CargoType)
	}

	if len(params.SttStatus) > 0 {
		table.Where(`stt_last_status_id IN ?`, params.SttStatus)
	}

	table.OrderBy("stt.stt_updated_at DESC")

	isNotNeedPaginate := params.IsForCount || params.IsForExportData

	if params.PerPage > 0 && !isNotNeedPaginate {
		table.Limit(uint64(params.PerPage))
	}

	if params.Offset > 0 && !isNotNeedPaginate {
		table.Offset(uint64(params.Offset))
	}

	return
}
