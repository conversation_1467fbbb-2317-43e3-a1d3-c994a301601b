package repository

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"github.com/Lionparcel/go-lptool/lputils"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"net/http"
	"time"

	"github.com/Lionparcel/hydra/src/model"
)

func (c *shipmentRepositoryCtx) getAlgoToken(ctx context.Context) (string, error) {
	respError := model.AlgoResponseError{}
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	algoToken, _ := c.cfg.Cache().Get("algo_token")
	if algoToken == `` {
		requestURL := fmt.Sprintf("%s/v1/account/auth/login",
			c.cfg.ALGOURL(),
		)
		body := model.AlgoAuthRequest{
			Username: c.cfg.AlgoAuthUsername(),
			Password: c.cfg.AlgoAuthPassword(),
			Role:     c.cfg.AlgoAuthRole(),
		}

		requestByte, _ := json.Marshal(body)
		resp, statusCode, err := shared.GetHTTPRequestJSON(ctx, "POST", requestURL, bytes.NewBuffer(requestByte), headers)
		if err != nil || statusCode != http.StatusOK {
			logger.Ef("%v:%v:%v", statusCode, err, string(resp))
			err = json.Unmarshal(resp, &respError)
			return "", shared.NewMultiStringBadRequestError(respError.ErrorID, map[string]string{
				"en": respError.Message.En,
				"id": respError.Message.ID,
			})
		}
		algoAuth := model.AlgoAuthResponse{}
		err = json.Unmarshal(resp, &algoAuth)
		if err != nil {
			logger.E(err)
			return "", shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed parse json response token",
				"id": "Gagal parse json response token",
			})
		}
		algoToken = algoAuth.Token
		go lputils.TrackGoroutine(func(goCtx context.Context) {
			c.cfg.Cache().Set("algo_token", algoAuth.Token, time.Hour*1)
		})
	}

	return algoToken, nil
}
