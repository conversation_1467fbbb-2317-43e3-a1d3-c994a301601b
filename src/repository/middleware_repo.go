package repository

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/Lionparcel/go-lptool/v2/lputils"

	"github.com/google/uuid"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/gateway_stt"
	"github.com/Lionparcel/hydra/src/usecase/stt"
)

type DetailTokopediaTrackingRequest struct {
	Awb          string
	ShipmentType string
}

type middlewareClientCtx struct {
	pubsubActivityLogRepo PartnerLogRepository
	sttRepo               SttRepository
	cfg                   *config.Config
	deliveryRepo          DeliveryRepository
	shipmentRepo          ShipmentRepository
	sttPieceRepo          SttPiecesRepository
	sttOptionalRateRepo   SttOptionalRateRepository
}

//go:generate mockery --name=MiddlewareCLient
type MiddlewareCLient interface {
	SubmitDataToMiddleware(ctx context.Context, req interface{}) error
	DetailSttTrackingJourney(ctx context.Context, param model.SttDetailTrackingJourneyParam) (string, error)
	DetailTokopediaTracking(ctx context.Context, params DetailTokopediaTrackingRequest) (*model.GetTrackingTokopediaResponse, error)
}

func NewMiddlewareCLient(pubsubActivityLogRepo PartnerLogRepository, sttRepo SttRepository, cfg *config.Config, deliveryRepo DeliveryRepository, shipmentRepo ShipmentRepository, sttPieceRepo SttPiecesRepository, sttOptionalRateRepo SttOptionalRateRepository) MiddlewareCLient {
	return &middlewareClientCtx{
		pubsubActivityLogRepo: pubsubActivityLogRepo,
		sttRepo:               sttRepo,
		cfg:                   cfg,
		deliveryRepo:          deliveryRepo,
		shipmentRepo:          shipmentRepo,
		sttPieceRepo:          sttPieceRepo,
		sttOptionalRateRepo:   sttOptionalRateRepo,
	}
}

func (c *middlewareClientCtx) SubmitDataToMiddleware(ctx context.Context, req interface{}) error {
	var (
		opName  = "UsecaseMiddleware_repo-SubmitDataToMiddleware"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		e        error
		tempUUID = uuid.New().String()
		payload  string
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": "", "error": e})
		c.pubsubActivityLogRepo.Insert(selfCtx, &model.PartnerLog{
			Action: opName,
			RefID:  tempUUID,
			Request: map[string]interface{}{
				"time_request": time.Now().String(),
				"request":      req,
			},
			Response: map[string]interface{}{
				"error":   e,
				"payload": payload,
			},
		})
	}()

	//add pre action
	reqByte, _ := json.Marshal(req)
	tmpLog := &model.PartnerLog{
		Action:   model.PLMiddlewareRequest,
		RefID:    fmt.Sprintf("%s/webhook/genesis", c.cfg.MiddlewareURL()),
		Request:  string(reqByte),
		Response: "pre-action",
	}
	c.pubsubActivityLogRepo.Insert(ctx, tmpLog)
	switch req.(type) {
	case *stt.EmbedSubmitSttRequestMiddleware:
		convertReq := req.(*stt.EmbedSubmitSttRequestMiddleware)
		convertReq.TypeStruct = "EmbedSubmitSttRequestMiddleware"
		convertReq.SttJourneyType, _ = c.DetailSttTrackingJourney(selfCtx, model.SttDetailTrackingJourneyParam{SttNo: convertReq.SttNo})
		if convertReq.CurrentStatus == "" {
			convertReq.CurrentStatus = convertReq.StatusCode
		}
		if convertReq.StatusCode == "" {
			convertReq.StatusCode = convertReq.CurrentStatus
		}
		tempUUID += "-" + convertReq.SttNo
		convertReq.CheckEmpty()
		requestBody, _ := json.Marshal(convertReq)
		payload = string(requestBody)
		e = c.sendMiddlewareRequest(selfCtx, requestBody, convertReq.SttNo, convertReq)
		return e

	case *gateway_stt.AdjustmentSttPublishMessageRequest:
		convertReq := req.(*gateway_stt.AdjustmentSttPublishMessageRequest)
		convertReq.TypeStruct = "AdjustmentSttPublishMessageRequest"
		convertReq.SttJourneyType, _ = c.DetailSttTrackingJourney(selfCtx, model.SttDetailTrackingJourneyParam{SttNo: convertReq.SttNo})
		convertReq.CurrentStatus = convertReq.Status
		convertReq.StatusCode = convertReq.Status
		tempUUID += "-" + convertReq.SttNo
		requestBody, _ := json.Marshal(convertReq)
		payload = string(requestBody)
		e = c.sendMiddlewareRequest(ctx, requestBody, convertReq.SttNo, convertReq)
		return e

	case *model.UpdateSttStatusWithExtendForMiddleware:
		convertReq := req.(*model.UpdateSttStatusWithExtendForMiddleware)
		convertReq.TypeStruct = "UpdateSttStatusWithExtendForMiddleware"
		convertReq.SttJourneyType, _ = c.DetailSttTrackingJourney(selfCtx, model.SttDetailTrackingJourneyParam{SttNo: convertReq.SttNo})

		// Get delivery attempt count
		attempt, err := c.getDeliveryAttempts(selfCtx, convertReq.SttNo, convertReq.StatusCode)
		if err != nil {
			logger.Ef(`Error getting delivery attempts for STT %s: %v`, convertReq.SttNo, err)
		}
		convertReq.Attempt = attempt

		c.setParamsForMiddleware(ctx, convertReq)

		tempUUID += "-" + convertReq.SttNo
		// convertReq.SetBackwardTime()
		convertReq.CheckEmptyAndValid()
		requestBody, _ := json.Marshal(convertReq)
		payload = string(requestBody)
		e = c.sendMiddlewareRequest(ctx, requestBody, convertReq.SttNo, convertReq)
		return e
	}

	//add action req not mapped
	tmpLog = &model.PartnerLog{
		Action:   model.PLMiddlewareRequest,
		RefID:    "",
		Request:  req,
		Response: "Type not define",
	}
	c.pubsubActivityLogRepo.Insert(selfCtx, tmpLog)

	return shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
		"en": "Type not define",
		"id": "Type tidak terdfinisi",
	})
}

func (c *middlewareClientCtx) getDeliveryAttempts(ctx context.Context, sttNo, statusCode string) (int, error) {
	// Get delivery attempt count
	delAttempt, err := c.deliveryRepo.CountSttDel(ctx, &model.ViewDashboardDeliveryParams{
		SttNo:                 sttNo,
		ExcludeFinishedStatus: []string{model.DELTRF, model.TFDREQ, model.TFDCNC},
	})
	if err != nil {
		logger.Ef(`Error getting del_attempt for STT %s: %v`, sttNo, err)
		return 0, err
	}

	// Get delivery exception attempt count (DEX, CODREJ)
	delExceptionAttempt, err := c.deliveryRepo.CountSTTDelException(ctx, sttNo, []string{model.DEX, model.CODREJ})
	if err != nil {
		logger.Ef(`Error getting del_exception_attempt for STT %s: %v`, sttNo, err)
		return 0, err
	}

	// Return the appropriate attempt count based on status code
	if statusCode == model.DEL {
		return delAttempt, nil
	} else if statusCode == model.DEX || statusCode == model.CODREJ {
		return delExceptionAttempt, nil
	}

	return 0, nil
}

func (c *middlewareClientCtx) sendMiddlewareRequest(ctx context.Context, requestBody []byte, refID string, request interface{}) error {
	headers := map[string]string{
		"Content-Type": "application/json",
	}
	requestURL := fmt.Sprintf("%s/webhook/genesis", c.cfg.MiddlewareURL())
	resp, _, err := shared.GetHTTPRequestJSON(ctx, http.MethodPost, requestURL, bytes.NewBuffer(requestBody), headers)
	if err != nil {
		// Log error action
		tmpLog := &model.PartnerLog{
			Action:   model.PLMiddlewareRequest,
			RefID:    refID,
			Request:  request,
			Response: err.Error(),
		}
		c.pubsubActivityLogRepo.Insert(ctx, tmpLog)
		return err
	}

	// Log success action
	respByte, _ := json.Marshal(resp)
	tmpLog := &model.PartnerLog{
		Action:   model.PLMiddlewareRequest,
		RefID:    refID,
		Request:  request,
		Response: string(respByte),
	}
	c.pubsubActivityLogRepo.Insert(ctx, tmpLog)

	return nil
}

func (c *middlewareClientCtx) GetSttTrackingJourney(ctx context.Context, sttNo string) (model.Stt, error) {
	opName := "middlewareCLientCtx-DetailSttTracking"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var (
		err     error
		sttData model.Stt
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": sttNo, "result": nil, "error": err})
	}()

	data, err := c.sttRepo.Get(selfCtx, &model.SttViewDetailParams{
		Stt: model.Stt{SttNo: sttNo},
	})
	if err != nil {
		return sttData, shared.ERR_UNEXPECTED_DB
	}

	if data != nil {
		sttData = *data
	}

	return sttData, nil
}

func (c *middlewareClientCtx) DetailSttTrackingJourney(ctx context.Context, param model.SttDetailTrackingJourneyParam) (string, error) {
	var (
		opName  = "middlewareCLientCtx-DetailSttTrackingJourney"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		err         error
		statusId    []string
		originalStt model.Stt
		sttMeta     model.DetailSttReverseJourney
		sttNo       = param.SttNo
	)

	if param.SttTrackingMeta != nil {
		sttNo = param.SttNo
		sttMeta = *param.SttTrackingMeta
	} else {
		originalStt, err = c.GetSttTrackingJourney(selfCtx, sttNo)
		if err != nil {
			return "", err
		}

		sttMeta = originalStt.GetSttMetaDetailReverseJourney()
	}

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": sttNo, "result": nil, "error": err})
		c.pubsubActivityLogRepo.Insert(selfCtx, &model.PartnerLog{
			Action: opName,
			RefID:  sttNo,
			Request: map[string]interface{}{
				"time_request": time.Now().String(),
				"parameter":    param,
			},
			Response: map[string]interface{}{
				"status":       statusId,
				"error":        err,
				"original_stt": originalStt,
				"stt_meta":     sttMeta,
			},
		})
	}()

	if sttMeta.RootReverseSttNo != "" {
		sttData, err := c.GetDetailSttTrackingSttDataRelative(selfCtx, sttMeta.RootReverseSttNo)
		if err != nil {
			return "", err
		}

		statusId = c.loopSttMetaJourneyType(sttData, sttNo, 0)
	}

	return strings.Join(statusId, "-"), nil
}

func (c *middlewareClientCtx) loopSttMetaJourneyType(sttData []model.Stt, sttNoOriginal string, idx int, statusId ...string) []string {
	if len(sttData) == idx {
		return statusId
	}

	sttMeta := sttData[idx].GetSttMetaDetailReverseJourney()
	status, found := model.MapSttJourneyType[sttMeta.ReverseJourneyStatusStt]
	if len(sttMeta.ReverseSttNo) > 0 && found {
		statusId = append(statusId, status)
	}

	// cegat pada stt dengan status "DELIVERED" dan "DELIVERED_TO_CUSTOMER"
	if sttData[idx].SttNo == sttNoOriginal {
		return statusId
	}

	return c.loopSttMetaJourneyType(sttData, sttNoOriginal, idx+1, statusId...)
}

func (c *middlewareClientCtx) GetDetailSttTrackingSttDataRelative(ctx context.Context, rootSttNo string) ([]model.Stt, error) {
	var (
		opName  = "middlewareCLientCtx-GetDetailSttTrackingSttDataRelative"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		err     error
		sttData []model.Stt
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": rootSttNo, "result": nil, "error": err})
		c.pubsubActivityLogRepo.Insert(selfCtx, &model.PartnerLog{
			Action: opName,
			RefID:  rootSttNo,
			Request: map[string]interface{}{
				"params": rootSttNo,
			},
			Response: map[string]interface{}{
				"error":    err,
				"stt_data": sttData,
			},
		})
	}()

	sttData, err = c.sttRepo.SelectRelative(selfCtx, &model.SttViewParams{SttNoOrShipmentID: rootSttNo, IsNeedDecrypt: true}, c.cfg.MaxDepthRecursive())
	if err != nil {
		return nil, shared.ERR_UNEXPECTED_DB
	}

	if len(sttData) == 0 {
		var sttDataTokopedia *model.Stt
		switch {
		case model.IsShipmentTokopedia[shared.GetPrefixShipmentID(rootSttNo)]:
			sttDataTokopedia, err = c.getDetailSttTrackingSttDataTokopedia(selfCtx, rootSttNo)
			if err != nil {
				return nil, err
			}
		}

		sttData = append(sttData, *sttDataTokopedia)
	}

	return sttData, err
}

func (c *middlewareClientCtx) getDetailSttTrackingSttDataTokopedia(ctx context.Context, rootSttNo string) (sttData *model.Stt, err error) {
	opName := "middlewareCLientCtx-getDetailSttTrackingSttDataTokopedia"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": rootSttNo, "result": sttData, "error": err})
	}()

	shipmentData := model.Shipment{}
	shipmentID := &shipmentData.ShipmentShipmentID
	paramsGetShipment := &model.ShipmentViewParams{
		ShipmentBookingID: rootSttNo,
	}
	if shared.GetPrefixShipmentID(rootSttNo) == model.T1 {
		shipmentID = &shipmentData.ShipmentBookingID
		paramsGetShipment = &model.ShipmentViewParams{
			ShipmentShipmentID: rootSttNo,
		}
	}

	shipment, err := c.shipmentRepo.GetShipment(selfCtx, paramsGetShipment)
	if err != nil {
		return nil, shared.ERR_UNEXPECTED_DB
	}
	if shipment != nil {
		shipmentData = *shipment
		sttData, err = c.sttRepo.Get(selfCtx, &model.SttViewDetailParams{
			Search:          *shipmentID,
			SearchByPattern: true,
		})
		if err != nil {
			return nil, shared.ERR_UNEXPECTED_DB
		}
	}
	return
}

func (c *middlewareClientCtx) DetailTokopediaTracking(ctx context.Context, params DetailTokopediaTrackingRequest) (*model.GetTrackingTokopediaResponse, error) {
	opName := "middlewareCLientCtx-DetailTokopediaTracking"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	var (
		respTracking *model.GetTrackingTokopediaResponse
		err          error
		requestBody  []byte
		resp         []byte
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": respTracking, "error": err})

		go lputils.TrackGoroutine(func(goCtx context.Context) {
			if err != nil {
				resp = []byte(err.Error())
			}
			tmpLog := &model.PartnerLog{
				Action:   model.PLMiddlewareRequest,
				RefID:    params.Awb,
				Request:  string(requestBody),
				Response: string(resp),
			}
			c.pubsubActivityLogRepo.Insert(ctx, tmpLog)
		})
	}()

	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + base64.StdEncoding.EncodeToString([]byte(c.cfg.TokopediaAuthToken())),
	}
	requestURL := fmt.Sprintf("%s/tokopedia/tracking", c.cfg.MiddlewareURL())
	paramRequest := map[string]interface{}{
		"awb":           params.Awb,
		"shipment_type": params.ShipmentType,
	}
	requestBody, _ = json.Marshal(paramRequest)
	resp, _, err = shared.GetHTTPRequestJSON(ctx, http.MethodPost, requestURL, bytes.NewBuffer(requestBody), headers)
	if err != nil {
		logger.E(err)
		return nil, err
	}
	err = json.Unmarshal(resp, &respTracking)

	return respTracking, nil
}

// setParamsForMiddleware sets the parameters for middleware submission
// It retrieves the STT data, insurance information, and pieces data,
// then populates the main parameters and system status with the retrieved data.
func (c *middlewareClientCtx) setParamsForMiddleware(ctx context.Context, params *model.UpdateSttStatusWithExtendForMiddleware) {
	sttData, err := c.sttRepo.Get(ctx, &model.SttViewDetailParams{
		Stt: model.Stt{SttNo: params.SttNo},
	})
	if err != nil || sttData == nil {
		return
	}

	hasInsurance, insuranceRate := c.getInsuranceData(ctx, sttData)
	piecesData := c.getPiecesData(ctx, sttData)

	c.setMainParam(params, sttData, hasInsurance, insuranceRate, piecesData)

	if len(params.ListSystemStatus) > 0 {
		for i := range params.ListSystemStatus {
			c.setSystemStatus(&params.ListSystemStatus[i], sttData, hasInsurance, insuranceRate, piecesData)
		}
	}
}

// getInsuranceData retrieves insurance information for the STT
func (c *middlewareClientCtx) getInsuranceData(ctx context.Context, sttData *model.Stt) (bool, float64) {
	optionalRates, _ := c.sttOptionalRateRepo.Select(ctx, &model.SttOptionalRate{
		SttOptionalRateSttID:  sttData.SttID,
		SttOptionalRateParams: model.INSURANCE,
	})

	for _, v := range optionalRates {
		if v.SttOptionalRateParams == model.INSURANCE && v.SttOptionalRateName != model.INSURANCEFREENAME {
			return true, v.SttOptionalRateRate
		}
	}

	return false, 0
}

// getPiecesData retrieves pieces information for the STT
func (c *middlewareClientCtx) getPiecesData(ctx context.Context, sttData *model.Stt) []model.SttPieceForClient {
	if sttData != nil {
		pieces, _ := c.sttPieceRepo.Select(ctx, &model.SttPiecesViewParam{
			SttID: int(sttData.SttID),
		})

		if len(pieces) == 0 {
			return nil
		}

		piecesDetails := make([]model.SttPieceForClient, 0, len(pieces))
		for _, piece := range pieces {
			piecesDetails = append(piecesDetails, model.SttPieceForClient{
				PieceID:           piece.SttPieceID,
				PieceLength:       piece.SttPieceLength,
				PieceWidth:        piece.SttPieceWidth,
				PieceHeight:       piece.SttPieceHeight,
				PieceGrossWeight:  piece.SttPieceGrossWeight,
				PieceVolumeWeight: piece.SttPieceVolumeWeight,
			})
		}
		return piecesDetails
	}
	return nil
}

// setMainParam sets the main parameters for the middleware submission
func (c *middlewareClientCtx) setMainParam(param *model.UpdateSttStatusWithExtendForMiddleware, sttData *model.Stt, hasInsurance bool, insuranceRate float64, piecesData []model.SttPieceForClient) {
	// Only set if not already populated
	if param.ShipmentID == "" {
		param.ShipmentID = sttData.SttShipmentID
	}
	if param.TotalTariff == 0 {
		param.TotalTariff = sttData.SttTotalAmount
	}
	if !param.IsInsurance {
		param.IsInsurance = hasInsurance
	}
	if param.InsuranceType == "" {
		param.InsuranceType = sttData.SttInsuranceType
	}
	if param.InsuranceRate == 0 {
		param.InsuranceRate = insuranceRate
	}
	if param.PiecesDetails == nil {
		param.PiecesDetails = piecesData
	}

	param.LastStatusPartnerType = ""
	param.LastStatusPartnerLoc = ""
	param.LastStatusPartnerName = ""
	param.LastStatusPartnerCode = ""
	param.AgentName = ""

	param.HubID = 0
	param.HubName = ""
}

// setSystemStatus sets the system status with the provided data
func (c *middlewareClientCtx) setSystemStatus(param *model.SystemStatus, sttData *model.Stt, hasInsurance bool, insuranceRate float64, piecesData []model.SttPieceForClient) {
	if param.ShipmentID == "" {
		param.ShipmentID = sttData.SttShipmentID
	}
	if param.TotalTariff == 0 {
		param.TotalTariff = sttData.SttTotalAmount
	}
	if !param.IsInsurance {
		param.IsInsurance = hasInsurance
	}
	if param.InsuranceType == "" {
		param.InsuranceType = sttData.SttInsuranceType
	}
	if param.InsuranceRate == 0 {
		param.InsuranceRate = insuranceRate
	}
	if param.PiecesDetails == nil {
		param.PiecesDetails = piecesData
	}

	param.LastStatusPartnerType = ""
	param.LastStatusPartnerLoc = ""
	param.LastStatusPartnerName = ""
	param.LastStatusPartnerCode = ""
	param.AgentName = ""

	param.HubID = 0
	param.HubName = ""
}
