// Code generated by mockery v2.46.0. DO NOT EDIT.

package mocks

import (
	context "context"

	delivery "github.com/Lionparcel/hydra/src/usecase/delivery"
	mock "github.com/stretchr/testify/mock"

	model "github.com/Lionparcel/hydra/src/model"

	time "time"
)

// DeliveryRepository is an autogenerated mock type for the DeliveryRepository type
type DeliveryRepository struct {
	mock.Mock
}

// CountDriverPendingReconcile provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) CountDriverPendingReconcile(ctx context.Context, params *model.CountDriverPendingReconcileParams) (int, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for CountDriverPendingReconcile")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.CountDriverPendingReconcileParams) (int, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.CountDriverPendingReconcileParams) int); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.CountDriverPendingReconcileParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CountSTTDelException provides a mock function with given fields: ctx, sttNo, finishedStatus
func (_m *DeliveryRepository) CountSTTDelException(ctx context.Context, sttNo string, finishedStatus []string) (int, error) {
	ret := _m.Called(ctx, sttNo, finishedStatus)

	if len(ret) == 0 {
		panic("no return value specified for CountSTTDelException")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string, []string) (int, error)); ok {
		return rf(ctx, sttNo, finishedStatus)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string, []string) int); ok {
		r0 = rf(ctx, sttNo, finishedStatus)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, string, []string) error); ok {
		r1 = rf(ctx, sttNo, finishedStatus)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CountSttByStatusBulk provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) CountSttByStatusBulk(ctx context.Context, params *model.CountDeliveryBySttStatusBulkParams) (map[string]int, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for CountSttByStatusBulk")
	}

	var r0 map[string]int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.CountDeliveryBySttStatusBulkParams) (map[string]int, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.CountDeliveryBySttStatusBulkParams) map[string]int); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(map[string]int)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.CountDeliveryBySttStatusBulkParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CountSttDel provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) CountSttDel(ctx context.Context, params *model.ViewDashboardDeliveryParams) (int, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for CountSttDel")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.ViewDashboardDeliveryParams) (int, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.ViewDashboardDeliveryParams) int); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.ViewDashboardDeliveryParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// CountSttDex provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) CountSttDex(ctx context.Context, params *model.ViewDashboardDeliveryParams) (int, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for CountSttDex")
	}

	var r0 int
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.ViewDashboardDeliveryParams) (int, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.ViewDashboardDeliveryParams) int); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Get(0).(int)
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.ViewDashboardDeliveryParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Create provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) Create(ctx context.Context, params *delivery.CreateDeliveryParams) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for Create")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *delivery.CreateDeliveryParams) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// CreateMasterDetail provides a mock function with given fields: ctx, data
func (_m *DeliveryRepository) CreateMasterDetail(ctx context.Context, data *model.DeliveryMasterCreateParams) (*model.DeliveryMasterCreateResponse, error) {
	ret := _m.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for CreateMasterDetail")
	}

	var r0 *model.DeliveryMasterCreateResponse
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryMasterCreateParams) (*model.DeliveryMasterCreateResponse, error)); ok {
		return rf(ctx, data)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryMasterCreateParams) *model.DeliveryMasterCreateResponse); ok {
		r0 = rf(ctx, data)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DeliveryMasterCreateResponse)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryMasterCreateParams) error); ok {
		r1 = rf(ctx, data)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDelivery provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) GetDelivery(ctx context.Context, params *model.DeliveryViewParam) (*model.Delivery, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetDelivery")
	}

	var r0 *model.Delivery
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) (*model.Delivery, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) *model.Delivery); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.Delivery)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryViewParam) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDeliveryCod provides a mock function with given fields: ctx, driverPhoneNumber
func (_m *DeliveryRepository) GetDeliveryCod(ctx context.Context, driverPhoneNumber string) ([]model.Delivery, error) {
	ret := _m.Called(ctx, driverPhoneNumber)

	if len(ret) == 0 {
		panic("no return value specified for GetDeliveryCod")
	}

	var r0 []model.Delivery
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, string) ([]model.Delivery, error)); ok {
		return rf(ctx, driverPhoneNumber)
	}
	if rf, ok := ret.Get(0).(func(context.Context, string) []model.Delivery); ok {
		r0 = rf(ctx, driverPhoneNumber)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.Delivery)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, string) error); ok {
		r1 = rf(ctx, driverPhoneNumber)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetDetail provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) GetDetail(ctx context.Context, params *model.DeliveryViewParam) (*model.DeliveryDetailResult, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for GetDetail")
	}

	var r0 *model.DeliveryDetailResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) (*model.DeliveryDetailResult, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) *model.DeliveryDetailResult); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.DeliveryDetailResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryViewParam) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// ListDriverPendingReconcile provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) ListDriverPendingReconcile(ctx context.Context, params *model.CountDriverPendingReconcileParams) ([]model.DeliveryDetailResult, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for ListDriverPendingReconcile")
	}

	var r0 []model.DeliveryDetailResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.CountDriverPendingReconcileParams) ([]model.DeliveryDetailResult, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.CountDriverPendingReconcileParams) []model.DeliveryDetailResult); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryDetailResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.CountDriverPendingReconcileParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Select provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) Select(ctx context.Context, params *model.DeliveryViewParam) ([]model.Delivery, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for Select")
	}

	var r0 []model.Delivery
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) ([]model.Delivery, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) []model.Delivery); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.Delivery)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryViewParam) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectDeliveryCodDashboard provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectDeliveryCodDashboard(ctx context.Context, params *model.DeliveryCodDashboardParams) ([]model.DeliveryWithCodAmount, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectDeliveryCodDashboard")
	}

	var r0 []model.DeliveryWithCodAmount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardParams) ([]model.DeliveryWithCodAmount, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardParams) []model.DeliveryWithCodAmount); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryWithCodAmount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryCodDashboardParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectDeliveryCodDashboardGrouping provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectDeliveryCodDashboardGrouping(ctx context.Context, params *model.DeliveryCodDashboardGroupingParams) ([]model.DeliveryWithCodAmount, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectDeliveryCodDashboardGrouping")
	}

	var r0 []model.DeliveryWithCodAmount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardGroupingParams) ([]model.DeliveryWithCodAmount, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardGroupingParams) []model.DeliveryWithCodAmount); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryWithCodAmount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryCodDashboardGroupingParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectDeliveryCodDashboardGroupingWithoutStt provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectDeliveryCodDashboardGroupingWithoutStt(ctx context.Context, params *model.DeliveryCodDashboardGroupingParams) ([]model.DeliveryWithCodAmount, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectDeliveryCodDashboardGroupingWithoutStt")
	}

	var r0 []model.DeliveryWithCodAmount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardGroupingParams) ([]model.DeliveryWithCodAmount, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardGroupingParams) []model.DeliveryWithCodAmount); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryWithCodAmount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryCodDashboardGroupingParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectDeliveryCodDashboardV2 provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectDeliveryCodDashboardV2(ctx context.Context, params *model.DeliveryCodDashboardParams) ([]model.DeliveryWithCodAmount, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectDeliveryCodDashboardV2")
	}

	var r0 []model.DeliveryWithCodAmount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardParams) ([]model.DeliveryWithCodAmount, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardParams) []model.DeliveryWithCodAmount); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryWithCodAmount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryCodDashboardParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectDeliveryCodDashboardWithoutStt provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectDeliveryCodDashboardWithoutStt(ctx context.Context, params *model.DeliveryCodDashboardParams) ([]model.DeliveryWithCodAmount, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectDeliveryCodDashboardWithoutStt")
	}

	var r0 []model.DeliveryWithCodAmount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardParams) ([]model.DeliveryWithCodAmount, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardParams) []model.DeliveryWithCodAmount); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryWithCodAmount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryCodDashboardParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectDeliveryDashboardGroupingWithoutStt provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectDeliveryDashboardGroupingWithoutStt(ctx context.Context, params *model.DeliveryCodDashboardGroupingParams) ([]model.DeliveryWithCodAmount, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectDeliveryDashboardGroupingWithoutStt")
	}

	var r0 []model.DeliveryWithCodAmount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardGroupingParams) ([]model.DeliveryWithCodAmount, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardGroupingParams) []model.DeliveryWithCodAmount); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryWithCodAmount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryCodDashboardGroupingParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectDeliveryDashboardWithSttPiece provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectDeliveryDashboardWithSttPiece(ctx context.Context, params *model.DeliveryCodDashboardParams) ([]model.DeliveryWithCodAmount, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectDeliveryDashboardWithSttPiece")
	}

	var r0 []model.DeliveryWithCodAmount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardParams) ([]model.DeliveryWithCodAmount, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryCodDashboardParams) []model.DeliveryWithCodAmount); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryWithCodAmount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryCodDashboardParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectDeliveryPicCodDashboardGroupingWithoutStt provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectDeliveryPicCodDashboardGroupingWithoutStt(ctx context.Context, params *model.DeliveryPicCodDashboardGroupingParams) ([]model.DeliveryPicWithCodAmount, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectDeliveryPicCodDashboardGroupingWithoutStt")
	}

	var r0 []model.DeliveryPicWithCodAmount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryPicCodDashboardGroupingParams) ([]model.DeliveryPicWithCodAmount, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryPicCodDashboardGroupingParams) []model.DeliveryPicWithCodAmount); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryPicWithCodAmount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryPicCodDashboardGroupingParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectDeliveryPicCodDashboardWithoutStt provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectDeliveryPicCodDashboardWithoutStt(ctx context.Context, params *model.DeliveryPicCodDashboardParams) ([]model.DeliveryPicWithCodAmount, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectDeliveryPicCodDashboardWithoutStt")
	}

	var r0 []model.DeliveryPicWithCodAmount
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryPicCodDashboardParams) ([]model.DeliveryPicWithCodAmount, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryPicCodDashboardParams) []model.DeliveryPicWithCodAmount); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryPicWithCodAmount)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryPicCodDashboardParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectDetail provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectDetail(ctx context.Context, params *model.DeliveryViewParam) ([]model.DeliveryDetailResult, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectDetail")
	}

	var r0 []model.DeliveryDetailResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) ([]model.DeliveryDetailResult, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) []model.DeliveryDetailResult); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryDetailResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryViewParam) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectDetailMaster provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectDetailMaster(ctx context.Context, params *model.DeliveryViewParam) ([]model.DeliveryDetailResult, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectDetailMaster")
	}

	var r0 []model.DeliveryDetailResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) ([]model.DeliveryDetailResult, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) []model.DeliveryDetailResult); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryDetailResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryViewParam) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectMasterDetail provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectMasterDetail(ctx context.Context, params *model.DeliveryViewParam) ([]model.DeliveryMasterDetailResult, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectMasterDetail")
	}

	var r0 []model.DeliveryMasterDetailResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) ([]model.DeliveryMasterDetailResult, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) []model.DeliveryMasterDetailResult); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryMasterDetailResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryViewParam) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectSlaveReportDetail provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectSlaveReportDetail(ctx context.Context, params *model.DeliveryViewParam) ([]model.DeliveryDetailResult, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectSlaveReportDetail")
	}

	var r0 []model.DeliveryDetailResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) ([]model.DeliveryDetailResult, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) []model.DeliveryDetailResult); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.DeliveryDetailResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryViewParam) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectV2 provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) SelectV2(ctx context.Context, params *model.DeliveryViewParam) ([]model.Delivery, error) {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for SelectV2")
	}

	var r0 []model.Delivery
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) ([]model.Delivery, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.DeliveryViewParam) []model.Delivery); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.Delivery)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.DeliveryViewParam) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// Update provides a mock function with given fields: ctx, params, history
func (_m *DeliveryRepository) Update(ctx context.Context, params *model.Delivery, history []model.SttPieceHistory) error {
	ret := _m.Called(ctx, params, history)

	if len(ret) == 0 {
		panic("no return value specified for Update")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Delivery, []model.SttPieceHistory) error); ok {
		r0 = rf(ctx, params, history)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateDeltrf provides a mock function with given fields: ctx, dateTimeNow, deliveryIdCurrent, data, dataStt
func (_m *DeliveryRepository) UpdateDeltrf(ctx context.Context, dateTimeNow time.Time, deliveryIdCurrent int, data *delivery.CreateDeliveryParams, dataStt *model.Stt) error {
	ret := _m.Called(ctx, dateTimeNow, deliveryIdCurrent, data, dataStt)

	if len(ret) == 0 {
		panic("no return value specified for UpdateDeltrf")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, int, *delivery.CreateDeliveryParams, *model.Stt) error); ok {
		r0 = rf(ctx, dateTimeNow, deliveryIdCurrent, data, dataStt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateHangingDels provides a mock function with given fields: ctx, params
func (_m *DeliveryRepository) UpdateHangingDels(ctx context.Context, params []model.DeliveryDetailResult) error {
	ret := _m.Called(ctx, params)

	if len(ret) == 0 {
		panic("no return value specified for UpdateHangingDels")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []model.DeliveryDetailResult) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdatePaymentMethod provides a mock function with given fields: ctx, data
func (_m *DeliveryRepository) UpdatePaymentMethod(ctx context.Context, data *model.Delivery) error {
	ret := _m.Called(ctx, data)

	if len(ret) == 0 {
		panic("no return value specified for UpdatePaymentMethod")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.Delivery) error); ok {
		r0 = rf(ctx, data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateTfdcnc provides a mock function with given fields: ctx, dateTimeNow, deliveryIdCurrent, data, dataStt
func (_m *DeliveryRepository) UpdateTfdcnc(ctx context.Context, dateTimeNow time.Time, deliveryIdCurrent int, data *delivery.CreateDeliveryParams, dataStt *model.Stt) error {
	ret := _m.Called(ctx, dateTimeNow, deliveryIdCurrent, data, dataStt)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTfdcnc")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, int, *delivery.CreateDeliveryParams, *model.Stt) error); ok {
		r0 = rf(ctx, dateTimeNow, deliveryIdCurrent, data, dataStt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateTfdreq provides a mock function with given fields: ctx, dateTimeNow, data, dataStt
func (_m *DeliveryRepository) UpdateTfdreq(ctx context.Context, dateTimeNow time.Time, data *delivery.CreateDeliveryParams, dataStt *model.Stt) error {
	ret := _m.Called(ctx, dateTimeNow, data, dataStt)

	if len(ret) == 0 {
		panic("no return value specified for UpdateTfdreq")
	}

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, time.Time, *delivery.CreateDeliveryParams, *model.Stt) error); ok {
		r0 = rf(ctx, dateTimeNow, data, dataStt)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// NewDeliveryRepository creates a new instance of DeliveryRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewDeliveryRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *DeliveryRepository {
	mock := &DeliveryRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
