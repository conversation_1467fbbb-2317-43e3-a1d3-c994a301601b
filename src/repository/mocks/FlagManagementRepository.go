// Code generated by mockery v2.46.3. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/Lionparcel/hydra/src/model"
	mock "github.com/stretchr/testify/mock"

	time "time"
)

// FlagManagementRepository is an autogenerated mock type for the FlagManagementRepository type
type FlagManagementRepository struct {
	mock.Mock
}

// CloudBeesCodDfodDayThreshold provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesCodDfodDayThreshold(ctx context.Context) time.Duration {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesCodDfodDayThreshold")
	}

	var r0 time.Duration
	if rf, ok := ret.Get(0).(func(context.Context) time.Duration); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(time.Duration)
	}

	return r0
}

// CloudBeesDEXAssessmentDashboardCache provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesDEXAssessmentDashboardCache(ctx context.Context) time.Duration {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesDEXAssessmentDashboardCache")
	}

	var r0 time.Duration
	if rf, ok := ret.Get(0).(func(context.Context) time.Duration); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(time.Duration)
	}

	return r0
}

// CloudBeesDFODPastiProgram provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesDFODPastiProgram(ctx context.Context) bool {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesDFODPastiProgram")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(context.Context) bool); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// CloudBeesDisableCodDfodForLimitedNumber provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesDisableCodDfodForLimitedNumber(ctx context.Context) bool {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesDisableCodDfodForLimitedNumber")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(context.Context) bool); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// CloudBeesEnableBookingInterpack provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesEnableBookingInterpack(ctx context.Context) bool {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesEnableBookingInterpack")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(context.Context) bool); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// CloudBeesEnableMappingS3Link provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesEnableMappingS3Link(ctx context.Context) bool {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesEnableMappingS3Link")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(context.Context) bool); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// CloudBeesHoldSttPenalty provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesHoldSttPenalty(ctx context.Context) *model.HoldSttPenalty {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesHoldSttPenalty")
	}

	var r0 *model.HoldSttPenalty
	if rf, ok := ret.Get(0).(func(context.Context) *model.HoldSttPenalty); ok {
		r0 = rf(ctx)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.HoldSttPenalty)
		}
	}

	return r0
}

// CloudBeesIsSendMessageDELAfterTransferDelivery provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesIsSendMessageDELAfterTransferDelivery(ctx context.Context) bool {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesIsSendMessageDELAfterTransferDelivery")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(context.Context) bool); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// CloudBeesRTSHQDefaultAddress provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesRTSHQDefaultAddress(ctx context.Context) string {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesRTSHQDefaultAddress")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(context.Context) string); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// CloudBeesRTSHQDefaultAddressType provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesRTSHQDefaultAddressType(ctx context.Context) string {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesRTSHQDefaultAddressType")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(context.Context) string); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// CloudBeesRTSHQDefaultDistrictCode provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesRTSHQDefaultDistrictCode(ctx context.Context) string {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesRTSHQDefaultDistrictCode")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(context.Context) string); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// CloudBeesRTSHQDefaultName provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesRTSHQDefaultName(ctx context.Context) string {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesRTSHQDefaultName")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(context.Context) string); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// CloudBeesRTSHQDefaultPhoneNumber provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesRTSHQDefaultPhoneNumber(ctx context.Context) string {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesRTSHQDefaultPhoneNumber")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(context.Context) string); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// CloudBeesSamedayCutOffTime provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesSamedayCutOffTime(ctx context.Context) string {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesSamedayCutOffTime")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(context.Context) string); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// CloudBeesSttHandleMissingStatusDate provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesSttHandleMissingStatusDate(ctx context.Context) string {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesSttHandleMissingStatusDate")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(context.Context) string); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// CloudBeesWebURL provides a mock function with given fields: ctx
func (_m *FlagManagementRepository) CloudBeesWebURL(ctx context.Context) string {
	ret := _m.Called(ctx)

	if len(ret) == 0 {
		panic("no return value specified for CloudBeesWebURL")
	}

	var r0 string
	if rf, ok := ret.Get(0).(func(context.Context) string); ok {
		r0 = rf(ctx)
	} else {
		r0 = ret.Get(0).(string)
	}

	return r0
}

// ShipmentCAGrossWeight provides a mock function with given fields: ctx, shipmentAccessWeigth
func (_m *FlagManagementRepository) ShipmentCAGrossWeight(ctx context.Context, shipmentAccessWeigth model.ShipmentAccessWeigth) bool {
	ret := _m.Called(ctx, shipmentAccessWeigth)

	if len(ret) == 0 {
		panic("no return value specified for ShipmentCAGrossWeight")
	}

	var r0 bool
	if rf, ok := ret.Get(0).(func(context.Context, model.ShipmentAccessWeigth) bool); ok {
		r0 = rf(ctx, shipmentAccessWeigth)
	} else {
		r0 = ret.Get(0).(bool)
	}

	return r0
}

// NewFlagManagementRepository creates a new instance of FlagManagementRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
// The first argument is typically a *testing.T value.
func NewFlagManagementRepository(t interface {
	mock.TestingT
	Cleanup(func())
}) *FlagManagementRepository {
	mock := &FlagManagementRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
