// Code generated by mockery v2.20.2. DO NOT EDIT.

package mocks

import (
	context "context"

	model "github.com/Lionparcel/hydra/src/model"
	mock "github.com/stretchr/testify/mock"
)

// SttDueRepository is an autogenerated mock type for the SttDueRepository type
type SttDueRepository struct {
	mock.Mock
}

// CountSttDue provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) CountSttDue(ctx context.Context, params model.CountSttDueParam) (int64, error) {
	ret := _m.Called(ctx, params)

	var r0 int64
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, model.CountSttDueParam) (int64, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, model.CountSttDueParam) int64); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Get(0).(int64)
	}

	if rf, ok := ret.Get(1).(func(context.Context, model.CountSttDueParam) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// FindAllAndPaginate provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) FindAllAndPaginate(ctx context.Context, params model.STTDueFindAllAndPaginateParameters) ([]model.STTDueWithSTTModel, int64, error) {
	ret := _m.Called(ctx, params)

	var r0 []model.STTDueWithSTTModel
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, model.STTDueFindAllAndPaginateParameters) ([]model.STTDueWithSTTModel, int64, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, model.STTDueFindAllAndPaginateParameters) []model.STTDueWithSTTModel); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.STTDueWithSTTModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, model.STTDueFindAllAndPaginateParameters) int64); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, model.STTDueFindAllAndPaginateParameters) error); ok {
		r2 = rf(ctx, params)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// FindAllBookedAndPaginate provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) FindAllBookedAndPaginate(ctx context.Context, params model.STTDueFindAllBookedAndPaginateParameters) ([]model.STTDueModel, int64, error) {
	ret := _m.Called(ctx, params)

	var r0 []model.STTDueModel
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, model.STTDueFindAllBookedAndPaginateParameters) ([]model.STTDueModel, int64, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, model.STTDueFindAllBookedAndPaginateParameters) []model.STTDueModel); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.STTDueModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, model.STTDueFindAllBookedAndPaginateParameters) int64); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, model.STTDueFindAllBookedAndPaginateParameters) error); ok {
		r2 = rf(ctx, params)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// Get provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) Get(ctx context.Context, params *model.STTDueModel) (*model.STTDueModel, error) {
	ret := _m.Called(ctx, params)

	var r0 *model.STTDueModel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.STTDueModel) (*model.STTDueModel, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.STTDueModel) *model.STTDueModel); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.STTDueModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.STTDueModel) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetBySttNo provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) GetBySttNo(ctx context.Context, params *model.GetBySttNoParams) (*model.STTDueModel, error) {
	ret := _m.Called(ctx, params)

	var r0 *model.STTDueModel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.GetBySttNoParams) (*model.STTDueModel, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.GetBySttNoParams) *model.STTDueModel); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.STTDueModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.GetBySttNoParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// GetSttNeedToSti provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) GetSttNeedToSti(ctx context.Context, params *model.GetSttNeedToStiParams) ([]model.SttNeedToSti, error) {
	ret := _m.Called(ctx, params)

	var r0 []model.SttNeedToSti
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.GetSttNeedToStiParams) ([]model.SttNeedToSti, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.GetSttNeedToStiParams) []model.SttNeedToSti); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.SttNeedToSti)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.GetSttNeedToStiParams) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// InsertBulk provides a mock function with given fields: ctx, data
func (_m *SttDueRepository) InsertBulk(ctx context.Context, data []*model.STTDueModel) error {
	ret := _m.Called(ctx, data)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, []*model.STTDueModel) error); ok {
		r0 = rf(ctx, data)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// SelectFilterSttDue provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) SelectFilterSttDue(ctx context.Context, params *model.STTDueFilterParam) ([]model.STTDueWithSTTModel, error) {
	ret := _m.Called(ctx, params)

	var r0 []model.STTDueWithSTTModel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.STTDueFilterParam) ([]model.STTDueWithSTTModel, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, *model.STTDueFilterParam) []model.STTDueWithSTTModel); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.STTDueWithSTTModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, *model.STTDueFilterParam) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectSttDue provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) SelectSttDue(ctx context.Context, params model.STTDuePartnerParam) ([]model.STTDueModel, error) {
	ret := _m.Called(ctx, params)

	var r0 []model.STTDueModel
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, model.STTDuePartnerParam) ([]model.STTDueModel, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, model.STTDuePartnerParam) []model.STTDueModel); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.STTDueModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, model.STTDuePartnerParam) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SelectSttDueNeedToStiscCount provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) SelectSttDueNeedToStiscCount(ctx context.Context, params model.CountSttDueSTISCParam) (*model.CountSttDueSTISCResult, error) {
	ret := _m.Called(ctx, params)

	var r0 *model.CountSttDueSTISCResult
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, model.CountSttDueSTISCParam) (*model.CountSttDueSTISCResult, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, model.CountSttDueSTISCParam) *model.CountSttDueSTISCResult); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.CountSttDueSTISCResult)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, model.CountSttDueSTISCParam) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// StiDestDelFindAllPaginate provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) StiDestDelFindAllPaginate(ctx context.Context, params model.SttDueStiDestDelFindAllAndPaginateParameter) ([]model.STTDueWithSTTAndPriorityDeliveryModel, int64, error) {
	ret := _m.Called(ctx, params)

	var r0 []model.STTDueWithSTTAndPriorityDeliveryModel
	var r1 int64
	var r2 error
	if rf, ok := ret.Get(0).(func(context.Context, model.SttDueStiDestDelFindAllAndPaginateParameter) ([]model.STTDueWithSTTAndPriorityDeliveryModel, int64, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, model.SttDueStiDestDelFindAllAndPaginateParameter) []model.STTDueWithSTTAndPriorityDeliveryModel); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).([]model.STTDueWithSTTAndPriorityDeliveryModel)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, model.SttDueStiDestDelFindAllAndPaginateParameter) int64); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Get(1).(int64)
	}

	if rf, ok := ret.Get(2).(func(context.Context, model.SttDueStiDestDelFindAllAndPaginateParameter) error); ok {
		r2 = rf(ctx, params)
	} else {
		r2 = ret.Error(2)
	}

	return r0, r1, r2
}

// SummaryAnalytic provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) SummaryAnalytic(ctx context.Context, params model.STTDueSummaryAnalyticParameters) (*model.STTDueSummaryAnalyticData, error) {
	ret := _m.Called(ctx, params)

	var r0 *model.STTDueSummaryAnalyticData
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, model.STTDueSummaryAnalyticParameters) (*model.STTDueSummaryAnalyticData, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, model.STTDueSummaryAnalyticParameters) *model.STTDueSummaryAnalyticData); ok {
		r0 = rf(ctx, params)
	} else {
		if ret.Get(0) != nil {
			r0 = ret.Get(0).(*model.STTDueSummaryAnalyticData)
		}
	}

	if rf, ok := ret.Get(1).(func(context.Context, model.STTDueSummaryAnalyticParameters) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// SummaryData provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) SummaryData(ctx context.Context, params model.STTDueFindAllAndPaginateParameters) (model.STTDueSummaryData, error) {
	ret := _m.Called(ctx, params)

	var r0 model.STTDueSummaryData
	var r1 error
	if rf, ok := ret.Get(0).(func(context.Context, model.STTDueFindAllAndPaginateParameters) (model.STTDueSummaryData, error)); ok {
		return rf(ctx, params)
	}
	if rf, ok := ret.Get(0).(func(context.Context, model.STTDueFindAllAndPaginateParameters) model.STTDueSummaryData); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Get(0).(model.STTDueSummaryData)
	}

	if rf, ok := ret.Get(1).(func(context.Context, model.STTDueFindAllAndPaginateParameters) error); ok {
		r1 = rf(ctx, params)
	} else {
		r1 = ret.Error(1)
	}

	return r0, r1
}

// UpdateBulk provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) UpdateBulk(ctx context.Context, params *model.STTDueUpdateBulkParams) error {
	ret := _m.Called(ctx, params)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.STTDueUpdateBulkParams) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateIsShowBulk provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) UpdateIsShowBulk(ctx context.Context, params *model.STTDueUpdateIsShow) error {
	ret := _m.Called(ctx, params)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.STTDueUpdateIsShow) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

// UpdateSttDueBagNo provides a mock function with given fields: ctx, params
func (_m *SttDueRepository) UpdateSttDueBagNo(ctx context.Context, params *model.UpdateSttDueBagNoParams) error {
	ret := _m.Called(ctx, params)

	var r0 error
	if rf, ok := ret.Get(0).(func(context.Context, *model.UpdateSttDueBagNoParams) error); ok {
		r0 = rf(ctx, params)
	} else {
		r0 = ret.Error(0)
	}

	return r0
}

type mockConstructorTestingTNewSttDueRepository interface {
	mock.TestingT
	Cleanup(func())
}

// NewSttDueRepository creates a new instance of SttDueRepository. It also registers a testing interface on the mock and a cleanup function to assert the mocks expectations.
func NewSttDueRepository(t mockConstructorTestingTNewSttDueRepository) *SttDueRepository {
	mock := &SttDueRepository{}
	mock.Mock.Test(t)

	t.Cleanup(func() { mock.AssertExpectations(t) })

	return mock
}
