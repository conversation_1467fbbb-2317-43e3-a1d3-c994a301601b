package repository

import (
	"context"

	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
)

func (c *sttDueRepository) SelectSttDue(ctx context.Context, params model.STTDuePartnerParam) ([]model.STTDueModel, error) {
	var (
		opName  = `sttDueRepository-SelectSttDue`
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		res     []model.STTDueModel
		err     error
	)
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": err})
	}()
	sttDue := c.DB.Slave().SelectRaw(`SELECT * FROM stt_due`)
	sttDue.Where(`eligible_archived_type = ?`, 0)
	sttDue.Where(`sd_ref_type = ?`, params.PartnerType)
	sttDue.Where(`sd_ref_id = ?`, params.PartnerID)
	sttDue.Where(`sd_target_status = ?`, "STI")
	sttDue.Where(`sd_is_show = ?`, 1)
	sttDue.Where(`sd_stt_booked_at >= date_sub(NOW(), INTERVAL 7 DAY)`)

	if params.BookedType != "" {
		sttDue.Where("sd_booked_type = ?", params.BookedType)
	}

	if len(params.BookedID) > 0 {
		sttDue.Where("sd_booked_id in ?", params.BookedID)
	}

	if params.WithoutSubconsole {
		sttDue.Where("sd_booked_type != ?", model.SUBCONSOLE)
	}

	if _, err = sttDue.LoadContext(selfCtx, &res); err != nil {
		return nil, err
	}

	return res, nil
}
