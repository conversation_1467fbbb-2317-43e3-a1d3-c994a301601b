package repository

import (
	"context"
	"time"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/config/cache"
	"github.com/Lionparcel/hydra/config/database"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/delivery"
)

// deliveryRepositoryCtx ...
type deliveryRepositoryCtx struct {
	pubsubActivityLogRepo PartnerLogRepository
	cfg                   *config.Config
	DB                    database.DbrDatabase
	NoSQLDB               database.MongoDB
	Cache                 cache.Client
	sttPieceHistory       SttPieceHistoryRepository
}

//go:generate mockery --name=DeliveryRepository
type DeliveryRepository interface {
	SelectDetail(ctx context.Context, params *model.DeliveryViewParam) ([]model.DeliveryDetailResult, error)
	SelectDetailMaster(ctx context.Context, params *model.DeliveryViewParam) ([]model.DeliveryDetailResult, error)
	GetDetail(ctx context.Context, params *model.DeliveryViewParam) (*model.DeliveryDetailResult, error)
	Create(ctx context.Context, params *delivery.CreateDeliveryParams) error
	CountSttDex(ctx context.Context, params *model.ViewDashboardDeliveryParams) (int, error)
	CountSttDel(ctx context.Context, params *model.ViewDashboardDeliveryParams) (int, error)
	CountSTTDelException(ctx context.Context, sttNo string, finishedStatus []string) (int, error)
	GetDelivery(ctx context.Context, params *model.DeliveryViewParam) (*model.Delivery, error)
	GetDeliveryCod(ctx context.Context, driverPhoneNumber string) ([]model.Delivery, error)
	SelectMasterDetail(ctx context.Context, params *model.DeliveryViewParam) ([]model.DeliveryMasterDetailResult, error)
	CreateMasterDetail(ctx context.Context, data *model.DeliveryMasterCreateParams) (*model.DeliveryMasterCreateResponse, error)
	Update(ctx context.Context, params *model.Delivery, history []model.SttPieceHistory) error
	UpdateHangingDels(ctx context.Context, params []model.DeliveryDetailResult) error
	Select(ctx context.Context, params *model.DeliveryViewParam) ([]model.Delivery, error)
	CountSttByStatusBulk(ctx context.Context, params *model.CountDeliveryBySttStatusBulkParams) (map[string]int, error)

	// Slave Report
	SelectSlaveReportDetail(ctx context.Context, params *model.DeliveryViewParam) ([]model.DeliveryDetailResult, error)

	// NoSQL
	SelectV2(ctx context.Context, params *model.DeliveryViewParam) ([]model.Delivery, error)

	// Delivery COD dashboard
	SelectDeliveryCodDashboardGrouping(ctx context.Context, params *model.DeliveryCodDashboardGroupingParams) ([]model.DeliveryWithCodAmount, error)
	// Delivery COD dashboard
	SelectDeliveryPicCodDashboardGroupingWithoutStt(ctx context.Context, params *model.DeliveryPicCodDashboardGroupingParams) ([]model.DeliveryPicWithCodAmount, error)
	SelectDeliveryCodDashboardGroupingWithoutStt(ctx context.Context, params *model.DeliveryCodDashboardGroupingParams) ([]model.DeliveryWithCodAmount, error)
	SelectDeliveryCodDashboard(ctx context.Context, params *model.DeliveryCodDashboardParams) ([]model.DeliveryWithCodAmount, error)
	SelectDeliveryCodDashboardV2(ctx context.Context, params *model.DeliveryCodDashboardParams) ([]model.DeliveryWithCodAmount, error)
	SelectDeliveryCodDashboardWithoutStt(ctx context.Context, params *model.DeliveryCodDashboardParams) ([]model.DeliveryWithCodAmount, error)
	SelectDeliveryPicCodDashboardWithoutStt(ctx context.Context, params *model.DeliveryPicCodDashboardParams) ([]model.DeliveryPicWithCodAmount, error)
	CountDriverPendingReconcile(ctx context.Context, params *model.CountDriverPendingReconcileParams) (int, error)
	SelectDeliveryDashboardWithSttPiece(ctx context.Context, params *model.DeliveryCodDashboardParams) ([]model.DeliveryWithCodAmount, error)
	ListDriverPendingReconcile(ctx context.Context, params *model.CountDriverPendingReconcileParams) ([]model.DeliveryDetailResult, error)
	SelectDeliveryDashboardGroupingWithoutStt(ctx context.Context, params *model.DeliveryCodDashboardGroupingParams) ([]model.DeliveryWithCodAmount, error)

	// Delivery Transfer Task
	UpdateDeltrf(ctx context.Context, dateTimeNow time.Time, deliveryIdCurrent int, data *delivery.CreateDeliveryParams, dataStt *model.Stt) error
	UpdateTfdcnc(ctx context.Context, dateTimeNow time.Time, deliveryIdCurrent int, data *delivery.CreateDeliveryParams, dataStt *model.Stt) error
	UpdateTfdreq(ctx context.Context, dateTimeNow time.Time, data *delivery.CreateDeliveryParams, dataStt *model.Stt) error
	UpdatePaymentMethod(ctx context.Context, data *model.Delivery) error
}

func NewDeliveryRepository(
	config *config.Config,
	sttPieceHistory SttPieceHistoryRepository,
	partnerLog PartnerLogRepository,
) DeliveryRepository {
	return &deliveryRepositoryCtx{
		cfg:                   config,
		Cache:                 config.Cache(),
		DB:                    config.DB(),
		NoSQLDB:               config.NoSqlDB(),
		sttPieceHistory:       sttPieceHistory,
		pubsubActivityLogRepo: partnerLog,
	}
}
