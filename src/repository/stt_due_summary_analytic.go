package repository

import (
	"context"
	"sync"

	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/abiewardani/dbr/v2"
)

func (c *sttDueRepository) SummaryAnalytic(ctx context.Context, params model.STTDueSummaryAnalyticParameters) (*model.STTDueSummaryAnalyticData, error) {
	var (
		opName  = "sttDueRepo.SummaryAnalytic"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		wg sync.WaitGroup
		mx sync.Mutex

		sttDueSummaryData = new(model.STTDueSummaryAnalyticData)
		err               error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": sttDueSummaryData, "error": err})
	}()

	wg.Add(1)
	go func(p model.STTDueSummaryAnalyticParameters) {
		defer mx.Unlock()
		defer wg.Done()

		var (
			er     error
			sumDel *model.STTDueSummaryAnalyticData
		)

		p.TargetStatus = model.DEL
		p.IsNeedInterval = true

		sumDel, er = c.SummaryAnalyticDel(selfCtx, p)
		if er != nil {
			mx.Lock()
			err = er
			return
		}

		mx.Lock()
		sttDueSummaryData.STTDueSummaryAnalyticDataDel = sumDel.STTDueSummaryAnalyticDataDel

		return
	}(params)

	wg.Add(1)
	go func(p model.STTDueSummaryAnalyticParameters) {
		defer mx.Unlock()
		defer wg.Done()

		p.TargetStatus = model.STIDEST
		var (
			er       error
			sumCargo *model.STTDueSummaryAnalyticData
		)
		sumCargo, er = c.SummaryAnalyticCargo(selfCtx, p)
		if er != nil {
			mx.Lock()
			err = er
			return
		}

		mx.Lock()
		sttDueSummaryData.STTDueSummaryAnalyticDataCargo = sumCargo.STTDueSummaryAnalyticDataCargo

		return
	}(params)

	wg.Wait()
	return sttDueSummaryData, err
}

func (c *sttDueRepository) SummaryAnalyticCargo(ctx context.Context, params model.STTDueSummaryAnalyticParameters) (*model.STTDueSummaryAnalyticData, error) {
	var (
		opName  = "sttDueRepo.SummaryAnalyticCargo"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		sttDueSummaryData = new(model.STTDueSummaryAnalyticData)
		err               error

		table = c.DB.SlaveAnalytics()

		sttDue = table.Select(
			`COUNT(DISTINCT CASE WHEN sd_cargo_type = 'plane' THEN sd_cargo_no END) AS total_cargo_plane`,
			`COUNT(DISTINCT CASE WHEN sd_cargo_type = 'truck' THEN sd_cargo_no END) AS total_cargo_truck`,
			`COUNT(DISTINCT CASE WHEN sd_cargo_type = 'ship' THEN sd_cargo_no END) AS total_cargo_ship`,
			`COUNT(sd_id) AS total_stt_need_stidest`,
		)
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": sttDueSummaryData, "error": err})
	}()

	sttDue.From(`stt_due`)
	c.filterSummaryAnalytics(sttDue, params)
	if _, err = sttDue.LoadContext(selfCtx, sttDueSummaryData); err != nil {
		return nil, err
	}

	return sttDueSummaryData, err
}

func (c *sttDueRepository) SummaryAnalyticDel(ctx context.Context, params model.STTDueSummaryAnalyticParameters) (*model.STTDueSummaryAnalyticData, error) {
	var (
		opName  = "sttDueRepo.SummaryAnalyticDel"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		sttDueSummaryData = new(model.STTDueSummaryAnalyticData)
		err               error

		table = c.DB.SlaveAnalytics()

		sttDue = table.Select(
			`COUNT(CASE WHEN sd_target_due_date >= NOW() THEN sd_id END) AS total_del_now`,
			`COUNT(CASE WHEN sd_target_due_date >= NOW() AND sd_is_intracity = 1 THEN sd_id END) AS total_del_now_intracity`,
			`COUNT(CASE WHEN sd_target_due_date >= NOW() AND sd_is_intracity = 0 THEN sd_id END) AS total_del_now_intercity`,
			`COUNT(CASE WHEN sd_target_due_date < NOW() THEN sd_id END) AS total_del_overdue`,
			`COUNT(CASE WHEN sd_target_due_date < NOW() AND sd_is_intracity = 1 THEN sd_id END) AS total_del_overdue_intracity`,
			`COUNT(CASE WHEN sd_target_due_date < NOW() AND sd_is_intracity = 0 THEN sd_id END) AS total_del_overdue_intercity`,
		)
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": sttDueSummaryData, "error": err})
	}()

	sttDue.From(`stt_due`)
	c.filterSummaryAnalytics(sttDue, params)
	if _, err = sttDue.LoadContext(selfCtx, sttDueSummaryData); err != nil {
		return sttDueSummaryData, err
	}

	return sttDueSummaryData, err
}

func (c *sttDueRepository) filterSummaryAnalytics(sttDue *dbr.SelectStmt, params model.STTDueSummaryAnalyticParameters) {
	sttDue.Where("stt_due.eligible_archived_type = ?", params.EligibleArchivedType)
	sttDue.Where("sd_is_show = ?", params.IsShow)

	if params.TargetStatus != "" {
		sttDue.Where("sd_target_status = ?", params.TargetStatus)
	}

	if params.RefType != "" {
		sttDue.Where("sd_ref_type = ?", params.RefType)
	}

	if params.RefID != 0 {
		sttDue.Where("sd_ref_id = ?", params.RefID)
	}

	if params.DayInterval > 0 && params.IsNeedInterval {
		sttDue.Where("sd_arr_date >= date_sub(now(), interval ? day)", params.DayInterval)
	}
}
