package repository

import (
	"context"
	"fmt"
	"time"

	"github.com/abiewardani/dbr/v2"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
)

func (c *sttDueRepository) FindAllAndPaginate(ctx context.Context, params model.STTDueFindAllAndPaginateParameters) ([]model.STTDueWithSTTModel, int64, error) {
	var (
		opName  = "sttDueRepo.FindAllAndPaginate"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		sttDues     []model.STTDueWithSTTModel
		sttDueTotal model.STTDueModelTotal
		err         error
		totalData   int64

		table       = c.DB.SlaveReport()
		nowDateTime = time.Now().Format(shared.FormatDateTime)
		sttDue      = table.Select(
			`sd_id`, `sd_ref_type`, `sd_ref_id`, `sd_stt_no`, `sd_ref_no`, `sd_bag_no`,
			`sd_booked_type`, `sd_booked_id`, `sd_booked_name`, `sd_stt_booked_at`, `sd_target_status`,
			`sd_target_due_date`, `sd_is_show`, `sd_created_at`, `sd_meta`, `stt_due.eligible_archived_type`,
			fmt.Sprintf(`CASE WHEN sd_target_due_date >= '%s' THEN 'Saat ini' ELSE 'Telat' END AS deadline`, nowDateTime), `stt_created_name`,
			`stt_product_type`, `stt_gross_weight`, `stt_last_status_id`, `stt_total_piece`, `stt_destination_city_id`, `stt_destination_city_name`,
		)

		sttDueCount = table.Select(`COUNT(sd_id) AS __total__`)
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": sttDues, "error": err})
	}()
	params.NowDateTime = nowDateTime
	c.filterFindAllAndPaginate(sttDue, params)
	if _, err = sttDue.LoadContext(selfCtx, &sttDues); err != nil {
		return nil, totalData, err
	}

	// get total data
	params.IsForCount = true
	c.filterFindAllAndPaginate(sttDueCount, params)
	if err = sttDueCount.LoadOneContext(selfCtx, &sttDueTotal); err != nil {
		return nil, totalData, err
	}

	totalData = sttDueTotal.TotalData

	return sttDues, totalData, err
}

func (c *sttDueRepository) filterFindAllAndPaginate(table *dbr.SelectStmt, params model.STTDueFindAllAndPaginateParameters) {
	table.From(`stt_due`).
		Join(`stt`, `sd_stt_no = stt_no`).Where(`stt.eligible_archived_type = ?`, 0)

	table.Where(`stt_due.eligible_archived_type = ?`, 0)
	switch params.PartnerType {
	case model.POS:
		table.Where(`sd_booked_type = ?`, params.PartnerType)
		table.Where(`sd_booked_id = ?`, params.PartnerID)
	default:
		table.Where(`sd_ref_type = ?`, params.PartnerType)
		table.Where(`sd_ref_id = ?`, params.PartnerID)
	}
	table.Where(`sd_is_show = ?`, 1)
	c.filterByBookedAt(table, params)
	c.filterByDeadline(table, params)
	if params.Query != "" {
		paramQuery := "%" + params.Query + "%"
		table.Where(`(sd_stt_no LIKE ? OR sd_ref_no LIKE ? OR sd_bag_no LIKE ?)`, paramQuery, paramQuery, paramQuery)
	}

	if params.BookedType != "" {
		table.Where("sd_booked_type = ?", params.BookedType)
	}

	if params.BookedID != "" {
		table.Where("sd_booked_id = ?", params.BookedID)
	}

	c.filterByTargetStatus(table, params)

	isNotNeedPaginate := params.IsForCount || params.IsForExportData || params.IsForSummary

	if params.SortBy != "" {
		table.OrderBy(params.SortColumn + " " + params.SortBy)
	} else if !isNotNeedPaginate {
		table.OrderBy("sd_stt_booked_at DESC")
	}

	if params.PerPage > 0 && !isNotNeedPaginate {
		table.Limit(uint64(params.PerPage))
	}

	if params.Offset > 0 && !isNotNeedPaginate {
		table.Offset(uint64(params.Offset))
	}

}

func (c *sttDueRepository) filterByTargetStatus(table *dbr.SelectStmt, params model.STTDueFindAllAndPaginateParameters) {
	if params.TargetStatus == "" {
		return
	}

	if params.TargetStatus == model.STISC {
		table.Where("sd_target_status = ? AND sd_booked_type != ?", model.STI, model.SUBCONSOLE)
	} else {
		table.Where("sd_target_status = ?", params.TargetStatus)
	}
}

func (c *sttDueRepository) filterByDeadline(table *dbr.SelectStmt, params model.STTDueFindAllAndPaginateParameters) {
	switch params.Deadline {
	case model.STTDueRepoNowDeadline:
		if params.IsValidNowDateTime() {
			table.Where("sd_target_due_date >= ?", params.NowDateTime)
		} else {
			table.Where("sd_target_due_date >= NOW()")
		}
	case model.STTDueRepoOverdueDeadline:
		if params.IsValidNowDateTime() {
			table.Where("sd_target_due_date < ?", params.NowDateTime)
		} else {
			table.Where("sd_target_due_date < NOW()")
		}
	}
}
func (c *sttDueRepository) filterByBookedAt(table *dbr.SelectStmt, params model.STTDueFindAllAndPaginateParameters) {
	if params.IsValidNowDateTime() {
		table.Where(`sd_stt_booked_at >= date_sub(?, INTERVAL 7 DAY)`, params.NowDateTime)
	} else {
		table.Where("sd_stt_booked_at >= date_sub(NOW(), INTERVAL 7 DAY)")
	}
}
