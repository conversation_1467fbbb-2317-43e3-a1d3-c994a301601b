package repository

import (
	"context"

	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/abiewardani/dbr/v2"
)

func (c *deliveryRepositoryCtx) CountDriverPendingReconcile(ctx context.Context, params *model.CountDriverPendingReconcileParams) (int, error) {
	res := 0
	opName := "deliveryRepositoryCtx-CountDriverPendingReconcile"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": err})
	}()
	startDate := params.StartDate.Format(shared.FormatDateTime)
	endDate := params.EndDate.Format(shared.FormatDateTime)
	query := `SELECT count(d.id) FROM delivery d 
			JOIN (
				SELECT 
					MIN(id) as min_id, 
					MAX(id) as max_id 
				FROM delivery 
				WHERE 
					(created_at >= ? AND created_at <= ?)
			) AS limitation ON 1=1 
            JOIN stt s ON d.stt_no = s.stt_no
		WHERE 
		  (d.id >= limitation.min_id AND d.id <= limitation.max_id) AND 
		  driver_phone = ? AND
		  reconcile_collected_at IS NULL AND
		  partner_id = ? AND
		  (s.stt_is_cod = true AND d.finished_status = 'POD' AND payment_method = 'CASH' OR d.finished_status IN('DEX','CODREJ')) AND
		  payment_method NOT IN ('QRIS','FREE');`
	queryParams := []interface{}{startDate, endDate, params.DriverPhone, params.PartnerID}
	if err = c.DB.SlaveReport().QueryRowContext(selfCtx, query, queryParams...).Scan(&res); err != nil {
		if err == dbr.ErrNotFound {
			return res, nil
		}
		logger.Ef(`DeliveryRepository-CountDriverPendingReconcile Error %v`, err)
		return res, err
	}
	return res, nil
}
