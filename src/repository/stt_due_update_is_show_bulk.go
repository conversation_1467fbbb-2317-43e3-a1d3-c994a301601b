package repository

import (
	"context"

	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
)

func (c *sttDueRepository) UpdateIsShowBulk(ctx context.Context, params *model.STTDueUpdateIsShow) error {
	var (
		opName  = "sttDueRepo.UpdateIsShowBulk"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		tx, err = c.DB.Master().Begin()
	)
	defer func() {
		trace.Finish(map[string]interface{}{"param": params, "error": err})
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
			tx.RollbackUnlessCommitted()
			return
		}
		if err == nil && tx.Commit() != nil {
			logger.Ef("sttDueRepo.UpdateIsShowBulk Commit Error: %v", err)
		}
	}()
	if err != nil {
		return err
	}

	if len(params.STTNos) <= 0 {
		return nil
	}

	_, err = tx.Update("stt_due").
		Set(`sd_is_show`, params.IsShow).
		Where(`sd_stt_no IN ? AND sd_target_status = ?`, params.STTNos, model.STI).
		ExecContext(selfCtx)
	if err != nil {
		logger.Ef("sttDueRepo.UpdateIsShowBulk Error: %v", err)
		_ = tx.Rollback()
		return err
	}
	return err
}
