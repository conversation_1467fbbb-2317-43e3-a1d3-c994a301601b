package repository

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/config/cache"
	"github.com/Lionparcel/hydra/config/database"
	"github.com/Lionparcel/hydra/config/elastic"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
)

type (
	PtPosRepository interface {
		GenerateToken(ctx context.Context) (*model.PtPosTokenResponse, error)
		GetFee(ctx context.Context, params *model.PtPosGetFeeParams) (*model.PtPosGetFeeResponse, error)
		CreateOrder(ctx context.Context, params *model.PtPosCreateOrderParams) (*model.PtPosCreateOrderResponse, error)
		Tracking(ctx context.Context, params *model.TrackingPtposParams) (*model.TrackingPtposResponse, error)
		GetStatusMapping(ctx context.Context) (model.ListPtposGenesisStatusMapping, error)
	}

	ptPosRepositoryCtx struct {
		cfg             *config.Config
		Cache           cache.Client
		DB              database.DbrDatabase
		ES              elastic.ElasticClient
		partnerLogIndex string
		partnerLog      PartnerLogRepository
	}

	PtPosParameters func(*ptPosRepositoryCtx)
)

func NewPtPosRepository(cfg *config.Config, partnerLog PartnerLogRepository) PtPosRepository {
	return &ptPosRepositoryCtx{
		cfg:             cfg,
		Cache:           cfg.Cache(),
		DB:              cfg.DB(),
		ES:              cfg.ES(),
		partnerLogIndex: cfg.Environment() + `-` + cfg.ServiceName() + `-` + model.PartnerLogIndex,
		partnerLog:      partnerLog,
	}
}

func (c *ptPosRepositoryCtx) CreateCache(data string, res interface{}, expiredIn int64) {
	var err error
	key := fmt.Sprintf("%v:%v", model.PtPosAccountToken, data)

	all, err := json.Marshal(res)
	if err != nil {
		return
	}

	// expired time from pt pos will be reduce 60min
	// res.ExpiresIn (n second) - model.PtPosAccountTokenTimeBeforeExpired
	ttl := expiredIn - model.PtPosAccountTokenTimeBeforeExpired
	if ttl < 0 {
		ttl = 0
	}
	ttl = 60 // hardcoded, inconsistent expired token from pt pos
	_, err = c.Cache.Set(key, string(all), time.Duration(ttl)*time.Second)
	if err != nil {
		logger.E(err)
	}
}

func (c *ptPosRepositoryCtx) GetCache(data string, res interface{}) {
	var err error

	key := fmt.Sprintf("%v:%v", model.PtPosAccountToken, data)
	ret, err := c.Cache.Get(key)
	if err != nil {
		return
	}

	json.Unmarshal([]byte(ret), &res)
}

func (c *ptPosRepositoryCtx) GenerateToken(ctx context.Context) (*model.PtPosTokenResponse, error) {
	var (
		opName      = "ptPosRepositoryCtx-GenerateToken"
		trace       = tracer.StartTrace(ctx, opName)
		selfCtx     = trace.Context()
		params      = new(model.PtPosTokenParams)
		response    = new(model.PtPosTokenResponse)
		respString  string
		err         error
		isFromCache bool
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": response, "error": err})

		go c.partnerLog.Insert(context.Background(), &model.PartnerLog{
			Action:  model.PLSttPTPOSGetToken,
			Request: params,
			Response: map[string]interface{}{
				"is_from_cache": isFromCache,
				"response":      response,
				"respString":    respString,
			},
		})
	}()

	params.PtPosKey = c.cfg.PtPosKey()
	params.PtPosSecret = c.cfg.PtPosSecret()
	c.GetCache(params.PtPosKey, response)
	if response != nil && response.TokenType != `` {
		isFromCache = true
		return response, nil
	}

	auth := params.PtPosKey + ":" + params.PtPosSecret
	headers := map[string]string{
		"Content-Type":  "application/x-www-form-urlencoded",
		"Authorization": "Basic " + base64.StdEncoding.EncodeToString([]byte(auth)),
	}
	payload := "grant_type=client_credentials"

	requestURL := fmt.Sprintf("%s/token", c.cfg.PtPosURL())
	resp, statusCode, e := shared.GetHTTPRequestJSON(selfCtx, "POST", requestURL, strings.NewReader(payload), headers)
	if e != nil {
		logger.Ef(`ptPosRepositoryCtx-GenerateToken Status Code %s`, statusCode)
		logger.Ef(`ptPosRepositoryCtx-GenerateToken Resp %s`, string(resp))
		logger.Ef(`ptPosRepositoryCtx-GenerateToken Error %v`, e)
		return nil, shared.ErrUnexpected
	}

	if statusCode != http.StatusOK {
		logger.Ef(`ptPosRepositoryCtx-GenerateToken Status Code %s`, statusCode)
		logger.Ef(`ptPosRepositoryCtx-GenerateToken Resp %s`, string(resp))
		logger.Ef(`ptPosRepositoryCtx-GenerateToken Error %v`, e)

		if shared.IsServerErrors[statusCode] {
			return nil, shared.ErrUnexpected
		}

		errRes := model.ErrorNinjaResponse{}
		if err := json.Unmarshal(resp, &errRes); err != nil {
			logger.Ef(`ptPosRepositoryCtx-GenerateToken Error Parse Failed Response %v`, err)
			return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Failed to generate access token",
				"id": "Gagal membuat access token",
			})
		}

		return nil, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": fmt.Sprintf("%s. %s", errRes.Error.Title, errRes.Error.Message),
			"id": fmt.Sprintf("%s. %s", errRes.Error.Title, errRes.Error.Message),
		})

	}
	respString = string(resp)

	e = json.Unmarshal(resp, &response)
	if e != nil {
		logger.Ef(`ptPosRepositoryCtx-GenerateToken Error Parse Success Response %v`, e)
		return nil, e
	}

	go c.CreateCache(params.PtPosKey, response, int64(response.ExpireIn))

	return response, nil
}

func (c *ptPosRepositoryCtx) GetFee(ctx context.Context, params *model.PtPosGetFeeParams) (*model.PtPosGetFeeResponse, error) {
	var (
		opName            = "ptPosRepositoryCtx-GetFee"
		trace             = tracer.StartTrace(ctx, opName)
		selfCtx           = trace.Context()
		response          = new(model.PtPosGetFeeResponse)
		rawResponseString = ``
		err               error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": response, "error": err})

		go c.partnerLog.Insert(context.Background(), &model.PartnerLog{
			Action:  model.PLSttPTPOSGetFee,
			RefID:   params.SttNo,
			Request: params,
			Response: map[string]interface{}{
				"response":     response,
				"raw_response": rawResponseString,
			},
		})
	}()

	token, err := c.GenerateToken(selfCtx)
	if err != nil {
		return nil, err
	}

	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": fmt.Sprintf("%s %s", token.TokenType, token.AccessToken),
	}

	if c.cfg.PtPosProduction() {
		headers["X-POS-USER"] = c.cfg.PtPosXPos()
		headers["X-POS-PASSWORD"] = c.cfg.PtPosXPassword()
	}

	params.CustomerID = c.cfg.PtPosMemberID()

	requestByte, _ := json.Marshal(params)
	requestURL := fmt.Sprintf("%s/utility/1.0.0/getFee", c.cfg.PtPosURL())
	resp, statusCode, e := shared.GetHTTPRequestJSON(selfCtx, "POST", requestURL, bytes.NewBuffer(requestByte), headers)
	if statusCode != 200 {
		logger.Ef(`ptPosRepositoryCtx-GetFee Status Code %d`, statusCode)
		logger.Ef(`ptPosRepositoryCtx-GetFee Resp %s`, string(resp))
		logger.Ef(`ptPosRepositoryCtx-GetFee Error %v`, e)

		return nil, shared.ErrUnexpected
	}

	var rawResponse struct {
		Response json.RawMessage `json:"response"`
	}

	err = json.Unmarshal(resp, &rawResponse)
	if err != nil {
		logger.Ef(`ptPosRepositoryCtx-GetFee Error Unmarshal %v`, err)
		return nil, shared.ErrUnexpected
	}

	// Check if response is array or single object
	rawResponseString = string(rawResponse.Response)
	if strings.Contains(rawResponseString, `data":[`) {
		if c.cfg.PtPosProduction() {
			feesInt := model.PtPosGetFeeResponseDatasInt{}
			err = json.Unmarshal(rawResponse.Response, &feesInt)
			if err != nil {
				logger.Ef(`ptPosRepositoryCtx-GetFee Error Unmarshal Array %v`, err)
				return nil, shared.ErrUnexpected
			}

			for i := range feesInt.Data {
				data := model.PtPosGetFeeResponseData{
					ServiceCode:           strconv.Itoa(feesInt.Data[i].ServiceCode),
					ServiceName:           feesInt.Data[i].ServiceName,
					Fee:                   feesInt.Data[i].Fee,
					FeeTax:                feesInt.Data[i].FeeTax,
					Insurance:             feesInt.Data[i].Insurance,
					InsuranceTax:          feesInt.Data[i].InsuranceTax,
					Notes:                 feesInt.Data[i].Notes,
					Estimation:            feesInt.Data[i].Estimation,
					Penyesuaian:           feesInt.Data[i].Penyesuaian,
					PenyesuaianPersentase: feesInt.Data[i].PenyesuaianPersentase,
					Discount:              feesInt.Data[i].Discount,
				}
				response.Response.Data = append(response.Response.Data, data)
			}

		} else {
			err = json.Unmarshal(rawResponse.Response, &response.Response)
			if err != nil {
				logger.Ef(`ptPosRepositoryCtx-GetFee Error Unmarshal Array %v`, err)
				return nil, shared.ErrUnexpected
			}
		}
	} else {
		var singleData model.PtPosGetFeeResponseSingleData
		err = json.Unmarshal(rawResponse.Response, &singleData)
		if err != nil {
			logger.Ef(`ptPosRepositoryCtx-GetFee Error Unmarshal Object %v`, err)
			return nil, shared.ErrUnexpected
		}

		data := model.PtPosGetFeeResponseData{
			ServiceCode:           strconv.Itoa(singleData.Data.ServiceCode),
			ServiceName:           singleData.Data.ServiceName,
			Fee:                   singleData.Data.Fee,
			FeeTax:                singleData.Data.FeeTax,
			Insurance:             singleData.Data.Insurance,
			InsuranceTax:          singleData.Data.InsuranceTax,
			Notes:                 singleData.Data.Notes,
			Estimation:            singleData.Data.Estimation,
			Penyesuaian:           singleData.Data.Penyesuaian,
			PenyesuaianPersentase: singleData.Data.PenyesuaianPersentase,
			Discount:              singleData.Data.Discount,
		}
		response.Response.Data = []model.PtPosGetFeeResponseData{data}
	}

	return response, nil
}

func (c *ptPosRepositoryCtx) CreateOrder(ctx context.Context, params *model.PtPosCreateOrderParams) (*model.PtPosCreateOrderResponse, error) {
	var (
		opName   = "ptPosRepositoryCtx-CreateOrder"
		trace    = tracer.StartTrace(ctx, opName)
		selfCtx  = trace.Context()
		response = new(model.PtPosCreateOrderResponse)
		err      error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": response, "error": err})

		go c.partnerLog.Insert(context.Background(), &model.PartnerLog{
			Action:   model.PLSttPTPOSOrderToPTPOS,
			RefID:    params.OrderID,
			Request:  params,
			Response: response,
		})
	}()

	token, err := c.GenerateToken(selfCtx)
	if err != nil {
		return nil, err
	}

	headers := map[string]string{
		"Content-Type":  "application/json",
		"Authorization": fmt.Sprintf("%s %s", token.TokenType, token.AccessToken),
	}
	endpoint := `webhookpos/1.0.1/AddPostingDoc`

	if c.cfg.PtPosProduction() {
		headers["X-POS-USER"] = c.cfg.PtPosXPos()
		headers["X-POS-PASSWORD"] = c.cfg.PtPosXPassword()
		endpoint = `webhook/1.0/AddPostingDoc`
	}

	requestByte, _ := json.Marshal(params)

	requestURL := fmt.Sprintf("%s/%s", c.cfg.PtPosURL(), endpoint)
	resp, statusCode, e := shared.GetHTTPRequestJSON(selfCtx, "POST", requestURL, bytes.NewBuffer(requestByte), headers)
	if statusCode != 200 {
		logger.Ef(`ptPosRepositoryCtx-CreateOrder Status Code %d`, statusCode)
		logger.Ef(`ptPosRepositoryCtx-CreateOrder Resp %s`, string(resp))
		logger.Ef(`ptPosRepositoryCtx-CreateOrder Error %v`, e)

		return nil, shared.ErrUnexpected
	}

	err = json.Unmarshal([]byte(resp), response)
	if err != nil {
		logger.E(err)
	}

	return response, nil
}
