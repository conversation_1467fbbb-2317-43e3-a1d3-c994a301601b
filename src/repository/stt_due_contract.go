package repository

import (
	"context"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/config/database"
	"github.com/Lionparcel/hydra/src/model"
)

//go:generate mockery --name SttDueRepository
type (
	sttDueRepository struct {
		cfg *config.Config
		DB  database.DbrDatabase
	}

	// SttDueRepository ...
	SttDueRepository interface {
		GetSttNeedToSti(ctx context.Context, params *model.GetSttNeedToStiParams) ([]model.SttNeedToSti, error)
		FindAllAndPaginate(ctx context.Context, params model.STTDueFindAllAndPaginateParameters) ([]model.STTDueWithSTTModel, int64, error)
		SummaryData(ctx context.Context, params model.STTDueFindAllAndPaginateParameters) (model.STTDueSummaryData, error)
		FindAllBookedAndPaginate(ctx context.Context, params model.STTDueFindAllBookedAndPaginateParameters) ([]model.STTDueModel, int64, error)
		UpdateIsShowBulk(ctx context.Context, params *model.STTDueUpdateIsShow) error
		SelectSttDue(ctx context.Context, params model.STTDuePartnerParam) ([]model.STTDueModel, error)
		CountSttDue(ctx context.Context, params model.CountSttDueParam) (int64, error)
		UpdateSttDueBagNo(ctx context.Context, params *model.UpdateSttDueBagNoParams) error
		SelectFilterSttDue(ctx context.Context, params *model.STTDueFilterParam) ([]model.STTDueWithSTTModel, error)
		InsertBulk(ctx context.Context, data []*model.STTDueModel) error
		StiDestDelFindAllPaginate(ctx context.Context, params model.SttDueStiDestDelFindAllAndPaginateParameter) ([]model.STTDueWithSTTAndPriorityDeliveryModel, int64, error)
		UpdateBulk(ctx context.Context, params *model.STTDueUpdateBulkParams) error
		Get(ctx context.Context, params *model.STTDueModel) (*model.STTDueModel, error)
		SummaryAnalytic(ctx context.Context, params model.STTDueSummaryAnalyticParameters) (*model.STTDueSummaryAnalyticData, error)
		SelectSttDueNeedToStiscCount(ctx context.Context, params model.CountSttDueSTISCParam) (*model.CountSttDueSTISCResult, error)
		GetBySttNo(ctx context.Context, params *model.GetBySttNoParams) (*model.STTDueModel, error)
	}

	STTDueParameters func(*sttDueRepository)
)

func SetSTTDueRepoCFG(cfg *config.Config) STTDueParameters {
	return func(r *sttDueRepository) {
		r.cfg = cfg
	}
}

func SetSTTDueRepoDB(db database.DbrDatabase) STTDueParameters {
	return func(r *sttDueRepository) {
		r.DB = db
	}
}

// NewSttDueRepository ...
func NewSttDueRepository(paramFn ...STTDueParameters) SttDueRepository {
	repo := &sttDueRepository{}
	for _, fn := range paramFn {
		fn(repo)
	}
	return repo
}
