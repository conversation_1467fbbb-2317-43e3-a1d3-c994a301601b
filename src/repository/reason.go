package repository

import (
	"context"

	"github.com/Lionparcel/hydra/shared/logger"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/abiewardani/dbr/v2"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/config/cache"
	"github.com/Lionparcel/hydra/config/database"
	"github.com/Lionparcel/hydra/src/model"
)

// ReasonRepository ...
type ReasonRepository interface {
	Select(ctx context.Context, params *model.ReasonViewParams) ([]model.Reason, error)
	GetDetail(ctx context.Context, params *model.ReasonViewParams) (*model.ReasonDetailResult, error)
	SelectDetail(ctx context.Context, params *model.ReasonViewParams) ([]model.ReasonDetailResult, error)
	SelectReasonExternalDetail(ctx context.Context, reasonCodes []string) ([]model.ReasonExternalDetailResult, error)
}

// reasonRepository ...
type reasonRepositoryCtx struct {
	cfg   *config.Config
	Cache cache.Client
	DB    database.DbrDatabase
}

// NewReasonRepository ...
func NewReasonRepository(cfg *config.Config) ReasonRepository {
	return &reasonRepositoryCtx{
		cfg:   cfg,
		Cache: cfg.Cache(),
		DB:    cfg.DB(),
	}
}

func (c *reasonRepositoryCtx) Select(ctx context.Context, params *model.ReasonViewParams) ([]model.Reason, error) {
	res := []model.Reason{}

	opName := "reasonRepositoryCtx-Select"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": err})
	}()

	db := c.DB.Slave().Select(`*`).From(`reason`).Join(`reason_mapping`, `reason.reason_id = reason_mapping.reason_mapping_reason_id`)

	if params.StatusCode != `` {
		db = db.Where(`reason_mapping.reason_mapping_status_code = ?`, params.StatusCode)
	}

	if params.ReasonStatus != `` {
		db = db.Where(`reason.reason_status = ?`, params.ReasonStatus)
	}

	if params.ReasonCode != `` {
		db = db.Where(`reason.reason_code = ?`, params.ReasonCode)
	}

	if params.IsNotShownEnable {
		db = db.Where(`reason.reason_is_not_shown = false`)
	}

	if params.IsCreateTicketSf {
		db = db.Where(`reason.is_create_ticket_sf = 1`)
	}

	if params.ReasonCategory != `` {
		db = db.Where(`reason.reason_category = ?`, params.ReasonCategory)
	}

	if _, err := db.Load(&res); err != nil {
		if err == dbr.ErrNotFound {
			return nil, nil
		}
		logger.Ef(`ReasonRepository-Select Error %s`, err.Error())
		return nil, err
	}

	return res, nil
}

func (c *reasonRepositoryCtx) GetDetail(ctx context.Context, params *model.ReasonViewParams) (*model.ReasonDetailResult, error) {
	res := model.ReasonDetailResult{}
	opName := "reasonRepositoryCtx-GetDetail"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var errLog error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": errLog})
	}()

	db := c.DB.Slave().Select(`*`).From(`reason`).Join(`reason_mapping`, `reason.reason_id = reason_mapping.reason_mapping_reason_id`)

	if params.ReasonCode != `` {
		db = db.Where(`reason.reason_code = ? OR reason.reason_external_code = ?`, params.ReasonCode, params.ReasonCode)
	}

	if params.ReasonTitle != `` {
		db = db.Where(`reason.reason_title = ? or reason.reason_description_en = ?`, params.ReasonTitle, params.ReasonTitle)
	}

	if params.StatusCode != `` {
		db = db.Where(`reason_mapping.reason_mapping_status_code = ?`, params.StatusCode)
	}

	if err := db.Limit(1).LoadOneContext(selfCtx, &res); err != nil {
		if err == dbr.ErrNotFound {
			return nil, nil
		}
		errLog = err
		logger.Ef(`ReasonRepository-GetDetail Error %s`, err.Error())
		return nil, err
	}

	return &res, nil
}

func (c *reasonRepositoryCtx) SelectDetail(ctx context.Context, params *model.ReasonViewParams) ([]model.ReasonDetailResult, error) {
	res := []model.ReasonDetailResult{}

	opName := "reasonRepositoryCtx-SelectDetail"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": params, "result": res, "error": err})
	}()

	db := c.DB.Slave().Select(`*`).From(`reason`).LeftJoin(`reason_mapping`, `reason.reason_id = reason_mapping.reason_mapping_reason_id`)

	if params.StatusCode != `` {
		db = db.Where(`reason_mapping.reason_mapping_status_code = ?`, params.StatusCode)
	}

	if params.ReasonStatus != `` {
		db = db.Where(`reason.reason_status = ?`, params.ReasonStatus)
	}

	if params.ReasonCode != `` {
		db = db.Where(`reason.reason_code = ?`, params.ReasonCode)
	}

	if params.IsNotShownEnable {
		db = db.Where(`reason.reason_is_not_shown = false`)
	}

	if params.IsCreateTicketSf {
		db = db.Where(`reason.is_create_ticket_sf = 1`)
	}

	if params.ReasonCategory != `` {
		db = db.Where(`reason.reason_category = ?`, params.ReasonCategory)
	}

	if params.ReasonTitle != `` {
		db = db.Where(`reason.reason_title = ?`, params.ReasonTitle)
	}

	if _, err := db.Load(&res); err != nil {
		if err == dbr.ErrNotFound {
			return nil, nil
		}
		logger.Ef(`ReasonRepository-Select Error %s`, err.Error())
		return nil, err
	}

	return res, nil
}
