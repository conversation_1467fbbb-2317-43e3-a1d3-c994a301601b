package repository

import (
	"context"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/config/cache"
	"github.com/Lionparcel/hydra/config/database"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase/dispatch"
)

type dispatchRepositoryCtx struct {
	cfg   *config.Config
	Cache cache.Client
	DB    database.DbrDatabase
}

type DispatchRepository interface {
	GetActiveDispatchTemporaryBySttNo(ctx context.Context, params *model.GetDispatchTemporaryByAccountSttNo) ([]model.DispatchTemporary, error)
	UpdateDispatchTemporary(ctx context.Context, record *model.DispatchTemporary) error
	CreateDispatchTemporary(ctx context.Context, record *model.DispatchTemporary) error
	CreateNew(ctx context.Context, params model.CreateNewDispatchRequest) (model.DispatchWithDetails, error)
	GetListScannedDispatch(ctx context.Context, params dispatch.ListScannedDispatch) ([]model.DispatchTemporary, error)
	GetDispatchTemporary(ctx context.Context, param model.GetDispatchTemporaryParams) ([]model.DispatchTemporary, error)
	GetDetailDispatch(ctx context.Context, dispatchId int64, search string) (model.DispatchWithDetails, error)
	GetListManifestDispatch(ctx context.Context, params dispatch.ListDispatchManifest) ([]model.Dispatch, int64, error)
}

func NewDispatchRepository(cfg *config.Config) DispatchRepository {
	return &dispatchRepositoryCtx{
		cfg:   cfg,
		Cache: cfg.Cache(),
		DB:    cfg.DB(),
	}
}
