package v2

import (
	"context"
	"crypto/rsa"
	"net/http"
	"strconv"

	"github.com/360EntSecGroup-Skylar/excelize/v2"
	"github.com/Lionparcel/hydra/middleware"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/usecase"
	"github.com/Lionparcel/hydra/src/usecase/sti"
	"github.com/Lionparcel/hydra/src/usecase/sti_temporary"
	"github.com/labstack/echo"
)

type StiDelivery struct {
	StiUc usecase.Sti
}

func NewStiDelivery(StiUc usecase.Sti) StiDelivery {
	return StiDelivery{StiUc: StiUc}
}

func (c *StiDelivery) Mount(group *echo.Group, publicKey *rsa.PublicKey) {
	group.GET("/stt-detail", c.<PERSON>il<PERSON>tt, middleware.JWTVerify(publicKey, false, []string{}))
	group.GET("/pdf/:id", c.ViewStiDetailV2, middleware.JWTVerify(publicKey, false, []string{`PARTNER`}))
	group.GET("", c.ViewStiV2, middleware.JWTVerify(publicKey, false, []string{`PARTNER`}))
	group.GET("/temporary", c.ViewStiTemporaryV2, middleware.JWTVerify(publicKey, false, []string{`PARTNER`, `INTERNAL`}))
	group.POST("/generate", c.CreateSti, middleware.JWTVerify(publicKey, false, []string{}))
	group.POST("/manifest", c.ManifestSti, middleware.JWTVerify(publicKey, false, []string{`PARTNER`}))
	group.GET("/excel/:id", c.DownloadSti, middleware.JWTVerify(publicKey, false, []string{}))
}

func (c *StiDelivery) ViewDetailStt(e echo.Context) error {
	ctx := e.Request().Context()
	form := new(sti.ViewDetailSttRequestV2)

	opName := "DeliverySti-ViewDetailStt"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	detail := &sti.ViewDetailSttStiResponse{}
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": detail, "error": err})
	}()

	partnerID := e.Get("partnerID").(int)
	partnerType := e.Get("partnerType").(string)
	token := e.Get("tokenStr").(string)

	if err = e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	form.PartnerID = partnerID
	form.PartnerType = partnerType
	form.Token = token

	detail, err = c.StiUc.ViewDetailSttV2(selfCtx, *form)
	if err != nil {
		return shared.HttpError(e, err)
	}

	resp := shared.JSONSuccess(`Success`, detail)
	return e.JSON(http.StatusOK, resp)
}

func (c *StiDelivery) ViewStiDetailV2(e echo.Context) error {
	id := e.Param("id")
	ctx := e.Request().Context()
	form := new(sti.ViewStiPDFV2Request)

	opName := "DeliverySti-ViewStiDetailV2"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res := &sti.ViewStiPDFV2{}
	var ID int
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": ID, "result": res, "error": err})
	}()

	form.Token = e.Get("tokenStr").(string)
	form.PartnerType = e.Get("partnerType").(string)
	form.PartnerID = e.Get("partnerID").(int)
	ID, err = strconv.Atoi(id)
	if err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	form.StiID = ID

	res, err = c.StiUc.ViewStiDetailV2(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}
	response := shared.JSONResponse(http.StatusCreated, `STI List successfully get`, true, res)
	return e.JSON(response.Code, response)
}

func (c *StiDelivery) ViewStiV2(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "DeliverySti-ViewStiV2"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	request := sti.ViewStiRequest{}
	pagination := new(shared.Pagination)
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": request, "result": pagination, "error": err})
	}()

	pageParam := e.QueryParam("page")
	limitParam := e.QueryParam("limit")
	var (
		page  int = 1
		limit int = 10
	)

	if pageParam != `` {
		page, err = strconv.Atoi(pageParam)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Please fill valid page query",
				"id": "Isi page parameter",
			})
			return shared.HttpError(e, err)
		}
	}

	if limitParam != `` {
		limit, err = strconv.Atoi(limitParam)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Please fill valid limit query",
				"id": "Isi limit parameter",
			})
			return shared.HttpError(e, err)
		}
	}

	partnerType := e.Get("partnerType").(string)

	isTotalData := false
	isTotalDataParams := e.QueryParam("is_total_data")
	if isTotalDataParams != `` {
		isTotalData, err = strconv.ParseBool(isTotalDataParams)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid is total data",
				"id": "Total data tidak valid",
			})
			return shared.HttpError(e, err)
		}
	}

	request = sti.ViewStiRequest{
		Page:        page,
		Limit:       limit,
		StartDate:   e.QueryParam("start_date"),
		EndDate:     e.QueryParam("end_date"),
		Search:      e.QueryParam("search"),
		PartnerType: partnerType,
		IsTotalData: isTotalData,
	}

	pagination, err = c.StiUc.ViewStiV2(selfCtx, request)
	if err != nil {
		return shared.HttpError(e, err)
	}
	resp := shared.JSONSuccess(`Success`, pagination)
	return e.JSON(resp.Code, resp)
}

func (c *StiDelivery) ViewStiTemporaryV2(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "DeliverySti-ViewStiTemporaryV2"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	request := new(sti_temporary.StiTemporaryListV2Request)
	data := []sti_temporary.StiTemporaryListV2Response{}
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": request, "result": data, "error": err})
	}()

	if err = e.Bind(request); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	request.AccountID = e.Get("accountID").(int64)

	data, err = c.StiUc.ViewStiTemporaryV2(selfCtx, request)
	if err != nil {
		return shared.HttpError(e, err)
	}

	resp := shared.JSONSuccess(`Success`, data)
	return e.JSON(resp.Code, resp)
}

func (c *StiDelivery) CreateSti(e echo.Context) error {
	req := new(sti.CreateStiRequest)
	ctx := e.Request().Context()
	ctx = context.WithValue(ctx, `tokenStr`, e.Get("tokenStr"))

	opName := "DeliverySti-CreateSti"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	res := sti.ResponseCreateV2{}
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": req, "result": res, "error": err})
	}()

	req.AccountID = e.Get("accountID").(int64)
	req.AccountName = e.Get("accountName").(string)

	req.PartnerID = e.Get("partnerID").(int)
	req.PartnerCode = e.Get("partnerCode").(string)
	req.PartnerName = e.Get("partnerName").(string)
	req.PartnerType = e.Get("partnerType").(string)
	req.Token = e.Get(`tokenStr`).(string)

	if err = e.Bind(req); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	res, err = c.StiUc.CreateSTIV2(selfCtx, req)
	if err != nil {
		return shared.HttpError(e, err)
	}
	response := shared.JSONResponse(http.StatusCreated, `STI successfully generate`, true, res)
	return e.JSON(response.Code, response)
}

// CreateStiDest ...
func (c *StiDelivery) ManifestSti(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "DeliveryV2-StiDelivery-ManifestSti"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	res := new(sti.ManifestStiResponse)
	form := new(sti.CreateStiRequest)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": res, "error": err})
	}()

	if err = e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	partnerID := e.Get("partnerID").(int)
	partnerType := e.Get("partnerType").(string)
	partnerName := e.Get("partnerName").(string)
	partnerCode := e.Get("partnerCode").(string)
	token := e.Get("tokenStr").(string)
	accountID := e.Get("accountID").(int64)
	accountName := e.Get("accountName").(string)

	form.PartnerID = partnerID
	form.PartnerType = partnerType
	form.PartnerCode = partnerCode
	form.PartnerName = partnerName
	form.AccountID = accountID
	form.AccountName = accountName
	form.Token = token

	res, err = c.StiUc.ManifestStiV2(selfCtx, form)

	if err != nil {
		return shared.HttpError(e, err)
	}

	resp := shared.JSONSuccess(`STI Dest successfully created`, res)
	return e.JSON(http.StatusOK, resp)
}

func (c *StiDelivery) DownloadSti(e echo.Context) error {
	id := e.Param("id")
	token := e.Get("tokenStr").(string)
	partnerID := e.Get("partnerID").(int)
	partnerType := e.Get("partnerType").(string)
	ctx := e.Request().Context()
	form := new(sti.DownloadExcelNeedToStiV2)
	var err error

	opName := "StiDelivery-DownloadSti"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": id, "result": nil, "error": err})
	}()

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": id, "error": err})
	}()

	if err = e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	data := new(excelize.File)

	form.PartnerID = partnerID
	form.PartnerType = partnerType
	form.StiID, _ = strconv.Atoi(id)
	form.Token = token
	if form.NeedToSti {
		data, err = c.StiUc.DownloadNeedToStiV2(selfCtx, form)
	} else {
		data, err = c.StiUc.DownloadStiV2(selfCtx, form)
	}

	if err != nil {
		err = shared.HttpError(e, err)
		return err
	}

	e.Response().Header().Set(echo.HeaderContentType, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	e.Response().Header().Set(echo.HeaderContentDisposition, "attachment; filename=sample.xlsx")
	e.Response().Header().Set("File-Name", "report_sti_manifest.xlsx")
	e.Response().Header().Set("Access-Control-Expose-Headers", "File-Name")

	if err := data.Write(e.Response().Writer); err != nil {
		return e.JSON(http.StatusInternalServerError, map[string]string{
			"message": "Unable to write Excel file",
		})
	}
	return nil
}
