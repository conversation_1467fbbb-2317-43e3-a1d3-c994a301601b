package v2

import (
	"context"
	"crypto/rsa"
	"net/http"
	"strings"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/middleware"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	"github.com/Lionparcel/hydra/src/usecase"
	"github.com/Lionparcel/hydra/src/usecase/cargo_v2"
	"github.com/labstack/echo"
)

// CargoDeliveryV2 ...
type CargoDeliveryV2 struct {
	cargoV2UC usecase.CargoV2
	cfg       *config.Config
}

// NewCargoDeliveryV2 ...
func NewCargoDeliveryV2(
	cargoV2UC usecase.CargoV2,
	cfg *config.Config,
) CargoDeliveryV2 {
	return CargoDeliveryV2{
		cargoV2UC: cargoV2UC,
		cfg:       cfg,
	}
}

// Mount ...
func (c *CargoDeliveryV2) Mount(group *echo.Group, publicKey *rsa.PublicKey) {
	group.POST("", c.CreateCargoV2, middleware.JWTVerify(publicKey, false, []string{}))
	group.GET("/scheduler", c.SchedulerCargoNgen)
	group.POST("/force/dep-arr", c.ForceUpdateDepArrCargoFlight, middleware.JWTVerify(publicKey, false, []string{`SYSTEM`}))
}

func (c *CargoDeliveryV2) CreateCargoV2(e echo.Context) error {
	form := &cargo_v2.CreateCargoV2Request{}
	ctx := e.Request().Context()

	opName := "DeliveryCargo-CreateCargoV2"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var res *cargo_v2.CreateCargoV2Response
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": res, "error": err})
	}()

	token := e.Get("tokenStr").(string)
	accountID := e.Get("accountID").(int64)
	accountName := e.Get("accountName").(string)
	partnerID := e.Get("partnerID").(int)
	partnerType := e.Get("partnerType").(string)
	hostUrl := e.Get("host_url").(string)
	responseId := e.Response().Header().Get("X-Request-Id")

	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	form.Token = token
	form.AccountID = int(accountID)
	form.AccountName = accountName
	form.PartnerID = partnerID
	form.PartnerTypeToken = partnerType
	form.HostUrl = hostUrl
	form.RequestID = responseId

	createCargo := c.cargoV2UC.CreateCargoV2
	if form.IsFromRtcSabre || (form.IsUseSabre && strings.ToUpper(form.CargoType) == model.PLANE) { // cargo plane
		createCargo = c.cargoV2UC.CreateCargoV2Sabre
	}

	res, err = createCargo(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}

	resp := shared.JSONSuccess(`Success`, res)
	return e.JSON(http.StatusOK, resp)
}

func (c *CargoDeliveryV2) SchedulerCargoNgen(e echo.Context) error {
	var (
		ctx     = context.Background()
		opName  = "DeliveryCargoV2-SchedulerCargoNgen"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		res     = map[string]bool{
			"success": true,
		}
		err error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{
			"param": nil,
			"result": func() interface{} {
				if err != nil {
					return nil
				}
				return res
			}(),
			"error": err,
		})
	}()

	h := e.Request().Header
	signature := ""
	for i, v := range h {
		if strings.EqualFold(i, "scheduler-ngen-signature") && len(v) != 0 {
			signature = v[0]
			break
		}
	}

	// validation signature
	if signature != c.cfg.SchedulerNgenSignature() {
		err = shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Invalid signature",
			"id": "Signature tidak valid",
		}))
		return err
	}

	c.cargoV2UC.SchedulerUpdateEstimationTimeCargoNgenV2(selfCtx)

	resp := shared.JSONSuccess(`Success`, res)
	return e.JSON(http.StatusOK, resp)
}

func (c *CargoDeliveryV2) ForceUpdateDepArrCargoFlight(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "DeliveryCargo-ForceUpdateDepArrCargoFlight"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		form    = new(cargo_v2.ForceUpdateDepArrCargoRequest)
		res     interface{}
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": res, "error": err})
	}()

	if err = e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	if err = c.cargoV2UC.ForceUpdateDepArrCargoFlight(selfCtx, form); err != nil {
		return shared.HttpError(e, err)
	}

	res = map[string]bool{"success": true}

	resp := shared.JSONSuccess(`Success`, res)
	return e.JSON(resp.Code, resp)
}
