package v1

import (
	"crypto/rsa"
	"strconv"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/middleware"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/usecase"
	"github.com/Lionparcel/hydra/src/usecase/cbp_pickup"
	"github.com/labstack/echo"
)

// ClearanceDelivery ...
type PickupManifestCbpDelivery struct {
	pickupManifestCbp usecase.PickupManifestCbp
	cfg               *config.Config
}

// NewClearanceDelivery ...
func NewPickupManifestCbpDelivery(
	pickupManifestCbpUc usecase.PickupManifestCbp,
	cfg *config.Config,
) PickupManifestCbpDelivery {
	return PickupManifestCbpDelivery{
		pickupManifestCbp: pickupManifestCbpUc,
		cfg:               cfg,
	}
}

// Mount ...
func (c *PickupManifestCbpDelivery) Mount(group *echo.Group, publicKey *rsa.PublicKey) {
	group.GET("", c.ViewPickupManifestCbp, middleware.JWTVerify(publicKey, false, []string{}))
	group.GET("/manifest", c.Manifest, middleware.JWTVerify(publicKey, false, []string{}))
}

// ViewPickupManifestCbp ...
func (c *PickupManifestCbpDelivery) ViewPickupManifestCbp(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "PickupManifestCbpDelivery-ViewPickupManifestCbp"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	partnerID := e.Get("partnerID").(int)
	form := new(cbp_pickup.PickupManifestCBPParams)
	result := new(shared.Pagination)
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": result, "error": err})
	}()

	isTotalData := false
	isTotalDataParams := e.QueryParam("is_total_data")
	if isTotalDataParams != `` {
		isTotalData, err = strconv.ParseBool(isTotalDataParams)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid is total data",
				"id": "Total data tidak valid",
			})
			return shared.HttpError(e, err)
		}
	}

	isCod := false
	isCodParams := e.QueryParam("is_cod")
	if isCodParams != `` {
		isCod, err = strconv.ParseBool(isCodParams)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid is cod",
				"id": "Is Cod tidak valid",
			})
			return shared.HttpError(e, err)
		}
	}

	cache := false
	cacheParams := e.QueryParam("cache")
	if cacheParams != `` {
		cache, err = strconv.ParseBool(cacheParams)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid cache",
				"id": "Cache tidak valid",
			})
			return shared.HttpError(e, err)
		}
	}

	pmcClientId := 0
	pmcClientIdParams := e.QueryParam("pmc_client_id")
	if pmcClientIdParams != `` {
		pmcClientId, err = strconv.Atoi(pmcClientIdParams)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid pmcClientId",
				"id": "pmcClientId tidak valid",
			})
			return shared.HttpError(e, err)
		}
	}

	page := 0
	pageParams := e.QueryParam("page")
	if pageParams != `` {
		page, err = strconv.Atoi(pageParams)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid page",
				"id": "page tidak valid",
			})
			return shared.HttpError(e, err)
		}
	}

	limit := 0
	limitParams := e.QueryParam("limit")
	if limitParams != `` {
		limit, err = strconv.Atoi(limitParams)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid limit",
				"id": "limit tidak valid",
			})
			return shared.HttpError(e, err)
		}
	}

	form.PartnerID = partnerID
	form.IsTotalData = isTotalData
	form.IsCod = isCod
	form.Cache = cache
	form.PmcClientId = pmcClientId
	form.PmcStatus = e.QueryParam("pmc_status")
	form.Page = page
	form.Limit = limit

	result, err = c.pickupManifestCbp.ViewCbpPickup(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}

	resp := shared.JSONSuccess(`Success`, result)
	return e.JSON(resp.Code, resp)
}

// ViewPickupManifestCbp ...
func (c *PickupManifestCbpDelivery) Manifest(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "PickupManifestCbpDelivery-ViewPickupManifestCbp"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	partnerID := e.Get("partnerID").(int)
	token := e.Get("tokenStr").(string)
	form := new(cbp_pickup.PickupManifestCBPParams)
	var resp *shared.Response
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": resp, "error": err})
	}()

	isTotalData := false
	isTotalDataParams := e.QueryParam("is_total_data")
	if isTotalDataParams != `` {
		isTotalData, err = strconv.ParseBool(isTotalDataParams)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid is total data",
				"id": "Total data tidak valid",
			})
			return shared.HttpError(e, err)
		}
	}

	isCod := false
	isCodParams := e.QueryParam("is_cod")
	if isCodParams != `` {
		isCod, err = strconv.ParseBool(isCodParams)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid is cod",
				"id": "Is Cod tidak valid",
			})
			return shared.HttpError(e, err)
		}
	}

	cache := false
	cacheParams := e.QueryParam("cache")
	if cacheParams != `` {
		cache, err = strconv.ParseBool(cacheParams)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid cache",
				"id": "Cache tidak valid",
			})
			return shared.HttpError(e, err)
		}
	}

	pmcClientId := 0
	pmcClientIdParams := e.QueryParam("pmc_client_id")
	if pmcClientIdParams != `` {
		pmcClientId, err = strconv.Atoi(pmcClientIdParams)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid pmcClientId",
				"id": "pmcClientId tidak valid",
			})
			return shared.HttpError(e, err)
		}
	}

	form.Token = token
	form.PartnerID = partnerID
	form.IsTotalData = isTotalData
	form.IsCod = isCod
	form.Cache = cache
	form.PmcClientId = pmcClientId
	form.PmcStatus = e.QueryParam("pmc_status")

	data, summary, err := c.pickupManifestCbp.CbpPickupManifest(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}

	resp = shared.JSONSuccess(`Success`, data)
	resp.Summary = summary
	return e.JSON(resp.Code, resp)
}
