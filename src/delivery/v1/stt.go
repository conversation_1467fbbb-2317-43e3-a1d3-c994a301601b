package v1

import (
	"bytes"
	"context"
	"crypto/rsa"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/labstack/echo"

	"github.com/Lionparcel/hydra/config"
	"github.com/Lionparcel/hydra/middleware"
	"github.com/Lionparcel/hydra/shared"
	"github.com/Lionparcel/hydra/shared/logger"
	md "github.com/Lionparcel/hydra/shared/middleware"
	sharedMiddleware "github.com/Lionparcel/hydra/shared/middleware"
	"github.com/Lionparcel/hydra/shared/tracer"
	"github.com/Lionparcel/hydra/src/model"
	ucModules "github.com/Lionparcel/hydra/src/modules/cod_dfod_fraud_detection/risk_classification/usecase"
	"github.com/Lionparcel/hydra/src/usecase"
	"github.com/Lionparcel/hydra/src/usecase/gateway_stt"
	"github.com/Lionparcel/hydra/src/usecase/stt"
	"github.com/Lionparcel/hydra/src/usecase/stt_payment"
)

// SttDelivery ...
type SttDelivery struct {
	SttUc                usecase.Stt
	GatewaySttUc         usecase.GatewayStt
	cfg                  *config.Config
	SttPaymentUc         usecase.SttPayment
	DictionaryError      shared.DictionaryError
	RiskClassificationUc ucModules.RiskClassification
}

// NewSttDelivery ...
func NewSttDelivery(
	SttUc usecase.Stt,
	GatewaySttUc usecase.GatewayStt,
	cfg *config.Config,
	SttPaymentUc usecase.SttPayment,
	DictionaryError shared.DictionaryError,
	RiskClassificationUc ucModules.RiskClassification,
) SttDelivery {
	return SttDelivery{
		SttUc:                SttUc,
		GatewaySttUc:         GatewaySttUc,
		cfg:                  cfg,
		SttPaymentUc:         SttPaymentUc,
		DictionaryError:      DictionaryError,
		RiskClassificationUc: RiskClassificationUc,
	}
}

// Mount ...
func (c *SttDelivery) Mount(group *echo.Group, publicKey *rsa.PublicKey) {
	group.POST("/manual", c.CreateSTTManual, middleware.JWTVerify(publicKey, false, []string{}))
	group.POST("/shipment", c.CreateSTTShipmentID, middleware.JWTVerify(publicKey, false, []string{}))
	group.POST("/corporate", c.CreateSTTManualForClient, middleware.JWTVerify(publicKey, false, []string{`PARTNER`, `INTERNAL`, `CLIENT`}))
	group.GET("", c.ViewStt, middleware.JWTVerify(publicKey, false, []string{`PARTNER`, `INTERNAL`, `CLIENT`}))
	group.GET("/:id", c.DetailSTT, middleware.JWTVerify(publicKey, false, []string{}))
	group.GET("/resi/:id", c.DetailResi, middleware.JWTVerify(publicKey, false, []string{}))
	group.GET("/oldest", c.GetOldestSTT, middleware.JWTVerify(publicKey, false, []string{}))

	group.POST("/resi-send-email", c.ResiSendEmail, middleware.JWTVerify(publicKey, false, []string{`PARTNER`, `INTERNAL`}))
	group.GET("/mockery/shipment_generate", c.MockShipmentGenerate)
	group.GET("/shipment-algo/:id", c.DetailShipmentByID, middleware.JWTVerify(publicKey, false, []string{}))
	group.GET("/manifest", c.ViewSttManifest, middleware.JWTVerify(publicKey, false, []string{}))
	group.GET("/manifest/export", c.ExportSttManifest, middleware.JWTVerify(publicKey, false, []string{}))
	group.POST("/cancel", c.CancelStt, middleware.JWTVerify(publicKey, false, []string{}))
	group.PATCH("/:id", c.UpdateSTT, middleware.JWTVerify(publicKey, false, []string{`PARTNER`, `INTERNAL`, `CLIENT`}))
	group.POST("/validate/phone", c.ValidatePhoneNumber, middleware.JWTVerify(publicKey, false, []string{`PARTNER`, `INTERNAL`, `CLIENT`}))
	group.GET("/bulk", c.ViewBulkStt, middleware.JWTVerify(publicKey, false, []string{`PARTNER`, `INTERNAL`, `CLIENT`}))
	group.GET("/tracking", c.ViewSttTracking, middleware.JWTVerify(publicKey, false, []string{`PARTNER`, `INTERNAL`, `CLIENT`, `SYSTEM`, `CUSTOMER-SERVICE`}))
	group.GET("/shipment/prefix", c.ViewSttShipmentPrefix, middleware.JWTVerify(publicKey, false, []string{`PARTNER`, `INTERNAL`, `CLIENT`}))
	group.GET("/plain", c.ViewSttPlain, middleware.JWTVerify(publicKey, false, []string{}))
	group.GET("/manifest/checklist", c.ViewSttForManifest, middleware.JWTVerify(publicKey, false, []string{model.PARTNER, model.CLIENT}))
	group.GET("/manifest-bag", c.ViewSttManifestBag, middleware.JWTVerify(publicKey, false, []string{}))

	group.GET("/tracking/list", c.ViewSttTrackingList, middleware.JWTVerify(publicKey, false, []string{model.CLIENT}))

	// sender
	group.GET("/sender/tracking/list", c.ViewListSttSenderTracking, middleware.JWTVerify(publicKey, false, []string{`INTERNAL`}))

	// dtpol
	group.GET("/dtpol/points", c.ViewDTPOLStartEndPoints, middleware.JWTVerify(publicKey, false, []string{}))
	group.GET("/dtpol/check/arr", c.ViewCargoPlaneStatusARR, middleware.JWTVerify(publicKey, false, []string{}))

	// migration stt, delete this after migration
	group.POST("/migration_additional_data_stt", c.MigrationAdditionalDataStt, middleware.JWTVerify(publicKey, false, []string{}))

	// scheduler booking transaction
	group.GET("/transaction/booking/retry", c.BookingTransactionScheduler)
	group.GET("/transaction/booking/retry/manual", c.ManualBookingTransactionScheduler, middleware.JWTVerify(publicKey, false, []string{`INTERNAL`}))
	group.GET("/shipment/payment/retry", c.SttShipmentPaymentNotificationRehit, middleware.JWTVerify(publicKey, false, []string{`INTERNAL`}))

	// migration stt, delete this after migration
	group.POST("/payment/status", c.PaymentStatusValidation, middleware.JWTVerify(publicKey, false, []string{}))
	group.GET("/payment", c.PaymentStatus, middleware.JWTVerify(publicKey, false, []string{}))
	group.GET("/payment/download", c.DownloadPaymentStatus, middleware.JWTVerify(publicKey, false, []string{}))

	// manual insert retail tariff
	group.PUT("/retail-tariff/manual", c.ManualUpdateTariffRetailSTT, middleware.JWTVerify(publicKey, false, []string{`INTERNAL`}))

	group.POST("/corporate/cross-docking/failed", c.CreateSTTManualForTokopediaFailed, middleware.JWTVerifyGcpPubSub(c.cfg, false))
	// booking stt tokopedia
	group.POST("/corporate/cross-docking", c.CreateSTTBookingForTokopedia, middleware.JWTVerifyGcpPubSub(c.cfg, false))
	// booking stt non cross docking
	group.POST("/corporate/non-cross-docking", c.CreateSTTBookingNonCrossDocking, middleware.JWTVerifyGcpPubSub(c.cfg, false))

	group.POST("/corporate/non-cross-docking/failed", c.CreateLogNonCrossDockingFailed, middleware.JWTVerifyGcpPubSub(c.cfg, false))

	// rename no ref
	group.POST("/rename-no-ref", c.RenameNoRef, md.BasicAuth(md.NewConfig(c.cfg.GetBasicAuthUsername(), c.cfg.GetBasicAuthPassword())))

	// reverse destination
	group.PATCH("/reverse-destination/:id", c.ReverseDestination, middleware.JWTVerify(publicKey, false, []string{`INTERNAL`, `PARTNER`}))

	// dfod pasti
	group.POST("/dfod-pasti", c.GetDFODPasti, middleware.JWTVerify(publicKey, false, []string{}))

	group.POST("/validate", c.Validate, middleware.JWTVerify(publicKey, false, []string{`PARTNER`, `INTERNAL`, `CLIENT`}))

	group.POST("/corporate/cross-docking/retry", c.CorporateCrossDockingRetry, middleware.JWTVerify(publicKey, false, []string{`INTERNAL`}))

	group.GET("/stt-relabel/:code", c.GetSttRelabel, middleware.JWTVerify(publicKey, false, []string{`INTERNAL`}))

	group.GET("/phone_checker", c.CheckPhoneNumberSttDfod, middleware.JWTVerify(publicKey, false, []string{}))

	// stt resolution centre
	group.GET("/resolution-centre", c.GetDetailSttShipmentResolutionCentre, middleware.JWTVerify(publicKey, false, []string{}))
	
	group.GET("/adjustment/field", c.GetSttAdjustmentConfig, middleware.JWTVerify(publicKey, false, []string{`PARTNER`, `INTERNAL`, `CLIENT`}))
}

func (c *SttDelivery) PaymentStatusValidation(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "DeliveryStt-PaymentStatusValidation"

	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	form := new(stt.SttPaymentValidation)
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "error": err})
	}()

	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	err = c.SttPaymentUc.SttPaymentValidation(selfCtx, form.SttNumber)
	if err != nil {
		logger.E(err)
		return shared.HttpError(e, err)
	}

	response := shared.JSONResponse(http.StatusCreated, `Success`, true, nil)
	return e.JSON(response.Code, response)
}

// CreateSTTShipmentID ...
func (c *SttDelivery) CreateSTTShipmentID(e echo.Context) error {
	ctx := e.Request().Context()

	opName := "DeliveryStt-CreateSTTShipmentID"

	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	accountID := e.Get("accountID").(int64)
	token := e.Get("tokenStr").(string)
	accountType := e.Get("type").(string)
	accountName := e.Get("accountName").(string)
	form := new(stt.CreateSttShipmentRequest)
	formCreateSTT := new(stt.CreateSttRequest)
	sttRes := new(stt.CreateSttResponse)
	var err error
	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": formCreateSTT, "result": sttRes, "error": err})
	}()
	if err = e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	var (
		referenceName, referenceType string
		referenceID                  int
	)

	switch accountType {
	case model.PARTNER:
		referenceName = e.Get("partnerName").(string)
		referenceID = e.Get("partnerID").(int)
		referenceType = e.Get("partnerType").(string)
	case model.CLIENT:
		referenceName = e.Get("clientName").(string)
		referenceID = e.Get("clientID").(int)
	}

	formCreateSTT = &stt.CreateSttRequest{
		Stt: []stt.Stt{
			form.Stt,
		},
		AccountID:            accountID,
		Token:                token,
		Source:               model.ALGO,
		AccountType:          accountType,
		AccountRefName:       referenceName,
		AccountRefType:       referenceType,
		AccountRefID:         referenceID,
		AccountName:          accountName,
		ShipmentPackageID:    form.ShipmentPackageID,
		ElexysTariff:         form.ElexysTariff,
		SttWeightAttachFiles: form.SttWeightAttachFiles,
		GoodsNames:           form.GoodsNames,
	}

	sttRes, err = c.SttUc.CreateSTT(selfCtx, formCreateSTT)
	if err != nil {
		logger.E(err)
		return shared.HttpError(e, err)
	}

	response := shared.JSONResponse(http.StatusCreated, `STT Booking By Shipment successfully created`, true, sttRes)
	return e.JSON(response.Code, response)
}

// CreateSTTManual ...
func (c *SttDelivery) CreateSTTManual(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "DeliveryStt-CreateSTTManual"

	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()

	accountID := e.Get("accountID").(int64)
	token := e.Get("tokenStr").(string)
	accountType := e.Get("type").(string)
	accountName := e.Get("accountName").(string)

	form := new(stt.CreateSttManualRequest)
	formCreateSTT := new(stt.CreateSttRequest)
	sttRes := new(stt.CreateSttResponse)
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": formCreateSTT, "result": sttRes, "error": err})
	}()
	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	var (
		referenceName, referenceType string
		referenceID                  int
	)

	switch accountType {
	case model.PARTNER:
		referenceName = e.Get("partnerName").(string)
		referenceID = e.Get("partnerID").(int)
		referenceType = e.Get("partnerType").(string)
	case model.CLIENT:
		referenceName = e.Get("clientName").(string)
		referenceID = e.Get("clientID").(int)
	}

	formCreateSTT = &stt.CreateSttRequest{
		Stt: []stt.Stt{
			form.Stt,
		},
		AccountID:             accountID,
		Token:                 token,
		Source:                model.MANUAL,
		AccountType:           accountType,
		AccountRefName:        referenceName,
		AccountRefType:        referenceType,
		AccountRefID:          referenceID,
		AccountName:           accountName,
		ElexysTariff:          form.ElexysTariff,
		SttWeightAttachFiles:  form.SttWeightAttachFiles,
		BulkID:                form.BulkID,
		BulkSTTRowNumber:      form.BulkSTTRowNumber,
		SaveCustomerSender:    form.SaveCustomerSender,
		SaveCustomerRecipient: form.SaveCustomerRecipient,
	}

	if form.Stt.SttIsCOD && accountType != model.CLIENT {
		if !form.Stt.SttIsDFOD && form.Stt.SttCODAmount < 1 {
			return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt COD Amount cannot be empty if Is COD",
				"id": "Stt COD Amount tidak boleh kosong jika Is COD",
			}))
		}

		if !form.Stt.SttIsDFOD && form.Stt.SttGoodsEstimatePrice != form.Stt.SttCODAmount {
			return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
				"en": "Stt COD Fee cannot be less than 0 or Stt COD Amount is not the same as Stt Goods Price",
				"id": "Stt COD Fee tidak boleh kurang dari 0 atau Stt COD Amount tidak sama dengan Stt Goods Price",
			}))
		}
	}

	sttRes, err = c.SttUc.CreateSTT(selfCtx, formCreateSTT)
	if err != nil {
		logger.E(err)
		return shared.HttpError(e, err)
	}

	response := shared.JSONResponse(http.StatusCreated, `STT Manual Booking successfully created`, true, sttRes)
	return e.JSON(response.Code, response)
}

// ViewStt ...
func (c *SttDelivery) ViewStt(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "SttDelivery-ViewStt"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	request := stt.ViewSttRequest{}
	pagination := new(shared.Pagination)
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": request, "result": pagination, "error": err})
	}()

	token := e.Get("tokenStr").(string)
	pageParam := e.QueryParam("page")
	limitParam := e.QueryParam("limit")
	isCodParam := e.QueryParam("is_cod")

	var (
		page  int  = 1
		limit int  = 10
		isCod bool = false
	)

	if pageParam != `` {
		page, err = strconv.Atoi(pageParam)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Please fill valid page query",
				"id": "Isi page parameter",
			})
			return shared.HttpError(e, err)
		}
	}

	if limitParam != `` {
		limit, err = strconv.Atoi(limitParam)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Please fill valid limit query",
				"id": "Isi limit parameter",
			})
			return shared.HttpError(e, err)
		}
	}

	if isCodParam == `true` {
		isCod = true
	}

	request = stt.ViewSttRequest{
		Search:                e.QueryParam("search"),
		SearchLike:            e.QueryParam("search_like"),
		BookedStart:           e.QueryParam("booked_start"),
		BookedEnd:             e.QueryParam("booked_end"),
		OriginDistrictID:      e.QueryParam("origin_district_id"),
		DestinationDistrictID: e.QueryParam("destination_district_id"),
		OriginCityID:          e.QueryParam("origin_city_id"),
		DestinationCityID:     e.QueryParam("destination_city_id"),
		ClientPartnerID:       e.QueryParam("client_partner_id"),
		ProductType:           e.QueryParam("product_type"),
		Status:                e.QueryParam("status"),
		IsCod:                 isCod,
		InsuranceType:         e.QueryParam("insurance_type"),
		Token:                 token,
		Page:                  page,
		Limit:                 limit,
		MinPayDate:            e.QueryParam("min_pay_date"),
		MaxPayDate:            e.QueryParam("max_pay_date"),
		PaymentStatus:         e.QueryParam("payment_status"),
	}

	pagination, err = c.SttUc.ViewStt(selfCtx, request)
	if err != nil {
		return shared.HttpError(e, err)
	}
	resp := shared.JSONSuccess(`Success`, pagination)
	return e.JSON(resp.Code, resp)
}

func (c *SttDelivery) ViewSttTracking(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "SttDelivery-ViewSttTracking"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	form := new(stt.SttDetailTrackingRequest)
	res := new(stt.SttDetailTrackingResponse)
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": res, "error": err})
	}()

	accountType := e.Get("type").(string)
	token := e.Get("tokenStr").(string)
	sttNo := e.QueryParam("q")
	showHiddenStatus := e.QueryParam("show_hidden_status")
	typeResponse := e.QueryParam("type")
	isEn, _ := strconv.ParseBool(e.QueryParam("is_en"))

	referenceType := ""
	referenceID := 0

	switch accountType {
	case model.PARTNER:
		referenceID = e.Get("partnerID").(int)
		referenceType = e.Get("partnerType").(string)
	case model.CLIENT:
		referenceID = e.Get("clientID").(int)
		referenceType = model.CLIENT
	case model.INTERNAL, model.CUSTOMERSERVICE:
		referenceType = model.INTERNAL
		referenceID = int(e.Get("accountID").(int64))
	}

	form = &stt.SttDetailTrackingRequest{
		SttNo:            sttNo,
		ShowHiddenStatus: showHiddenStatus,
		ReferenceID:      referenceID,
		ReferenceType:    referenceType,
		ResponseType:     typeResponse,
		IsEn:             isEn,
		Token:            token,
	}
	res, err = c.SttUc.DetailSttTracking(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}

	if form.ResponseType == model.External {
		resp := shared.JSONSuccess(`Success`, res.ConvertToTypeExternal())
		return e.JSON(resp.Code, resp)
	}

	resp := shared.JSONSuccess(`Success`, res)
	return e.JSON(resp.Code, resp)
}

// DetailSTT ...
func (c *SttDelivery) DetailSTT(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "SttDelivery-DetailSTT"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	form := new(stt.SttDetailRequest)
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": nil, "error": err})
	}()

	accountType := e.Get("type").(string)
	getID := e.Param("id")
	sttID, err := strconv.Atoi(getID)
	if sttID == 0 || err != nil {
		return shared.HttpError(e, c.DictionaryError.MalformatRequest())
	}

	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, c.DictionaryError.MalformatRequest())
	}

	referenceType := ""
	referenceID := 0
	switch accountType {
	case model.PARTNER:
		referenceID = e.Get("partnerID").(int)
		referenceType = e.Get("partnerType").(string)
	case model.CLIENT:
		referenceID = e.Get("clientID").(int)
		referenceType = model.CLIENT
	}

	accountRoleName := e.Get("accountRoleName").(string)

	form.SttID = sttID
	form.AccountType = accountType
	form.AccountRefID = referenceID
	form.AccountRefType = referenceType
	form.AccountRoleName = accountRoleName
	form.Token = e.Get("tokenStr").(string)
	res, err := c.SttUc.DetailStt(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}
	resp := shared.JSONSuccess(`Detail STT successfully retrieved`, res)
	return e.JSON(resp.Code, resp)
}

// DetailResi ...
func (c *SttDelivery) DetailResi(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "SttDelivery-DetailResi"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
		form    = stt.DetailResiRequest{}
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": nil, "error": err})
	}()

	getID := e.Param("id")
	sttID, err := strconv.Atoi(getID)
	accountType := e.Get("type").(string)
	if err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	referenceType := ""
	referenceID := 0
	switch accountType {
	case model.PARTNER:
		referenceID = e.Get("partnerID").(int)
		referenceType = e.Get("partnerType").(string)
	case model.CLIENT:
		referenceID = e.Get("clientID").(int)
		referenceType = model.CLIENT
	case model.INTERNAL, model.CUSTOMERSERVICE:
		referenceID = 0
		referenceType = model.INTERNAL
	}

	selfCtx = context.WithValue(selfCtx, `tokenStr`, e.Get("tokenStr"))
	form = stt.DetailResiRequest{
		ID:             sttID,
		AccountType:    e.QueryParam("account_type"),
		Token:          e.Get("tokenStr").(string),
		AccountRefID:   referenceID,
		AccountRefType: referenceType,
	}

	res, err := c.SttUc.DetailResi(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}
	resp := shared.JSONSuccess(`Detail Resi successfully retrieved`, res)
	return e.JSON(resp.Code, resp)
}

// ResiSendEmail ...
func (c *SttDelivery) ResiSendEmail(e echo.Context) error {
	var (
		ctx         = e.Request().Context()
		opName      = "DeliveryStt-ResiSendEmail"
		trace       = tracer.StartTrace(ctx, opName)
		selfCtx     = trace.Context()
		accountType = e.Get("type").(string)
		now         = time.Now()
		form        = new(stt.ResiSendEmailRequest)
		err         error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": nil, "error": err})
	}()

	if err = e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	switch accountType {
	case model.PARTNER:
		form.AccountRefID = e.Get("partnerID").(int)
		form.AccountRefType = e.Get("partnerType").(string)
	case model.CLIENT:
		form.AccountRefID = e.Get("clientID").(int)
		form.AccountRefType = model.CLIENT
	case model.INTERNAL:
		form.AccountRefID = 0
		form.AccountRefType = model.INTERNAL
	}

	if form.IsWithAttachment {
		file, tmpFile, err := e.Request().FormFile("attachment")
		if err != nil {
			err = shared.HttpError(e, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "File attachment failed to open",
				"id": "Gagal membuka attachment file",
			}))
			return err
		}
		defer file.Close()

		// ext file validation
		arrExtFile := strings.Split(tmpFile.Filename, ".")
		extFile := arrExtFile[len(arrExtFile)-1]
		if len(arrExtFile) < 2 {
			return shared.HttpError(e, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "File format type is not allowed",
				"id": "Tipe format file tidak diperbolehkan",
			}))
		}
		if !stt.IsAllowedAttachmentFileType[extFile] {
			return shared.HttpError(e, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "File format type is not allowed",
				"id": "Tipe format file tidak diperbolehkan",
			}))
		}

		content := new(bytes.Buffer)
		_, err = io.Copy(content, file)
		if err != nil {
			err = shared.HttpError(e, shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "File attachment failed to open",
				"id": "Gagal membuka attachment file",
			}))
			logger.E(err)
			return err
		}

		form.Attachment = content.Bytes()
	}

	form.Token = e.Get("tokenStr").(string)
	form.Date = now
	form.CurrentYear = now.Year()

	err = c.SttUc.ResiSendEmail(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}
	resp := shared.JSONSuccess(`Resi successfully sent to email`, nil)
	return e.JSON(resp.Code, resp)
}

// MockShipmentGenerate ...
func (c *SttDelivery) MockShipmentGenerate(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "SttDelivery-MockShipmentGenerate"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
		prefix  = e.QueryParam("prefix")
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": prefix, "result": nil, "error": err})
	}()

	shipmentID, err := c.SttUc.MockeryShipmentGenerate(selfCtx, prefix)
	if err != nil {
		return shared.HttpError(e, err)
	}
	resp := shared.JSONSuccess(`Mock Shipment ID Generated`, shipmentID)
	return e.JSON(resp.Code, resp)
}

// DetailShipmentByID ...
func (c *SttDelivery) DetailShipmentByID(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "DeliveryStt-DetailShipmentByID"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()

		request        = new(stt.ViewDetailShipmentAlgoRequest)
		res            *shared.Pagination
		shipmentAlgoID = e.Param("id")
		accountID      = e.Get("accountID").(int64)
		accountType    = e.Get("type").(string)
		err            error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": request, "result": res, "error": err})
	}()

	if err = e.Bind(request); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	partnerID := 0
	partnerType := ``

	if accountType == model.PARTNER {
		partnerID = e.Get("partnerID").(int)
		partnerType = e.Get("partnerType").(string)
	}

	selfCtx = context.WithValue(selfCtx, `tokenStr`, e.Get("tokenStr"))
	request.ShipmentID = shipmentAlgoID
	request.AccountID = int(accountID)
	request.AccountType = accountType
	request.PartnerID = partnerID
	request.PartnerType = partnerType

	res, err = c.SttUc.DetailShipmentAlgoByID(selfCtx, request)
	if err != nil {
		return shared.HttpError(e, err)
	}
	resp := shared.JSONSuccess(`Success`, res)
	return e.JSON(resp.Code, resp)
}

// ViewSttManifest ...
func (c *SttDelivery) ViewSttManifest(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "SttDelivery-ViewSttManifest"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
		request = stt.ViewSttManifestRequest{}
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": request, "result": nil, "error": err})
	}()

	token := e.Get("tokenStr").(string)
	typeUser := e.Get("type").(string)
	partnerID := e.Get("partnerID").(int)
	partnerType := e.Get("partnerType").(string)
	clientID := e.Get("clientID").(int)
	pageParam := e.QueryParam("page")
	limitParam := e.QueryParam("limit")
	bookedByParam := e.QueryParam("booked_by")
	bookedTypeParam := e.QueryParam("booked_type")
	var (
		page          int = 1
		limit         int = 50
		bookedBy      int = 0
		costumerPhone string
	)

	if pageParam != `` {
		page, err = strconv.Atoi(pageParam)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Please fill valid page query",
				"id": "Isi page parameter",
			})
			return shared.HttpError(e, err)
		}
	}

	if limitParam != `` {
		limit, err = strconv.Atoi(limitParam)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Please fill valid limit query",
				"id": "Isi limit parameter",
			})
			return shared.HttpError(e, err)
		}
	}

	if bookedByParam != `` {
		if strings.ToLower(bookedTypeParam) != model.COSTUMER {
			bookedBy, err = strconv.Atoi(bookedByParam)
			if err != nil {
				err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
					"en": "Please fill valid booked by",
					"id": "Isi dengan valid booked by",
				})
				return shared.HttpError(e, err)
			}
		} else {
			costumerPhone = bookedByParam
		}
	}

	request = stt.ViewSttManifestRequest{
		Search:            e.QueryParam("search"),
		UserType:          e.QueryParam("user_type"),
		BookedBy:          bookedBy,
		BookedType:        bookedTypeParam,
		FromDate:          e.QueryParam("from_date"),
		EndDate:           e.QueryParam("end_date"),
		SttNumber:         e.QueryParam("stt_number"),
		DestinationCityID: e.QueryParam("destination_city_id"),
		CostumerPhone:     costumerPhone,
		Token:             token,
		Type:              typeUser,
		PartnerID:         partnerID,
		PartnerType:       partnerType,
		ClientID:          clientID,
		Page:              page,
		Limit:             limit,
	}

	pagination, err := c.SttUc.ViewSttManifest(selfCtx, request)
	if err != nil {
		return shared.HttpError(e, err)
	}
	resp := shared.JSONSuccess(`Success`, pagination)
	return e.JSON(resp.Code, resp)
}

// ExportSttManifest ...
func (c *SttDelivery) ExportSttManifest(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "SttDelivery-ExportSttManifest"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		request = &stt.ViewSttManifestRequest{}
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": request, "result": nil, "error": err})
	}()

	token := e.Get("tokenStr").(string)
	typeUser := e.Get("type").(string)
	partnerID := e.Get("partnerID").(int)
	partnerType := e.Get("partnerType").(string)
	clientID := e.Get("clientID").(int)
	bookedByParam := e.QueryParam("booked_by")
	bookedTypeParam := e.QueryParam("booked_type")
	var (
		bookedBy      int
		costumerPhone string
	)

	if bookedByParam != `` {
		if strings.ToLower(bookedTypeParam) != model.COSTUMER {
			bookedBy, err = strconv.Atoi(bookedByParam)
			if err != nil {
				err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
					"en": "Please fill valid booked by",
					"id": "Isi dengan valid booked by",
				})
				return shared.HttpError(e, err)
			}
		} else {
			costumerPhone = bookedByParam
		}
	}

	destinationCityId := ""
	if e.QueryParam("destination_city_id") != "all" {
		destinationCityId = e.QueryParam("destination_city_id")
	}

	request = &stt.ViewSttManifestRequest{
		Search:            e.QueryParam("search"),
		UserType:          e.QueryParam("user_type"),
		BookedBy:          bookedBy,
		BookedType:        bookedTypeParam,
		FromDate:          e.QueryParam("from_date"),
		EndDate:           e.QueryParam("end_date"),
		SttNumber:         e.QueryParam("stt_number"),
		DestinationCityID: destinationCityId,
		CostumerPhone:     costumerPhone,
		Token:             token,
		Type:              typeUser,
		PartnerID:         partnerID,
		PartnerType:       partnerType,
		ClientID:          clientID,
	}
	err = c.SttUc.ExportSttManifest(selfCtx, request, e)
	if err != nil {
		return shared.HttpError(e, err)
	}

	return nil
}

// CancelStt ...
func (c *SttDelivery) CancelStt(e echo.Context) error {
	opName := "DeliveryStt-CancelStt"
	ctx := e.Request().Context()
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error
	res := new(stt.ResponseCancelStt)

	accountID := e.Get("accountID").(int64)
	token := e.Get("tokenStr").(string)
	accountType := e.Get("type").(string)
	accountName := e.Get("accountName").(string)
	form := new(stt.RequestCancelStt)
	accountRoleName := e.Get("accountRoleName").(string)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": res, "error": err})
	}()

	if err = e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	var (
		referenceName, referenceType string
		referenceID                  int
	)

	switch accountType {
	case model.PARTNER:
		referenceName = e.Get("partnerName").(string)
		referenceID = e.Get("partnerID").(int)
		referenceType = e.Get("partnerType").(string)
	case model.CLIENT:
		referenceName = e.Get("clientName").(string)
		referenceID = e.Get("clientID").(int)
		referenceType = model.CLIENT
	case model.INTERNAL:
		referenceName = model.INTERNAL
		referenceType = model.INTERNAL
	}

	form.AccountID = accountID
	form.Token = token
	form.AccountType = accountType
	form.AccountRefName = referenceName
	form.AccountRefType = referenceType
	form.AccountRefID = referenceID
	form.AccountName = accountName
	form.AccountRoleName = accountRoleName

	res, err = c.SttUc.CancelStt(selfCtx, form)
	if err != nil {
		logger.E(err)
		return shared.HttpError(e, err)
	}

	response := shared.JSONResponse(http.StatusOK, `Cancel STT Booking successfully`, true, res)
	return e.JSON(response.Code, response)
}

// ValidatePhoneNumber ...
func (c *SttDelivery) ValidatePhoneNumber(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "SttDelivery-ValidatePhoneNumber"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		form    = new(stt.ValidatePhoneNumberRequest)
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": nil, "error": err})
	}()

	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	res, err := c.SttUc.ValidatePhoneNumber(selfCtx, form)
	if err != nil {
		logger.E(err)
		return shared.HttpError(e, err)
	}

	response := shared.JSONResponse(http.StatusOK, `Validate phone number successfully retrieved`, true, res)
	return e.JSON(response.Code, response)
}

// ViewBulkStt ...
func (c *SttDelivery) ViewBulkStt(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "SttDelivery-ViewBulkStt"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		request = stt.ViewBulkSttRequest{}
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": request, "result": nil, "error": err})
	}()

	token := e.Get("tokenStr").(string)
	accountType := e.Get("type").(string)
	clientID := e.Get("clientID").(int)

	request = stt.ViewBulkSttRequest{
		SttIDArray:  e.QueryParam("stt_id"),
		AccountType: accountType,
		ClientID:    clientID,
		Token:       token,
	}

	result, err := c.SttUc.ViewBulkStt(selfCtx, request)
	if err != nil {
		return shared.HttpError(e, err)
	}
	resp := shared.JSONSuccess(`Success`, result)
	return e.JSON(resp.Code, resp)
}

func (c *SttDelivery) ViewSttShipmentPrefix(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "SttDelivery-ViewSttShipmentPrefix"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		res     = []stt.ViewSttShipmentPrefixResponse{}
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": res, "error": err})
	}()

	res = c.SttUc.ViewSttShipmentPrefix(selfCtx)

	resp := shared.JSONSuccess(`Success`, res)
	return e.JSON(resp.Code, resp)
}

// ViewSttPlain ...
func (c *SttDelivery) ViewSttPlain(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "SttDelivery-ViewSttPlain"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		form    = new(stt.ViewSttPlainRequest)
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": nil, "error": err})
	}()
	token := e.Get("tokenStr").(string)

	if err = e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	form.Token = token
	pagination, err := c.SttUc.ViewSttPlain(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}
	resp := shared.JSONSuccess(`Success`, pagination)
	return e.JSON(resp.Code, resp)
}

// MigrationAdditionalDataStt ...
func (c *SttDelivery) MigrationAdditionalDataStt(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "SttDelivery-MigrationAdditionalDataStt"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": nil, "error": err})
	}()

	err = c.SttUc.MigrateAdditionalData(selfCtx)
	if err != nil {
		return shared.HttpError(e, err)
	}
	resp := shared.JSONSuccess(`Success`, ``)
	return e.JSON(resp.Code, resp)
}

// ViewSttForManifest ...
func (c *SttDelivery) ViewSttForManifest(e echo.Context) error {
	var (
		ctx         = e.Request().Context()
		opName      = "DeliveryStt-ViewSttForManifest"
		trace       = tracer.StartTrace(ctx, opName)
		selfCtx     = trace.Context()
		accountType = e.Get("type").(string)
		form        = new(stt.ViewSttForManifestRequest)
		pagination  = new(shared.Pagination)
		err         error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": pagination, "error": err})
	}()

	if err = e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	form.Token = e.Get("tokenStr").(string)

	switch accountType {
	case model.PARTNER:
		form.AccountReffID = e.Get("partnerID").(int)
		form.AccountReffType = e.Get("partnerType").(string)
	case model.CLIENT:
		form.AccountReffID = e.Get("clientID").(int)
		form.AccountReffType = model.CLIENT
	}

	pagination, err = c.SttUc.ViewSttForManifest(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}

	resp := shared.JSONSuccess(`Success`, pagination)
	return e.JSON(resp.Code, resp)
}

// ViewDTPOLStartEndPoints ...
func (c *SttDelivery) ViewDTPOLStartEndPoints(e echo.Context) error {
	ctx := e.Request().Context()

	opName := "DeliveryStt-ViewDTPOLStartEndPoints"

	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	token := e.Get("tokenStr").(string)
	form := new(stt.ViewDTPOLStartEndPointsRequest)
	res := new(stt.ViewDTPOLStartEndPointsResponse)
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": res, "error": err})
	}()
	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}
	form.Token = token
	res, err = c.SttUc.ViewDTPOLStartEndPointsV2(selfCtx, form)
	if err != nil {
		logger.E(err)
		return shared.HttpError(e, err)
	}

	response := shared.JSONResponse(http.StatusOK, `Success`, true, res)
	return e.JSON(response.Code, response)
}

// BookingTransactionScheduler ...
func (c *SttDelivery) BookingTransactionScheduler(e echo.Context) error {
	ctx := e.Request().Context()

	opName := "DeliveryStt-BookingTransactionScheduler"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": nil, "error": err})
	}()

	if !sharedMiddleware.ValidateAuth(e, &sharedMiddleware.Validate{
		HeaderKey: sharedMiddleware.HeaderSecretKeyCloudScheduler,
		SecretKey: c.cfg.CloudSchedulerAuth(),
	}) {
		err = shared.HttpError(e, shared.NewMultiStringUnauthorizedError(shared.HTTPErrorUnauthorized, map[string]string{
			"en": "Unauthorized",
			"id": "Unauthorized",
		}))
		return err
	}

	go func() {
		opName := "DeliveryStt-goBookingTransactionScheduler"
		trace := tracer.StartTrace(context.Background(), opName)
		selfCtx := trace.Context()
		var err error

		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": nil, "result": nil, "error": err})
		}()

		err = c.GatewaySttUc.BookingTransactionScheduler(selfCtx, &gateway_stt.BookingTransactionSchedulerRequest{})
	}()

	response := shared.JSONResponse(http.StatusOK, `Success`, true, nil)
	return e.JSON(response.Code, response)
}

// ManualBookingTransactionScheduler ...
func (c *SttDelivery) ManualBookingTransactionScheduler(e echo.Context) error {
	ctx := e.Request().Context()

	opName := "DeliveryStt-ManualBookingTransactionScheduler"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": nil, "result": nil, "error": err})
	}()

	go func() {
		opName := "DeliveryStt-goManualBookingTransactionScheduler"
		trace := tracer.StartTrace(context.Background(), opName)
		selfCtx := trace.Context()
		var err error

		defer func() {
			if r := recover(); r != nil {
				msg := tracer.IdentifyPanic(opName, r)
				tracer.Log(selfCtx, "panic_recovered", msg)
			}
			trace.Finish(map[string]interface{}{"param": nil, "result": nil, "error": err})
		}()

		err = c.GatewaySttUc.BookingTransactionScheduler(selfCtx, &gateway_stt.BookingTransactionSchedulerRequest{
			Date: e.QueryParam("date"),
		})
	}()

	response := shared.JSONResponse(http.StatusOK, `Success`, true, nil)
	return e.JSON(response.Code, response)
}

// ViewCargoPlaneStatusARR ...
func (c *SttDelivery) ViewCargoPlaneStatusARR(e echo.Context) error {
	ctx := e.Request().Context()

	opName := "DeliveryStt-ViewCargoPlaneStatusARR"

	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	token := e.Get("tokenStr").(string)
	form := new(stt.ViewCargoPlaneStatusARRRequest)
	res := new(stt.ViewCargoPlaneStatusARRResponse)
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": res, "error": err})
	}()
	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}
	form.Token = token
	res, err = c.SttUc.ViewCargoPlaneStatusARR(selfCtx, form)
	if err != nil {
		logger.E(err)
		return shared.HttpError(e, err)
	}

	response := shared.JSONResponse(http.StatusOK, `Success`, true, res)
	return e.JSON(response.Code, response)
}

// SttShipmentPaymentNotificationRehit ...
func (c *SttDelivery) SttShipmentPaymentNotificationRehit(e echo.Context) error {
	ctx := e.Request().Context()

	opName := "DeliveryStt-SttShipmentPaymentNotificationRehit"

	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	form := new(gateway_stt.SttShipmentPaymentNotificationManualMessageRequest)
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": nil, "error": err})
	}()
	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	err = c.GatewaySttUc.SttShipmentPaymentNotificationRehit(selfCtx, form)
	if err != nil {
		logger.E(err)
		return shared.HttpError(e, err)
	}

	response := shared.JSONResponse(http.StatusOK, `Success`, true, nil)
	return e.JSON(response.Code, response)
}

// ManualUpdateTariffRetailSTT ...
func (c *SttDelivery) ManualUpdateTariffRetailSTT(e echo.Context) error {
	ctx := e.Request().Context()

	opName := "DeliveryStt-ManualUpdateTariffRetailSTT"

	trace := tracer.StartTrace(ctx, opName)
	token := e.Get("tokenStr").(string)
	selfCtx := trace.Context()
	form := new(stt.RequestManualUpdateCalculateTariff)
	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": nil, "error": err})
	}()
	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	form.Token = token
	err = c.SttUc.ManualUpdateTariffRetailSTT(selfCtx, form)
	if err != nil {
		logger.E(err)
		return shared.HttpError(e, err)
	}

	response := shared.JSONResponse(http.StatusOK, `Success`, true, nil)
	return e.JSON(response.Code, response)
}

func (c *SttDelivery) PaymentStatus(e echo.Context) error {
	var (
		ctx        = e.Request().Context()
		opName     = "DeliveryStt-PaymentStatus"
		trace      = tracer.StartTrace(ctx, opName)
		selfCtx    = trace.Context()
		form       = new(stt_payment.ViewSttPaymentRequest)
		pagination = new(shared.Pagination)
		err        error

		token             = e.Get("tokenStr").(string)
		pageParam         = e.QueryParam("page")
		limitParam        = e.QueryParam("limit")
		isCodParam        = e.QueryParam("is_cod")
		isPaidUnpaidParam = e.QueryParam("is_paid_unpaid")

		page                             int = 1
		limit                            int = 10
		isCod, isTotalData, isPaidUnpaid bool
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "error": err})
	}()

	if pageParam != `` {
		page, err = strconv.Atoi(pageParam)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Please fill valid page query",
				"id": "Isi page parameter",
			})
			return shared.HttpError(e, err)
		}
	}

	if limitParam != `` {
		limit, err = strconv.Atoi(limitParam)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Please fill valid limit query",
				"id": "Isi limit parameter",
			})
			return shared.HttpError(e, err)
		}
	}

	if isPaidUnpaidParam == `true` {
		isPaidUnpaid = true
	}

	if isCodParam == `true` {
		isCod = true
	}

	isTotalDataParams := e.QueryParam("is_total_data")
	if isTotalDataParams != `` {
		isTotalData, err = strconv.ParseBool(isTotalDataParams)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Invalid is total data",
				"id": "Total data tidak valid",
			})
			return shared.HttpError(e, err)
		}
	}

	form = &stt_payment.ViewSttPaymentRequest{
		Search:                e.QueryParam("search"),
		SearchLike:            e.QueryParam("search_like"),
		BookedStart:           e.QueryParam("booked_start"),
		BookedEnd:             e.QueryParam("booked_end"),
		OriginDistrictID:      e.QueryParam("origin_district_id"),
		DestinationDistrictID: e.QueryParam("destination_district_id"),
		OriginCityID:          e.QueryParam("origin_city_id"),
		DestinationCityID:     e.QueryParam("destination_city_id"),
		ClientPartnerID:       e.QueryParam("client_partner_id"),
		ProductType:           e.QueryParam("product_type"),
		Status:                e.QueryParam("status"),
		IsCod:                 isCod,
		InsuranceType:         e.QueryParam("insurance_type"),
		Token:                 token,
		Page:                  page,
		Limit:                 limit,
		IsTotalData:           isTotalData,
		MinPayDate:            e.QueryParam("min_pay_date"),
		MaxPayDate:            e.QueryParam("max_pay_date"),
		PaymentStatus:         e.QueryParam("payment_status"),
		OrderBy:               e.QueryParam("order_by"),
		SortBy:                e.QueryParam("sort_by"),
		IsPaidUnpaid:          isPaidUnpaid,
		ProcessState:          e.QueryParam("process_state"),
		StartUpdated:          e.QueryParam("start_updated"),
		EndUpdated:            e.QueryParam("end_updated"),
	}

	pagination, err = c.SttPaymentUc.ViewSttPayment(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}

	resp := shared.JSONSuccess(`Success`, pagination)
	return e.JSON(resp.Code, resp)
}

func (c *SttDelivery) DownloadPaymentStatus(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "DeliveryStt-DownloadPaymentStatus"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		form    = new(stt_payment.ViewSttPaymentRequest)
		err     error

		token      = e.Get("tokenStr").(string)
		pageParam  = e.QueryParam("page")
		limitParam = e.QueryParam("limit")

		page  int = 1
		limit int = 10
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "error": err})
	}()

	if pageParam != `` {
		page, err = strconv.Atoi(pageParam)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Please fill valid page query",
				"id": "Isi page parameter",
			})
			return shared.HttpError(e, err)
		}
	}

	if limitParam != `` {
		limit, err = strconv.Atoi(limitParam)
		if err != nil {
			err := shared.NewMultiStringValidationError(shared.HTTPErrorUnprocessableEntity, map[string]string{
				"en": "Please fill valid limit query",
				"id": "Isi limit parameter",
			})
			return shared.HttpError(e, err)
		}
	}

	err = c.SttPaymentUc.DownloadSttPayment(selfCtx, stt_payment.ViewDownloadSttPaymentRequest{
		Token:             token,
		SearchSttNo:       e.QueryParam("search"),
		StatusPayment:     e.QueryParam("payment_status"),
		ProcessStatus:     e.QueryParam("process_state"),
		LastSttStatus:     e.QueryParam("status"),
		StartSttUpdatedAt: e.QueryParam("start_updated"),
		EndSttUpdatedAt:   e.QueryParam("end_updated"),
		OrderBy:           e.QueryParam("order_by"),
		SortBy:            e.QueryParam("sort_by"),
		OriginCityID:      e.QueryParam("origin_city_id"),
		DestinationCityID: e.QueryParam("destination_city_id"),
		Page:              page,
		Limit:             limit,
	})
	if err != nil {
		return shared.HttpError(e, err)
	}

	resp := shared.JSONSuccess(`Success`, nil)
	return e.JSON(resp.Code, resp)
}

func (c *SttDelivery) ViewListSttSenderTracking(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "DeliveryStt-ViewListSttSenderTracking"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		form    = new(stt.ViewSttTrackingRequest)
		res     = new(shared.Pagination)
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": res, "error": err})
	}()

	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	form.AccountType = e.Get("type").(string)
	form.Username = e.Get("username").(string)
	form.Token = e.Get("tokenStr").(string)

	pagination, err := c.SttUc.ViewSttSenderTracking(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}

	resp := shared.JSONSuccess(`Success`, pagination)
	return e.JSON(resp.Code, resp)
}

func (c *SttDelivery) ViewSttTrackingList(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "DeliveryStt-ViewSttShipmentList"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		form    = new(stt.ViewSttTrackingListRequest)
		res     = new(shared.Pagination)
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": res, "error": err})
	}()

	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	form.Token = e.Get(`tokenStr`).(string)
	form.AccountRefType = e.Get("type").(string)

	switch form.AccountRefType {
	case model.CLIENT:
		form.AccountRefID = e.Get("clientID").(int)
	}

	pagination, err := c.SttUc.ViewSttTrackingList(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}

	resp := shared.JSONSuccess(`Success`, pagination)
	return e.JSON(resp.Code, resp)
}

// CreateSTTManualForTokopediaFailed ...
func (c *SttDelivery) CreateSTTManualForTokopediaFailed(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "DeliveryStt-CreateSTTManualForTokopediaFailed"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	form := new(stt.PublishSttBookingTokopediaFailed)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "error": errors})
	}()

	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	errors = c.SttUc.CreateSTTManualForTokopediaFailed(selfCtx, form)
	if errors != nil {
		logger.E(errors)
		return shared.HttpError(e, errors)
	}

	response := shared.JSONResponse(http.StatusOK, `STT Manual Booking for tokopedia failed successfully`, true, nil)
	return e.JSON(response.Code, response)
}

// CreateSTTBookingForTokopedia ...
func (c *SttDelivery) CreateSTTBookingForTokopedia(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "DeliveryStt-CreateSTTBookingForTokopedia"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	form := new(stt.PublishSttBookingTokopedia)
	stt := new(stt.CreateSttResponse)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": stt, "error": errors})
	}()

	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	stt, errors = c.SttUc.CreateSTTManualForTokopedia(selfCtx, form)
	if errors != nil {
		logger.E(errors)
		return shared.HttpError(e, errors)
	}

	response := shared.JSONResponse(http.StatusCreated, `STT Manual Booking for tokopedia successfully created`, true, stt)
	return e.JSON(response.Code, response)
}

func (c *SttDelivery) ViewSttManifestBag(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "DeliveryStt-ViewSttManifestBag"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	var err error

	token := e.Get("tokenStr").(string)

	request := stt.ViewSttManifestBagRequest{
		BagNumber: e.QueryParam("bag_number"),
		Token:     token,
	}

	data, err := c.SttUc.ViewSttManifestBag(selfCtx, request)
	if err != nil {
		return shared.HttpError(e, err)
	}
	resp := shared.JSONSuccess(`Success`, data)
	return e.JSON(resp.Code, resp)
}

// CreateLogNonCrossDockingFailed ...
func (c *SttDelivery) CreateLogNonCrossDockingFailed(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "DeliveryStt-CreateLogNonCrossDockingFailed"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	form := new(stt.PublishSttBookingTokopediaFailed)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "error": errors})
	}()

	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	form.NonCrossDocking = true

	errors = c.SttUc.CreateSTTManualForTokopediaFailed(selfCtx, form)
	if errors != nil {
		logger.E(errors)
		return shared.HttpError(e, errors)
	}

	response := shared.JSONResponse(http.StatusOK, `STT Manual Booking for tokopedia failed successfully`, true, nil)
	return e.JSON(response.Code, response)
}

// CreateSTTBookingNonCrossDocking ...
func (c *SttDelivery) CreateSTTBookingNonCrossDocking(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "DeliveryStt-CreateSTTBookingNonCrossDocking"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	form := new(stt.PublishSttBookingNonCrossDocking)
	stt := new(stt.CreateSttResponse)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": stt, "error": errors})
	}()
	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	stt, errors = c.SttUc.CreateSTTBookingNonCrossDocking(selfCtx, form)
	if errors != nil {
		logger.E(errors)
		return shared.HttpError(e, errors)
	}

	response := shared.JSONResponse(http.StatusCreated, `STT Manual Booking for tokopedia successfully created`, true, stt)
	return e.JSON(response.Code, response)
}

func (c *SttDelivery) RenameNoRef(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "Stt-RenameNoRef"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	form := new(stt.RenameNoRefRequest)
	resp := new(stt.RenameNoRefResponse)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": resp, "error": errors})
	}()
	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	resp, errors = c.SttUc.RenameNoRef(selfCtx, form)
	if errors != nil {
		logger.E(errors)
		return shared.HttpError(e, errors)
	}

	response := shared.JSONResponse(http.StatusCreated, `Rename No Ref Success`, true, resp)
	return e.JSON(response.Code, response)
}

// OldestSTT ...
func (c *SttDelivery) GetOldestSTT(e echo.Context) error {

	ctx := e.Request().Context()
	opName := "SttDelivery-OldestSTT"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	form := new(stt.GetOldestSttRequest)
	var res *model.Stt

	var err error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}

		trace.Finish(map[string]interface{}{"param": form, "result": res, "error": err})
	}()

	form.PartnerType = e.QueryParam("partner_type")
	form.PartnerIdString = e.QueryParam("partner_id")

	res, err = c.SttUc.GetOldestSTT(selfCtx, form)
	if err != nil {
		return shared.HttpError(e, err)
	}

	resp := shared.JSONSuccess(`Get Oldest STT successfully retrieved`, res)
	return e.JSON(resp.Code, resp)
}

func (c *SttDelivery) ReverseDestination(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "Stt-ReverseDestination"
	token := e.Get("tokenStr").(string)
	trace := tracer.StartTrace(ctx, opName)
	getID := e.Param("id")
	selfCtx := trace.Context()
	form := new(stt.ReverseDestinationParams)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": nil, "error": errors})
	}()
	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}
	sttID, err := strconv.Atoi(getID)
	if sttID == 0 || err != nil {
		return shared.HttpError(e, c.DictionaryError.MalformatRequest())
	}

	form.Token = token
	form.SttId = sttID

	errors = c.SttUc.ReverseDestination(selfCtx, form)
	if errors != nil {
		logger.E(errors)
		return shared.HttpError(e, errors)
	}

	response := shared.JSONResponse(http.StatusCreated, `Success`, true, nil)
	return e.JSON(response.Code, response)
}

func (c *SttDelivery) GetDFODPasti(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "Stt-GetDFODPasti"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	form := new(stt.SttDfodPastiRequest)
	resp := new(stt.SttDfodPastiResponse)
	var errors error

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": nil, "error": errors})
	}()
	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	resp, errors = c.SttUc.GetDfodPasti(selfCtx, form)
	if errors != nil {
		logger.E(errors)
		return shared.HttpError(e, errors)
	}

	response := shared.JSONResponse(http.StatusCreated, `Success`, true, resp)
	return e.JSON(response.Code, response)
}

func (c *SttDelivery) Validate(e echo.Context) error {
	var (
		ctx     = e.Request().Context()
		opName  = "SttDelivery-Validate"
		trace   = tracer.StartTrace(ctx, opName)
		selfCtx = trace.Context()
		form    = new(stt.ValidateRequest)
		err     error
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{"param": form, "result": nil, "error": err})
	}()

	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	res, err := c.SttUc.Validate(selfCtx, form)
	if err != nil {
		logger.E(err)
		return shared.HttpError(e, err)
	}

	response := shared.JSONResponse(http.StatusOK, `Success`, true, res)
	return e.JSON(response.Code, response)
}

func (c *SttDelivery) CorporateCrossDockingRetry(e echo.Context) error {
	ctx := e.Request().Context()
	opName := "Stt-CorporateCrossDockingRetry"
	trace := tracer.StartTrace(ctx, opName)
	selfCtx := trace.Context()
	form := new(stt.CorporateCrossDockingRetryRequest)

	var (
		errors error
		result *stt.CorporateCrossDockingRetryResponse
	)

	defer func() {
		if r := recover(); r != nil {
			msg := tracer.IdentifyPanic(opName, r)
			tracer.Log(selfCtx, "panic_recovered", msg)
		}
		trace.Finish(map[string]interface{}{
			"param":  form,
			"result": result,
			"error":  errors,
		})
	}()
	if err := e.Bind(form); err != nil {
		return shared.HttpError(e, shared.NewMultiStringBadRequestError(shared.HTTPErrorBadRequest, map[string]string{
			"en": "Malformat request",
			"id": "Permintaan tidak benar",
		}))
	}

	result, errors = c.SttUc.CorporateCrossDockingRetry(selfCtx, form)
	if errors != nil {
		logger.E(errors)
		return shared.HttpError(e, errors)
	}

	response := shared.JSONResponse(http.StatusOK, `Success`, true, result)
	return e.JSON(response.Code, response)
}
