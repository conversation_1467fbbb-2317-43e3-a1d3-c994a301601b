name: CI

on:
  pull_request:
    branches: [development]

jobs:
  unit-testing:
    runs-on: self-hosted
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          token: ${{ secrets.LP_GITHUB_TOKEN }}
      - name: Set up Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.23
     
      - name: Run cover
        run: make cover
